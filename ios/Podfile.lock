PODS:
  - AliyunOSSiOS (2.10.16)
  - AMap3DMap (9.5.0):
    - AMapFoundation (>= 1.8.0)
  - amap_flutter_location (0.0.1):
    - AMapLocation
    - Flutter
  - amap_flutter_map (0.0.1):
    - AMap3DMap
    - Flutter
  - AMapFoundation (1.8.0)
  - AMapLocation (2.9.0):
    - AMapFoundation (>= 1.7.0)
  - audioplayers_darwin (0.0.1):
    - Flutter
  - camera_avfoundation (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_app_upgrade (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - fluwx (0.0.1):
    - Flutter
    - OpenWeChatSDK (~> 1.9.9)
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - image_gallery_saver (1.5.0):
    - Flutter
  - "OpenWeChatSDK (1.9.9+1)"
  - package_info (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - rhttp (0.0.1):
    - Flutter
  - scan (0.0.1):
    - AliyunOSSiOS
    - Flutter
    - ScanKitFrameWork (~> 1.1.0.306)
    - Toast
  - ScanKitFrameWork (1.1.0.306)
  - share (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.2):
    - Flutter
    - FMDB (>= 2.7.5)
  - Toast (4.0.0)
  - tobias (0.0.1):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - amap_flutter_location (from `.symlinks/plugins/amap_flutter_location/ios`)
  - amap_flutter_map (from `.symlinks/plugins/amap_flutter_map/ios`)
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - Flutter (from `Flutter`)
  - flutter_app_upgrade (from `.symlinks/plugins/flutter_app_upgrade/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - fluwx (from `.symlinks/plugins/fluwx/ios`)
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - package_info (from `.symlinks/plugins/package_info/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - rhttp (from `.symlinks/plugins/rhttp/ios`)
  - scan (from `.symlinks/plugins/scan/ios`)
  - share (from `.symlinks/plugins/share/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - tobias (from `.symlinks/plugins/tobias/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - AliyunOSSiOS
    - AMap3DMap
    - AMapFoundation
    - AMapLocation
    - FMDB
    - OpenWeChatSDK
    - ScanKitFrameWork
    - Toast

EXTERNAL SOURCES:
  amap_flutter_location:
    :path: ".symlinks/plugins/amap_flutter_location/ios"
  amap_flutter_map:
    :path: ".symlinks/plugins/amap_flutter_map/ios"
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  Flutter:
    :path: Flutter
  flutter_app_upgrade:
    :path: ".symlinks/plugins/flutter_app_upgrade/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  fluwx:
    :path: ".symlinks/plugins/fluwx/ios"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  package_info:
    :path: ".symlinks/plugins/package_info/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  rhttp:
    :path: ".symlinks/plugins/rhttp/ios"
  scan:
    :path: ".symlinks/plugins/scan/ios"
  share:
    :path: ".symlinks/plugins/share/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  tobias:
    :path: ".symlinks/plugins/tobias/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  AliyunOSSiOS: d624effdd24535da414a2bacf0027bedf64cc90e
  AMap3DMap: dc972a62d7fe451795e4b3637e377a2cf593d0e7
  amap_flutter_location: 44ff5beb64f42e0bf5feb402fe299dac0013af6f
  amap_flutter_map: 979e54d227cedac6c7504a2151bfbf3bcf96760a
  AMapFoundation: f48153f724114b58da9b01875ab88a1f6856e3db
  AMapLocation: f5eb11e11c62f0f599f80b578eec8a1f70deb985
  audioplayers_darwin: 877d9a4d06331c5c374595e46e16453ac7eafa40
  camera_avfoundation: dd002b0330f4981e1bbcb46ae9b62829237459a4
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_app_upgrade: a62fbe2a09a2176a7b44211c8355adfcfb969779
  fluttertoast: 48c57db1b71b0ce9e6bba9f31c940ff4b001293c
  fluwx: f14acb5301bc227adc092f6052cefac56b2100f2
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  image_gallery_saver: 259eab68fb271cfd57d599904f7acdc7832e7ef2
  OpenWeChatSDK: ea48e9db20645f78128db9091893910280b8e4b1
  package_info: 873975fc26034f0b863a300ad47e7f1ac6c7ec62
  path_provider_foundation: c68054786f1b4f3343858c1e1d0caaded73f0be9
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  rhttp: 367a8162e63311c6dde543169b591cc04454dcd6
  scan: 1f71fc348fbb72b4161740043df7c88074066111
  ScanKitFrameWork: 25a6ba4adae30de08c358c27152d7a8ed4ca80ab
  share: 0b2c3e82132f5888bccca3351c504d0003b3b410
  shared_preferences_foundation: 986fc17f3d3251412d18b0265f9c64113a8c2472
  sqflite: 6d358c025f5b867b29ed92fc697fd34924e11904
  Toast: 91b396c56ee72a5790816f40d3a94dd357abc196
  tobias: 2aded9b83e3663b907360a800d8e3c13284f25c5
  url_launcher_ios: 08a3dfac5fb39e8759aeb0abbd5d9480f30fc8b4
  webview_flutter_wkwebview: b7e70ef1ddded7e69c796c7390ee74180182971f

PODFILE CHECKSUM: 87d9e6cce077b3d82ef6f1f1c5a949c24ef127fc

COCOAPODS: 1.16.2
