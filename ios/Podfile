# Uncomment this line to define a global platform for your project
# platform :ios, '12.0'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  use_frameworks!
  use_modular_headers!

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
end

$iOSVersion= "10.0"

post_install do |installer|
    installer.generated_projects.each do |project|
          project.targets.each do |target|
              target.build_configurations.each do |config|
                  config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '11.0'
               end
          end
  end
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)
  end

  # add these lines:
#     installer.pods_project.build_configurations.each do |config|
#       config.build_settings["EXCLUDED_ARCHS[sdk=*]"] = "armv7"
#       config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = $iOSVersion
#     end
#
#     installer.pods_project.targets.each do |target|
#       flutter_additional_ios_build_settings(target)
#
#
#       target.build_configurations.each do |config|
#         config.build_settings['ENABLE_BITCODE'] = 'NO'
#
#
#         # https://pub.flutter-io.cn/packages/permission_handler
#         # permission_handler的权限设置（详细参考官网）。
#         # 在dart代码中，如果是通过permission_handler去申请一些应用权限，需要在这里打开对应宏设置。
#         # 否则通过permission_handler获取到的权限状态只是默认值，而不是正确的状态！
#         config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
#            '$(inherited)',
#
#           # Disable firebase-hosted ML models
#            'MLKIT_FIREBASE_MODELS=0',
#
#            ## dart: PermissionGroup.camera
#            'PERMISSION_CAMERA=1',
#
#            ## dart: PermissionGroup.microphone
#            'PERMISSION_MICROPHONE=1',
#
#            ## dart: PermissionGroup.photos
#            'PERMISSION_PHOTOS=1',
#
#            ## dart: [PermissionGroup.location, PermissionGroup.locationAlways, PermissionGroup.locationWhenInUse]
#            'PERMISSION_LOCATION=1',
#          ]
#       end
#     end

end
