
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:flutter/material.dart';

class CollectIconButtonWidget extends StatelessWidget {
  final VoidCallback? onPress;
  final bool value;

  CollectIconButtonWidget({
    Key? key,
    this.onPress,
    this.value = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        onPress!();
      },
      child: Row(
        children: [
          Icon(value ? Icons.star : Icons.star_border,
              size: AppConstant.middleTextWhiteSize, color: value ? Theme.of(context).primaryColor : Colors.grey),
          Text(value ? '已收藏' : '收藏',
              style: TextStyle(fontSize: AppConstant.smallTextSize, color: value ? Theme.of(context).primaryColor : Colors.grey))
        ],
      ),
    );
  }
}
