import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:flutter/material.dart';

class RichTextWidget extends StatefulWidget {
  final String? label;
  final String? value;
  final TextStyle? labelStyle;
  final TextStyle? valueStyle;

  RichTextWidget({this.label, this.value, this.labelStyle, this.valueStyle});

  @override
  _RichTextWidgetState createState() => _RichTextWidgetState();
}

class _RichTextWidgetState extends State<RichTextWidget> {
  @override
  Widget build(BuildContext context) {
    List<InlineSpan> widgets = [];
    widgets.add(TextSpan(
      text: '${widget.value}',
      style: widget.valueStyle ??
          new TextStyle(
            color: Colors.black87,
            fontWeight: FontWeight.w400,
            fontSize: 14.0,
          ),
    ));
    return RichText(
        text: TextSpan(
            text: '${widget.label ?? ''}',
            style: widget.labelStyle ?? TextStyle(fontSize: AppConstant.smallTextSize, color: Colors.grey[400]),
            children: widgets));
  }
}
