import 'package:flutter/material.dart';

class InputDropdown extends StatelessWidget {
  const InputDropdown(
      {Key? key,
      this.isNormal = false,
      this.labelText,
      this.valueText = '',
      this.valueStyle,
      this.onPressed,
      this.fontColor = Colors.white})
      : super(key: key);

  final bool isNormal;
  final String? labelText;
  final String valueText;
  final TextStyle? valueStyle;
  final VoidCallback? onPressed;
  final Color fontColor;

  @override
  Widget build(BuildContext context) {
    if (isNormal) {
      return InkWell(
        onTap: onPressed,
        child: InputDecorator(
          decoration: InputDecoration(labelText: labelText, contentPadding: EdgeInsets.fromLTRB(0.0, 5.0, 0.0, 5.0)),
          baseStyle: valueStyle,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Text(valueText, style: valueStyle),
              Icon(Icons.arrow_drop_down, color: Colors.grey.shade700),
//                  color: Theme.of(context).brightness == Brightness.light ? Colors.grey.shade700 : Colors.white70),
            ],
          ),
        ),
      );
    } else {
      return InkWell(
        onTap: onPressed,
        child: new Container(
            height: 40.0,
            decoration: BoxDecoration(border: Border.all(width: 1.0, color: Colors.white)),
            child: new Align(
              alignment: Alignment.center,
              child: new Text(
                valueText,
                style: new TextStyle(fontSize: 16.0, color: fontColor),
                textAlign: TextAlign.center,
              ),
            )),
      );
    }
  }
}
