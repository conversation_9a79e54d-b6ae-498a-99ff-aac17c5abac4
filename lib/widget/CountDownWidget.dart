import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// 倒计时组件
const int COUNT_DOWN = 100;

// GlobalKey<CountDownWidgetState> countDownKey = GlobalKey<CountDownWidgetState>();
class CountDownWidget extends StatefulWidget {
  final ValueChanged<int?>? onChanged;
  final VoidCallback? onTimerFinish;
  final bool isBlack;
  final Key key;

  CountDownWidget(this.key, {this.isBlack = false, this.onChanged, this.onTimerFinish});

  @override
  CountDownWidgetState createState() => new CountDownWidgetState();
}

class CountDownWidgetState extends State<CountDownWidget> {
  Timer? _timer;
  int countDown = COUNT_DOWN;

  @override
  void initState() {
    super.initState();
    initCountDown();
  }

  resetCountDown() async {
    await Future.delayed(const Duration(milliseconds: 200), () {
      setState(() {
        countDown = COUNT_DOWN;
      });
    });
    initCountDown();
  }

  stopCountDown() {
    _timer?.cancel();
    countDown = COUNT_DOWN;
    setState(() {});
  }

  initCountDown({bool isReset = false}) {
    _timer?.cancel();
    _timer = Timer.periodic(
      Duration(seconds: 1),
      (Timer timer) => {
        setState(() {
          if (countDown < 1) {
            if (widget.onTimerFinish != null) {
              if (!kDebugMode)
              widget.onTimerFinish!();
            }
            _timer?.cancel();
          } else {
            countDown--;
          }
        })
      },
    );
  }

  @override
  void dispose() {
    super.dispose();
    _timer?.cancel();
    _timer = null;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 60.0,
      padding: EdgeInsets.only(right: 12),
      alignment: Alignment.centerRight,
      child: Text(
        '$countDown' + 's',
        style: TextStyle(
            fontSize: 20.0,
            color: countDown < 30
                ? Colors.red
                : widget.isBlack
                    ? Colors.black
                    : Colors.white),
      ),
    );
  }
}
