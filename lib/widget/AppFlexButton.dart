import 'package:flutter/material.dart';

///
/// 充满的button
/// Created by denonzhu
///
class AppFlexButton extends StatelessWidget {
  final String text;

  final Color? color;

  final Color? textColor;

  final VoidCallback? onPress;

  final double fontSize;
  final int maxLines;

  final MainAxisAlignment mainAxisAlignment;

  AppFlexButton(
      {Key? key,
      this.text = '',
      this.color,
      this.textColor,
      this.onPress,
      this.fontSize = 16.0,
      this.mainAxisAlignment = MainAxisAlignment.center,
      this.maxLines = 1})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextButton(
      style: ButtonStyle(
        // shape: RoundedRectangleBorder(
        //   borderRadius: BorderRadius.all(Radius.circular(4)),
        // ),
        padding:
            MaterialStateProperty.all<EdgeInsets>(EdgeInsets.only(left: 20.0, top: 10.0, right: 20.0, bottom: 10.0)),
        textStyle: MaterialStateProperty.all<TextStyle>(TextStyle(
          color: textColor,
          // disabledColor: Colors.grey,
          // disabledTextColor: Colors.white70,
          backgroundColor: color,
        )),
      ),
      child: Flex(
        mainAxisAlignment: mainAxisAlignment,
        direction: Axis.horizontal,
        children: <Widget>[
          new Expanded(
            child: new Container(
              alignment: Alignment.center,
              child:
                  Text(text, style: TextStyle(fontSize: fontSize), maxLines: maxLines, overflow: TextOverflow.ellipsis),
            ),
          )
        ],
      ),
      onPressed: this.onPress,
    );
  }
}
