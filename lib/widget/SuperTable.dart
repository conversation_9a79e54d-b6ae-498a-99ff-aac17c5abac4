import 'package:cabinet_flutter_app/widget/NoResult.dart';
import 'package:flutter/material.dart';

///通用下上刷新控件
class SuperTableWidget extends StatefulWidget {
  ///加载更多回调
  final RefreshCallback onLoadMore;

  ///下拉刷新回调
  final RefreshCallback onRefresh;

  ///控制器，比如数据和一些配置
  final AppPullLoadWidgetControl control;

  final Key refreshKey;

  final List config;

  SuperTableWidget(this.control, this.onRefresh, this.onLoadMore, this.config, {required this.refreshKey});

  @override
  _SuperTableWidgetState createState() =>
      _SuperTableWidgetState(this.control, this.onRefresh, this.onLoadMore, this.config, this.refreshKey);
}

class _SuperTableWidgetState extends State<SuperTableWidget> {
  final RefreshCallback onLoadMore;

  final RefreshCallback onRefresh;

  final Key refreshKey;

  final List config;

  AppPullLoadWidgetControl control;

  _SuperTableWidgetState(this.control, this.onRefresh, this.onLoadMore, this.config, this.refreshKey);

  final ScrollController _scrollController = new ScrollController();
  ScrollController _mScrollControllerRight = new ScrollController();
  ScrollController _mScrollControllerFixedLabel = new ScrollController();

  double sortWidth = 30.0;
  double itemHeight = 30.0;
  List fixedKeys = [];
  List notFixedKeys = [];
  List fixedWidths = [];
  List notFixedWidths = [];

  @override
  void initState() {
    ///增加滑动监听
    _scrollController.addListener(() {
      ///判断当前滑动位置是不是到达底部，触发加载更多回调
      if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
        if (this.control.needLoadMore) {
          this.onLoadMore.call();
        }
      }
    });

    _mScrollControllerRight.addListener(() {
      _mScrollControllerFixedLabel.animateTo(_mScrollControllerRight.offset,
          duration: new Duration(milliseconds: 1), curve: Curves.ease);
    });
    super.initState();
    getAllKeys();
  }


  ///根据配置状态返回实际列表渲染Item
  _getItem() {
//    if (!control.needHeader && index == control.dataList.length && control.dataList.length != 0) {
//      ///如果不需要头部，并且数据不为0，当index等于数据长度时，渲染加载更多Item（因为index是从0开始）
//      return _buildProgressIndicator();
//    } else if (control.needHeader && index == _getListCount() - 1 && control.dataList.length != 0) {
//      ///如果需要头部，并且数据不为0，当index等于实际渲染长度 - 1时，渲染加载更多Item（因为index是从0开始）
//      return _buildProgressIndicator();
//    }
    if (!control.needHeader && control.dataList.length == 0) {
      ///如果不需要头部，并且数据为0，渲染空页面
      return NoResult();
    } else {
      ///回调外部正常渲染Item，如果这里有需要，可以直接返回相对位置的index
      return new Column(
        children: <Widget>[
          new Row(
            children: <Widget>[
              new Column(
                children: _getItemLeft(),
              ),
              new Expanded(
                child: new SingleChildScrollView(
                  controller: _mScrollControllerRight,
                  scrollDirection: Axis.horizontal,
                  child: new Column(
                    children: _getItemRight(),
                  ),
                ),
              )
            ],
          ),
        ],
      );
    }
  }

  _buildRightRow(data) {
    List<Widget> row = <Widget>[];
    notFixedKeys.forEach((v2) {
      int index = notFixedKeys.indexWhere((v3) => v2 == v3);
      data.forEach((key, v) {
        if (key == v2) {
          row.add(new Container(
            width: (index >= 0) ? notFixedWidths[index] : 0.0,
            height: itemHeight,
            padding: EdgeInsets.fromLTRB(2.0, 0.0, 2.0, 0.0),
            decoration: new BoxDecoration(
                border: Border(
                    bottom: BorderSide(color: const Color(0xFFEEEEEE), width: 1.0),
                    right: BorderSide(color: const Color(0xFFEEEEEE), width: 1.0))),
            child: new Align(
              alignment: Alignment.centerLeft,
              child: new Text(
                '$v',
                style: new TextStyle(color: Colors.grey),
              ),
            ),
          ));
        }
      });
    });
    return new Row(
      children: row,
    );
  }

  _buildLeftRow(data, _index) {
    List<Widget> row = <Widget>[];
    fixedKeys.forEach((v2) {
      int index = fixedKeys.indexWhere((v3) => v2 == v3);
      data.forEach((key, v) {
        if (key == v2) {
          row.add(new Container(
            width: (index >= 0) ? fixedWidths[index] : 0.0,
            height: itemHeight,
            padding: EdgeInsets.fromLTRB(2.0, 0.0, 2.0, 0.0),
            decoration: new BoxDecoration(
                border: Border(
                    bottom: BorderSide(color: const Color(0xFFEEEEEE), width: 1.0),
                    right: BorderSide(color: const Color(0xFFEEEEEE), width: 1.0))),
            child: new Align(
              alignment: Alignment.centerLeft,
              child: new Text(
                '$v',
                style: new TextStyle(color: Colors.grey),
              ),
            ),
          ));
        }
      });
    });
    row.insert(
        0,
        new Container(
          width: sortWidth,
          height: itemHeight,
          padding: EdgeInsets.fromLTRB(2.0, 0.0, 2.0, 0.0),
          decoration: new BoxDecoration(
              border: Border(
                  bottom: BorderSide(color: const Color(0xFFEEEEEE), width: 1.0),
                  right: BorderSide(color: const Color(0xFFEEEEEE), width: 1.0))),
          child: new Align(
            alignment: Alignment.centerLeft,
            child: new Text(
              (_index + 1).toString(),
              style: new TextStyle(color: Colors.grey),
            ),
          ),
        ));
    return new Row(
      children: row,
    );
  }

  _buildLeftTopLabel() {
    List<Widget> leftLabels = <Widget>[];
    config.forEach((v) {
      if (v['isFixed'] == true) {
        leftLabels.add(new Container(
          width: (v['width'] is String) ? double.parse(v['width']) : v['width'],
          height: itemHeight,
          padding: EdgeInsets.fromLTRB(2.0, 0.0, 2.0, 0.0),
          decoration: new BoxDecoration(
              border: Border(
                  bottom: BorderSide(color: const Color(0xFFEEEEEE), width: 1.0),
                  right: BorderSide(color: const Color(0xFFEEEEEE), width: 1.0))),
          child: new Align(
            alignment: Alignment.centerLeft,
            child: new Text(
              '${v['label']}',
              style: new TextStyle(color: Colors.grey),
            ),
          ),
        ));
      }
    });
    leftLabels.insert(
        0,
        new Container(
          width: sortWidth,
          height: itemHeight,
          padding: EdgeInsets.fromLTRB(2.0, 0.0, 2.0, 0.0),
          decoration: new BoxDecoration(
              border: Border(
                  bottom: BorderSide(color: const Color(0xFFEEEEEE), width: 1.0),
                  right: BorderSide(color: const Color(0xFFEEEEEE), width: 1.0))),
          child: new Align(
            alignment: Alignment.centerLeft,
            child: new Text(
              '#',
              style: new TextStyle(color: Colors.grey),
            ),
          ),
        ));
    return leftLabels;
  }

  _buildRightTopLabel() {
    List<Widget> rightLabels = <Widget>[];
    config.forEach((v) {
      if (v['isFixed'] != true) {
        rightLabels.add(new Container(
          width: (v['width'] is String) ? double.parse(v['width']) : v['width'],
          height: itemHeight,
          padding: EdgeInsets.fromLTRB(2.0, 0.0, 2.0, 0.0),
          decoration: new BoxDecoration(
              border: Border(
                  bottom: BorderSide(color: const Color(0xFFEEEEEE), width: 1.0),
                  right: BorderSide(color: const Color(0xFFEEEEEE), width: 1.0))),
          child: new Align(
            alignment: Alignment.centerLeft,
            child: new Text(
              '${v['label']}',
              style: new TextStyle(color: Colors.grey),
            ),
          ),
        ));
      }
    });
    return rightLabels;
  }

  getAllKeys() {
    List leftKeys = [];
    List leftWidths = [];
    List rightKeys = [];
    List rightWidths = [];
    config.forEach((v) {
      if (v['isFixed'] == true) {
        leftKeys.add(v['key']);
        leftWidths.add(v['width']);
      } else {
        rightKeys.add(v['key']);
        rightWidths.add(v['width']);
      }
    });
    setState(() {
      fixedKeys = leftKeys;
      fixedWidths = leftWidths;
      notFixedKeys = rightKeys;
      notFixedWidths = rightWidths;
    });
  }

  _getItemRight() {
    List<Widget> list = <Widget>[];
    for (var index = 0; index < control.dataList.length; index++) {
      list.add(_buildRightRow(control.dataList[index]));
    }
    return list;
  }

  _getItemLeft() {
    List<Widget> list = <Widget>[];
    for (var index = 0; index < control.dataList.length; index++) {
      list.add(
        new Container(
          height: itemHeight,
          child: _buildLeftRow(control.dataList[index], index),
        ),
      );
    }
    return list;
  }

  @override
  Widget build(BuildContext context) {
    return new Column(
      children: <Widget>[
        new Row(
          children: <Widget>[
            (control.dataList.length > 0)
                ? new Row(
                    children: _buildLeftTopLabel(),
                  )
                : new Container(),
            new Expanded(
              child: new SingleChildScrollView(
                controller: _mScrollControllerFixedLabel,
                scrollDirection: Axis.horizontal,
                child: (control.dataList.length > 0)
                    ? new Row(
                        children: _buildRightTopLabel(),
                      )
                    : new Container(),
              ),
            )
          ],
        ),
        new Expanded(
          child: new RefreshIndicator(
            ///GlobalKey，用户外部获取RefreshIndicator的State，做显示刷新
            key: refreshKey,

            ///下拉刷新触发，返回的是一个Future
            onRefresh: onRefresh,
            child: new SingleChildScrollView(
              controller: _scrollController,

              ///保持ListView任何情况都能滚动，解决在RefreshIndicator的兼容问题。
              physics: const AlwaysScrollableScrollPhysics(),

              child: _getItem(),

              ///根据状态返回子孔健
//              itemBuilder: (context, index) {
//                return _getItem(index);
//              },

              ///根据状态返回数量
//              itemCount: _getListCount(),

              ///滑动监听
//              controller: _scrollController,
            ),
          ),
        )
      ],
    );
  }

}

class AppPullLoadWidgetControl {
  ///数据，对齐增减，不能替换
  List dataList = [];

  ///是否需要加载更多
  bool needLoadMore = true;

  ///是否需要头部
  bool needHeader = false;
}
