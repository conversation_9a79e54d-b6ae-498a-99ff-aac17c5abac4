import 'dart:async';

import 'package:flutter/material.dart';

// GlobalKey<DateDownWidgetState> countDownKey = GlobalKey<DateDownWidgetState>();
class DateDownWidget extends StatefulWidget {
  String? endTime;
  String? titleText;
  final VoidCallback timeClose;

  DateDownWidget(this.endTime, this.titleText, this.timeClose);

  @override
  DateDownWidgetState createState() => new DateDownWidgetState();
}

class DateDownWidgetState extends State<DateDownWidget> {
  Timer? _timer;
  String times = '';

  /// 获取当前剩余时间
  getResidueTime() {
    DateTime nowTime = new DateTime.now();
    DateTime endTime = DateTime.parse((widget.endTime).toString());
    int seconds = endTime.difference(nowTime).inSeconds;
    List<String> parts = Duration(seconds: seconds).toString().split(':');
    String days = (int.parse(parts[0]) / 24).toStringAsFixed(2).split('.')[0];
    String hours = (int.parse(parts[0]) - (int.parse(days) * 24)).toString();
    if (seconds.toString().substring(0, 1) == '-') {
      times = '';
    } else {
      times = '${widget.titleText}' +
          '${int.parse(days) > 0 ? '$days天' : ""}' +
          '${int.parse(hours) > 0 ? '$hours时' : ""}' +
          '${int.parse(parts[1]) > 0 ? '${parts[1]}分' : ""}' +
          parts[2].split('.')[0] +
          '秒';
    }
  }

  @override
  void initState() {
    super.initState();
    startTimes();

    ///循环执行
    ///间隔1秒
  }

  startTimes() {
    _timer = Timer.periodic(Duration(milliseconds: 1000), (timer) {
      ///自增
      getResidueTime();

      ///到5秒后停止
      if (times == '') {
        _timer?.cancel();
        widget.timeClose();
      }
      setState(() {});
    });
  }

  @override
  void dispose() {
    super.dispose();
    _timer?.cancel();
    _timer = null;
  }

  @override
  Widget build(BuildContext context) {
    return Text(times, style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 14));
  }
}
