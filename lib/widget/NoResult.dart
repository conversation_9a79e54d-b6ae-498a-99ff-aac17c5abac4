import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:flutter/material.dart';

class NoResult extends StatefulWidget {
  final double size;
  final String label;
  final String type;
  final Widget? subWidget;

  NoResult({this.size = 120.0, this.label = '暂无信息', this.type = 'package', this.subWidget});

  @override
  State<StatefulWidget> createState() => _NoResultState();
}

class _NoResultState extends State<NoResult> {
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return new Center(
      child: new Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Image(
              image: AssetImage(widget.type == 'package' ? AppICons.NO_PACKAGE_IMAGE : AppICons.NO_BOX_IMAGE),
              height: widget.size),
          Container(
            child: Text(widget.label, style: AppConstant.smallSubText),
          ),
          widget.subWidget ?? Container()
        ],
      ),
    );
  }
}
