import 'package:flutter/material.dart';

/// 带图标的输入框
class AppDropdownWidget extends StatefulWidget {
  final bool obscureText;
  final bool enabled;

  final String? hintText;

  final String? labelText;

  final ValueChanged<String?>? onChanged;

  final TextStyle? textStyle;

  final TextEditingController? controller;

  List<String>? list;

  final String value;

  AppDropdownWidget(
      {Key? key,
      this.hintText,
      this.labelText,
      this.onChanged,
      this.textStyle,
      this.controller,
      this.obscureText = false,
      this.enabled = true,
      this.list,
      this.value = ''})
      : super(key: key);

  @override
  _AppDropdownWidgetState createState() => new _AppDropdownWidgetState();
}

/// State for [AppDropdownWidget] widgets.
class _AppDropdownWidgetState extends State<AppDropdownWidget> {
  _AppDropdownWidgetState() : super();

  @override
  Widget build(BuildContext context) {
    return DropdownButtonHideUnderline(
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: widget.labelText,
          hintText: widget.hintText,
          contentPadding: EdgeInsets.only(bottom: 6.0),
          enabledBorder: UnderlineInputBorder(borderSide: BorderSide(color: Colors.grey, width: 0.5)),
        ),
        isEmpty: widget.value == '',
        child: widget.enabled
            ? DropdownButton<String>(
                style: TextStyle(fontSize: 18.0, color: Theme.of(context).primaryColor),
                isDense: true,
                isExpanded: true,
                value: widget.value,
                onChanged: widget.onChanged,
                items: widget.list?.map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(
                      value,
                      overflow: TextOverflow.ellipsis,
                      softWrap: false,
                    ),
                  );
                }).toList(),
              )
            : new Text(
                widget.value,
                style: new TextStyle(fontSize: 17.0, color: Colors.black54),
              ),
      ),
    );
  }
}
