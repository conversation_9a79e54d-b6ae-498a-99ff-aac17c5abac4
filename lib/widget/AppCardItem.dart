import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:flutter/material.dart';

/// Card Widget
/// Created by denonzhu
/// Date: 2018-07-16
class AppCardItem extends StatelessWidget {
  final Widget child;
  final EdgeInsets margin;
  final Color color;
  final RoundedRectangleBorder shape;
  final double elevation;

  AppCardItem(
      {required this.child, required this.margin, required this.color, required this.shape, this.elevation = 5.0});

  @override
  Widget build(BuildContext context) {
    EdgeInsets margin = this.margin;
    RoundedRectangleBorder shape = this.shape;
    Color color = this.color;
    // ignore: dead_null_aware_expression
    margin ??= EdgeInsets.only(left: 10.0, top: 10.0, right: 10.0, bottom: 10.0);
    // ignore: dead_null_aware_expression
    shape ??= new RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(4.0)));
    // ignore: dead_null_aware_expression
    color ??= new Color(AppColors.cardWhite);
    return new Card(elevation: elevation, shape: shape, color: color, margin: margin, child: child);
  }
}
