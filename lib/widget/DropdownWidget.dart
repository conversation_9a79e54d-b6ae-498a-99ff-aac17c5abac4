import 'package:flutter/material.dart';

/// 带图标的输入框
class DropdownWidget extends StatefulWidget {
  final bool obscureText;

  final bool enabled;

  final bool isRequire;

  final String hintText;

  final String label;

  final ValueChanged<String?>? onChanged;

  final TextStyle? textStyle;

  final TextEditingController? controller;

  List<String>? list;

  final String? value;

  final bool noBorder;

  final bool littleLabel;

  final bool isBorderRadius;

  final int flex;

  final bool isContentLittleFont;

  final Color bgColor;

  final bool isLeftNoBorder;

  final bool isRightNoBorder;

  final bool isTopNoBorder;

  final bool isBottomNoBorder;

  final double height;

  DropdownWidget(
      {Key? key,
      this.flex = 1,
      this.isLeftNoBorder = false,
      this.isRightNoBorder = false,
      this.isTopNoBorder = false,
      this.isBottomNoBorder = false,
      this.hintText = '',
      this.label = '',
      this.onChanged,
      this.textStyle,
      this.controller,
      this.obscureText = false,
      this.enabled = true,
      this.isRequire = false,
      this.littleLabel = true,
      this.isBorderRadius = true,
      this.isContentLittleFont = false,
      this.height = 40.0,
      this.bgColor = Colors.white,
      this.list,
      this.value,
      this.noBorder = false})
      : super(key: key);

  @override
  _DropdownWidgetState createState() => new _DropdownWidgetState();
}

/// State for [DropdownWidget] widgets.
class _DropdownWidgetState extends State<DropdownWidget> {
  _DropdownWidgetState() : super();

  @override
  Widget build(BuildContext context) {
    return new Expanded(
      flex: widget.flex,
      child: new Container(
        child: new Row(
          children: <Widget>[
            new Offstage(
              offstage: widget.label == '',
              child: new Container(
                width: 70.0,
                height: widget.height,
                alignment: Alignment.centerLeft,
                child: new Text(
                  '${widget.label}',
                  style: new TextStyle(
                    color: widget.isRequire ? Colors.red : Colors.black87,
                    fontSize: widget.littleLabel ? 14.0 : 16.0,
                  ),
                  overflow: TextOverflow.ellipsis,
                  softWrap: false,
                ),
              ),
            ),
            new Offstage(
              offstage: widget.label == '',
              child: new Padding(padding: EdgeInsets.only(left: 5.0)),
            ),
            new Expanded(
              child: new Container(
                padding: EdgeInsets.fromLTRB(5.0, 5.0, 5.0, 0.0),
                alignment: Alignment.center,
                height: widget.height,
                decoration: (widget.isLeftNoBorder ||
                        widget.isRightNoBorder ||
                        widget.isBottomNoBorder ||
                        widget.isTopNoBorder)
                    ? new BoxDecoration(
                        color: widget.bgColor,
                        border: Border(
                            top: BorderSide(color: Color(0xFFEEEEEE), width: widget.isTopNoBorder ? 0.0 : 1.0),
                            bottom: BorderSide(color: Color(0xFFEEEEEE), width: widget.isBottomNoBorder ? 0.0 : 1.0),
                            left: BorderSide(color: Color(0xFFEEEEEE), width: widget.isLeftNoBorder ? 0.0 : 1.0),
                            right: BorderSide(color: Color(0xFFEEEEEE), width: widget.isRightNoBorder ? 0.0 : 1.0)))
                    : new BoxDecoration(
                        color: widget.bgColor,
                        border: Border.all(color: Color(0xFFEEEEEE), width: 1.0),
                        borderRadius: widget.isBorderRadius ? BorderRadius.circular(2.0) : BorderRadius.zero,
                      ),
                child: new DropdownButtonHideUnderline(
                  child: InputDecorator(
                    decoration: InputDecoration(
                        border: InputBorder.none, contentPadding: EdgeInsets.fromLTRB(0.0, 0.0, 0.0, 2.0)),
                    isEmpty: widget.value == null,
                    child: widget.enabled
                        ? new DropdownButton<String>(
                            style: TextStyle(fontSize: 16.0, color: Colors.black87),
                            isDense: true,
                            isExpanded: true,
                            value: widget.value,
                            onChanged: widget.onChanged,
                            items: widget.list?.map((String value) {
                              return DropdownMenuItem<String>(
                                value: value,
                                child: Text(
                                  value,
                                  overflow: TextOverflow.ellipsis,
                                  softWrap: false,
                                  style: new TextStyle(fontSize: widget.isContentLittleFont ? 14.0 : 18.0),
                                ),
                              );
                            }).toList(),
                          )
                        : new Text(
                            widget.value ?? '',
                            style: new TextStyle(fontSize: 17.0, color: Colors.black54),
                          ),
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
