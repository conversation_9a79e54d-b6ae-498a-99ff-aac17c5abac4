import 'package:cabinet_flutter_app/common/entitys/cabinet_shop_entity.dart';
import 'package:flutter/material.dart';

class PointMultiSelect extends StatefulWidget {
  final List<CabinetShopEntity?> options;
  final List<dynamic> selectedOptions;
  final String title;
  final Function(List value) onChanged;

  PointMultiSelect(
      {super.key, required this.title, required this.selectedOptions, required this.onChanged, required this.options});

  @override
  State<PointMultiSelect> createState() => _PointMultiSelectState();
}

class _PointMultiSelectState extends State<PointMultiSelect> {
  void _showMultiSelect() async {
    final List? results = await showDialog(
      context: context,
      builder: (BuildContext context) {
        return MultiSelectDialog(
          options: widget.options,
          selectedOptions: widget.selectedOptions,
        );
      },
    );
    if (results != null) {
      widget.onChanged(results);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        ElevatedButton(
          onPressed: _showMultiSelect,
          child: Text(widget.title),
        ),
        SizedBox(
          width: 10,
        ),
        Text(
          '已选择: ${widget.selectedOptions.length}',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
      ],
    );
  }
}

class MultiSelectDialog extends StatefulWidget {
  final List<CabinetShopEntity?> options;
  final List selectedOptions;

  const MultiSelectDialog({
    super.key,
    required this.options,
    required this.selectedOptions,
  });

  @override
  State<MultiSelectDialog> createState() => _MultiSelectDialogState();
}

class _MultiSelectDialogState extends State<MultiSelectDialog> {
  late List _selected;

  @override
  void initState() {
    super.initState();
    _selected = List.from(widget.selectedOptions);
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('请选择'),
      content: SingleChildScrollView(
        child: ListBody(
          children: widget.options.map((option) {
            return CheckboxListTile(
              title: Text(option?.name ?? ''),
              value: _selected.contains('${option?.id}'),
              onChanged: (bool? value) {
                setState(() {
                  if (value != null) {
                    if (value) {
                      _selected.add('${option?.id}');
                    } else {
                      _selected.remove('${option?.id}');
                    }
                  }
                });
              },
            );
          }).toList(),
        ),
      ),
      actions: [
        TextButton(
          child: const Text('取消'),
          onPressed: () => Navigator.pop(context),
        ),
        TextButton(
          child: const Text('确定'),
          onPressed: () => Navigator.pop(context, _selected),
        ),
      ],
    );
  }
}
