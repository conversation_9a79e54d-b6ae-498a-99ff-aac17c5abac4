import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/widget/NumberPickerWidget.dart';
import 'package:flutter/material.dart';

/// 可下单格口
class CabinetBoxListUseWidget extends StatefulWidget {
  final List<dynamic> list;
  final VoidCallback getCabinetBoxList;
  final String type;

  CabinetBoxListUseWidget(this.list, this.getCabinetBoxList,
      {this.type = '', Key? key})
      : super(key: key);

  @override
  _CabinetBoxListUseWidgetState createState() =>
      new _CabinetBoxListUseWidgetState();
}

/// State for [InorderWidget] widgets.
class _CabinetBoxListUseWidgetState extends State<CabinetBoxListUseWidget> {
  int firstIndexType = 1;

  void initState() {
    super.initState();
  }

  ///格口列表
  List<Widget> buildAvailableGK() {
    return widget.list.map((item) {
      Key key = ObjectKey(item);
      return Container(
          child: Container(
        decoration: BoxDecoration(
            border: firstIndexType == item['type']
                ? Border()
                : Border(
                    bottom: BorderSide(color: Color(0xFFE5E5E5), width: 1.0),
                  )),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${DefaultConfig().configs.CABINET_BOX_TYPE[item['type']]}口',
              style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                  color: Colors.black),
            ),
            Text(
              '${item['availableNum']}',
              style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                  color: Colors.black),
            ),
            Text(
              '${widget.type != '' ? '￥${item['price'] / 1000} * ${item['availableNum']}(个)' : '￥${item['price'] / 1000}'}',
              style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black),
            ),
            widget.type == ''
                ? NumberControllerWidget(
                    key: key,
                    num: '${item['checked']}',
                    height: 32,
                    maxNum: '${item['availableNum']}',
                    onValueChanged: (String value) {
                      item['checked'] = value;
                      setState(() {});
                      widget.getCabinetBoxList();
                    })
                : Text(
                    '￥${item['price'] / 1000 * item['availableNum']}',
                    style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.black),
                  )
          ],
        ),
        padding: EdgeInsets.fromLTRB(15, 10, 10, 10),
      ));
    }).toList();
  }

  getFirstIndex(emptyList) {
    if (emptyList.length > 0) {
      firstIndexType = emptyList[emptyList.length - 1]['type'];
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    getFirstIndex(widget.list);
    return Container(
      child: Column(
        children: buildAvailableGK(),
      ),
    );
  }
}
