import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:flutter/material.dart';

///
/// 充满的button
/// Created by denonzhu
///
class IconButtonWidget extends StatelessWidget {
  final VoidCallback onPress;

  IconButtonWidget({
    Key? key,
    required this.onPress,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 35.0,
      height: 35.0,
//      decoration: new BoxDecoration(
//        color: Theme.of(context).primaryColor,
////        border: new Border.all(width: 10.0, color: Colors.black38),
//        borderRadius:new BorderRadius.all(const Radius.circular(8.0)),
//      ),
      padding: EdgeInsets.all(0.0),
      child: new IconButton(
        icon: Icon(AppICons.MAIN_SEARCH),
        color: Theme.of(context).primaryColor,
        onPressed: () {
          onPress();
        },
      ),
    );
  }
}
