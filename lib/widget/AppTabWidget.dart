
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:flutter/material.dart';

/// 带图标的输入框
class AppTabWidget extends StatefulWidget {
  final ValueChanged<int> onChanged;

  final List<Map<String, dynamic>> tabs;

  final int index;

  final Color? bgColor;

  AppTabWidget({required this.onChanged, required this.tabs, required this.index, this.bgColor});

  @override
  _AppTabWidgetState createState() => new _AppTabWidgetState();
}

class _AppTabWidgetState extends State<AppTabWidget> {
  double itemWidth = 100;

  selectTab(info) {
    int _index = widget.tabs.indexWhere((tab) => tab['label'] == info['label'] && tab['value'] == info['value']);
    widget.onChanged(_index);
  }

  buildTabs() {
    double fontSize = 15.0;
    double height = 35.0;
    var selectOne = widget.tabs[widget.index];
    var _first = widget.tabs.first;
    var _last = widget.tabs.last;
    var _middle = widget.tabs.sublist(1, widget.tabs.length - 1);
    List<Widget> widgets = [];
    widgets.add(new InkWell(
      onTap: () {
        selectTab(_first);
      },
      child: new Container(
        padding: EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
        height: height,
        width: itemWidth,
        alignment: Alignment.center,
        decoration: new BoxDecoration(
          color: selectOne['label'] == _first['label'] ? Theme.of(context).primaryColor : DefaultConfig().configs.PRIMARY_COLOR_LIGHT,
          borderRadius: BorderRadius.all(Radius.circular(20.0)),
          // border: Border(
          //     top: BorderSide(width: 1.0, color: Colors.white),
          //     left: BorderSide(width: 1.0, color: Colors.white),
          //     bottom: BorderSide(width: 1.0, color: Colors.white)),
        ),
        child: new Text(
          selectOne['label'] == _first['label'] ? _first['activeLabel'] ?? _first['label'] : _first['label'],
          style: new TextStyle(
              color: selectOne['label'] == _first['label'] ? Colors.white : Theme.of(context).primaryColor,
              fontSize: fontSize),
        ),
      ),
    ));

    _middle.forEach((v) {
      widgets.add(new InkWell(
        onTap: () {
          selectTab(v);
        },
        child: new Container(
          padding: EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
          height: height,
          width: itemWidth,
          alignment: Alignment.center,
          decoration: new BoxDecoration(
            color: selectOne['label'] == v['label'] ? Theme.of(context).primaryColor : DefaultConfig().configs.PRIMARY_COLOR_LIGHT,
            borderRadius: BorderRadius.all(Radius.circular(20.0)),
          ),
          child: new Text(
            selectOne['label'] == v['label'] ? v['activeLabel'] ?? v['label'] : v['label'],
            style: new TextStyle(
                color: selectOne['label'] == v['label'] ? Colors.white : Theme.of(context).primaryColor,
                fontSize: fontSize),
          ),
        ),
      ));
    });

    widgets.add(new InkWell(
      onTap: () {
        selectTab(_last);
      },
      child: new Container(
        padding: EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
        height: height,
        width: itemWidth,
        alignment: Alignment.center,
        decoration: new BoxDecoration(
          color: selectOne['label'] == _last['label'] ? Theme.of(context).primaryColor : DefaultConfig().configs.PRIMARY_COLOR_LIGHT,
          borderRadius: BorderRadius.all(Radius.circular(20.0)),
          // border: Border(
          //     top: BorderSide(width: 1.0, color: Colors.white),
          //     right: BorderSide(width: 1.0, color: Colors.white),
          //     bottom: BorderSide(width: 1.0, color: Colors.white)),
        ),
        child: new Text(
          selectOne['label'] == _last['label'] ? _last['activeLabel'] ?? _last['label'] : _last['label'],
          style: new TextStyle(
              color: selectOne['label'] == _last['label'] ? Colors.white : Theme.of(context).primaryColor,
              fontSize: fontSize),
        ),
      ),
    ));

    return widgets;
  }

  @override
  Widget build(BuildContext context) {
    return new Container(
      width: widget.tabs.length * itemWidth,
      decoration: BoxDecoration(
        color: DefaultConfig().configs.PRIMARY_COLOR_LIGHT,
        borderRadius: BorderRadius.all(Radius.circular(20.0)),
      ),
      child: new Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: buildTabs(),
      ),
    );
  }
}
