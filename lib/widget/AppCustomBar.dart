import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppCustomerBar extends StatelessWidget implements PreferredSizeWidget {
  final Widget title;
  final bool showLeading;
  final double leadingWidth;
  final List<Widget>? actions;
  final PreferredSizeWidget? bottom;
  final Color? color;
  final Color? iconColor;
  final double? elevation;
  final Brightness? brightness;

  AppCustomerBar(
      {required this.title,
      this.showLeading = true,
      this.leadingWidth = 60,
      this.actions,
      this.bottom,
      this.color,
      this.iconColor,
      this.elevation,
      this.brightness});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      leading: showLeading
          ? Container(
              width: leadingWidth,
              child: IconButton(
                icon: Icon(Icons.arrow_back_ios_new, color: this.iconColor ?? DefaultConfig().configs.BLACK_COLOR),
                onPressed: () {
                  Navigator.of(context).pop(true);
                },
              ),
            )
          : Container(width: leadingWidth),
      // iconTheme: IconThemeData(
      //   color: this.iconColor ?? DefaultConfig().configs.BLACK_COLOR, //change your color here
      // ),
      backgroundColor: this.color ?? DefaultConfig().configs.WHITE_COLOR,
      elevation: this.elevation ?? 0.0,
      title: title,
      centerTitle: true,
      actions: actions,
      bottom: bottom,
      systemOverlayStyle: SystemUiOverlayStyle(statusBarBrightness: this.brightness ?? Brightness.dark),
    );
  }

  // TODO: implement preferredSize
  @override
  Size get preferredSize => AppBar().preferredSize;
}
