import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:flutter/material.dart';

/// 带图标的输入框
class CabinetBoxListWidget extends StatefulWidget {
  final List<dynamic> list;
  final bool showPrice;

  CabinetBoxListWidget(this.list, {this.showPrice = true});

  @override
  _CabinetBoxListWidgetState createState() => new _CabinetBoxListWidgetState();
}

/// State for [InorderWidget] widgets.
class _CabinetBoxListWidgetState extends State<CabinetBoxListWidget> {
  int firstIndexType = 0;

  ///格口列表
  List<Widget> buildAvailableGK() {
    return widget.list.map((item) {
      double width = (MediaQuery.of(context).size.width - 45) / 5;
      return Offstage(
          offstage: item['availableNum'] == 0 && (item['type'] == 0 || item['type'] == 6),
          child: Container(
              width: width,
              child: Container(
                decoration: BoxDecoration(
                    border: firstIndexType == item['type']
                        ? Border()
                        : Border(
                            left: BorderSide(color: Color(0xFFE5E5E5), width: 1.0),
                          )),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '${item['availableNum'] > 0 ? item['availableNum'] : '-'}',
                      style: TextStyle(fontWeight: FontWeight.w600, fontSize: 18, color: Colors.black),
                    ),
                    Text(
                      '${DefaultConfig().configs.CABINET_BOX_TYPE[item['type']]}',
                      style: TextStyle(fontWeight: FontWeight.w600, fontSize: 11, color: Colors.black),
                    ),
                    Offstage(
                      offstage: !widget.showPrice,
                      child: Text(
                        '${item['price'] / 1000}元',
                        style: TextStyle(fontSize: 10, fontWeight: FontWeight.w600, color: Colors.black),
                      ),
                    )
                  ],
                ),
                padding: EdgeInsets.fromLTRB(15, 10, 10, 10),
              )));
    }).toList();
  }

  getFirstIndex(emptyList) {
    if (emptyList.length > 0) {
      if (emptyList[0]['type'] == 6 && emptyList[0]['availableNum'] == 0) {
        firstIndexType = emptyList[1]['type'];
      } else {
        firstIndexType = emptyList[0]['type'];
      }
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    getFirstIndex(widget.list);
    return Container(
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: buildAvailableGK(),
        ),
      ),
    );
  }
}
