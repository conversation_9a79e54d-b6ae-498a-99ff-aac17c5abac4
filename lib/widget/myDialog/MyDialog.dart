import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:flutter/material.dart';

const colorH = Color(0xFFEEEEEE);
const colorWithQ = Color(0xFF2989F2);

class MyDialog extends StatefulWidget {
  double width;
  double miniHeight;
  double maxHeight;
  bool autoClose;
  bool showClose;
  String? title;
  TextStyle? titleStyle;
  Widget? titleWidget;
  String? content;
  Widget? contentWidget;

  /// 自定义按钮list 优先级：widgetButtons > buttons
  final List<Widget?>? customWidgetButtons;

  /// 按钮list
  final List<String>? buttons;

  Widget? otherButtons;

  /// 点击返回index 0 1
  final Function(int index, BuildContext context)? onTap;

  MyDialog({this.width = 300,
    this.miniHeight = 200,
    this.maxHeight = 500,
    this.autoClose = false,
    this.showClose = true,
    this.title = '标题',
    this.titleStyle,
    this.titleWidget,
    this.content = '内容',
    this.contentWidget,
    this.customWidgetButtons,
    this.buttons,
    this.otherButtons,
    this.onTap,
    Key? key})
      : super(key: key);

  @override
  _MyDialogState createState() => _MyDialogState();
}

class _MyDialogState extends State<MyDialog> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        child: Material(
            color: Colors.transparent,
            child: Center(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(6.0),
                child: Container(
                    color: Colors.white,
                    constraints: BoxConstraints(
                      maxWidth: widget.width,
                      minHeight: widget.miniHeight,
                      maxHeight: widget.maxHeight,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Padding(
                            padding: EdgeInsets.only(top: 20.0, left: 20.0, right: 20.0),
                            child: Stack(
                              children: [
                                buildTitle(),
                                Align(
                                    alignment: Alignment.centerRight,
                                    child: (widget.showClose == false)
                                        ? Container()
                                        : InkWell(
                                      child: Icon(Icons.close, color: Color(0xFFB2B4C6)),
                                      onTap: () {
                                        Navigator.pop(context);
                                      },
                                    ))
                              ],
                            )),
                        SizedBox(height: widget.title == null ? 20 : 10),
                        buildContent(),
                        SizedBox(height: 10),
                        (widget.otherButtons != null) ? widget.otherButtons! : Container(),
                        Container(
                          decoration: BoxDecoration(border: Border(bottom: BorderSide(color: colorH, width: 1))),
                        ),
                        (widget.customWidgetButtons != null) ? _createCustomButtons() : _createDefaultButtons()
                      ],
                    )),
              ),
            )),
        onWillPop: () async {
          return widget.autoClose;
        });
  }

  buildTitle() {
    return Align(
      alignment: Alignment.center,
      child: widget.titleWidget != null
          ? widget.titleWidget!
          : Container(
        width: double.infinity,
        child: Text(
          widget.title!,
          textAlign: TextAlign.center,
          style: TextStyle(color: Color(0xFF121D34), fontSize: 17, fontWeight: FontWeight.w500),
        ),
      ),
    );
  }

  buildContent() {
    return Expanded(
        child: Container(
            width: double.infinity, // 跟外部容器等宽
            margin: EdgeInsets.only(left: 20, right: 20),
            child: (widget.contentWidget != null)
                ? widget.contentWidget
                : Text(
              widget.content!,
              textAlign: TextAlign.center,
              style: TextStyle(color: Color(0xFF121D34), fontSize: 14, fontWeight: FontWeight.w400),
            )));
  }

  _createCustomButtons() {}

  _createDefaultButtons() {
    return (widget.buttons == null)
        ? Container()
        : Container(
      child: Row(
        children: widget.buttons!.map((res) {
          int index = widget.buttons!.indexOf(res);
          return Expanded(
            flex: 1,
            child: Container(
                child: defaultCustomButton(context,
                    needCloseDialog: false,
                    text: res,
                    textColor: (index == 0) ? Color(0xFF999999) : Theme
                        .of(context)
                        .primaryColor,
                    buttonHeight: 56.0,
                    onTap: () {
                      if (widget.onTap != null) {
                        widget.onTap!(index, context);
                      }
                    }),
                decoration: (index == widget.buttons!.length - 1)
                    ? BoxDecoration()
                    : BoxDecoration(
                  border: Border(
                    right: BorderSide(
                      color: colorH,
                      width: 1.0,
                    ),
                  ),
                )),
          );
        }).toList(),
      ),
    );
  }
}

/// 默认自定义按钮（中间、底部弹窗自定义按钮）
Widget defaultCustomButton(context,
    {text = '确认',
      textColor = colorWithQ,
      textFontSize = 17.0,
      fontWeight = FontWeight.w500,
      needCloseDialog = true,
      buttonHeight = 44.0,
      backgroundColor = Colors.white,
      onTap}) =>
    TextButton(
      onPressed: () {
        if (needCloseDialog) {
          Navigator.pop(context);
        }
        if (onTap != null) {
          onTap();
        }
      },
      style: ButtonStyle(
        minimumSize: MaterialStateProperty.all(Size(60, buttonHeight)),
        shape: MaterialStateProperty.all(RoundedRectangleBorder(borderRadius: BorderRadius.circular(0))),
        //背景颜色
        backgroundColor: MaterialStateProperty.resolveWith((states) {
          //设置按下时的背景颜色
          if (states.contains(MaterialState.pressed)) {
            return Theme
                .of(context)
                .primaryColorLight;
          }
          //默认背景颜色
          return backgroundColor;
        }),
      ),
      child: Text(text, style: TextStyle(fontSize: textFontSize, color: textColor, fontWeight: fontWeight)),
    );
