import 'dart:io';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:flutter/material.dart';

enum ButtonType { primary, primaryDisabled, success, error, warning, tiffany, forbidden, info }

/// 带图标的输入框
class BottomBtnWidget extends StatefulWidget {
  /// 按钮名称
  final String title;

  /// 按钮类型 [type] 默认 'primary'
  /// @range {'primary', 'primary-disabled', 'success', 'error','warning','tiffany','forbidden', info}
  final ButtonType type;

  /// 点击事件
  final GestureTapCallback? action;

  /// 宽度
  final double? width;

  /// 是否显示顶部阴影
  final bool showShadow;

  /// 按钮形状
  ///  @range {'circle', 'rectangle'}
  final String shape;

  /// 默认按钮文字大小 默认 15
  final double fontSize;

  BottomBtnWidget(
      {this.type = ButtonType.primary,
      this.title = '新增',
      this.action,
      this.width,
      this.showShadow = true,
      this.fontSize = 15,
      this.shape = 'circle'});

  @override
  _BottomBtnWidgetState createState() => new _BottomBtnWidgetState();
}

/// State for [InorderWidget] widgets.
class _BottomBtnWidgetState extends State<BottomBtnWidget> {
  getBtnType(type) {
    Map<ButtonType, dynamic> map = {
      ButtonType.primary: [Theme.of(context).primaryColor, DefaultConfig().configs.WHITE_COLOR],
      ButtonType.info: [Colors.white, Colors.black],
      ButtonType.primaryDisabled: [DefaultConfig().configs.PRIMARY_COLOR_LIGHT, DefaultConfig().configs.WHITE_COLOR],
      ButtonType.success: [DefaultConfig().configs.SUCCESS_COLOR, DefaultConfig().configs.SUCCESS_COLOR_GHOST],
      ButtonType.error: [DefaultConfig().configs.ERROR_COLOR, DefaultConfig().configs.ERROR_COLOR_GHOST],
      ButtonType.warning: [DefaultConfig().configs.WARNING_COLOR, DefaultConfig().configs.WARNING_COLOR_GHOST],
      ButtonType.tiffany: [DefaultConfig().configs.TIFFANY_COLOR, DefaultConfig().configs.TIFFANY_COLOR_GHOST],
      ButtonType.forbidden: [DefaultConfig().configs.DISABLED_COLOR, DefaultConfig().configs.DISABLED_COLOR_GHOST]
    };
    return map[type];
  }

  @override
  Widget build(BuildContext context) {
    List<dynamic> colors = getBtnType(widget.type);
    return new Container(
      padding: EdgeInsets.only(top: 10, bottom: Platform.isIOS ? 15 : 10),
      decoration: widget.showShadow
          ? BoxDecoration(boxShadow: [
              BoxShadow(color: Colors.grey.shade300, offset: Offset(0, 0), blurRadius: 0),
              BoxShadow(color: Colors.white, offset: Offset(0, 2.5), blurRadius: 0)
            ])
          : BoxDecoration(color: Colors.white),
      child: new Row(
        children: <Widget>[
          new Padding(padding: EdgeInsets.only(left: 10)),
          new Container(
            width: widget.width ?? MediaQuery.of(context).size.width - 20,
            child: Material(
                color: Colors.white,
                child: Ink(
                  decoration: new BoxDecoration(
                      color: colors[0],
                      border: Border.all(
                          width: 1,
                          color: widget.type == ButtonType.info ? DefaultConfig().configs.INFO_COLOR : colors[0]),
                      borderRadius: widget.shape == 'circle'
                          ? BorderRadius.all(Radius.circular(20.0))
                          : BorderRadius.all(Radius.circular(4.0))),
                  child: new InkResponse(
                    enableFeedback: widget.type == ButtonType.primaryDisabled,
                    borderRadius: widget.shape == 'circle'
                        ? BorderRadius.all(Radius.circular(20.0))
                        : BorderRadius.all(Radius.circular(4.0)),
                    onTap: () {
                      if (widget.action != null) {
                        return widget.action!();
                      }
                    },
                    //  splashColor: Colors.black,//水波纹的颜色
                    radius: 300.0,
                    highlightShape: BoxShape.rectangle,
                    containedInkWell: true,
                    //true表示要剪裁水波纹响应的界面   false不剪裁  如果控件是圆角不剪裁的话水波纹是矩形
                    child: new Container(
                      height: 40.0,
                      alignment: Alignment.center,
                      child: new Text(
                        '${widget.title}',
                        style: new TextStyle(color: colors[1], fontSize: widget.fontSize),
                      ),
                    ),
                  ),
                )),
          ),
          new Padding(padding: EdgeInsets.only(left: 10)),
        ],
      ),
    );
  }
}
