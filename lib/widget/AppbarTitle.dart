import 'package:flutter/material.dart';

class AppbarTitle extends StatelessWidget {
  final String? title;

  final String? subtitle;

  final CrossAxisAlignment? crossAxisAlignment;

  final bool isCenter;

  AppbarTitle({@required this.title, this.subtitle, this.crossAxisAlignment, this.isCenter = false});

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: isCenter ? CrossAxisAlignment.center : CrossAxisAlignment.start,
//      crossAxisAlignment: crossAxisAlignment == null ? CrossAxisAlignment.center : crossAxisAlignment,
      children: <Widget>[
        Text(
          title!,
          style: TextStyle(fontSize: 18.0, color: Colors.black),
        ),
        subtitle == null || subtitle == ""
            ? Container()
            : Text(
                subtitle!,
                style: TextStyle(fontSize: 12.0),
              ),
      ],
    );
  }
}
