import 'package:cabinet_flutter_app/widget/DateTimePicker.dart';
import 'package:flutter/material.dart';

/// 带图标的输入框
class TopSearchTimeWidget extends StatefulWidget {
  final callBack;

  TopSearchTimeWidget({Key? key, this.callBack}) : super(key: key);

  @override
  _TopSearchTimeWidgetState createState() => new _TopSearchTimeWidgetState();
}

/// State for [AppFormInputWidget] widgets.
class _TopSearchTimeWidgetState extends State<TopSearchTimeWidget> {
  _TopSearchTimeWidgetState() : super();
  List<String> list = ['今天', '昨天', '近三日', '近一周', '近一月', '自定义'];
  String _selected = '昨天';
  DateTime beginTime = DateTime.now();
  DateTime endTime = DateTime.now();
  bool isShow = false;

  @override
  void initState() {
    super.initState();
    _setTime(_selected);
  }

  _setTime(value) {
    setState(() {
      _selected = value;
      switch (value) {
        case '今天':
          beginTime = DateTime.now();
          endTime = DateTime.now();
          break;
        case '昨天':
          beginTime = DateTime.now().subtract(new Duration(days: 1));
          endTime = DateTime.now().subtract(new Duration(days: 1));
          break;
        case '近三日':
          beginTime = DateTime.now().subtract(new Duration(days: 2));
          endTime = DateTime.now();
          break;
        case '近一周':
          beginTime = DateTime.now().subtract(new Duration(days: 6));
          endTime = DateTime.now();
          break;
        case '近一月':
          beginTime = DateTime.now().subtract(new Duration(days: 30));
          endTime = DateTime.now();
          break;
        case '自定义':
          setState(() {
            isShow = true;
          });
          break;
      }
      if (value != '自定义') {
        setState(() {
          isShow = false;
        });
//        beginTime = DateTime.now();
//        endTime = DateTime.now();
        String begin = (beginTime.toString()).substring(0, 11) + '00:00:00';
        String end = (endTime.toString()).substring(0, 11) + '23:59:59';
        widget.callBack(begin, end);
      }
    });
  }

  _buildItem() {
    List<Widget> chioce = list.map((v) {
      return new Expanded(
        child: new InkWell(
          onTap: () {
            _setTime(v);
          },
          child: new Container(
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
            ),
            child: new Container(
              padding: EdgeInsets.fromLTRB(0.0, 2.0, 0.0, 2.0),
              decoration: BoxDecoration(
                  border: Border(
                      bottom: BorderSide(
                          color: _selected == v ? Colors.white : Theme.of(context).primaryColor, width: 2.0))),
              child: new Text(
                v,
                style: new TextStyle(fontSize: 16.0, color: _selected == v ? Colors.white : const Color(0xFFD3D3D3)),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ),
      );
    }).toList();
    return new Row(
      children: chioce,
    );
  }

  _buildDateTimePicker() {
    return new Container(
      color: Theme.of(context).primaryColor,
      padding: EdgeInsets.fromLTRB(20.0, 5.0, 20.0, 5.0),
      child: new Row(
        children: <Widget>[
          new Expanded(
            child: DateTimePicker(
              isNormal: false,
              labelText: '开始时间',
              selectedDate: beginTime,
              selectDate: (DateTime date) {
                setState(() {
                  beginTime = date;
                });
                String begin = (date.toString()).substring(0, 11) + '00:00:00';
                String end = (endTime.toString()).substring(0, 11) + '23:59:59';
                widget.callBack(begin, end);
              },
            ),
          ),
          new Container(
            padding: EdgeInsets.fromLTRB(15.0, 0.0, 15.0, 0.0),
            child: new Center(
              child: new Text(
                '至',
                style: new TextStyle(fontSize: 18.0, color: Colors.white),
              ),
            ),
          ),
          new Expanded(
            child: DateTimePicker(
              isNormal: false,
              labelText: '结束时间',
              selectedDate: endTime,
              selectDate: (DateTime date) {
                setState(() {
                  endTime = date;
                });
                String begin = (beginTime.toString()).substring(0, 11) + '00:00:00';
                String end = (date.toString()).substring(0, 11) + '23:59:59';
                widget.callBack(begin, end);
              },
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return new Column(
      children: <Widget>[
        _buildItem(),
        (isShow) ? _buildDateTimePicker() : new Container(),
      ],
    );
  }
}
