import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:flutter/material.dart';

/// 搜索输入框
/// Created by denonzhu
/// Date: 2018-07-20
class AppSearchInputWidget extends StatelessWidget {
  final ValueChanged<String>? onChanged;

  final ValueChanged<String>? onSubmitted;

  final VoidCallback? onSubmitPressed;

  final VoidCallback? valueClear;

  final String? hitText;
  final Icon? leftIcon;

  final FocusNode? focusNode;

  final bool? autofocus;

  final TextEditingController? controller;

  final TextInputAction? textInputAction;

  final bool? showIcon;

  final IconData? icon;

  final Color? iconColor;

  final double? borderRadius;

  final double? opacity;

  final Color? bgColor;

  final String? value;

  AppSearchInputWidget(
      {this.onChanged,
      this.onSubmitted,
      this.onSubmitPressed,
      this.valueClear,
      this.hitText,
      this.leftIcon,
      this.focusNode,
      this.autofocus = false,
      this.controller,
      this.textInputAction = TextInputAction.done,
      this.showIcon,
      this.icon,
      this.iconColor,
      this.borderRadius = 5.0,
      this.opacity = 1.0,
      this.bgColor = Colors.white,
      this.value});

  @override
  Widget build(BuildContext context) {
    if (focusNode != null) {
      focusNode?.addListener(() {
        if (focusNode?.hasFocus != null) {
          controller?.selection = TextSelection(baseOffset: 0, extentOffset: controller?.text.length ?? 0);
        }
      });
    }
    return Opacity(
      opacity: opacity ?? 1,
      child: Container(
        height: 40,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(borderRadius ?? 0)),
          color: bgColor,
        ),
        padding: EdgeInsets.only(left: leftIcon == null ? 15 : 0.0, top: 0, right: 5.0, bottom: 0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Expanded(
              child: TextField(
                focusNode: focusNode,
                controller: controller,
                autofocus: autofocus!,
                maxLines: 1,
                maxLength: 24,
                keyboardType: TextInputType.phone,
                textInputAction: textInputAction,
                decoration: InputDecoration(
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(vertical: 12.0),
                  hintText: hitText,
                  prefixIcon: leftIcon ?? null,
                  hintStyle: TextStyle(
                    color: Color(AppColors.subTextColor),
                    fontSize: AppConstant.smallTextSize,
                  ),
                  counterText: "", //此处控制最大字符是否显示
                ),
                style: TextStyle(
                  color: Color(AppColors.mainTextColor),
                  fontSize: AppConstant.smallTextSize,
                  textBaseline: TextBaseline.alphabetic,
                ),
                onChanged: onChanged,
                onSubmitted: onSubmitted,
              ),
            ),
            (value != null && value != '')
                ? RawMaterialButton(
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    padding: const EdgeInsets.only(right: 5.0, left: 10.0),
                    constraints: const BoxConstraints(minWidth: 0.0, minHeight: 0.0),
                    child: Icon(
                      Icons.clear,
                      size: 20.0,
                      color: Colors.grey.shade500,
                    ),
                    onPressed: valueClear,
                  )
                : (showIcon != null && showIcon!
                    ? RawMaterialButton(
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        padding: const EdgeInsets.only(right: 5.0, left: 10.0),
                        constraints: const BoxConstraints(minWidth: 0.0, minHeight: 0.0),
                        child: Icon(
                          this.icon != null ? this.icon : AppICons.SEARCH,
                          size: 20.0,
                          color: iconColor ?? Theme.of(context).primaryColorDark,
                        ),
                        onPressed: onSubmitPressed,
                      )
                    : Container()),
          ],
        ),
      ),
    );
  }
}
