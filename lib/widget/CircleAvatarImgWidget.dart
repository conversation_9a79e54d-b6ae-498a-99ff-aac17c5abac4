import 'package:flutter/material.dart';

class CircleAvatarImgWidget extends StatefulWidget {
  final double width;
  final double height;
  final double radius;
  final String title;
  final String url;
  final EdgeInsetsGeometry edgeInsets;

  CircleAvatarImgWidget(
      {Key? key,
      this.title = '',
      this.width = 50.0,
      this.height = 50.0,
      this.radius = 20.0,
      this.url = 'static/images/logo.png',
      this.edgeInsets = const EdgeInsets.fromLTRB(0, 0, 10, 0)})
      : super(key: key);

  @override
  _CircleAvatarImgWidgetState createState() => _CircleAvatarImgWidgetState();
}

class _CircleAvatarImgWidgetState extends State<CircleAvatarImgWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return new Container(
      width: widget.width,
      height: widget.height,
      margin: widget.edgeInsets,
      child: new Center(
        child: new CircleAvatar(
          radius: widget.radius,
          backgroundColor: Colors.white,
          backgroundImage: AssetImage(widget.url),
        ),
      ),
    );
  }
}
