/// 我的收件
import 'dart:async';

import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/entitys/package_view_sj_entity.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/widget/AppListState.dart';
import 'package:cabinet_flutter_app/widget/AppPullLoadWidget.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CustomerDropDownSelectWidget.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/DateTimeRange.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/MakeWaybillWidget.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/PackageStayWidget.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';

import '../../common/config/DefaultConfig.dart';
import '../NoResult.dart';

class CustomerSearchQuery {
  String startTime = new DateFormat("yyyy-MM-dd").format(DateTime.now());
  String timeRanger = 'all';
  String endTime = new DateFormat("yyyy-MM-dd").format(DateTime.now());
  String? queryType = '0';
}

class PackageStayListWidget extends StatefulWidget {
  final PackageType type;
  final String? cabinetCode;
  final double marginTop;

  PackageStayListWidget(this.type, this.cabinetCode, {Key? key, this.marginTop = 0}) : super(key: key);

  @override
  PackageListSjWidgetState createState() => PackageListSjWidgetState();
}

class PackageListSjWidgetState extends State<PackageStayListWidget>
    with AutomaticKeepAliveClientMixin<PackageStayListWidget>, AppListState<PackageStayListWidget> {
  int total = 0;
  int failTotal = 0;
  CustomerSearchQuery query = new CustomerSearchQuery();
  Map<dynamic, dynamic> listTypeState = {
    PackageType.DS: ListType.WAIT,
    PackageType.YS: ListType.REMOVED,
    PackageType.YWC: ListType.COMPLETE
  };

  final Map<String, dynamic>? queryMap = {'0': '全部', '9': '包裹已寄出', '5': '快递员拒收', '6': '快递员取消'};
  bool hasFeedBack = false;
  bool isOver7 = false;
  Timer? boxStatusTimer;
  bool isCheckBox = true;
  List statusList = ['5', '6', '9'];

  @override
  initState() {
    this.initData();
    super.initState();
  }

  initData() async {
    setDefaultQuery();
    handleRefresh();
  }

  /// 设置查询条件默认值
  setDefaultQuery() {
    /// 所有快递品牌
    query.timeRanger = 'all';
    query.startTime = new DateFormat("yyyy-MM-dd").format(DateTime.now());
    query.endTime = new DateFormat("yyyy-MM-dd").format(DateTime.now());
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<Null> onFresh() async {
    handleRefresh();
  }

  @protected
  Future<Null> handleRefresh({PackageType? type}) async {
    if (isLoading) {
      return null;
    }
    refreshIndicatorKey.currentState?.show();
    isLoading = true;
    page = 1;
    var res = await requestRefresh(type: type);
    resolveRefreshResult(res);
    resolveDataResult(res);
    if (res.next != null) {
      var resNext = await res.next;
      resolveRefreshResult(resNext);
      resolveDataResult(resNext);
    }
    isLoading = false;
    return null;
  }

  // 上拉加载更多
  _getData({isRefresh = false, type}) async {
    DataResult result = new DataResult('', false, total: 0);
    if (type == null) {
      type = widget.type;
    }
    if (type == PackageType.DS) {
      // 待收件
      result = await CourierDao.courierSjCabinetOrderPagList(page.toString(), '20');
    } else if (type == PackageType.YS) {
      statusList = [];
      statusList.add(4);
      // 已收件
      result = await CourierDao.courierSjSendTakenPagList(statusList, page.toString(), '20');
    } else if (type == PackageType.YWC) {
      if (query.queryType == '0') {
        statusList = [];
        statusList.add('5');
        statusList.add('6');
        statusList.add('9');
      }
      // 已完成
      result = await CourierDao.courierSjSendTakenPagList(statusList, page.toString(), '20', ymd: query.startTime);
    }
    List<dynamic> list = [];
    if (result.data.length > 0) {
      result.data.forEach((item) {
        list.add(item);
      });
    } else {
      list = result.data;
    }
    total = result.total!;
    return new DataResult(list, true, total: total);
  }

  Future<Null> onLoadMore() async {
    if (isLoading) {
      return null;
    }
    isLoading = true;
    page++;
    var res = await requestLoadMore();
    if (res != null && res.result) {
      setState(() {
        pullLoadWidgetControl.dataList.addAll(res.data);
      });
    }
    resolveDataResult(res);
    isLoading = false;
    return null;
  }

  _renderShortItem(int index) {
    PackageViewSjEntity item = dataList[index];
    return PackageStayWidget(PackageType.DS, indexNo: dataList.length - index, package: item, bindTap: () {
      // NavigatorUtils.goPackageDetailPage(context, item);
    }, bottom: buildBottom(item));
  }

  _renderEventItem(int index) {
    PackageViewSjEntity item = dataList[index];
    return PackageStayWidget(widget.type,
        indexNo: dataList.length - index, package: item, bindTap: () {
      // NavigatorUtils.goPackageDetailPage(context, item);
    }, bottom: buildBottom(item));
  }

  buildBottom(PackageViewSjEntity item) {
    return Container(
      child: Column(
        children: [
          Offstage(
            offstage: widget.type != PackageType.DS,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Icon(
                  Icons.help,
                  size: 15,
                  color: Theme.of(context).primaryColor,
                ),
                Text(
                  '请至对应柜机并扫描快递员二维码进行取出操作',
                  style: TextStyle(fontSize: 12, color: Theme.of(context).primaryColor),
                ),
              ],
            ),
          ),
          Offstage(
            offstage: widget.type == PackageType.DS,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Offstage(
                  offstage: widget.type == PackageType.YWC,
                  child: OutlinedButton(
                    child: Text("取消订单",
                        style: TextStyle(fontSize: AppConstant.smallTextSize, color: Theme.of(context).primaryColor)),
                    onPressed: () {
                      pickOrderOut(item);
                    },
                    style: ButtonStyle(
                        minimumSize: MaterialStateProperty.all(Size(80, 32)),
                        shape: MaterialStateProperty.all(StadiumBorder()),
                        side: MaterialStateProperty.all(BorderSide(color: Theme.of(context).primaryColor))),
                  ),
                ),
                Offstage(
                  offstage: item.storeType == 1 ? false : true,
                  child: ElevatedButton(
                    child: Text(widget.type == PackageType.YWC ? '修改单号' : "回填单号",
                        style:
                            TextStyle(fontSize: AppConstant.smallTextSize, color: DefaultConfig().configs.WHITE_COLOR)),
                    onPressed: () {
                      Navigator.push(context, MaterialPageRoute(builder: (BuildContext context) {
                        return MakeWaybillWidget();
                      })).then((waybillNo) {
                        if (waybillNo != null) {
                          backOrderNo(item, waybillNo);
                        }
                      });
                    },
                    style: ButtonStyle(
                        minimumSize: MaterialStateProperty.all(Size(80, 32)),
                        shape: MaterialStateProperty.all(StadiumBorder()),
                        side: MaterialStateProperty.all(BorderSide(color: Theme.of(context).primaryColor)),
                        backgroundColor: MaterialStateProperty.all(Theme.of(context).primaryColor)),
                  ),
                ),
              ],
            ),
          )
        ],
      ),
      margin: EdgeInsets.only(top: 7),
    );
  }

  /// 取出
  pickOrderOut(PackageViewSjEntity item) {
    CommonUtils.customConfirmByReason(context, '是否确认取消订单？', (backReason) async {
      print(backReason);
      LoadingUtil(
        status: '订单正在取消...',
      ).show(context);
      var res = await CourierDao.courierOutboundWaybillSj(item.cabinetLocationCode!, item.id!, backReason, false);
      LoadingUtil.dismiss(context);
      if (res.result) {
        Navigator.of(context).pop();
        handleRefresh();
        Fluttertoast.showToast(msg: ' 订单取消成功');
      }
    }, changeText: 'changeText', title: '取消订单', showClose: false, showInput: true);
  }

  /// 回填单号
  backOrderNo(PackageViewSjEntity item, waybillNo) async {
    LoadingUtil(
      status: '单号上传中...',
    ).show(context);
    var res = await CourierDao.courierBackOrderNo(item.id!, waybillNo);
    LoadingUtil.dismiss(context);
    if (res.result) {
      handleRefresh();
      Fluttertoast.showToast(msg: ' 单号上传成功');
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Container(
      child: Column(
        children: <Widget>[
          /// 头部筛选项框
          Container(
            color: Colors.transparent,
            height: widget.type == PackageType.YWC ? 48 : 10,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Padding(padding: EdgeInsets.only(left: 10)),
                widget.type == PackageType.YWC
                    ? Container(
                        constraints: BoxConstraints(maxWidth: 80),
                        child: DateTimeRangeWidget(
                          value: DateTime.parse(query.startTime),
                          cb: (date) {
                            if (!isSameDay(date, DateTime.parse(query.startTime))) {
                              setState(() {
                                query.startTime = new DateFormat("yyyy-MM-dd").format(date);
                              });
                              handleRefresh();
                            }
                          },
                        ))
                    : Container(),
                Offstage(
                  offstage: widget.type != PackageType.DS || widget.type == PackageType.YWC,
                  child: Padding(padding: EdgeInsets.only(left: 10)),
                ),
                Padding(padding: EdgeInsets.only(left: 10)),
                widget.type == PackageType.YWC
                    ? Expanded(
                        flex: 9,
                        child: CustomerDropDownSelectWidget(
                          hintText: '全部',
                          map: queryMap!,
                          value: query.queryType ?? '0',
                          cb: (item) {
                            setState(() {
                              query.queryType = item;
                              statusList = [];
                              if (query.queryType == '0') {
                                statusList.add('5');
                                statusList.add('6');
                                statusList.add('9');
                              } else {
                                statusList.add(query.queryType!);
                              }
                            });
                            handleRefresh();
                          },
                        ))
                    : Container(),
              ],
            ),
          ),
          new Expanded(
            child: dataList.length > 0
                ? Stack(
                    children: [
                      Positioned(
                          top: widget.marginTop,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          child: new AppPullLoadWidget(
                            pullLoadWidgetControl,
                            (BuildContext context, int index) =>
                                widget.type == PackageType.DS ? _renderShortItem(index) : _renderEventItem(index),
                            handleRefresh,
                            onLoadMore,
                            refreshKey: refreshIndicatorKey,
                          ))
                    ],
                  )
                : RefreshIndicator(
                    key: refreshIndicatorKey,
                    child: SingleChildScrollView(
                      physics: AlwaysScrollableScrollPhysics(),
                      child: Container(
                        height: 300,
                        child: NoResult(
                            size: 64,
                            type: 'box',
                            subWidget: Container(
                                padding: EdgeInsets.only(top: 10), child: Text('', style: AppConstant.smallSubText))),
                      ),
                    ),
                    onRefresh: handleRefresh),
          )
        ],
      ),
    );
  }

  // TODO: implement wantKeepAlive
  @override
  bool get wantKeepAlive => true;

  // TODO: implement isRefreshFirst
  @override
  bool get isRefreshFirst => true;

  @override
  requestLoadMore() async {
    return await _getData();
  }

  @override
  requestRefresh({PackageType? type}) async {
    return await _getData(isRefresh: true, type: type);
  }

  clearData() {
    pullLoadWidgetControl.dataList.clear();
  }
}
