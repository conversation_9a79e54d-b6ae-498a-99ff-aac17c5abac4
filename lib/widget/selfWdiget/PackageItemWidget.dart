import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/WaybillDao.dart';
import 'package:cabinet_flutter_app/common/entitys/package_view_entity.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/ThrottleUtil.dart';
import 'package:cabinet_flutter_app/common/utils/text_util.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CommonRowWidget.dart';
import 'package:flutter/material.dart';

/// 带图标的输入框
class PackageItemWidget extends StatefulWidget {
  final PackageType type;
  final PackageViewEntity package;
  final int indexNo;
  final VoidCallback? bindTap;
  final Widget? bottom;
  final int overDueDays;

  PackageItemWidget(this.type,
      {required this.package, required this.indexNo, this.bindTap, this.bottom, this.overDueDays = 7});

  @override
  _PackageItemWidgetState createState() => new _PackageItemWidgetState();
}

/// State for [InorderWidget] widgets.
class _PackageItemWidgetState extends State<PackageItemWidget> {
  ThrottleUtil throttleUtil = ThrottleUtil();

  @override
  Widget build(BuildContext context) {
    String day = '';
    bool isOverDue = false;
    PackageViewEntity? package = widget.package;
    day = CheckUtils.countDay(package.keepEffectTime);
    isOverDue = CheckUtils.checkEffectTimeIsOverTime(package.keepEffectTime, overDueDay: widget.overDueDays);
    getPackageStatus() {
      if (package.hasOutbound == 0) {
        return package.keepEffect ? '滞留件' : '待取件';
      } else {
        return DefaultConfig().configs.OUTBOUND_TYPE[package.outboundType] ?? '';
      }
    }

    Color bgColor = package.hasOutbound == 0
        ? isOverDue
            ? DefaultConfig().configs.PRIMARY_COLOR_LIGHT
            : Colors.white
        : Colors.white;
    reNotice() {
      WaybillDao.reNotice(context, widget.package.receiverMobile, widget.package.id);
    }

    return Container(
      margin: EdgeInsets.fromLTRB(10, 0, 10, 10),
      decoration: new BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(
            Radius.circular(7),
          )),
      child: Material(
        color: bgColor,
        borderRadius: BorderRadius.all(
          Radius.circular(7),
        ),
        child: Ink(
          decoration: new BoxDecoration(
              borderRadius: BorderRadius.all(
            Radius.circular(7),
          )),
          child: InkWell(
            focusColor: bgColor,
            borderRadius: BorderRadius.all(
              Radius.circular(7),
            ),
            onTap: () {
              widget.bindTap!();
            },
            child: Container(
              padding: EdgeInsets.all(10),
              child: Column(
                children: [
                  CommonRowWidget(
                    leftWidget: Row(
                      children: [
                        Text(
                            widget.type == ListType.SEARCH
                                ? TextUtil.formatDigitPattern(package.waybillNo ?? '', digit: 5, pattern: '  ')
                                : 'NO.${widget.indexNo.toString()}',
                            style: TextStyle(
                                fontSize: AppConstant.normalTextSize,
                                color: widget.type == ListType.SEARCH ? Colors.black : Theme.of(context).primaryColor,
                                fontWeight: FontWeight.bold)),
                        Offstage(
                          offstage: package.hasOutbound == 0 ? !isOverDue : true,
                          child: Text('（超${widget.overDueDays}日滞留件）',
                              style: TextStyle(
                                  fontSize: AppConstant.smallTextSize,
                                  color: Theme.of(context).primaryColor,
                                  fontWeight: FontWeight.w500)),
                        )
                      ],
                    ),
                    value: '',
                    rightWidget: Text(
                      package.hasOutbound == 0 ? '未取出' : '已取出',
                      style: TextStyle(fontSize: 16, color: package.hasOutbound == 0 ? Colors.red : Colors.green),
                    ),
                  ),
                  CommonRowWidget(
                    label: '快递品牌',
                    value: DefaultConfig().configs.EXPRESS2[package.brandCode],
                  ),
                  Offstage(
                    offstage: widget.type == ListType.SEARCH,
                    child: CommonRowWidget(
                      label: '单号',
                      rightWidget: Text(TextUtil.formatDigitPattern(package.waybillNo ?? '', digit: 5, pattern: '  '),
                          style: TextStyle(fontSize: AppConstant.smallTextSize, color: Colors.black)),
                    ),
                  ),
                  CommonRowWidget(
                    leftWidget: Row(
                      children: [
                        Text('收件人', style: TextStyle(fontSize: AppConstant.smallTextSize, color: Color(0xFF585858))),
                        InkWell(
                          onTap: () => throttleUtil.throttle(() {
                            reNotice();
                          }),
                          child: Row(
                            children: [
                              Padding(padding: EdgeInsets.only(left: 5)),
                              Image.asset(
                                LocalImageUtil.getImagePath('again'),
                                width: 15,
                              ),
                              Padding(padding: EdgeInsets.only(left: 5)),
                              Text('重新通知',
                                  style: TextStyle(
                                      fontSize: AppConstant.smallTextSize, color: Theme.of(context).primaryColor))
                            ],
                          ),
                        )
                      ],
                    ),
                    rightWidget: InkWell(
                        onTap: () async {
                          await CommonUtils.makePhoneCall(context, package.receiverMobile);
                        },
                        child: Row(
                          children: [
                            Icon(Icons.phone_in_talk, size: 16, color: Colors.blue),
                            Text(package.receiverMobile,
                                style: TextStyle(fontSize: AppConstant.smallTextSize, color: Colors.blue)),
                          ],
                        )),
                  ),
                  CommonRowWidget(
                    isShow: widget.type == ListType.SEARCH || widget.type == PackageType.DQ || widget.type ==PackageType.ZL,
                    label: '取件码',
                    value: package.checkCode ?? '',
                  ),
                  CommonRowWidget(
                    label: package.storeType == 1 ? '入柜时间' : '入仓时间',
                    value: package.inboundTime,
                  ),
                  Offstage(
                    offstage: package.hasOutbound == 0 ? true : false,
                    child: CommonRowWidget(
                      label: package.storeType == 1 ? '出柜时间' : '出仓时间',
                      value: package.outboundTime,
                    ),
                  ),
                  package.storeType == 1
                      ? CommonRowWidget(
                          label: '柜机名称',
                          value: package.cabinetLocationName,
                        )
                      : new Container(),
                  CommonRowWidget(
                    label: package.storeType == 1 ? '格口号码' : '货架号码',
                    value: package.storeType == 1 ? '${package.cabinetBoxLabel}号' : package.checkCode,
                  ),
                  CommonRowWidget(
                    label: '包裹状态',
                    labelFontSize: 15,
                    valueFontSize: 15,
                    value: getPackageStatus(),
                  ),
                  CommonRowWidget(
                    isShow: widget.type == ListType.RETENTION,
                    label: '滞留时间',
                    value: '$day' + '天',
                  ),
                  widget.bottom ?? Container()
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
