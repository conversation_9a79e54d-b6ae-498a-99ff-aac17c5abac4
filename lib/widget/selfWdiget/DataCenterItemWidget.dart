
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/entitys/data_pj_entity.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CommonRowWidget.dart';
import 'package:flutter/material.dart';

/// 带图标的输入框
class DataCenterItemWidget extends StatefulWidget {
  final DataPjEntity package;
  final VoidCallback? bindTap;
  final Widget? bottom;

  DataCenterItemWidget({required this.package, this.bindTap, this.bottom});

  @override
  _DataCenterItemWidgetState createState() => new _DataCenterItemWidgetState();
}

_render(BuildContext context, DataPjEntity package, DataCenterItemWidget widget) {
  return InkWell(
    onTap: () {
      widget.bindTap!();
    },
    child: Container(
      color: Colors.white,
      padding: EdgeInsets.only(left: 10, right: 10),
      child: Container(
        height: 48,
        decoration: BoxDecoration(
          border: Border(bottom: BorderSide(width: 1, color: DefaultConfig().configs.BG_COLOR)),
        ),
        child: CommonRowWidget(
          label: package.cabinetLocationName,
          labelStyle: TextStyle(fontSize: 15, color: Colors.black, fontWeight: FontWeight.w500),
          rightWidget: Row(
            children: [
              Text('${package.num.toString()}', style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 15)),
              Padding(padding: EdgeInsets.only(left: 5)),
              Icon(
                Icons.arrow_forward_ios_sharp,
                size: 18,
                color: Color(0xFF999999),
              )
            ],
          ),
        ),
      ),
    ),
  );
}

/// State for [InorderWidget] widgets.
class _DataCenterItemWidgetState extends State<DataCenterItemWidget> {
  @override
  Widget build(BuildContext context) {
    DataPjEntity? package = widget.package;
    return _render(context, package, widget);
  }
}
