import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/widget/Marquee.dart';
import 'package:flutter/material.dart';

class ScanNotice extends StatefulWidget {
  final String content;

  ScanNotice({Key? key, this.content = ''}) : super(key: key);

  @override
  _ScanNoticeState createState() => _ScanNoticeState();
}

class _ScanNoticeState extends State<ScanNotice> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      padding: EdgeInsets.only(left: 15, top: 8, bottom: 8),
      color: Theme.of(context).primaryColorLight,
      child: Row(
        children: [
          LocalImageUtil.getImageAsset('tz', isChannel: true, width: 16),
          Padding(padding: EdgeInsets.only(left: 5)),
          // Container(
          //   padding: EdgeInsets.only(left: 5, right: 10),
          //   width: MediaQuery.of(context).size.width - 40,
          //   height: 28,
          //   child: Marquee(
          //       new Text(
          //         widget.content,
          //         style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 16),
          //       ),
          //       180.0,
          //       new Duration(seconds: 10),
          //       230.0),
          // ),
          Text(widget.content, style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 16))
        ],
      ),
    );
  }
}
