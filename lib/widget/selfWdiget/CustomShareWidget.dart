import 'package:flutter/material.dart';

class CustomShareWidget extends StatelessWidget {
  final List buttonTitles = ["微信好友", "朋友圈", "新浪微博"];
  final List buttonImages = ["isNew.png", "isNew.png", "isNew.png"];

  static showShareView(BuildContext context) {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white.withAlpha(0),
        elevation: 2,
        builder: (BuildContext context) {
          return ClipRRect(
            borderRadius: BorderRadius.only(topLeft: Radius.circular(10), topRight: Radius.circular(10)),
            child: Container(
              height: 250,
              color: Color.fromRGBO(255, 255, 255, 1),
              child: Stack(
                children: [
                  Padding(
                      padding: EdgeInsets.only(top: 14, left: 15, right: 15),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "分享到",
                            style: TextStyle(
                                fontSize: 20, fontWeight: FontWeight.bold, color: Color.fromRGBO(51, 51, 51, 1)),
                          ),
                          GestureDetector(
                            onTap: () {
                              CustomShareWidget()._dismiss(context);
                            },
                            child:
                                Image.network("https://cabinet-img.oss-cn-hangzhou.aliyuncs.com/app/images/isNew.png"),
                          ),
                        ],
                      )),
                  Padding(
                    padding: EdgeInsets.only(left: 0, right: 0, top: 0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: List.generate(CustomShareWidget().buttonTitles.length, (index) {
                        return Container(
                          child: CustomShareWidget()._buttonWidget(context, CustomShareWidget().buttonTitles[index],
                              CustomShareWidget().buttonImages[index], index),
                        );
                      }).toList(),
                    ),
                  )
                ],
              ),
            ),
          );
        });
  }

  _buttonWidget(BuildContext context, String title, String imageName, int index) {
    return GestureDetector(
      onTap: () {
        switch (index) {
          case 0: //微信好友
            _dismiss(context);
            break;
          case 1: //朋友圈
            _dismiss(context);
            break;
          case 2: //新浪微博
            _dismiss(context);
            break;
          default:
            break;
        }
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.network("https://cabinet-img.oss-cn-hangzhou.aliyuncs.com/app/images/$imageName"),
          Padding(
            padding: EdgeInsets.only(top: 5),
            child: Text(title, style: TextStyle(fontSize: 12, color: Color.fromRGBO(51, 51, 51, 1))),
          )
        ],
      ),
    );
  }

  _dismiss(BuildContext context) {
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return showShareView(context);
  }
}
