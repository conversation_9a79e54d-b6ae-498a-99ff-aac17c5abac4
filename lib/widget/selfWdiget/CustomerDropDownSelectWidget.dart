import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:flutter/material.dart';

/// 带图标的输入框
class CustomerDropDownSelectWidget extends StatefulWidget {
  final Map<dynamic, dynamic> map;
  final String value;
  final String hintText;
  final TextStyle? labelStyle;
  final cb;

  CustomerDropDownSelectWidget({
    Key? key,
    required this.map,
    this.hintText = '请选择',
    required this.value,
    this.cb,
    this.labelStyle,
  }) : super(key: key);

  @override
  _CustomerDropDownSelectWidgetState createState() => new _CustomerDropDownSelectWidgetState();
}

class _CustomerDropDownSelectWidgetState extends State<CustomerDropDownSelectWidget> {
  _CustomerDropDownSelectWidgetState() : super();
  String? label = '请选择';
  String value = '';

  @override
  void initState() {
    super.initState();
    label = widget.hintText;
    if (CheckUtils.isNotNull(widget.value) && widget.map.isNotEmpty) {
      setState(() {
        label = widget.map[widget.value];
      });
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print(widget);
  }

  void deactive() {}

  Widget popMenu() {
    return new PopupMenuButton<String>(
        child: Container(
            alignment: Alignment.center,
            child: Row(
              children: [
                Expanded(
                    child: Text('${label ?? widget.hintText}',
                        style: widget.labelStyle ?? TextStyle(fontSize: 14, color: Colors.black),
                        overflow: TextOverflow.ellipsis)),
                Padding(padding: EdgeInsets.only(left: 3)),
                Icon(
                  Icons.keyboard_arrow_down_sharp,
                  size: 20,
                ),
              ],
            )),
        tooltip: '',
        itemBuilder: (BuildContext context) => widget.map.keys
            .map((item) => PopupMenuItem<String>(
                value: '$item',
                child: Text(
                  '${widget.map[item]}',
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(color: item == widget.value ? Theme.of(context).primaryColor : Colors.black),
                )))
            .toList(),
        onSelected: (String value) {
          widget.cb(value);
          setState(() {
            label = widget.map[value];
          });
        });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.map.isNotEmpty) {
      setState(() {
        label = widget.map[widget.value];
      });
    }
    return Container(
      height: 40.0,
      alignment: Alignment.centerLeft,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: <Widget>[
          Expanded(
            child: popMenu(),
          ),
        ],
      ),
    );
  }
}
