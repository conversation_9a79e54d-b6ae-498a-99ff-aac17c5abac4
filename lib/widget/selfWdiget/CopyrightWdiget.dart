import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:flutter/material.dart';

/// 带图标的输入框
class CopyrightWidget extends StatefulWidget {
  CopyrightWidget({
    Key? key,
  }) : super(key: key);

  @override
  _CopyrightWidgetState createState() => new _CopyrightWidgetState();
}

class _CopyrightWidgetState extends State<CopyrightWidget> {
  _CopyrightWidgetState() : super();
  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      padding: EdgeInsets.fromLTRB(0, 10, 10, 10),
//                          height: 40.0,
      alignment: Alignment.center,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Text(
            '欢迎使用${DefaultConfig().configs.APP_NAME}',
            style: TextStyle(color: Color(0xFF010101), fontSize: 15.0),
          )
        ],
      ),
    );
  }
}
