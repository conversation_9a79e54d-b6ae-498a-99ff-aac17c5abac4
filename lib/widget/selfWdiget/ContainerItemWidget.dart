import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_entity.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CabinetBoxUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/widget/CabinetBoxListWidget.dart';
import 'package:cabinet_flutter_app/widget/CollectIconButton.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

import 'CommonRowWidget.dart';

/// 带图标的输入框
class ContainerItemWidget extends StatefulWidget {
  CabinetBoxUseType boxUseType;
  final ListType type;
  final CabinetEntity cabinet;
  final VoidCallback? bindTap;
  final ValueChanged<int> cb;
  final ValueChanged<bool> cbIsFresh;
  final Widget? bottom;

  ContainerItemWidget(this.type,
      {required this.cabinet,
      required this.cb,
      this.bindTap,
      this.bottom,
      this.boxUseType = CabinetBoxUseType.KXCX,
      required this.cbIsFresh});

  @override
  _ContainerItemWidgetState createState() => new _ContainerItemWidgetState();
}

/// State for [InorderWidget] widgets.
class _ContainerItemWidgetState extends State<ContainerItemWidget> {
  double marginValue = 10;
  List<dynamic> emptyList = [];

  /// 点位信息
  buildCabinetInfo() {
    return Container(
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(
              Radius.circular(7),
            ),
            border: Border.all(width: 1, color: Colors.white)),
        margin: EdgeInsets.all(marginValue),
        width: double.infinity,
        child: InkWell(
          onTap: () {
            widget.bindTap!();
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: EdgeInsets.fromLTRB(10, 14, 10, 4),
                child: Row(
                  children: [
                    Text(
                      '${widget.cabinet.name}',
                      style: TextStyle(fontSize: AppConstant.middleTextWhiteSize, color: Colors.black, fontWeight: FontWeight.w600),
                    ),
                    Expanded(
                        child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          '${widget.cabinet.code}',
                          style: TextStyle(fontSize: 14),
                        )
                      ],
                    ))
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.fromLTRB(0, 0, 10, 4),
                child: Row(
                  children: [
                    Expanded(
                        child: Container(
                      margin: EdgeInsets.fromLTRB(10, 4, 10, 4),
                      child: Text(
                        '${widget.cabinet.address}',
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(fontSize: AppConstant.smallTextSize, color: Color(0xFF858585)),
                      ),
                    )),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Offstage(
                          offstage: !(widget.cabinet.hasUsed == 1),
                          child: Container(
                            padding: EdgeInsets.fromLTRB(8, 2, 8, 2),
                            decoration: BoxDecoration(
                                border: Border.all(color: Theme.of(context).primaryColor),
                                borderRadius: BorderRadius.all(Radius.circular(20))),
                            child: Text('常用柜', style: TextStyle(color: Theme.of(context).primaryColor, fontSize: AppConstant.tinyTextSize)),
                          ),
                        )
                      ],
                    )
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.fromLTRB(10, 0, 10, 4),
                child: CommonRowWidget(
                  leftWidget: Offstage(
                    offstage: widget.cabinet.distance == 0,
                    child: Row(
                      children: [
                        LocalImageUtil.getImageAsset('location', width: 14),
                        Padding(padding: EdgeInsets.only(left: 3)),
                        Text(
                          CabinetBoxUtil.getDistance(widget.cabinet.distance),
                          style: TextStyle(fontSize: AppConstant.smallTextSize, color: DefaultConfig().configs.GREY_COLOR),
                        ),
                      ],
                    ),
                  ),
                  rightWidget: InkWell(
                    onTap: () async {
                      final res = await NavigatorUtils.goCabinetLocationPage(context, widget.cabinet.code!);
                      widget.cbIsFresh(res);
                    },
                    child: Text(
                      '查看地址',
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(fontSize: 14, color: Theme.of(context).primaryColor),
                    ),
                  ),
                ),
              ),
              buildGKInfo(),
              Container(
                margin: EdgeInsets.fromLTRB(10, 0, 10, 14),
                child: CommonRowWidget(
                    leftWidget: Row(
                      children: [
                        CollectIconButtonWidget(
                            value: widget.cabinet.hasCollected == 1,
                            onPress: () {
                              setCabinetCollect();
                            }),
                      ],
                    ),
                    rightWidget: Container(
                      child: Row(
                        children: [
                          Offstage(
                            offstage: !(widget.boxUseType == CabinetBoxUseType.KXCX || widget.boxUseType == CabinetBoxUseType.YYGK),
                            child: InkWell(
                              onTap: () async {
                                goReservation(widget.cabinet, CabinetBoxUseType.YYGK);
                              },
                              child: Offstage(
                                offstage: widget.cabinet.switchBook != 1,
                                child: Container(
                                  padding: EdgeInsets.fromLTRB(8, 2, 8, 2),
                                  decoration: BoxDecoration(
                                      color: Theme.of(context).primaryColor,
                                      border: Border.all(color: Theme.of(context).primaryColor),
                                      borderRadius: BorderRadius.all(Radius.circular(5))),
                                  child: Text('去预约', style: TextStyle(color: Colors.white, fontSize: AppConstant.tinyTextSize)),
                                ),
                              ),
                            ),
                          ),
                          Padding(padding: EdgeInsets.only(right: 10)),
                          Offstage(
                            offstage: !(widget.boxUseType == CabinetBoxUseType.KXCX || widget.boxUseType == CabinetBoxUseType.XGZY),
                            child: InkWell(
                              onTap: () async {
                                goReservation(widget.cabinet, CabinetBoxUseType.XGZY);
                              },
                              child: Offstage(
                                offstage: widget.cabinet.switchRent != 1,
                                child: Container(
                                  padding: EdgeInsets.fromLTRB(8, 2, 8, 2),
                                  decoration: BoxDecoration(
                                      color: Theme.of(context).primaryColor,
                                      border: Border.all(color: Theme.of(context).primaryColor),
                                      borderRadius: BorderRadius.all(Radius.circular(5))),
                                  child: Text('去租用', style: TextStyle(color: Colors.white, fontSize: AppConstant.tinyTextSize)),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    )),
              )
            ],
          ),
        ));
  }

  /// 去预约
  goReservation(cabinet, boxUseType) async {
    await NavigatorUtils.goCabinetBoxCreatePage(context, cabinet, boxUseType);
  }

  /// 收藏点位
  setCabinetCollect() async {
    var res = await CourierDao.setCabinetCollected(widget.cabinet.code!, !(widget.cabinet.hasCollected == 1));
    if (res != null && res.result) {
      int isCollect = res.data['hasCollect'];
      Fluttertoast.showToast(msg: isCollect == 1 ? '收藏成功' : '取消收藏成功', gravity: ToastGravity.CENTER);
      widget.cb(isCollect);
    }
  }

  /// 格口信息
  buildGKInfo() {
    Map<String, dynamic> countMap = {
      'microCount': widget.cabinet.microCount,
      'miniCount': widget.cabinet.miniCount,
      'smallCount': widget.cabinet.smallCount,
      'mediumCount': widget.cabinet.mediumCount,
      'largeCount': widget.cabinet.largeCount,
      'hugeCount': widget.cabinet.hugeCount,
      'superCount': widget.cabinet.superCount,
    };
    emptyList = CabinetBoxUtil.getCabinetBoxAvailableCount(widget.cabinet.dispatchJson, countMap);
    return Container(
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(
            Radius.circular(7),
          ),
          border: Border.all(width: 1, color: Colors.white)),
      margin: EdgeInsets.only(left: marginValue, right: marginValue),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(top: 15, bottom: 20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(5)),
              color: Color(0xFFF5F5F5),
            ),
            // child: Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   children: buildAvailableGK(),
            // ),
            child: CabinetBoxListWidget(emptyList, showPrice: false),
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        child: Column(
      children: [
        ///点位信息
        buildCabinetInfo(),
      ],
    ));
  }
}
