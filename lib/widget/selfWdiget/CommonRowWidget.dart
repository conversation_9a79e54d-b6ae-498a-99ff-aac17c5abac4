import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:flutter/material.dart';

const EdgeInsets _rowPadding = EdgeInsets.only(top: 5);
const Color _labelColor =  Color(0xFF585858);
const Color _valueColor = Colors.black;

/// 带图标的输入框
class CommonRowWidget extends StatefulWidget {
  final EdgeInsets? rowPadding;
  final bool isShow;

  /// 标题
  final String? label;
  final Color? labelColor;
  final double? labelFontSize;
  final TextStyle? labelStyle;
  final Widget? leftWidget;
  final VoidCallback? actionLeft;

  /// 中间
  final String? middleValue;
  final Color? middleValueColor;
  final double? middleValueFontSize;
  final TextStyle? middleValueStyle;
  final Widget? middleWidget;
  final VoidCallback? actionMiddle;

  /// 名称
  final String? value;
  final Color? valueColor;
  final double? valueFontSize;
  final TextStyle? valueStyle;
  final Widget? rightWidget;
  final VoidCallback? actionRight;

  CommonRowWidget(
      {this.rowPadding = _rowPadding,
      this.isShow = true,
      this.label = '',
      this.labelColor = _labelColor,
      this.labelFontSize = AppConstant.smallTextSize,
      this.labelStyle,
      this.leftWidget,
      this.actionLeft,
      this.value = '',
      this.valueColor = _valueColor,
      this.valueFontSize = AppConstant.smallTextSize,
      this.actionMiddle,
      this.rightWidget,
      this.actionRight,
      this.valueStyle,
      this.middleValue = '',
      this.middleValueColor = _valueColor,
      this.middleValueFontSize = AppConstant.smallTextSize,
      this.middleValueStyle,
      this.middleWidget});

  @override
  _CommonRowWidgetState createState() => new _CommonRowWidgetState();
}

/// State for [InorderWidget] widgets.
class _CommonRowWidgetState extends State<CommonRowWidget> {
  @override
  Widget build(BuildContext context) {
    return Offstage(
      offstage: !widget.isShow,
      child: Container(
        padding: widget.rowPadding,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            widget.leftWidget ??
                Expanded(
                  child: Text(
                    '${widget.label}',
                    style: widget.labelStyle ?? TextStyle(fontSize: widget.labelFontSize, color: widget.labelColor),
                  ),
                ),
            widget.middleWidget ??
                Text(
                  '${widget.middleValue}',
                  style: widget.middleValueStyle ??
                      TextStyle(fontSize: widget.middleValueFontSize, color: widget.middleValueColor),
                ),
            widget.rightWidget ?? buildRightText(),
          ],
        ),
      ),
    );
  }

  buildRightText() {
    return widget.actionRight != null
        ? InkWell(
            onTap: () {
              widget.actionRight!();
            },
            child: _buildRightText(),
          )
        : _buildRightText();
  }

  _buildRightText() {
    return Text(
      '${widget.value}',
      style: widget.valueStyle ??
          TextStyle(
            fontSize: widget.valueFontSize,
            color: widget.valueColor,
          ),
    );
  }
}
