import 'dart:async';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/page_life_cycle.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:scan/scan.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

class MakeWaybillWidget extends StatefulWidget {
  MakeWaybillWidget({Key? key}) : super(key: key);

  @override
  _MakeWaybillWidgetState createState() => _MakeWaybillWidgetState();
}

class _MakeWaybillWidgetState extends State<MakeWaybillWidget>
    with TickerProviderStateMixin, WidgetsBindingObserver, PageLifeCycle<MakeWaybillWidget> {
  final GlobalKey<ScaffoldState> _scaffoldKey = new GlobalKey<ScaffoldState>();
  String waybillNo = "";
  bool isInProcess = false;
  Scan scanPlugin = Scan();
  ScanView? scanView;
  StreamSubscription<dynamic>? _scanSubscription;
  PanelController scanPanelController = new PanelController();
  late ScanViewController scanViewController;
  bool checkFlash = false;
  String lastScanWaybillNo = '';
  double scanInputModelHeight = 0;
  double scanListModelHeight = 62;

  String pickRule = 'end_four_sheetNo'; // 取件码规则，默认编号自然累加
  @override
  void initState() {
    super.initState();
    // 初始化生命周期监听 用于解决扫码库在生命周期变动时候被杀死
    WidgetsBinding.instance.addObserver(this);
    initScan();
  }


  // 路由显示触发
  @override
  void onShow() {
    this.initScan();
    this.startSpot();
  }

  @override
  void onHide() {
    _scanSubscription?.cancel();
    scanViewController.stopSpot();
    this.stopSpot();
  }

  void initScan() {
    _scanSubscription = scanPlugin.onScanChanged.listen((result) {
      stopSpot();
      if (result['text'] != '' && result['text'] != null) {
        addScan(
          result['text'],
        );
      }
    });
    // 初始化摄像头
    Timer(const Duration(milliseconds: 500), () {
      this.scanView = ScanView(
        onScanViewCreated: (ScanViewController controller) {
          scanViewController = controller;
          scanViewController.setScanTopOffset(
              60 + int.parse(MediaQuery.of(context).padding.top.toStringAsFixed(0)), 50);
          scanViewController.changeScanType(type: 'ck');
          scanViewController.setCornerColor(DefaultConfig().configs.PRIMARY_COLOR_TEXT);
          scanViewController.enableOcr();
        },
        onScanViewError: () {
          CommonUtils.showMessage(context, msg: "调用摄像头失败", success: false, duration: 2);
        },
      );
      setState(() {});
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    ///通过state判断App前后台切换
    switch (state) {
      case AppLifecycleState.hidden:
      case AppLifecycleState.inactive: // 处于这种状态的应用程序应该假设它们可能在任何时候暂停。
        break;
      case AppLifecycleState.resumed: // 应用程序可见，前台
        this.initScan();
        break;
      case AppLifecycleState.paused: // 应用程序不可见，后台
        _scanSubscription?.cancel();
        this.stopSpot();
        break;
      case AppLifecycleState.detached: // 申请将暂时暂停
        break;
    }
  }

  @override
  void dispose() {
    super.dispose();
    _scanSubscription?.cancel();
    scanViewController.stopSpot();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void deactivate() {
    super.deactivate();
  }

  ///开始识别
  startSpot() {
    scanViewController.startSpot();
    isInProcess = false;
    setState(() {});
  }

  /// 停止识别
  stopSpot() {
    scanViewController.stopSpot();
    isInProcess = false;
  }

  addScan(String? waybillNo) async {
    if (waybillNo == null) {
      startSpot();
      return false;
    }
    Navigator.pop(context, waybillNo);
  }

  /// 输入框 扫描框
  _buildScanOrInputArea() {
    return buildCKScan();
  }

  // 打开、关闭闪光灯
  setFlash() {
    checkFlash = !checkFlash;
    if (checkFlash) {
      scanViewController.openFlashlight();
    } else {
      scanViewController.closeFlashlight();
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: Colors.black,
      body: _buildScanOrInputArea(),
    );
  }

  buildScanView() {
    if (this.scanView != null) {
      return this.scanView;
    }
    return Container();
  }

  /// 扫码界面
  buildCKScan() {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Stack(
        children: <Widget>[
          /// 扫码界面
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: MediaQuery.of(context).size.height,
            child: buildScanView(),
          ),

          /// 标题部分
          buildTitle(),
        ],
      ),
    );
  }

  /// 标题部分
  buildTitle() {
    double _navHeight = 55.0 + MediaQuery.of(context).padding.top;
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: new Container(
        width: MediaQuery.of(context).size.width,
        height: _navHeight,
        padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
        child: new Container(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Container(
                  width: 60.0,
                  padding: EdgeInsets.only(left: 12),
                  alignment: Alignment.centerLeft,
                  child: Icon(
                    Icons.arrow_back,
                    size: 25.0,
                    color: Colors.white,
                  ),
                ),
              ),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Text(
                      '条形码扫描',
                      style: TextStyle(fontSize: 20.0, color: Colors.white),
                    ),
                    InkWell(
                      onTap: () async {
                        setFlash();
                      },
                      child: Padding(
                          padding: EdgeInsets.only(left: 10),
                          child: LocalImageUtil.getImageAsset(checkFlash ? 'flashOpen' : 'flashClose',
                              width: 20, height: 20)),
                    ),
                    Padding(
                      padding: EdgeInsets.only(right: 30.0),
                    )
                  ],
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  InkWell(
                    onTap: () {
                      openInputDialog();
                    },
                    child: Container(
                      width: 35.0,
                      height: 35.0,
                      margin: EdgeInsets.only(right: 10),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(20.0)), color: Theme.of(context).primaryColor),
                      child: Image.asset(
                        LocalImageUtil.getImagePath('editCk'),
                        width: 20.0,
                      ),
                    ),
                  )
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 单号录入
  openInputDialog() {
    CommonUtils.inputWaybillNo(context, '', (waybillNo) {
      if (waybillNo != '') {
        setState(() {
          Navigator.pop(context, waybillNo);
        });
      } else {
        Fluttertoast.showToast(msg: '请录入单号');
      }
    });
  }
}
