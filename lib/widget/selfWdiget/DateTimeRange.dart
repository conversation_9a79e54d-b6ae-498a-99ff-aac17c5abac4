
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';

/// 带图标的输入框
class DateTimeRangeWidget extends StatefulWidget {
  final DateTime value;
  final cb;

  DateTimeRangeWidget({
    Key? key,
    required this.value,
    this.cb,
  }) : super(key: key);

  @override
  _DateTimeRangeWidgetState createState() => new _DateTimeRangeWidgetState();
}

class _DateTimeRangeWidgetState extends State<DateTimeRangeWidget> {
  _DateTimeRangeWidgetState() : super();

  showCalendar() {
    return showModalBottomSheet(
      isDismissible: true,
      isScrollControlled: true,
      context: context,
      builder: (BuildContext context) {
        return Container(
            constraints: BoxConstraints(maxHeight: 380, minHeight: 340),
            color: Colors.grey.shade200,
            child: TableCalendar(
                headerStyle: HeaderStyle(
                    titleCentered: true,
                    leftChevronVisible: true,
                    rightChevronVisible: true,
                    formatButtonVisible: false,
                    headerPadding: EdgeInsets.symmetric(vertical: 0)),
                locale: 'zh_cn',
                firstDay: DateTime.now().subtract(new Duration(days: 90)),
                lastDay: DateTime.now(),
                focusedDay: widget.value,
                selectedDayPredicate: (day) {
                  return isSameDay(widget.value, day);
                },
                daysOfWeekStyle:
                    DaysOfWeekStyle(weekdayStyle: TextStyle(fontSize: 12), weekendStyle: TextStyle(fontSize: 12)),
                calendarStyle: CalendarStyle(
                    outsideDaysVisible: false,
                    todayDecoration: BoxDecoration(
                      color: DefaultConfig().configs.PRIMARY_COLOR_GHOST,
                      shape: BoxShape.circle,
                    ),
                    // markerSizeScale: 5,
                    selectedDecoration: BoxDecoration(color: Theme.of(context).primaryColor, shape: BoxShape.circle),
                    canMarkersOverflow: true),
                onDaySelected: (selectedDay, focusedDay) {
                  widget.cb(selectedDay);
                  Navigator.of(context).pop();
                },
                onFormatChanged: (format) {
                  print('$format');
                }));
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        showCalendar();
        // CommonUtils.showCalendar(context, (dateList) async {
        //   widget.cb(dateList);
        // }, selectDay: widget.value, selectMode: CalendarConstants.MODE_MULTI_SELECT, maxMultiSelectCount: 2);
      },
      child: Container(
        height: 40.0,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: <Widget>[
            widget.value != []
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Padding(padding: EdgeInsets.only(top: 14)),
                      Text('${DateFormat('MM/dd').format(widget.value)}',
                          style: TextStyle(fontSize: 11, color: Colors.black)),
                      // Text('${DateFormat('yy/MM/dd').format(widget.value[1])} ',
                      //     style: TextStyle(fontSize: 11, color: Colors.black))
                    ],
                  )
                : Text('选择日期'),
            Icon(
              Icons.keyboard_arrow_down_sharp,
              size: 20,
            )
          ],
        ),
      ),
    );
  }
}
