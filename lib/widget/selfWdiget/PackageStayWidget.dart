import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/entitys/package_view_sj_entity.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/page/login/AgreementPage.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CommonRowWidget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';

/// 带图标的输入框
class PackageStayWidget extends StatefulWidget {
  final PackageType type;
  final PackageViewSjEntity package;
  final int indexNo;
  final VoidCallback? bindTap;
  final Widget? bottom;

  PackageStayWidget(this.type, {required this.package, required this.indexNo, this.bindTap, this.bottom});

  @override
  _PackageStayWidgetState createState() => new _PackageStayWidgetState();
}

phoneMiss(phone) {
  return phone.replaceFirst(new RegExp(r'\d{4}'), '****', 3);
}

/// State for [InorderWidget] widgets.
class _PackageStayWidgetState extends State<PackageStayWidget> {
  @override
  Widget build(BuildContext context) {
    PackageViewSjEntity? package = widget.package;
    return Container(
      margin: EdgeInsets.fromLTRB(10, 0, 10, 10),
      decoration: new BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(
            Radius.circular(7),
          )),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.all(
          Radius.circular(7),
        ),
        child: Ink(
          decoration: new BoxDecoration(
              borderRadius: BorderRadius.all(
            Radius.circular(7),
          )),
          child: InkWell(
            borderRadius: BorderRadius.all(
              Radius.circular(7),
            ),
            child: Container(
              padding: EdgeInsets.all(10),
              child: Column(
                children: [
                  CommonRowWidget(
                    leftWidget: Row(
                      children: [
                        Text('NO.${widget.indexNo.toString()}',
                            style: TextStyle(
                                fontSize: AppConstant.normalTextSize,
                                color: Theme.of(context).primaryColor,
                                fontWeight: FontWeight.bold)),
                      ],
                    ),
                    value: '',
                  ),
                  CommonRowWidget(
                    label: '点位信息',
                    value: package.cabinetLocationName,
                  ),
                  CommonRowWidget(
                    label: '柜门号',
                    value: '${package.cabinetName}-${package.cabinetBoxLabel}',
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 5),
                    height: 1,
                    color: Colors.grey.shade200,
                    width: double.infinity,
                  ),
                  CommonRowWidget(
                      leftWidget: Row(
                        children: [
                          Text('寄件人信息',
                              style: TextStyle(
                                  fontSize: AppConstant.smallTextSize,
                                  color: Theme.of(context).primaryColor,
                                  fontWeight: FontWeight.bold)),
                        ],
                      ),
                      rightWidget: widget.type != ListType.STAY
                          ? Container(
                              height: 25,
                              child: ElevatedButton(
                                onPressed: () {
                                  Clipboard.setData(ClipboardData(
                                      text: '${package.senderName}' +
                                          '${package.senderMobile}' +
                                          '${package.senderProvinceName}${package.senderCityName}${package.senderAreaName}${package.senderStreetName}${package.senderAddress}'));
                                  Fluttertoast.showToast(msg: '复制成功');
                                },
                                child: Text(
                                  '复制寄件人信息',
                                  style: TextStyle(fontSize: 12),
                                ),
                                style: ElevatedButton.styleFrom(
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(5),
                                    ),
                                    backgroundColor: Theme.of(context).primaryColor,
                                    minimumSize: Size(50, 25)),
                              ),
                            )
                          : Container()),
                  CommonRowWidget(
                    label: '${package.senderName}',
                    rightWidget: InkWell(
                        onTap: () async {
                          if (widget.type != ListType.STAY) {
                            await CommonUtils.makePhoneCall(context, package.senderMobile);
                          }
                        },
                        child: Row(
                          children: [
                            widget.type != ListType.STAY
                                ? Icon(Icons.phone_in_talk, size: 16, color: Colors.blue)
                                : Container(),
                            Text(widget.type != ListType.STAY ? package.senderMobile : phoneMiss(package.senderMobile),
                                style: TextStyle(
                                    fontSize: AppConstant.smallTextSize,
                                    color: widget.type != ListType.STAY ? Colors.blue : Colors.black)),
                          ],
                        )),
                  ),
                  CommonRowWidget(
                    label:
                        '${package.senderProvinceName}${package.senderCityName}${package.senderAreaName}${package.senderStreetName}${package.senderAddress}',
                    value: '',
                  ),
                  CommonRowWidget(
                      leftWidget: Row(
                        children: [
                          Text('收件人信息',
                              style: TextStyle(
                                  fontSize: AppConstant.smallTextSize,
                                  color: Theme.of(context).primaryColor,
                                  fontWeight: FontWeight.bold)),
                        ],
                      ),
                      rightWidget: widget.type != ListType.STAY
                          ? Container(
                              height: 25,
                              child: ElevatedButton(
                                onPressed: () {
                                  Clipboard.setData(ClipboardData(
                                      text: '${package.receiverName}' +
                                          '${package.receiverMobile}' +
                                          '${package.receiverProvinceName}${package.receiverCityName}${package.receiverAreaName}${package.receiverStreetName}${package.receiverAddress}'));
                                  Fluttertoast.showToast(msg: '复制成功');
                                },
                                child: Text(
                                  '复制收件人信息',
                                  style: TextStyle(fontSize: 12),
                                ),
                                style: ElevatedButton.styleFrom(
                                    shadowColor: DefaultConfig().configs.PRIMARY_COLOR_LIGHT,
                                    backgroundColor: Theme.of(context).primaryColor,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(5),
                                    ),
                                    minimumSize: Size(50, 25)),
                              ),
                            )
                          : Container()),
                  CommonRowWidget(
                    label: '${package.receiverName}',
                    rightWidget: InkWell(
                        child: Row(
                      children: [
                        Text(widget.type != ListType.STAY ? package.receiverMobile : phoneMiss(package.receiverMobile),
                            style: TextStyle(fontSize: AppConstant.smallTextSize, color: Colors.black)),
                      ],
                    )),
                  ),
                  CommonRowWidget(
                    label:
                        '${package.receiverProvinceName}${package.receiverCityName}${package.receiverAreaName}${package.receiverStreetName}${package.receiverAddress}',
                    value: '',
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 8),
                    height: 1,
                    color: Colors.grey.shade200,
                    width: double.infinity,
                  ),
                  CommonRowWidget(
                    label: '快递品牌',
                    value: DefaultConfig().configs.EXPRESS2[package.brandCode],
                  ),
                  Offstage(
                    offstage: widget.type == ListType.STAY || widget.type == ListType.TAKEN,
                    child: CommonRowWidget(
                      label: '运单号',
                      rightWidget: Row(
                        children: [
                          Container(
                            child: Text('${package.waybillNo ?? '暂无'}'),
                            margin: EdgeInsets.only(right: 5),
                          ),
                          Offstage(
                            offstage: package.waybillNo == null || package.waybillNo == '',
                            child: Container(
                              height: 25,
                              child: ElevatedButton(
                                onPressed: () {
                                  Navigator.push(context, new MaterialPageRoute(builder: (context) {
                                    return AgreementPage(
                                        url: DefaultConfig().configs.KUAIDI100 + 'nu=' + package.waybillNo!,
                                        isLocalUrl: false,
                                        title: '物流轨迹');
                                  }));
                                },
                                child: Text(
                                  '查物流',
                                  style: TextStyle(fontSize: 12),
                                ),
                                style: ElevatedButton.styleFrom(
                                    shadowColor: Theme.of(context).primaryColor,
                                    backgroundColor: Theme.of(context).primaryColor,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(5),
                                    ),
                                    minimumSize: Size(25, 5)),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                  CommonRowWidget(
                    label: '订单金额',
                    value:
                        '¥${((package.inboundUserFee ?? 0) / 1000).toStringAsFixed(2)}-${DefaultConfig().configs.CABINET_BOX_TYPE[package.cabinetBoxType]}',
                  ),
                  CommonRowWidget(
                    label: '投柜时间',
                    value: package.inboundTime,
                  ),
                  CommonRowWidget(
                    label: '备注',
                    value: '${package.remarks}',
                  ),
                  CommonRowWidget(
                    label: '实名信息',
                    value: '${package.senderName}  ${package.senderIdNumber}',
                  ),
                  widget.bottom ?? Container()
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
