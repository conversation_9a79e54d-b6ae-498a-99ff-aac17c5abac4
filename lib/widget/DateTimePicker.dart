import 'package:cabinet_flutter_app/widget/InputDropdown.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class DateTimePicker extends StatelessWidget {
  const DateTimePicker({
    Key? key,
    this.labelText = '',
    this.icon,
    this.iconSize = 24.0,
    this.padding,
    this.selectedDate,
    this.selectDate,
    this.isNormal = true,
    this.fontColor = Colors.white,
  }) : super(key: key);

  final bool isNormal;
  final String labelText;
  final DateTime? selectedDate;
  final ValueChanged<DateTime>? selectDate;
  final Color fontColor;
  final IconData? icon;
  final double iconSize;
  final EdgeInsetsGeometry? padding;

  Future<Null> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
        context: context, initialDate: selectedDate!, firstDate: DateTime(2019, 7), lastDate: DateTime(2026));
    if (picked != null && picked != selectedDate) selectDate!(picked);
  }

  @override
  Widget build(BuildContext context) {
//    final TextStyle valueStyle = Theme.of(context).textTheme.title;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: <Widget>[
        Expanded(
          child: this.icon != null
              ? TextButton(
                  style: ButtonStyle(padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.all(5))),
                  onPressed: () {
                    _selectDate(context);
                  },
                  child: Container(
                    child: Icon(
                      icon,
                      color: fontColor,
                      size: iconSize,
                    ),
                  ),
                )
              : InputDropdown(
                  fontColor: fontColor,
                  isNormal: isNormal,
                  labelText: labelText,
                  valueText: DateFormat('yyyy-MM-dd').format(selectedDate!),
                  valueStyle: TextStyle(
                    fontSize: 17.0,
                  ),
                  onPressed: () {
                    _selectDate(context);
                  },
                ),
        ),
      ],
    );
  }
}
