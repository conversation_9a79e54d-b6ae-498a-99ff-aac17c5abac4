import 'package:flutter/material.dart';

/// title 控件
/// Created by denonzhu
/// on 2018/7/24.
class AppTitleBar extends StatelessWidget {
  final String title;

  final IconData? iconData;

  final VoidCallback? onPressed;

  final bool needRightLocalIcon;

  final Widget? rightWidget;

  AppTitleBar(this.title, {this.iconData, this.onPressed, this.needRightLocalIcon = false, this.rightWidget});

  @override
  Widget build(BuildContext context) {
    Widget? widget = rightWidget;
    if (rightWidget == null) {
      widget = (needRightLocalIcon)
          ? new IconButton(
              icon: new Icon(
                iconData,
                size: 19.0,
              ),
              onPressed: onPressed)
          : new Container();
    }
    return Container(
      child: new Row(
        children: <Widget>[
          new Expanded(
            child: new Text(
              title,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          widget ?? Container()
        ],
      ),
    );
  }
}
