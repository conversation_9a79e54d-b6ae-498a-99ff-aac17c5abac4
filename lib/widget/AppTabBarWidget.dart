import 'dart:async';

import 'package:cabinet_flutter_app/common/event/TabChangeEvent.dart';
import 'package:cabinet_flutter_app/common/net/Code.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:flutter/material.dart';

import 'AppCustomBar.dart';

// GlobalKey<AppTabBarWidget> countDownKey = GlobalKey<AppTabBarWidget>();
///支持顶部和顶部的TabBar控件
///配合AutomaticKeepAliveClientMixin可以keep住
class AppTabBarWidget extends StatefulWidget {
  ///底部模式type
  static const int BOTTOM_TAB = 1;

  ///顶部模式type
  static const int TOP_TAB = 2;

  final int type;

  final List<Widget> tabItems;

  final List<Widget> tabViews;

  final Color backgroundColor;

  final Color indicatorColor;

  final Widget? title;

  final Widget? drawer;

  final Widget floatingActionButton;

  final TarWidgetControl? tarWidgetControl;

  final PageController? topPageControl;

  final ValueChanged<int> onPageChanged;

  AppTabBarWidget({
    Key? key,
    required this.type,
    required this.tabItems,
    required this.tabViews,
    required this.backgroundColor,
    required this.indicatorColor,
    this.title,
    this.drawer,
    required this.floatingActionButton,
    this.tarWidgetControl,
    this.topPageControl,
    required this.onPageChanged,
  }) : super(key: key);

  @override
  _AppTabBarState createState() => new _AppTabBarState(
        type,
        tabViews,
        indicatorColor,
        title,
        drawer,
        floatingActionButton,
        tarWidgetControl,
        topPageControl,
        onPageChanged,
      );
}

// ignore: mixin_inherits_from_not_object
class _AppTabBarState extends State<AppTabBarWidget> with SingleTickerProviderStateMixin {
  final int _type;

  final List<Widget> _tabViews;

  final Color _indicatorColor;

  final Widget? _title;

  final Widget? _drawer;

  final Widget _floatingActionButton;

  final TarWidgetControl? _tarWidgetControl;

  final PageController? _pageController;

  final ValueChanged<int> _onPageChanged;

  String routeName = 'indexPage';

  _AppTabBarState(
    this._type,
    this._tabViews,
    this._indicatorColor,
    this._title,
    this._drawer,
    this._floatingActionButton,
    this._tarWidgetControl,
    this._pageController,
    this._onPageChanged,
  ) : super();

  late StreamSubscription stream;

  void scanQr() async {
//    String sheetNo = await scanPlugin.showScan();
//    CommonUtils.showLoadingDialog(context, '加载中');
//    var sheetInfoList = await CodeRegExpDao.getInfo(context, sheetNo, 'DS');
//    if (sheetInfoList == null) {
//      SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
//      return;
//    }
//    Navigator.pop(context);
//    ScanItemEntity sheet = sheetInfoList[0];
//
//    ScanNewItemEntity sc = new ScanNewItemEntity();
//    sc.sheetNo = sheetNo;
//    sc.company = sheet.company;
//    NavigatorUtils.goSheetLogs(context, sc);
  }

  @override
  void initState() {
    super.initState();
    tabController = new TabController(vsync: this, length: widget.tabItems.length)
      ..addListener(() {
        //TabBar的监听
        if (tabController.indexIsChanging) {
          //判断TabBar是否切换
          tabController.animateTo(tabController.index);
          _onPageChanged.call(tabController.index);
          setState(() {});
        }
      });

    ///监听手动切换tab
    stream = Code.eventBus.on<TabChangeEvent>().listen((event) {
      tabController.animateTo(event.index);
    });
  }

  ///整个页面dispose时，记得把控制器也dispose掉，释放内存
  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (this._type == AppTabBarWidget.TOP_TAB) {
      ///顶部tab bar
      return new Scaffold(
        resizeToAvoidBottomInset: false,
        floatingActionButton: _floatingActionButton,
        persistentFooterButtons: _tarWidgetControl == null ? [] : _tarWidgetControl?.footerButton,
        appBar: new AppCustomerBar(
          title: _title ?? Text(""),
          bottom: new TabBar(
            controller: tabController,
            tabs: widget.tabItems,
            indicatorColor: _indicatorColor,
          ),
        ),
        body: new PageView(
          controller: _pageController,
          children: _tabViews,
          onPageChanged: (index) {
            tabController.animateTo(index);
            _onPageChanged.call(index);
          },
        ),
      );
    }

    ///底部tab bar
    return new Scaffold(
      resizeToAvoidBottomInset: false,
      drawer: _drawer,
      floatingActionButton: widget.floatingActionButton,
      body: TabBarView(
        //TabBarView呈现内容，因此放到Scaffold的body中
        controller: tabController, //配置控制器
        children: _tabViews,
        physics: NeverScrollableScrollPhysics(),
      ),
      bottomNavigationBar: BottomAppBar(
          //为了适配主题风格，包一层Material实现风格套用
          color: Colors.white,
          shape: CircularNotchedRectangle(),
          child: Stack(
            children: [
              TabBar(
                labelColor: Theme.of(context).primaryColor,
                controller: tabController,
                //配置控制器
                tabs: widget.tabItems,
                indicatorWeight: 0.01,
                unselectedLabelColor: Colors.grey,
                indicatorColor: _indicatorColor, //tab标签的下划线颜色
              ),
              // Positioned(right: 80, bottom: 25, child: badge(13))
              // GStyle.badge(0, isdot:true)
              // GStyle.badge(13)
              // GStyle.badge(168, color: Colors.green, height: 17.0, width: 17.0)
            ],
          )),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }
}

badge(int count, {Color color = Colors.red, bool isdot = false, double height = 18.0, double width = 18.0}) {
  final _num = count > 99 ? '···' : count;
  return Container(
      alignment: Alignment.center,
      height: !isdot ? height : height / 2,
      width: !isdot ? width : width / 2,
      decoration: BoxDecoration(color: color, borderRadius: BorderRadius.circular(100.0)),
      child: !isdot ? Text('$_num', style: TextStyle(color: Colors.white, fontSize: 12.0)) : null);
}

class TarWidgetControl {
  List<Widget> footerButton = [];
}
