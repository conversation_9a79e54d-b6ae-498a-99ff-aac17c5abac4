import 'package:flutter/material.dart';

/// 带图标的输入框
class InputWidget extends StatefulWidget {
  final bool enabled;
  final bool isRequire;
  final bool isLittleFont;
  final String label;
  final String hintText;
  final ValueChanged<String?>? onSaved;
  final ValueChanged<String?>? onSubmit;
  final TextStyle? textStyle;
  final TextEditingController? controller;
  final String? initialValue;
  final bool autoValidate;
  final TextInputType? keyboardType;
  final bool littleLabel;
  final bool isBorderRadius;
  final bool isBorder;
  final int flex;
  final bool obscureText;
  final bool autofocus;
  final FocusNode? focusNode;
  final Color color;
  final double width;
  final double height;
  final int maxLength;
  final EdgeInsets contentPadding;
  final VoidCallback? onEditingComplete;

  InputWidget(
      {Key? key,
      this.flex = 1,
      this.label = '',
      this.hintText = '',
      this.keyboardType,
      this.onSaved,
      this.onSubmit,
      this.textStyle,
      this.controller,
      this.obscureText = false,
      this.enabled = true,
      this.isRequire = false,
      this.isLittleFont = false,
      this.littleLabel = true,
      this.isBorderRadius = false,
      this.isBorder = true,
      this.initialValue,
      this.autoValidate = false,
      this.focusNode,
      this.width = 50.0,
      this.height = 30.0,
      this.autofocus = false,
      this.maxLength = 20,
      this.color = Colors.white,
      this.contentPadding = const EdgeInsets.symmetric(vertical: 9.0),
      this.onEditingComplete})
      : super(key: key);

  @override
  _InputWidgetState createState() => _InputWidgetState();
}

/// State for [InputWidget] widgets.
class _InputWidgetState extends State<InputWidget> {
  _InputWidgetState() : super();

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: widget.flex,
      child: Container(
        child: Row(
          children: <Widget>[
            Offstage(
              offstage: widget.label == '',
              child: Container(
                width: widget.width,
//                width: 50.0,
                height: widget.height,
                alignment: Alignment.centerLeft,
                child: Text(
                  '${widget.label}',
                  style: TextStyle(
                    color: widget.isRequire ? Colors.red : Colors.black87,
                    fontSize: widget.littleLabel ? 14.0 : 16.0,
                  ),
                  overflow: TextOverflow.ellipsis,
                  softWrap: false,
                ),
              ),
            ),
            Expanded(
              child: Container(
                padding: const EdgeInsets.only(left: 5.0),
                alignment: Alignment.center,
                height: widget.height,
                decoration: BoxDecoration(
                  color: widget.color,
                  border: widget.isBorder ? Border.all(color: Color(0xFFEEEEEE), width: 1.0) : null,
                  borderRadius: widget.isBorderRadius ? BorderRadius.circular(30.0) : BorderRadius.circular(4.0),
                ),
                child: TextField(
                  focusNode: widget.focusNode,
                  style: TextStyle(
                    fontSize: widget.isLittleFont ? 16.0 : 18.0,
//                    color: widget.enabled ? Theme.of(context).primaryColor : Colors.grey,
                    color: Theme.of(context).primaryColor,
                    textBaseline: TextBaseline.alphabetic,
                  ),
                  autofocus: widget.autofocus,
                  obscureText: widget.obscureText,
                  controller: widget.controller,
                  enabled: widget.enabled,
                  onChanged: widget.onSaved,
                  keyboardType: widget.keyboardType,
                  onSubmitted: widget.onSubmit,
                  onEditingComplete: widget.onEditingComplete,
                  maxLength: widget.maxLength,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    hintText: widget.hintText,
                    hintStyle: TextStyle(color: Colors.grey, fontSize: 16.0),
                    contentPadding: widget.contentPadding,
                    counterText: "",
//                    filled: true,
//                    fillColor: Colors.red,
//                    contentPadding: const EdgeInsets.all(5),
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
