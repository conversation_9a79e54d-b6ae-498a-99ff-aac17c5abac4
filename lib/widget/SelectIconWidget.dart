import 'package:flutter/material.dart';

class SelectIconWidget extends StatefulWidget {
  /// value
  final bool select;

  /// icon size
  final double fontSize;

  /// unselected color
  final Color defaultColor;

  /// selected color
  final Color? selectedColor;

  /// kind of icon, default check_circle
  /// enum ['check_circle', 'check_box']
  final String type;

  SelectIconWidget(this.select, this.defaultColor,
      {Key? key, this.fontSize = 16.0, this.selectedColor, this.type = 'check_circle'})
      : super(key: key);

  @override
  _SelectIconWidgetState createState() => _SelectIconWidgetState();
}

class _SelectIconWidgetState extends State<SelectIconWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Map<String, dynamic> info = {
      'check_circle': [Icons.check_circle, Icons.radio_button_unchecked],
      'check_box': [Icons.check_box, Icons.check_box_outline_blank]
    };
    return Icon(
      widget.select ? info[widget.type][0] : info[widget.type][1],
      size: widget.fontSize,
      color: widget.select ? widget.selectedColor ?? Theme.of(context).primaryColor : widget.defaultColor,
    );
  }
}
