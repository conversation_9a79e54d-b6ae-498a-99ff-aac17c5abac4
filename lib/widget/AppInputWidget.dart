import 'package:flutter/material.dart';

/// 带图标的输入框
class AppInputWidget extends StatefulWidget {
  final bool obscureText;
  final bool readOnly;

  final String? hintText;
  final String? labelText;

  final IconData? iconData;

  final ValueChanged<String>? onChanged;

  final TextStyle? textStyle;
  final TextInputType? keyboardType;

  final FocusNode? focusNode;
  final TextInputAction? textInputAction;
  final TextEditingController? controller;
  final VoidCallback? onEditingComplete;
  final bool autofocus;

  AppInputWidget({
    Key? key,
    this.hintText,
    this.iconData,
    this.onChanged,
    this.textStyle,
    this.controller,
    this.textInputAction = TextInputAction.next,
    this.obscureText = false,
    this.readOnly = false,
    this.autofocus = false,
    this.focusNode,
    this.onEditingComplete,
    this.keyboardType,
    this.labelText,
  }) : super(key: key);

  @override
  _AppInputWidgetState createState() => new _AppInputWidgetState();
}

/// State for [AppInputWidget] widgets.
class _AppInputWidgetState extends State<AppInputWidget> {
  _AppInputWidgetState() : super();

  @override
  Widget build(BuildContext context) {
    return TextField(
      cursorColor: Theme.of(context).primaryColor,
      controller: widget.controller,
      onChanged: widget.onChanged,
      obscureText: widget.obscureText,
      focusNode: widget.focusNode,
      keyboardType: widget.keyboardType,
      textInputAction: widget.textInputAction,
      onEditingComplete: widget.onEditingComplete,
      autofocus: widget.autofocus,
      enableInteractiveSelection: false,
      style: TextStyle(textBaseline: TextBaseline.alphabetic),
      readOnly: widget.readOnly,
      decoration: InputDecoration(
        enabledBorder: UnderlineInputBorder(
          borderSide: BorderSide(color: Colors.green.shade400),
        ),
        contentPadding: EdgeInsets.all(9.0),
        hintText: widget.hintText,
        labelText: widget.labelText,
        icon: widget.iconData == null ? null : new Icon(widget.iconData),
      ),
    );
  }
}
