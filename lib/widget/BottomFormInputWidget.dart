import 'package:cabinet_flutter_app/widget/RichTextWidget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

//验证信息
typedef String? MyValidator(String? value);

class BottomFormInputWidget extends StatefulWidget {
  final String label;
  final String value;
  final double labelWidth;
  final String hintText;
  final FocusNode? focusNode;
  final bool require;
  final Icon? deleteIcon;
  final Color? color;
  final TextEditingController? controller;
  final List<TextInputFormatter>?  inputFormatters;
  final TextInputType? textInputType;
  final TextInputAction? textInputAction;
  final ValueChanged<String?>? onChanged;
  final VoidCallback? onEditingComplete;
  final EdgeInsetsGeometry? padding;
  final bool enabled;
  final bool isSelect;
  final bool obscureText;
  final bool readOnly;
  final GestureTapCallback? ontap;
  final GestureTapCallback? inputTap;
  final InputDecoration? decoration;
  final Widget? suffixIcon;

// 验证信息
  final MyValidator? validator;
  final bool autoFocus;

  BottomFormInputWidget(
      {this.label = '标签',
      this.value = '',
      this.labelWidth = 80.0,
      this.hintText = '',
      this.focusNode,
      this.inputFormatters,
      this.require = false,
      this.deleteIcon,
      this.controller,
      this.onChanged,
      this.textInputType = TextInputType.text,
      this.textInputAction = TextInputAction.next,
      this.onEditingComplete,
      this.padding,
      this.enabled = true,
      this.autoFocus = false,
      this.ontap,
      this.inputTap,
      this.color,
      this.isSelect = false,
      this.obscureText = false,
      this.readOnly = false,
      this.decoration,
      this.validator,
      this.suffixIcon});

  @override
  _BottomFormInputWidgetState createState() => _BottomFormInputWidgetState();
}

class _BottomFormInputWidgetState extends State<BottomFormInputWidget> {
  bool showClear = false;

  checkNull(String value) {
    if (value == null) {
      return false;
    }
    return value.isNotEmpty;
  }

  @override
  Widget build(BuildContext context) {
    String _inputText = widget.value;
    return Container(
      padding: widget.padding == null ? EdgeInsets.fromLTRB(15, 5, 10, 5) : widget.padding,
      color: widget.color == null ? Colors.white : widget.color,
      child: Row(
        children: <Widget>[
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                Offstage(
                  offstage: widget.labelWidth == 0,
                  child: Row(
                    children: [
                      Container(
                        width: widget.labelWidth,
                        child: widget.require
                            ? RichTextWidget(
                                label: '* ',
                                labelStyle: TextStyle(color: Colors.red, fontSize: 18),
                                value: '${widget.label}',
                                valueStyle: TextStyle(
                                  color: Colors.black,
                                  fontSize: 15.0,
                                ),
                              )
                            : Text(
                                '${widget.label}',
                                style: new TextStyle(
                                  color: Colors.black,
                                  fontSize: 15.0,
                                ),
                              ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(left: 20),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Container(
                    child: InkWell(
                      onTap: widget.inputTap,
                      child: new TextFormField(
                        readOnly: widget.readOnly,
                        textAlignVertical: TextAlignVertical.center,
                        inputFormatters: widget.inputFormatters,
                        validator: widget.validator,
                        obscureText: widget.obscureText,
                        autofocus: widget.autoFocus,
                        style: TextStyle(
                            color: widget.enabled
                                ? Colors.black
                                : widget.isSelect
                                    ? Colors.black
                                    : Colors.grey[400]),
                        onTap: widget.ontap,
                        enabled: widget.enabled,
                        controller: TextEditingController.fromValue(TextEditingValue(
                            text: '${widget.value == null ? "" : widget.value}', //判断keyword是否为空
                            // 保持光标在最后
                            selection: TextSelection.fromPosition(
                                TextPosition(affinity: TextAffinity.downstream, offset: '${widget.value}'.length)))),
                        keyboardType: widget.textInputType,
                        maxLines: widget.textInputType == TextInputType.multiline ? 2 : 1,
                        focusNode: widget.focusNode,
                        textInputAction: widget.textInputAction,
                        onEditingComplete: widget.onEditingComplete,
                        decoration: widget.decoration != null
                            ? widget.decoration
                            : InputDecoration(
                                errorStyle: TextStyle(fontSize: 14, height: 1),
                                errorMaxLines: 1,
                                suffixIcon: checkNull(_inputText) && (widget.enabled)
                                    ? new Container(
                                        width: 20.0,
                                        height: 20.0,
                                        child: new IconButton(
                                          alignment: Alignment.center,
                                          padding: const EdgeInsets.all(0.0),
                                          iconSize: 18.0,
                                          icon: Icon(Icons.cancel),
                                          onPressed: () {
                                            setState(() {
                                              _inputText = widget.textInputType == TextInputType.number ? "" : "";
                                              showClear = widget.textInputType == TextInputType.number
                                                  ? false
                                                  : _inputText.isNotEmpty;
                                              widget.onChanged!(_inputText);
                                            });
                                          },
                                        ),
                                      )
                                    : new Text(""),
                                border: InputBorder.none,
                                hintText: '${widget.hintText}',
                                hintStyle: TextStyle(
                                    color: Colors.grey, fontSize: 14.0, textBaseline: TextBaseline.ideographic),
                                contentPadding: EdgeInsets.fromLTRB(5.0, 0.0, 5.0, 0.0),
                                counterText: "",
                              ),
                        onChanged: (value) {
                          _inputText = value;
                          showClear = _inputText.isNotEmpty;
                          widget.onChanged!(_inputText);
                        },
                      ),
                    ),
                  ),
                ),
                Offstage(
                  offstage: widget.suffixIcon == null,
                  child: widget.suffixIcon,
                )
              ],
            ),
          ),
          Padding(padding: EdgeInsets.only(left: 5)),
        ],
      ),
    );
  }
  String defaultValidator(String? value) {
    return '';
  }
}
