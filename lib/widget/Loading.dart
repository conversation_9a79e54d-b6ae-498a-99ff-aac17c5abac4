import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class Loading extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return new Center(
      child: new Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          new SpinKitCircle(color: Theme.of(context).primaryColor),
          new Padding(padding: EdgeInsets.only(top: 10.0)),
          new Text(
            '正在加载',
            style: AppConstant.normalText,
          ),
        ],
      ),
    );
  }
}
