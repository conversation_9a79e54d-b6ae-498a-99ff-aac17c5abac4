import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/widget/AppCardItem.dart';
import 'package:flutter/material.dart';

/// 详情issue列表头部，PreferredSizeWidget
/// Created by denonzhu
/// Date: 2018-07-19

typedef void SelectItemChanged<int>(int value);

class AppSelectItemWidget extends StatefulWidget implements PreferredSizeWidget {
  final List<String> itemNames;

  final SelectItemChanged selectItemChanged;

  final double elevation;

  final double height;

  final EdgeInsets margin;

  AppSelectItemWidget(
    this.itemNames,
    this.selectItemChanged, {
    this.elevation = 5.0,
    this.height = 70.0,
    this.margin = const EdgeInsets.all(10.0),
  });

  @override
  _AppSelectItemWidgetState createState() => _AppSelectItemWidgetState();

  @override
  Size get preferredSize {
    return new Size.fromHeight(height);
  }
}

class _AppSelectItemWidgetState extends State<AppSelectItemWidget> {
  int selectIndex = 0;

  _AppSelectItemWidgetState();

  _renderItem(String name, int index) {
    var style = index == selectIndex ? AppConstant.middleTextWhite : AppConstant.middleSubLightText;
    return new Expanded(
      child: RawMaterialButton(
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          constraints: const BoxConstraints(minWidth: 0.0, minHeight: 0.0),
          padding: EdgeInsets.all(10.0),
          child: new Text(
            name,
            style: style,
            textAlign: TextAlign.center,
          ),
          onPressed: () {
            if (selectIndex != index) {
              widget.selectItemChanged.call(index);
            }
            setState(() {
              selectIndex = index;
            });
          }),
    );
  }

  _renderList() {
    List<Widget> list = [];
    for (int i = 0; i < widget.itemNames.length; i++) {
      if (i == widget.itemNames.length - 1) {
        list.add(_renderItem(widget.itemNames[i], i));
      } else {
        list.add(_renderItem(widget.itemNames[i], i));
        list.add(new Container(width: 1.0, height: 25.0, color: Color(AppColors.subLightTextColor)));
      }
    }
    return list;
  }

  @override
  Widget build(BuildContext context) {
    return new AppCardItem(
        elevation: widget.elevation,
        margin: widget.margin,
        color: Theme.of(context).primaryColor,
        shape: new RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(4.0)),
        ),
        child: new Row(
          children: _renderList(),
        ));
  }
}
