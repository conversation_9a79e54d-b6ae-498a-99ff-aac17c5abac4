import 'package:flutter/material.dart';

class NumberControllerWidget extends StatefulWidget {
  final double height;
  String num;
  final ValueChanged<String> onValueChanged;
  String maxNum;
  String minNum;

  NumberControllerWidget(
      {required Key key,
      this.height = 36.0,
      this.num = '0',
      this.maxNum = '',
      this.minNum = '0',
      required this.onValueChanged})
      : super(key: key);

  @override
  _NumberControllerWidgetState createState() {
    return _NumberControllerWidgetState();
  }
}

class _NumberControllerWidgetState extends State<NumberControllerWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(2.0)),
        border: Border.all(color: Colors.grey, width: 0.5),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          GestureDetector(
            onTap: _minusNum,
            child: Icon(Icons.remove),
          ),
          Container(
            width: 0.5,
            color: Colors.grey,
          ),
          Container(
            width: 32.0,
            alignment: Alignment.center,
            child: Text(
              widget.num,
              maxLines: 1,
              style: TextStyle(fontSize: 14.0, color: Colors.black),
            ),
          ),
          Container(
            width: 0.5,
            color: Colors.grey,
          ),
          GestureDetector(
            onTap: _addNum,
            child: Icon(Icons.add),
          ),
        ],
      ),
    );
  }

  void _minusNum() {
    int number = int.parse(widget.num);
    if (number > int.parse(widget.minNum)) {
      if (number == 0) {
        return;
      }

      setState(() {
        widget.num = (number -= 1).toString();
        if (widget.onValueChanged != null) {
          widget.onValueChanged('$number');
        }
      });
    }
  }

  void _addNum() {
    int number = int.parse(widget.num);
    if (widget.maxNum != '') {
      if (number < int.parse(widget.maxNum)) {
        setState(() {
          widget.num = (number += 1).toString();
          if (widget.onValueChanged != null) {
            widget.onValueChanged('$number');
          }
        });
      }
    } else {
      setState(() {
        widget.num = (number += 1).toString();
        if (widget.onValueChanged != null) {
          widget.onValueChanged('$number');
        }
      });
    }
  }
}
