import 'package:flutter/material.dart';

///  自定义 键盘 按钮
class CustomKbBtn extends StatefulWidget {
  ///  按钮显示的文本内容
  late double width;
  late double height;
  late String text;
  late int flex;

  CustomKbBtn(this.width, this.height, this.text, {Key? key, this.callback, this.flex = 2}) : super(key: key);

  ///  按钮 点击事件的回调函数
  final callback;

  @override
  State<StatefulWidget> createState() {
    return ButtonState();
  }
}

class ButtonState extends State<CustomKbBtn> {
  ///回调函数执行体
  var backMethod;

  void back() {
    widget.callback('$backMethod');
  }

  @override
  Widget build(BuildContext context) {
    bool orientation = MediaQuery.of(context).orientation == Orientation.landscape;
    bool isDelete = widget.text == '删除';
    bool isUpper = widget.text == '大写';
    bool showIcon = isDelete || isUpper;
    IconData icon = Icons.backspace_outlined;
    if (isUpper) {
      icon = Icons.upload;
    }
    bool isDown = widget.text == '搜索' || widget.text == '完成';
    return Expanded(
      flex: widget.flex,
      child: TextButton(
        style: ButtonStyle(
            maximumSize: MaterialStateProperty.resolveWith((states) {
              return Size(widget.width, widget.height);
            }),
            minimumSize: MaterialStateProperty.resolveWith((states) {
              return Size(widget.width, widget.height);
            }),
            foregroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.white;
            }),
            backgroundColor: MaterialStateProperty.resolveWith((states) {
              if (states.contains(MaterialState.pressed)) {
                return isDown ? Theme.of(context).primaryColorLight : Color(0x87484D50);
              }
              return isDown ? Theme.of(context).primaryColor : Color(0xFF484D50);
            }),
            shape: MaterialStateProperty.all(RoundedRectangleBorder(borderRadius: BorderRadius.circular(6.0)))),
        // 按钮点击事件
        onPressed: back,
        child: showIcon
            ? Icon(icon, size: 32.0)
            : Text(
                widget.text,
                style: TextStyle(fontSize: isNumber(widget.text) ? 30 : 20, fontWeight: FontWeight.w500),
              ),
      ),
    );
  }

  static isNumber(String text) {
    RegExp exp = RegExp(r'^[0-9]$');
    return exp.hasMatch(text);
  }
}
