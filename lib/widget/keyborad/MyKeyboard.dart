import 'package:cabinet_flutter_app/common/utils/ThrottleUtil.dart';
import 'package:cabinet_flutter_app/widget/keyborad/MyKeyEvent.dart';
import 'package:flutter/material.dart';

import 'CustomKbBtn.dart';

/// 自定义密码 键盘

class MyKeyboard extends StatefulWidget {
  final bool isShow;
  final TextInputType textInputType;
  final TextInputAction? textInputAction;
  final double? width;
  final double? height;

  // ignore: prefer_typing_uninitialized_variables
  final callback;

  const MyKeyboard(this.isShow, this.callback,
      {super.key,
      this.textInputType = TextInputType.number,
      this.textInputAction = TextInputAction.done,
      this.width,
      this.height});

  @override
  State<StatefulWidget> createState() {
    return MyKeyboardStat();
  }
}

class MyKeyboardStat extends State<MyKeyboard> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  double width = 0;
  double height = 0;
  double itemWidth = 0;
  double itemHeight = 0;
  bool isUpper = false;

  ThrottleUtil throttleUtil = ThrottleUtil();
  List<String> brandList = [
    'JT',
    'JD',
    'YT',
    'SF',
    'JDV',
    'DPK',
  ];

  /// 定义 确定 按钮 接口  暴露给调用方
  ///回调函数执行体
  // ignore: prefer_typing_uninitialized_variables
  var backMethod;

  void onClearChange() {
    widget.callback(MyKeyEvent("clear"));
  }

  void onValueChange(BuildContext cont, String value) {
    widget.callback(MyKeyEvent(value));
  }

  /// 点击删除
  void onDeleteChange() {
    widget.callback(MyKeyEvent("del"));
  }

  /// 切换字母键盘
  void onTypeChange2Text() {
    widget.callback(MyKeyEvent("change2Text"));
  }

  /// 切换数字键盘
  void onTypeChange2Number() {
    widget.callback(MyKeyEvent("change2Number"));
  }

  /// 点击完成
  void onDoneChange() {
    widget.callback(MyKeyEvent("done"));
  }

  /// 点击重置
  void onResetChange() {
    widget.callback(MyKeyEvent("reset"));
  }

  /// 点击隐藏
  void onHide() {
    widget.callback(MyKeyEvent("hide"));
  }

  /// 点击大小写切换
  void onCaseChange() {
    isUpper = !isUpper;
    setState(() {});
  }

  getChar(String value) {
    if (isUpper) {
      return value.toUpperCase();
    }
    return value.toLowerCase();
  }

  getDownChar() {
    if (widget.textInputAction == TextInputAction.search) {
      return '搜索';
    }
    return '完成';
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height * .40;
    itemWidth = width / 4;
    itemHeight = height / 5;
    return Container(
      child: Offstage(offstage: !widget.isShow, child: buildKeyboardByTextInputType()),
    );
  }

  buildKeyboardByTextInputType() {
    if (widget.textInputType == TextInputType.number) {
      return numberKeyboard();
    }
    if (widget.textInputType == TextInputType.name) {
      return textKeyboard();
    }
  }

  /// 字母键盘
  textKeyboard() {
    return SizedBox(
        key: _scaffoldKey,
        width: width,
        height: widget.height,
        child: Container(
          color: Color(0xFF2B3032),
          child: Column(
            children: <Widget>[
              Container(
                height: itemHeight - 12,
                width: width,
                child: Container(
                  child: Row(
                    children: [
                      Container(
                        child: Expanded(
                            child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children: buildBrandSelect(),
                          ),
                        )),
                      ),
                      Container(
                        child: IconButton(
                          icon: Icon(
                            Icons.keyboard_hide_outlined,
                            size: 28,
                            color: Colors.grey.shade50,
                          ),
                          onPressed: () {
                            onHide();
                          },
                        ),
                      )
                    ],
                  ),
                ),
              ),

              ///  第一行
              Container(
                width: width,
                height: itemHeight - 3,
                child: Row(
                  children: <Widget>[
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getChar('q'),
                        callback: (val) => onValueChange(context, getChar('q'))),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getChar('w'),
                        callback: (val) => onValueChange(context, getChar('w'))),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getChar('e'),
                        callback: (val) => onValueChange(context, getChar('e'))),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getChar('r'),
                        callback: (val) => onValueChange(context, getChar('r'))),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getChar('t'),
                        callback: (val) => onValueChange(context, getChar('t'))),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getChar('y'),
                        callback: (val) => onValueChange(context, getChar('y'))),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getChar('u'),
                        callback: (val) => onValueChange(context, getChar('u'))),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getChar('i'),
                        callback: (val) => onValueChange(context, getChar('i'))),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getChar('o'),
                        callback: (val) => onValueChange(context, getChar('o'))),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getChar('p'),
                        callback: (val) => onValueChange(context, getChar('p'))),
                    Container(width: 4),
                  ],
                ),
              ),
              Container(height: 4),

              ///  第二行
              Container(
                width: width,
                height: itemHeight - 3,
                child: Row(
                  children: <Widget>[
                    Container(width: 4),
                    Expanded(flex: 2, child: Container()),
                    Container(width: 2),
                    CustomKbBtn(itemWidth, itemHeight, getChar('a'),
                        callback: (val) => onValueChange(context, getChar('a'))),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getChar('s'),
                        callback: (val) => onValueChange(context, getChar('s'))),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getChar('d'),
                        callback: (val) => onValueChange(context, getChar('d'))),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getChar('f'),
                        callback: (val) => onValueChange(context, getChar('f'))),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getChar('g'),
                        callback: (val) => onValueChange(context, getChar('g'))),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getChar('h'),
                        callback: (val) => onValueChange(context, getChar('h'))),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getChar('j'),
                        callback: (val) => onValueChange(context, getChar('j'))),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getChar('k'),
                        callback: (val) => onValueChange(context, getChar('k'))),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getChar('l'),
                        callback: (val) => onValueChange(context, getChar('l'))),
                    Container(width: 2),
                    Expanded(flex: 2, child: Container()),
                    Container(width: 4),
                  ],
                ),
              ),

              Container(height: 4),

              ///  第三行
              Container(
                width: width,
                height: itemHeight - 3,
                child: Row(
                  children: <Widget>[
                    Container(width: 6),
                    CustomKbBtn(itemWidth, itemHeight, '大写', flex: 3, callback: (val) => onCaseChange()),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getChar('z'),
                        callback: (val) => onValueChange(context, getChar('z'))),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getChar('x'),
                        callback: (val) => onValueChange(context, getChar('x'))),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getChar('c'),
                        callback: (val) => onValueChange(context, getChar('c'))),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getChar('v'),
                        callback: (val) => onValueChange(context, getChar('v'))),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getChar('b'),
                        callback: (val) => onValueChange(context, getChar('b'))),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getChar('n'),
                        callback: (val) => onValueChange(context, getChar('n'))),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getChar('m'),
                        callback: (val) => onValueChange(context, getChar('m'))),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, '删除', flex: 3, callback: (val) => onDeleteChange()),
                    Container(width: 6),
                  ],
                ),
              ),

              Container(height: 4),

              ///  第四行
              Container(
                width: width,
                height: itemHeight - 3,
                child: Row(
                  children: <Widget>[
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, '123', flex: 3, callback: (val) => onTypeChange2Number()),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, '-', flex: 2, callback: (val) => onValueChange(context, "-")),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, ' ', flex: 10, callback: (val) => onValueChange(context, " ")),
                    Container(width: 4),
                    CustomKbBtn(itemWidth, itemHeight, getDownChar(), flex: 5, callback: (val) => onDoneChange()),
                    Container(width: 4),
                  ],
                ),
              ),
              Container(width: 4),
            ],
          ),
        ));
  }

  /// 数字键盘
  numberKeyboard() {
    return SizedBox(
        key: _scaffoldKey,
        width: width,
        height: widget.height,
        child: Container(
          color: Color(0xFF2B3032),
          child: Column(
            children: <Widget>[
              ///  第一行
              Container(
                height: itemHeight - 12,
                width: width,
                child: Container(
                  child: Row(
                    children: [
                      Container(
                        child: Expanded(
                            child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children: buildBrandSelect(),
                          ),
                        )),
                      ),
                      Container(
                        child: IconButton(
                          icon: Icon(
                            Icons.keyboard_hide_outlined,
                            size: 28,
                            color: Colors.grey.shade50,
                          ),
                          onPressed: () {
                            onHide();
                          },
                        ),
                      )
                    ],
                  ),
                ),
              ),

              Container(
                width: width,
                height: itemHeight * 4,
                child: Row(
                  children: [
                    Container(width: 4),
                    Container(
                      width: itemWidth - 5,
                      height: itemHeight * 4,
                      child: Column(
                        children: [
                          Container(height: 4),
                          CustomKbBtn(itemWidth, itemHeight, '1', callback: (val) => onValueChange(context, "1")),
                          Container(height: 4),
                          CustomKbBtn(itemWidth, itemHeight, '4', callback: (val) => onValueChange(context, "4")),
                          Container(height: 4),
                          CustomKbBtn(itemWidth, itemHeight, '7', callback: (val) => onValueChange(context, "7")),
                          Container(height: 4),
                          CustomKbBtn(itemWidth, itemHeight, '.', callback: (val) => onValueChange(context, ".")),
                          Container(height: 4),
                        ],
                      ),
                    ),
                    Container(width: 4),
                    Container(
                      width: itemWidth - 5,
                      height: itemHeight * 4 + 12,
                      child: Column(
                        children: [
                          Container(height: 4),
                          CustomKbBtn(itemWidth, itemHeight, '2', callback: (val) => onValueChange(context, "2")),
                          Container(height: 4),
                          CustomKbBtn(itemWidth, itemHeight, '5', callback: (val) => onValueChange(context, "5")),
                          Container(height: 4),
                          CustomKbBtn(itemWidth, itemHeight, '8', callback: (val) => onValueChange(context, "8")),
                          Container(height: 4),
                          CustomKbBtn(itemWidth, itemHeight, '0', callback: (val) => onValueChange(context, "0")),
                          Container(height: 4),
                        ],
                      ),
                    ),
                    Container(width: 4),
                    Container(
                      width: itemWidth - 5,
                      height: itemHeight * 4 + 12,
                      child: Column(
                        children: [
                          Container(height: 4),
                          CustomKbBtn(itemWidth, itemHeight, '3', callback: (val) => onValueChange(context, "3")),
                          Container(height: 4),
                          CustomKbBtn(itemWidth, itemHeight, '6', callback: (val) => onValueChange(context, "6")),
                          Container(height: 4),
                          CustomKbBtn(itemWidth, itemHeight, '9', callback: (val) => onValueChange(context, "9")),
                          Container(height: 4),
                          CustomKbBtn(itemWidth, itemHeight, '*', callback: (val) => onValueChange(context, "*")),
                          Container(height: 4),
                        ],
                      ),
                    ),
                    Container(width: 4),
                    Container(
                      width: itemWidth - 5,
                      height: itemHeight * 4 + 12,
                      child: Column(
                        children: [
                          Container(height: 4),
                          CustomKbBtn(itemWidth, itemHeight, '删除', callback: (val) => onDeleteChange()),
                          Container(height: 4),
                          CustomKbBtn(itemWidth, itemHeight, '清空', callback: (val) => onClearChange()),
                          Container(height: 4),
                          widget.textInputAction == TextInputAction.search
                              ? Container(
                                  width: itemWidth,
                                  height: itemHeight * 2 - 4,
                                  child: Column(
                                    children: [
                                      CustomKbBtn(itemWidth, itemHeight * 2 + 4, getDownChar(),flex: 2,
                                          callback: (val) => onDoneChange()),
                                    ],
                                  ),
                                )
                              : Container(
                                  width: itemWidth,
                                  height: itemHeight * 2 - 4,
                                  child: Column(
                                    children: [
                                      CustomKbBtn(itemWidth, itemHeight, 'ABC', callback: (val) => onTypeChange2Text()),
                                      Container(height: 4),
                                      CustomKbBtn(itemWidth, itemHeight, getDownChar(),
                                          callback: (val) => onDoneChange()),
                                    ],
                                  ),
                                ),
                          Container(height: 4),
                        ],
                      ),
                    ),
                    Container(width: 4),
                  ],
                ),
              )
            ],
          ),
        ));
  }

  buildBrandSelect() {
    if (widget.textInputType == TextInputType.number) {
      return <Widget>[];
    } else {
      return brandList.map((e) {
        return InkWell(
          onTap: () => throttleUtil.throttle(() {
            onValueChange(context, e);
          }),
          child: ClipRRect(
            borderRadius: BorderRadius.only(bottomRight: Radius.circular(8), topRight: Radius.circular(8)),
            child: Container(
              padding: EdgeInsets.only(right: 15, left: 15),
              decoration: BoxDecoration(border: Border(right: BorderSide(width: 1, color: Colors.grey.shade400))),
              child: Text(e, style: TextStyle(color: Colors.white, fontSize: 20)),
            ),
          ),
        );
      }).toList();
    }
  }
}
