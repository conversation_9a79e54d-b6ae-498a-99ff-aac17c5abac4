import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:flutter/material.dart';

/// 搜索输入框
/// Created by denonzhu
/// Date: 2018-07-20
class AppSearchAdressInputWidget extends StatelessWidget {
  final ValueChanged<String> onChanged;

  final ValueChanged<String> onSubmitted;

  final VoidCallback? onSubmitPressed;

  final VoidCallback? valueClear;

  final String? hitText;
  final Widget? prefixIcon;
  final Widget? suffixIcon;

  final FocusNode? focusNode;

  final TextEditingController? controller;
  final bool enabled;
  final bool readOnly;

  final TextInputAction? textInputAction;
  final TextInputType? keyboardType;

  final bool? showIcon;

  final IconData? icon;

  final Color? iconColor;

  AppSearchAdressInputWidget(
      {required this.onChanged,
      required this.onSubmitted,
      this.onSubmitPressed,
      this.valueClear,
      this.hitText,
      this.prefixIcon,
      this.suffixIcon,
      this.focusNode,
      this.controller,
      this.textInputAction = TextInputAction.done,
      this.keyboardType = TextInputType.text,
      this.showIcon,
      this.icon,
      this.iconColor,
      this.borderRadius = 5.0,
      this.enabled = true,
      this.readOnly = false,
      this.opacity = 1.0,
      this.bgColor = Colors.white,
      this.value});

  final double? borderRadius;

  final double? opacity;

  final Color? bgColor;

  final String? value;

  @override
  Widget build(BuildContext context) {
    if (focusNode != null) {
      focusNode?.addListener(() {
        if (focusNode?.hasFocus != null) {
          controller?.selection = TextSelection(baseOffset: 0, extentOffset: controller?.text.length ?? 0);
        }
      });
    }
    return Opacity(
      opacity: opacity ?? 1,
      child: Container(
        height: 40,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(borderRadius ?? 0)),
          color: bgColor,
        ),
        padding: EdgeInsets.only(left: prefixIcon == null ? 15 : 0.0, top: 0, right: 5.0, bottom: 0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Expanded(
              child: TextField(
                focusNode: focusNode,
                controller: controller,
                autofocus: false,
                showCursor: true,
                enabled: enabled,
                maxLines: 1,
                maxLength: 24,
                readOnly: readOnly,
                keyboardType: keyboardType,
                textInputAction: textInputAction,
                decoration: InputDecoration(
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(vertical: 12.0),
                  hintText: hitText,
                  suffixIcon: suffixIcon ?? null,
                  prefixIcon: prefixIcon ?? null,
                  hintStyle: TextStyle(
                    color: DefaultConfig().configs.GREY_LIGHT1_COLOR,
                    fontSize: AppConstant.smallTextSize,
                  ),
                  counterText: "", //此处控制最大字符是否显示
                ),
                style: TextStyle(
                  color: Color(AppColors.mainTextColor),
                  fontSize: AppConstant.smallTextSize,
                  textBaseline: TextBaseline.alphabetic,
                ),
                onChanged: onChanged,
                onSubmitted: onSubmitted,
              ),
            ),
            (value != null && value != '')
                ? RawMaterialButton(
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    padding: const EdgeInsets.only(right: 5.0, left: 10.0),
                    constraints: const BoxConstraints(minWidth: 0.0, minHeight: 0.0),
                    child: Icon(
                      Icons.clear,
                      size: 20.0,
                      color: Colors.grey.shade500,
                    ),
                    onPressed: valueClear,
                  )
                : (showIcon != null && showIcon!
                    ? RawMaterialButton(
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        padding: const EdgeInsets.only(right: 5.0, left: 10.0),
                        constraints: const BoxConstraints(minWidth: 0.0, minHeight: 0.0),
                        child: Icon(
                          this.icon != null ? this.icon : AppICons.SEARCH,
                          size: 20.0,
                          color: iconColor ?? Theme.of(context).primaryColorDark,
                        ),
                        onPressed: onSubmitPressed,
                      )
                    : Container()),
          ],
        ),
      ),
    );
  }
}
