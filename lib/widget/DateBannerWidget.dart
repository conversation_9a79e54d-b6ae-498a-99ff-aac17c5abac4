import 'package:cabinet_flutter_app/widget/SelectIconWidget.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class DateBannerWidget extends StatefulWidget {
  final VoidCallback? cb;
  final String date;
  final int? count;
  final bool isSelect;
  final bool showSelect;
  final bool showCount;

  DateBannerWidget(this.date,
      {Key? key, this.cb, this.count, this.showCount = true, this.isSelect = false, this.showSelect = false})
      : super(key: key);

  @override
  _DateBannerWidgetState createState() => _DateBannerWidgetState();
}

class _DateBannerWidgetState extends State<DateBannerWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    String today = new DateFormat("yyyy-MM-dd").format(DateTime.now());
    return Material(
      child: InkWell(
        onTap: () async {
          widget.cb!();
        },
        child: new Container(
          padding: EdgeInsets.fromLTRB(10.0, 5.0, 10.0, 5.0),
          decoration: new BoxDecoration(
            color: Color(0xFFEEEEEE),
          ),
          child: new Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Row(
                children: [
                  Offstage(
                    offstage: !widget.showSelect,
                    child: SelectIconWidget(widget.isSelect, Colors.grey.shade300, fontSize: 20),
                  ),
                  new Text(
                    '${widget.date == today ? '今天' : widget.date}',
                    style: new TextStyle(color: Colors.grey.shade500, fontSize: 12.0),
                  )
                ],
              ),
              Offstage(
                offstage: !widget.showCount,
                child: new Text(
                  '共${widget.count}条',
                  style: new TextStyle(color: Colors.grey.shade500, fontSize: 12.0),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
