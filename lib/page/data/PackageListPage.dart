import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/WaybillDao.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/package_search_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/package_view_entity.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/page/data/PackageListKdyPage.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class PackageListPage extends StatefulWidget {
  final PackageType type;
  final String? cabinetCode;
  final int? keepEffectDay;
  final String? query;

  PackageListPage(this.type, this.cabinetCode, this.keepEffectDay, this.query, {Key? key}) : super(key: key);

  @override
  _PackageListPageState createState() => _PackageListPageState();
}

class _PackageListPageState extends State<PackageListPage> {
  final GlobalKey<ScaffoldState> _cabinetRetentionListKey = GlobalKey<ScaffoldState>();
  int total = 0;
  int failTotal = 0;
  Map<dynamic, dynamic> timeMap = {};
  PackageSearchEntity query = new PackageSearchEntity();
  CabinetEntity cabinet = new CabinetEntity();
  Map<dynamic, dynamic> listTypeState = {
    PackageType.ZL: ListType.RETENTION,
    PackageType.DQ: ListType.WAIT,
    PackageType.YQC: ListType.REMOVED
  };
  bool isCourier = true;

  @override
  initState() {
    this.initData();
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    isCourier = CheckUtils.isCourier(context);
  }

  initData() async {
    ///4 室外柜  6室内柜
    timeMap = {'oneDay': '大于一天', 'twoDays': '大于两天', 'before3Days': '大于三天', 'before7Days': '大于七天', 'all': '全部'};
    setDefaultQuery();
  }

  /// 设置查询条件默认值
  setDefaultQuery() {
    if (widget.cabinetCode != null) {
      query.cabinetLocationCode = widget.cabinetCode!;
    }
    if (widget.keepEffectDay != null) {
      query.keepEffectDay = widget.keepEffectDay!;
    }

    /// 上页面传来的点位code
    query.brandCode = 'ALL';

    /// 所有快递品牌
    query.timeRanger = 'all';
    query.beginYmd = new DateFormat("yyyy-MM-dd").format(DateTime.now());
    query.endYmd = new DateFormat("yyyy-MM-dd").format(DateTime.now());
  }

  @override
  void dispose() {
    super.dispose();
  }

  // 上拉加载更多

  /// 重新通知
  reNotice(PackageViewEntity item) {
    WaybillDao.reNotice(context, item.receiverMobile, item.id);
  }

  getTitle() {
    String title = '包裹';
    if (widget.type == PackageType.ZL) {
      title = '滞留' + title;
    } else if (widget.type == PackageType.DQ) {
      title = '待取' + title;
    } else if (widget.type == PackageType.YQC) {
      title = '已取' + title;
    } else {
      title = title + '列表';
    }
    return title;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _cabinetRetentionListKey,
      backgroundColor: DefaultConfig().configs.BG_COLOR,
      appBar: new AppCustomerBar(
          title: AppbarTitle(
            title: getTitle(),
            isCenter: true,
          ),
          actions: [Container(width: 60)]),
      body: buildItemWidget(), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }

  buildItemWidget() {
    return PackageListKdyPage(isCourier, widget.type, widget.cabinetCode,
        keepEffectDay: widget.keepEffectDay, query: widget.query, marginTop: isCourier ? 10 : 0);
  }
}
