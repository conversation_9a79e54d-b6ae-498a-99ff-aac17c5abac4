import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/dao/WaybillDao.dart';
import 'package:cabinet_flutter_app/common/entitys/package_view_entity.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/ThrottleUtil.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppListState.dart';
import 'package:cabinet_flutter_app/widget/AppPullLoadWidget.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:fluttertoast/fluttertoast.dart';

class OrderAbnormalPage extends StatefulWidget {
  OrderAbnormalPage({Key? key}) : super(key: key);

  @override
  _OrderAbnormalPageState createState() => _OrderAbnormalPageState();
}

class _OrderAbnormalPageState extends State<OrderAbnormalPage>
    with AutomaticKeepAliveClientMixin<OrderAbnormalPage>, AppListState<OrderAbnormalPage> {
  final GlobalKey<ScaffoldState> _cabinetRetentionListKey = GlobalKey<ScaffoldState>();
  bool isCourier = true;
  List checkList = [];
  List _checkList = [];
  ThrottleUtil throttleUtil = ThrottleUtil();
  String action = '';

  @override
  initState() {
    this.initData();
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    isCourier = CheckUtils.isCourier(context);
  }

  initData() async {
    action = 'INBOUND';
    handleRefresh();
  }

  courierGatewayResend(String waybillId) async {
    EasyLoading.show(status: '正在重投...', maskType: EasyLoadingMaskType.black);
    var res = await CourierDao.gatewayResend(waybillId, action);
    EasyLoading.dismiss();
    if (res.result) {
      Fluttertoast.showToast(msg: '重投成功');
      await Future.delayed(Duration(milliseconds: 100));
    }
  }

  reInboundChannel(String waybillId) async {
    EasyLoading.show(status: '正在修改...', maskType: EasyLoadingMaskType.black);
    var res = await CourierDao.reInboundChannel(waybillId);
    EasyLoading.dismiss();
    if (res.result) {
      Fluttertoast.showToast(msg: '操作成功');
      await Future.delayed(Duration(milliseconds: 100));
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  batchOperation(String type) async {
    List newList = [];
    for (var data in checkList) {
      newList.add(data['waybillId']);
    }
    newList = newList.toSet().toList();
    if (type == 'SMS') {
      bool result = await CommonUtils.confirm(context, '是否给客户再次发送取件通知', title: '重新通知');
      if (result) {
        for (var item in newList) {
          await WaybillDao.notice('', item);
          await Future.delayed(Duration(milliseconds: 100));
        }
        await handleRefresh();
      }
    } else if (type == 'RESEND') {
      bool result = await CommonUtils.confirm(context, '是否修改默认通道并重投', title: '修改默认通道并重投');
      if (result) {
        for (var item in newList) {
          await reInboundChannel(item);
        }
        await handleRefresh();
      }
    } else {
      bool result = await CommonUtils.confirm(context, '是否进行重投操作', title: '重投操作');
      if (result) {
        for (var item in newList) {
          await courierGatewayResend(item);
        }
        await handleRefresh();
      }
    }
  }

  ignore(Map<String, dynamic> item) async {
    EasyLoading.show(status: '正在忽略...', maskType: EasyLoadingMaskType.black);
    var res = await CourierDao.orderExceptionLogDel(item['waybillId'], action);
    EasyLoading.dismiss();
    if (res.result) {
      Fluttertoast.showToast(msg: '忽略成功');
      handleRefresh();
    }
  }

  _renderEventItem(index) {
    var item = dataList[index];
    bool isCheck = _checkList.contains(item['id']);
    return Container(
        padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
        child: Container(
            padding: EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(5.0)),
            ),
            child: Column(children: [
              Row(
                children: [
                  Container(
                    width: 20.0,
                    height: 20.0,
                    margin: EdgeInsets.fromLTRB(0, 0.0, 10.0, 0.0),
                    child: Checkbox(
                      value: isCheck,
                      activeColor: Theme.of(context).primaryColor,
                      onChanged: (bool? value) {
                        setState(() {
                          if (isCheck) {
                            _checkList.remove(item['id']);
                            checkList.remove(item);
                          } else {
                            _checkList.add(item['id']);
                            checkList.add(item);
                          }
                        });
                      },
                    ),
                  ),
                  Container(
                    child: Text(
                      '${item['shopName']}',
                      style: TextStyle(fontSize: 16, color: Colors.black),
                    ),
                  ),
                ],
              ),
              Container(
                height: 1.5,
                width: double.infinity,
                margin: EdgeInsets.only(left: 0, top: 10, right: 0, bottom: 10),
                color: DefaultConfig().configs.BG_COLOR,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    child: Text(
                      '订单号',
                      style: TextStyle(fontSize: 16, color: Colors.black),
                    ),
                  ),
                  Container(
                    child: Text(
                      '${item['orderNo']}',
                      style: TextStyle(fontSize: 16, color: Colors.black),
                    ),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    child: Text(
                      '快递单号',
                      style: TextStyle(fontSize: 16, color: Colors.black),
                    ),
                  ),
                  Container(
                    child: Text(
                      '${item['waybillNo']}',
                      style: TextStyle(fontSize: 16, color: Colors.black),
                    ),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    child: Text(
                      '快递品牌',
                      style: TextStyle(fontSize: 16, color: Colors.black),
                    ),
                  ),
                  Container(
                    child: Text(
                      '${DefaultConfig().configs.EXPRESS[item['brandCode']]}',
                      style: TextStyle(fontSize: 16, color: Colors.black),
                    ),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    child: Text(
                      '驿站品牌',
                      style: TextStyle(fontSize: 16, color: Colors.black),
                    ),
                  ),
                  Container(
                    child: Text(
                      '${DefaultConfig().configs.brandMap[item['yzChannel']] ?? '无'}',
                      style: TextStyle(fontSize: 16, color: Colors.black),
                    ),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    child: Text(
                      '异常原因',
                      style: TextStyle(fontSize: 16, color: Colors.black),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return AlertDialog(
                                title: Text('异常原因'),
                                content: Text('${item['exception']}'),
                                actions: <Widget>[
                                  TextButton(
                                      child: Text('确定'),
                                      onPressed: () {
                                        Navigator.of(context).pop();
                                      })
                                ]);
                          });
                    },
                    child: Text('查看原因', style: TextStyle(fontSize: 16, color: Theme.of(context).primaryColor)),
                  ),
                ],
              ),
              Container(
                height: 1.5,
                width: double.infinity,
                margin: EdgeInsets.only(left: 0, top: 10, right: 0, bottom: 0),
                color: DefaultConfig().configs.BG_COLOR,
              ),
              Wrap(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      OutlinedButton(
                        onPressed: () {
                          /// 重发短信
                          ignore(item);
                        },
                        child: Text(
                          '忽略',
                          style: TextStyle(fontSize: 12),
                        ),
                        style: ButtonStyle(
                            minimumSize: MaterialStateProperty.all(Size(70, 36)),
                            foregroundColor: MaterialStateProperty.resolveWith((states) {
                              return Colors.white;
                            }),
                            backgroundColor: MaterialStateProperty.resolveWith((states) {
                              return Theme.of(context).primaryColor;
                            })),
                      ),
                    ],
                  ),
                ],
              ),
            ])));
  }

  buildSearchItem(String title, value, {VoidCallback? cb}) {
    return InkWell(
      onTap: () => throttleUtil.throttle(() {
        if (cb != null) {
          cb();
        }
      }),
      child: Container(
        margin: EdgeInsets.only(right: 10),
        padding: EdgeInsets.fromLTRB(5, 3, 5, 3),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: action == value ? Theme.of(context).primaryColor : Colors.grey.shade200),
        child: Text(title, style: TextStyle(color: action == value ? Colors.white : Colors.black87)),
      ),
    );
  }

  Widget buildSearchBtn() {
    return Offstage(
      offstage: false,
      child: Container(
          padding: EdgeInsets.only(left: 15, right: 10),
          color: Colors.white,
          height: 48,
          width: MediaQuery.of(context).size.width,
          child: Row(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      buildSearchItem('入库', 'INBOUND', cb: () {
                        action = 'INBOUND';
                        handleRefresh();
                        setState(() {});
                      }),
                      buildSearchItem('出库', 'SIGN', cb: () {
                        action = 'SIGN';
                        handleRefresh();
                        setState(() {});
                      }),
                      buildSearchItem('退回', 'BACK', cb: () {
                        action = 'BACK';
                        handleRefresh();
                        setState(() {});
                      }),
                      buildSearchItem('短信', 'SMS', cb: () {
                        action = 'SMS';
                        handleRefresh();
                        setState(() {});
                      }),
                    ],
                  ),
                ),
              ),
            ],
          )),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      key: _cabinetRetentionListKey,
      backgroundColor: DefaultConfig().configs.BG_COLOR,
      appBar: new AppCustomerBar(
          title: AppbarTitle(
            title: '异常管理',
            isCenter: true,
          ),
          actions: [Container(width: 60)]),
      body: Container(
        child: Stack(
          children: [
            Column(
              children: <Widget>[
                buildSearchBtn(),
                new Expanded(
                  child: Container(
                    child: Stack(
                      children: [
                        Positioned(
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            child: AppPullLoadWidget(
                              pullLoadWidgetControl,
                              (BuildContext context, int index) => _renderEventItem(index),
                              handleRefresh,
                              onLoadMore,
                              // showNoMore: true,
                              refreshKey: refreshIndicatorKey,
                            ))
                      ],
                    ),
                  ),
                ),
              ],
            ),
            Positioned(
              bottom: 0,
              child: Offstage(
                offstage: _checkList.length == 0,
                child: Container(
                    padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                      color: Colors.white,
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 20.0,
                          height: 20.0,
                          margin: EdgeInsets.fromLTRB(10, 0.0, 10.0, 0.0),
                          child: Checkbox(
                            value: _checkList.length == dataList.length,
                            activeColor: Theme.of(context).primaryColor,
                            onChanged: (bool? value) {
                              setState(() {
                                _checkList.clear();
                                checkList.clear();
                                if (value!) {
                                  dataList.forEach((item) {
                                    _checkList.add(item['id']);
                                    checkList.add(item);
                                  });
                                }
                              });
                            },
                          ),
                        ),
                        Text('选中${_checkList.length}项'),
                        Expanded(
                          child: Wrap(
                            children: [
                              Container(
                                margin: EdgeInsets.only(left: 10),
                                child: Offstage(
                                  offstage: action != 'INBOUND',
                                  child: OutlinedButton(
                                    onPressed: () {
                                      batchOperation('inbound');
                                    },
                                    child: Text(
                                      '重投入库',
                                      style: TextStyle(fontSize: 12),
                                    ),
                                    style: ButtonStyle(
                                        minimumSize: MaterialStateProperty.all(Size(54, 36)),
                                        foregroundColor: MaterialStateProperty.resolveWith((states) {
                                          return Colors.white;
                                        }),
                                        backgroundColor: MaterialStateProperty.resolveWith((states) {
                                          return Theme.of(context).primaryColor;
                                        })),
                                  ),
                                ),
                              ),
                              Container(
                                margin: EdgeInsets.only(left: 10),
                                child: Offstage(
                                  offstage: action != 'INBOUND',
                                  child: OutlinedButton(
                                    onPressed: () {
                                      batchOperation('RESEND');
                                    },
                                    child: Text(
                                      '修改默认通道并重投',
                                      style: TextStyle(fontSize: 12),
                                    ),
                                    style: ButtonStyle(
                                        minimumSize: MaterialStateProperty.all(Size(54, 36)),
                                        foregroundColor: MaterialStateProperty.resolveWith((states) {
                                          return Colors.white;
                                        }),
                                        backgroundColor: MaterialStateProperty.resolveWith((states) {
                                          return Theme.of(context).primaryColor;
                                        })),
                                  ),
                                ),
                              ),
                              Container(
                                margin: EdgeInsets.only(left: 10),
                                child: Offstage(
                                  offstage: action != 'SIGN',
                                  child: OutlinedButton(
                                    onPressed: () {
                                      batchOperation('SIGN');
                                    },
                                    child: Text(
                                      '重投出库',
                                      style: TextStyle(fontSize: 12),
                                    ),
                                    style: ButtonStyle(
                                        minimumSize: MaterialStateProperty.all(Size(54, 36)),
                                        foregroundColor: MaterialStateProperty.resolveWith((states) {
                                          return Colors.white;
                                        }),
                                        backgroundColor: MaterialStateProperty.resolveWith((states) {
                                          return Theme.of(context).primaryColor;
                                        })),
                                  ),
                                ),
                              ),
                              Container(
                                margin: EdgeInsets.only(left: 10),
                                child: Offstage(
                                  offstage: action != 'BACK',
                                  child: OutlinedButton(
                                    onPressed: () {
                                      batchOperation('BACK');
                                    },
                                    child: Text(
                                      '重投退回',
                                      style: TextStyle(fontSize: 12),
                                    ),
                                    style: ButtonStyle(
                                        minimumSize: MaterialStateProperty.all(Size(54, 36)),
                                        foregroundColor: MaterialStateProperty.resolveWith((states) {
                                          return Colors.white;
                                        }),
                                        backgroundColor: MaterialStateProperty.resolveWith((states) {
                                          return Theme.of(context).primaryColor;
                                        })),
                                  ),
                                ),
                              ),
                              Container(
                                margin: EdgeInsets.only(left: 10),
                                child: Offstage(
                                  offstage: action != 'SMS',
                                  child: OutlinedButton(
                                    onPressed: () {
                                      batchOperation('SMS');
                                    },
                                    child: Text(
                                      '重发通知',
                                      style: TextStyle(fontSize: 12),
                                    ),
                                    style: ButtonStyle(
                                        minimumSize: MaterialStateProperty.all(Size(54, 36)),
                                        foregroundColor: MaterialStateProperty.resolveWith((states) {
                                          return Colors.white;
                                        }),
                                        backgroundColor: MaterialStateProperty.resolveWith((states) {
                                          return Theme.of(context).primaryColor;
                                        })),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    )),
              ),
            )
          ],
        ),
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }

  _getData({isRefresh = false}) async {
    Map<String, dynamic> info = {
      'action': action,
      'current': page.toString(),
      'size': 10,
    };
    setState(() {});
    DataResult result = await CourierDao.orderExceptionLog(info);
    return result;
  }

  // TODO: implement wantKeepAlive
  @override
  bool get wantKeepAlive => true;

  // TODO: implement isRefreshFirst
  @override
  bool get isRefreshFirst => false;

  @override
  requestLoadMore() async {
    return await _getData();
  }

  @override
  requestRefresh() async {
    return await _getData(isRefresh: true);
  }

  @protected
  Future<Null> handleRefresh({PackageType? type}) async {
    setState(() {
      _checkList = [];
      checkList = [];
    });
    if (isLoading) {
      return null;
    }
    refreshIndicatorKey.currentState?.show();
    isLoading = true;
    page = 1;
    var res = await requestRefresh();
    resolveRefreshResult(res);
    resolveDataResult(res);
    if (res.next != null) {
      var resNext = await res.next;
      resolveRefreshResult(resNext);
      resolveDataResult(resNext);
    }
    isLoading = false;
    return null;
  }
}
