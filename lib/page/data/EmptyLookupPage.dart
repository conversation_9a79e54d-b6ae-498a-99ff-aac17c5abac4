import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/CabinetBoxListWidget.dart';
import 'package:cabinet_flutter_app/widget/CollectIconButton.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CustomShareWidget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';

class EmptyLookupPage extends StatefulWidget {
  @override
  _EmptyLookupPageState createState() => _EmptyLookupPageState();
}

class _EmptyLookupPageState extends State<EmptyLookupPage> {
  double marginValue = 10;
  List<dynamic> emptyList = [];

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  /// 格口信息
  buildGKInfo() {
    emptyList = [
      {'type': 5, 'availableNum': 0, 'price': 1000},
      {'type': 4, 'availableNum': 1, 'price': 2000},
      {'type': 3, 'availableNum': 2, 'price': 3000},
      {'type': 2, 'availableNum': 3, 'price': 4000},
      {'type': 1, 'availableNum': 4, 'price': 5000},
    ];
    return Container(
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(
            Radius.circular(7),
          ),
          border: Border.all(width: 1, color: Colors.white)),
      margin: EdgeInsets.only(left: marginValue, right: marginValue),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(left: 10, top: 15, right: 10, bottom: 20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(5)),
              color: Color(0xFFF5F5F5),
            ),
            // child: Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   children: buildAvailableGK(),
            // ),
            child: CabinetBoxListWidget(emptyList),
          )
        ],
      ),
    );
  }

  /// 收藏点位
  setCabinetCollect() async {
    var res = await CourierDao.setCabinetCollected('G30187399', !true);
    if (res != null && res.result) {
      bool isCollect = res.data['hasCollect'] == 1;
      // cabinetEntity.hasCollected = isCollect;
      // Fluttertoast.showToast(msg: isCollect ? '收藏成功' : '取消收藏成功', gravity: ToastGravity.CENTER);
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return new StoreBuilder<AppState>(builder: (context, store) {
      UserEntity? user = store.state.userInfo;
      return Scaffold(
        backgroundColor: Colors.grey.shade200,
        appBar: new AppCustomerBar(
          title: Text(
            '查看地址',
            style: new TextStyle(fontSize: 18.0),
          ),
          actions: [
            IconButton(
                icon: Icon(Icons.share),
                onPressed: () {
                  CustomShareWidget.showShareView(context);
                }),
          ],
        ),
        body: Column(
          children: [
            Container(
              height: 200,
              alignment: Alignment.center,
              color: Colors.green.shade200,
              child: Text('地图'),
            ),
            Expanded(
                child: Container(
              padding: EdgeInsets.fromLTRB(10, 15, 10, 10),
              alignment: Alignment.bottomLeft,
              decoration: BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(20)), color: Colors.white),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: EdgeInsets.fromLTRB(10, 14, 10, 4),
                    child: Row(
                      children: [
                        Text(
                          '识别码：XM456123',
                          style: TextStyle(fontSize: AppConstant.smallTextSize, color: Colors.black),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.fromLTRB(10, 14, 10, 4),
                    child: Row(
                      children: [
                        Text(
                          '名称：幽谷三号楼1号柜',
                          style: TextStyle(fontSize: AppConstant.smallTextSize, color: Colors.black),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.fromLTRB(10, 14, 10, 4),
                    child: Row(
                      children: [
                        Text(
                          '地址：南京市江宁区幽谷3号楼1202',
                          style: TextStyle(fontSize: AppConstant.smallTextSize, color: Colors.black),
                        ),
                      ],
                    ),
                  ),
                  buildGKInfo(),
                  Container(
                    margin: EdgeInsets.fromLTRB(10, 0, 10, 14),
                    child: Row(
                      children: [
                        CollectIconButtonWidget(
                            value: true,
                            onPress: () {
                              setCabinetCollect();
                            }),
                      ],
                    ),
                  )
                ],
              ),
            ))
          ],
        ),
      );
    });
  }

  // TODO: implement wantKeepAlive
  @override
  bool get wantKeepAlive => true;
}
