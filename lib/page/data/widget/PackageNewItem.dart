import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/WaybillDao.dart';
import 'package:cabinet_flutter_app/common/entitys/package_view_entity.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/ThrottleUtil.dart';
import 'package:cabinet_flutter_app/common/utils/text_util.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CommonRowWidget.dart';
import 'package:flutter/material.dart';

import 'package:intl/intl.dart';

/// 带图标的输入框
class PackageNewItem extends StatefulWidget {
  final ListType type;
  final PackageViewEntity package;
  final int indexNo;
  final VoidCallback? bindTap;
  final Widget? bottom;
  final int overDueDays;
  final bool? isCheck;
  final VoidCallback? checkItem;

  PackageNewItem(this.type,
      {required this.package,
      required this.indexNo,
      this.bindTap,
      this.bottom,
      this.overDueDays = 7,
      this.isCheck,
      this.checkItem});

  @override
  _PackageNewItemState createState() => new _PackageNewItemState();
}

/// State for [InorderWidget] widgets.
class _PackageNewItemState extends State<PackageNewItem> {
  ThrottleUtil throttleUtil = ThrottleUtil();

  @override
  Widget build(BuildContext context) {
    String day = '';
    bool isOverDue = false;
    PackageViewEntity? package = widget.package;
    day = CheckUtils.countDay(package.inboundTime);
    if (package.hasOutbound == 1 && package.outboundTime != '') {
      var hours = DateTime.parse(package.outboundTime).difference(DateTime.parse(package.inboundTime)).inHours;
      day = (hours / 24).toStringAsFixed(1);
    }
    bool showDayCount = double.parse(day) > 0;
      if (package.overdueEffectTime != null && package.overdueEffectTime!.isNotEmpty) {
      // 安全处理outboundTime的null和空值情况
      DateTime compareTime;
      if (package.outboundTime != null && package.outboundTime!.isNotEmpty) {
        compareTime = DateTime.parse(package.outboundTime!);
        // print('取出时间${compareTime}，超期时间${DateTime.parse(package.overdueEffectTime!)}，当前时间${DateTime.now()}');
      } else {
        compareTime = DateTime.now();
        // print('取出时间为空，使用当前时间${compareTime}，超期时间${DateTime.parse(package.overdueEffectTime!)}');
      }
      isOverDue = DateTime.parse(package.overdueEffectTime!).isBefore(compareTime);
    }

    Color bgColor = Colors.white;
    int digit = (package.checkCode!.length / 2).toInt();

    return Container(
      margin: EdgeInsets.fromLTRB(10, 0, 10, 10),
      decoration: new BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(
            Radius.circular(7),
          )),
      child: Material(
        color: bgColor,
        borderRadius: BorderRadius.all(
          Radius.circular(7),
        ),
        child: Ink(
          decoration: new BoxDecoration(
              borderRadius: BorderRadius.all(
            Radius.circular(7),
          )),
          child: InkWell(
            focusColor: bgColor,
            borderRadius: BorderRadius.all(
              Radius.circular(7),
            ),
            onTap: () {
              widget.bindTap!();
            },
            child: Container(
              padding: EdgeInsets.all(10),
              child: Column(
                children: [
                  CommonRowWidget(
                    leftWidget: Row(
                      children: [
                        Offstage(
                          offstage: widget.type == ListType.SEARCH ||
                              package.hasOutbound == 1 ||
                              CheckUtils.isCourier(context),
                          child: Container(
                            width: 20.0,
                            height: 20.0,
                            margin: EdgeInsets.fromLTRB(0, 0.0, 10.0, 0.0),
                            child: Checkbox(
                              value: widget.isCheck,
                              activeColor: Theme.of(context).primaryColor,
                              onChanged: (bool? value) {
                                widget.checkItem!();
                              },
                            ),
                          ),
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            RichText(
                              text: TextSpan(children: [
                                TextSpan(
                                    text: package.hasOutbound == 0 ? '待取件' : '已取件',
                                    style: TextStyle(
                                        fontSize: AppConstant.normalTextSize,
                                        color:
                                            package.hasOutbound == 1 ? Colors.black38 : Theme.of(context).primaryColor,
                                        fontWeight: FontWeight.bold)),
                                widget.type == ListType.RETENTION || (widget.type == ListType.SEARCH && showDayCount)
                                    ? TextSpan(
                                        text: '·$day天',
                                        style: TextStyle(
                                            fontSize: AppConstant.normalTextSize,
                                            color: package.hasOutbound == 1
                                                ? Colors.black38
                                                : Theme.of(context).primaryColor,
                                            fontWeight: FontWeight.bold))
                                    : TextSpan(text: '')
                              ]),
                            ),
                            Text(
                                package.inboundTime != ''
                                    ? new DateFormat("MM/dd HH:mm:ss").format(DateTime.parse(package.inboundTime))
                                    : '入库时间未知',
                                style: TextStyle(color: Colors.grey))
                          ],
                        ),
                      ],
                    ),
                    rightWidget: Container(
                      padding: EdgeInsets.fromLTRB(8, 3, 8, 3),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(5)),
                          color: package.hasOutbound == 1 ? Color(0xFFF5F5F5) : Theme.of(context).primaryColorLight),
                      child: Text(
                        package.storeType == 1
                            ? TextUtil.formatDigitPattern(package.checkCode ?? '', digit: digit, pattern: '  ')
                            : TextUtil.formatDigitPatternEnd(package.checkCode ?? '', digit: digit, pattern: '  '),
                        style: TextStyle(
                            fontSize: 24,
                            color: package.hasOutbound == 1 ? Colors.black26 : Theme.of(context).primaryColor,
                            fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                  Container(
                    height: 1.5,
                    width: double.infinity,
                    margin: EdgeInsets.only(left: 0, top: 2, right: 0, bottom: 0),
                    color: DefaultConfig().configs.BG_COLOR,
                  ),
                  CommonRowWidget(
                    leftWidget: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          package.receiverMobile,
                          style: TextStyle(
                              color: Colors.black87,
                              fontSize: package.receiverMobile.length > 11 ? 18 : 22,
                              fontWeight: FontWeight.bold),
                        ),
                        Padding(padding: EdgeInsets.only(left: 8)),
                        Offstage(
                          offstage: true,
                          child: Container(
                            margin: EdgeInsets.only(left: 5),
                            padding: EdgeInsets.fromLTRB(7, 3, 7, 3),
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor,
                              borderRadius: BorderRadius.all(Radius.circular(4)),
                            ),
                            child: Text(
                              '多',
                              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 14),
                            ),
                          ),
                        )
                      ],
                    ),
                    rightWidget: Text(
                      package.storeType == 1 ? '${package.cabinetBoxLabel}号格口' : '${package.shelfName}',
                      style: TextStyle(
                        color: package.hasOutbound == 1 ? Colors.black38 : Theme.of(context).primaryColor,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  CommonRowWidget(
                    leftWidget: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                package.receiverName ?? '',
                                style: TextStyle(color: Colors.black87, fontSize: 16, fontWeight: FontWeight.bold),
                              ),
                              Offstage(
                                offstage: package.storeType == 2,
                                child: Container(
                                  width: 270,
                                  child: Text(package.cabinetLocationName,
                                  style: TextStyle(color: Colors.grey, fontSize: 16, fontWeight: FontWeight.w700),
                                ), 
                                )
                              ),
                              Offstage(
                                offstage: package.cabinetType != 3,
                                child: Text(
                                  package.cabinetName ?? '',
                                  style: TextStyle(
                                      color: Theme.of(context).primaryColor, fontSize: 16, fontWeight: FontWeight.bold),
                                ),
                              ),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Padding(
                                    padding: EdgeInsets.only(right: 8, top: 3),
                                    child: Text(
                                      package.waybillNo ?? '',
                                      style: TextStyle(color: Colors.grey, fontSize: 16, fontWeight: FontWeight.w700),
                                    ),
                                  ),
                                  Text(
                                    package.brandName ?? '',
                                    style: TextStyle(color: Colors.grey, fontSize: 16, fontWeight: FontWeight.w700),
                                  ),
                                ],
                              ),
                              Offstage(
                                  offstage: package.outboundTime == '',
                                  child: Text(
                                      package.outboundTime != ''
                                          ? '取出时间：${new DateFormat("MM/dd HH:mm:ss").format(DateTime.parse(package.outboundTime))}'
                                          : '',
                                      style: TextStyle(color: Colors.grey, fontSize: 16, fontWeight: FontWeight.bold))),
                            ]),
                        Padding(padding: EdgeInsets.only(left: 40)),
                        Offstage(
                          offstage: !isOverDue,
                          child: Image.asset(
                            LocalImageUtil.getImagePath('billed'),
                            width: 60.0,
                          ),
                        ),
                      ],
                    ),
                    rightWidget: Container(
                      child: Icon(
                        Icons.arrow_forward_ios_rounded,
                        size: 20,
                        color: Colors.grey.shade400,
                      ),
                    ),
                  ),
                  Padding(padding: EdgeInsets.only(top: 4)),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Offstage(
                        offstage: false,
                        child: Container(
                          padding: EdgeInsets.fromLTRB(12, 2, 12, 2),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.all(Radius.circular(4)),
                              color: package.messageSmsStatus == 4 ? Colors.red : Colors.white,
                              border: Border.all(
                                  width: 1, color: package.messageSmsStatus == 4 ? Colors.red : Colors.grey)),
                          child: Text('通知' + getStatus(package.messageSmsStatus),
                              style: TextStyle(color: package.messageSmsStatus == 4 ? Colors.white : Colors.black87)),
                        ),
                      ),
                      Offstage(
                        offstage: false,
                        child: Container(
                          margin: EdgeInsets.only(left: 8),
                          padding: EdgeInsets.fromLTRB(12, 2, 12, 2),
                          decoration: BoxDecoration(
                              color: package.inboundDeliveryStatus == 4 ? Colors.red : Colors.white,
                              borderRadius: BorderRadius.all(Radius.circular(4)),
                              border: Border.all(
                                  width: 1, color: package.inboundDeliveryStatus == 4 ? Colors.red : Colors.grey)),
                          child: Text('入库' + getStatus(package.inboundDeliveryStatus),
                              style:
                                  TextStyle(color: package.inboundDeliveryStatus == 4 ? Colors.white : Colors.black87)),
                        ),
                      ),
                      Offstage(
                        offstage: package.yzAccountId == '',
                        child: Container(
                          margin: EdgeInsets.only(left: 8),
                          padding: EdgeInsets.fromLTRB(12, 2, 12, 2),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.all(Radius.circular(4)),
                              border: Border.all(width: 1, color: Colors.grey)),
                          child: Text('聚', style: TextStyle(color: Colors.black87)),
                        ),
                      ),
                      Offstage(
                        offstage: package.hasSign == 0,
                        child: Container(
                          margin: EdgeInsets.only(left: 8),
                          padding: EdgeInsets.fromLTRB(12, 2, 12, 2),
                          decoration: BoxDecoration(
                              color: package.signDeliveryStatus == 4 ? Colors.red : Colors.white,
                              borderRadius: BorderRadius.all(Radius.circular(4)),
                              border: Border.all(
                                  width: 1, color: package.signDeliveryStatus == 4 ? Colors.red : Colors.grey)),
                          child: Text('出库' + getStatus(package.signDeliveryStatus),
                              style: TextStyle(color: package.signDeliveryStatus == 4 ? Colors.white : Colors.black87)),
                        ),
                      ),
                      Offstage(
                        offstage: package.hasReturn == 0,
                        child: Container(
                          margin: EdgeInsets.only(left: 8),
                          padding: EdgeInsets.fromLTRB(12, 2, 12, 2),
                          decoration: BoxDecoration(
                              color: package.returnDeliveryStatus == 4 ? Colors.red : Colors.white,
                              borderRadius: BorderRadius.all(Radius.circular(4)),
                              border: Border.all(
                                  width: 1, color: package.returnDeliveryStatus == 4 ? Colors.red : Colors.grey)),
                          child: Text('退回' + getStatus(package.returnDeliveryStatus),
                              style:
                                  TextStyle(color: package.returnDeliveryStatus == 4 ? Colors.white : Colors.black87)),
                        ),
                      ),
                    ],
                  ),
                  Offstage(
                    offstage: widget.bottom == null,
                    child: Container(
                      height: 1.5,
                      width: double.infinity,
                      margin: EdgeInsets.only(left: 0, top: 10, right: 0, bottom: 0),
                      color: DefaultConfig().configs.BG_COLOR,
                    ),
                  ),
                  widget.bottom ?? Container()
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  getStatus(int? status) {
    if (status != null) {
      return DefaultConfig().configs.messageStateMap[status]!;
    }
    return '无';
  }
}
