import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/dao/WaybillDao.dart';
import 'package:cabinet_flutter_app/common/entitys/order_log_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/package_view_entity.dart';
import 'package:cabinet_flutter_app/common/page_life_cycle.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/BrandUtil.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/utils/SoundUtils.dart';
import 'package:cabinet_flutter_app/common/utils/text_util.dart';
import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/page/login/AgreementPage.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CommonRowWidget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';

class PackageDetailPage extends StatefulWidget {
  final PackageViewEntity package;

  /// 是否显示物流同步 入柜界面跳转过来则显示 数据中心跳转不显示
  final bool showSync;

  /// 是否显示重新通知 入柜界面跳转过来则显示 数据中心-已取出跳转不显示
  final bool showReNotice;

  /// 是否显示快件退回 入柜界面跳转过来则显示 数据中心-已取出跳转不显示
  final bool showBack;

  /// 是否显示手机号编辑 入柜界面跳转过来则显示 数据中心-已取出跳转不显示
  final bool showEditPhone;

  PackageDetailPage(this.package, this.showSync, this.showReNotice, this.showBack, this.showEditPhone, {Key? key})
      : super(key: key);

  @override
  _PackageDetailPageState createState() => _PackageDetailPageState();
}

class _PackageDetailPageState extends State<PackageDetailPage> with PageLifeCycle<PackageDetailPage> {
  Map<int, String> waybillStatusMap = {0: '待取件', 1: '滞留件'};
  Map<int, String> waybillTypeMap = {1: '', 2: '拦截件'};
  Map<int, String> messageStateMap = DefaultConfig().configs.messageStateMap;
  Map<String, String> syncTypeMap = {
    'INBOUND': '入库',
    'BACK': '退回',
    'SIGN': '签收',
    'PJ': '快递员派件',
    'DP': '快递员到派',
    'QS': '快递员签收',
    'NONE': '仅入库',
    'VALIDATE': '入库检测',
    'RECEIVER_INFO': '收件人信息',
    'REGISTER': '快递柜开通',
    'UPDATE': '快递柜更新',
    'GUSS_BRAND': '快递柜列表查询',
    'QUERY': '快递品牌猜测',
    'WAYBILL_SMS': '运单代发短信',
    'ERROR': '错误'
  };

  Map<int, String> messageTypeMap = {1: '短信', 2: '微信'};
  Map<int, String> messageTypeSelectMap = {1: '短信', 2: '微信优先', 3: '短信+微信'};
  Map<int, String> inboundTypeMap = {1: '入柜', 2: '出柜', 3: '签收', 4: '退回', 5: '发送短信', 6: '发送微信'};
  Map<int, String> inboundDeliveryStatus = {0: '未同步', 1: '待同步', 2: '同步中', 3: '已同步', 4: '同步失败'};
  PackageViewEntity data = new PackageViewEntity();
  List<OrderLogEntity?>? syncLog = [];
  List<OrderLogEntity?>? orderLog = [];
  List<OrderLogEntity?>? noticeLog = [];
  Map<dynamic, dynamic> brandMap = {};
  bool isCourier = true;

  @override
  void initState() {
    super.initState();
    this.init();
  }

  init() async {
    getWaybillInfo();
    getBrandList();
  }

  getBrandList() async {
    var res = await BrandUtils.getBrandBindList();
    brandMap = {};
    if (res != null) {
      brandMap = res;
    }
    setState(() {});
  }

//获取用户通知记录
  getNoticeLog() async {
    LoadingUtil(
      status: '数据加载中...',
    ).show(context);
    var res = await CourierDao.getOrderMessageList(widget.package.id);
    LoadingUtil.dismiss(context);
    if (res.result) {
      noticeLog = jsonConvert.convertList<OrderLogEntity>(res.data);
      if (noticeLog != null && noticeLog!.length > 0) {
        checkDeliveryRecord();
      } else {
        Fluttertoast.showToast(msg: '暂无数据');
      }
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    isCourier = CheckUtils.isCourier(this.context);
    super.didChangeDependencies();
  }

  Future<Null> onFresh() async {
    getWaybillInfo();
  }

  getWaybillInfo() async {
    DataResult res = await CourierDao.courierPackageDetail(widget.package.cabinetLocationCode, widget.package.id);
    if (res.result) {
      data = PackageViewEntity.fromJson(res.data);
      setState(() {});
    }
  }

  /// ----------------单号信息----------------
  ///
  ///
  /// 单号信息
  buildWaybillInfo() {
    return Container(
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.fromLTRB(10, 5, 10, 5),
            child: CommonRowWidget(
              label: '单号详情',
            ),
          ),
          Container(
            padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
            color: Colors.white,
            child: Column(
              children: [
                CommonRowWidget(
                  leftWidget: InkWell(
                      onTap: () async {
                        Navigator.push(context, new MaterialPageRoute(builder: (context) {
                          return AgreementPage(
                              url: DefaultConfig().configs.KUAIDI100 + 'nu=' + data.waybillNo!,
                              isLocalUrl: false,
                              title: '物流轨迹');
                        }));
                      },
                      child: Text(
                        TextUtil.formatDigitPattern(data.waybillNo ?? '', digit: 5, pattern: '  '),
                        style: TextStyle(
                          fontSize: AppConstant.middleTextWhiteSize,
                          fontWeight: FontWeight.w600,
                          decoration: TextDecoration.underline,
                        ),
                      )),
                  rightWidget: Row(
                    children: [
                      InkWell(
                        onTap: () async {
                          changeWayBillNo();
                        },
                        child: Text('修改单号',
                            style: TextStyle(
                              color: DefaultConfig().configs.GREY_COLOR,
                              fontSize: 15,
                              decoration: TextDecoration.underline,
                            )),
                      ),
                      SizedBox(width: 10),
                      InkWell(
                        onTap: () async {
                          await CommonUtils.clipboradData(data.waybillNo!);
                        },
                        child: Text('复制', style: TextStyle(color: DefaultConfig().configs.GREY_COLOR, fontSize: 15)),
                      )
                    ],
                  ),
                ),
                CommonRowWidget(
                    rowPadding: EdgeInsets.only(top: 10),
                    label: '快递品牌',
                    labelFontSize: 15,
                    valueFontSize: 15,
                    value: '${data.brandName ?? ''}'),
                CommonRowWidget(
                    label: '包裹状态',
                    labelFontSize: 15,
                    valueFontSize: 15,
                    value: getPackageStatus(),
                    valueColor: Theme.of(context).primaryColor),
                // CommonRowWidget(
                //     label: '特殊件', value: '${waybillTypeMap[data.orderType]}', valueColor: Theme.of(context).primaryColor),
                Offstage(
                  // offstage: !widget.showSync,
                  offstage: false,
                  child: CommonRowWidget(
                      labelFontSize: 15,
                      valueFontSize: 15,
                      leftWidget: Row(
                        children: [
                          Text('物流对接', style: TextStyle(fontSize: 15, color: DefaultConfig().configs.GREY_COLOR)),
                          InkWell(
                            onTap: () {
                              if (data.inboundDeliveryStatus == 4) {
                                syncTrack();
                              }
                            },
                            child: Row(
                              children: [
                                Padding(padding: EdgeInsets.only(left: 5)),
                                LocalImageUtil.getImageAsset(
                                    data.inboundDeliveryStatus == 4 ? 'refresh' : 'refreshGrey',
                                    width: 14),
                                Padding(padding: EdgeInsets.only(left: 5)),
                                Text('重传',
                                    style: TextStyle(
                                        fontSize: AppConstant.smallTextSize,
                                        color: data.inboundDeliveryStatus == 4
                                            ? DefaultConfig().configs.BLUE_COLOR
                                            : DefaultConfig().configs.INFO_COLOR))
                              ],
                            ),
                          )
                        ],
                      ),
                      value: inboundDeliveryStatus[data.inboundDeliveryStatus] ?? ''),
                ),
                Offstage(
                    offstage: data.hasDp == 0,
                    child: CommonRowWidget(
                      label: '是否补到派',
                      labelFontSize: 15,
                      valueFontSize: 15,
                      value: '是',
                    )),
                Offstage(
                  offstage: data.hasDp == 0,
                  child: CommonRowWidget(
                    label: '补到派状态',
                    labelFontSize: 15,
                    valueFontSize: 15,
                    rightWidget: Row(
                      children: [
                        Text(getBDPStatus(), style: TextStyle(color: Theme.of(context).primaryColor)),
                        Offstage(
                          offstage: data.dpStatus != 4,
                          child: InkWell(
                            onTap: () async {
                              await CommonUtils.confirm(context, '${data.dpMsg}', cancelText: '');
                            },
                            child: Container(
                              padding: EdgeInsets.only(left: 5),
                              child: Text('查看', style: TextStyle(color: Theme.of(context).primaryColor)),
                            ),
                          ),
                        ),
                        Offstage(
                          offstage: data.dpStatus != 4,
                          child: InkWell(
                            onTap: () async {
                              resendBDP();
                            },
                            child: Container(
                              padding: EdgeInsets.only(left: 5),
                              child: Text('重投', style: TextStyle(color: Theme.of(context).primaryColor)),
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                ),
                CommonRowWidget(
                  labelFontSize: 15,
                  valueFontSize: 15,
                  label: '',
                  rightWidget: Row(
                    children: [
                      OutlinedButton(
                        onPressed: () {
                          /// 同步记录
                          getSyncLog();
                        },
                        child: Text(
                          '同步记录',
                          style: TextStyle(color: Colors.black, fontSize: 14),
                        ),
                        style: ButtonStyle(
                            minimumSize: MaterialStateProperty.all(Size(80, 32)),
                            shape: MaterialStateProperty.all(StadiumBorder()),
                            side: MaterialStateProperty.all(BorderSide(color: Color(0xFFB5B5B5)))),
                      ),
                      Offstage(
                        offstage: data.hasOutbound == 1,
                        child: Row(
                          children: [
                            Padding(padding: EdgeInsets.only(left: 10)),
                            OutlinedButton(
                              onPressed: () {
                                /// 修改品牌
                                changeCompanyCode();
                              },
                              child: Text(
                                '修改品牌',
                                style: TextStyle(
                                    fontSize: 14,
                                    color: phoneEditBtnStatus() ? Theme.of(context).primaryColor : Colors.grey),
                              ),
                              style: ButtonStyle(
                                minimumSize: MaterialStateProperty.all(Size(80, 32)),
                                shape: MaterialStateProperty.all(StadiumBorder()),
                                side: MaterialStateProperty.all(BorderSide(
                                    color: phoneEditBtnStatus() ? Theme.of(context).primaryColor : Colors.grey)),
                              ),
                            )
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  getPackageStatus() {
    if (data.hasOutbound == 0) {
      return data.keepEffect ? '滞留件' : '待取件';
    } else {
      return DefaultConfig().configs.OUTBOUND_TYPE[data.outboundType] ?? '';
    }
  }

  getBDPStatus() {
    return DefaultConfig().configs.BDP_TYPE[data.dpStatus] ?? '';
  }

  /// 同步物流信息
  syncTrack() async {
    LoadingUtil(
      status: '数据加载中...',
    ).show(context);
    var res = await CourierDao.courierGatewayResend(widget.package.id);
    LoadingUtil.dismiss(context);
    if (res.result) {
      if (res.data) {
        Fluttertoast.showToast(msg: '物流重传成功');
      } else {
        Fluttertoast.showToast(msg: '物流重传失败');
      }
    }
  }

  resendBDP() async {
    LoadingUtil(
      status: '数据加载中...',
    ).show(context);
    var res = await CourierDao.resendBdp(widget.package.id);
    LoadingUtil.dismiss(context);
    if (res.result) {
      if (res.data) {
        Fluttertoast.showToast(msg: '补到派重传成功');
      } else {
        Fluttertoast.showToast(msg: '补到派重传失败');
      }
    }
  }

  /// 快件退回
  backPackage() {
    CommonUtils.customConfirm(context, '是否退回？', title: '快件退回', showClose: false, actions: <Widget>[
      Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
            },
            child: Text('取消', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
            style: ButtonStyle(
                minimumSize: MaterialStateProperty.all(Size(90, 40)),
                foregroundColor: MaterialStateProperty.resolveWith((states) {
                  return Colors.black;
                }),
                backgroundColor: MaterialStateProperty.resolveWith((states) {
                  return DefaultConfig().configs.BG_COLOR;
                }))),
        TextButton(
            onPressed: () async {
              Navigator.of(context).pop();

              /// todo
            },
            child: Text('是的', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
            style: ButtonStyle(
                minimumSize: MaterialStateProperty.all(Size(90, 40)),
                foregroundColor: MaterialStateProperty.resolveWith((states) {
                  return Colors.white;
                }),
                backgroundColor: MaterialStateProperty.resolveWith((states) {
                  return Colors.blue;
                }))),
      ])
    ]);
  }

  ///
  ///
  /// ----------------单号信息----------------

  /// ----------------取件信息----------------
  ///
  ///
  /// 取件信息
  buildPickUpInfo() {
    return Container(
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.fromLTRB(10, 5, 10, 5),
            child: CommonRowWidget(
              label: '取件信息',
            ),
          ),
          Container(
            padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
            color: Colors.white,
            child: Column(
              children: [
                CommonRowWidget(
                  labelFontSize: 15,
                  valueFontSize: 15,
                  leftWidget: Row(
                    children: [
                      Text('客户手机号', style: TextStyle(fontSize: 15, color: DefaultConfig().configs.GREY_COLOR)),
                      Offstage(
                        offstage: false,
                        child: InkWell(
                          onTap: () {
                            if (phoneEditBtnStatus()) {
                              editMobilePhone();
                            } else {
                              Fluttertoast.showToast(msg: '包裹已取出');
                            }
                          },
                          child: Row(
                            children: [
                              Padding(padding: EdgeInsets.only(left: 5)),
                              LocalImageUtil.getImageAsset(phoneEditBtnStatus() ? 'edit' : 'editGrey', width: 14),
                              Padding(padding: EdgeInsets.only(left: 5)),
                              Text('修改',
                                  style: TextStyle(
                                      fontSize: AppConstant.smallTextSize,
                                      color: phoneEditBtnStatus() ? DefaultConfig().configs.BLUE_COLOR : Colors.grey))
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                  rightWidget: InkWell(
                      onTap: () async {
                        await CommonUtils.makePhoneCall(context, data.receiverMobile);
                      },
                      child: Row(
                        children: [
                          Icon(Icons.phone_in_talk, size: 16, color: DefaultConfig().configs.BLUE_COLOR),
                          Text(data.receiverMobile,
                              style: TextStyle(
                                  fontSize: AppConstant.smallTextSize, color: DefaultConfig().configs.BLUE_COLOR)),                    
                        ],
                      )),
                    

                ),
                Offstage(
                  offstage:data.virtualPhone == null || data.virtualPhone == '',
                  child:  CommonRowWidget(
                  labelFontSize: 15,
                  valueFontSize: 15,
                  leftWidget: Row(
                    children: [
                      Text('客户虚拟号', style: TextStyle(fontSize: 15, color: DefaultConfig().configs.GREY_COLOR)),
                    ],
                  ),
                  rightWidget: InkWell(
                      onTap: () async {
                        await CommonUtils.makePhoneCall(context, (data.virtualPhone != null ? data.virtualPhone!.split('转')[0] : ''));
                      },
                      child: Row(
                        children: [
                          Icon(Icons.phone_in_talk, size: 16, color: DefaultConfig().configs.BLUE_COLOR),
                          Text(data.virtualPhone ?? '',
                              style: TextStyle(
                                  fontSize: AppConstant.smallTextSize, color: DefaultConfig().configs.BLUE_COLOR)),                     
                        ],
                      )),
                 ),
                ),
                CommonRowWidget(labelFontSize: 15, valueFontSize: 15, label: '取件码', value: '${data.checkCode ?? ''}'),
                CommonRowWidget(
                    labelFontSize: 15,
                    valueFontSize: 15,
                    label: '通知方式',
                    value: geMessageType(),
                    valueColor: Theme.of(context).primaryColor),
                CommonRowWidget(
                    labelFontSize: 15,
                    valueFontSize: 15,
                    label: '短信通知',
                    value: getNoticeStatus('sms'),
                    valueColor: Theme.of(context).primaryColor),
                Offstage(
                  offstage: data.messageType == 1,
                  child: CommonRowWidget(
                      labelFontSize: 15,
                      valueFontSize: 15,
                      label: '微信通知',
                      value: getNoticeStatus('wx'),
                      valueColor: Theme.of(context).primaryColor),
                ),
                CommonRowWidget(
                    labelFontSize: 15,
                    valueFontSize: 15,
                    label: '',
                    rightWidget: Row(
                      children: [
                        OutlinedButton(
                          onPressed: () {
                            /// 查看通知记录
                            getNoticeLog();
                          },
                          child: Text(
                            '查看记录',
                            style: TextStyle(color: Colors.black, fontSize: 14),
                          ),
                          style: ButtonStyle(
                              shape: MaterialStateProperty.all(StadiumBorder()),
                              minimumSize: MaterialStateProperty.all(Size(80, 32)),
                              side: MaterialStateProperty.all(BorderSide(color: Color(0xFFB5B5B5)))),
                        ),
                        Offstage(
                          offstage: false,
                          child: Row(
                            children: [
                              Padding(padding: EdgeInsets.only(left: 10)),
                              OutlinedButton(
                                onPressed: () {
                                  /// 重新通知
                                  if (phoneEditBtnStatus()) {
                                    reNotice(data.receiverMobile);
                                  } else {
                                    Fluttertoast.showToast(msg: '包裹已取出');
                                  }
                                },
                                child: Text(
                                  '重新通知',
                                  style: TextStyle(
                                      fontSize: 14,
                                      color: phoneEditBtnStatus() ? Theme.of(context).primaryColor : Colors.grey),
                                ),
                                style: ButtonStyle(
                                  minimumSize: MaterialStateProperty.all(Size(80, 32)),
                                  shape: MaterialStateProperty.all(StadiumBorder()),
                                  side: MaterialStateProperty.all(BorderSide(
                                      color: phoneEditBtnStatus() ? Theme.of(context).primaryColor : Colors.grey)),
                                ),
                              )
                            ],
                          ),
                        )
                      ],
                    ))
              ],
            ),
          )
        ],
      ),
    );
  }

  /// 获取通知状态
  getNoticeStatus(String type) {
    if (type == 'sms') {
      return data.messageSmsStatus == null ? '无' : messageStateMap[data.messageSmsStatus] ?? '无';
    } else {
      return data.messageWxStatus == null ? '无' : messageStateMap[data.messageWxStatus] ?? '无';
    }
  }

  geMessageType() {
    return messageTypeSelectMap[data.messageType];
  }

  /// 手机号修改按钮 重新通知按钮状态
  /// 若已取出 按钮为false 禁用状态
  phoneEditBtnStatus() {
    return data.hasOutbound == 0;
  }

  editMobilePhone() {
    CommonUtils.inputMobileDialog(context, true, (mobile, isSendMessage) {
      if (CheckUtils.isNotNull(mobile)) {
        getEditMobilePhone(mobile, isSendMessage: isSendMessage);
      }
    });
  }

  changeCompanyCode() {
    CommonUtils.changeCompanyDialog(context, brandMap, true, (brandCode, isSync) {
      if (CheckUtils.isNotNull(brandCode)) {
        if (brandCode != data.brandCode) {
          changeBrand(brandCode, isSync);
        }
      }
    }, initBrandCode: data.brandCode!);
  }

  changeWayBillNo() {
    CommonUtils.changeWayBillNoDialog(context, (waybillNo, isSync) {
      if (CheckUtils.isNotNull(waybillNo)) {
        if (waybillNo != data.waybillNo) {
          changeBillNo(waybillNo, isSync);
        }
      }
    }, initWayBillNo: data.waybillNo!);
  }

  changeBillNo(waybillNo, bool isSync) async {
    var res = await CourierDao.changeWayBillNo({'orderId': widget.package.id, 'waybillNo': waybillNo});
    if (res.result) {
      Fluttertoast.showToast(msg: '快递单号修改成功');
      if (isSync) {
        await syncTrack();
      }
      getWaybillInfo();
    }
  }

  changeBrand(String brand, bool isSync) async {
    var res = await CourierDao.waybillChangeCompany({'orderId': widget.package.id, 'brandCode': brand});
    if (res.result) {
      Fluttertoast.showToast(msg: '快递品牌修改成功');
      if (isSync) {
        await syncTrack();
      }
      getWaybillInfo();
    }
  }

  getEditMobilePhone(mobile, {bool isSendMessage = false}) async {
    var res = await CourierDao.courierEditOrderMobile(mobile, widget.package.id);
    if (res.result) {
      SoundUtils.audioPushFn(SoundUtils.SUCCESS_OK);
      Fluttertoast.showToast(msg: '收件人手机号修改成功');
      if (isSendMessage) {
        await reNotice(mobile, isForce: true);
      }
      getWaybillInfo();
    }
  }

  /// 重新通知
  /// @param [receiverMobile]  手机号
  /// @param [isForce]  是否显示弹窗确认 从修改手机号勾选的发送则直接发送 否则弹窗提示
  reNotice(String receiverMobile, {bool isForce = false}) {
    WaybillDao.reNotice(context, receiverMobile, widget.package.id, isForce: isForce);
  }

  getSyncLog() async {
    LoadingUtil(
      status: '数据加载中...',
    ).show(context);
    var res = await CourierDao.getSyncLogList(widget.package.id);
    LoadingUtil.dismiss(context);
    if (res.result) {
      syncLog = jsonConvert.convertList<OrderLogEntity>(res.data);
      if (syncLog != null && syncLog!.length > 0) {
        checkInboundOutboundSync();
      } else {
        Fluttertoast.showToast(msg: '暂无数据');
      }
    }
  }

  /// 查看投递记录
  getOrderLog() async {
    LoadingUtil(
      status: '数据加载中...',
    ).show(context);
    var res = await CourierDao.getOrderLogList(widget.package.id);
    LoadingUtil.dismiss(context);
    if (res.result) {
      orderLog = jsonConvert.convertList<OrderLogEntity>(res.data);
      if (orderLog != null && orderLog!.length > 0) {
        checkInboundOutboundRecord();
      } else {
        Fluttertoast.showToast(msg: '暂无数据');
      }
    }
  }

  checkInboundOutboundSync() async {
    List<Widget> contentList = [];
    if (syncLog != null && syncLog!.length > 0) {
      contentList = syncLog!.map((e) {
        return Container(
          child: Column(
            children: [
              CommonRowWidget(
                  leftWidget: Row(
                    children: [
                      Icon(
                        Icons.assignment_rounded,
                        size: 13,
                        color: Theme.of(context).primaryColor,
                      ),
                      Padding(padding: EdgeInsets.only(left: 5)),
                      Text(
                        '${e?.beginTime ?? ''}',
                        style:
                            TextStyle(fontSize: 14, color: Theme.of(context).primaryColor, fontWeight: FontWeight.w500),
                      )
                    ],
                  ),
                  value: ''),
              CommonRowWidget(
                leftWidget: Container(
                  constraints: BoxConstraints().tighten(),
                  alignment: Alignment.centerLeft,
                  margin: EdgeInsets.only(left: 5),
                  padding: EdgeInsets.only(left: 10),
                  decoration: BoxDecoration(
                      border: Border(
                    left: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                  )),
                  child: Text('${syncTypeMap[e?.action] ?? ''}',
                      style: TextStyle(fontSize: 14, color: Color(0xFF585858), fontWeight: FontWeight.w500)),
                ),
                value: '',
                rightWidget: Container(
                  constraints: BoxConstraints().tighten(),
                  alignment: Alignment.centerLeft,
                  margin: EdgeInsets.only(left: 5),
                  child: Text('${messageStateMap[e?.status] ?? ''}',
                      style: TextStyle(fontSize: 14, color: Color(0xFF585858), fontWeight: FontWeight.w500)),
                ),
              ),
              Offstage(
                offstage: e?.exception == null || e?.exception == 'null',
                child: Container(
                  constraints: BoxConstraints().tighten(),
                  alignment: Alignment.centerLeft,
                  margin: EdgeInsets.only(left: 5),
                  padding: EdgeInsets.only(left: 10),
                  decoration: BoxDecoration(
                      border: Border(
                    left: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                  )),
                  child: Text('${e?.exception ?? ''}',
                      style: TextStyle(fontSize: 14, color: Colors.red, fontWeight: FontWeight.w500)),
                ),
              )
            ],
          ),
        );
      }).toList();
      CommonUtils.showList(context, '', title: '同步记录', contentList: contentList);
    }
  }

  checkInboundOutboundRecord() async {
    List<Widget> contentList = [];
    if (orderLog != null && orderLog!.length > 0) {
      contentList = orderLog!.map((e) {
        return Container(
          child: Column(
            children: [
              CommonRowWidget(
                  leftWidget: Row(
                    children: [
                      Icon(
                        Icons.assignment_rounded,
                        size: 13,
                        color: Theme.of(context).primaryColor,
                      ),
                      Padding(padding: EdgeInsets.only(left: 5)),
                      Text(
                        '${e?.createTime ?? ''}',
                        style:
                            TextStyle(fontSize: 14, color: Theme.of(context).primaryColor, fontWeight: FontWeight.w500),
                      )
                    ],
                  ),
                  value: ''),
              Container(
                constraints: BoxConstraints().tighten(),
                alignment: Alignment.centerLeft,
                margin: EdgeInsets.only(left: 5),
                padding: EdgeInsets.only(left: 10),
                decoration: BoxDecoration(
                    border: Border(
                  left: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                )),
                child: Text('${e?.content ?? ''}',
                    style: TextStyle(fontSize: 14, color: Color(0xFF585858), fontWeight: FontWeight.w500)),
              )
            ],
          ),
        );
      }).toList();
      CommonUtils.showList(context, '', title: '投递记录', contentList: contentList);
    }
  }

  ///
  ///
  /// ----------------取件信息----------------

  /// ----------------时间记录----------------
  ///
  ///
  /// 时间记录
  buildTimeRecordInfo() {
    return Container(
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.fromLTRB(10, 5, 10, 5),
            child: CommonRowWidget(
              label: '时间记录',
            ),
          ),
          Container(
            padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
            color: Colors.white,
            child: Column(
              children: [
                CommonRowWidget(
                    labelFontSize: 15,
                    valueFontSize: 15,
                    label: '创建时间',
                    value: '${getTimeFormat(data.createTime) ?? ''}'),
                CommonRowWidget(
                    labelFontSize: 15,
                    valueFontSize: 15,
                    label: '入柜时间',
                    value: '${getTimeFormat(data.inboundTime) ?? ''}'),
                Offstage(
                  offstage: data.inboundImageUrl == null || data.inboundImageUrl == '' ? true : false,
                  child: CommonRowWidget(
                      label: '入库照片',
                      labelFontSize: 15,
                      valueFontSize: 15,
                      rightWidget: InkWell(
                        onTap: () async {
                          if (data.inboundImageUrl != null || data.inboundImageUrl != '') {
                            NavigatorUtils.goPhotoViewGalleryScreen(context, [data.inboundImageUrl], 0);
                          }
                        },
                        child: Text('查看图片',
                            style: TextStyle(
                              fontSize: 15,
                              decoration: TextDecoration.underline,
                            )),
                      )),
                ),
                CommonRowWidget(
                    labelFontSize: 15,
                    valueFontSize: 15,
                    label: '取出时间',
                    value: '${getTimeFormat(data.outboundTime) ?? '暂未取出'}'),
                Offstage(
                  offstage: data.outboundImageUrl == null || data.outboundImageUrl == '' ? true : false,
                  child: CommonRowWidget(
                      label: '出库照片',
                      labelFontSize: 15,
                      valueFontSize: 15,
                      rightWidget: InkWell(
                        onTap: () async {
                          if (data.outboundImageUrl != null || data.outboundImageUrl != '') {
                            NavigatorUtils.goPhotoViewGalleryScreen(context, [data.outboundImageUrl], 0);
                          }
                        },
                        child: Text('查看图片',
                            style: TextStyle(
                              fontSize: 15,
                              decoration: TextDecoration.underline,
                            )),
                      )),
                ),
                CommonRowWidget(labelFontSize: 15, valueFontSize: 15, label: '滞留费用', value: getKeepEffectFee()),
                CommonRowWidget(
                  labelFontSize: 15,
                  valueFontSize: 15,
                  label: '',
                  rightWidget: OutlinedButton(
                    onPressed: () {
                      /// 查看投递记录
                      getOrderLog();
                    },
                    child: Text(
                      '投递记录',
                      style: TextStyle(color: Colors.black, fontSize: 14),
                    ),
                    style: ButtonStyle(
                        minimumSize: MaterialStateProperty.all(Size(80, 32)),
                        shape: MaterialStateProperty.all(StadiumBorder()),
                        side: MaterialStateProperty.all(BorderSide(color: Color(0xFFB5B5B5)))),
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  getKeepEffectFee() {
    if (data.price != null) {
      double price = data.price! / 1000;
      return price > 0 ? price.toStringAsFixed(2) : '无';
    }
    return '无';
  }

  /// 时间格式化
  getTimeFormat(String time) {
    if (CheckUtils.isNotNull(time)) {
      return DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.parse(time));
    }
    return null;
  }

  /// 通知记录
  checkDeliveryRecord() async {
    List<Widget> contentList = [];
    if (noticeLog != null && noticeLog!.length > 0) {
      contentList = noticeLog!.map((e) {
        return Container(
          child: Column(
            children: [
              CommonRowWidget(
                  leftWidget: Row(
                    children: [
                      Icon(
                        Icons.assignment_rounded,
                        size: 13,
                        color: Theme.of(context).primaryColor,
                      ),
                      Padding(padding: EdgeInsets.only(left: 5)),
                      Text(
                        '${e?.createTime ?? ''}',
                        style:
                            TextStyle(fontSize: 14, color: Theme.of(context).primaryColor, fontWeight: FontWeight.w500),
                      )
                    ],
                  ),
                  value: ''),
              CommonRowWidget(
                leftWidget: Container(
                  constraints: BoxConstraints().tighten(),
                  alignment: Alignment.centerLeft,
                  margin: EdgeInsets.only(left: 5),
                  padding: EdgeInsets.only(left: 10),
                  decoration: BoxDecoration(
                      border: Border(
                    left: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                  )),
                  child: Text('${messageTypeMap[e?.type] ?? ''}',
                      style: TextStyle(fontSize: 14, color: Color(0xFF585858), fontWeight: FontWeight.w500)),
                ),
                value: '',
                rightWidget: Container(
                  constraints: BoxConstraints().tighten(),
                  alignment: Alignment.centerLeft,
                  margin: EdgeInsets.only(left: 5),
                  child: Text('${messageStateMap[e?.status] ?? ''}',
                      style: TextStyle(fontSize: 14, color: Color(0xFF585858), fontWeight: FontWeight.w500)),
                ),
              ),
              // Offstage(
              //   offstage: e?.type == 2,
              //   child: Container(
              //     constraints: BoxConstraints().tighten(),
              //     alignment: Alignment.centerLeft,
              //     margin: EdgeInsets.only(left: 5),
              //     padding: EdgeInsets.only(left: 10),
              //     decoration: BoxDecoration(
              //         border: Border(
              //           left: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
              //         )),
              //     child: Text('${e?.smsContent ?? ''}',
              //         style: TextStyle(fontSize: 14, color: Color(0xFF585858), fontWeight: FontWeight.w500)),
              //   ),
              // ),
              Offstage(
                offstage: e?.errorMessage == null || e?.errorMessage == 'null',
                child: Container(
                  constraints: BoxConstraints().tighten(),
                  alignment: Alignment.centerLeft,
                  margin: EdgeInsets.only(left: 5),
                  padding: EdgeInsets.only(left: 10),
                  decoration: BoxDecoration(
                      border: Border(
                    left: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                  )),
                  child: Text('${e?.errorMessage ?? ''}',
                      style: TextStyle(fontSize: 14, color: Colors.red, fontWeight: FontWeight.w500)),
                ),
              )
            ],
          ),
        );
      }).toList();
      CommonUtils.showList(context, '', title: '通知记录', contentList: contentList);
    }
  }

  ///
  ///
  /// ----------------时间记录----------------

  /// ----------------点位信息----------------
  ///
  ///
  /// 点位信息
  buildCabinetInfo() {
    return Offstage(
        offstage: data.storeType == 2,
        child: Container(
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.fromLTRB(10, 5, 10, 5),
                child: CommonRowWidget(
                  label: '柜机信息',
                ),
              ),
              Container(
                padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
                color: Colors.white,
                child: Column(
                  children: [
                    CommonRowWidget(
                        labelFontSize: 15, valueFontSize: 15, label: '柜机名称', value: '${data.cabinetLocationName}'),
                    CommonRowWidget(
                        labelFontSize: 15, valueFontSize: 15, label: '格口编号', value: '${data.cabinetBoxLabel}号'),
                    Offstage(
                      offstage: !isCourier,
                      child: CommonRowWidget(
                        labelFontSize: 15,
                        valueFontSize: 15,
                        label: '',
                        rightWidget: OutlinedButton(
                          onPressed: () {
                            /// 查看点位
                            toCabinetInfo();
                          },
                          child: Text(
                            '查看点位',
                            style: TextStyle(color: Colors.black),
                          ),
                          style: ButtonStyle(
                              minimumSize: MaterialStateProperty.all(Size(80, 32)),
                              shape: MaterialStateProperty.all(StadiumBorder()),
                              side: MaterialStateProperty.all(BorderSide(color: Color(0xFFB5B5B5)))),
                        ),
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
        ));
  }

  /// 查看点位信息
  toCabinetInfo() {
    NavigatorUtils.goCabinetLocationPage(context, data.cabinetLocationCode);
  }

  ///
  ///
  /// ----------------点位信息----------------
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DefaultConfig().configs.BG_COLOR,
      appBar: new AppCustomerBar(
        title: AppbarTitle(
          title: '单号详情',
          isCenter: true,
        ),
        actions: [
          Container(
            width: 60,
          )
        ],
      ),
      body: RefreshIndicator(
          onRefresh: onFresh,
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  physics: AlwaysScrollableScrollPhysics(),
                  child: Column(
                    children: [
                      /// 单号信息
                      buildWaybillInfo(),

                      /// 点位信息
                      buildCabinetInfo(),

                      /// 取件信息
                      buildPickUpInfo(),

                      /// 时间记录
                      buildTimeRecordInfo(),

                      Padding(
                        padding: EdgeInsets.only(bottom: 18),
                      )
                    ],
                  ),
                ),
              ),
            ],
          )), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }
}
