// ignore_for_file: invalid_use_of_protected_member

import 'dart:convert';

import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/entitys/data_pj_entity.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/page_life_cycle.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/utils/BrandUtil.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/page/data/PackageListKdyPage.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppListState.dart';
import 'package:cabinet_flutter_app/widget/AppTabWidget.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/DataCenterItemWidget.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/MyUnderlineTabIndicator.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/PackageStayListWidget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';

GlobalKey<_DataCenterPageState> dataCenterGlobalKey = GlobalKey();

class DataCenterPage extends StatefulWidget {
  DataCenterPage({Key? key}) : super(key: key);

  @override
  _DataCenterPageState createState() => _DataCenterPageState();
}

class _DataCenterPageState extends State<DataCenterPage>
    with
        TickerProviderStateMixin,
        AutomaticKeepAliveClientMixin,
        AppListState<DataCenterPage>,
        PageLifeCycle<DataCenterPage> {
  /// 室外柜
  List<Map<String, dynamic>> topTabs4 = [
    {'label': '派件', 'activeLabel': '我的派件', 'value': 'PJ'},
    {'label': '收件', 'activeLabel': '我的收件', 'value': 'SJ'}
  ];
  int topTabIndex = 0;
  String? storeType;
  late TabController middleTabController;
  List<Map<String, dynamic>> middleTabs = [
    {'label': '滞留件', 'value': PackageType.ZL},
    {'label': '待取件', 'value': PackageType.DQ},
    {'label': '已取出', 'value': PackageType.YQC},
    {'label': '退回件', 'value': PackageType.THJ},
    {'label': '问题件', 'value': PackageType.WTJ}
  ];
  int middleTabIndex = 1;

  late TabController middleTabControllerSJ;
  List<Map<String, dynamic>> middleTabsSJ = [
    {'label': '待收件', 'value': PackageType.DS},
    {'label': '已收件', 'value': PackageType.YS},
    {'label': '已完成', 'value': PackageType.YWC},
  ];
  int middleTabIndexSJ = 0;
  bool isFirstRender = true;
  GlobalKey<PackageListKdyPageState> packageListKey = GlobalKey();
  GlobalKey<PackageListKdyPageState> packageListKdyKey = GlobalKey();
  GlobalKey<PackageListSjWidgetState> packageListSjKey = GlobalKey();
  List listData = [];
  final GlobalKey<RefreshIndicatorState> refreshIndicatorKey = new GlobalKey<RefreshIndicatorState>();
  bool isCourier = true;

  @override
  void initState() {
    super.initState();
    middleTabController = TabController(length: middleTabs.length, vsync: this);
    middleTabControllerSJ = TabController(length: middleTabsSJ.length, vsync: this);
    initTab();
  }

  initTab() async {
    BrandUtils.saveHomePageRefresh();

    ///4 室外柜  6室内柜
    String preTop = '';
    String preMiddle = '';
    var _info = await LocalStorage.get(DefaultConfig().configs.INDEX_QUERY);
    if (_info != null) {
      var info = jsonDecode(_info);
      preTop = info['top'];
      preMiddle = info['middle'];
    }
    LocalStorage.remove(DefaultConfig().configs.INDEX_QUERY);
    if (preTop != '') {
      if (preTop == 'SJ') {
        setState(() {
          topTabIndex = 1;
        });
        if (preMiddle != '') {
          if (preMiddle == 'YS') {
            middleTabControllerSJ.index = 1;
            middleTabIndexSJ = 1;
          } else {
            middleTabControllerSJ.index = 0;
            middleTabIndexSJ = 0;
          }
        } else {
          middleTabControllerSJ.index = 1;
        }
      } else {
        Map<String, dynamic> storeTypeMap = {'PJ': null, 'GJ': '1', 'HJ': '2'};
        setState(() {
          storeType = storeTypeMap[preTop];
          topTabIndex = 0;
        });
        if (preMiddle != '') {
          if (preMiddle == 'DQ') {
            middleTabController.index = 1;
            middleTabIndex = 1;
          } else {
            middleTabController.index = 0;
            middleTabIndex = 0;
          }
        } else {
          middleTabController.index = 1;
          middleTabIndex = 1;
        }
      }
      initStoreType();
      handlePageRefresh();
    } else {
      /// todo
      if (isFirstRender) {
        middleTabController.index = 1;
        middleTabIndex = 1;
        handlePageRefresh();
      }
      isFirstRender = false;
    }
    middleTabController.addListener(() {
      if (middleTabController.indexIsChanging) {
        setState(() {
          middleTabIndex = middleTabController.index;
        });
        handlePageRefresh();
      }
    });
    middleTabControllerSJ.addListener(() {
      if (middleTabControllerSJ.indexIsChanging) {
        setState(() {
          middleTabIndexSJ = middleTabControllerSJ.index;
        });
        handlePageSjRefresh();
      }
    });
  }

  initStoreType() {
    if (!isCourier) {
      packageListKey.currentState?.initStoreType(storeType);
    }
  }

  handlePageRefresh() {
    if (!isCourier) {
      packageListKey.currentState?.clearData();
      packageListKey.currentState?.handleRefresh(type: middleTabs[middleTabIndex]['value']);
      storeType = null;
    } else {
      packageListKdyKey.currentState?.handleRefresh(type: middleTabs[middleTabIndex]['value']);
    }
  }

  handlePageSjRefresh() {
    if (isCourier) {
      packageListSjKey.currentState?.clearData();
      packageListSjKey.currentState?.handleRefresh(type: middleTabsSJ[middleTabIndexSJ]['value']);
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

// 路由显示触发
  @override
  void onShow() {}

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    isCourier = CheckUtils.isCourier(context);
  }

  // setPackageType(int index) {
  //   if (index > -1) {
  //     setState(() {
  //       middleTabIndex = index;
  //     });
  //   }
  // }

  buildTitle() {
    if (isCourier) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            width: 60,
          ),
          AppTabWidget(
            onChanged: (int index) {
              if (index > -1) {
                setState(() {
                  topTabIndex = index;
                });
                handlePageRefresh();
              }
            },
            tabs: topTabs4,
            index: topTabIndex,
          ),
          InkWell(
            onTap: () {
              NavigatorUtils.goPackageSearchPage(context);
            },
            child: Container(
              width: 60,
              padding: EdgeInsets.only(right: 2),
              alignment: Alignment.centerRight,
              child: LocalImageUtil.getImageAsset('search', width: 20),
            ),
          )
        ],
      );
    }
    return Container();
  }

  kdyRender(double _navHeight) {
    return Scaffold(
      backgroundColor: DefaultConfig().configs.BG_COLOR,
      body: Container(
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.fromLTRB(10, 5, 10, 5),
              margin: EdgeInsets.only(top: _navHeight, bottom: 0),
              height: 92,
              color: Colors.white,
              child: Column(
                children: [
                  /// 头部标题
                  buildTitle(),
                  Padding(
                    padding: EdgeInsets.only(top: 10),
                  ),

                  /// 包裹类型
                  buildMiddleTab(),
                ],
              ),
            ),
            Expanded(child: buildKdy()),
          ],
        ),
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }

  yzRender(double _navHeight) {
    return Scaffold(
      backgroundColor: DefaultConfig().configs.BG_COLOR,
      appBar: AppCustomerBar(
          showLeading: false,
          title: AppbarTitle(
            title: '派件',
            isCenter: true,
          ),
          actions: <Widget>[
            InkWell(
              onTap: () {
                NavigatorUtils.goPackageSearchPage(context);
              },
              child: Container(
                width: 60,
                padding: EdgeInsets.only(right: 2),
                alignment: Alignment.centerRight,
                child: LocalImageUtil.getImageAsset('search', width: 20),
              ),
            )
          ]),
      body: Container(
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.fromLTRB(10, 5, 10, 0),
              margin: EdgeInsets.only(top: _navHeight, bottom: 0),
              height: 48,
              color: Colors.white,
              child: Column(
                children: [
                  /// 包裹类型
                  buildMiddleTab(),
                ],
              ),
            ),
            Expanded(child: buildYz()),
          ],
        ),
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    double _navHeight = MediaQuery.of(context).padding.top;
    if (!isCourier) {
      _navHeight = 1;
    }
    return new StoreBuilder<AppState>(builder: (context, store) {
      return isCourier ? kdyRender(_navHeight) : yzRender(_navHeight);
    });
  }

  buildMiddleTabRender(TabController ctrl, List<Map<String, dynamic>> tabs) {
    return Container(
      color: Colors.white,
      width: double.infinity, // 确保撑满
      child: TabBar(
        isScrollable: true,
        labelStyle: TextStyle(fontSize: 15, fontWeight: FontWeight.bold),
        unselectedLabelStyle: TextStyle(fontSize: 15),
        indicator: MyUnderlineTabIndicator(borderSide: BorderSide(width: 2, color: Theme.of(context).primaryColor)),
        labelColor: Theme.of(context).primaryColor,
        unselectedLabelColor: Colors.black,
        //生成Tab菜单
        // onTap: setPackageType,
        controller: ctrl,
        tabs: tabs
            .map((e) => new Container(
                  height: 32.0,
                  child:  Tab(
                      child: Text(
                        e['label'],
                        style: TextStyle(fontSize: 15), // 你想要的大小
                        // overflow: TextOverflow.visible, 
                        maxLines: 1,
                      ),
                    ),
                ))
            .toList(),
      ),
    );
  }

  buildMiddleTab() {
    if (isCourier) {
      if (topTabIndex == 0) {
        return buildMiddleTabRender(middleTabController, middleTabs);
      } else {
        return buildMiddleTabRender(middleTabControllerSJ, middleTabsSJ);
      }
    }
    return buildMiddleTabRender(middleTabController, middleTabs);
  }

  _getData({isRefresh = false}) async {
    pullLoadWidgetControl.dataList.clear();
    return DataResult([], true, total: 0);
  }

  buildList(index) {
    if (topTabIndex == 0) {
      /// 派件
      return buildPJ(index);
    }
  }

  /// 快递员数据中心
  buildKdy() {
    if (topTabIndex == 0) {
      return PackageListKdyPage(isCourier,middleTabs[middleTabIndex]['value'], null, marginTop: -10, key: packageListKdyKey);
    } else {
      /// 收件
      return PackageStayListWidget(
        middleTabsSJ[middleTabIndexSJ]['value'],
        '',
        key: packageListSjKey,
        marginTop: -CommonUtils.sStaticBarHeight,
      );
    }
  }

  /// 驿站数据中心
  buildYz() {
    return PackageListKdyPage(
      isCourier,
      middleTabs[middleTabIndex]['value'],
      null,
      key: packageListKey,
      marginTop: 10,
    );
  }

  /// 派件列表
  buildPJ(index) {
    DataPjEntity item = DataPjEntity.fromJson(dataList[index]);
    return DataCenterItemWidget(
        package: item,
        bindTap: () {
          NavigatorUtils.goPackageListPage(context, middleTabs[middleTabIndex]['value'], item.cabinetLocationCode!);
        },
        bottom: null);
  }

  /// 收件列表
  buildSJ() {
    return (Container());
  }

  // TODO: implement wantKeepAlive
  @override
  bool get wantKeepAlive => true;

  // TODO: implement isRefreshFirst
  @override
  bool get isRefreshFirst => true;

  @override
  bool get needHeader => false;

  // 库存统计刷新
  Future<Null> onFresh() async {
    _getData(isRefresh: true);
  }

  @override
  requestLoadMore() async {
    return await _getData();
  }

  @override
  requestRefresh() async {
    return await _getData(isRefresh: true);
  }
}
