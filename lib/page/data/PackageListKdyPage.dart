/// 室外柜列表
import 'dart:async';

import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CabinetDao.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/dao/WaybillDao.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/package_search_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/package_view_entity.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/BrandUtil.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/utils/SoundUtils.dart';
import 'package:cabinet_flutter_app/common/utils/ThrottleUtil.dart';
import 'package:cabinet_flutter_app/common/utils/text_util.dart';
import 'package:cabinet_flutter_app/page/data/widget/PackageNewItem.dart';
import 'package:cabinet_flutter_app/widget/AppListState.dart';
import 'package:cabinet_flutter_app/widget/AppPullLoadWidget.dart';
import 'package:cabinet_flutter_app/widget/BottomBtnWidget.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CommonRowWidget.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CustomerDropDownSelectWidget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';

class PackageListKdyPage extends StatefulWidget {
  final bool isCourier;
  final PackageType type;
  final String? cabinetCode;
  final double marginTop;
  final int? keepEffectDay;
  final String? query;

  PackageListKdyPage(this.isCourier, this.type, this.cabinetCode,
      {Key? key, this.keepEffectDay, this.query, this.marginTop = 0})
      : super(key: key);

  @override
  PackageListKdyPageState createState() => PackageListKdyPageState();
}

class PackageListKdyPageState extends State<PackageListKdyPage>
    with AutomaticKeepAliveClientMixin<PackageListKdyPage>, AppListState<PackageListKdyPage> {
  Map<dynamic, dynamic> brandMap = {};
  Map<dynamic, dynamic> timeMap = {};
  PackageSearchEntity query = new PackageSearchEntity();
  CabinetEntity cabinet = new CabinetEntity();
  Map<dynamic, dynamic> listTypeState = DefaultConfig().configs.listTypeState;
  ThrottleUtil throttleUtil = ThrottleUtil();
  List<PackageViewEntity> checkList = [];
  List _checkList = [];

  // 筛选面板相关变量
  PackageSearchEntity _tempQuery = PackageSearchEntity(); // 临时筛选条件

  // 搜索相关变量
  TextEditingController _searchController = TextEditingController();
  FocusNode _searchFocusNode = FocusNode();

  @override
  initState() {
    this.initData();
    // 初始化搜索框
    if (query.keyword != null && query.keyword!.isNotEmpty) {
      _searchController.text = query.keyword!;
    }
    super.initState();
  }

  initData() async {
    timeMap = {
      'near3Days': '最近3天',
      'near5Days': '最近5天',
      'near7Days': '最近7天',
      'near14Days': '最近14天',
      'near30Days': '最近1个月',
      'near90Days': '最近3个月'
    };
    await getBrandList();
    setDefaultQuery();
    handleRefresh();
  }

  /// 设置查询条件默认值
  setDefaultQuery() {
    if (widget.cabinetCode != null) {
      query.cabinetLocationCode = widget.cabinetCode!;
    }
    query.size = 20;
    query.current = '1';
    query.beginYmd = DateFormat("yyyy-MM-dd").format(DateTime.now().subtract(Duration(days: 14)));

    if (widget.keepEffectDay != null) {
      query.keepEffectDay = widget.keepEffectDay!;
      query.beginYmd = '';
    }
    if (widget.query != null) {
      if (widget.query == 'noticeFail') {
        query.smsError = true;
        query.beginYmd = '';
      }
      if (widget.query == 'syncFail') {
        query.inboundError = true;
        query.beginYmd = '';
      }
    }
    query.endYmd = new DateFormat("yyyy-MM-dd").format(DateTime.now());
  }

  getBrandList() async {
    brandMap = {'ALL': '全部品牌'};
    var res = await BrandUtils.getBrandBindList();
    if (res != null) {
      brandMap.addAll(res);
    }
    setState(() {});
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  Future<Null> onFresh() async {
    handleRefresh();
  }

  // 上拉加载更多
  _getData({isRefresh = false, type}) async {
    query.current = page.toString();
    query.keepEffectStatus = listTypeState[widget.type];
    query.outbound = widget.type == PackageType.YQC ? 1 : 0;
    if (type != null) {
      query.outbound = type == PackageType.YQC ? 1 : 0;
      query.keepEffectStatus = listTypeState[type];
    }
    if (widget.query != null) {
      query.keepEffectStatus = 1;
    }
    setState(() {});
    DataResult result = await CourierDao.courierCabinetOrderPagList(query.toJson());
    return result;
  }

  initStoreType(String? storeType) {
    if (query.storeType != storeType) {
      query.storeType = storeType;
    }
  }

  _renderShortItem(index) {
    PackageViewEntity item = PackageViewEntity.fromJson(dataList[index]);
    return InkWell(
      onTap: () {
        NavigatorUtils.goPackageDetailPage(context, item,
            showSync: false, showReNotice: false, showBack: false, showEditPhone: false);
      },
      child: Container(
        color: Colors.white,
        margin: EdgeInsets.only(bottom: 1),
        padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
        child: CommonRowWidget(
          leftWidget: Row(
            children: [
              Container(
                width: 30,
                height: 30,
                child: CircleAvatar(
                  radius: 10.0,
                  backgroundColor: Colors.white,
                  backgroundImage: AssetImage(CommonUtils.getExpressLogo(item.brandCode)),
                ),
                margin: EdgeInsets.only(right: 5),
              ),
              Text(
                TextUtil.formatDigitPattern(item.waybillNo ?? '', digit: 5, pattern: '  '),
                style: TextStyle(fontSize: AppConstant.smallTextSize, fontWeight: FontWeight.w600),
              )
            ],
          ),
          rightWidget: Text(item.outboundTime != ''
              ? new DateFormat("MM/dd HH:mm:ss").format(DateTime.parse(item.outboundTime))
              : '出库时间未知'),
        ),
      ),
    );
  }

  _renderEventItem(int index) {
    PackageViewEntity item = PackageViewEntity.fromJson(dataList[index]);
    bool isCheck = _checkList.contains(item.id);
    return PackageNewItem(widget.type == PackageType.ZL ? ListType.RETENTION : ListType.WAIT,
        indexNo: index, package: item, bottom: buildBottom(item), isCheck: isCheck, bindTap: () {
      NavigatorUtils.goPackageDetailPage(context, item, showSync: false);
    }, checkItem: () {
      setState(() {
        if (isCheck) {
          _checkList.remove(item.id);
          checkList.remove(item);
        } else {
          _checkList.add(item.id);
          checkList.add(item);
        }
      });
    });
  }

  reNotice(PackageViewEntity item) {
    WaybillDao.reNotice(context, item.receiverMobile, item.id);
  }

  /// 异常出库
  unusualOut(PackageViewEntity item) {
    CommonUtils.customConfirmByReason(context, '是否进行异常出库操作？', (backReason) async {
      Navigator.of(context).pop();
      var cabinetLocationCode = item.storeType == 1 ? item.cabinetLocationCode : '0';
      EasyLoading.show(status: '正在取出...', maskType: EasyLoadingMaskType.black);
      var res = await CourierDao.courierUnusualOutWaybill(cabinetLocationCode, item.id, backReason);
      EasyLoading.dismiss();
      if (res.result) {
        handleRefresh();
        Fluttertoast.showToast(msg: '出库成功');
      }
    }, title: '出库提示', changeText: 'unusualOut', showClose: false, showInput: true);
  }

  unusualALLOut(PackageViewEntity item, String backReason) async {
    var cabinetLocationCode = item.storeType == 1 ? item.cabinetLocationCode : '0';
    EasyLoading.show(status: '正在取出...', maskType: EasyLoadingMaskType.black);
    var res = await CourierDao.courierUnusualOutWaybill(cabinetLocationCode, item.id, backReason);
    EasyLoading.dismiss();
    if (res.result) {
      Fluttertoast.showToast(msg: '出库成功');
      await Future.delayed(Duration(milliseconds: 100));
    }
  }

  courierGatewayResend(PackageViewEntity item) async {
    EasyLoading.show(status: '正在上传...', maskType: EasyLoadingMaskType.black);
    var res = await CourierDao.courierGatewayResend(item.id);
    EasyLoading.dismiss();
    if (res.result) {
      Fluttertoast.showToast(msg: '出库成功');
      await Future.delayed(Duration(milliseconds: 100));
    }
  }

  pickOutAll(PackageViewEntity item) async {
    EasyLoading.show(status: '正在取出...', maskType: EasyLoadingMaskType.black);
    var res = await CourierDao.courierOutboundWaybill(item.cabinetLocationCode, item.id);
    EasyLoading.dismiss();
    if (res.result) {
      Fluttertoast.showToast(msg: '出库成功');
      await Future.delayed(Duration(milliseconds: 100));
    }
  }

  reDispatchAll(PackageViewEntity item) async {
    DataResult res = await WaybillDao.reCalculateFee(item.cabinetLocationCode, item.id);
    if (res.result) {
      handleRefresh();
      Fluttertoast.showToast(msg: '重投成功');
      await Future.delayed(Duration(milliseconds: 100));
    }
  }

  // 批量操作
  batchOperation(String type) async {
    if (type == 'out') {
      await CommonUtils.customConfirmByReason(context, '是否进行异常出库操作？', (backReason) async {
        Navigator.of(context).pop();
        for (var item in checkList) {
          await unusualALLOut(item, backReason);
        }
        await handleRefresh();
      }, title: '出库提示', changeText: 'unusualOut', showClose: false, showInput: true);
    } else if (type == 'takeOut') {
      CommonUtils.customConfirm(context, '是否打开柜门取出包裹？', title: '开门提示', showClose: false, actions: <Widget>[
        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
              },
              child: Text('取消', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
              style: ButtonStyle(foregroundColor: MaterialStateProperty.resolveWith((states) {
                return Colors.black;
              }), backgroundColor: MaterialStateProperty.resolveWith((states) {
                return DefaultConfig().configs.WHITE_COLOR;
              }))),
          TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                for (var item in checkList) {
                  await pickOutAll(item);
                }
                await handleRefresh();
              },
              child: Text('是的', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
              style: ButtonStyle(foregroundColor: MaterialStateProperty.resolveWith((states) {
                return Colors.black;
              }), backgroundColor: MaterialStateProperty.resolveWith((states) {
                return Colors.white;
              }))),
        ])
      ]);
    } else if (type == 'sendMsg') {
      CommonUtils.customConfirm(context, '是否给客户再次发送取件通知？', title: '重新通知', showClose: false, actions: <Widget>[
        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
              },
              child: Text('取消', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
              style: ButtonStyle(
                  foregroundColor: MaterialStateProperty.resolveWith((states) {
                    return Colors.black;
                  }),
                  backgroundColor: MaterialStateProperty.resolveWith((states) {
                    return DefaultConfig().configs.WHITE_COLOR;
                  }),
                  minimumSize: MaterialStateProperty.all(Size(90, 40)))),
          TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                for (var item in checkList) {
                  await WaybillDao.notice(item.receiverMobile, item.orderId);
                  await Future.delayed(Duration(milliseconds: 100));
                }
                await handleRefresh();
              },
              child: Text('是的', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
              style: ButtonStyle(
                  foregroundColor: MaterialStateProperty.resolveWith((states) {
                    return Theme.of(context).primaryColor;
                  }),
                  backgroundColor: MaterialStateProperty.resolveWith((states) {
                    return Colors.white;
                  }),
                  minimumSize: MaterialStateProperty.all(Size(90, 40)))),
        ])
      ]);
    } else if (type == 'inbound') {
      String title = widget.isCourier ? '重投会自动重置计费，不做任何物流轨迹变更' : '重投会重置计时，不做任何物流轨迹变更';
      bool result = await CommonUtils.confirm(context, title, title: '确认重投');
      if (result) {
        for (var item in checkList) {
          await reDispatchAll(item);
        }
        await handleRefresh();
      }
    } else if (type == 'refreshInbound') {
      bool result = await CommonUtils.confirm(context, '是否进行重投入库操作', title: '确认重投');
      if (result) {
        for (var item in checkList) {
          await courierGatewayResend(item);
        }
        await handleRefresh();
      }
    }
  }

  /// 开门检查
  openCabinetCheck(PackageViewEntity item) {
    CommonUtils.customConfirm(context, '是否打开柜门检查包裹？', title: '开门检查', showClose: false, actions: <Widget>[
      Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
            },
            child: Text('取消', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
            style: ButtonStyle(foregroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.black;
            }), backgroundColor: MaterialStateProperty.resolveWith((states) {
              return DefaultConfig().configs.WHITE_COLOR;
            }))),
        TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              EasyLoading.show(status: '正在开门...', maskType: EasyLoadingMaskType.black);
              DataResult res = await CourierDao.courierBoxOpenCheck(item.cabinetBoxId);
              EasyLoading.dismiss();
              if (res.result) {
                showBoxOpen(context, item, (item) {});
              }
            },
            child: Text('是的', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
            style: ButtonStyle(foregroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.black;
            }), backgroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.white;
            }))),
      ])
    ]);
  }

  /// 出仓
  outBound(PackageViewEntity item) {
    CommonUtils.customConfirm(context, '是否进行出仓操作？', title: '出仓提醒', showClose: false, actions: <Widget>[
      Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
            },
            child: Text('取消', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
            style: ButtonStyle(foregroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.black;
            }), backgroundColor: MaterialStateProperty.resolveWith((states) {
              return DefaultConfig().configs.WHITE_COLOR;
            }))),
        TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              EasyLoading.show(status: '正在出仓...', maskType: EasyLoadingMaskType.black);
              var res = await CourierDao.courierShopOutDoor(item.waybillNo);
              EasyLoading.dismiss();
              if (res != null && res.result) {
                Fluttertoast.showToast(msg: '出仓成功');
                handleRefresh();
              } else {
                SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
              }
            },
            child: Text('是的', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
            style: ButtonStyle(foregroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.black;
            }), backgroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.white;
            }))),
      ])
    ]);
  }

  /// 格口打开
  showBoxOpen(BuildContext context, PackageViewEntity item, onPressed(item)) {
    return showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
        topLeft: Radius.circular(10.0),
        topRight: Radius.circular(10.0),
      )),
      builder: (BuildContext _context) {
        return StatefulBuilder(
          builder: (_context, state) {
            return Stack(
              children: [
                Container(
                  height: 30.0,
                  width: double.infinity,
                  color: Colors.black54,
                ),
                Container(
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      )),
                ),
                Container(
                  height: 550,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            LocalImageUtil.getImageAsset('boxOpen', width: MediaQuery.of(context).size.width * 0.3),
                            Padding(padding: EdgeInsets.only(bottom: 10)),
                            RichText(
                                text: TextSpan(children: <TextSpan>[
                              TextSpan(
                                text: '${item.cabinetName ?? ''}-',
                                style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 16),
                              ),
                              TextSpan(
                                text: '${item.cabinetBoxLabel}号',
                                style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 16),
                              ),
                              TextSpan(
                                text: ' 格口已开 ',
                                style: TextStyle(color: Colors.black, fontSize: 14),
                              ),
                            ])),
                            Padding(padding: EdgeInsets.only(bottom: 10)),
                            Offstage(
                              offstage: false,
                              child: Text('放入包裹后请关门', style: TextStyle(color: Color(0xFF999999), fontSize: 14)),
                            )
                          ],
                        ),
                      ),
                      Offstage(
                          offstage: false,
                          child: BottomBtnWidget(
                              showShadow: false,
                              title: '我已关门',
                              action: () {
                                closeCabinetBox(item.cabinetBoxId!);
                              }))
                    ],
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  closeCabinetBox(String cabinetBoxId) async {
    EasyLoading.show(status: '正在请求关门...', maskType: EasyLoadingMaskType.black);
    var res = await CabinetDao.cabinetBoxCloseDoor(cabinetBoxId);
    EasyLoading.dismiss();
    if (res.result) {
      Navigator.pop(context);
    }
  }

  /// 取出
  pickOut(PackageViewEntity item) {
    CommonUtils.customConfirm(context, '是否打开柜门取出包裹？', title: '开门提示', showClose: false, actions: <Widget>[
      Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
            },
            child: Text('取消', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
            style: ButtonStyle(foregroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.black;
            }), backgroundColor: MaterialStateProperty.resolveWith((states) {
              return DefaultConfig().configs.WHITE_COLOR;
            }))),
        TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              EasyLoading.show(status: '正在取出...', maskType: EasyLoadingMaskType.black);
              var res = await CourierDao.courierOutboundWaybill(item.cabinetLocationCode, item.id);
              EasyLoading.dismiss();
              if (res.result) {
                handleRefresh();
                Fluttertoast.showToast(msg: '取出成功');
              }
            },
            child: Text('是的', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
            style: ButtonStyle(foregroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.black;
            }), backgroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.white;
            }))),
      ])
    ]);
  }

  reDispatch(PackageViewEntity item) async {
    String title = widget.isCourier ? '重投会自动重置计费，不做任何物流轨迹变更' : '重投会重置计时，不做任何物流轨迹变更';
    bool result = await CommonUtils.confirm(context, title, title: '确认重投');
    if (result) {
      DataResult res = await WaybillDao.reCalculateFee(item.cabinetLocationCode, item.id);
      if (res.result) {
        handleRefresh();
        Fluttertoast.showToast(msg: '重投成功');
      }
    }
  }

  buildBottom(PackageViewEntity item) {
    if (widget.isCourier) {
      return CommonRowWidget(
        leftWidget: Row(
          children: [
            Offstage(
              offstage: false,
              child: OutlinedButton(
                onPressed: () {
                  /// 重发短信
                  reNotice(item);
                },
                child: Text(
                  '重新通知',
                  style: TextStyle(fontSize: 12),
                ),
                style: ButtonStyle(
                    minimumSize: MaterialStateProperty.all(Size(70, 36)),
                    foregroundColor: MaterialStateProperty.resolveWith((states) {
                      return Colors.white;
                    }),
                    backgroundColor: MaterialStateProperty.resolveWith((states) {
                      return Theme.of(context).primaryColor;
                    })),
              ),
            ),
            Padding(padding: EdgeInsets.only(left: 5)),
            Offstage(
              offstage: widget.type != PackageType.ZL,
              child: OutlinedButton(
                onPressed: () {
                  /// 重投
                  reDispatch(item);
                },
                child: Text(
                  '一键重投',
                  style: TextStyle(fontSize: 12),
                ),
                style: ButtonStyle(
                    minimumSize: MaterialStateProperty.all(Size(70, 36)),
                    foregroundColor: MaterialStateProperty.resolveWith((states) {
                      return Colors.white;
                    }),
                    backgroundColor: MaterialStateProperty.resolveWith((states) {
                      return Theme.of(context).primaryColor;
                    })),
              ),
            )
          ],
        ),
      );
    } else {
      return CommonRowWidget(
          leftWidget: Expanded(
        child: Wrap(
          children: [
            OutlinedButton(
              onPressed: () {
                /// 异常出库
                unusualOut(item);
              },
              child: Text(
                '异常出库',
                style: TextStyle(fontSize: 12),
              ),
              style: ButtonStyle(
                  minimumSize: MaterialStateProperty.all(Size(70, 36)),
                  foregroundColor: MaterialStateProperty.resolveWith((states) {
                    return Colors.white;
                  }),
                  backgroundColor: MaterialStateProperty.resolveWith((states) {
                    return Theme.of(context).primaryColor;
                  })),
            ),
            Padding(padding: EdgeInsets.only(left: 5)),
            OutlinedButton(
              onPressed: () {
                /// 重发短信
                reNotice(item);
              },
              child: Text(
                '重新通知',
                style: TextStyle(fontSize: 12),
              ),
              style: ButtonStyle(
                  minimumSize: MaterialStateProperty.all(Size(70, 36)),
                  foregroundColor: MaterialStateProperty.resolveWith((states) {
                    return Colors.white;
                  }),
                  backgroundColor: MaterialStateProperty.resolveWith((states) {
                    return Theme.of(context).primaryColor;
                  })),
            ),
            Padding(padding: EdgeInsets.only(left: 5)),
            Offstage(
              offstage: !(widget.type == PackageType.ZL && item.storeType == 1),
              child: OutlinedButton(
                onPressed: () {
                  /// 重投
                  reDispatch(item);
                },
                child: Text(
                  '一键重投',
                  style: TextStyle(fontSize: 12),
                ),
                style: ButtonStyle(
                    minimumSize: MaterialStateProperty.all(Size(70, 36)),
                    foregroundColor: MaterialStateProperty.resolveWith((states) {
                      return Colors.white;
                    }),
                    backgroundColor: MaterialStateProperty.resolveWith((states) {
                      return Theme.of(context).primaryColor;
                    })),
              ),
            ),
            Padding(padding: EdgeInsets.only(left: (widget.type == PackageType.ZL && item.storeType == 1) ? 5 : 0)),
            OutlinedButton(
              onPressed: () {
                if (item.storeType == 1) {
                  openCabinetCheck(item);
                } else {
                  outBound(item);
                }
              },
              child: Text(
                item.storeType == 1 ? "开门检查" : '出仓',
                style: TextStyle(fontSize: 12),
              ),
              style: ButtonStyle(
                  minimumSize: MaterialStateProperty.all(Size(70, 36)),
                  foregroundColor: MaterialStateProperty.resolveWith((states) {
                    return Colors.white;
                  }),
                  backgroundColor: MaterialStateProperty.resolveWith((states) {
                    return Theme.of(context).primaryColor;
                  })),
            ),
            Padding(padding: EdgeInsets.only(left: 5)),
            OutlinedButton(
              onPressed: () {
                pickOut(item);
              },
              child: Text(
                '取出',
                style: TextStyle(fontSize: 12),
              ),
              style: ButtonStyle(
                  minimumSize: MaterialStateProperty.all(Size(54, 36)),
                  foregroundColor: MaterialStateProperty.resolveWith((states) {
                    return Colors.white;
                  }),
                  backgroundColor: MaterialStateProperty.resolveWith((states) {
                    return Theme.of(context).primaryColor;
                  })),
            ),
          ],
        ),
      ));
    }
  }

  getTimeRanger(String type) {
    return type;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Container(
      child: Stack(
        children: [
          Column(
            children: <Widget>[
              /// 头部筛选项框
              buildSearchInput(),
              // buildFilterButtons(),  //原先的方法
              new Expanded(
                child: Container(
                  child: Stack(
                    children: [
                      Positioned(
                          top: widget.marginTop,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          child: AppPullLoadWidget(
                            pullLoadWidgetControl,
                            (BuildContext context, int index) =>
                                widget.type == PackageType.YQC ? _renderShortItem(index) : _renderEventItem(index),
                            handleRefresh,
                            onLoadMore,
                            // showNoMore: true,
                            refreshKey: refreshIndicatorKey,
                          ))
                    ],
                  ),
                ),
              ),
            ],
          ),
          Positioned(
            bottom: 0,
            child: Offstage(
              offstage: _checkList.length == 0,
              child: Container(
                  padding: EdgeInsets.fromLTRB(10, 10, 10, 25),
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                    color: Colors.white,
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 20.0,
                        height: 20.0,
                        margin: EdgeInsets.fromLTRB(10, 0.0, 10.0, 0.0),
                        child: Checkbox(
                          value: _checkList.length == dataList.length,
                          activeColor: Theme.of(context).primaryColor,
                          onChanged: (bool? value) {
                            setState(() {
                              _checkList.clear();
                              checkList.clear();
                              if (value!) {
                                dataList.forEach((item) {
                                  PackageViewEntity data = PackageViewEntity.fromJson(item);
                                  _checkList.add(item['id']);
                                  checkList.add(data);
                                });
                              }
                            });
                          },
                        ),
                      ),
                      Text('选中${_checkList.length}项'),
                      Expanded(
                        child: Wrap(
                          children: [
                            Container(
                              margin: EdgeInsets.only(left: 10),
                              child: OutlinedButton(
                                onPressed: () {
                                  batchOperation('out');
                                },
                                child: Text(
                                  '异常出库',
                                  style: TextStyle(fontSize: 12),
                                ),
                                style: ButtonStyle(
                                    minimumSize: MaterialStateProperty.all(Size(70, 36)),
                                    foregroundColor: MaterialStateProperty.resolveWith((states) {
                                      return Colors.white;
                                    }),
                                    backgroundColor: MaterialStateProperty.resolveWith((states) {
                                      return Theme.of(context).primaryColor;
                                    })),
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(left: 10),
                              child: OutlinedButton(
                                onPressed: () {
                                  batchOperation('takeOut');
                                },
                                child: Text(
                                  '取出',
                                  style: TextStyle(fontSize: 12),
                                ),
                                style: ButtonStyle(
                                    minimumSize: MaterialStateProperty.all(Size(54, 36)),
                                    foregroundColor: MaterialStateProperty.resolveWith((states) {
                                      return Colors.white;
                                    }),
                                    backgroundColor: MaterialStateProperty.resolveWith((states) {
                                      return Theme.of(context).primaryColor;
                                    })),
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(left: 10),
                              child: OutlinedButton(
                                onPressed: () {
                                  batchOperation('sendMsg');
                                },
                                child: Text(
                                  '重新通知',
                                  style: TextStyle(fontSize: 12),
                                ),
                                style: ButtonStyle(
                                    minimumSize: MaterialStateProperty.all(Size(54, 36)),
                                    foregroundColor: MaterialStateProperty.resolveWith((states) {
                                      return Colors.white;
                                    }),
                                    backgroundColor: MaterialStateProperty.resolveWith((states) {
                                      return Theme.of(context).primaryColor;
                                    })),
                              ),
                            ),
                            Offstage(
                              offstage: query.inboundError == null,
                              child: Container(
                                margin: EdgeInsets.only(left: 10),
                                child: OutlinedButton(
                                  onPressed: () {
                                    batchOperation('refreshInbound');
                                  },
                                  child: Text(
                                    '重投入库',
                                    style: TextStyle(fontSize: 12),
                                  ),
                                  style: ButtonStyle(
                                      minimumSize: MaterialStateProperty.all(Size(54, 36)),
                                      foregroundColor: MaterialStateProperty.resolveWith((states) {
                                        return Colors.white;
                                      }),
                                      backgroundColor: MaterialStateProperty.resolveWith((states) {
                                        return Theme.of(context).primaryColor;
                                      })),
                                ),
                              ),
                            ),
                            Offstage(
                                offstage: widget.type != PackageType.ZL,
                                child: Container(
                                  margin: EdgeInsets.only(left: 10),
                                  child: OutlinedButton(
                                    onPressed: () {
                                      batchOperation('inbound');
                                    },
                                    child: Text(
                                      '一键重投',
                                      style: TextStyle(fontSize: 12),
                                    ),
                                    style: ButtonStyle(
                                        minimumSize: MaterialStateProperty.all(Size(54, 36)),
                                        foregroundColor: MaterialStateProperty.resolveWith((states) {
                                          return Colors.white;
                                        }),
                                        backgroundColor: MaterialStateProperty.resolveWith((states) {
                                          return Theme.of(context).primaryColor;
                                        })),
                                  ),
                                )),
                          ],
                        ),
                      )
                    ],
                  )),
            ),
          )
        ],
      ),
    );
  }

  /// 构建搜索输入框
  Widget buildSearchInput() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: Colors.white,
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 45,
              decoration: BoxDecoration(
                color: Color(0xFFF5F5F5),
                borderRadius: BorderRadius.circular(20),
              ),
              child: TextField(
                controller: _searchController,
                focusNode: _searchFocusNode,
                decoration: InputDecoration(
                  hintText: '取件码/单号/手机号搜索',
                  hintStyle: TextStyle(
                    color: Colors.grey[500],
                    fontSize: 14,
                  ),
                  prefixIcon: Icon(
                    Icons.search,
                    color: Colors.grey[500],
                    size: 20,
                  ),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                ),
                style: TextStyle(fontSize: 14),
                onSubmitted: (value) {
                  _performSearch(value);
                },
                onChanged: (value) {
                  // 可以添加实时搜索逻辑
                },
              ),
            ),
          ),
          SizedBox(width: 8),
          // 筛选按钮
          InkWell(
            onTap: () {
              _showFilterPanel();
            },
            child: Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Color(0xFFF5F5F5),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.tune,
                color: Colors.grey[600],
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 执行搜索
  void _performSearch(String keyword) {
    setState(() {
      query.keyword = keyword.trim().isEmpty ? null : keyword.trim();
    });
    handleRefresh();
  }

  Widget buildFilterButtons() {
    return Offstage(
      offstage: false,
      child: Container(
          padding: EdgeInsets.only(left: 15, right: 10),
          color: Colors.white,
          height: 48,
          width: MediaQuery.of(context).size.width,
          child: Row(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      buildSearchItem('用户需付费快件', query.overdue, cb: () {
                        if (query.overdue != null) {
                          query.overdue = null;
                        } else {
                          query.overdue = true;
                        }
                        handleRefresh();
                        setState(() {});
                      }),
                      buildSearchItem('短信失败', query.smsError, cb: () {
                        if (query.smsError != null) {
                          query.smsError = null;
                        } else {
                          query.smsError = true;
                        }
                        handleRefresh();
                        setState(() {});
                      }),
                      buildSearchItem('超3天滞留', query.keepEffectDay == 3, cb: () {
                        if (query.keepEffectDay != null) {
                          query.keepEffectDay = null;
                        } else {
                          query.keepEffectDay = 3;
                        }
                        handleRefresh();
                        setState(() {});
                      }),
                       buildSearchItem('超5天滞留', query.keepEffectDay == 5, cb: () {
                        if (query.keepEffectDay != null) {
                          query.keepEffectDay = null;
                        } else {
                          query.keepEffectDay = 5;
                        }
                        handleRefresh();
                        setState(() {});
                      }),
                      buildSearchItem('超7天滞留', query.keepEffectDay == 7, cb: () {
                        if (query.keepEffectDay != null) {
                          query.keepEffectDay = null;
                        } else {
                          query.keepEffectDay = 7;
                        }
                        handleRefresh();
                        setState(() {});
                      }),
                      buildSearchItem('入库失败', query.inboundError, cb: () {
                        if (query.inboundError != null) {
                          query.inboundError = null;
                        } else {
                          query.inboundError = true;
                        }
                        handleRefresh();
                        setState(() {});
                      }),
                      buildSearchItem('签收失败', query.signError, cb: () {
                        if (query.signError != null) {
                          query.signError = null;
                        } else {
                          query.signError = true;
                        }
                        handleRefresh();
                        setState(() {});
                      }),
                      buildSearchItem('退回失败', query.returnError, cb: () {
                        if (query.returnError != null) {
                          query.returnError = null;
                        } else {
                          query.returnError = true;
                        }
                        handleRefresh();
                        setState(() {});
                      }),
                    ],
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.only(left: 4),
                margin: EdgeInsets.only(left: 4),
                constraints: BoxConstraints(maxWidth: 105),
                decoration: BoxDecoration(boxShadow: [
                  BoxShadow(color: Colors.grey.shade300, offset: Offset(-2, 0), blurRadius: 3),
                  BoxShadow(color: Colors.white, offset: Offset(0, -3), blurRadius: 0),
                  BoxShadow(color: Colors.white, offset: Offset(0, 3), blurRadius: 0),
                ]),
                child: CustomerDropDownSelectWidget(
                  hintText: '最近14天',
                  map: timeMap,
                  value: query.timeRanger ?? 'near14Day',
                  cb: (item) {
                    String time = getTimeRanger(item);
                    setState(() {
                      query.timeRanger = time;
                    });
                    handleRefresh();
                  },
                ),
              ),
          
            ],
          )),
    );
  }

  buildSearchItem(String title, value, {VoidCallback? cb}) {
    return InkWell(
      onTap: () => throttleUtil.throttle(() {
        if (cb != null) {
          cb();
        }
      }),
      child: Container(
        margin: EdgeInsets.only(right: 10),
        padding: EdgeInsets.fromLTRB(5, 3, 5, 3),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: value == true ? Theme.of(context).primaryColor : Colors.grey.shade200),
        child: Text(title, style: TextStyle(color: value == true ? Colors.white : Colors.black87)),
      ),
    );
  }

// TODO: implement wantKeepAlive
  @override
  bool get wantKeepAlive => true;

// TODO: implement isRefreshFirst
  @override
  bool get isRefreshFirst => false;

  @override
  requestLoadMore() async {
    return await _getData();
  }

  @override
  requestRefresh({PackageType? type}) async {
    return await _getData(isRefresh: true, type: type);
  }

  @protected
  Future<Null> handleRefresh({PackageType? type}) async {
    setState(() {
      _checkList = [];
      checkList = [];
    });
    if (isLoading) {
      return null;
    }
    refreshIndicatorKey.currentState?.show();
    isLoading = true;
    page = 1;
    var res = await requestRefresh(type: type);
    resolveRefreshResult(res);
    resolveDataResult(res);
    if (res.next != null) {
      var resNext = await res.next;
      resolveRefreshResult(resNext);
      resolveDataResult(resNext);
    }
    isLoading = false;
    return null;
  }

  /// 显示筛选面板 - 使用Drawer抽屉样式
  void _showFilterPanel() {
    // 复制当前查询条件到临时变量
    _tempQuery = PackageSearchEntity();
    _tempQuery.beginYmd = query.beginYmd;
    _tempQuery.endYmd = query.endYmd;
    _tempQuery.timeRanger = query.timeRanger;
    _tempQuery.overdue = query.overdue;
    _tempQuery.smsError = query.smsError;
    _tempQuery.keepEffectDay = query.keepEffectDay;
    _tempQuery.inboundError = query.inboundError;
    _tempQuery.signError = query.signError;
    _tempQuery.returnError = query.returnError;
    _tempQuery.brandCode = query.brandCode;

    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      barrierColor: Colors.black54,
      transitionDuration: Duration(milliseconds: 300),
      pageBuilder: (context, animation, secondaryAnimation) {
        return Align(
          alignment: Alignment.centerRight,
          child: Material(
            child: Container(
              width: MediaQuery.of(context).size.width * 0.85,
              height: MediaQuery.of(context).size.height,
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 10,
                    offset: Offset(-2, 0),
                  ),
                ],
              ),
              child: StatefulBuilder(
                builder: (context, setModalState) {
                  return Column(
                    children: [
                      // 标题栏
                      Container(
                        padding: EdgeInsets.fromLTRB(16, MediaQuery.of(context).padding.top + 12, 16, 0),
                        decoration: BoxDecoration(
                          color: Colors.white,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '筛选',
                              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                            ),
                            
                            Material(
                              color: Colors.transparent,
                              child: InkWell(
                                onTap: () {
                                  Navigator.pop(context);
                                },
                                borderRadius: BorderRadius.circular(20),
                                splashColor: Colors.grey[300]?.withValues(alpha: 0.3),
                                highlightColor: Colors.grey[200]?.withValues(alpha: 0.2),
                                child: Container(
                                  padding: EdgeInsets.all(8),
                                  child: Icon(
                                    Icons.close,
                                    color: Colors.grey[600],
                                    size: 26,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      // 筛选内容
                      Expanded(
                        child: Container(
                          child: SingleChildScrollView(
                            padding: EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildFilterSection(context, setModalState),
                              ],
                            ),
                          ),
                        ),
                      ),
                      // 底部按钮
                      Container(
                        padding: EdgeInsets.fromLTRB(16, 12, 16, MediaQuery.of(context).padding.bottom + 12),
                        decoration: BoxDecoration(
                          color: Colors.white,
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: OutlinedButton(
                                onPressed: () {
                                  _resetFilter();
                                  setModalState(() {});
                                },
                                child: Text(
                                  '重置', 
                                  style: TextStyle(color: const Color.fromARGB(255, 0, 0, 0))
                                  ),
                                style: OutlinedButton.styleFrom(
                                  padding: EdgeInsets.symmetric(vertical: 12),
                                  side: BorderSide(color: Colors.grey[400]!),
                                ),
                              ),
                            ),
                            SizedBox(width: 12),
                            Expanded(
                              flex: 2,
                              child: ElevatedButton(
                                onPressed: () {
                                  Navigator.pop(context);
                                  _applyFilter();
                                },
                                child: Text('确定'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Theme.of(context).primaryColor,
                                  foregroundColor: Colors.white,
                                  padding: EdgeInsets.symmetric(vertical: 12),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        );
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return SlideTransition(
          position: Tween<Offset>(
            begin: Offset(1.0, 0.0),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: Curves.easeInOut,
          )),
          child: child,
        );
      },
    );
  }

  /// 隐藏筛选面板
  void _hideFilterPanel() {
    // 不需要设置状态，因为我们使用的是模态底部表单
  }

  /// 应用筛选条件
  void _applyFilter() {
    query.beginYmd = _tempQuery.beginYmd;
    query.endYmd = _tempQuery.endYmd;
    query.timeRanger = _tempQuery.timeRanger;
    query.overdue = _tempQuery.overdue;
    query.smsError = _tempQuery.smsError;
    query.keepEffectDay = _tempQuery.keepEffectDay;
    query.inboundError = _tempQuery.inboundError;
    query.signError = _tempQuery.signError;
    query.returnError = _tempQuery.returnError;
    query.brandCode = _tempQuery.brandCode;

    _hideFilterPanel();
    handleRefresh();
  }

  /// 重置筛选条件
  void _resetFilter() {
    _tempQuery = PackageSearchEntity();
    setDefaultQuery();
    _tempQuery.beginYmd = query.beginYmd;
    _tempQuery.endYmd = query.endYmd;
    _tempQuery.timeRanger = query.timeRanger;
    setState(() {});
  }
  /// 构建筛选面板内容
  Widget _buildFilterSection(BuildContext context, StateSetter setModalState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 日期筛选
        _buildDrawerSection(
          title: '日期',
          child: Column(
            children: [
              _buildDateRangeButtons(setModalState),
            ],
          ),
        ),

        // 快递状态筛选
        _buildDrawerSection(
          title: '快递状态',
          child: Column(
            children: [
              _buildStatusButtons(setModalState),
            ],
          ),
        ),

        // 快递公司筛选
        _buildDrawerSection(
          title: '快递公司',
          child: Column(
            children: [
              _buildExpressCompanyButtons(setModalState),
            ],
          ),
        ),

        // 短信状态筛选
        _buildDrawerSection(
          title: '短信状态',
          child: Column(
            children: [
              _buildSmsStatusButtons(setModalState),
            ],
          ),
        ),

        
      ],
    );
  }

  /// 构建抽屉样式的筛选区块
  Widget _buildDrawerSection({required String title, required Widget child}) {
    return Container(
      margin: EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(bottom: 12),
            child: Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          ),
          child,
        ],
      ),
    );
  }

  /// 构建日期范围按钮
  Widget _buildDateRangeButtons(StateSetter setModalState) {
    final dateOptions = [
      {'key': 'all', 'label': '全部'},
      {'key': 'today', 'label': '今日'},
      {'key': 'near7Days', 'label': '近7天'},
      {'key': 'near30Days', 'label': '近30天'},
      {'key': 'thisMonth', 'label': '本月'},
      {'key': 'custom', 'label': _getCustomDateLabel()},
    ];

    return Column(
      children: [
        // 第一行：全部、今日、近7天
        Row(
          children: dateOptions.take(3).map((option) {
            return Expanded(
              child: Container(
                margin: EdgeInsets.only(right: option == dateOptions[2] ? 0 : 8),
                child: _buildDateButton(option, setModalState),
              ),
            );
          }).toList(),
        ),
        SizedBox(height: 8),
        // 第二行：近30天、本月、自定义
        Row(
          children: dateOptions.skip(3).map((option) {
            return Expanded(
              child: Container(
                margin: EdgeInsets.only(right: option == dateOptions.last ? 0 : 8),
                child: _buildDateButton(option, setModalState),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// 构建单个日期按钮
  Widget _buildDateButton(Map<String, dynamic> option, StateSetter setModalState) {
    bool isSelected = _tempQuery.timeRanger == option['key'] ||
                     (option['key'] == 'all' && _tempQuery.timeRanger == null) ||
                     (option['key'] == 'custom' && _isCustomDateSelected());

    return InkWell(
      onTap: () {
        if (option['key'] == 'custom') {
          // 点击自定义时弹出日期选择器
          _showCustomDatePicker(context, setModalState);
        } else {
          setModalState(() {
            if (option['key'] == 'all') {
              _tempQuery.timeRanger = null;
              _tempQuery.beginYmd = '';
              _tempQuery.endYmd = '';
            } else {
              _tempQuery.timeRanger = option['key'] as String;
              _setDateRangeByKey(option['key'] as String);
            }
          });
        }
      },
      borderRadius: BorderRadius.circular(4),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        decoration: BoxDecoration(
          color: isSelected ? Theme.of(context).primaryColor : Colors.white,
          border: Border.all(
            color: isSelected ? Theme.of(context).primaryColor : Colors.grey[300]!,
          ),
          borderRadius: BorderRadius.circular(4), // 轻微圆角
        ),
        child: Text(
          option['label'] as String,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.black87,
            fontSize: option['key'] == 'custom' && _isCustomDateSelected() ? 12 : 14,
            fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
          ),
          // maxLines: 1,
          // overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }

  /// 获取自定义日期标签文本
  String _getCustomDateLabel() {
    if (_tempQuery.beginYmd.isNotEmpty && _tempQuery.endYmd.isNotEmpty && _tempQuery.timeRanger == 'custom') {
      // 如果开始和结束日期相同，只显示一个日期
      if (_tempQuery.beginYmd == _tempQuery.endYmd) {
        return _tempQuery.beginYmd.substring(5); // 显示 MM-dd 格式
      }
      // 显示日期范围，格式：MM-dd~MM-dd，使用更紧凑的格式
      String startDate = _tempQuery.beginYmd.substring(5);
      String endDate = _tempQuery.endYmd.substring(5);
      return '$startDate~$endDate';
    } 
    return '自定义';
  }

  /// 判断是否选择了自定义日期
  bool _isCustomDateSelected() {
    return _tempQuery.beginYmd.isNotEmpty && _tempQuery.endYmd.isNotEmpty &&
           _tempQuery.timeRanger == 'custom';
  }

  /// 显示自定义日期选择器
  void _showCustomDatePicker(BuildContext context, StateSetter setModalState) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.0),
          topRight: Radius.circular(16.0),
        ),
      ),
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDateModalState) {
            return Container(
              height: MediaQuery.of(context).size.height * 0.6,
              padding: EdgeInsets.all(16),
              child: Column(
                children: [
                  // 标题栏
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      TextButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        child: Text('取消', style: TextStyle(color: Colors.grey[600])),
                      ),
                      Text(
                        '选择时间范围',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      TextButton(
                        onPressed: () {
                          Navigator.pop(context);
                          setModalState(() {
                            _tempQuery.timeRanger = 'custom';
                          });
                        },
                        child: Text('确定', style: TextStyle(color: Theme.of(context).primaryColor)),
                      ),
                    ],
                  ),
                  SizedBox(height: 20),
                  // 日期选择区域
                  Expanded(
                    child: Column(
                      children: [
                        // 开始日期
                        Container(
                          margin: EdgeInsets.only(bottom: 16),
                          child: InkWell(
                            onTap: () => _selectCustomDate(context, true, setDateModalState, setModalState),
                            child: Container(
                              padding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey[300]!),
                                borderRadius: BorderRadius.circular(8),
                                color: Colors.white,
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text('开始日期', style: TextStyle(fontSize: 12, color: Colors.grey[600])),
                                      SizedBox(height: 4),
                                      Text(
                                        _tempQuery.beginYmd.isEmpty ? '请选择开始日期' : _tempQuery.beginYmd,
                                        style: TextStyle(
                                          fontSize: 16,
                                          color: _tempQuery.beginYmd.isEmpty ? Colors.grey[400] : Colors.black87,
                                        ),
                                      ),
                                    ],
                                  ),
                                  Icon(Icons.calendar_today, color: Colors.grey[600]),
                                ],
                              ),
                            ),
                          ),
                        ),
                        // 结束日期
                        Container(
                          child: InkWell(
                            onTap: () => _selectCustomDate(context, false, setDateModalState, setModalState),
                            child: Container(
                              padding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey[300]!),
                                borderRadius: BorderRadius.circular(8),
                                color: Colors.white,
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text('结束日期', style: TextStyle(fontSize: 12, color: Colors.grey[600])),
                                      SizedBox(height: 4),
                                      Text(
                                        _tempQuery.endYmd.isEmpty ? '请选择结束日期' : _tempQuery.endYmd,
                                        style: TextStyle(
                                          fontSize: 16,
                                          color: _tempQuery.endYmd.isEmpty ? Colors.grey[400] : Colors.black87,
                                        ),
                                      ),
                                    ],
                                  ),
                                  Icon(Icons.calendar_today, color: Colors.grey[600]),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }


  /// 设置日期范围根据key
  void _setDateRangeByKey(String key) {
    DateTime now = DateTime.now();
    switch (key) {
      case 'today':
        _tempQuery.beginYmd = DateFormat("yyyy-MM-dd").format(now);
        _tempQuery.endYmd = DateFormat("yyyy-MM-dd").format(now);
        break;
      case 'near7Days':
        _tempQuery.beginYmd = DateFormat("yyyy-MM-dd").format(now.subtract(Duration(days: 7)));
        _tempQuery.endYmd = DateFormat("yyyy-MM-dd").format(now);
        break;
      case 'near30Days':
        _tempQuery.beginYmd = DateFormat("yyyy-MM-dd").format(now.subtract(Duration(days: 30)));
        _tempQuery.endYmd = DateFormat("yyyy-MM-dd").format(now);
        break;
      case 'thisMonth':
        _tempQuery.beginYmd = DateFormat("yyyy-MM-dd").format(DateTime(now.year, now.month, 1));
        _tempQuery.endYmd = DateFormat("yyyy-MM-dd").format(now);
        break;
    }
  }

  /// 构建状态筛选按钮
  Widget _buildStatusButtons(StateSetter setModalState) {
    final statusOptions = [
      {'key': 'overdue', 'label': '用户需付费快件', 'value': _tempQuery.overdue},
      {'key': 'keepEffect3', 'label': '超3天', 'value': _tempQuery.keepEffectDay == 3},
      {'key': 'keepEffect5', 'label': '最近3天', 'value': _tempQuery.keepEffectDay == 5},
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: statusOptions.map((option) {
        bool isSelected = option['value'] as bool? ?? false;
        return InkWell(
          onTap: () {
            setModalState(() {
              switch (option['key']) {
                case 'overdue':
                  _tempQuery.overdue = isSelected ? null : true;
                  break;
                case 'smsError':
                  _tempQuery.smsError = isSelected ? null : true;
                  break;
                case 'keepEffect3':
                  _tempQuery.keepEffectDay = isSelected ? null : 3;
                  break;
                case 'keepEffect5':
                  _tempQuery.keepEffectDay = isSelected ? null : 5;
                  break;
              }
            });
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: isSelected ? Theme.of(context).primaryColor : Colors.white,
              border: Border.all(
                color: isSelected ? Theme.of(context).primaryColor : Colors.grey[300]!,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              option['label'] as String,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.black87,
                fontSize: 14,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  /// 构建短信状态筛选按钮
  /// @param setModalState 状态设置器
  Widget _buildSmsStatusButtons(StateSetter setModalState) {
    final smsStatusOptions = [
      {'key': 'smsSuccess', 'label': '短信成功', 'value': _tempQuery.smsSuccess},
      {'key': 'smsError', 'label': '短信失败', 'value': _tempQuery.smsError},
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: smsStatusOptions.map((option) {
        bool isSelected = option['value'] as bool? ?? false;
        return InkWell(
          onTap: () {
            setModalState(() {
              switch (option['key']) {
                case 'smsSuccess':
                  _tempQuery.smsSuccess = isSelected ? null : true;
                  break;
                case 'smsError':
                  _tempQuery.smsError = isSelected ? null : true;
                  break;
              }
            });
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: isSelected ? Theme.of(context).primaryColor : Colors.white,
              border: Border.all(
                color: isSelected ? Theme.of(context).primaryColor : Colors.grey[300]!,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              option['label'] as String,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.black87,
                fontSize: 14,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  /// 构建快递公司筛选按钮
  Widget _buildExpressCompanyButtons(StateSetter setModalState) {
    // 使用 ConfigBase 中的 EXPRESS Map 来构建快递公司选项
    final expressMap = DefaultConfig().configs.EXPRESS;
    final companyOptions = expressMap.entries.map((entry) => {
      'key': entry.key,
      'label': entry.value,
    }).toList();

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: companyOptions.map((option) {
        bool isSelected = _tempQuery.brandCode == option['key'];
        return InkWell(
          onTap: () {
            setModalState(() {
              _tempQuery.brandCode = isSelected ? null : option['key'] as String;
            });
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: isSelected ? Theme.of(context).primaryColor : Colors.white,
              border: Border.all(
                color: isSelected ? Theme.of(context).primaryColor : Colors.grey[300]!,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              option['label'] as String,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.black87,
                fontSize: 14,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }



  /// 选择自定义日期
  void _selectCustomDate(BuildContext context, bool isStartDate, StateSetter setDateModalState, StateSetter setModalState) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now().subtract(Duration(days: 365)),
      lastDate: DateTime.now(),
      locale: Locale('zh', 'CN'),
    );

    if (picked != null) {
      String formattedDate = DateFormat("yyyy-MM-dd").format(picked);
      setDateModalState(() {
        if (isStartDate) {
          _tempQuery.beginYmd = formattedDate;
        } else {
          _tempQuery.endYmd = formattedDate;
        }
      });
      // 同时更新外层的状态
      setModalState(() {
        if (isStartDate) {
          _tempQuery.beginYmd = formattedDate;
        } else {
          _tempQuery.endYmd = formattedDate;
        }
      });
    }
  }
}
