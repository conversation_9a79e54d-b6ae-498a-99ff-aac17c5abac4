import 'dart:async';
import 'dart:io';

import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CabinetDao.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/dao/WaybillDao.dart';
import 'package:cabinet_flutter_app/common/entitys/package_view_entity.dart';
import 'package:cabinet_flutter_app/common/net/Address.dart';
import 'package:cabinet_flutter_app/common/net/HttpManager.dart';
import 'package:cabinet_flutter_app/common/net/ResultData.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/utils/SoundUtils.dart';
import 'package:cabinet_flutter_app/page/data/widget/PackageNewItem.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppListState.dart';
import 'package:cabinet_flutter_app/widget/AppPullLoadWidget.dart';
import 'package:cabinet_flutter_app/widget/AppSearchAdressInputWidget.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/BottomBtnWidget.dart';
import 'package:cabinet_flutter_app/widget/NoResult.dart';
import 'package:cabinet_flutter_app/widget/keyborad/MyKeyEvent.dart';
import 'package:cabinet_flutter_app/widget/keyborad/MyKeyboard.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/PackageItemWidget.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class PackageSearchPage extends StatefulWidget {
  final String? value;
  final bool autoFocus;

  PackageSearchPage(this.value, this.autoFocus, {Key? key}) : super(key: key);

  @override
  _PackageSearchPage createState() => _PackageSearchPage();
}

class _PackageSearchPage extends State<PackageSearchPage>
    with
        AutomaticKeepAliveClientMixin<PackageSearchPage>,
        AppListState<PackageSearchPage>,
        WidgetsBindingObserver,
        SingleTickerProviderStateMixin {
  ScrollController _controller = new ScrollController();

  final GlobalKey<ScaffoldState> customerListPageKey = GlobalKey<ScaffoldState>();
  FocusNode searchFocusNode = FocusNode();
  String keyword = '';

  int total = 0;
  bool hasFeedBack = false;
  Timer? boxStatusTimer;
  bool isCheckBox = true;
  String? value;
  TextEditingController controller = TextEditingController();
  bool isCheckNull = true;
  bool isCourier = true;
  bool isCustomerKeyborad = false;
  bool showKeyboard = false;
  TextInputType textInputType = TextInputType.name;

  void initState() {
    super.initState();
    if (widget.value != null) {
      keyword = widget.value!;
      setState(() {
        controller.text = widget.value!;
      });
    }
    this.init();
  }

  callback(context) {
    if (widget.autoFocus) {
      if (isCustomerKeyborad) {
        showKeyBoard(TextInputType.number);
      }
      FocusScope.of(context).requestFocus(searchFocusNode);
    }
  }

  init() async {
    isCustomerKeyborad = await CheckUtils.isCustomerKeyboard();
    WidgetsBinding.instance.addPostFrameCallback((_) => callback(context));
    setState(() {});
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    isCourier = CheckUtils.isCourier(context);
  }

  // 上拉加载更多
  _getData() async {
    Map<String, dynamic> info = {'keyword': keyword, 'current': page.toString(), 'size': 10};
    ResultData res = await HttpManager.post(Courier.getCourierOrderSearch, info);
    if (res.result) {
      total = int.parse(res.data['total']);
      return new DataResult(res.data['records'], true, total: int.parse(res.data['total']));
    } else {
      Fluttertoast.showToast(msg: res.data);
    }
  }

  Future<Null> onLoadMore() async {
    if (isLoading) {
      return null;
    }
    isLoading = true;
    page++;
    var res = await requestLoadMore();
    if (res != null && res.result) {
      setState(() {
        pullLoadWidgetControl.dataList.addAll(res.data);
      });
    }
    resolveDataResult(res);
    isLoading = false;
    return null;
  }

  @protected
  Future<Null> handleRefresh() async {
    if (isLoading) {
      return null;
    }
    refreshIndicatorKey.currentState?.show();
    isLoading = true;
    page = 1;
    var res = await requestRefresh();
    resolveRefreshResult(res);
    resolveDataResult(res);
    if (res.next != null) {
      var resNext = await res.next;
      resolveRefreshResult(resNext);
      resolveDataResult(resNext);
    }
    isLoading = false;
    return null;
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  void deactivate() {
    super.deactivate();
  }

  onSearchChanged(String value) {
    keyword = value;
    setState(() {
      if (value == '') {
        isCheckNull = true;
      } else {
        isCheckNull = false;
      }
    });
  }

  onSearchSubmit(String value) {
    if (value.length >= 4) {
      isLoading = false;
      handleRefresh();
    } else {
      Fluttertoast.showToast(msg: '请输入至少四位关键字', gravity: ToastGravity.CENTER);
    }
  }

  _renderEventItem(int index) {
    PackageViewEntity package = PackageViewEntity.fromJson(dataList[index]);
    return PackageNewItem(ListType.SEARCH, indexNo: dataList.length - index, isCheck: false, package: package,
        bindTap: () {
      NavigatorUtils.goPackageDetailPage(context, package);
    }, bottom: package.hasOutbound == 0 ? buildBottom(package) : buildBottomOutBound(package));
    return PackageItemWidget(
      PackageType.SEARCH,
      indexNo: dataList.length - index,
      package: package,
      bindTap: () {
        NavigatorUtils.goPackageDetailPage(context, package);
      },
      bottom: package.hasOutbound == 0 ? buildBottom(package) : Container(),
    );
  }

  buildBottom(PackageViewEntity item) {
    return Container(
      width: double.infinity,
      child: Wrap(
        alignment: isCourier ? WrapAlignment.start : WrapAlignment.spaceBetween,
        children: [
          Offstage(
            offstage: false,
            child: OutlinedButton(
              child: Text(isCourier ? "取出退回" : "异常出库",
                  style: TextStyle(fontSize: AppConstant.smallTextSize, color: Colors.white)),
              onPressed: () {
                unusualOut(item);
              },
              style: ButtonStyle(
                  minimumSize: MaterialStateProperty.all(Size(50, 36)),
                  foregroundColor: MaterialStateProperty.resolveWith((states) {
                    return Colors.white;
                  }),
                  backgroundColor: MaterialStateProperty.resolveWith((states) {
                    return Theme.of(context).primaryColor;
                  })
                  // shape: MaterialStateProperty.all(StadiumBorder()),
                  // side: MaterialStateProperty.all(BorderSide(color: Theme.of(context).primaryColor)),
                  ),
            ),
          ),
          Padding(padding: EdgeInsets.only(left: 5)),
          Offstage(
            offstage: false,
            child: OutlinedButton(
              onPressed: () {
                /// 重投
                reDispatch(item);
              },
              child: Text(
                '一键重投',
                style: TextStyle(fontSize: 12),
              ),
              style: ButtonStyle(
                  minimumSize: MaterialStateProperty.all(Size(70, 36)),
                  foregroundColor: MaterialStateProperty.resolveWith((states) {
                    return Colors.white;
                  }),
                  backgroundColor: MaterialStateProperty.resolveWith((states) {
                    return Theme.of(context).primaryColor;
                  })),
            ),
          ),
          Padding(padding: EdgeInsets.only(left: 5)),
          Offstage(
            offstage: isCourier,
            child: OutlinedButton(
              child: Text(item.storeType == 1 ? "开门检查" : '出仓',
                  style: TextStyle(fontSize: AppConstant.smallTextSize, color: Colors.white)),
              onPressed: () {
                if (item.storeType == 1) {
                  openCabinetCheck(item);
                } else {
                  outBound(item);
                }
              },
              style: ButtonStyle(
                  minimumSize: MaterialStateProperty.all(Size(60, 36)),
                  foregroundColor: MaterialStateProperty.resolveWith((states) {
                    return Colors.white;
                  }),
                  backgroundColor: MaterialStateProperty.resolveWith((states) {
                    return Theme.of(context).primaryColor;
                  })),
            ),
          ),
          Padding(padding: EdgeInsets.only(left: 5)),
          Offstage(
            offstage: item.storeType == 2 || isCourier,
            child: OutlinedButton(
              child: Text("取出", style: TextStyle(fontSize: AppConstant.smallTextSize, color: Colors.white)),
              onPressed: () {
                pickOut(item);
              },
              style: ButtonStyle(
                minimumSize: MaterialStateProperty.all(Size(50, 36)),
                foregroundColor: MaterialStateProperty.resolveWith((states) {
                  return Colors.white;
                }),
                backgroundColor: MaterialStateProperty.resolveWith((states) {
                  return Theme.of(context).primaryColor;
                }),
              ),
            ),
          )
        ],
      ),
      margin: EdgeInsets.only(top: 7),
    );
  }

  buildBottomOutBound(PackageViewEntity item) {
    return Container(
      width: double.infinity,
      child: Wrap(
        alignment: isCourier ? WrapAlignment.start : WrapAlignment.spaceBetween,
        children: [
          Offstage(
            offstage: false,
            child: OutlinedButton(
              child: Text("拒收", style: TextStyle(fontSize: AppConstant.smallTextSize, color: Colors.white)),
              onPressed: () {
                orderBack(item);
              },
              style: ButtonStyle(
                  minimumSize: MaterialStateProperty.all(Size(50, 36)),
                  foregroundColor: MaterialStateProperty.resolveWith((states) {
                    return Colors.white;
                  }),
                  backgroundColor: MaterialStateProperty.resolveWith((states) {
                    return Theme.of(context).primaryColor;
                  })
                  // shape: MaterialStateProperty.all(StadiumBorder()),
                  // side: MaterialStateProperty.all(BorderSide(color: Theme.of(context).primaryColor)),
                  ),
            ),
          )
        ],
      ),
      margin: EdgeInsets.only(top: 7),
    );
  }

  /// 开门检查
  openCabinetCheck(PackageViewEntity item) {
    CommonUtils.customConfirm(context, '是否打开柜门检查包裹？', title: '开门检查', showClose: false, actions: <Widget>[
      Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
            },
            child: Text('取消', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
            style: ButtonStyle(foregroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.black;
            }), backgroundColor: MaterialStateProperty.resolveWith((states) {
              return DefaultConfig().configs.WHITE_COLOR;
            }))),
        TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              LoadingUtil(
                status: '正在开门...',
              ).show(context);
              DataResult res = await CourierDao.courierBoxOpenCheck(item.cabinetBoxId);
              LoadingUtil.dismiss(context);
              if (res.result) {
                showBoxOpen(context, item, (item) {});
                // boxStatusTimer = Timer.periodic(Duration(milliseconds: 2000), (timer) async {
                //   var result = await CabinetDao.cabinetBoxOpenStatus(item.cabinetBoxId!);
                //   if (result != null && result.result) {
                //     if (!result.data) {
                //       if (!isCheckBox) {
                //         return;
                //       }
                //       boxStatusTimer?.cancel();
                //       Navigator.of(context).pop();
                //     }
                //   }
                // });
              }
            },
            child: Text('是的', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
            style: ButtonStyle(foregroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.black;
            }), backgroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.white;
            }))),
      ])
    ]);
  }

  /// 格口打开
  showBoxOpen(BuildContext context, PackageViewEntity item, onPressed(item)) {
    return showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
        topLeft: Radius.circular(10.0),
        topRight: Radius.circular(10.0),
      )),
      builder: (BuildContext _context) {
        return StatefulBuilder(
          builder: (_context, state) {
            return Stack(
              children: [
                Container(
                  height: 30.0,
                  width: double.infinity,
                  color: Colors.black54,
                ),
                Container(
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      )),
                ),
                Container(
                  height: 550,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            LocalImageUtil.getImageAsset('boxOpen', width: MediaQuery.of(context).size.width * 0.28),
                            Padding(padding: EdgeInsets.only(bottom: 10)),
                            RichText(
                                text: TextSpan(children: <TextSpan>[
                              TextSpan(
                                text: '${item.cabinetName ?? ''}-',
                                style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 16),
                              ),
                              TextSpan(
                                text: '${item.cabinetBoxLabel}号',
                                style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 16),
                              ),
                              TextSpan(
                                text: ' 格口已开 ',
                                style: TextStyle(color: Colors.black, fontSize: 14),
                              ),
                            ])),
                            Padding(padding: EdgeInsets.only(bottom: 10)),
                            Offstage(
                              offstage: hasFeedBack,
                              child: Text('放入包裹后请关门', style: TextStyle(color: Color(0xFF999999), fontSize: 14)),
                            )
                          ],
                        ),
                      ),
                      Offstage(
                          offstage: hasFeedBack,
                          child: BottomBtnWidget(
                              showShadow: false,
                              title: '我已关门',
                              action: () {
                                closeCabinetBox(item.cabinetBoxId!);
                              }))
                    ],
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  closeCabinetBox(String cabinetBoxId) async {
    LoadingUtil(
      status: '关门请求中...',
    ).show(context);
    var res = await CabinetDao.cabinetBoxCloseDoor(cabinetBoxId);
    LoadingUtil.dismiss(context);
    if (res.result) {
      isCheckBox = false;
      boxStatusTimer?.cancel();
      Navigator.pop(context);
    }
  }

  /// 重新通知
  reNotice(PackageViewEntity item) {
    WaybillDao.reNotice(context, item.receiverMobile, item.id);
  }

  /// 取出
  pickOut(PackageViewEntity item) {
    CommonUtils.customConfirm(context, '是否打开柜门取出包裹？', title: '开门提示', showClose: false, actions: <Widget>[
      Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
            },
            child: Text('取消', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
            style: ButtonStyle(foregroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.black;
            }), backgroundColor: MaterialStateProperty.resolveWith((states) {
              return DefaultConfig().configs.WHITE_COLOR;
            }))),
        TextButton(
            onPressed: () async {
              var res = await CourierDao.courierOutboundWaybill(item.cabinetLocationCode, item.id);
              if (res.result) {
                Navigator.of(context).pop();
                handleRefresh();
                Fluttertoast.showToast(msg: '取出成功');
              }
            },
            child: Text('是的', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
            style: ButtonStyle(foregroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.black;
            }), backgroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.white;
            }))),
      ])
    ]);
  }

  /// 一键重投
  reDispatch(PackageViewEntity item) async {
    bool result = await CommonUtils.confirm(context, '重投会自动重置计费，不做任何物流轨迹变更', title: '确认重投');
    if (result) {
      DataResult res = await WaybillDao.reCalculateFee(item.cabinetLocationCode, item.id);
      if (res.result) {
        handleRefresh();
        Fluttertoast.showToast(msg: '重投成功');
      }
    }
  }

  /// 异常出库
  unusualOut(PackageViewEntity item) async {
    if (isCourier) {
      String? res = await NavigatorUtils.goCabinetBindPage(context, scanAction: ScanAction.SCANQR);
      if (res != null) {
        if (res.contains(item.cabinetLocationCode)) {
          unusualOut_(item);
        } else {
          Fluttertoast.showToast(msg: '请扫描正确的柜机二维码');
        }
      }
    } else {
      unusualOut_(item);
    }
  }

  orderBack(item) {
    CommonUtils.customConfirm(context, '是否进行拒收操作？', title: '拒收提醒', showClose: false, actions: <Widget>[
      Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
            },
            child: Text('取消', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
            style: ButtonStyle(foregroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.black;
            }), backgroundColor: MaterialStateProperty.resolveWith((states) {
              return DefaultConfig().configs.WHITE_COLOR;
            }))),
        TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              LoadingUtil(
                status: '正在拒收...',
              ).show(context);
              var res = await CourierDao.resendOrderBack(item.id);
              LoadingUtil.dismiss(context);
              if (res != null && res.result) {
                Fluttertoast.showToast(msg: '拒收成功');
                handleRefresh();
              } else {
                SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
              }
            },
            child: Text('是的', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
            style: ButtonStyle(foregroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.black;
            }), backgroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.white;
            }))),
      ])
    ]);
  }

  unusualOut_(item) {
    CommonUtils.customConfirmByReason(context, '是否进行异常出库操作？', (backReason) async {
      print(backReason);
      var cabinetLocationCode = item.storeType == 1 ? item.cabinetLocationCode : '0';
      var res = await CourierDao.courierUnusualOutWaybill(cabinetLocationCode, item.id, backReason);
      if (res.result) {
        Navigator.of(context).pop();
        handleRefresh();
        Fluttertoast.showToast(msg: '出库成功');
      }
    }, title: '出库提示', changeText: 'unusualOut', showClose: false, showInput: true);
  }

  /// 出仓
  outBound(PackageViewEntity item) {
    CommonUtils.customConfirm(context, '是否进行出仓操作？', title: '出仓提醒', showClose: false, actions: <Widget>[
      Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
            },
            child: Text('取消', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
            style: ButtonStyle(foregroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.black;
            }), backgroundColor: MaterialStateProperty.resolveWith((states) {
              return DefaultConfig().configs.WHITE_COLOR;
            }))),
        TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              LoadingUtil(
                status: '正在出仓...',
              ).show(context);
              var res = await CourierDao.courierShopOutDoor(item.waybillNo);
              LoadingUtil.dismiss(context);
              if (res != null && res.result) {
                Fluttertoast.showToast(msg: '出仓成功');
                handleRefresh();
              } else {
                SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
              }
            },
            child: Text('是的', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
            style: ButtonStyle(foregroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.black;
            }), backgroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.white;
            }))),
      ])
    ]);
  }

  showKeyBoard(TextInputType type) {
    textInputType = type;
    showKeyboard = true;
    setState(() {});
  }

  getInputAction() {
    TextInputAction type = TextInputAction.done;
    if (controller.text.length == 4 ||
        controller.text.length == 6 ||
        controller.text.length == 8 ||
        controller.text.length == 11) {
      type = TextInputAction.search;
    }
    return type;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      backgroundColor: Colors.grey.shade100,
      key: customerListPageKey,
      appBar: AppCustomerBar(
        title: AppbarTitle(
          title: '包裹查询',
          isCenter: true,
        ),
        actions: <Widget>[Container(width: 60)],
      ),
      body: Column(
        children: <Widget>[
          Stack(
            children: [
              isCustomerKeyborad
                  ? GestureDetector(
                      onTap: () {
                        showKeyBoard(TextInputType.number);
                        FocusScope.of(context).requestFocus(searchFocusNode);
                      },
                      child: RawKeyboardListener(
                          focusNode: FocusNode(),
                          onKey: (RawKeyEvent event) {},
                          child: AbsorbPointer(
                            child: Container(
                              padding: EdgeInsets.fromLTRB(15.0, 0.0, 55.0, 10.0),
                              decoration: BoxDecoration(color: DefaultConfig().configs.WHITE_COLOR),
                              child: AppSearchAdressInputWidget(
                                  readOnly: true,
                                  prefixIcon: Container(
                                      width: 48,
                                      height: 48,
                                      padding: EdgeInsets.all(12),
                                      child: LocalImageUtil.getImageAsset('searchGrey')),
                                  borderRadius: 20,
                                  focusNode: searchFocusNode,
                                  bgColor: DefaultConfig().configs.BG_COLOR,
                                  controller: controller,
                                  keyboardType: Platform.isAndroid ? TextInputType.number : TextInputType.text,
                                  textInputAction: TextInputAction.search,
                                  onChanged: onSearchChanged,
                                  onSubmitted: onSearchSubmit,
                                  hitText: "请输入单号后5位/手机号/取件码进行搜索"),
                            ),
                          )),
                    )
                  : Container(
                      padding: EdgeInsets.fromLTRB(15.0, 0.0, 55.0, 10.0),
                      decoration: BoxDecoration(color: DefaultConfig().configs.WHITE_COLOR),
                      child: AppSearchAdressInputWidget(
                          prefixIcon: Container(
                              width: 48,
                              height: 48,
                              padding: EdgeInsets.all(12),
                              child: LocalImageUtil.getImageAsset('searchGrey')),
                          borderRadius: 20,
                          focusNode: searchFocusNode,
                          bgColor: DefaultConfig().configs.BG_COLOR,
                          controller: controller,
                          keyboardType: Platform.isAndroid ? TextInputType.number : TextInputType.text,
                          textInputAction: TextInputAction.search,
                          onChanged: onSearchChanged,
                          onSubmitted: onSearchSubmit,
                          hitText: "请输入单号/手机号/取件码进行搜索"),
                    ),
              Positioned(
                  right: 65,
                  top: 10,
                  child: Offstage(
                    offstage: isCheckNull,
                    child: InkWell(
                      child: Icon(
                        Icons.clear,
                        size: 20,
                      ),
                      onTap: () {
                        setState(() {
                          keyword = '';
                          controller.value = TextEditingValue(text: '');
                          isCheckNull = true;
                        });
                      },
                    ),
                  )),
              Positioned(
                right: 15,
                top: 8,
                child: InkWell(
                  child: Text(
                    '搜索',
                    style: TextStyle(color: Theme.of(context).primaryColor),
                  ),
                  onTap: () {
                    onSearchSubmit(controller.text);
                  },
                ),
              ),
            ],
          ),
          Padding(padding: EdgeInsets.only(top: 10)),
          Expanded(
              child: Column(
            children: [
              dataList.length > 0
                  ? Expanded(
                      child: AppPullLoadWidget(
                      pullLoadWidgetControl,
                      (BuildContext context, int index) => _renderEventItem(index),
                      handleRefresh,
                      onLoadMore,
                      refreshKey: refreshIndicatorKey,
                    ))
                  : RefreshIndicator(
                      key: refreshIndicatorKey,
                      child: SingleChildScrollView(
                        physics: AlwaysScrollableScrollPhysics(),
                        child: Container(
                          height: 300,
                          child: NoResult(
                              size: 64,
                              subWidget: Container(
                                  padding: EdgeInsets.only(top: 10),
                                  child: Text('（支持手机号后四位和单号后五位搜索）', style: AppConstant.smallSubText))),
                        ),
                      ),
                      onRefresh: handleRefresh),
              MyKeyboard(
                showKeyboard,
                onKeyDown,
                textInputType: textInputType,
                textInputAction: getInputAction(),
              )
            ],
          ))
        ],
      ),
    );
  }

  onKeyDown(MyKeyEvent data) {
    bool mobileFocus = searchFocusNode.hasFocus;
    if (mobileFocus) {
      handleEvent(data, controller, 11, isScanMobile: true, checkMobileLast4: true);
    }
  }

  handleEvent(MyKeyEvent data, TextEditingController controller, int length,
      {bool isScanMobile = false, bool checkMobileLast4 = false}) {
    if (data.isChangeText()) {
      textInputType = TextInputType.name;
    } else if (data.isChangeNumber()) {
      textInputType = TextInputType.number;
    } else if (data.isHide()) {
      showKeyboard = false;
    } else if (data.isDone()) {
      showKeyboard = false;
      TextInputAction action = getInputAction();
      if (action == TextInputAction.search) {
        onSearchSubmit(controller.text);
        searchFocusNode.unfocus();
      }
    } else if (data.isDelete()) {
      if (controller.text.isNotEmpty) {
        String value = controller.text.substring(0, controller.text.length - 1);
        controller.value = TextEditingValue(text: value);
        controller.selection = TextSelection.fromPosition(TextPosition(offset: controller.text.length));
      }
    } else if (data.isClear()) {
      controller.value = const TextEditingValue(text: '');
    } else {
      String value = controller.text;
      if (value.length < length) {
        value += data.key;
        controller.value = TextEditingValue(text: value);
        controller.selection = TextSelection.fromPosition(TextPosition(offset: controller.text.length));
      }
    }
    onSearchChanged(controller.text);
    setState(() {});
  }

  // TODO: implement wantKeepAlive
  @override
  bool get wantKeepAlive => true;

  // TODO: implement isRefreshFirst
  @override
  bool get isRefreshFirst => true;

  @override
  requestLoadMore() async {
    return await _getData();
  }

  @override
  requestRefresh() async {
    if (keyword.length >= 4) {
      return await _getData();
    }
    return new DataResult([], true, total: 0);
  }
}
