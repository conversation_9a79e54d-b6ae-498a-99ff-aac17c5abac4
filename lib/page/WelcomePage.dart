import 'dart:async';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:scan/scan.dart';

class WelcomePage extends StatefulWidget {
  static final String sName = "/";

  @override
  _WelcomePageState createState() => _WelcomePageState();
}

class _WelcomePageState extends State<WelcomePage> {
  Scan scan = Scan();
  bool hadInit = false;
  String? loginName = "";
  String? password = "";

  autoLogin(store) async {
    loginName = await LocalStorage.get<String>(DefaultConfig().configs.USER_NAME_KEY);
    password = await LocalStorage.get(DefaultConfig().configs.PW_KEY, isPrivate: true);
    var isLogout = await LocalStorage.get(DefaultConfig().configs.IS_LOGOUT);
    int? userType = await LocalStorage.get(DefaultConfig().configs.USER_TYPE, isPrivate: true);
    if (loginName != null && password != null && isLogout != true) {
      UserDao.autoLogin(loginName, password, userType, store, context, checkTime: false);
    }
    NavigatorUtils.goLoginNew(context);
  }

  toNext(res, store) async {
    if (res != null && res.result) {
      NavigatorUtils.goHome(context);
    } else {
      // autoLogin(store);
      NavigatorUtils.goLoginNew(context);
    }
    return true;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (hadInit) {
      return;
    }
    hadInit = true;

    ///防止多次进入
    CommonUtils.initStatusBarHeight(context);
    Future.delayed(const Duration(seconds: 2), () {
      Store<AppState> store = StoreProvider.of(context);
      UserDao.initUserInfo(store).then((res) {
        toNext(res, store);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return StoreBuilder<AppState>(
      builder: (context, store) {
        return Container(
          child: Image.asset(
            LocalImageUtil.getImagePath('splash', isChannel: true),
            fit: BoxFit.fill,
          ),
        );
      },
    );
  }
}
