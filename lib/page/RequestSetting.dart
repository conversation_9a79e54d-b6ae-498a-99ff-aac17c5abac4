import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/ThrottleUtil.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/InputWidget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class RequestSetting extends StatefulWidget {
  RequestSetting();

  @override
  State createState() {
    return new _RequestSettingState();
  }
}

class _RequestSettingState extends State<RequestSetting> {
  final TextEditingController urlController = new TextEditingController();
  Map apiUrl = {'isOpen': false, 'url': DefaultConfig().configs.DEV_URL, 'isProdSecret': true};

  _RequestSettingState() : super();
  ThrottleUtil throttleUtil = ThrottleUtil();

  @override
  void initState() {
    super.initState();
    initParams();
  }

  getChannelApiUrl() {
    Map map = apiUrl;
    map['url'] = DefaultConfig().configs.DEV_URL;
    return map;
  }

  initParams() async {
    var _apiUrl = await LocalStorage.getJson('apiUrl');
    if (_apiUrl == null) {
      _apiUrl = getChannelApiUrl();
    } else {
      if (_apiUrl['isProdSecret'] == null) {
        _apiUrl['isProdSecret'] = true;
      }
    }
    urlController.value = new TextEditingValue(text: _apiUrl['url']);
    setState(() {
      apiUrl = _apiUrl;
    });
  }

  save() async {
    bool res = await CommonUtils.confirm(context, '确认保存?');
    if (res) {
      await LocalStorage.save(DefaultConfig().configs.USER_LIST, []);
      await LocalStorage.save('apiUrl', apiUrl);
      String msg = '请求地址保存成功, ' + (apiUrl['isOpen'] ? '已开启' : '未开启');
      Fluttertoast.showToast(msg: msg);
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return new Scaffold(
        backgroundColor: DefaultConfig().configs.BG_COLOR,
        appBar: new AppCustomerBar(
          title: new AppbarTitle(title: "请求设置", isCenter: true),
          actions: [Container(width: 60)],
        ),
        body: new SingleChildScrollView(
          child: new Column(
            children: <Widget>[
              new Container(
                decoration: BoxDecoration(color: Colors.white),
                child: new Column(
                  children: <Widget>[
                    new Padding(padding: EdgeInsets.only(top: 15.0)),
                    new Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: <Widget>[
                        new Padding(padding: EdgeInsets.only(left: 10.0)),
                        new InputWidget(
                          label: '请求地址',
                          width: 70.0,
                          height: 40.0,
                          maxLength: 60,
                          hintText: '请输入请求地址',
                          contentPadding: EdgeInsets.fromLTRB(5.0, 5.0, 5.0, 13.0),
                          color: Colors.grey.shade100,
                          controller: urlController,
                          onSaved: (String? value) {
                            apiUrl['url'] = value;
                          },
                        ),
                        new Padding(padding: EdgeInsets.only(left: 10.0)),
                      ],
                    ),
                    new Padding(padding: EdgeInsets.only(top: 10.0)),
                    new Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: <Widget>[
                        new Padding(padding: EdgeInsets.only(left: 10.0)),
                        new Container(
                          width: 60.0,
                          height: 40.0,
                          child: new Align(
                            alignment: Alignment.centerLeft,
                            child: new Text(
                              '是否开启',
                              style: new TextStyle(fontSize: 14.0),
                            ),
                          ),
                        ),
                        new Padding(padding: EdgeInsets.only(left: 10.0)),
                        Transform.scale(
                          scale: 0.7,
                          child: CupertinoSwitch(
                              value: apiUrl['isOpen'],
                              activeColor: Theme.of(context).primaryColor,
                              onChanged: (bool isCheck) {
                                apiUrl['isOpen'] = isCheck;
                                apiUrl['isProdSecret'] = !isCheck;
                                setState(() {});
                              }),
                        ),
                        new Padding(padding: EdgeInsets.only(left: 10.0)),
                      ],
                    ),
                    new Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: <Widget>[
                        new Padding(padding: EdgeInsets.only(left: 10.0)),
                        new Container(
                          width: 60.0,
                          height: 40.0,
                          child: new Align(
                            alignment: Alignment.centerLeft,
                            child: new Text(
                              '生产秘钥',
                              style: new TextStyle(fontSize: 14.0),
                            ),
                          ),
                        ),
                        new Padding(padding: EdgeInsets.only(left: 10.0)),
                        Transform.scale(
                          scale: 0.7,
                          child: CupertinoSwitch(
                              value: apiUrl['isProdSecret'],
                              activeColor: Theme.of(context).primaryColor,
                              onChanged: (bool isCheck) {
                                setState(() {
                                  apiUrl['isProdSecret'] = isCheck;
                                });
                              }),
                        ),
                        new Padding(padding: EdgeInsets.only(left: 10.0)),
                      ],
                    ),
                    new Padding(padding: EdgeInsets.only(top: 15.0))
                  ],
                ),
              ),
              new Padding(padding: EdgeInsets.only(top: 10.0)),
              Container(
                width: double.infinity,
                margin: EdgeInsets.only(left: 20, top: 20, right: 20),
                height: 47,
                child: MaterialButton(
                  color: Theme.of(context).primaryColor,
                  textColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25.0),
                  ),
                  onPressed: () => throttleUtil.throttle(() {
                    save();
                  }),
                  child: Text('保存', style: TextStyle(fontSize: 18)),
                ),
              ),
            ],
          ),
        ));
  }
}
