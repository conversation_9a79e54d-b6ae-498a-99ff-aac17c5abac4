import 'dart:async';

import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_shop_entity.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/redux/CurrentTabIndexRedux.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/page/data/DataCenterPage.dart';
import 'package:cabinet_flutter_app/page/index/IndexPage.dart';
import 'package:cabinet_flutter_app/page/my/MyPage.dart';
import 'package:cabinet_flutter_app/page/notify/NotifySummaryPage.dart';
import 'package:cabinet_flutter_app/page/scan/CabinetBindPage.dart';
import 'package:cabinet_flutter_app/widget/AppTabBarWidget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:scan/scan.dart';

// ignore: must_be_immutable
class HomePage extends StatelessWidget {
  static final String sName = "home";
  static final Scan scanPlugin = Scan();
  int backTimes = 0;
  Timer? _timer;
  List<CabinetShopEntity?> cabinetList = [];

  /// 提示退出
  exitApp(BuildContext context) {
    if (backTimes < 2) {
      backTimes++;
    }
    if (backTimes == 2) {
      return true;
    } else {
      Fluttertoast.showToast(msg: '请再按一次退出${DefaultConfig().configs.APP_NAME}');
      _timer = Timer.periodic(Duration(milliseconds: 2000), (t) {
        backTimes = 0;
        t.cancel();
      });
    }
    return false;
  }

  _renderTab({String? icon, String text = ''}) {
    return new Tab(
        child: Container(
      // width: MediaQuery.of(context).size.width / 5,
      child: new Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          CheckUtils.isNotNull(text)
              ? LocalImageUtil.getImageAsset(icon, isChannel: true, width: CheckUtils.isNotNull(text) ? 20 : 42.0)
              : Container(),
          CheckUtils.isNotNull(text)
              ? new Text(
                  text,
                  style: new TextStyle(fontSize: 12.0),
                )
              : Container()
        ],
      ),
    ));
  }

  @override
  Widget build(BuildContext context) {
    return new StoreBuilder<AppState>(builder: (context, store) {
      List<Widget> tabs = [
        _renderTab(icon: store.state.currentTabIndex == 0 ? 'homeActive' : 'home', text: '首页'),
        _renderTab(icon: store.state.currentTabIndex == 1 ? 'dataActive' : 'data', text: '数据'),
        Text(''),
        _renderTab(icon: store.state.currentTabIndex == 3 ? 'notifyActive' : 'notify', text: '消息'),
        _renderTab(icon: store.state.currentTabIndex == 4 ? 'myActive' : 'my', text: "我的"),
      ];
      return WillPopScope(
        onWillPop: () async {
          EasyLoading.dismiss();
          return exitApp(context);
        },
        child: new AppTabBarWidget(
          type: AppTabBarWidget.BOTTOM_TAB,
          tabItems: tabs,
          tabViews: [
            new IndexPage(key: indexGlobalKey),
            new DataCenterPage(key: dataCenterGlobalKey),
            new CabinetBindPage(scanAction: ScanAction.BINDCABINET),
            new NotifySummaryPage(),
            new MyPage(key: mineGlobalKey),
          ],
          floatingActionButton: FloatingActionButton(
            onPressed: () async {
              bool hasReal = await CheckUtils.checkUserHasReal();
              if (hasReal) {
                if (CheckUtils.isCourier(context)) {
                  indexGlobalKey.currentState?.cancelScanSubscription();
                  await NavigatorUtils.goCabinetBindPage(context);
                  indexGlobalKey.currentState?.needRefresh();
                  indexGlobalKey.currentState?.initScan();
                } else {
                  _getShopData(context);
                }
              }
            },
            backgroundColor: Colors.white,
            child: LocalImageUtil.getImageAsset('scanIndex', isChannel: true, width: 42),
          ),
          onPageChanged: (index) {
            switch (index) {
              case 0:
                indexGlobalKey.currentState?.needRefresh();
                break;
              case 1:
                dataCenterGlobalKey.currentState?.initTab();
                break;
              case 4:
                mineGlobalKey.currentState?.onFresh();
            }
            store.dispatch(new UpdateCurrentTabIndexAction(index));
          },
          backgroundColor: AppColors.primarySwatch,
          indicatorColor: Color(AppColors.white),
        ),
      );
    });
  }

  _getShopData(context) async {
    LoadingUtil(
      status: '数据请求中...',
    ).show(context);
    var res = await CourierDao.shopCabinetLocation();
    LoadingUtil.dismiss(context);
    if (res != null && res.result) {
      cabinetList = jsonConvert.convertList<CabinetShopEntity>(res.data)!;
      if (cabinetList.length > 1) {
        selectShopCabinet(context);
      } else if (cabinetList.length == 1) {
        NavigatorUtils.goDeliveryPage(context, cabinetList[0]!.code);
      } else {
        Fluttertoast.showToast(msg: '无可用点位');
      }
    }
  }

  selectShopCabinet(context) async{
    CabinetShopEntity? item = await CommonUtils.showBottomShopSelectModal(context, cabinetList);
    if(item != null) {
      NavigatorUtils.goDeliveryPage(context, item.code);
    }
  }
}
