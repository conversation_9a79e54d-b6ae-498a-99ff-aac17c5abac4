
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/BottomBtnWidget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:redux/redux.dart';

class BrandBindPage extends StatefulWidget {
  BrandBindPage({Key? key}) : super(key: key);

  @override
  _BrandBindPageState createState() => _BrandBindPageState();
}

class _BrandBindPageState extends State<BrandBindPage> {
  List<dynamic> companyList = [];

  @override
  void initState() {
    super.initState();
    getUserInfo();
    getCompanyList();
  }

  getUserInfo() async {
    var res = await UserDao.getUserInfoLocal();
    UserEntity user;
    if (res != null && res.result) {
      user = res.data;
    }
    setState(() {});
  }

  getCompanyList() async {
    var res = await UserDao.getBrandList();
    companyList = [];
    if (res != null && res.result) {
      Map<dynamic, dynamic> data = res.data;
      if (data.length > 0) {
        data.forEach((key, value) {
          Map<String, dynamic> info = {'id': key, 'name': value, 'brandCode': key, 'allowIn': 1, 'syncTrace': 1};
          if (key != 'UNKNOW') {
            if (['SF', 'JD'].indexOf(key) > -1) {
              info['syncTrace'] = 0;
            }
            print('${info}');
            companyList.add(info);
          }
        });
      }
    }
    setState(() {});
  }

  @override
  void dispose() {
    super.dispose();
  }

  selectCompany(item) {
    int index = companyList.indexWhere((comp) => comp['brandCode'] == item['brandCode']);
    bool isJDSF = ['JD', 'SF'].indexOf(item['brandCode']) > -1;
    if (item['allowIn'] == 0) {
      companyList[index]['allowIn'] = 1;
      companyList[index]['syncTrace'] = isJDSF ? 0 : 1;
    } else {
      companyList[index]['allowIn'] = 0;
      companyList[index]['syncTrace'] = 0;
    }
    setState(() {
      companyList = companyList;
    });
  }

  buildCompanyList() {
    List<Widget> widgets = <Widget>[];
    int rowNumber = 2;
    double width = (MediaQuery.of(context).size.width - 40) / rowNumber;
    companyList.forEach((item) {
      widgets.add(new InkWell(
        onTap: () {
          selectCompany(item);
        },
        child: new Container(
          width: width,
          padding: EdgeInsets.fromLTRB(0, 10.0, 0, 10.0),
          child: new Row(
            children: <Widget>[
              new Icon(
                item['allowIn'] == 1 ? Icons.check_box : Icons.check_box_outline_blank,
                size: 20.0,
                color: item['allowIn'] == 1 ? Theme.of(context).primaryColor : Colors.grey.shade300,
              ),
              Padding(padding: EdgeInsets.only(left: 10)),
              new Text(
                item['name'],
                style: new TextStyle(fontSize: 16.0, color: Colors.black54),
              )
            ],
          ),
        ),
      ));
    });

    /// 解决Wrap WrapAlignment.start无效问题
    for (int i = 0; i < rowNumber; i++) {
      widgets.add(Container(width: width));
    }
    int num = widgets.length - widgets.length % rowNumber - 1;
    widgets.sublist(0, num);
    return widgets;
  }

  bindCompany() async {
    List brandList = [];
    companyList.forEach((item) {
      brandList.add({'brandCode': item['brandCode'], 'allowIn': item['allowIn'], 'syncTrace': item['syncTrace']});
    });
    Map<String, dynamic> info = {'brands': brandList};
    LoadingUtil(
      status: '数据加载中...',
    ).show(context);
    var res = await UserDao.setBrand(info);
    LoadingUtil.dismiss(context);
    if (res != null && res.result) {
      if (res.data) {
        Fluttertoast.showToast(msg: '绑定快递公司成功');
        Store<AppState> store = StoreProvider.of(context);
        UserDao.getUserInfo(store);
        NavigatorUtils.goHome(context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: DefaultConfig().configs.WHITE_COLOR,
        appBar: new AppCustomerBar(
          title: AppbarTitle(
            title: '选择快递公司',
            isCenter: true,
          ),
          actions: [
            Container(
              width: 60,
            )
          ],
        ),
        body: Column(
          children: [
            Container(
                margin: EdgeInsets.fromLTRB(20, 20, 20, 20),
                child: Row(
                  children: [
                    Text('请选择您要入柜的快递品牌（',
                        style: TextStyle(color: Color(0xFF101010), fontSize: 18, fontWeight: FontWeight.w500)),
                    Text('可多选', style: TextStyle(color: Colors.red, fontSize: 18, fontWeight: FontWeight.w500)),
                    Text('）', style: TextStyle(color: Color(0xFF101010), fontSize: 18, fontWeight: FontWeight.w500))
                  ],
                )),
            Expanded(
                child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Container(
                    margin: EdgeInsets.fromLTRB(20, 0, 20, 20),
                    width: MediaQuery.of(context).size.width,
                    padding: EdgeInsets.only(bottom: 20.0),
                    decoration: new BoxDecoration(color: Colors.white),
                    child: Wrap(
                      children: buildCompanyList(),
                    ),
                  )
                ],
              ),
            )),
            Container(
                width: MediaQuery.of(context).size.width,
                height: 121.0,
                decoration: new BoxDecoration(color: Colors.white, boxShadow: [
                  BoxShadow(color: Colors.grey[400]!, offset: Offset(3, 3), blurRadius: 1),
                  BoxShadow(color: Colors.grey[300]!, offset: Offset(-2, -2), blurRadius: 0),
                ]),
                child: Column(
                  children: [
                    Container(
                      padding: EdgeInsets.fromLTRB(20, 2, 20, 2),
                      child: Text("注：后面如需新增品牌也可在：我的-快递品牌管理处开启对应品牌即可", style: TextStyle(fontSize: 14)),
                    ),
                    BottomBtnWidget(
                      showShadow: false,
                      shape: 'rectangle',
                      type: ButtonType.primary,
                      title: '进入首页',
                      action: () async {
                        bindCompany();
                      },
                    ),
                  ],
                ))
          ],
        ));
  }
}
