
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/team_manger_entity.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/page/my/TeamAddPage.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/NoResult.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class TeamMangerPage extends StatefulWidget {
  const TeamMangerPage({Key? key}) : super(key: key);

  @override
  _TeamMangerPageState createState() => _TeamMangerPageState();
}

class _TeamMangerPageState extends State<TeamMangerPage> {
  String _userName = '';
  int? hasAdmin;
  List<TeamMangerEntity> list = [];

  @override
  void initState() {
    super.initState();
    init();
  }

  @override
  void dispose() {
    super.dispose();
  }

  List<String> getDataList() {
    List<String> list = [];
    for (int i = 0; i < 2; i++) {
      list.add(i.toString());
    }
    return list;
  }

  init() async {
    _userName = await LocalStorage.get<String>(DefaultConfig().configs.USER_NAME_KEY);
    hasAdmin = await LocalStorage.get(DefaultConfig().configs.HAS_ADMIN, isPrivate: true);
    this.handleRefresh();
  }

  getData() async {
    LoadingUtil(
      status: '数据加载中...',
    ).show(context);
    Map<String, dynamic> info = {};
    var res = await UserDao.myTeamList(info);
    list.clear();
    LoadingUtil.dismiss(context);
    if (res != null && res.result) {
      res.data.forEach((item) {
        list.add(TeamMangerEntity.fromJson(item));
      });
    }
    setState(() {});
  }

  myTeamDelete(id) async {
    LoadingUtil(
      status: '数据加载中...',
    ).show(context);
    Map<String, dynamic> info = {'id': id};
    var res = await UserDao.myTeamDelete(info);
    LoadingUtil.dismiss(context);
    if (res != null && res.result) {
      this.handleRefresh();
    }
  }

  Future<Null> handleRefresh() async {
    new Future.delayed(const Duration(microseconds: 200), () {
      this.getData();
    });
  }

  getItemContainer() {
    List<Widget> widgets = <Widget>[];
    list.forEach((item) {
      widgets.add(InkWell(
          onTap: () {},
          child: Container(
              alignment: Alignment.center,
              height: 97,
              margin: EdgeInsets.only(left: 15, right: 15, top: 15),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(5)),
                  border: Border.all(width: 0.5, color: Color(0xFFB5B5B5))),
              child: Column(
                children: [
                  Container(
                      child: Container(
                    margin: EdgeInsets.only(left: 15, top: 15, right: 15, bottom: 15),
                    child: Row(
                      children: [
                        Text(
                          "${item.nickname ?? ''}" + " " + item.phone!,
                          style: TextStyle(color: Colors.black, fontSize: 15),
                        ),
                        Expanded(
                            child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [Text(item.hasAdmin == 1 ? '管理员' : '成员')],
                        ))
                      ],
                    ),
                  )),
                  Offstage(
                    offstage: hasAdmin == 1
                        ? item.hasAdmin == 1
                            ? true
                            : false
                        : true,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        InkWell(
                            onTap: () {
                              myTeamDelete(item.id);
                            },
                            child: Container(
                              width: 88,
                              height: 28,
                              margin: EdgeInsets.only(right: 15, bottom: 5),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.all(Radius.circular(20)),
                                  border: Border.all(width: 1, color: Color(0xFFB5B5B5))),
                              alignment: Alignment.center,
                              child: Text(
                                '解除',
                                style: TextStyle(fontSize: 15),
                              ),
                            ))
                      ],
                    ),
                  )
                ],
              ))));
    });
    return widgets;
  }

  @override
  Widget build(BuildContext context) {
    return Material(
        child: Scaffold(
            backgroundColor: Color(0xFFF5F5F5),
            appBar: AppCustomerBar(
              title: AppbarTitle(
                title: '团队管理',
                isCenter: true,
              ),
              actions: <Widget>[Container(width: 60)],
            ),
            body: RefreshIndicator(
                onRefresh: handleRefresh,
                child: Column(
                  children: [
                    Container(
                        color: Colors.white,
                        margin: EdgeInsets.only(top: 20),
                        child: Container(
                          margin: EdgeInsets.only(left: 15, top: 15, right: 15, bottom: 15),
                          child: Row(
                            children: [
                              Text(
                                '当前登录账号：' + _userName,
                                style: TextStyle(color: Colors.black),
                              ),
                              Expanded(
                                  child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [Text(hasAdmin == 1 ? '管理员' : '成员')],
                              ))
                            ],
                          ),
                        )),
                    Container(
                      margin: EdgeInsets.only(left: 15, right: 15),
                      height: 1,
                    ),
                    Container(
                      padding: EdgeInsets.only(left: 15, top: 15, right: 15, bottom: 15),
                      color: Colors.white,
                      child: Row(
                        children: [
                          Text('当前成员'),
                          Expanded(
                              child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [Text(list.length.toString())],
                          ))
                        ],
                      ),
                    ),
                    Expanded(
                        child: SingleChildScrollView(
                      physics: AlwaysScrollableScrollPhysics(),
                      child: list.length > 0
                          ? Container(
                              color: Colors.white,
                              width: MediaQuery.of(context).size.width,
                              padding: EdgeInsets.fromLTRB(0, 0, 0, 20),
                              child: Column(
                                children: getItemContainer(),
                              ),
                            )
                          : Container(
                              height: 300,
                              child: NoResult(
                                  size: 64,
                                  type: 'box',
                                  subWidget: Container(
                                      padding: EdgeInsets.only(top: 10),
                                      child: Text('', style: AppConstant.smallSubText))),
                            ),
                    )),
                    Offstage(
                      offstage: hasAdmin == 0,
                      child: Container(
                        width: double.infinity,
                        color: Colors.white,
                        child: Container(
                          margin: EdgeInsets.only(left: 15, right: 15, top: 10, bottom: 15),
                          height: 37,
                          child: MaterialButton(
                            color: Theme.of(context).primaryColor,
                            textColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20.0),
                            ),
                            onPressed: () async {
                              Navigator.push(
                                      context,
                                      new CupertinoPageRoute(
                                          builder: (context) => new TeamAddPage(),
                                          settings: RouteSettings(name: 'teamMangerPage')))
                                  .then((value) => {getData()});
                              // NavigatorUtils.goTeamAddPage(context);
                            },
                            child: Text("新增成员"),
                          ),
                        ),
                      ),
                    ),
                  ],
                ))));
  }
}
