import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/redux/UserRedux.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/FormValidateUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/utils/ThrottleUtil.dart';
import 'package:cabinet_flutter_app/page/my/ChangeRealNamePage.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/BottomBtnWidget.dart';
import 'package:cabinet_flutter_app/widget/BottomFormInputWidget.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CommonRowWidget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:redux/redux.dart';

class MyRealNamePage extends StatefulWidget {
  MyRealNamePage({Key? key}) : super(key: key);

  @override
  _MyRealNamePageState createState() => _MyRealNamePageState();
}

class _MyRealNamePageState extends State<MyRealNamePage> {
  GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  TextEditingController realNameController = new TextEditingController();
  TextEditingController idNumberController = new TextEditingController();
  ThrottleUtil throttleUtil = ThrottleUtil();
  UserEntity user = new UserEntity();

  @override
  void initState() {
    super.initState();
    getUserInfo();
  }

  getUserInfo() async {
    var res = await UserDao.getUserInfoLocal();
    if (res != null && res.result) {
      user = res.data;
      idNumberController.value = TextEditingValue(text: user.idNumber ?? '');
      realNameController.value = TextEditingValue(text: user.realName);
    }
    setState(() {});
  }

  @override
  void dispose() {
    super.dispose();
  }

  buildList() {
    List<Widget> widgets = [];
    if (user.hasReal == 1) {
      widgets.add(Container(
        padding: EdgeInsets.fromLTRB(12, 15, 12, 15),
        margin: EdgeInsets.only(top: 15, bottom: 1),
        color: Colors.white,
        child: CommonRowWidget(
          label: '姓名',
          labelFontSize: AppConstant.middleTextWhiteSize,
          labelStyle: TextStyle(color: Colors.black),
          value: '${user.realName}',
          valueFontSize: AppConstant.middleTextWhiteSize,
          valueStyle: TextStyle(color: Colors.black),
        ),
      ));
      widgets.add(Container(
        padding: EdgeInsets.fromLTRB(12, 15, 12, 15),
        color: Colors.white,
        child: CommonRowWidget(
          label: '身份证',
          labelFontSize: AppConstant.middleTextWhiteSize,
          labelStyle: TextStyle(color: Colors.black),
          value: '${user.idNumber}',
          valueFontSize: AppConstant.middleTextWhiteSize,
          valueStyle: TextStyle(color: Colors.black),
        ),
      ));
    } else {
      widgets.add(Container(
        margin: EdgeInsets.only(top: 15, bottom: 1),
        child: BottomFormInputWidget(
          value: user.realName,
          labelWidth: 0,
          hintText: '请输入您的姓名',
          textInputType: TextInputType.name,
          controller: realNameController,
          padding: EdgeInsets.fromLTRB(5, 5, 0, 5),
          validator: (value) {
            return FormValidateUtil.realNameValidate(value);
          },
          onChanged: (String? value) {
            setState(() {
              user.realName = value!;
            });
          },
        ),
      ));
      widgets.add(Container(
        child: BottomFormInputWidget(
          value: user.idNumber ?? '',
          labelWidth: 0,
          hintText: '请输入您的身份证号',
          textInputType: TextInputType.name,
          controller: idNumberController,
          validator: (value) {
            return FormValidateUtil.idNumberValidate(value);
          },
          padding: EdgeInsets.fromLTRB(5, 5, 0, 5),
          onChanged: (String? value) {
            setState(() {
              user.idNumber = value;
            });
          },
        ),
      ));
    }
    return widgets;
  }

  realNameUpload() async {
    Map<String, dynamic> info = {'realName': user.realName, 'idNumber': user.idNumber};
    bool validator = CheckUtils.isNotNull(info['realName']) && CheckUtils.isNotNull(info['idNumber']);
    if (validator) {
      LoadingUtil(
        status: '实名认证中...',
      ).show(context);
      var res = await UserDao.realNameAuth(info);
      LoadingUtil.dismiss(context);
      if (res != null && res.result) {
        if (res.data) {
          Fluttertoast.showToast(msg: '实名认证成功');
          user.hasReal = 1;
          LocalStorage.save(DefaultConfig().configs.USER_INFO, user);
          Store<AppState> store = StoreProvider.of(context);
          store.dispatch(new UpdateUserAction(user));
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return new Material(
      child: new Scaffold(
          backgroundColor: DefaultConfig().configs.BG_COLOR,
          appBar: new AppCustomerBar(
            title: new AppbarTitle(
              title: '实名认证',
              isCenter: true,
            ),
            actions: <Widget>[
              new Container(
                width: 60.0,
              )
            ],
          ),
          body: new Column(
            children: <Widget>[
              Expanded(
                  child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(children: buildList()),
                ),
              )),
              Offstage(
                offstage: user.hasReal == 1,
                child: Container(
                  child: Column(
                    children: [
                      Text(
                        '实名认证后才可以操作投递',
                        style: TextStyle(color: DefaultConfig().configs.GREY_COLOR, fontSize: 14),
                      ),
                      Padding(padding: EdgeInsets.only(bottom: 10)),
                      BottomBtnWidget(
                          showShadow: false,
                          title: '验证',
                          type: CheckUtils.isNotNull(user.idNumber) && CheckUtils.isNotNull(user.realName)
                              ? ButtonType.primary
                              : ButtonType.primaryDisabled,
                          action: () => throttleUtil.throttle(() {
                                realNameUpload();
                              })),
                    ],
                  ),
                ),
              ),
              Offstage(
                offstage: user.hasReal != 1,
                child: Container(
                  child: Column(
                    children: [
                      BottomBtnWidget(
                          showShadow: false,
                          title: '修改',
                          type: CheckUtils.isNotNull(user.idNumber) && CheckUtils.isNotNull(user.realName)
                              ? ButtonType.primary
                              : ButtonType.primaryDisabled,
                          action: () => throttleUtil.throttle(() {
                                Navigator.of(context)
                                    .push(new MaterialPageRoute(builder: (_) => ChangeRealName()))
                                    .then((value) => getUserInfo());
                              })),
                    ],
                  ),
                ),
              )
            ],
          )),
    );
  }
}
