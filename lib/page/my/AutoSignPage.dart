import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/brand_bind_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/BrandUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CommonRowWidget.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CustomerDropDownSelectWidget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class AutoSignPage extends StatefulWidget {
  AutoSignPage({Key? key}) : super(key: key);

  @override
  _AutoSignPageState createState() => _AutoSignPageState();
}

class _AutoSignPageState extends State<AutoSignPage> {
  List<dynamic> companyList = [];
  late UserEntity user;
  bool isChange = false;
  Map<String, String> timeMap = DefaultConfig().configs.shopAutoSignDay;

  @override
  void initState() {
    super.initState();
    getUserInfo();
    homePageRefresh();
    getCompanyList();
  }

  homePageRefresh() async {
    BrandUtils.saveHomePageRefresh();
  }

  getUserInfo() async {
    var res = await UserDao.getUserInfoLocal();
    if (res != null && res.result) {
      user = res.data;
    }
    setState(() {});
  }

  getCompanyList() async {
    var res = await UserDao.getAutoSignList();
    companyList = [];
    if (res != null && res.result) {
      companyList = res.data;
    }
    setState(() {});
  }

  @override
  void dispose() {
    super.dispose();
  }

  changeValue() {
    setState(() {
      isChange = true;
    });
  }

  buildCompanyList() {
    List<Widget> widgets = <Widget>[];
    widgets = List.generate(companyList.length, (index) {
      BrandBindEntity entity = BrandBindEntity.fromJson(companyList[index]);
      return _render(entity, index);
    });
    return widgets;
  }

  _render(entity, index) {
    return Container(
        padding: EdgeInsets.fromLTRB(10, 0, 10, 0),
        child: Container(
            padding: EdgeInsets.only(bottom: 8),
            decoration: BoxDecoration(
                color: Colors.white,
                border: Border(
                  bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                )),
            child: CommonRowWidget(
              leftWidget: Expanded(
                  flex: 2,
                  child: Text(
                    DefaultConfig().configs.EXPRESS2[entity.brandCode] ?? '',
                    style: TextStyle(
                        fontSize: AppConstant.middleTextWhiteSize, color: entity.switchAutoSign == 1 ? Colors.black : Color(0xFF999999)),
                    textAlign: TextAlign.left,
                  )),
              middleWidget: Expanded(
                  flex: 4,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(entity.switchAutoSign == 1 ? '允许' : '不允许',
                          style: TextStyle(
                              fontSize: 14, color: entity.switchAutoSign == 1 ? Color(0xFF858585) : Theme.of(context).primaryColor)),
                      Transform.scale(
                        scale: 0.7,
                        child: CupertinoSwitch(
                            value: entity.switchAutoSign == 1,
                            activeColor: Theme.of(context).primaryColor,
                            trackColor: Colors.grey[200],
                            onChanged: (bool value) async {
                              changeValue();
                              BrandBindEntity brandBindEntity = new BrandBindEntity();
                              brandBindEntity.id = entity.id;
                              brandBindEntity.brandCode = entity.brandCode;
                              brandBindEntity.switchAutoSign = value ? 1 : 0;
                              brandBindEntity.autoSignDay = entity.autoSignDay;
                              await updateCompany(brandBindEntity, index);
                            }),
                      )
                    ],
                  )),
              rightWidget: Expanded(
                  flex: 6,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text('超时自动出库', style: TextStyle(fontSize: 14, color: Color(0xFF858585))),
                      Container(
                        padding: EdgeInsets.only(left: 4),
                        margin: EdgeInsets.only(left: 4),
                        constraints: BoxConstraints(maxWidth: 80),
                        decoration: BoxDecoration(boxShadow: [
                          BoxShadow(color: Colors.grey.shade300, offset: Offset(-2, 0), blurRadius: 3),
                          BoxShadow(color: Colors.white, offset: Offset(0, -3), blurRadius: 0),
                          BoxShadow(color: Colors.white, offset: Offset(0, 3), blurRadius: 0),
                        ]),
                        child: CustomerDropDownSelectWidget(
                          map: timeMap,
                          value: '${entity.autoSignDay}',
                          cb: (item) async {
                            changeValue();
                            BrandBindEntity brandBindEntity = new BrandBindEntity();
                            brandBindEntity.id = entity.id;
                            brandBindEntity.brandCode = entity.brandCode;
                            brandBindEntity.switchAutoSign = entity.switchAutoSign;
                            brandBindEntity.autoSignDay = int.parse(item);
                            await updateCompany(brandBindEntity, index);
                          },
                        ),
                      )
                    ],
                  )),
            )));
  }

  updateCompany(BrandBindEntity item, int index) async {
    LoadingUtil(
      status: '数据更新中...',
    ).show(context);
    Map<String, dynamic> info = {
      'id': item.id,
      'brandCode': item.brandCode,
      'switchAutoSign': item.switchAutoSign,
      'autoSignDay': item.autoSignDay
    };
    if (['JD', 'SF'].indexOf(item.brandCode!) > -1) {
      LoadingUtil.dismiss(context);
      Fluttertoast.showToast(msg: '该品牌未开通自动签收');
    }else{
      var res = await UserDao.updateAutoSign(info);
      LoadingUtil.dismiss(context);
      if (res != null && res.result) {
        companyList[index] = info;
        setState(() {});
        Fluttertoast.showToast(msg: '设置成功');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        // ignore: missing_return
        onWillPop: () async {
          Navigator.pop(context);
          return true;
        },
        child: Scaffold(
            backgroundColor: DefaultConfig().configs.BG_COLOR,
            appBar: new AppCustomerBar(
              title: AppbarTitle(
                title: '签收设置',
                isCenter: true,
              ),
              actions: [
                Container(
                  width: 60,
                )
              ],
            ),
            body: Column(
              children: [
                Container(
                    color: DefaultConfig().configs.PRIMARY_COLOR_LIGHT,
                    padding: EdgeInsets.fromLTRB(10, 6, 10, 6),
                    child: Text(
                      '超时自动出库:开启该功能后，超过自动签收日期，'
                      '未取走的包裹将自动出库，客户仍可取件。'
                      '签收功能需打开，超时出库设置才可生效!',
                      style: TextStyle(fontSize: 13, color: Theme.of(context).primaryColor),
                    )),
                Container(
                    color: DefaultConfig().configs.BG_COLOR,
                    padding: EdgeInsets.fromLTRB(10, 4, 10, 10),
                    child: CommonRowWidget(
                      leftWidget: Expanded(
                          flex: 2, child: Text('快递品牌', style: TextStyle(fontSize: AppConstant.smallTextSize), textAlign: TextAlign.left)),
                      middleWidget: Expanded(
                          flex: 4,
                          child: Container(
                            alignment: Alignment.centerRight,
                            child: Text('是否自动签收', style: TextStyle(fontSize: AppConstant.smallTextSize), textAlign: TextAlign.center),
                          )),
                      rightWidget: Expanded(
                          flex: 6,
                          child: Container(
                            alignment: Alignment.centerRight,
                            child: Text('超时签收天数', style: TextStyle(fontSize: AppConstant.smallTextSize), textAlign: TextAlign.center),
                          )),
                    )),
                Expanded(
                    child: Container(
                  color: Colors.white,
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: buildCompanyList(),
                    ),
                  ),
                )),
              ],
            )));
  }
}
