import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:cabinet_flutter_app/common/config/Config.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_shop_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/user_info_entity.dart';
import 'package:cabinet_flutter_app/common/utils/CabinetBoxUtil.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/FormValidateUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/PointMultiselect.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CustomerDropDownSelectWidget.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:scan/device_info.dart';
import 'package:scan/scan.dart';

class UserUpdatePage extends StatefulWidget {
  UserInfoEntity? userData;
  String type;

  UserUpdatePage(this.userData, this.type, {Key? key}) : super(key: key);

  @override
  _UserUpdatePageState createState() => _UserUpdatePageState();
}

class _UserUpdatePageState extends State<UserUpdatePage> {
  TextEditingController loginNameController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController captchaEditingController = TextEditingController();
  TextEditingController imageController = TextEditingController();
  String verifyCode = '';
  var countTime = 0;
  Timer? timer;
  String captcha = '';
  bool captchaShow = false;
  String channel = '';
  String type = '1';
  String title = '添加账号';
  List cabinetLocationIds = [];
  List<CabinetShopEntity?> options = [];

  /// 计时器
  void startTimer() {
    countTime = 60;
    if (timer != null) {
      return;
    }
    timer = Timer.periodic(Duration(seconds: 1), (time) {
      setState(() {
        if (countTime > 0) {
          countTime--;
        } else {
          timer?.cancel();
          timer = null;
        }
      });
    });
  }

  /// 发送验证码
  Future<void> sendCode() async {
    Map<String, dynamic> data = {'sopAccountId': widget.userData?.sopAccountId};
    var res = await UserDao.sendUserSms(data);
    if (res != null && res.result) {
      if (res.data) {
        Fluttertoast.showToast(msg: '验证码发送成功,请查收');
        startTimer();
      } else {
        Fluttertoast.showToast(msg: '验证码发送失败请重试');
      }
    }
  }

  @override
  void initState() {
    super.initState();
    if (widget.type == 'update') {
      title = '修改账号';
      setState(() {
        loginNameController.text = widget.userData?.loginName ?? '';
        passwordController.text = widget.userData?.password ?? '';
        channel = widget.userData?.channel ?? '';
        type = widget.userData?.type ?? '';
        cabinetLocationIds = widget.userData!.cabinetLocationIds != '' ? widget.userData!.cabinetLocationIds.split(',') : [];
      });
    }
    getPointList();
  }

  getPointList() async {
    var res = await CourierDao.shopCabinetLocation();
    LoadingUtil.dismiss(context);
    if (res != null && res.result) {
      options = jsonConvert.convertList<CabinetShopEntity>(res.data)!;
      if (options.length < 1) {
        Fluttertoast.showToast(msg: '无可用点位');
      }
      setState(() {});
    }
  }

  checkImage() {
    String image = '';
    int time = 0;
    getCaptcha(setState) async {
      LoadingUtil(
        status: '获取验证码中...',
      ).show(context);
      var res = await UserDao.getCaptcha({'sopAccountId': widget.userData?.sopAccountId});
      LoadingUtil.dismiss(context);
      if (res != null && res.result) {
        setState(() {
          imageController.text = '';
          image = res.data['data']['imageBase64'];
        });
      }
    }

    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            time++;
            if (time == 1) {
              getCaptcha(setState);
            }
            return WillPopScope(
              onWillPop: () async => true,
              child: Material(
                type: MaterialType.transparency,
                child: Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Container(
                        width: 350,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.all(
                            Radius.circular(10.0),
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          mainAxisSize: MainAxisSize.min,
                          children: <Widget>[
                            Row(
                              children: <Widget>[
                                Expanded(
                                  child: Container(
                                    height: 50.0,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).primaryColor,
                                      borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(10.0),
                                        topRight: Radius.circular(10.0),
                                      ),
                                    ),
                                    child: Text(
                                      '图形验证码验证',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 20,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                )
                              ],
                            ),
                            Container(
                              width: 290,
                              padding: EdgeInsets.only(bottom: 20, top: 10, right: 10),
                              child: Column(
                                children: [
                                  image != ''
                                      ? Image.memory(
                                          Base64Decoder().convert(image.split(',')[1]),
                                          width: 290.0,
                                        )
                                      : Container(),
                                  Container(
                                    margin: EdgeInsets.fromLTRB(0, 10, 0, 0),
                                    padding: EdgeInsets.fromLTRB(0, 10, 0, 10),
                                    child: TextFormField(
                                      autofocus: true,
                                      validator: (value) => FormValidateUtil.phoneValidate(value!, context),
                                      decoration: InputDecoration(
                                        hintText: '请输入图形验证码',
                                        labelText: '图形验证码',
                                        focusedBorder: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(7),
                                            borderSide: BorderSide(color: Colors.red)),
                                        enabledBorder: InputBorder.none,
                                        errorBorder: InputBorder.none,
                                        disabledBorder: InputBorder.none,
                                        errorMaxLines: 1,
                                        border: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(7),
                                            borderSide: BorderSide(color: Colors.red)),
                                        contentPadding: EdgeInsets.fromLTRB(5, 2, 5, 2),
                                      ),
                                      onChanged: (String value) {
                                        imageController.text = value;
                                      },
                                      controller: imageController,
                                      textInputAction: TextInputAction.done,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Row(
                              children: <Widget>[
                                Expanded(
                                  child: InkWell(
                                    onTap: () {
                                      Navigator.of(context).pop();
                                    },
                                    child: Container(
                                      height: 50.0,
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                        border: Border(
                                          top: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                          right: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                        ),
                                      ),
                                      child: Text(
                                        '取消',
                                        style: TextStyle(
                                          fontSize: 15,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                Expanded(
                                  child: InkWell(
                                    onTap: () async {
                                      var res = await verifyCaptcha();
                                      if (res != null && res.result) {
                                        if (res.data) {
                                          Navigator.of(context).pop();
                                          checkedLogin();
                                        } else {
                                          Fluttertoast.showToast(msg: '图形验证码错误');
                                          getCaptcha(setState);
                                        }
                                      }
                                    },
                                    child: Container(
                                      height: 50.0,
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                        border: Border(
                                          top: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                        ),
                                      ),
                                      child: Text(
                                        '验证',
                                        style: TextStyle(
                                          color: Colors.red,
                                          fontSize: 15,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            )
                          ],
                        ),
                      ),
                      Padding(padding: EdgeInsets.only(bottom: 60.0))
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  clickImage() async {
    String image = "";
    var wordList = [];
    LoadingUtil(
      status: '获取验证码中...',
    ).show(context);
    var res = await UserDao.getCaptcha({'sopAccountId': widget.userData?.sopAccountId});
    LoadingUtil.dismiss(context);
    if (res != null && res.result) {
      setState(() {
        imageController.text = '';
        image = res.data['data']['imageBase64'];
        wordList = res.data['data']['wordList'];
      });
    }

    final List<Map<String, dynamic>> _markers = [];
    final List<Map<String, dynamic>> _postJson = [];
    var imageBytes = Base64Decoder().convert(image);
    int _counter = 1;
    void _handleImageClick(setState, TapDownDetails details) async {
      final localOffset = details.localPosition; // 获取局部坐标
      setState(() {
        // 将坐标和标记数字添加到列表中
        _markers.add({
          'x': localOffset.dx,
          'y': localOffset.dy,
          'number': _counter++,
        });
        _postJson.add({
          'x': localOffset.dx.truncateToDouble(),
          'y': localOffset.dy.truncateToDouble(),
        });
      });
      if (_counter > 3) {
        Navigator.of(context).pop();
        LoadingUtil(
          status: '校验中...',
        ).show(context);
        var verifyCode = jsonEncode(_postJson);
        var res =
            await UserDao.verifyCaptcha({'sopAccountId': widget.userData?.sopAccountId, 'verifyCode': verifyCode});
        LoadingUtil.dismiss(context);
        if (res != null && res.result) {
          if (res.data) {
            checkedLogin();
          } else {
            Fluttertoast.showToast(msg: '校验失败');
            clickImage();
          }
        }
        return;
      }
    }

    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return WillPopScope(
              onWillPop: () async => true,
              child: Material(
                type: MaterialType.transparency,
                child: Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Container(
                        width: 350,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.all(
                            Radius.circular(10.0),
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          mainAxisSize: MainAxisSize.min,
                          children: <Widget>[
                            Row(
                              children: <Widget>[
                                Expanded(
                                  child: Container(
                                    height: 50.0,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).primaryColor,
                                      borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(10.0),
                                        topRight: Radius.circular(10.0),
                                      ),
                                    ),
                                    child: Text(
                                      '请完成安全验证',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                )
                              ],
                            ),
                            GestureDetector(
                              onTapDown: (TapDownDetails details) {
                                _handleImageClick(setState, details); // 点击图片时获取坐标
                              }, // 监听点击事件
                              child: Stack(
                                children: [
                                  Image.memory(
                                    imageBytes,
                                  ),
                                  // 在点击位置显示标记数字
                                  for (var marker in _markers)
                                    Positioned(
                                      left: marker['x'] - 12, // 调整数字位置居中
                                      top: marker['y'] - 12, // 调整数字位置居中
                                      child: Container(
                                        padding: EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          color: Colors.red,
                                          shape: BoxShape.circle,
                                        ),
                                        child: Text(
                                          marker['number'].toString(),
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 16,
                                          ),
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                            Text(
                              '请依次点击文字: ' + wordList.join(', '),
                            ),
                            Row(
                              children: <Widget>[
                                Expanded(
                                  child: InkWell(
                                    onTap: () {
                                      Navigator.of(context).pop();
                                    },
                                    child: Container(
                                      height: 50.0,
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                        border: Border(
                                          top: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                          right: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                        ),
                                      ),
                                      child: Text(
                                        '取消',
                                        style: TextStyle(
                                          fontSize: 15,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            )
                          ],
                        ),
                      ),
                      Padding(padding: EdgeInsets.only(bottom: 60.0))
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  verifyCaptcha() async {
    if (imageController.text.isEmpty) {
      Fluttertoast.showToast(msg: '请输入图形验证码');
      return;
    }
    LoadingUtil(
      status: '校验中...',
    ).show(context);
    var res = await UserDao.verifyCaptcha(
        {'sopAccountId': widget.userData?.sopAccountId, 'verifyCode': imageController.text});
    LoadingUtil.dismiss(context);
    return res;
  }

  checkedLogin() async {
    var res =
        await CabinetBoxUtil.loginCheck(context, widget.userData, captchaEditingController.text, captchaShow, channel);
    if (res != null && res.result) {
      if (res.data == 1) {
        setState(() {
          captchaShow = true;
        });
        Fluttertoast.showToast(msg: '校验失败,请输入验证码重新校验');
      } else if (res.data == 2) {
        imageController.text = '';
        setState(() {});
        if ('YZ_PDD' == channel) {
          await checkImage();
        }
        if ('YZ_YDCS' == channel) {
          await clickImage();
        }
      } else {
        setState(() {
          captchaShow = false;
        });
        Fluttertoast.showToast(msg: '校验成功');
      }
    }
  }

  updateDevices() async {
    if (channel == 'YZ_PDD' && Platform.isAndroid) {
      String msg = '';
      if (widget.userData?.deviceId == '') {
        msg = '是否同步设备号，请确保该设备与多多APP设备一致';
      } else {
        msg = '已有设备号，是否继续同步设备号？请确认该设备为多多APP设备';
      }
      bool res = await CommonUtils.confirm(context, msg);
      if (res) {
        String ddString = await scan.getDdKey();
        if (ddString != '') {
          var res = await UserDao.ddGet({'content': ddString});
          if (res.result) {
            await myUserUpdate(deviceId: res.data['deviceId']);
          }
        }
      }
    }
  }

  myUserUpdate({String? deviceId = ''}) async {
    if (channel == '') {
      Fluttertoast.showToast(msg: '驿站品牌不能为空');
      return;
    }
    if (loginNameController.text == '') {
      Fluttertoast.showToast(msg: '登录名不能为空');
      return;
    }
    if (passwordController.text == '') {
      Fluttertoast.showToast(msg: '密码不能为空');
      return;
    }
    if (type == "2" && cabinetLocationIds.length == 0) {
      Fluttertoast.showToast(msg: '所属点位不能为空');
      return;
    }
    LoadingUtil(
      status: '数据保存中...',
    ).show(context);
    DeviceInfo deviceInfo = DeviceInfo();
    String? deviceModel = '';
    if (Platform.isAndroid) {
      deviceInfo = await scan.getDeviceInfo();
      deviceModel = deviceInfo.model;
      if (channel == "YZ_PDD") {
        deviceModel = '${deviceInfo.model}#${deviceInfo.release}#${deviceInfo.id}|${deviceInfo.property}';
      }
    }
    Map<String, dynamic> info = {
      'type': type,
      'channel': channel,
      'cabinetLocationIds': cabinetLocationIds,
      'loginName': loginNameController.text,
      'password': passwordController.text,
      'deviceId': deviceId,
      'deviceModel': deviceModel
    };
    if (widget.type == 'update') {
      info['shopYzAccountId'] = widget.userData?.id;
    }
    var res = await UserDao.updateAccount(info);
    LoadingUtil.dismiss(context);
    if (res != null && res.result) {
      if (widget.type == 'update') {
        widget.userData?.deviceId = deviceId;
        widget.userData?.deviceModel = deviceModel;
        checkedLogin();
      } else {
        Fluttertoast.showToast(msg: '新增成功');
        Navigator.pop(context);
      }
    }
  }

  delAccount() async {
    bool result = await CommonUtils.confirm(context, '确定删除吗？');
    if (!result) {
      return;
    }
    Map<String, dynamic> info = {
      'shopYzAccountId': widget.userData?.id,
    };
    var res = await UserDao.accountDel(info);
    if (res != null && res.result) {
      Fluttertoast.showToast(msg: '删除成功');
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Scaffold(
        backgroundColor: Color(0xFFF5F5F5),
        appBar: AppCustomerBar(
          title: AppbarTitle(
            title: title,
            isCenter: true,
          ),
          actions: <Widget>[
            widget.type == 'update'
                ? Container(
                    width: 60,
                    child: TextButton(
                      onPressed: () async {
                        delAccount();
                      },
                      child: Text('删除'),
                    ),
                  )
                : Container(width: 60),
          ],
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 1,
              color: Color(0xFFEEEEED),
            ),
            Offstage(
                offstage: widget.type == 'update',
                child: Container(
                  padding: EdgeInsets.only(left: 15),
                  color: Colors.white,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        width: 80,
                        child: Text('驿站品牌'),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Container(
                              margin: EdgeInsets.all(10),
                              constraints: BoxConstraints(maxWidth: 290),
                              child: CustomerDropDownSelectWidget(
                                map: Config().brandMap,
                                value: channel,
                                cb: (item) {
                                  setState(() {
                                    channel = item;
                                  });
                                },
                              )),
                        ],
                      ),
                    ],
                  ),
                )),
            Container(
              height: 1,
              color: Color(0xFFEEEEED),
            ),
            Container(
              padding: EdgeInsets.only(left: 15),
              color: Colors.white,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    width: 80,
                    child: Text('类型'),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Container(
                          margin: EdgeInsets.all(10),
                          constraints: BoxConstraints(maxWidth: 290),
                          child: CustomerDropDownSelectWidget(
                            map: Config().brandType,
                            value: type,
                            cb: (item) {
                              setState(() {
                                type = item;
                              });
                            },
                          )),
                    ],
                  ),
                ],
              ),
            ),
            Container(
              height: 1,
              color: Color(0xFFEEEEED),
            ),
            Offstage(
                offstage: type == '1',
                child: Column(
                  children: [
                    Container(
                      padding: EdgeInsets.only(left: 15),
                      color: Colors.white,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            width: 80,
                            child: Text('所属点位'),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Container(
                                  margin: EdgeInsets.all(10),
                                  constraints: BoxConstraints(maxWidth: 290),
                                  child: PointMultiSelect(
                                    title: '选择点位',
                                    selectedOptions: cabinetLocationIds,
                                    onChanged: (List value) {
                                      setState(() {
                                        cabinetLocationIds = value;
                                      });
                                    },
                                    options: options,
                                  )),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Container(
                      height: 1,
                      color: Color(0xFFEEEEED),
                    ),
                  ],
                )),
            Container(
              padding: EdgeInsets.only(left: 15, right: 15),
              color: Colors.white,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    width: 80,
                    child: Text('登录名'),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Container(
                        padding: EdgeInsets.only(left: 10, right: 10),
                        margin: EdgeInsets.only(top: 10, bottom: 10),
                        decoration: new BoxDecoration(color: Color(0xFFEEEEED)),
                        child: new TextField(
                          controller: loginNameController,
                          style: TextStyle(
                            fontSize: 15.0,
                            color: Colors.black87,
                          ),
                          decoration: new InputDecoration(
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.all(0.0),
                              hintText: '请输入用户名',
                              hintStyle: new TextStyle(fontSize: 15.0, color: Colors.grey.shade500)),
                          onChanged: (name) {},
                          onSubmitted: (name) async {},
                        ),
                        constraints: BoxConstraints(maxWidth: 290),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Container(
              height: 1,
              color: Color(0xFFEEEEED),
            ),
            Container(
              padding: EdgeInsets.only(left: 15, right: 15),
              color: Colors.white,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    width: 80,
                    child: Text('密码'),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Container(
                        padding: EdgeInsets.only(left: 10, right: 10),
                        margin: EdgeInsets.only(top: 10, bottom: 10),
                        decoration: new BoxDecoration(color: Color(0xFFEEEEED)),
                        child: new TextField(
                          style: TextStyle(
                            fontSize: 15.0,
                            color: Colors.black87,
                          ),
                          controller: passwordController,
                          decoration: new InputDecoration(
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.all(0.0),
                              hintText: '请输入密码',
                              hintStyle: new TextStyle(fontSize: 15.0, color: Colors.grey.shade500)),
                          onChanged: (name) {},
                          onSubmitted: (name) async {},
                        ),
                        constraints: BoxConstraints(maxWidth: 290),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Offstage(
              offstage: widget.type != 'update',
              child: Column(
                children: [
                  Container(
                    height: 1,
                    color: Color(0xFFEEEEED),
                  ),
                  Container(
                    padding: EdgeInsets.only(left: 15, right: 15),
                    color: Colors.white,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          width: 80,
                          child: Text('手机号'),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Container(
                              constraints: BoxConstraints(maxWidth: 290),
                              height: 50.0,
                              alignment: Alignment.centerLeft,
                              child: Text(
                                '${widget.userData?.mobile != '' ? widget.userData?.mobile : '暂无'}',
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Container(
                    height: 1,
                    color: Color(0xFFEEEEED),
                  ),
                  Container(
                    padding: EdgeInsets.only(left: 15, right: 15),
                    color: Colors.white,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          width: 80,
                          child: Text('设备名称'),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Container(
                              constraints: BoxConstraints(maxWidth: 290, minHeight: 50),
                              alignment: Alignment.centerLeft,
                              child: Text(
                                '${widget.userData?.deviceModel != '' ? widget.userData?.deviceModel : '暂无'}',
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Container(
                    height: 1,
                    color: Color(0xFFEEEEED),
                  ),
                  Container(
                    padding: EdgeInsets.only(left: 15, right: 15),
                    color: Colors.white,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          width: 80,
                          child: Text('设备号'),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Container(
                              constraints: BoxConstraints(maxWidth: 180, minHeight: 50),
                              alignment: Alignment.centerLeft,
                              child: Text(
                                '${widget.userData?.deviceId != '' ? widget.userData?.deviceId : '暂无'}',
                              ),
                            ),
                            Offstage(
                              offstage: channel != 'YZ_PDD',
                              child: Row(
                                children: [
                                  MaterialButton(
                                    color: Theme.of(context).primaryColor,
                                    textColor: Colors.white,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(20.0),
                                    ),
                                    onPressed: () async {
                                      updateDevices();
                                    },
                                    child: Text('同步设备号'),
                                  ),
                                ],
                              ),
                            ),
                            Offstage(
                              offstage: channel == 'YZ_PDD',
                              child: Row(
                                children: [
                                  Container(
                                    width: 110,
                                  )
                                ],
                              ),
                            )
                          ],
                        ),
                      ],
                    ),
                  ),
                  Offstage(
                    offstage: !captchaShow,
                    child: Column(
                      children: [
                        Container(
                          height: 1,
                          color: Color(0xFFEEEEED),
                        ),
                        Container(
                            padding: EdgeInsets.only(left: 15, right: 15),
                            color: Colors.white,
                            child: Row(
                              children: [
                                Container(
                                  width: 80,
                                  child: Text('验证码'),
                                ),
                                Expanded(
                                    child: Row(mainAxisAlignment: MainAxisAlignment.end, children: [
                                  Container(
                                      constraints: BoxConstraints(maxWidth: 200),
                                      child: TextFormField(
                                        keyboardType: TextInputType.number,
                                        controller: captchaEditingController,
                                        style: TextStyle(
                                          height: 1.5,
                                          fontSize: 16.0,
                                          color: Colors.black,
                                        ),
                                        decoration: new InputDecoration(
                                            border: InputBorder.none,
                                            contentPadding: EdgeInsets.symmetric(vertical: 10),
                                            hintText: '请输入验证码',
                                            hintStyle: new TextStyle(fontSize: 16.0, color: Colors.grey.shade500),
                                            suffixIcon: captchaEditingController.text.length > 0
                                                ? Container(
                                                    width: 20,
                                                    child: new IconButton(
                                                      alignment: Alignment.center,
                                                      padding: const EdgeInsets.all(0.0),
                                                      iconSize: 18.0,
                                                      icon: Icon(Icons.cancel),
                                                      onPressed: () {
                                                        setState(() {
                                                          captchaEditingController.value = TextEditingValue(text: '');
                                                          captcha = '';
                                                        });
                                                      },
                                                    ),
                                                  )
                                                : new Text("")),
                                        onChanged: (value) {
                                          setState(() {
                                            captcha = value;
                                          });
                                        },
                                      )),
                                  Container(
                                    width: 100,
                                    child: TextButton(
                                      child: Text(countTime <= 0 ? '获取验证码' : "$countTime秒后重试"),
                                      style: ButtonStyle(
                                          foregroundColor:
                                              MaterialStateProperty.all<Color>(Theme.of(context).primaryColor)),
                                      onPressed: countTime <= 0 ? sendCode : null,
                                    ),
                                  )
                                ]))
                              ],
                            )),
                      ],
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.all(10),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 3,
                          child: MaterialButton(
                            color: Color(0xFFF9A825),
                            textColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20.0),
                            ),
                            onPressed: () async {
                              if (widget.userData?.deviceId == '' && channel == 'YZ_PDD') {
                                await updateDevices();
                              } else {
                                await checkedLogin();
                              }
                            },
                            child: Text("校验登录"),
                          ),
                        ),
                        Expanded(flex: 1, child: Container()),
                        Expanded(
                          flex: 3,
                          child: MaterialButton(
                            color: Theme.of(context).primaryColor,
                            textColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20.0),
                            ),
                            onPressed: () async {
                              if (widget.userData?.deviceId == '' && channel == 'YZ_PDD') {
                                await updateDevices();
                              } else {
                                myUserUpdate(deviceId: widget.userData?.deviceId);
                              }
                            },
                            child: Text("保存并验证"),
                          ),
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Spacer(),
            Offstage(
              offstage: widget.type == 'update',
              child: Container(
                width: double.infinity,
                color: Colors.white,
                child: Container(
                  margin: EdgeInsets.only(left: 15, right: 15, top: 10, bottom: 15),
                  height: 37,
                  child: MaterialButton(
                    color: Theme.of(context).primaryColor,
                    textColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20.0),
                    ),
                    onPressed: () async {
                      myUserUpdate();
                    },
                    child: Text("确定添加"),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
