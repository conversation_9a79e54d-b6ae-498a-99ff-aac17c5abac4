import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
class MyFeedbackPage extends StatefulWidget {
  @override
  _MyFeedbackPageState createState() => _MyFeedbackPageState(); 
}

class _MyFeedbackPageState extends State<MyFeedbackPage> {
  List<FeedbackItem> feedbackList = [];
  bool isLoading = true;
  
  @override
  void initState() {
    super.initState();
    _loadFeedbackList();
  }
  
  DateTime? _formatReplyTime(dynamic replyTime) {
    print('格式化回复时间: $replyTime');
    if (replyTime == null) return null;
    
    try {
      int timestamp;
      if (replyTime is int) {
        timestamp = replyTime;
      } else if (replyTime is String && replyTime.trim().isNotEmpty) {
        timestamp = int.parse(replyTime.trim());
      } else {
        return null;
      }
      
      // 将时间戳转换为DateTime对象
      return DateTime.fromMillisecondsSinceEpoch(timestamp);
    } catch (e) {
      print('时间戳转换失败: $e');
      return null;
    }
  }

  Future<void> _loadFeedbackList() async {
    try {
      setState(() {
        isLoading = true;
      });
      var resultData = await CourierDao.queryFeedbackList();
      if (resultData.result && resultData.data != null) {
        List<dynamic> dataList = resultData.data;
        List<FeedbackItem> items = dataList.map((item) {
        print('查询反馈列表: ${ item}');
          return FeedbackItem(
            type: item['feedbackType'] == 1 ? 'APP建议' : item['feedbackType'] == 2 ? '功能建议' : '其他',
            content: item['content'] ?? '',
            time: item['createTime'] ?? '',
            hasImage: (item['imageUrls'] != null && item['imageUrls'].toString().trim().isNotEmpty),
            reply: item['replyContent'],
            replyTime:item['replyTime']?? '',
            images: item['imageUrls'] != null && item['imageUrls'].toString().trim().isNotEmpty 
                ? item['imageUrls'].toString().split(',').where((url) => url.trim().isNotEmpty).toList()
                : [],
          );
        }).toList();
        
        setState(() {
          feedbackList = items;
          isLoading = false;
        });
      } else {
        setState(() {
          feedbackList = [];
          isLoading = false;
        });
      }
    } catch (e) {
      print('加载反馈列表失败: $e');
      setState(() {
        isLoading = false;
      });
      Fluttertoast.showToast(
        msg: '加载失败，请稍后重试',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
      );
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: Colors.black, size: 18.sp),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          '我的反馈',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
      ),
      body: isLoading
          ? Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF2196F3)),
              ),
            )
          : feedbackList.isEmpty
              ? _buildEmptyState()
              : RefreshIndicator(
                  onRefresh: _loadFeedbackList,
                  child: ListView.builder(
                    padding: EdgeInsets.all(16.w),
                    itemCount: feedbackList.length,
                    itemBuilder: (context, index) {
                      return _buildFeedbackCard(feedbackList[index]);
                    },
                  ),
                ),
    );
  }
  
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.feedback_outlined,
            size: 80.sp,
            color: Colors.grey[300],
          ),
          SizedBox(height: 16.h),
          Text(
            '暂无反馈记录',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildFeedbackCard(FeedbackItem item) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 反馈内容部分
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      '反馈内容',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    Spacer(),
                    Text(
                      item.time,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.h),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: Color(0xFF2196F3).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Text(
                    '[${item.type}]',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Color(0xFF2196F3),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                SizedBox(height: 12.h),
                Text(
                  item.content,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.black87,
                    height: 1.5,
                  ),
                ),
                if (item.hasImage && item.images.isNotEmpty) ...[
                  SizedBox(height: 12.h),
                  _buildFeedbackImages(item.images),
                ],
              ],
            ),
          ),
          
          // 分割线
          Container(
            height: 1,
            color: Colors.grey[100],
            margin: EdgeInsets.symmetric(horizontal: 16.w),
          ),
          
          // 客服回复部分
          if (item.reply != null) ...[
            Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.support_agent,
                        size: 16.sp,
                        color: Color(0xFF4CAF50),
                      ),
                      SizedBox(width: 6.w),
                      Text(
                        '客服回复',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF4CAF50),
                        ),
                      ),
                      Spacer(),
                      Text(
                        item.replyTime != null 
                            ? item.replyTime! : '',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8.h),
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(12.w),
                    decoration: BoxDecoration(
                      color: Color(0xFF4CAF50).withValues(alpha: 0.05),
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(
                        color: Color(0xFF4CAF50).withValues(alpha: 0.2),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      item.reply!,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.black87,
                        height: 1.5,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ] else ...[
            Padding(
              padding: EdgeInsets.all(16.w),
              child: Row(
                children: [
                  Icon(
                    Icons.schedule,
                    size: 16.sp,
                    color: Colors.orange,
                  ),
                  SizedBox(width: 6.w),
                  Text(
                    '等待客服回复...',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.orange,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
  
  Widget _buildFeedbackImages(List<String> images) {
    return Wrap(
      spacing: 8.w,
      runSpacing: 8.w,
      children: images.map((imageUrl) {
        return Container(
          width: 60.w,
          height: 60.w,
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: Colors.grey[300]!, width: 1),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8.r),
            child: Image.network(
              imageUrl,
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Color(0xFFF5F5F5),
                  child: Icon(
                    Icons.image,
                    color: Colors.grey[400],
                    size: 24.sp,
                  ),
                );
              },
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return Container(
                  color: Color(0xFFF5F5F5),
                  child: Center(
                    child: CircularProgressIndicator(
                      value: loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                      strokeWidth: 2,
                    ),
                  ),
                );
              },
            ),
          ),
        );
      }).toList(),
    );
  }
}

class FeedbackItem {
  final String type;
  final String content;
  final String time;
  final bool hasImage;
  final String? reply;
  final String? replyTime;
  final List<String> images;
  
  FeedbackItem({
    required this.type,
    required this.content,
    required this.time,
    this.hasImage = false,
    this.reply,
    this.replyTime,
    this.images = const [],
  });
}