
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class TeamAddPage extends StatefulWidget {
  const TeamAddPage({Key? key}) : super(key: key);

  @override
  _TeamAddPageState createState() => _TeamAddPageState();
}

class _TeamAddPageState extends State<TeamAddPage> {
  bool isOpenWallet = false;

  TextEditingController nameController = TextEditingController();
  TextEditingController phoneController = TextEditingController();

  myTeamAdd() async {
    if (nameController.text == '') {
      Fluttertoast.showToast(msg: ' 成员姓名不能为空');
      return;
    }
    if (phoneController.text == '') {
      Fluttertoast.showToast(msg: ' 成员手机号不能为空');
      return;
    }
    LoadingUtil(
      status: '数据加载中...',
    ).show(context);
    Map<String, dynamic> info = {
      'name': nameController.text,
      'phone': phoneController.text,
      'hasWallet': isOpenWallet ? 1 : 0
    };
    var res = await UserDao.myTeamAdd(info);
    LoadingUtil.dismiss(context);
    if (res != null && res.result) {
      Fluttertoast.showToast(msg: '新增成功');
      Navigator.of(context).pop(true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Scaffold(
        backgroundColor: Color(0xFFF5F5F5),
        appBar: AppCustomerBar(
          title: AppbarTitle(
            title: '团队管理',
            isCenter: true,
          ),
          actions: <Widget>[Container(width: 60)],
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
                margin: EdgeInsets.only(left: 15, top: 12, bottom: 12),
                child: Text(
                  '信息填写',
                  style: TextStyle(fontSize: 14, color: Color(0xFF999999)),
                )),
            Container(
              padding: EdgeInsets.only(left: 15),
              color: Colors.white,
              child: Row(
                children: [
                  Text('成员姓名'),
                  Expanded(
                      child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Container(
                        child: new TextField(
                          controller: nameController,
                          style: TextStyle(
                            fontSize: 15.0,
                            color: Colors.black87,
                          ),
                          decoration: new InputDecoration(
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.all(0.0),
                              hintText: '请输入',
                              hintStyle: new TextStyle(fontSize: 15.0, color: Colors.grey.shade500)),
                          onChanged: (name) {},
                          onSubmitted: (name) async {},
                        ),
                        width: 110,
                      ),
                    ],
                  )),
                ],
              ),
            ),
            Container(
              height: 1,
              color: Color(0xFFEEEEED),
            ),
            Container(
              padding: EdgeInsets.only(left: 15),
              color: Colors.white,
              child: Row(
                children: [
                  Text('手机号'),
                  Expanded(
                      child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Container(
                        child: new TextField(
                          style: TextStyle(
                            fontSize: 15.0,
                            color: Colors.black87,
                          ),
                          controller: phoneController,
                          decoration: new InputDecoration(
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.all(0.0),
                              hintText: '请输入',
                              hintStyle: new TextStyle(fontSize: 15.0, color: Colors.grey.shade500)),
                          onChanged: (name) {},
                          onSubmitted: (name) async {},
                        ),
                        width: 110,
                      ),
                    ],
                  )),
                ],
              ),
            ),
            Container(
                margin: EdgeInsets.only(left: 15, top: 12, bottom: 12),
                child: Text(
                  '钱包开关',
                  style: TextStyle(fontSize: 14, color: Color(0xFF999999)),
                )),
            Container(
              padding: EdgeInsets.only(left: 15, top: 7, right: 15, bottom: 7),
              color: Colors.white,
              child: Row(
                children: [
                  Text('钱包功能'),
                  Expanded(
                      child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Container(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: <Widget>[
                            Text(
                              '钱包功能',
                              style: TextStyle(fontSize: 15.0, color: Colors.white),
                            ),
                            Transform.scale(
                              scale: 0.7,
                              child: CupertinoSwitch(
                                  value: isOpenWallet,
                                  activeColor: Theme.of(context).primaryColor,
                                  trackColor: Colors.grey.shade200,
                                  onChanged: (bool isOpen) async {
                                    isOpenWallet = isOpen;
                                    setState(() {});
                                  }),
                            ),
                          ],
                        ),
                      ),
                    ],
                  )),
                ],
              ),
            ),
            Spacer(),
            Container(
              width: double.infinity,
              color: Colors.white,
              child: Container(
                margin: EdgeInsets.only(left: 15, right: 15, top: 10, bottom: 15),
                height: 37,
                child: MaterialButton(
                  color: Theme.of(context).primaryColor,
                  textColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  onPressed: () async {
                    myTeamAdd();
                  },
                  child: Text("确定新增"),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
