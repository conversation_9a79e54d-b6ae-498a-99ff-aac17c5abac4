import 'dart:io';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/ReposDao.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/utils/ThrottleUtil.dart';
import 'package:cabinet_flutter_app/page/login/AgreementPage.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/BottomBtnWidget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';

import '../../common/dao/CodeRegExpDao.dart';

class SettingPage extends StatefulWidget {
  SettingPage({Key? key}) : super(key: key);

  @override
  _SettingPage createState() => _SettingPage();
}

enum SettingMenuType {
  version,
  changePwd,
  checkRealName,
  clearCache,
  sjSwitch,
  keyboardSwitch,
  logOff,
  pdaSetting,
  fixStorage,
  privacyProtocol,
  optimizeNetwork
}

class _SettingPage extends State<SettingPage> {
  List menus = [
    {"label": "当前版本", "value": SettingMenuType.version, 'subTitle': Platform.isIOS ? '' : '检测更新'},
    {"label": "修改登录密码", "value": SettingMenuType.changePwd, 'subTitle': ''},
    {"label": "实名验证", "value": SettingMenuType.checkRealName, 'subTitle': '验证'},
    {"label": "清除缓存", "value": SettingMenuType.clearCache, 'subTitle': ''},
    {"label": "收件功能", "value": SettingMenuType.sjSwitch, 'subTitle': ''},
    {"label": "pda模式", "value": SettingMenuType.pdaSetting, 'subTitle': ''},
    {"label": "网络优化", "value": SettingMenuType.optimizeNetwork, 'subTitle': ''},
    {"label": "自定义键盘", "value": SettingMenuType.keyboardSwitch, 'subTitle': ''},
    {"label": "权限修复", "value": SettingMenuType.fixStorage, 'subTitle': ''},
    {"label": "隐私政策", "value": SettingMenuType.privacyProtocol, 'subTitle': ''},
    {"label": "注销账号", "value": SettingMenuType.logOff, 'subTitle': ''},
  ];
  String version = '';
  ThrottleUtil throttleUtil = ThrottleUtil();
  bool isSwitchSend = false;
  bool isPda = false;
  bool isOptimizeNetwork = true;
  bool isCustomerKeyborad = true;

  @override
  void initState() {
    super.initState();
    this.init();
  }

  init() async {
    isPda = await CheckUtils.isPda();
    isOptimizeNetwork = await CheckUtils.isOptimizeNetwork();
    isCustomerKeyborad = await CheckUtils.isCustomerKeyboard();
    isSwitchSend = await LocalStorage.get(DefaultConfig().configs.HAS_SWITCH_SEND, isPrivate: true) == 1 ? true : false;
    await getVersion();
    bool isCourier = CheckUtils.isCourier(context);
    if (!isCourier) {
      int index = menus.indexWhere((item) => item['value'] == SettingMenuType.sjSwitch);
      if (index != -1) {
        menus.removeAt(index);
      }
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  getVersion() async {
    String _version = await CheckUtils.getVersion();
    setState(() {
      version = _version;
    });
  }

  switchSend(bool isOpen, type) async {
    if (type == SettingMenuType.sjSwitch) {
      Map<String, dynamic> info = {'switchSend': isOpen ? 1 : 0};
      var res = await UserDao.switchSend(info);
      if (res != null && res.result) {
        isSwitchSend = isOpen;
        Store<AppState> store = StoreProvider.of(context);
        UserDao.getUserInfo(store);
        setState(() {});
      }
    } else if (type == SettingMenuType.pdaSetting) {
      isPda = isOpen;
      await CheckUtils.saveIsPda(isPda, isForce: true);
      setState(() {});
    } else if (type == SettingMenuType.optimizeNetwork) {
      isOptimizeNetwork = isOpen;
      await CheckUtils.saveIsOptimizeNetwork(isOptimizeNetwork, isForce: true);
      setState(() {});
    } else if (type == SettingMenuType.keyboardSwitch) {
      isCustomerKeyborad = isOpen;
      await CheckUtils.saveIsCustomerKeyborad(isCustomerKeyborad);
      setState(() {});
    }
  }

  toNext(type) {
    switch (type) {
      case SettingMenuType.clearCache:
        CommonUtils.clearCache(context, isCheck: true);
        break;
      case SettingMenuType.checkRealName:
        NavigatorUtils.goMyRealName(context);
        break;
      case SettingMenuType.changePwd:
        NavigatorUtils.goModifyPwdPage(context);
        break;
      case SettingMenuType.version:
        ReposDao.getNewsVersion(context, isForce: true);
        break;
      case SettingMenuType.fixStorage:
        scanPlugin.fixStorageAccess();
        break;
      case SettingMenuType.privacyProtocol:
        Navigator.push(context, new MaterialPageRoute(builder: (context) {
          return AgreementPage(
              url: DefaultConfig().configs.WEB_URL + DefaultConfig().configs.USER_AGREE1,
              isLocalUrl: false,
              title: '隐私政策');
        }));
        return;
      case SettingMenuType.logOff:
        CommonUtils.customConfirmByReason(context, '是否注销账号？', (backReason) async {
          if (backReason != '' && backReason != null) {
            Map<String, dynamic> info = {'content': backReason};
            var res = await UserDao.delUser(info);
            if (res.result) {
              Navigator.of(context).pop();
              CommonUtils.isSaveDialog(context, (isTrue) {
                // if (isTrue) {
                //   UserDao.logout(context);
                // }
              }, content: res.data, showCancel: false, successText: '确认');
            }
          }
        },
            title: '账号注销',
            changeText: 'logOff',
            warningText: '注销后账号无法恢复，请谨慎操作',
            showClose: false,
            showInput: true,
            confirmText: '注销');
        break;
    }
  }

  buildList() {
    List<Widget> widgets = [];
    menus.forEach((item) {
      widgets.add(Material(
        child: InkWell(
          onTap: () {
            toNext(item['value']);
          },
          child: new Container(
            height: 50,
            decoration: new BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: BorderSide(
                  width: 1.0,
                  color: Color(0xFFF4F4F4),
                ),
              ),
            ),
            padding: EdgeInsets.fromLTRB(12.0, 8.0, 8.0, 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(
                  '${item['label']}',
                  style: TextStyle(
                      color: item['value'] == SettingMenuType.logOff ? Colors.red : Colors.black, fontSize: 15.0),
                ),
                Padding(padding: EdgeInsets.only(left: 5)),
                Offstage(
                  offstage: item['value'] != SettingMenuType.version,
                  child: Text(
                    'v' + version,
                    style: TextStyle(color: Colors.black, fontSize: 15.0),
                  ),
                ),
                Expanded(
                  child: item['value'] != SettingMenuType.sjSwitch &&
                          item['value'] != SettingMenuType.pdaSetting &&
                          item['value'] != SettingMenuType.optimizeNetwork &&
                          item['value'] != SettingMenuType.keyboardSwitch
                      ? Container(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: <Widget>[
                              buildSubTitle(item['value'], item['subTitle']),
                              Offstage(
                                offstage: Platform.isIOS && item['value'] == SettingMenuType.version,
                                child: Icon(
                                  Icons.arrow_forward_ios_sharp,
                                  size: 18.0,
                                  color: Color(0xFF999999),
                                ),
                              )
                            ],
                          ),
                        )
                      : Container(
                          padding: EdgeInsets.only(left: 15, top: 7, right: 0, bottom: 7),
                          color: Colors.white,
                          child: Row(
                            children: [
                              Expanded(
                                  child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Container(
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      children: <Widget>[
                                        Transform.scale(
                                          scale: 0.7,
                                          child: CupertinoSwitch(
                                              value: getBoolRender(item['value']),
                                              activeColor: Theme.of(context).primaryColor,
                                              trackColor: Colors.grey.shade200,
                                              onChanged: (bool isOpen) async {
                                                switchSend(isOpen, item['value']);
                                              }),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              )),
                            ],
                          ),
                        ),
                )
              ],
            ),
          ),
        ),
      ));
    });
    return widgets;
  }

  getBoolRender(SettingMenuType type) {
    if (type == SettingMenuType.sjSwitch) {
      return isSwitchSend;
    } else if (type == SettingMenuType.pdaSetting) {
      return isPda;
    } else if (type == SettingMenuType.optimizeNetwork) {
      return isOptimizeNetwork;
    } else if (type == SettingMenuType.keyboardSwitch) {
      return isCustomerKeyborad;
    }
  }

  Widget buildSubTitle(type, String subTitle) {
    String _subTitle = subTitle;
    if (type == SettingMenuType.checkRealName) {
      Store<AppState> store = StoreProvider.of(context);
      UserEntity? user = store.state.userInfo;
      _subTitle = '未验证';
      if (CheckUtils.isNotNull(user?.hasReal)) {
        _subTitle = user?.hasReal == 1 ? '已验证' : '未验证';
      }
    }
    Widget widget = Container(
      child: Text(_subTitle, style: TextStyle(color: Theme.of(context).primaryColor)),
    );
    return widget;
  }

  @override
  Widget build(BuildContext context) {
    return new Material(
      child: new Scaffold(
          backgroundColor: DefaultConfig().configs.BG_COLOR,
          appBar: new AppCustomerBar(
            color: DefaultConfig().configs.WHITE_COLOR,
            iconColor: DefaultConfig().configs.BLACK_COLOR,
            title: new AppbarTitle(
              title: '我的设置',
              isCenter: true,
            ),
            actions: <Widget>[
              new Container(
                width: 60.0,
              )
            ],
          ),
          body: new Column(
            children: <Widget>[
              Container(
                width: MediaQuery.of(context).size.width,
                height: 130,
                color: Colors.white,
                margin: EdgeInsets.only(top: 10, bottom: 10),
                padding: EdgeInsets.only(top: 20, bottom: 20),
                child: LocalImageUtil.getImageAsset('logo',
                    isChannel: true, width: MediaQuery.of(context).size.width * 0.7),
              ),
              Expanded(
                  child: SingleChildScrollView(
                      physics: AlwaysScrollableScrollPhysics(),
                      child: Column(
                        children: buildList(),
                      ))),
              BottomBtnWidget(
                  type: ButtonType.info,
                  showShadow: false,
                  title: '切换账户',
                  action: () => throttleUtil.throttle(() async {
                        await NavigatorUtils.goTSwitchUsersPage(context);
                      })),
              BottomBtnWidget(
                  showShadow: false,
                  title: '退出登录',
                  action: () => throttleUtil.throttle(() {
                        UserDao.logout(context);
                      })),
            ],
          )),
    );
  }
}
