import 'dart:convert';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/NoResult.dart';
import 'package:cabinet_flutter_app/widget/myDialog/MyDialog.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CustomerDropDownSelectWidget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';

class UserListBindPage extends StatefulWidget {
  UserListBindPage({Key? key}) : super(key: key);

  @override
  _UserListBindPageState createState() => _UserListBindPageState();
}

class _UserListBindPageState extends State<UserListBindPage> {
  List<dynamic> list = [];
  Map<dynamic, dynamic> brandMap = {
    'YZ_DEFAULT': '默认',
    'YZ_MM': '妈妈驿站',
    'YZ_TX': '兔喜',
    'YZ_PDD': '多多驿站',
    'YZ_YDCS': '韵达超市',
    'YZ_MZ': '喵站',
  };
  Map<dynamic, dynamic> newBrandMap = {};
  bool hasPdd = false;
  String? shopId;
  String other = 'YZ_DEFAULT';
  String configId = '';
  List ebusiness = [
    {"channel": 'EB_PDD', "max": 500, "isOpen": false},
    {"channel": 'TAOBAO', "max": 500, "isOpen": false}
  ];
  List express = [];

  @override
  void initState() {
    super.initState();
    _getUserInfo();
    getRuleDetail();
  }

  getRuleDetail() async {
    shopId = await LocalStorage.get(DefaultConfig().configs.SHOP_ID, isPrivate: true);
    Map<String, dynamic> info = {
      'shopId': shopId,
    };
    var res = await UserDao.ruleDetail(info);
    if (res.data != '') {
      configId = res.data['id'];
      var obj = jsonDecode(res.data['rule']);
      if (obj != null) {
        other = obj['other'] != null ? obj['other'] : "YZ_DEFAULT";
        if (obj['express'] != '{}') {
          express = [];
          for (var colour in obj['express'].keys) {
            express.add({
              "brandCode": colour,
              "channels": obj['express'][colour],
            });
          }
        }
        if (obj['ebusiness'] != '{}') {
          ebusiness = [];
          for (var colour in obj['ebusiness'].keys) {
            ebusiness.add({
              "channel": colour,
              "max": obj['ebusiness'][colour]['max'],
              "isOpen": obj['ebusiness'][colour]['isOpen'] == 1,
            });
          }
        }
      }
    }
    setState(() {});
    print(express);
    print(ebusiness);
  }

  _getUserInfo() async {
    Map<String, dynamic> info = {};
    var res = await UserDao.accountList(info);
    if (res != null && res.result) {
      res.data.forEach((element) {
        if (element['channel'] == 'YZ_PDD') {
          hasPdd = true;
        }
        newBrandMap[element['channel']] = brandMap[element['channel']];
      });
    }
    brandMap = {
      'YZ_DEFAULT': '默认',
      'YZ_MM': '妈妈驿站',
      'YZ_TX': '兔喜',
      'YZ_YDCS': '韵达超市',
      'YZ_MZ': '喵站',
    };
    setState(() {});
  }

  saveUserListBind() async {
    ebusiness.forEach((item) {
      if (item['max'] == null || item['max'] == '') {
        item['max'] = 500;
      }
    });
    var newExpress = {};
    var newEbusiness = {};
    express.forEach((item) {
      newExpress[item['brandCode']] = item['channels'];
    });
    this.ebusiness.forEach((item) {
      newEbusiness[item['channel']] = {"max": item['max'], "isOpen": item['isOpen'] ? 1 : 0};
    });
    setState(() {});
    LoadingUtil(
      status: '数据保存中...',
    ).show(context);
    Map<String, dynamic> info = {
      'shopId': shopId,
      'rule': jsonEncode({
        'other': other,
        'express': newExpress,
        'ebusiness': newEbusiness,
      }),
      "ruleSwitch": 1,
      'id': configId,
    };
    var res = await UserDao.ruleSaveOrUpdate(info);
    LoadingUtil.dismiss(context);
    if (res.result) {
      Fluttertoast.showToast(msg: '保存成功');
    }
  }

  requestRefresh() async {
    return await getRuleDetail();
  }

  Future<Null> onFresh() async {
    getRuleDetail();
  }

  @override
  void dispose() {
    super.dispose();
  }

  ebusinessList() {
    List<Widget> widgets = [];
    List.generate(ebusiness.length, (index) {
      Map<String, dynamic> element = ebusiness[index];
      TextEditingController maxController = TextEditingController();
      maxController.text = element['max'].toString();
      widgets.add(Column(
        children: [
          Container(
            padding: EdgeInsets.only(left: 5),
            child: Row(
              children: [
                InkWell(
                  onTap: () {
                    FocusManager.instance.primaryFocus?.unfocus();
                    if (element['channel'] == 'EB_PDD') {
                      setState(() {
                        element['isOpen'] = !element['isOpen'];
                      });
                    } else {
                      Fluttertoast.showToast(msg: '暂不支持');
                    }
                  },
                  child: Container(
                    height: 30.0,
                    child: Container(
                      width: 10.0,
                      height: 10.0,
                      margin: EdgeInsets.fromLTRB(0, 0.0, 10.0, 0.0),
                      child: Checkbox(
                        value: element['isOpen'],
                        activeColor: Theme.of(context).primaryColor,
                        onChanged: (bool? value) {},
                      ),
                    ),
                  ),
                ),
                Text(element['channel'] == 'EB_PDD' ? '是否开启拼多多入多多驿站' : '是否开启淘宝件入菜鸟驿站'),
                SizedBox(width: 10),
                Text('数量：'),
                Container(
                    width: 60,
                    decoration: BoxDecoration(
                      border: Border.all(
                        width: 1,
                      ),
                      borderRadius: BorderRadius.circular(5),
                    ),
                    child: Column(
                      children: [
                        TextField(
                          onChanged: (String value) {
                            ebusiness[index]['max'] = value;
                          },
                          controller: maxController,
                          keyboardType: TextInputType.number,
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(RegExp(r'[0-9]')) //设置只允许输入数字
                          ],
                          decoration: InputDecoration(
                            counterText: '',
                            border: InputBorder.none,
                            isCollapsed: true,
                            contentPadding: EdgeInsets.all(5.0),
                          ),
                          style: TextStyle(
                            fontSize: 14.0,
                            color: Colors.black,
                          ),
                          maxLength: 3,
                        )
                      ],
                    ))
              ],
            ),
          ),
          SizedBox(height: 10),
        ],
      ));
    });
    return Column(children: widgets);
  }

  updateExpress(Map<String, dynamic> newExpress, {expressIndex = null}) async {
    FocusManager.instance.primaryFocus?.unfocus();
    var brandCode = newExpress['brandCode'] ?? '';
    List channels = newExpress['channels'] ??
        [
          {"max": 500, "channel": ''}
        ];
    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (_context, state) {
            List<Widget> widgets = [];
            List.generate(channels.length, (index) {
              TextEditingController maxController = TextEditingController();
              maxController.text = channels[index]['max'].toString();
              widgets.add(
                Column(
                  children: [
                    Row(
                      children: [
                        Text('网关：'),
                        Container(
                            width: 100,
                            padding: EdgeInsets.fromLTRB(5.0, 0.0, 5.0, 0.0),
                            decoration: BoxDecoration(
                              border: Border.all(
                                width: 1,
                              ),
                              borderRadius: BorderRadius.circular(5),
                            ),
                            child: CustomerDropDownSelectWidget(
                              map: newBrandMap,
                              value: channels[index]['channel'],
                              cb: (item) {
                                FocusManager.instance.primaryFocus?.unfocus();
                                bool isChange = true;
                                channels.forEach((element) {
                                  if (element['channel'] == item) {
                                    isChange = false;
                                  }
                                });
                                if (isChange) {
                                  state(() {
                                    channels[index]['channel'] = item;
                                  });
                                } else {
                                  Fluttertoast.showToast(msg: '网关已存在');
                                }
                              },
                            )),
                        Offstage(
                          offstage: channels.length <= 1,
                          child: Row(
                            children: [
                              Text('数量：'),
                              Container(
                                  padding: EdgeInsets.all(5),
                                  width: 60,
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      width: 1,
                                    ),
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                  child: Column(
                                    children: [
                                      TextField(
                                        onChanged: (String value) {
                                          channels[index]['max'] = value;
                                        },
                                        controller: maxController,
                                        keyboardType: TextInputType.number,
                                        inputFormatters: [
                                          FilteringTextInputFormatter.allow(RegExp(r'[0-9]')) //设置只允许输入数字
                                        ],
                                        decoration: InputDecoration(
                                          counterText: '',
                                          border: InputBorder.none,
                                          isCollapsed: true,
                                          contentPadding: EdgeInsets.all(5.0),
                                        ),
                                        style: TextStyle(
                                          fontSize: 14.0,
                                          color: Colors.black,
                                        ),
                                        maxLength: 3,
                                      )
                                    ],
                                  ))
                            ],
                          ),
                        ),
                        SizedBox(width: 10),
                        InkWell(
                            onTap: () {
                              channels.removeAt(index);
                              state(() {});
                            },
                            child: Container(
                                width: 60.0,
                                padding: EdgeInsets.all(10.0),
                                decoration: BoxDecoration(
                                  color: Theme.of(context).primaryColor,
                                  borderRadius: BorderRadius.circular(5),
                                ),
                                child: Column(
                                  children: [
                                    Text(
                                      '删除',
                                      style: TextStyle(
                                        color: Colors.white,
                                      ),
                                    ),
                                  ],
                                ))),
                      ],
                    ),
                    SizedBox(height: 10),
                  ],
                ),
              );
            });
            return MyDialog(
              width: MediaQuery.of(context).size.width - 10,
              autoClose: false,
              showClose: false,
              title: '驿站网关',
              content: '',
              buttons: ['取消', '确认'],
              contentWidget: Column(
                children: [
                  Container(
                      child: Row(children: [
                    Text('快递品牌：'),
                    Container(
                        width: 100,
                        padding: EdgeInsets.fromLTRB(5.0, 0.0, 5.0, 0.0),
                        decoration: BoxDecoration(
                          border: Border.all(
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: CustomerDropDownSelectWidget(
                          map: DefaultConfig().configs.expressChannel,
                          value: brandCode,
                          cb: (item) {
                            FocusManager.instance.primaryFocus?.unfocus();
                            bool isChange = true;
                            express.forEach((element) {
                              if (element['brandCode'] == item) {
                                isChange = false;
                              }
                            });
                            if (isChange) {
                              state(() {
                                brandCode = item;
                              });
                            } else {
                              Fluttertoast.showToast(msg: '快递品牌已存在');
                            }
                          },
                        )),
                  ])),
                  SizedBox(height: 10),
                  Row(
                    children: [
                      Text(
                        '驿站',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                      ),
                      SizedBox(width: 10),
                      InkWell(
                          onTap: () {
                            channels.add({
                              'channel': '',
                              'max': 500,
                            });
                            state(() {});
                          },
                          child: Container(
                              width: 60.0,
                              padding: EdgeInsets.all(5.0),
                              decoration: BoxDecoration(
                                color: Theme.of(context).primaryColor,
                                borderRadius: BorderRadius.circular(5),
                              ),
                              child: Column(
                                children: [
                                  Text(
                                    '添加',
                                    style: TextStyle(
                                      color: Colors.white,
                                    ),
                                  ),
                                ],
                              ))),
                    ],
                  ),
                  SizedBox(height: 10),
                  Expanded(
                      child: ListView(
                    children: widgets,
                  ))
                ],
              ),
              onTap: (index, context_) {
                FocusManager.instance.primaryFocus?.unfocus();
                if (index == 1) {
                  bool isChange = true;
                  if (brandCode == '') {
                    Fluttertoast.showToast(msg: '快递品牌不能为空');
                    return;
                  }
                  if (channels.length == 0) {
                    Fluttertoast.showToast(msg: '驿站不能为空');
                    return;
                  }
                  channels.forEach((element) {
                    if (element['channel'] == '') {
                      Fluttertoast.showToast(msg: '驿站不能为空');
                      isChange = false;
                      return;
                    }
                    if (element['max'] == '') {
                      Fluttertoast.showToast(msg: '数量不能为空');
                      isChange = false;
                      return;
                    }
                  });
                  Map<String, dynamic> replace = {
                    'brandCode': brandCode,
                    'channels': channels,
                  };
                  if (isChange) {
                    if (expressIndex != null) {
                      express[expressIndex] = replace;
                    } else {
                      express.add(replace);
                    }
                    Navigator.pop(context_);
                  }
                } else {
                  Navigator.pop(context_);
                }
              },
            );
          });
        });
    setState(() {});
  }

  expressList() {
    List<Widget> widgets = [];
    if (express.length == 0) {
      widgets.add(Container(
        padding: EdgeInsets.all(10),
        child: NoResult(
            size: 90,
            subWidget:
                Container(padding: EdgeInsets.only(top: 10), child: Text('请添加网关配置', style: AppConstant.smallSubText))),
      ));
    }
    List.generate(express.length, (index) {
      Map<String, dynamic> element = express[index];
      List<Widget> widgets2 = [];
      widgets2.add(
        Text("${DefaultConfig().configs.EXPRESS2[element['brandCode']]}入"),
      );
      element['channels'].forEach((item) {
        widgets2.add(Wrap(
          children: [
            Offstage(
              offstage: element['channels'].length <= 1,
              child: Text('再入'),
            ),
            Text(
              "${DefaultConfig().configs.brandMap[item['channel']]}",
              style: TextStyle(
                color: Colors.black,
                fontSize: 14,
              ),
            ),
            Offstage(
              offstage: element['channels'].length <= 1,
              child: Text('${item['max']}件；'),
            ),
            Offstage(
              offstage: element['channels'].length > 1,
              child: Text('；'),
            ),
          ],
        ));
      });
      widgets2.add(
        Offstage(
          offstage: element['channels'].length <= 1,
          child: Text('依次循环！'),
        ),
      );
      widgets2.add(Row(children: [
        InkWell(
            onTap: () {
              updateExpress(element, expressIndex: index);
            },
            child: Container(
                width: 60.0,
                padding: EdgeInsets.all(5.0),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Column(
                  children: [
                    Text(
                      '编辑',
                      style: TextStyle(
                        color: Colors.white,
                      ),
                    ),
                  ],
                ))),
        SizedBox(width: 10),
        InkWell(
            onTap: () {
              setState(() {
                express.removeAt(index);
              });
            },
            child: Container(
                width: 60.0,
                padding: EdgeInsets.all(5.0),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Column(
                  children: [
                    Text(
                      '删除',
                      style: TextStyle(
                        color: Colors.white,
                      ),
                    ),
                  ],
                )))
      ]));
      widgets.add(Column(
        children: [
          Wrap(
            children: widgets2,
          ),
          SizedBox(height: 10),
        ],
      ));
    });
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: widgets);
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        // ignore: missing_return
        onWillPop: () async {
          Navigator.pop(context);
          return true;
        },
        child: Scaffold(
            backgroundColor: DefaultConfig().configs.BG_COLOR,
            appBar: new AppCustomerBar(
              title: AppbarTitle(
                title: '账号绑定',
                isCenter: true,
              ),
              actions: [
                Container(
                  width: 60,
                  child: TextButton(
                    onPressed: () async {
                      saveUserListBind();
                    },
                    child: Text(
                      '保存',
                    ),
                  ),
                ),
              ],
            ),
            body: RefreshIndicator(
                onRefresh: () async {
                  // await getRuleDetail();
                },
                child: Container(
                    child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                      SizedBox(height: 10),
                      Container(
                        padding: EdgeInsets.fromLTRB(10, 0, 10, 0),
                        child: Text('电商网关', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
                      ),
                      SizedBox(height: 10),
                      Container(
                          padding: EdgeInsets.fromLTRB(10, 0, 10, 0),
                          child: Text(
                            '配置电商网关后，电商平台件优先使用电商网关！数量达标后，将匹配驿站网关规则！',
                            style: TextStyle(fontSize: 13, color: Theme.of(context).primaryColor),
                          )),
                      SizedBox(height: 10),
                      Container(
                        margin: EdgeInsets.only(bottom: 10),
                        padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
                        decoration: BoxDecoration(
                          color: Colors.white,
                        ),
                        child: ebusinessList(),
                      ),
                      Container(
                          padding: EdgeInsets.fromLTRB(10, 0, 10, 0),
                          child: Row(
                            children: [
                              Text('驿站网关', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
                              SizedBox(width: 10),
                              InkWell(
                                  onTap: () {
                                    FocusManager.instance.primaryFocus?.unfocus();
                                    updateExpress({});
                                  },
                                  child: Container(
                                      width: 60.0,
                                      padding: EdgeInsets.all(5.0),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).primaryColor,
                                        borderRadius: BorderRadius.circular(5),
                                      ),
                                      child: Column(
                                        children: [
                                          Text(
                                            '添加',
                                            style: TextStyle(
                                              color: Colors.white,
                                            ),
                                          ),
                                        ],
                                      )))
                            ],
                          )),
                      SizedBox(height: 10),
                      Container(
                          padding: EdgeInsets.fromLTRB(10, 0, 10, 0),
                          child: Text(
                            '例如：中通网关勾选了兔喜(每轮最大入库数为50)和多多驿站(每轮最大入库数为30)那么在中通包裹入库时,会先入到兔喜,入完50单后,再入多多驿站,入完30单后,重新计数,又再入兔喜,依次循环！如果只有一个网关,所有的都走这个网关！',
                            style: TextStyle(fontSize: 13, color: Theme.of(context).primaryColor),
                          )),
                      SizedBox(height: 10),
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                        ),
                        padding: EdgeInsets.all(10),
                        child: Row(
                          children: [
                            Text('驿站默认网关：'),
                            Container(
                                width: 100,
                                padding: EdgeInsets.fromLTRB(5.0, 0.0, 5.0, 0.0),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    width: 1,
                                  ),
                                  borderRadius: BorderRadius.circular(5),
                                ),
                                child: CustomerDropDownSelectWidget(
                                  map: brandMap,
                                  value: other,
                                  cb: (item) {
                                    setState(() {
                                      other = item;
                                    });
                                  },
                                )),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: Colors.white,
                          ),
                          child: SingleChildScrollView(
                            child: expressList(),
                          ),
                        ),
                      )
                    ])))));
  }
}
