import 'package:cabinet_flutter_app/common/config/Config.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/user_info_entity.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/page/my/UserUpdatePage.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/NoResult.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class UserListPage extends StatefulWidget {
  UserListPage({Key? key}) : super(key: key);

  @override
  _UserListPageState createState() => _UserListPageState();
}

class _UserListPageState extends State<UserListPage> {
  List<dynamic> list = [];

  @override
  void initState() {
    super.initState();
    _getUserInfo();
  }

  _getUserInfo() async {
    Map<String, dynamic> info = {};
    var res = await UserDao.accountList(info);
    if (res != null && res.result) {
      list = res.data;
    }
    setState(() {});
  }

  requestRefresh() async {
    return await _getUserInfo();
  }

  Future<Null> onFresh() async {
    _getUserInfo();
  }

  @override
  void dispose() {
    super.dispose();
  }

  _render(index) {
    UserInfoEntity entity = UserInfoEntity.fromJson(list[index]);
    return InkWell(
      onTap: () async {
        Navigator.push(
                context,
                new CupertinoPageRoute(
                    builder: (context) => new UserUpdatePage(entity, 'update'),
                    settings: RouteSettings(name: 'UserUpdatePage')))
            .then((value) => _getUserInfo());
      },
      child: Container(
          color: Colors.white,
          padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
          margin: EdgeInsets.only(bottom: 10),
          child: Stack(children: [
            Positioned(
                right: 15,
                top: 0,
                child: Container(
                  padding: EdgeInsets.fromLTRB(5, 2, 5, 2),
                  decoration: BoxDecoration(
                      color: entity.verified == 1 ? Theme.of(context).primaryColor : Colors.red,
                      borderRadius: BorderRadius.all(Radius.circular(5))),
                  child: Text('${entity.verified == 1 ? '正常' : '未校验'}',
                      style: TextStyle(color: Colors.white, fontSize: 12)),
                )),
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  child: CircleAvatar(
                    radius: 10.0,
                    backgroundColor: Colors.white,
                    backgroundImage: AssetImage(CommonUtils.getExpressLogo(entity.channel)),
                  ),
                  margin: EdgeInsets.only(right: 20),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '类型：${Config().brandType[entity.type]}',
                      ),
                      entity.type == "2"
                          ? Text(
                              '归属点位：${entity.cabinetNameList}',
                            )
                          : Container(),
                      Text(
                        '登录名：${entity.loginName}',
                      ),
                      // Text(
                      //   '姓名：${entity.name != '' ? entity.name : '暂无'}',
                      // ),
                      Text(
                        '驿站品牌：${Config().brandMap[entity.channel]}',
                      ),
                      Text(
                        '手机号码：${entity.mobile != '' ? entity.mobile : '暂无'}',
                      ),
                      Text(
                        '店铺名称：${entity.stationName != '' ? entity.stationName : '暂无'}',
                      ),
                      Text(
                        '地址：${entity.stationAddress != '' ? entity.stationAddress : '暂无'}',
                      ),
                      Text(
                        '设备名称：${entity.deviceModel != '' ? entity.deviceModel : '暂无'}',
                      ),
                      Text(
                        '设备号：${entity.deviceId != '' ? entity.deviceId : '暂无'}',
                      ),
                    ],
                  ),
                ),
                Container(
                  child: new Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: <Widget>[
                      new Container(),
                      new Icon(
                        Icons.arrow_forward_ios_sharp,
                        size: 18.0,
                        color: Color(0xFF999999),
                      )
                    ],
                  ),
                ),
              ],
            ),
          ])),
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        // ignore: missing_return
        onWillPop: () async {
          Navigator.pop(context);
          return true;
        },
        child: Scaffold(
            backgroundColor: DefaultConfig().configs.BG_COLOR,
            appBar: new AppCustomerBar(
              title: AppbarTitle(
                title: '账号管理',
                isCenter: true,
              ),
              actions: [
                Container(
                  width: 60,
                  child: TextButton(
                    onPressed: () async {
                      UserInfoEntity entity = UserInfoEntity.fromJson({});
                      Navigator.push(
                              context,
                              new CupertinoPageRoute(
                                  builder: (context) => new UserUpdatePage(entity, 'add'),
                                  settings: RouteSettings(name: 'UserUpdatePage')))
                          .then((value) => _getUserInfo());
                    },
                    child: Text(
                      '添加',
                    ),
                  ),
                ),
              ],
            ),
            body: RefreshIndicator(
              onRefresh: () async {
                await _getUserInfo();
              },
              child: list.length > 0
                  ? ListView.builder(
                      itemBuilder: (context, index) {
                        return _render(index);
                      },
                      itemCount: list.length,
                    )
                  : ListView.builder(
                      itemBuilder: (context, index) {
                        return Container(
                          height: 300,
                          child: NoResult(
                              size: 90,
                              subWidget: Container(
                                  padding: EdgeInsets.only(top: 10),
                                  child: Text('请添加账号', style: AppConstant.smallSubText))),
                        );
                      },
                      itemCount: 1,
                    ),
            )));
  }
}
