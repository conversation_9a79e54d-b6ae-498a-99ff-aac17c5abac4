import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/ThrottleUtil.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/MyTextFormField.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';

class CoMatchingBindPage extends StatefulWidget {
  CoMatchingBindPage({Key? key}) : super(key: key);

  @override
  _CoMatchingBindPageState createState() => _CoMatchingBindPageState();
}

class _CoMatchingBindPageState extends State<CoMatchingBindPage> {
  late UserEntity user;
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController loginNameController = TextEditingController();
  FocusNode passwordTextFieldNode = FocusNode();
  FocusNode loginNameTextFieldNode = FocusNode();
  var _userName = "";
  var _password = "";
  bool isPw = false;
  bool isUserName = false;
  ThrottleUtil throttleUtil = ThrottleUtil();

  @override
  void initState() {
    super.initState();
    getUserInfo();
    initParams();
  }

  initParams() async {
    passwordController.addListener(() {
      setState(() {
        if (passwordController.text != '') {
          isPw = true;
        } else {
          isPw = false;
        }
      });
    });
    loginNameController.addListener(() {
      setState(() {
        if (loginNameController.text.length > 0) {
          isUserName = true;
        } else {
          isUserName = false;
        }
      });
    });
    setState(() {});
  }

  getUserInfo() async {
    var res = await UserDao.getUserInfoLocal();
    if (res != null && res.result) {
      user = res.data;
    }
    setState(() {});
  }

  @override
  void dispose() {
    super.dispose();
  }

  buildLoginName() {
    return TextFormField(
      cursorColor: Theme.of(context).primaryColor,
      decoration: InputDecoration(
          labelText: '请输入共配登录用户',
          errorMaxLines: 1,
          suffixIcon: CheckUtils.isNotNull(_userName)
              ? Container(
                  width: 20.0,
                  height: 20.0,
                  child: new IconButton(
                    alignment: Alignment.center,
                    padding: const EdgeInsets.all(0.0),
                    iconSize: 18.0,
                    icon: Icon(Icons.cancel),
                    onPressed: () {
                      setState(() {
                        _userName = '';
                        loginNameController.value = TextEditingValue(text: '');
                      });
                    },
                  ),
                )
              : Text("")),
      onChanged: (String value) {
        setState(() {
          _userName = value;
        });
      },
      keyboardType: TextInputType.text,
      obscureText: false,
      controller: loginNameController,
      textInputAction: TextInputAction.next,
      focusNode: loginNameTextFieldNode,
      onEditingComplete: () {
        bool validator = _userName.length > 0;
        if (!validator) {
          Fluttertoast.showToast(msg: '请输入共配登录用户', gravity: ToastGravity.CENTER);
        } else {
          FocusScope.of(context).requestFocus(passwordTextFieldNode);
        }
      },
    );
  }

  MyTextFormField buildPassword() => MyTextFormField(
        labelText: "请输入密码",
        isPassword: true,
        controller: passwordController,
        focusNode: passwordTextFieldNode,
        keyboardType: TextInputType.text,
        onChanged: (value) {
          this.setState(() {
            _password = value;
          });
        },
        onEditingComplete: () {
          if (isPw && isUserName) {
            preLogin();
          }
        },
      );

  preLogin() {
    passwordTextFieldNode.unfocus();
    loginNameTextFieldNode.unfocus();
    SystemChannels.textInput.invokeMethod('TextInput.hide');
    if (isPw && isUserName) {
      login();
    }
  }

  login() async {
    if (_password.length == 0) {
      Fluttertoast.showToast(msg: '请输入密码');
      return;
    }
    LoadingUtil(
      status: '正在绑定...',
    ).show(context);
    Map<String, dynamic> info = {
      'loginName': _userName,
      'password': _password.trim(),
    };
    var res = await UserDao.bindGP(info);
    LoadingUtil.dismiss(context);
    if (res != null && res.result) {
      Fluttertoast.showToast(msg: '绑定成功');
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    bool smallScreen = MediaQuery.of(context).size.width * MediaQuery.of(context).devicePixelRatio < 480;
    var height = MediaQuery.of(context).size.height * .2;
    return WillPopScope(
        onWillPop: () async {
          Navigator.pop(context);
          return true;
        },
        child: Scaffold(
            backgroundColor: DefaultConfig().configs.BG_COLOR,
            appBar: new AppCustomerBar(
              title: AppbarTitle(
                title: '绑定共配',
                isCenter: true,
              ),
              actions: [
                Container(
                  width: 60,
                )
              ],
            ),
            body: Column(
              children: [
                Offstage(
                  offstage: smallScreen,
                  child: LocalImageUtil.getImageAsset('gp-login',
                      width: MediaQuery.of(context).size.width * .8, height: height, fit: BoxFit.fitWidth),
                ),
                Container(
                  height: smallScreen ? MediaQuery.of(context).size.height * .6 : MediaQuery.of(context).size.height * .32,
                  padding: EdgeInsets.only(bottom: smallScreen ? 0 : 20, top: smallScreen ? 80 : 0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.only(left: 30.0, top: 0.0, right: 30.0, bottom: 0),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.max,
                            children: <Widget>[buildLoginName(), Padding(padding: EdgeInsets.only(top: 0)), buildPassword()],
                          ),
                        ),
                      ),
                      Container(
                        width: double.infinity,
                        margin: EdgeInsets.only(left: 20, top: smallScreen ? 20 : 40, right: 20),
                        height: 47,
                        child: MaterialButton(
                          color: Theme.of(context).primaryColor,
                          disabledColor: Colors.grey.shade500,
                          textColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25.0),
                          ),
                          onPressed: isPw && isUserName
                              ? () => throttleUtil.throttle(() {
                                    preLogin();
                                  })
                              : null,
                          child: Text('确认绑定', style: TextStyle(fontSize: 18)),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Wrap(
                        alignment: WrapAlignment.start,
                        children: [
                          Container(
                            padding: EdgeInsets.only(left: 20, top: 0.0, right: 20, bottom: 10.0),
                            child: Text(
                              '绑定须知:',
                              style: TextStyle(
                                fontSize: 14,
                              ),
                            ),
                          )
                        ],
                      ),
                      Wrap(
                        alignment: WrapAlignment.start,
                        children: [
                          Container(
                            padding: EdgeInsets.only(left: 20, top: 0.0, right: 20, bottom: 10.0),
                            child: Text(
                              '  1.需要在共配系统注册账号',
                              style: TextStyle(
                                fontSize: 14,
                              ),
                            ),
                          )
                        ],
                      ),
                      Wrap(
                        alignment: WrapAlignment.start,
                        children: [
                          Container(
                            padding: EdgeInsets.only(left: 20, top: 0.0, right: 20, bottom: 10.0),
                            child: Text(
                              '  2.共配系统添加相关快递公司账号',
                              style: TextStyle(
                                fontSize: 14,
                              ),
                            ),
                          )
                        ],
                      ),
                      Wrap(
                        alignment: WrapAlignment.start,
                        children: [
                          Container(
                            padding: EdgeInsets.only(left: 20, top: 0.0, right: 20, bottom: 10.0),
                            child: Text(
                              '  3.输入共配系统账号密码确认绑定',
                              style: TextStyle(
                                fontSize: 14,
                              ),
                            ),
                          )
                        ],
                      ),
                      Wrap(
                        alignment: WrapAlignment.start,
                        children: [
                          Container(
                            padding: EdgeInsets.only(left: 20, top: 0.0, right: 20, bottom: 10.0),
                            child: Text(
                              '  4.每单服务费5厘，到派处理成功后自动扣款备注:',
                              style: TextStyle(
                                fontSize: 14,
                              ),
                            ),
                          )
                        ],
                      ),
                      Wrap(
                        alignment: WrapAlignment.start,
                        children: [
                          Container(
                            padding: EdgeInsets.only(left: 20, top: 0.0, right: 20, bottom: 10.0),
                            child: Text(
                              '  1,2步骤可以联系客服处理，把相关账号发给平台客服',
                              style: TextStyle(
                                fontSize: 14,
                              ),
                            ),
                          )
                        ],
                      ),
                    ],
                  ),
                )
              ],
            )));
  }
}
