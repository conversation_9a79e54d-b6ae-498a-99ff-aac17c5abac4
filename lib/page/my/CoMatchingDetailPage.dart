import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/gp_user_list_entity.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class CoMatchingDetailPage extends StatefulWidget {
  final GpUserListEntity gpUserListEntity;

  CoMatchingDetailPage(this.gpUserListEntity, {Key? key}) : super(key: key);

  @override
  _CoMatchingDetailPageState createState() => _CoMatchingDetailPageState();
}

class _CoMatchingDetailPageState extends State<CoMatchingDetailPage> {
  Map<dynamic, dynamic> brandMap = {
    'EMS': '中国邮政',
    'ZTO': '中通速递',
    'YTO': '圆通速递',
    'STO': '申通快递',
    'YUNDA': '韵达速递',
    'HTKY': '百世快递',
    'JT': '极兔速递',
    'FENGWANG': '丰网速运',
  };

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  loginText() {
    String text = '';
    if (widget.gpUserListEntity.company == 'STO') {
      text = '工号(后4位)';
    }
    if (widget.gpUserListEntity.company == 'YUNDA') {
      text = widget.gpUserListEntity.isYbx == 1 ? '登录名(手机号)' : '工号(4位)';
    }
    if (widget.gpUserListEntity.company == 'JT') {
      text = '工号';
    }
    if (widget.gpUserListEntity.company == 'EMS') {
      text = '工号';
    }
    if (widget.gpUserListEntity.company == 'YTO') {
      text = widget.gpUserListEntity.isXingzhe == 1 ? '工号(行者)' : '工号(尊者)';
    }
    if (widget.gpUserListEntity.company == 'HTKY') {
      text = '工号(Q9)';
    }
    if (widget.gpUserListEntity.company == 'ZTO') {
      text = '工号(掌中通)';
    }
    if (widget.gpUserListEntity.company == 'FENGWANG') {
      text = '工号';
    }
    return text;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        // ignore: missing_return
        onWillPop: () async {
          Navigator.pop(context);
          return true;
        },
        child: Scaffold(
            backgroundColor: DefaultConfig().configs.BG_COLOR,
            appBar: new AppCustomerBar(
              title: AppbarTitle(
                title: '${brandMap[widget.gpUserListEntity.company]}',
                isCenter: true,
              ),
              actions: [
                Container(
                  width: 60,
                ),
              ],
            ),
            body: Column(
              children: [
                Offstage(
                  offstage: false,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                        bottom: BorderSide(color: Colors.grey.shade100, width: 1.0),
                      ),
                    ),
                    padding: EdgeInsets.all(10),
                    height: 50,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        Text(
                          loginText(),
                          style: TextStyle(color: Colors.black, fontSize: 14),
                        ),
                        Text(
                          widget.gpUserListEntity.loginName,
                          style: TextStyle(color: Colors.black, fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                ),
                Offstage(
                  offstage: false,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                        bottom: BorderSide(color: Colors.grey.shade100, width: 1.0),
                      ),
                    ),
                    padding: EdgeInsets.all(10),
                    height: 50,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        Text(
                          '密码',
                          style: TextStyle(color: Colors.black, fontSize: 14),
                        ),
                        Text(
                          widget.gpUserListEntity.password,
                          style: TextStyle(color: Colors.black, fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                ),
                Offstage(
                  offstage: false,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                        bottom: BorderSide(color: Colors.grey.shade100, width: 1.0),
                      ),
                    ),
                    padding: EdgeInsets.all(10),
                    height: 50,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        Text(
                          '手机号',
                          style: TextStyle(color: Colors.black, fontSize: 14),
                        ),
                        Text(
                          widget.gpUserListEntity.mobile !=''  ? widget.gpUserListEntity.mobile : '暂无',
                          style: TextStyle(color: Colors.black, fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                ),
                Offstage(
                  offstage: false,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                        bottom: BorderSide(color: Colors.grey.shade100, width: 1.0),
                      ),
                    ),
                    padding: EdgeInsets.all(10),
                    height: 50,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        Text(
                          '网点编码',
                          style: TextStyle(color: Colors.black, fontSize: 14),
                        ),
                        Text(
                          widget.gpUserListEntity.siteCode !=''  ? widget.gpUserListEntity.siteCode : '暂无',
                          style: TextStyle(color: Colors.black, fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                ),
                Offstage(
                  offstage: false,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                        bottom: BorderSide(color: Colors.grey.shade100, width: 1.0),
                      ),
                    ),
                    padding: EdgeInsets.all(10),
                    height: 50,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        Text(
                          '三段码',
                          style: TextStyle(color: Colors.black, fontSize: 14),
                        ),
                        Text(
                          widget.gpUserListEntity.threeCode !=''  ? widget.gpUserListEntity.threeCode : '暂无',
                          style: TextStyle(color: Colors.black, fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                ),
                Offstage(
                  offstage: false,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                        bottom: BorderSide(color: Colors.grey.shade100, width: 1.0),
                      ),
                    ),
                    padding: EdgeInsets.all(10),
                    height: 50,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        Text(
                          '名称',
                          style: TextStyle(color: Colors.black, fontSize: 14),
                        ),
                        Text(
                          widget.gpUserListEntity.name !=''  ? widget.gpUserListEntity.name : '暂无',
                          style: TextStyle(color: Colors.black, fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                ),
                Offstage(
                  offstage: false,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                        bottom: BorderSide(color: Colors.grey.shade100, width: 1.0),
                      ),
                    ),
                    padding: EdgeInsets.all(10),
                    height: 50,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        Text(
                          '设备号',
                          style: TextStyle(color: Colors.black, fontSize: 14),
                        ),
                        Text(
                          widget.gpUserListEntity.deviceId !=''  ? widget.gpUserListEntity.deviceId : '暂无',
                          style: TextStyle(color: Colors.black, fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                ),
                Offstage(
                  offstage: false,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                        bottom: BorderSide(color: Colors.grey.shade100, width: 1.0),
                      ),
                    ),
                    padding: EdgeInsets.all(10),
                    height: 50,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        Text(
                          '设备品牌',
                          style: TextStyle(color: Colors.black, fontSize: 14),
                        ),
                        Text(
                          widget.gpUserListEntity.deviceName !=''  ? widget.gpUserListEntity.deviceName : '暂无',
                          style: TextStyle(color: Colors.black, fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            )));
  }
}
