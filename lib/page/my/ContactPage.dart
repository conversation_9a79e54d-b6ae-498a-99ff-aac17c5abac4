import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

class ContactPage extends StatefulWidget {
  @override
  _ContactPage createState() => _ContactPage();
}

class _ContactPage extends State<ContactPage> {
  @override
  void initState() {
    super.initState();
  }

  // 拨打客服电话
  makeCall() async {
    DateTime now = DateTime.now();
    DateTime date8 = DateTime.parse(new DateFormat("yyyy-MM-dd").format(DateTime.now()) + ' 08:00:00');
    DateTime date18 = DateTime.parse(new DateFormat("yyyy-MM-dd").format(DateTime.now()) + ' 18:00:00');
    DateTime date23 = DateTime.parse(new DateFormat("yyyy-MM-dd").format(DateTime.now()) + ' 23:00:00');
    String phoneNumber = 'tel:15556666047';
    if (now.isAfter(date8) && now.isBefore(date18)) {
      phoneNumber = 'tel:15556666047';
    }
    if (now.isAfter(date18) && now.isBefore(date23)) {
      phoneNumber = 'tel:15556666047';
    }
    if (phoneNumber == '') {
      Fluttertoast.showToast(msg: '当前不在营业时间哦~');
      return false;
    }
    if (await canLaunch(phoneNumber)) {
      await launch(phoneNumber);
    } else {
      CommonUtils.alert(context, dialogContent: '拨打电话出错');
    }
  }

  void callQQ({int number = 1709069938, bool isGroup = false}) async {
    String url = isGroup
        ? 'mqqapi://card/show_pslcard?src_type=internal&version=1&uin=$number&card_type=group&source=qrcode'
        : 'mqqwpa://im/chat?chat_type=wpa&uin=$number&version=1&src_type=web&web_src=oicqzone.com';
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      Fluttertoast.showToast(msg: '请检查是否安装QQ客户端');
    }
  }

  copyWechat() {
    Clipboard.setData(ClipboardData(text: '18110622322'));
    Fluttertoast.showToast(msg: '微信复制成功');
  }

  @override
  Widget build(BuildContext context) {
    return new Material(
      child: new Scaffold(
          backgroundColor: Colors.grey.shade100,
          appBar: new AppCustomerBar(
            title: new AppbarTitle(
              title: '联系客服',
              isCenter: true,
            ),
            actions: <Widget>[
              new Container(
                width: 60.0,
              )
            ],
          ),
          body: new Column(
            children: <Widget>[
              // Container(
              //   padding: EdgeInsets.fromLTRB(20, 25, 20, 25),
              //   margin: EdgeInsets.only(top: 10),
              //   color: Colors.white,
              //   child: Row(
              //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //     children: [
              //       Container(
              //         child: Column(
              //           crossAxisAlignment: CrossAxisAlignment.start,
              //           children: [
              //             Row(
              //               children: [
              //                 Padding(
              //                   padding: EdgeInsets.only(right: 5),
              //                   child: Image.asset(
              //                     'static/images/QQ.png',
              //                     width: 25,
              //                     height: 25,
              //                   ),
              //                 ),
              //                 Text(
              //                   '客服QQ',
              //                   style: TextStyle(
              //                     fontSize: 14,
              //                     color: Colors.grey.shade500,
              //                   ),
              //                 ),
              //               ],
              //             ),
              //             Padding(
              //               padding: EdgeInsets.only(top: 5),
              //             ),
              //             Text(
              //               '1709069938',
              //               style: TextStyle(
              //                 fontSize: 14,
              //                 color: Colors.black87,
              //               ),
              //             ),
              //           ],
              //         ),
              //       ),
              //       Material(
              //         borderRadius: BorderRadius.all(Radius.circular(5)),
              //         color: Colors.blueAccent,
              //         child: InkWell(
              //           onTap: () {
              //             callQQ();
              //           },
              //           child: Container(
              //             width: 90,
              //             padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
              //             alignment: Alignment.center,
              //             child: Text(
              //               '联系QQ',
              //               style: TextStyle(color: Colors.white, fontSize: 14),
              //             ),
              //           ),
              //         ),
              //       )
              //     ],
              //   ),
              // ),
              Container(
                padding: EdgeInsets.fromLTRB(20, 25, 20, 25),
                margin: EdgeInsets.only(top: 10),
                color: Colors.white,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Padding(
                                  padding: EdgeInsets.only(right: 5),
                                  child: LocalImageUtil.getImageAsset('noticeWx', width: 22)),
                              Text(
                                '客服微信',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey.shade500,
                                ),
                              ),
                            ],
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 5),
                          ),
                          Text(
                            '南京便利熊科技有限公司',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Material(
                      borderRadius: BorderRadius.all(Radius.circular(5)),
                      color: Colors.blueAccent,
                      child: InkWell(
                        onTap: () {
                          copyWechat();
                        },
                        child: Container(
                          width: 90,
                          padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
                          alignment: Alignment.center,
                          child: Text(
                            '复制微信号',
                            style: TextStyle(color: Colors.white, fontSize: 14),
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              ),
              Container(
                padding: EdgeInsets.fromLTRB(20, 15, 20, 15),
                margin: EdgeInsets.only(top: 10),
                color: Colors.white,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Padding(
                                  padding: EdgeInsets.only(right: 5),
                                  child: LocalImageUtil.getImageAsset('contact', width: 22)),
                              Text(
                                '客服电话',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey.shade500,
                                ),
                              ),
                            ],
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 10),
                          ),
                          Row(
                            children: [
                              Text(
                                '15556666047',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.black87,
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.only(left: 10),
                              ),
                              Text(
                                '0:00 ~ 24:00',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.black54,
                                ),
                              ),
                            ],
                          ),
                          // Row(
                          //   children: [
                          //     Text(
                          //       '15556666047',
                          //       style: TextStyle(
                          //         fontSize: 14,
                          //         color: Colors.black87,
                          //       ),
                          //     ),
                          //     Padding(
                          //       padding: EdgeInsets.only(left: 10),
                          //     ),
                          //     Text(
                          //       '18:00 ~ 23:00',
                          //       style: TextStyle(
                          //         fontSize: 14,
                          //         color: Colors.black54,
                          //       ),
                          //     ),
                          //   ],
                          // )
                        ],
                      ),
                    ),
                    Material(
                      borderRadius: BorderRadius.all(Radius.circular(5)),
                      color: Colors.blueAccent,
                      child: InkWell(
                        onTap: () {
                          makeCall();
                        },
                        child: Container(
                          width: 90,
                          padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
                          alignment: Alignment.center,
                          child: Text(
                            '拨打电话',
                            style: TextStyle(color: Colors.white, fontSize: 14),
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ],
          )),
    );
  }
}
