import 'dart:convert';
import 'dart:io';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/gp_user_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/wallet_entity.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';

import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/utils/SoundUtils.dart';
import 'package:cabinet_flutter_app/page/finance/CashOutPage.dart';
import 'package:cabinet_flutter_app/page/login/AgreementPage.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:redux/redux.dart';
import 'package:scan/scan.dart';

GlobalKey<_MyPageState> mineGlobalKey = GlobalKey();

class MyPage extends StatefulWidget {
  MyPage({Key? key}) : super(key: key);

  @override
  _MyPageState createState() => _MyPageState();
}

class _MyPageState extends State<MyPage> with AutomaticKeepAliveClientMixin {
  Scan scan = new Scan();
  int uploadTime = 0;
  bool _isPda = false;
  bool _isAi = false;
  late UserEntity? user;
  String _balance = '0.00';
  int _uploadTime = 0;
  String isOpenAutoUpload = 'Y';
  Map<dynamic, dynamic> brandMap = {};

  List menuListOut = [
    {'icon': 'brands', 'type': 'brandsManage', 'url': '', 'name': '快递品牌管理'},
    {'icon': 'shopCourier', 'type': 'shopSettle', 'url': '', 'name': '驿站结算'},
    {'icon': 'coMatching', 'type': 'coMatchingPage', 'url': '', 'name': '补到派管理'},
    {'icon': 'help', 'type': 'help', 'url': '', 'name': '帮助中心'},
    {'icon': 'setting', 'type': 'setting', 'url': '', 'name': '我的设置'},
    {'icon': 'feedback', 'type': 'feedback', 'url': '', 'name': '反馈建议'}
  ];
  List menuListIn = [
    {'icon': 'brands', 'type': 'brandsManage', 'url': '', 'name': '快递品牌管理'},
    {'icon': 'shopCourier', 'type': 'shopSettle', 'url': '', 'name': '驿站结算'},
    {'icon': 'team', 'type': 'teamManger', 'url': '', 'name': '团队管理'},
    {'icon': 'autoSign', 'type': 'settingAutoSign', 'url': '', 'name': '签收设置'},
    {'icon': 'coMatching', 'type': 'coMatchingPage', 'url': '', 'name': '补到派管理'},
    {'icon': 'userList', 'type': 'userListPage', 'url': '', 'name': '账号管理'},
    {'icon': 'userListBind', 'type': 'userListBindPage', 'url': '', 'name': '账号绑定'},
    {'icon': 'help', 'type': 'help', 'url': '', 'name': '帮助中心'},
    {'icon': 'setting', 'type': 'setting', 'url': '', 'name': '我的设置'},
    {'icon': 'feedback', 'type': 'feedback', 'url': '', 'name': '反馈建议'}
  ];
  List menuList = [];
  late WalletEntity data;
  int balance = 0;
  String balanceNum = '';
  int totalWithdrawalMoney = 0;
  String totalWithdrawalMoneyNum = '';
  int frozenMoney = 0;
  String frozenMoneyNum = '';
  bool isCourier = true;
  var hasAdmin, hasWallet;
  bool isShowBtn = true;

  @override
  void initState() {
    super.initState();
    this.init();
    onFresh();
  }

  init() async {
    ///4 室外柜  6室内柜
    hasAdmin = await LocalStorage.get(DefaultConfig().configs.HAS_ADMIN, isPrivate: true);
    hasWallet = await LocalStorage.get(DefaultConfig().configs.HAS_WALLET, isPrivate: true);
  }

  getLocalData() async {
    // 是否开启PDA
    var isPda = await LocalStorage.get(DefaultConfig().configs.IS_PDA);
    if (isPda != null) {
      setState(() {
        _isPda = isPda == 'Y';
      });
    }
    // 是否开启智能识别
    var isAi = await LocalStorage.get(DefaultConfig().configs.IS_AI);
    if (isAi != null) {
      setState(() {
        _isAi = isAi;
      });
    }
    // 自动回传的时间
    var uploadTime = await LocalStorage.get(DefaultConfig().configs.UPLOAD_TIME);
    if (uploadTime != null) {
      setState(() {
        _uploadTime = uploadTime;
      });
    }

    var site = await LocalStorage.get(DefaultConfig().configs.SITE_INFO);
    if (site != null) {
      Map siteInfo = jsonDecode(site);
      setState(() {
        isOpenAutoUpload = siteInfo['isOpenAutoUpload'] ?? 'Y';
      });
    }
  }

  Future<Null> onFresh() async {
    // await getUserBalance();
    await getWealthInfo();
    await getLocalData();
    await getBrandList();
  }

  getBrandList() async {
    var res = await UserDao.getBrandList();
    if (res != null && res.result) {
      brandMap = res.data;
    }
    if (mounted) {
      setState(() {});
    }
  }

  getWealthInfo() async {
    var res = await UserDao.getWealthInfo();
    if (res != null && res.result) {
      data = WalletEntity.fromJson(res.data);
      balance = data.balance!;
      balanceNum = (balance / 1000).toStringAsFixed(2);
      totalWithdrawalMoney = data.totalWithdrawalMoney!;
      totalWithdrawalMoneyNum = (data.totalWithdrawalMoney! / 1000).toStringAsFixed(2);
      frozenMoney = data.frozenMoney!;
      frozenMoneyNum = (data.frozenMoney! / 1000).toStringAsFixed(2);
      setState(() {});
    }
  }

  // Future<Null> getUserBalance() async {
  //   DataResult userRes = await UserDao.getUserBalance();
  //   if (userRes.result) {
  //     setState(() {
  //       _balance = (userRes.data / 1000).toString();
  //     });
  //   }
  // }

  setUploadTime(time) async {
    await LocalStorage.save(DefaultConfig().configs.UPLOAD_TIME, time);
    setState(() {
      _uploadTime = time;
    });
//    ScanDao.uploadByTime(context, true);
    Navigator.pop(context);
  }

  goSecondPage(type) async {
    switch (type) {
      case 'brandsManage':
        NavigatorUtils.goBrandsManage(context);
        break;
      case 'shopSettle':
        NavigatorUtils.goCourierShop(context);
        break;
      case 'teamManger':
        NavigatorUtils.goTeamMangerPage(context);
        break;
      case 'settingAutoSign':
        NavigatorUtils.goAutoSign(context);
      case 'setting':
        await NavigatorUtils.goSettingPage(context);
        break;
      case 'download':
        await NavigatorUtils.goDownloadAppPage(context);
        break;
      case 'coMatchingPage':
        var res = await UserDao.hasGpconfig();
        if (res != null && res.result) {
          if (res.data == null || res.data.isEmpty) {
            await NavigatorUtils.goCoMatchingBind(context);
          } else {
            GpUserEntity gpUserEntity = GpUserEntity.fromJson(res.data);
            await NavigatorUtils.goCoMatching(context, gpUserEntity.gpUserId);
          }
        }
        break;
      case 'userListPage':
        await NavigatorUtils.goUserListPage(context);
        break;
      case 'userListBindPage':
        await NavigatorUtils.goUserListBindPage(context);
        break;
      case 'help':
        Navigator.push(context, new MaterialPageRoute(builder: (context) {
          return AgreementPage(
              url: DefaultConfig().configs.WEB_URL + DefaultConfig().configs.HELP_CENTER, isLocalUrl: false, title: '帮助中心');
        }));
        break;
      case 'feedback':
        print('feedback');
        NavigatorUtils.goToFeedbackPage(context);
        break;
    }
  }

  @override
  Future<void> didChangeDependencies() async {
    super.didChangeDependencies();
    isCourier = CheckUtils.isCourier(context);
    if (!isCourier) {
      menuList = menuListIn;
      var userRes = await UserDao.getUserInfoLocal();
      if (userRes != null) {
        UserEntity? userEntity = userRes.data;
        if (userEntity?.hasAdmin == 1 && userEntity?.hasWallet == 1) {
          isShowBtn = true;
        } else {
          isShowBtn = false;
        }
      }
    } else {
      isShowBtn = isCourier;
      menuList = menuListOut;
    }
    if (Platform.isAndroid) {
      int index = menuList.indexWhere((v) => v['name'] == '软件分享');
      if (index < 0) {
        menuList.insert(menuList.length - 1, {'icon': 'share', 'type': 'download', 'url': '', 'name': '软件分享'});
      }
    }
    setState(() {});
  }

  /// 品牌选择
  selectBrand() {
    SoundUtils.audioPushFn(SoundUtils.CHOOSE_COMPANY);
    CommonUtils.showBottomBrandSelectModal(context, brandMap, (item) async {
      print(item);
      SoundUtils.audioPushFn(SoundUtils.BRAND_SOUND[item]!);
      if (item != null) {
        Map<String, dynamic> info = {'brandCode': item};
        var res = await UserDao.setBindCompanyBrand(info);
        if (res != null && res.result) {
          if (res.data) {
            Fluttertoast.showToast(msg: '绑定快递公司成功');
            Store<AppState> store = StoreProvider.of(context);
            UserDao.getUserInfo(store);
          }
        }
      }
    });
  }

  showLabelAdd() {
    showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
          return Material(
            type: MaterialType.transparency,
            child: Scaffold(
              backgroundColor: Colors.transparent,
              body: Center(
                child: SingleChildScrollView(
                  physics: AlwaysScrollableScrollPhysics(),
                  child: Container(
                    width: MediaQuery.of(context).size.width - 80,
                    padding: EdgeInsets.fromLTRB(20, 20, 20, 10),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.all(Radius.circular(4.0)),
                    ),
                    child: Column(
                      children: [
                        Padding(
                          padding: EdgeInsets.only(top: 3, bottom: 3),
                          child: Text(
                            '${user?.shopName ?? ''}**********'.substring(0, 10).replaceAll('*', ''),
                            style: TextStyle(
                              fontSize: 18.0,
                              color: Colors.black,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: <Widget>[
                            Container(
                              width: MediaQuery.of(context).size.width * .5 + 2,
                              decoration: BoxDecoration(color: Colors.white),
                              child: QrImage(
                                data: '${user?.shopCode}',
                                version: QrVersions.auto,
                                size: MediaQuery.of(context).size.width * .5,
                              ),
                            ),
                          ],
                        ),
                        Text(
                          '${user?.shopCode ?? ''}',
                          style: TextStyle(
                            fontSize: 16.0,
                            color: Colors.black,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        InkWell(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Container(
                            color: Colors.white,
                            height: 50,
                            padding: EdgeInsets.only(top: 6, bottom: 6),
                            child: Container(
                              height: 37,
                              margin: EdgeInsets.only(left: 20, right: 20),
                              width: double.infinity,
                              decoration: BoxDecoration(
                                  color: Theme.of(context).primaryColor,
                                  borderRadius: BorderRadius.all(Radius.circular(20)),
                                  border: Border.all(width: 1, color: Theme.of(context).primaryColor)),
                              alignment: Alignment.center,
                              child: Text(
                                '确定',
                                style: TextStyle(color: Colors.white, fontSize: 14),
                              ),
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        });
      },
    );
  }

  initData(Store<AppState> store) async {
    user = await UserDao.getUserInfo(store);
  }

  goWithdrawal() {
    Navigator.push(context, new CupertinoPageRoute(builder: (context) => new CashOutPage(), settings: RouteSettings(name: 'cashOutPage')))
        .then((value) => {getWealthInfo()});
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return StoreBuilder<AppState>(builder: (context, store) {
      user = store.state.userInfo;
      if (user == null) {
        initData(store);
      }
      return Scaffold(
          backgroundColor: Colors.white,
          body: new RefreshIndicator(
              onRefresh: onFresh,
              child: SingleChildScrollView(
                physics: AlwaysScrollableScrollPhysics(),
                child: Column(
                  children: [
                    Stack(
                      children: [
                        Container(
                          width: double.infinity,
                          decoration: BoxDecoration(color: Colors.red.shade100),
                          child: LocalImageUtil.getImageAsset('bg', isChannel: true, width: MediaQuery.of(context).size.width),
                        ),
                        // Container(
                        //     margin: EdgeInsets.only(top: 36),
                        //     child: Row(
                        //       mainAxisAlignment: MainAxisAlignment.end,
                        //       children: [
                        //         InkWell(
                        //           child: Align(
                        //             alignment: Alignment.centerLeft,
                        //             child: Padding(
                        //                 padding: EdgeInsets.only(right: 10.0),
                        //                 child: LocalImageUtil.getImageAsset(
                        //                     'server',
                        //                     width: 22)),
                        //           ),
                        //           onTap: () {
                        //             Navigator.push(context,
                        //                 new MaterialPageRoute(
                        //                     builder: (context) {
                        //               return AgreementPage(
                        //                   url: DefaultConfig().configs.CUSTOMER_SERVICE,
                        //                   isLocalUrl: false,
                        //                   title: '在线咨询');
                        //             }));
                        //           },
                        //         ),
                        //       ],
                        //     )),
                        Container(
                          margin: EdgeInsets.fromLTRB(15, 90, 15, 13),
                          height: !isCourier && user?.hasAdmin != 1 ? 75 : 175,
                          width: double.infinity,
                          decoration: BoxDecoration(
                              color: Colors.white,
                              boxShadow: [
                                BoxShadow(color: Color(0xFFF5F5F5), offset: Offset(-3, 9), blurRadius: 7),
                                BoxShadow(color: Color(0xFFF5F5F4), offset: Offset(3, 0), blurRadius: 7),
                              ],
                              borderRadius: BorderRadius.all(Radius.circular(7))),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                  margin: EdgeInsets.only(left: 100, top: 5),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        child: Row(
                                          children: [
                                            Text(
                                              '${isCourier ? user?.realName : ('${user?.shopName ?? ''}**********').substring(0, 10).replaceAll('*', '')}',
                                              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
                                            ),
                                            Offstage(
                                                offstage: isCourier,
                                                child: InkWell(
                                                  onTap: () {
                                                    showLabelAdd();
                                                  },
                                                  child: Icon(
                                                    Icons.qr_code,
                                                    size: 20,
                                                    color: Theme.of(context).primaryColor,
                                                  ),
                                                )),
                                          ],
                                        ),
                                      ),
                                      Padding(padding: EdgeInsets.only(top: 2)),
                                      Container(
                                        child: Row(
                                          children: [
                                            Text(
                                              '${user?.name}',
                                              style:
                                                  TextStyle(fontSize: AppConstant.smallTextSize, color: DefaultConfig().configs.GREY_COLOR),
                                            ),
                                            Offstage(
                                                offstage: !isCourier,
                                                child: InkWell(
                                                  onTap: () {
                                                    selectBrand();
                                                  },
                                                  child: Row(
                                                    children: [
                                                      Padding(padding: EdgeInsets.only(left: 5)),
                                                      Text(
                                                        '${DefaultConfig().configs.EXPRESS2[user?.brandCode ?? '']}',
                                                        style: TextStyle(
                                                            fontSize: AppConstant.smallTextSize, color: DefaultConfig().configs.GREY_COLOR),
                                                      ),
                                                      Padding(padding: EdgeInsets.only(left: 5)),
                                                      LocalImageUtil.getImageAsset('editGrey', width: 14)
                                                    ],
                                                  ),
                                                ))
                                          ],
                                        ),
                                      ),
                                    ],
                                  )),
                              Offstage(
                                offstage: !isCourier && user?.hasAdmin != 1,
                                child: InkWell(
                                  onTap: () {
                                    if (!isCourier) {
                                      if (hasAdmin == 1) {
                                        NavigatorUtils.goWalletPage(context);
                                      } else if (hasAdmin == 0 && hasWallet == 1) {
                                        NavigatorUtils.goWalletPage(context);
                                      } else {
                                        Fluttertoast.showToast(msg: '未开启钱包功能');
                                      }
                                    } else {
                                      NavigatorUtils.goWalletPage(context);
                                    }
                                  },
                                  child: Container(
                                      width: double.infinity,
                                      margin: EdgeInsets.only(left: 5, top: 20, right: 5),
                                      decoration: BoxDecoration(
                                          color: DefaultConfig().configs.PRIMARY_COLOR_LIGHT,
                                          borderRadius: BorderRadius.all(Radius.circular(7))),
                                      child: Stack(
                                        children: [
                                          Container(
                                            padding: EdgeInsets.only(top: 10, bottom: 10),
                                            child: Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                                              children: [
                                                Column(
                                                  children: [
                                                    Text(
                                                      '余额（元）',
                                                      style: TextStyle(fontSize: AppConstant.smallTextSize, fontWeight: FontWeight.w800),
                                                    ),
                                                    SizedBox(
                                                      height: 5,
                                                    ),
                                                    Row(
                                                      children: [
                                                        Text(
                                                          '$balanceNum',
                                                          style: TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
                                                        ),
                                                        Offstage(
                                                          offstage: frozenMoney == 0,
                                                          child: Text(
                                                            '(冻结$frozenMoneyNum)',
                                                            style: TextStyle(
                                                              fontSize: AppConstant.tinyTextSize,
                                                            ),
                                                          ),
                                                        )
                                                      ],
                                                    ),
                                                    Row(
                                                      children: [
                                                        InkWell(
                                                          onTap: () {
                                                            if (!isCourier) {
                                                              if (hasAdmin == 1) {
                                                                NavigatorUtils.goRechargePage(context, balanceNum);
                                                              } else if (hasAdmin == 0 && hasWallet == 1) {
                                                                NavigatorUtils.goRechargePage(context, balanceNum);
                                                              } else {
                                                                Fluttertoast.showToast(msg: '未开启钱包功能');
                                                              }
                                                            } else {
                                                              NavigatorUtils.goRechargePage(context, balanceNum);
                                                            }
                                                          },
                                                          child: Container(
                                                            margin: EdgeInsets.only(left: 3),
                                                            padding: EdgeInsets.fromLTRB(12, 3, 12, 3),
                                                            decoration: BoxDecoration(
                                                              border: Border.all(width: 1, color: Theme.of(context).primaryColor),
                                                              borderRadius: BorderRadius.all(
                                                                Radius.circular(15.0),
                                                              ),
                                                            ),
                                                            child: Text('充值',
                                                                style: TextStyle(fontSize: 13, color: Theme.of(context).primaryColor)),
                                                          ),
                                                        ),
                                                        Offstage(
                                                            offstage: !isShowBtn,
                                                            child: Row(
                                                              children: [
                                                                SizedBox(width: 20),
                                                                InkWell(
                                                                  onTap: () {
                                                                    if (!isCourier) {
                                                                      if (hasAdmin == 1) {
                                                                        goWithdrawal();
                                                                      } else if (hasAdmin == 0 && hasWallet == 1) {
                                                                        goWithdrawal();
                                                                      } else {
                                                                        Fluttertoast.showToast(msg: '未开启钱包功能');
                                                                      }
                                                                    } else {
                                                                      goWithdrawal();
                                                                    }
                                                                  },
                                                                  child: Container(
                                                                    margin: EdgeInsets.only(left: 3),
                                                                    padding: EdgeInsets.fromLTRB(12, 3, 12, 3),
                                                                    decoration: BoxDecoration(
                                                                      border: Border.all(width: 1, color: Theme.of(context).primaryColor),
                                                                      borderRadius: BorderRadius.all(
                                                                        Radius.circular(15.0),
                                                                      ),
                                                                    ),
                                                                    child: Text('提现',
                                                                        style:
                                                                            TextStyle(fontSize: 13, color: Theme.of(context).primaryColor)),
                                                                  ),
                                                                ),
                                                              ],
                                                            ))
                                                      ],
                                                    )
                                                  ],
                                                ),
                                                Container(height: 25, width: 1, decoration: BoxDecoration(color: Color(0xFFE5E5E5))),
                                                Column(
                                                  children: [
                                                    Text(
                                                      '总提现金额（元）',
                                                      style: TextStyle(fontSize: AppConstant.smallTextSize, fontWeight: FontWeight.w800),
                                                    ),
                                                    SizedBox(
                                                      height: 5,
                                                    ),
                                                    Text(
                                                      '$totalWithdrawalMoneyNum',
                                                      style: TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
                                                    ),
                                                    Row(
                                                      children: [
                                                        SizedBox(
                                                          width: 20,
                                                        ),
                                                        InkWell(
                                                          child: Container(
                                                            margin: EdgeInsets.only(left: 3),
                                                            padding: EdgeInsets.fromLTRB(12, 3, 12, 3),
                                                            child: Text(
                                                              '',
                                                            ),
                                                          ),
                                                        )
                                                      ],
                                                    )
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                          Positioned(top: 0, child: LocalImageUtil.getImageAsset('wallet', isChannel: true, width: 36))
                                        ],
                                      )),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          width: 70.0,
                          height: 70.0,
                          padding: EdgeInsets.only(top: 2.0),
                          margin: EdgeInsets.fromLTRB(40, 77, 0, 0),
                          decoration: new BoxDecoration(
                              color: Theme.of(context).primaryColor, borderRadius: BorderRadius.all(Radius.circular(50.0))),
                          child: LocalImageUtil.getImageAsset('logoAvatarCircle', isChannel: true, width: 58),
                        )
                      ],
                    ),
                    Container(
                      margin: EdgeInsets.fromLTRB(15.0, 0.0, 15.0, 10.0),
                      decoration: new BoxDecoration(
                          color: Colors.white,
                          boxShadow: [
                            BoxShadow(color: Color(0xFFF5F5F4), offset: Offset(-3, 9), blurRadius: 8),
                            BoxShadow(color: Color(0xFFF5F5F4), offset: Offset(3, 0), blurRadius: 8),
                          ],
                          borderRadius: BorderRadius.all(Radius.circular(8.0))),
                      child: new Column(
                        children: menuItem(),
                      ),
                    ),
                  ],
                ),
              )));
    });
  }

  menuItem() {
    List<Widget> widgets = <Widget>[];
    widgets = List.generate(menuList.length, (index) {
      return _renderCard(menuList[index], index);
    });
    return widgets;
  }

  _renderCard(v, index) {
    return new InkWell(
        onTap: () {
          goSecondPage(v['type']);
        },
        child: new Container(
          decoration: new BoxDecoration(
              border: Border(bottom: index != menuList.length ? BorderSide(width: 1.0, color: Color(0xFFF4F4F4)) : BorderSide.none)),
          padding: EdgeInsets.fromLTRB(12.0, 18.0, 8.0, 16.0),
          margin: EdgeInsets.only(left: 20, right: 20),
          child: new Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              new Row(
                children: <Widget>[
                  LocalImageUtil.getImageAsset(v['icon'], width: 18),
                  new Padding(padding: EdgeInsets.only(left: 5.0)),
                  new Text(
                    '${v['name']}',
                    style: new TextStyle(color: Colors.black, fontSize: 15.0, fontWeight: FontWeight.w600),
                  )
                ],
              ),
              new Expanded(
                child: new Container(
                  child: new Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: <Widget>[
                      new Container(),
                      new Icon(
                        Icons.arrow_forward_ios_sharp,
                        size: 18.0,
                        color: Color(0xFF999999),
                      )
                    ],
                  ),
                ),
              )
            ],
          ),
        ));
  }

  showThemeDialog(BuildContext context, Store store) {
    List<String> list = [
      CommonUtils.getLocale(context).homeThemeDefault,
      CommonUtils.getLocale(context).homeTheme1,
      CommonUtils.getLocale(context).homeTheme2,
      CommonUtils.getLocale(context).homeTheme3,
      CommonUtils.getLocale(context).homeTheme4,
      CommonUtils.getLocale(context).homeTheme5,
      CommonUtils.getLocale(context).homeTheme6,
    ];
    CommonUtils.showCommitOptionDialog(context, list, (index) {
      CommonUtils.pushTheme(store, index);
      LocalStorage.save(DefaultConfig().configs.THEME_COLOR, index.toString());
    }, colorList: CommonUtils.getThemeListColor());
  }

  // TODO: implement wantKeepAlive
  @override
  bool get wantKeepAlive => true;
}
