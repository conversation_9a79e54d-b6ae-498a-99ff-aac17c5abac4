import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/brand_bind_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/BrandUtil.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CommonRowWidget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class BrandsManagePage extends StatefulWidget {
  BrandsManagePage({Key? key}) : super(key: key);

  @override
  _BrandsManagePageState createState() => _BrandsManagePageState();
}

class _BrandsManagePageState extends State<BrandsManagePage> {
  List<dynamic> companyList = [];
  late UserEntity user;
  bool isChange = false;

  @override
  void initState() {
    super.initState();
    getUserInfo();
    homePageRefresh();
    getCompanyList();
  }

  homePageRefresh() async {
    BrandUtils.saveHomePageRefresh();
  }

  getUserInfo() async {
    var res = await UserDao.getUserInfoLocal();
    if (res != null && res.result) {
      user = res.data;
    }
    setState(() {});
  }

  getCompanyList() async {
    var res = await UserDao.getBrandBindList();
    companyList = [];
    if (res != null && res.result) {
      companyList = res.data;
    }
    setState(() {});
  }

  @override
  void dispose() {
    super.dispose();
  }

  // selectCompany(item) {
  //   int index = companyList.indexWhere((comp) => comp['code'] == item['code']);
  //   if (companyList[index]['allowIn'] == 0) {
  //     companyList[index]['allowIn'] = 1;
  //     companyList[index]['syncTrace'] = 1;
  //   } else {
  //     companyList[index]['allowIn'] = 0;
  //     companyList[index]['syncTrace'] = 0;
  //   }
  //   setState(() {
  //     companyList = companyList;
  //   });
  // }

  changeValue() {
    setState(() {
      isChange = true;
    });
  }

  buildCompanyList() {
    List<Widget> widgets = <Widget>[];
    widgets = List.generate(companyList.length, (index) {
      BrandBindEntity entity = BrandBindEntity.fromJson(companyList[index]);
      return _render(entity, index);
    });
    return widgets;
  }

  getSyncTraceLabel(BrandBindEntity entity) {
    String label = '';
    if (entity.syncTrace == 1) {
      label = '同步';
    } else {
      label = '不同步';
      if (entity.allowIn == 1) {
        label = '手动关闭';
        if (['JD', 'SF'].indexOf(entity.brandCode!) > -1) {
          label = '未对接';
        }
      }
    }
    return label;
  }

  _render(entity, index) {
    return Container(
        padding: EdgeInsets.fromLTRB(10, 0, 10, 0),
        child: Container(
            padding: EdgeInsets.only(bottom: 8),
            decoration: BoxDecoration(
                color: Colors.white,
                border: Border(
                  bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                )),
            child: CommonRowWidget(
              leftWidget: Expanded(
                  flex: 3,
                  child: Text(
                    DefaultConfig().configs.EXPRESS2[entity.brandCode] ?? '',
                    style: TextStyle(
                        fontSize: AppConstant.middleTextWhiteSize,
                        color: entity.allowIn == 1 ? Colors.black : Color(0xFF999999)),
                    textAlign: TextAlign.left,
                  )),
              middleWidget: Expanded(
                  flex: 4,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(entity.allowIn == 1 ? '允许' : '不允许',
                          style: TextStyle(
                              fontSize: 14,
                              color: entity.allowIn == 1 ? Color(0xFF858585) : Theme.of(context).primaryColor)),
                      Transform.scale(
                        scale: 0.7,
                        child: CupertinoSwitch(
                            value: entity.allowIn == 1,
                            activeColor: Theme.of(context).primaryColor,
                            trackColor: Colors.grey[200],
                            onChanged: (bool value) async {
                              changeValue();
                              BrandBindEntity brandBindEntity = new BrandBindEntity();
                              brandBindEntity.brandCode = entity.brandCode;
                              brandBindEntity.allowIn = value ? 1 : 0;
                              brandBindEntity.syncTrace = value ? 1 : 0;
                              await updateCompany(brandBindEntity, index);
                            }),
                      )
                    ],
                  )),
              rightWidget: Expanded(
                  flex: 4,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(getSyncTraceLabel(entity),
                          style: TextStyle(
                              fontSize: 14,
                              color: entity.syncTrace == 1
                                  ? Color(0xFF858585)
                                  : entity.allowIn == 0
                                      ? Theme.of(context).primaryColor
                                      : Colors.red)),
                      Transform.scale(
                        scale: 0.7,
                        child: CupertinoSwitch(
                            value: entity.syncTrace == 1,
                            activeColor: entity.allowIn == 0
                                ? DefaultConfig().configs.PRIMARY_COLOR_LIGHT
                                : Theme.of(context).primaryColor,
                            trackColor: Colors.grey[200],
                            onChanged: (bool value) async {
                              BrandBindEntity brandBindEntity = new BrandBindEntity();
                              brandBindEntity.brandCode = entity.brandCode;
                              brandBindEntity.allowIn = entity.allowIn;
                              if (['JD', 'SF'].indexOf(entity.brandCode!) <= -1) {
                                if (entity.allowIn == 1) {
                                  if (entity.syncTrace == 1) {
                                    bool res = await CommonUtils.confirm(context, '禁用后，该快递品牌的包裹入柜后无法保证签收失效并无法同步物流',
                                        title: '重要提示');
                                    if (res) {
                                      brandBindEntity.syncTrace = value ? 1 : 0;
                                      await updateCompany(brandBindEntity, index);
                                    }
                                  } else {
                                    brandBindEntity.syncTrace = value ? 1 : 0;
                                    await updateCompany(brandBindEntity, index);
                                  }
                                } else {
                                  Fluttertoast.showToast(msg: '请允许该品牌入柜');
                                }
                              }
                            }),
                      )
                    ],
                  )),
            )));
  }

  updateCompany(BrandBindEntity item, int index) async {
    LoadingUtil(
      status: '数据更新中...',
    ).show(context);
    Map<String, dynamic> info = {'brandCode': item.brandCode, 'allowIn': item.allowIn, 'syncTrace': item.syncTrace};
    if (['JD', 'SF'].indexOf(item.brandCode!) > -1) {
      info['syncTrace'] = 0;
    }
    var res = await UserDao.updateBrand(info);
    LoadingUtil.dismiss(context);
    if (res != null && res.result) {
      companyList[index] = info;
      setState(() {});
      Fluttertoast.showToast(msg: '设置成功');
    }
  }

  // bindCompany({isGoBack = false}) async {
  //   List brandList = [];
  //   companyList.forEach((item) {
  //     brandList.add({'brandCode': item['brandCode'], 'allowIn': item['allowIn'], 'syncTrace': item['syncTrace']});
  //   });
  //   Map<String, dynamic> info = {'brands': brandList};
  //   var res = await UserDao.setBrand(info);
  //   if (res != null && res.result) {
  //     EasyLoading.dismiss();
  //     if (res.data) {
  //       Fluttertoast.showToast(msg: '绑定快递公司成功');
  //       if (isGoBack) {
  //         Navigator.pop(context);
  //       }
  //     }
  //   }
  //   EasyLoading.dismiss();
  // }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        // ignore: missing_return
        onWillPop: () async {
          Navigator.pop(context);
          // if (isChange) {
          //   CommonUtils.confirm(context, '数据发生改变，是否保存？', onPressed: () async {
          //     await bindCompany(isGoBack: true);
          //   });
          // } else {
          //   Navigator.pop(context);
          // }
          return true;
        },
        child: Scaffold(
            backgroundColor: DefaultConfig().configs.BG_COLOR,
            appBar: new AppCustomerBar(
              title: AppbarTitle(
                title: '选择快递公司',
                isCenter: true,
              ),
              actions: [
                Container(
                  width: 60,
                )
              ],
            ),
            body: Column(
              children: [
                Container(
                    color: DefaultConfig().configs.PRIMARY_COLOR_LIGHT,
                    padding: EdgeInsets.fromLTRB(10, 6, 10, 6),
                    child: Text(
                      '为保证签收时效及入柜同步物流，请在【物流同步】处保持“同步”状态，'
                      '如您确定不入柜某个品牌，可以在【是否入柜】处关闭该品牌，'
                      '提高品牌的入柜效率和准确性',
                      style: TextStyle(fontSize: 13, color: Theme.of(context).primaryColor),
                    )),
                Container(
                    color: DefaultConfig().configs.BG_COLOR,
                    padding: EdgeInsets.fromLTRB(10, 4, 10, 10),
                    child: CommonRowWidget(
                      leftWidget: Expanded(
                          flex: 3,
                          child: Text('快递品牌',
                              style: TextStyle(fontSize: AppConstant.smallTextSize), textAlign: TextAlign.left)),
                      middleWidget: Expanded(
                          flex: 4,
                          child: Container(
                            alignment: Alignment.centerRight,
                            child: Text('是否允许入柜',
                                style: TextStyle(fontSize: AppConstant.smallTextSize), textAlign: TextAlign.center),
                          )),
                      rightWidget: Expanded(
                          flex: 4,
                          child: Container(
                            alignment: Alignment.centerRight,
                            child: Text('物流同步',
                                style: TextStyle(fontSize: AppConstant.smallTextSize), textAlign: TextAlign.center),
                          )),
                    )),
                Expanded(
                    child: Container(
                  color: Colors.white,
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: buildCompanyList(),
                    ),
                  ),
                )),
                // Container(
                //     width: MediaQuery.of(context).size.width,
                //     height: 121.0,
                //     decoration: new BoxDecoration(color: Colors.white, boxShadow: [
                //       BoxShadow(color: Colors.grey[400], offset: Offset(3, 3), blurRadius: 1),
                //       BoxShadow(color: Colors.grey[300], offset: Offset(-2, -2), blurRadius: 0),
                //     ]),
                //     child: Column(
                //       children: [
                //         Container(
                //           padding: EdgeInsets.fromLTRB(20, 2, 20, 2),
                //           child: Text("注：后面如需新增品牌也可在：我的-快递品牌管理处开启对应品牌即可", style: TextStyle(fontSize: 14)),
                //         ),
                //         BottomBtnWidget(
                //           showShadow: false,
                //           shape: 'rectangle',
                //           type: 'primary',
                //           title: '进入首页',
                //           action: () async {
                //             bindCompany();
                //           },
                //         ),
                //       ],
                //     ))
              ],
            )));
  }
}
