import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/gp_user_list_entity.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/NoResult.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class CoMatchingPage extends StatefulWidget {
  final String gpUserId;

  CoMatchingPage(this.gpUserId, {Key? key}) : super(key: key);

  @override
  _CoMatchingPageState createState() => _CoMatchingPageState();
}

class _CoMatchingPageState extends State<CoMatchingPage> {
  List<dynamic> list = [];
  Map<dynamic, dynamic> brandMap = {
    'EMS': '中国邮政',
    'ZTO': '中通速递',
    'YTO': '圆通速递',
    'STO': '申通快递',
    'YUNDA': '韵达速递',
    'HTKY': '百世快递',
    'JT': '极兔速递',
    'FENGWANG': '丰网速运',
  };

  @override
  void initState() {
    super.initState();
    getUserInfo();
  }

  getUserInfo() async {
    Map<String, dynamic> info = {
      'gpUserId': widget.gpUserId,
    };
    var res = await UserDao.userAccounts(info);
    if (res != null && res.result) {
      list = res.data;
    }
    setState(() {});
  }

  @override
  void dispose() {
    super.dispose();
  }

  _render(index) {
    GpUserListEntity entity = GpUserListEntity.fromJson(list[index]);
    return InkWell(
      onTap: () async {
        await NavigatorUtils.goCoMatchingDetail(context, entity);
      },
      child: Container(
        color: Colors.white,
        padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
        margin: EdgeInsets.only(bottom: 10),
        child: Container(
          child: Row(
            children: [
              Container(
                width: 50,
                height: 50,
                child: CircleAvatar(
                  radius: 10.0,
                  backgroundColor: Colors.white,
                  backgroundImage: AssetImage(CommonUtils.getExpressLogo(entity.company)),
                ),
                margin: EdgeInsets.only(right: 20),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${brandMap[entity.company]}',
                    ),
                    Text(
                      '登录名：${entity.loginName}',
                    ),
                    Text(
                      '网点编号：${entity.siteCode}',
                    ),
                  ],
                ),
              ),
              Container(
                child: new Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: <Widget>[
                    new Container(),
                    new Icon(
                      Icons.arrow_forward_ios_sharp,
                      size: 18.0,
                      color: Color(0xFF999999),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        // ignore: missing_return
        onWillPop: () async {
          Navigator.pop(context);
          return true;
        },
        child: Scaffold(
            backgroundColor: DefaultConfig().configs.BG_COLOR,
            appBar: new AppCustomerBar(
              title: AppbarTitle(
                title: '共配账号',
                isCenter: true,
              ),
              actions: [
                Container(
                  width: 60,
                  child: TextButton(
                    onPressed: () async {
                      bool data = await CommonUtils.confirm(context, '是否解绑共配账号？');
                      if (!data) {
                        return;
                      }
                      var res = await UserDao.gpDelete();
                      if (res != null && res.result) {
                        Fluttertoast.showToast(msg: '解绑成功');
                        await LocalStorage.save(
                            DefaultConfig().configs.IS_DP, false,
                            isPrivate: true);
                        Navigator.pop(context);
                      }
                    },
                    child: Text(
                      '解绑',
                    ),
                  ),
                ),
              ],
            ),
            body: RefreshIndicator(
              onRefresh: () async {
                await getUserInfo();
              },
              child:list.length > 0 ? ListView.builder(
                itemBuilder: (context, index) {
                  return _render(index);
                },
                itemCount: list.length,
              ):  ListView.builder(
                itemBuilder: (context, index) {
                  return Container(
                    height: 300,
                    child: NoResult(
                        size: 90,
                        subWidget: Container(
                            padding: EdgeInsets.only(top: 10),
                            child: Text('请联系管理员添加', style: AppConstant.smallSubText))),
                  );
                },
                itemCount: 1,
              ),
            )
        ));
  }
}
