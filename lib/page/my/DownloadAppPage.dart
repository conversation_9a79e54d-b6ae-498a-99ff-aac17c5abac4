import 'dart:async';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CustomShareWidget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share_plus/share_plus.dart';

class DownloadAppPage extends StatefulWidget {
  @override
  _DownloadAppPageState createState() => _DownloadAppPageState();
}

class _DownloadAppPageState extends State<DownloadAppPage> {
  GlobalKey repaintWidgetKey = GlobalKey(); // 绘图key值
  late Uint8List pngBytes;
  String downloadUrl = '';

  @override
  void initState() {
    super.initState();
    initDownloadUrl();
  }

  initDownloadUrl() async {
    DataResult res = await UserDao.appQr({'type': 7});
    if (res.result) {
      downloadUrl = res.data ?? '';
    }
    setState(() {});
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  Future requestPermission() async {
    // 申请权限
    if (await Permission.photos.request().isGranted) {
      saveImage();
    }
  }

  /// 截屏图片生成图片流ByteData
  capturePngToByteData() async {
    try {
      RenderRepaintBoundary boundary = repaintWidgetKey.currentContext?.findRenderObject() as RenderRepaintBoundary;
      double dpr = ui.window.devicePixelRatio; // 获取当前设备的像素比
      var image = await boundary.toImage(pixelRatio: dpr);
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      Uint8List? _pngBytes = byteData?.buffer.asUint8List();
      return _pngBytes;
    } catch (e) {
      print(e);
    }
    return null;
  }

  saveImage() async {
    var res = await ImageGallerySaver.saveImage(pngBytes.buffer.asUint8List());
    if (res != null) {
      Fluttertoast.showToast(msg: '保存图片成功');
    }
  }

  shareDownLoadUrl() {
    Share.share('$downloadUrl');
  }

  shareFunc() async {
    String? type = await CommonUtils.selectSavePhoto(context);
    if (type == 'save') {
      /// 保存图片
      pngBytes = await capturePngToByteData();
      if (pngBytes != null) {
        requestPermission();
      } else {
        Fluttertoast.showToast(msg: '生成图片错误');
      }
    } else if (type == 'share') {
      /// 分享下载链接
      shareDownLoadUrl();
    }
  }

  @override
  Widget build(BuildContext context) {
    return new StoreBuilder<AppState>(builder: (context, store) {
      UserEntity? user = store.state.userInfo;
      return Scaffold(
        backgroundColor: Colors.grey.shade200,
        appBar: new AppCustomerBar(
          title: Text(
            '下载app',
            style: new TextStyle(fontSize: 18.0, color: Colors.black),
          ),
          actions: [
            IconButton(
                icon: Icon(Icons.share),
                onPressed: () {
                  CustomShareWidget.showShareView(context);
                }),
          ],
        ),
        body: Container(
            width: MediaQuery.of(context).size.width,
            alignment: Alignment.center,
            child: Column(
              children: [
                Expanded(
                    child: SingleChildScrollView(
                  child: InkWell(
                    onLongPress: () async {
                      shareFunc();
                    },
                    child: Container(
                      alignment: Alignment(0, 0),
                      width: MediaQuery.of(context).size.width,
                      height: MediaQuery.of(context).size.height - MediaQuery.of(context).padding.top - 50 - 56,
                      child: RepaintBoundary(
                        key: repaintWidgetKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisSize: MainAxisSize.min,
                          children: <Widget>[
                            Container(
                              width: MediaQuery.of(context).size.width * 0.75,
                              padding: EdgeInsets.fromLTRB(0, 10, 0, 5),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(10),
                                  topRight: Radius.circular(10),
                                ),
                              ),
                              child: Column(
                                children: <Widget>[
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: <Widget>[
                                      Container(
                                        height: 100,
                                        color: Colors.white,
                                        margin: EdgeInsets.only(top: 10, bottom: 10),
                                        padding: EdgeInsets.only(top: 20, bottom: 20),
                                        child: LocalImageUtil.getImageAsset('logo',
                                            width: MediaQuery.of(context).size.width * 0.6,isChannel: true,fit: BoxFit.fitWidth),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              width: MediaQuery.of(context).size.width * 0.75,
                              height: MediaQuery.of(context).size.width * 0.75,
                              padding: EdgeInsets.fromLTRB(0, 15, 0, 15),
                              decoration: BoxDecoration(
                                color: Theme.of(context).primaryColor,
                                borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(10),
                                  bottomRight: Radius.circular(10),
                                ),
                              ),
                              child: Column(
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: <Widget>[
                                      Container(
                                        width: MediaQuery.of(context).size.width * .5 + 2,
                                        decoration: BoxDecoration(color: Colors.white),
                                        child: QrImage(
                                          data: downloadUrl,
                                          version: QrVersions.auto,
                                          size: MediaQuery.of(context).size.width * .5,
                                        ),
                                      ),
                                    ],
                                  ),
                                  Container(
                                    width: MediaQuery.of(context).size.width * 0.75,
                                    padding: EdgeInsets.only(top: 5),
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).primaryColor,
                                      borderRadius: BorderRadius.only(
                                        bottomLeft: Radius.circular(10),
                                        bottomRight: Radius.circular(10),
                                      ),
                                    ),
                                    child: Column(
                                      children: <Widget>[
                                        Text(
                                          '微信【扫一扫】下载',
                                          style: TextStyle(
                                            fontSize: 16.0,
                                            color: Colors.white,
                                            fontWeight: FontWeight.w400,
                                          ),
                                        ),
                                        Container(
                                          padding: EdgeInsets.only(top: 3),
                                          child: Text(
                                            '${DefaultConfig().configs.APP_NAME}',
                                            style: TextStyle(
                                              fontSize: 16.0,
                                              color: Colors.white,
                                              fontWeight: FontWeight.w400,
                                            ),
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                )),
                InkWell(
                  onTap: () {
                    shareFunc();
                  },
                  child: Container(
                    color: Colors.white,
                    height: 50,
                    padding: EdgeInsets.only(top: 6, bottom: 6),
                    child: Container(
                      height: 37,
                      margin: EdgeInsets.only(left: 20, right: 20),
                      width: double.infinity,
                      decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          borderRadius: BorderRadius.all(Radius.circular(20)),
                          border: Border.all(width: 1, color: Theme.of(context).primaryColor)),
                      alignment: Alignment.center,
                      child: Text(
                        '保存图片',
                        style: TextStyle(color: Colors.white, fontSize: 14),
                      ),
                    ),
                  ),
                )
              ],
            )),
      );
    });
  }

  // TODO: implement wantKeepAlive
  @override
  bool get wantKeepAlive => true;
}
