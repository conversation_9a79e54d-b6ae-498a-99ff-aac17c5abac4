import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/NoResult.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';

class SwitchUsersPage extends StatefulWidget {
  const SwitchUsersPage({Key? key}) : super(key: key);

  @override
  _SwitchUsersPageState createState() => _SwitchUsersPageState();
}

class _SwitchUsersPageState extends State<SwitchUsersPage> {
  String _userName = '';
  int? hasAdmin;
  List<dynamic> list = [];

  @override
  void initState() {
    super.initState();
    init();
  }

  @override
  void dispose() {
    super.dispose();
  }

  init() async {
    _userName = await LocalStorage.get<String>(DefaultConfig().configs.USER_NAME_KEY);
    this.handleRefresh();
  }

  getData() async {
    LoadingUtil(
      status: '数据加载中...',
    ).show(context);
    var res = await LocalStorage.get<Map>(DefaultConfig().configs.USER_LIST);
    LoadingUtil.dismiss(context);
    if (res != null) {
      list = res;
    }
    setState(() {});
  }

  delUser(item) async {
    bool res = await CommonUtils.confirm(context, '是否删除该账户', title: '删除账户', cancelText: '我在想想', confirmText: '是的');
    if (res) {
      list.remove(item);
      await LocalStorage.save(DefaultConfig().configs.USER_LIST, list);
      this.handleRefresh();
    }
  }

  checkUser(item) async {
    LoadingUtil(
      status: '正在登录...',
    ).show(context);
    await LocalStorage.save(
        DefaultConfig().configs.LOGIN_USER, {'loginName': item['loginName'], 'password': item['password']});
    await UserDao.logout(context);
  }

  Future<Null> handleRefresh() async {
    new Future.delayed(const Duration(microseconds: 200), () {
      this.getData();
    });
  }

  getItemContainer(store) {
    List<Widget> widgets = <Widget>[];
    list.forEach((item) {
      widgets.add(Container(
          height: 61,
          alignment: Alignment.center,
          margin: EdgeInsets.only(left: 15, right: 15, top: 15),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(10)),
              border: Border.all(width: 0.5, color: Color(0xFFB5B5B5))),
          child: Column(
            children: [
              Stack(children: [
                Positioned(
                  right: 0,
                  child: Offstage(
                      offstage: item['loginName'] != _userName,
                      child: Image.asset(
                        LocalImageUtil.getImagePath('checked'),
                        color: Theme.of(context).primaryColor,
                        fit: BoxFit.fill,
                        width: 30,
                      )),
                ),
                Container(
                  margin: EdgeInsets.only(left: 15, top: 15, right: 15, bottom: 15),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        "${item['loginName'] ?? ''}",
                        style: TextStyle(color: Colors.black, fontSize: 15),
                      ),
                      Offstage(
                          offstage: item['loginName'] == _userName,
                          child: Container(
                            margin: EdgeInsets.only(left: 15),
                            child: Row(
                              children: [
                                InkWell(
                                  onTap: () async {
                                    checkUser(item);
                                  },
                                  child: Container(
                                    alignment: Alignment.center,
                                    width: 80,
                                    height: 30,
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).primaryColor,
                                      borderRadius: BorderRadius.all(Radius.circular(5)),
                                    ),
                                    child: Text(
                                      '切换',
                                      style: TextStyle(color: Colors.white, fontSize: 15),
                                    ),
                                  ),
                                ),
                                Container(
                                  width: 5,
                                ),
                                InkWell(
                                  onTap: () async {
                                    delUser(item);
                                  },
                                  child: Container(
                                    alignment: Alignment.center,
                                    width: 80,
                                    height: 30,
                                    decoration: BoxDecoration(
                                      color: Colors.red,
                                      borderRadius: BorderRadius.all(Radius.circular(5)),
                                    ),
                                    child: Text(
                                      '删除',
                                      style: TextStyle(color: Colors.white, fontSize: 15),
                                    ),
                                  ),
                                )
                              ],
                            ),
                          )),
                    ],
                  ),
                )
              ]),
            ],
          )));
    });
    return widgets;
  }

  @override
  Widget build(BuildContext context) {
    return StoreBuilder<AppState>(builder: (context, store) {
      return Material(
          child: Scaffold(
              backgroundColor: Color(0xFFF5F5F5),
              appBar: AppCustomerBar(
                title: AppbarTitle(
                  title: '切换账户',
                  isCenter: true,
                ),
                actions: <Widget>[Container(width: 60)],
              ),
              body: RefreshIndicator(
                  onRefresh: handleRefresh,
                  child: Column(
                    children: [
                      Expanded(
                          child: SingleChildScrollView(
                        physics: AlwaysScrollableScrollPhysics(),
                        child: list.length > 0
                            ? Container(
                                color: Colors.white,
                                width: MediaQuery.of(context).size.width,
                                padding: EdgeInsets.fromLTRB(0, 0, 0, 20),
                                child: Column(
                                  children: getItemContainer(store),
                                ),
                              )
                            : Container(
                                height: 300,
                                child: NoResult(
                                    size: 64,
                                    type: 'box',
                                    subWidget: Container(
                                        padding: EdgeInsets.only(top: 10),
                                        child: Text('', style: AppConstant.smallSubText))),
                              ),
                      )),
                      Container(
                        width: double.infinity,
                        color: Colors.white,
                        child: Container(
                          margin: EdgeInsets.only(left: 15, right: 15, top: 10, bottom: 15),
                          height: 37,
                          child: MaterialButton(
                            color: Theme.of(context).primaryColor,
                            textColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20.0),
                            ),
                            onPressed: () async {
                              UserDao.logout(context);
                            },
                            child: Text("新增账户"),
                          ),
                        ),
                      ),
                    ],
                  ))));
    });
  }
}
