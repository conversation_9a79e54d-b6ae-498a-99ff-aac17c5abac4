import 'dart:math' as math;

import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CabinetDao.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/entitys/shop_courier_entity.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/FormValidateUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/utils/SoundUtils.dart';
import 'package:cabinet_flutter_app/common/utils/ThrottleUtil.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/BottomBtnWidget.dart';
import 'package:cabinet_flutter_app/widget/BottomFormInputWidget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';

class CourierShopDetailPage extends StatefulWidget {
  ShopCourierEntity? shopCourierEntity;

  CourierShopDetailPage(this.shopCourierEntity, {Key? key}) : super(key: key);

  @override
  _CourierShopDetailPageState createState() => _CourierShopDetailPageState();
}

enum ViewType { view, edit }

class FormEntity {
  String shopCode = '';
  String brandCode = '';
  String? fee;
}

class _CourierShopDetailPageState extends State<CourierShopDetailPage> {
  ViewType vType = ViewType.view;
  GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  TextEditingController shopCodeController = new TextEditingController();
  final FocusNode brandSelectNode = FocusNode();
  TextEditingController brandCodeController = new TextEditingController();
  TextEditingController feeController = new TextEditingController();
  ThrottleUtil throttleUtil = ThrottleUtil();
  FormEntity form = new FormEntity();
  bool showBrandSelectModal = false;
  Map<dynamic, dynamic> brandMap = {
    'ANE': '安能',
    'DBKD': '德邦',
    'EMS': 'EMS',
    'FW': '丰网',
    'HTKY': '百世',
    'JD': '京东',
    'JT': '极兔',
    'POSTB': '邮政',
    'SF': '顺丰',
    'SNWL': '苏宁',
    'STO': '申通',
    'UC56': '优速',
    'YTO': '圆通',
    'YUNDA': '韵达',
    'CAINIAO': '菜鸟',
    'ZTO': '中通'
  };
  late ShopCourierEntity entity;
  bool isCourier = true;

  @override
  void initState() {
    super.initState();
    init();
  }

  init() {
    if (widget.shopCourierEntity == null) {
      vType = ViewType.edit;
    } else {
      entity = widget.shopCourierEntity!;
    }
    brandSelectNode.addListener(() {
      bool isFocus = brandSelectNode.hasFocus;
      if (isFocus && !showBrandSelectModal) {
        selectBrand();
      } else {}
    });
    setState(() {});
  }

  /// 品牌选择
  selectBrand() {
    if (showBrandSelectModal) {
      return false;
    }
    SoundUtils.audioPushFn(SoundUtils.CHOOSE_COMPANY);
    showBrandSelectModal = true;
    brandSelectNode.unfocus();
    CommonUtils.showBottomBrandSelectModal(context, brandMap, (item) async {
      SoundUtils.audioPushFn(SoundUtils.BRAND_SOUND[item]!);
      if (item != null) {
        form.brandCode = item;
        brandCodeController.value = TextEditingValue(text: brandMap[item]);
        showBrandSelectModal = false;
        setState(() {});
      }
    }, showText: 'show', isForce: true, bindCompany: form.brandCode);
  }

  @override
  void dispose() {
    super.dispose();
  }

  void didChangeDependencies() {
    super.didChangeDependencies();
    isCourier = CheckUtils.isCourier(context);
  }

  getTitle() {
    String title = '合作详情';
    if (vType == ViewType.edit) {
      title = '新增合作详情';
    }
    return title;
  }

  scanQr() async {
    String? res = await NavigatorUtils.goCabinetBindPage(context, scanAction: ScanAction.SCANQR);
    if (res != null) {
      DataResult result = await CabinetDao.decryptShopCode(res);
      if (result.result) {
        if (result.data != null) {
          form.shopCode = result.data;
          shopCodeController.value = TextEditingValue(text: result.data);
        }
      }
    }
    setState(() {});
  }

  addForm() async {
    var validator = _formKey.currentState?.validate();
    if (validator!) {
      double fee = double.parse(form.fee!) * 1000;
      Map info = {
        'shopCode': form.shopCode,
        'brandCode': form.brandCode,
        'deliveryFee': fee
      };
      DataResult res = await CourierDao.createShopCourier(info);
      if (res.result && res.data) {
        Fluttertoast.showToast(msg: '创建成功');
        CommonUtils.isSaveDialog(context, (save) {
          Navigator.of(context).pop();
        },
            showCancel: false,
            successText: '我知道了',
            content: '创建成功，需等待驿站审核通过后生效！');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const DefaultConfig().configs.BG_COLOR,
      appBar: AppCustomerBar(
        title: AppbarTitle(
          title: getTitle(),
          isCenter: true,
        ),
        actions: [
          Offstage(
            offstage: vType != ViewType.view,
            child: IconButton(
              icon: Icon(Icons.delete, color: Colors.red),
              onPressed: () {
                deleteCooperation();
              },
            ),
          ),
          Container(
            width: 20,
          )
        ],
      ),
      body: vType == ViewType.edit
          ? editPage()
          : viewPage(), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }

  getFeeDisplay(double? fee) {
    String text = '0.0元/单';
    if (fee != null) {
      text = '${fee / 1000}元/单';
    }
    return text;
  }

  editFee() async {
    String? number = await CommonUtils.inputNumber(
        context, (entity.deliveryFee! / 1000).toString(),
        title: '派件单价', label: '单价(元)', labelWidth: 80.0);
    if (number != null) {
      try {
        double price = double.parse(number);
        if (price > 0) {
          Map info = {'id': entity.id, 'deliveryFee': price * 1000};
          DataResult res = await CourierDao.courierShopChangePrice(info);
          if (res.result && res.data) {
            Fluttertoast.showToast(msg: '修改价格成功');
            entity.deliveryFee = price * 1000;
            entity.status = 0;
            setState(() {});
            CommonUtils.isSaveDialog(context, (save) {
              if (!save) {
                Navigator.of(context).pop();
              }
            },
                cancelText: '返回',
                successText: '我知道了',
                content: '修改价格成功，需等待驿站审核通过后生效！');
          }
        }
      } catch (e) {
        Fluttertoast.showToast(msg: '单价输入错误');
      }
    }
  }

  getName() {
    String name = entity.courierName ?? '';
    if (isCourier) {
      name = entity.shopName ?? '';
    }
    return name;
  }

  getLinkMan() {
    String name = '';
    if (isCourier) {
      name = entity.shopLinkman ?? '';
    }
    return name == '' ? name : name + '    ';
  }

  getMobile() {
    String name = entity.courierMobile ?? '';
    if (isCourier) {
      name = entity.shopMobile ?? '';
    }
    return name;
  }

  // 添加删除方法
  deleteCooperation() {
    CommonUtils.isSaveDialog(context, (save) {
      if (save) {
        deleteCooperationAction();
      }
    },
        content: '确定要删除该合作关系吗？',
        cancelText: '取消',
        successText: '确定',
        successColor: Colors.red);
  }

  // 执行删除操作
  deleteCooperationAction() async {
    DataResult res;
    if (isCourier) {
      print('快递员删除合作关系，1650833365526102017' + entity.id!);
      // 快递员删除合作关系
      res = await CourierDao.shopCourierDelCourier(entity.id!);
    } else {
      // 驿站删除合作关系
      res = await CourierDao.shopCourierDelShop(entity.id!);
    }

    if (res.result && res.data) {
      Fluttertoast.showToast(msg: '删除成功');
      Navigator.of(context).pop();
    } else {
      Fluttertoast.showToast(msg: '删除失败');
    }
  }

  viewPage() {
    return Column(
      children: [
        Expanded(
            child: Column(
          children: [
            Container(
              margin: EdgeInsets.only(top: 2),
              color: Colors.white,
              width: double.infinity,
              padding: EdgeInsets.fromLTRB(15, 15, 15, 15),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    getName(),
                    style: TextStyle(
                        fontSize: 20, fontWeight: FontWeight.w700, height: 2),
                  ),
                  Offstage(
                    offstage: !isCourier,
                    child: Text(
                      entity.shopAddress ?? '',
                      style:
                          TextStyle(fontSize: 16, fontWeight: FontWeight.w400),
                    ),
                  ),
                  Offstage(
                    offstage: isCourier,
                    child: Text(
                      brandMap[entity.brandCode],
                      style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w400,
                          color: Theme.of(context).primaryColor),
                    ),
                  ),
                  Text(
                    '${getLinkMan()}${getMobile()}',
                    style: TextStyle(
                        fontSize: 16, fontWeight: FontWeight.w400, height: 2),
                  ),
                ],
              ),
            ),
            Stack(
              children: [
                Container(
                  margin: EdgeInsets.only(top: 20),
                  color: Colors.white,
                  width: double.infinity,
                  padding: EdgeInsets.fromLTRB(15, 15, 15, 15),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            '派件单价',
                            style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.w700,
                                height: 2),
                          ),
                        ],
                      ),
                      Container(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              getFeeDisplay(entity.deliveryFee),
                              style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w400,
                                  color: Theme.of(context).primaryColor),
                            ),
                            Padding(padding: EdgeInsets.only(left: 20)),
                            Offstage(
                              offstage: !isCourier,
                              child: TextButton(
                                  onPressed: () {
                                    editFee();
                                  },
                                  child: Text('修改价格',
                                      style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w400,
                                          color: Colors.black87,
                                          decoration:
                                              TextDecoration.underline))),
                            )
                          ],
                        ),
                      ),
                      Text(
                        '驿站确定价格立即生效，不建议频繁修改价格',
                        style: TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.w400,
                            height: 1.5,
                            color: Colors.black38),
                      ),
                    ],
                  ),
                ),
                Positioned(
                  top: 40,
                  right: 20,
                  child: getIcon(entity.status),
                )
              ],
            ),
          ],
        )),
        Offstage(
          offstage: isCourier || entity.status != 0,
          child: BottomBtnWidget(
            showShadow: true,
            type: ButtonType.primary,
            title: '审核',
            action: () async {
              review();
            },
          ),
        ),
        // 添加删除按钮（只在状态不为0时显示，避免与审核按钮冲突）（但是用户沙雕审核的时候也能删）
        // Offstage(
        //   offstage: false,
        //   child: BottomBtnWidget(
        //     showShadow: true,
        //     type: ButtonType.primary,
        //     title: '删除合作',
        //     action: () async {
        //       deleteCooperation();
        //     },
        //   ),
        // )
      ],
    );
  }

  reviewInfo(bool isPass) async {
    Map info = {'isPass': isPass, 'id': entity.id};
    DataResult res = await CourierDao.reviewShopCourier(info);
    if (res.result && res.data) {
      String title = isPass ? '审核成功' : '拒绝成功';
      int status = isPass ? 1 : 2;
      entity.status = status;
      setState(() {});
      Fluttertoast.showToast(msg: title);
    }
  }

  review() {
    CommonUtils.isSaveDialog(context, (save) {
      bool isPass = false;
      if (save is String) {
        if (save == 'middle') {
          isPass = false;
          reviewInfo(isPass);
        }
      } else if (save is bool) {
        if (save) {
          isPass = true;
          reviewInfo(isPass);
        }
      }
    },
        content: '是否审核通过?',
        cancelText: '取消',
        middleText: '拒绝',
        showMiddle: true,
        successText: '通过',
        middleColor: Colors.red,
        successColor: Theme.of(context).primaryColor);
  }

  getIcon(int? status) {
    String text = DefaultConfig().configs.COURIER_SHOP_STATUS[status ?? 0];
    return Transform(
        transform: Matrix4.identity()..rotateZ(0 * math.pi / 180),
        child: Container(
          padding: EdgeInsets.only(left: 20, right: 20, top: 5, bottom: 5),
          decoration: BoxDecoration(
            color: getColorGhost(status),
            borderRadius: BorderRadius.all(Radius.circular(4.0)),
          ),
          child: Text(text,
              style: TextStyle(color: getColor(status), fontSize: 16)),
        ));
  }

  getColor(int? status) {
    int type = status ?? 0;
    Color color = Color(0xFFe6a23c);
    switch (type) {
      case 0:
        color = Color(0xFFe6a23c);
        break;
      case 1:
        color = Color(0xFF67c239);
        break;
      case 2:
        color = Color(0xFFf56c6c);
        break;
      case 3:
        color = Color(0xFF909399);
        break;
    }
    return color;
  }

  getColorGhost(int? status) {
    int type = status ?? 0;
    Color color = Color(0xFFfaecd8);
    switch (type) {
      case 0:
        color = Color(0xFFfaecd8);
        break;
      case 1:
        color = Color(0xFFe1f3d8);
        break;
      case 3:
        color = Color(0xFFfef0f0);
        break;
      case 2:
        color = Color(0xFFe9e9eb);
        break;
    }
    return color;
  }

  editPage() {
    return Column(
      children: [
        Expanded(
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                Padding(padding: EdgeInsets.only(top: 10)),
                Container(
                  decoration: BoxDecoration(
                      border: Border(
                          bottom: BorderSide(
                              color: Color(0xFFEEEEEE), width: 1.0))),
                  child: BottomFormInputWidget(
                    value: form.shopCode,
                    require: true,
                    label: '驿站编码',
                    labelWidth: 80,
                    hintText: '请输入驿站编码',
                    textInputType: TextInputType.name,
                    controller: shopCodeController,
                    validator: (value) {
                      return FormValidateUtil.shopCodeValidate(value);
                    },
                    padding: EdgeInsets.fromLTRB(5, 5, 0, 5),
                    onChanged: (String? value) {
                      setState(() {
                        form.shopCode = value ?? '';
                      });
                    },
                    suffixIcon: IconButton(
                      icon: Icon(Icons.qr_code_scanner, size: 22),
                      onPressed: () => scanQr(),
                    ),
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                      border: Border(
                          bottom: BorderSide(
                              color: Color(0xFFEEEEEE), width: 1.0))),
                  child: BottomFormInputWidget(
                    value: brandMap[form.brandCode] ?? '',
                    focusNode: brandSelectNode,
                    label: '合作快递',
                    require: true,
                    readOnly: true,
                    labelWidth: 80,
                    hintText: '请输入合作快递',
                    textInputType: TextInputType.name,
                    validator: (value) {
                      return FormValidateUtil.brandCodeValidate(value);
                    },
                    controller: brandCodeController,
                    padding: EdgeInsets.fromLTRB(5, 5, 0, 5),
                    onChanged: (String? value) {
                      setState(() {
                        form.brandCode = value ?? '';
                      });
                    },
                    suffixIcon: Icon(Icons.arrow_drop_down_outlined, size: 22),
                  ),
                ),
                Container(
                  child: BottomFormInputWidget(
                    value: form.fee ?? '',
                    require: true,
                    label: '派件价格',
                    labelWidth: 80,
                    hintText: '请输入派件价格',
                    textInputType:
                        TextInputType.numberWithOptions(decimal: true),
                    textInputAction: TextInputAction.done,
                    controller: feeController,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp("[0-9.]"))
                    ],
                    validator: (value) {
                      return FormValidateUtil.isNotZeroNumber(value);
                    },
                    padding: EdgeInsets.fromLTRB(5, 5, 0, 5),
                    onChanged: (String? value) {
                      setState(() {
                        form.fee = value;
                      });
                    },
                    suffixIcon: Text('元/单',
                        style:
                            TextStyle(color: Theme.of(context).primaryColor)),
                  ),
                ),
              ],
            ),
          ),
        ),
        BottomBtnWidget(
          showShadow: true,
          type: ButtonType.primary,
          title: '提交',
          action: () async {
            addForm();
          },
        ),
      ],
    );
  }
}
