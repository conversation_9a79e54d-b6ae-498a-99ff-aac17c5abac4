import 'dart:math' as math;

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/entitys/shop_courier_entity.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppListState.dart';
import 'package:cabinet_flutter_app/widget/AppPullLoadWidget.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/BottomBtnWidget.dart';
import 'package:flutter/material.dart';

class CourierShopPage extends StatefulWidget {
  CourierShopPage({Key? key}) : super(key: key);

  @override
  _CourierShopPageState createState() => _CourierShopPageState();
}

class _CourierShopPageState extends State<CourierShopPage>
    with
        AutomaticKeepAliveClientMixin<CourierShopPage>,
        AppListState<CourierShopPage>,
        WidgetsBindingObserver,
        SingleTickerProviderStateMixin {
  bool isCourier = true;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void didChangeDependencies() {
    super.didChangeDependencies();
    isCourier = CheckUtils.isCourier(context);
  }

  toDetail(BuildContext context, {ShopCourierEntity? item}) async {
    await NavigatorUtils.goCourierShopDetailPage(context, item);
    handleRefresh();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const DefaultConfig().configs.BG_COLOR,
      appBar: AppCustomerBar(
        title: AppbarTitle(
          title: '驿站合作',
          isCenter: true,
        ),
        actions: [
          Container(
            width: 60,
          )
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: RefreshIndicator(
              onRefresh: onFresh,
              child: AppPullLoadWidget(
                pullLoadWidgetControl,
                (BuildContext context, int index) => _renderEventItem(index),
                handleRefresh,
                onLoadMore,
                refreshKey: refreshIndicatorKey,
              ),
            ),
          ),
          Offstage(
            offstage: !isCourier,
            child: BottomBtnWidget(
              showShadow: true,
              type: ButtonType.primary,
              title: '新增驿站合作',
              action: () async {
                await toDetail(context);
              },
            ),
          )
        ],
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }

  getName(ShopCourierEntity item) {
    String name = item.courierName ?? '';
    if (isCourier) {
      name = item.shopName ?? '';
    }
    return name;
  }

  getAddress(ShopCourierEntity item) {
    String name = item.courierMobile ?? '';
    if (isCourier) {
      name = item.shopAddress ?? '';
    }
    return name;
  }

  _renderEventItem(index) {
    ShopCourierEntity item = ShopCourierEntity.fromJson(dataList[index]);
    return InkWell(
      onTap: () async {
        await toDetail(context, item: item);
      },
      child: Stack(
        children: [
          Container(
            margin: EdgeInsets.only(top: 5),
            color: Colors.white,
            width: MediaQuery.of(context).size.width,
            child: Container(
              child: Row(
                children: [
                  Container(
                    width: 52,
                    height: 52,
                    color: Colors.black12,
                    padding: EdgeInsets.all(3),
                    margin: EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 10),
                    child: CircleAvatar(
                      radius: 10.0,
                      backgroundColor: Colors.white,
                      backgroundImage: AssetImage(CommonUtils.getExpressLogo(item.brandCode)),
                    ),
                  ),
                  Expanded(
                      child: Container(
                    padding: EdgeInsets.only(top: 10, bottom: 10),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          getName(item),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style:
                              TextStyle(fontWeight: FontWeight.w700, fontSize: 16, color: Colors.black87, height: 1.8),
                        ),
                        Offstage(
                          offstage: !isCourier,
                          child: Text(
                            item.shopMobile ?? '',
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                fontWeight: FontWeight.w700, fontSize: 16, color: Colors.black87, height: 1.8),
                          ),
                        ),
                        Text(
                          getAddress(item),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style:
                              TextStyle(fontWeight: FontWeight.w400, fontSize: 15, color: Colors.black54, height: 1.8),
                        ),
                        RichText(
                            text: TextSpan(children: [
                          TextSpan(
                              text: '派件价格:  ',
                              style: TextStyle(
                                  fontWeight: FontWeight.w400, color: Colors.black54, fontSize: 15, height: 1.8)),
                          TextSpan(
                              text: getFeeDisplay(item.deliveryFee),
                              style: TextStyle(
                                  fontWeight: FontWeight.w400,
                                  color: Theme.of(context).primaryColor,
                                  fontSize: 15,
                                  height: 1.8)),
                          WidgetSpan(child: getIcon(item.status))
                          // TextSpan(
                          //     text: DefaultConfig().configs.COURIER_SHOP_STATUS[item.status ?? 0],
                          //     style: TextStyle(
                          //         fontWeight: FontWeight.w400,
                          //         color: getColor(item.status),
                          //         backgroundColor: getColorGhost(item.status),
                          //         fontSize: 15)),
                        ])),
                      ],
                    ),
                  )),
                  Container(
                    child: Icon(Icons.chevron_right_outlined, size: 28, color: Colors.black38),
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  getColor(int? status) {
    int type = status ?? 0;
    Color color = Color(0xFFe6a23c);
    switch (type) {
      case 0:
        color = Color(0xFFe6a23c);
        break;
      case 1:
        color = Color(0xFF67c239);
        break;
      case 2:
        color = Color(0xFFf56c6c);
        break;
      case 3:
        color = Color(0xFF909399);
        break;
    }
    return color;
  }

  getColorGhost(int? status) {
    int type = status ?? 0;
    Color color = Color(0xFFfaecd8);
    switch (type) {
      case 0:
        color = Color(0xFFfaecd8);
        break;
      case 1:
        color = Color(0xFFe1f3d8);
        break;
      case 2:
        color = Color(0xFFfef0f0);
        break;
      case 3:
        color = Color(0xFFe9e9eb);
        break;
    }
    return color;
  }

  getIcon(int? status) {
    String text = DefaultConfig().configs.COURIER_SHOP_STATUS[status ?? 0];
    return Transform(
        transform: Matrix4.identity()..rotateZ(0 * math.pi / 180),
        child: Container(
          margin: EdgeInsets.only(left: 25, top: 5),
          padding: EdgeInsets.only(left: 5, right: 5),
          decoration: BoxDecoration(
            color: getColorGhost(status),
            borderRadius: BorderRadius.all(Radius.circular(4.0)),
          ),
          child: Text(text, style: TextStyle(color: getColor(status), fontSize: 14)),
        ));
  }

  getFeeDisplay(double? fee) {
    String text = '0.0元/单';
    if (fee != null) {
      text = '${fee / 1000}元/单';
    }
    return text;
  }

  Future<Null> onLoadMore() async {
    if (isLoading) {
      return null;
    }
    isLoading = true;
    page++;
    var res = await requestLoadMore();
    if (res != null && res.result) {
      setState(() {
        pullLoadWidgetControl.dataList.addAll(res.data);
      });
    }
    resolveDataResult(res);
    isLoading = false;
    return null;
  }

  @protected
  Future<Null> handleRefresh() async {
    if (isLoading) {
      return null;
    }
    refreshIndicatorKey.currentState?.show();
    isLoading = true;
    page = 1;
    var res = await requestRefresh();
    resolveRefreshResult(res);
    resolveDataResult(res);
    if (res.next != null) {
      var resNext = await res.next;
      resolveRefreshResult(resNext);
      resolveDataResult(resNext);
    }
    isLoading = false;
    return null;
  }

  // 上拉加载更多
  _getData({isRefresh = false}) async {
    Map<String, dynamic> info = {
      'current': page.toString(),
      'size': 20,
    };
    DataResult res = await CourierDao.getCourierShopList(info);
    return res;
  }

// TODO: implement wantKeepAlive
  @override
  bool get wantKeepAlive => true;

  // TODO: implement isRefreshFirst
  @override
  bool get isRefreshFirst => true;

  @override
  requestLoadMore() async {
    return await _getData();
  }

  //
  Future<Null> onFresh() async {
    _getData(isRefresh: true);
  }

  @override
  requestRefresh() async {
    return await _getData();
  }
}
