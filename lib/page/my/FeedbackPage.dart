import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';

class FeedbackPage extends StatefulWidget {
  @override
  _FeedbackPageState createState() => _FeedbackPageState();
}

class _FeedbackPageState extends State<FeedbackPage> {
  final TextEditingController _feedbackController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final ImagePicker _picker = ImagePicker();
  
  String selectedType = 'APP建议';
  List<String> feedbackTypes = ['APP建议', '功能建议', '其他'];
  List<File> selectedImages = [];
  List<String> uploadedImageUrls = []; // 存储上传到OSS的图片URL
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: Colors.black, size: 18.sp),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          '意见反馈',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
        actions: [
          TextButton(
            onPressed: () {
              NavigatorUtils.goToMyFeedbackPage(context);
            },
            child: Text(
              '我的反馈',
              style: TextStyle(
                color: Colors.black54,
                fontSize: 14.sp,
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 意见类型
            _buildSectionTitle('意见类型'),
            SizedBox(height: 12.h),
            _buildTypeSelector(),
            
            SizedBox(height: 12.h),
            
            // 意见输入
            _buildSectionTitle('请留下您的宝贵意见或建议'),
            SizedBox(height: 12.h),
            _buildFeedbackInput(),
            
            SizedBox(height: 12.h),
            
            // 图片上传
            _buildImageUpload(),
            
            SizedBox(height: 12.h),
            
            // 联系方式
            _buildSectionTitle('联系方式'),
            SizedBox(height: 12.h),
            _buildPhoneInput(),
            
            SizedBox(height: 40.h),
            
            // 提交按钮
            _buildSubmitButton(),
          ],
        ),
      ),
    );
  }
  
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.w500,
        color: Colors.black87,
      ),
    );
  }
  
  Widget _buildTypeSelector() {
    return Row(
      children: feedbackTypes.map((type) {
        bool isSelected = selectedType == type;
        return GestureDetector(
          onTap: () {
            setState(() {
              selectedType = type;
            });
          },
          child: Container(
            margin: EdgeInsets.only(right: 12.w),
            padding: EdgeInsets.symmetric(horizontal: 14.w, vertical: 4.h),
            decoration: BoxDecoration(
              color: isSelected ? Theme.of(context).primaryColor: Colors.white,
              borderRadius: BorderRadius.circular(20.r),
              border: Border.all(
                color: isSelected ? Theme.of(context).primaryColor : Color(0xFFE0E0E0),
                width: 1,
              ),
            ),
            child: Text(
              type,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.black54,
                fontSize: 14.sp,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
  
  Widget _buildFeedbackInput() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: TextField(
        controller: _feedbackController,
        maxLines: 6,
        decoration: InputDecoration(
          hintText: '请详细描述您遇到的问题或建议...',
          hintStyle: TextStyle(
            color: Colors.grey[400],
            fontSize: 14.sp,
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(16.w),
        ),
      ),
    );
  }
  
  Widget _buildImageUpload() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.camera_alt, size: 20.sp, color: Colors.grey[600]),
            SizedBox(width: 8.w),
            Text(
              '上传图片（最多3张）',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        Wrap(
          spacing: 12.w,
          runSpacing: 12.h,
          children: [
            ...selectedImages.map((image) => _buildImageItem(image)).toList(),
            if (selectedImages.length < 3) _buildAddImageButton(),
          ],
        ),
      ],
    );
  }
  
  Widget _buildImageItem(File image) {
    return Stack(
      children: [
        Container(
          width: 80.w,
          height: 80.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.r),
            image: DecorationImage(
              image: FileImage(image),
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          top: 2,
          right: 2,
          child: GestureDetector(
            onTap: () {
              setState(() {
                int index = selectedImages.indexOf(image);
                selectedImages.remove(image);
                if (index >= 0 && index < uploadedImageUrls.length) {
                  uploadedImageUrls.removeAt(index);
                }
              });
            },
            child: Container(
              width: 20.w,
              height: 20.w,
              decoration: BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.close,
                color: Colors.white,
                size: 14.sp,
              ),
            ),
          ),
        ),
      ],
    );
  }
  
  Widget _buildAddImageButton() {
    return GestureDetector(
      onTap: _pickImage,
      child: Container(
        width: 80.w,
        height: 80.w,
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: Colors.grey[300]!, width: 1),
        ),
        child: Icon(
          Icons.add,
          color: Colors.grey[400],
          size: 30.sp,
        ),
      ),
    );
  }
  
  Widget _buildPhoneInput() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: TextField(
        controller: _phoneController,
        keyboardType: TextInputType.emailAddress,
        decoration: InputDecoration(
          hintText: '请输入手机号或邮箱（选填）',
          hintStyle: TextStyle(
            color: Colors.grey[400],
            fontSize: 14.sp,
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(16.w),
        ),
      ),
    );
  }
  
  Widget _buildSubmitButton() {
    return Container(
      width: double.infinity,
      height: 35.h,
      child: ElevatedButton(
        onPressed: _submitFeedback,
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24.r),
          ),
          elevation: 0,
        ),
        child: Text(
          '提交',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
  
  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 80,
      );
     
      if (image != null) {
        setState(() {
          selectedImages.add(File(image.path));
        });
        // 上传图片到OSS
        try {
          String fileName = 'feedback_${DateTime.now().millisecondsSinceEpoch}';
          print('上传图片 image: ${image.path}');
          String imageUrl = await CheckUtils.checkPhotoUpload(
            image.path ?? '', 
            fileName, 
            'feedback', 
            'feedback_brand', 
            'feedback',
            type: 'ck'
          );
          if (imageUrl.isNotEmpty) {
            uploadedImageUrls.add(imageUrl);
            print('图片上传到OSS成功: $imageUrl');
          }
        } catch (e) {
          print('图片上传到OSS失败: $e');
          Fluttertoast.showToast(
            msg: '图片上传失败',
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.CENTER,
          );
        }
      }
    } catch (e) {
      Fluttertoast.showToast(
        msg: '选择图片失败',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
      );
    }
  }
  
  Future<void> _submitFeedback() async {
    if (_feedbackController.text.trim().isEmpty) {
      Fluttertoast.showToast(
        msg: '请输入反馈内容',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
      );
      return;
    }

    try {
      Map<String, dynamic> params = {
        'feedbackType': selectedType == 'APP建议' ? 1 : selectedType == '功能建议' ? 2 : 3,
        'content': _feedbackController.text.trim(),
        'imageUrls': uploadedImageUrls.join(','), // 图片URL组合成字符串
        'contact': _phoneController.text.trim().isNotEmpty ? _phoneController.text.trim() : '', // 使用输入的手机号或邮箱
      };
      print('图片params: ${params['feedbackType']}');
      // 调用API提交反馈
      var resultData = await CourierDao.submitFeedback(params);

      if (resultData.result) {
        Fluttertoast.showToast(
          msg: '反馈提交成功',
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.CENTER,
        );

        // 清空表单
        _feedbackController.clear();
        setState(() {
          selectedImages.clear();
          uploadedImageUrls.clear();
          selectedType = 'APP建议';
        });

        // 返回上一页
        Navigator.of(context).pop();
      } else {
         Fluttertoast.showToast(
           msg: '提交失败',
           toastLength: Toast.LENGTH_SHORT,
           gravity: ToastGravity.CENTER,
         );
       }
    } catch (e) {
      print('提交反馈失败: $e');
      Fluttertoast.showToast(
        msg: '网络错误，请稍后重试',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
      );
    }
  }
  
  @override
  void dispose() {
    _feedbackController.dispose();
    _phoneController.dispose();
    super.dispose();
  }
}