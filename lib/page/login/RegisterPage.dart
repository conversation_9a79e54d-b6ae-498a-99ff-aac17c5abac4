import 'dart:async';
import 'dart:io';

import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CabinetDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/FormValidateUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/utils/RegExpUtil.dart';
import 'package:cabinet_flutter_app/common/utils/SoundUtils.dart';
import 'package:cabinet_flutter_app/page/login/AgreementPage.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/MyTextFormField.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CopyrightWdiget.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:redux/src/store.dart';
import 'package:scan/scan.dart';

class RegisterPage extends StatefulWidget {
  final String mobile;
  final int userType;

  RegisterPage(this.mobile, this.userType, {Key? key}) : super(key: key);

  @override
  _RegisterPage createState() => _RegisterPage();
}

class _RegisterPage extends State<RegisterPage> {
  bool isCheck = false;
  Scan scanPlugin = Scan();
  Map<dynamic, dynamic> brandMap = {
    'ANE': '安能',
    'DBKD': '德邦',
    'EMS': 'EMS',
    'FW': '丰网',
    'HTKY': '百世',
    'JD': '京东',
    'JT': '极兔',
    'POSTB': '邮政',
    'SF': '顺丰',
    'SNWL': '苏宁',
    'STO': '申通',
    'UC56': '优速',
    'YTO': '圆通',
    'YUNDA': '韵达',
    'CAINIAO': '菜鸟',
    'ZTO': '中通'
  };
  TextEditingController phoneController = TextEditingController();

  //验证码的控制器
  TextEditingController _codeController = TextEditingController();

  //密码的控制器
  TextEditingController _pwdController = TextEditingController();
  final GlobalKey<FormState> _formKey = new GlobalKey<FormState>();

  //确认密码的控制器
  TextEditingController _rePwdController = TextEditingController();

  //确认密码的控制器
  TextEditingController _shopCodeController = TextEditingController();
  final FocusNode brandSelectNode = FocusNode();
  TextEditingController _brandCodeController = TextEditingController();
  String brandCode = '';
  final TapGestureRecognizer recognizer = TapGestureRecognizer();
  final TapGestureRecognizer recognizer2 = TapGestureRecognizer();

  bool showBrandSelectModal = false;
  bool isCode = false;
  bool isPhone = false;
  bool isPw = false;
  bool isShopCode = false;
  bool isRePw = false;

  Timer? _timer;

  //倒计时数值
  var _enable = true;
  var _time = 0;

  //倒计时方法
  void startCountdown(int count) {
    if (!_enable) return;
    setState(() {
      _enable = false;
      _time = count;
    });
    //倒计时时间
    _timer = Timer.periodic(Duration(seconds: 1), (Timer it) {
      setState(() {
        if (it.tick == count) {
          _enable = true;
          it.cancel();
        }
        _time = count - it.tick;
      });
    });
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _codeController.addListener(() {
      setState(() {
        if (_codeController.text != '') {
          isCode = true;
        } else {
          isCode = false;
        }
        setState(() {});
      });
    });
    phoneController.addListener(() {
      if (phoneController.text != '') {
        if (CheckUtils.isMobile(phoneController.text)) {
          isPhone = true;
        } else {
          isPhone = false;
        }
      } else {
        isPhone = false;
      }
      setState(() {});
    });
    _pwdController.addListener(() {
      if (_pwdController.text != '') {
        isPw = true;
      } else {
        isPw = false;
      }
    });
    _rePwdController.addListener(() {
      if (_rePwdController.text != '') {
        isRePw = true;
      } else {
        isRePw = false;
      }
      setState(() {});
    });
    _shopCodeController.addListener(() {
      if (_shopCodeController.text != '') {
        isShopCode = true;
      } else {
        isShopCode = false;
      }
      setState(() {});
    });
    brandSelectNode.addListener(() {
      bool isFocus = brandSelectNode.hasFocus;
      if (isFocus && !showBrandSelectModal) {
        selectBrand();
      } else {}
    });
    phoneController.value = TextEditingValue(text: widget.mobile ?? '');
    recognizer.onTap = () {
      Navigator.push(context, new MaterialPageRoute(builder: (context) {
        return AgreementPage(
            url: DefaultConfig().configs.WEB_URL + DefaultConfig().configs.USER_AGREE,
            isLocalUrl: false,
            title: '${DefaultConfig().configs.APP_NAME}协议');
      }));
    };
    recognizer2.onTap = () {
      Navigator.push(context, new MaterialPageRoute(builder: (context) {
        return AgreementPage(
            url: DefaultConfig().configs.WEB_URL + DefaultConfig().configs.USER_AGREE1,
            isLocalUrl: false,
            title: '隐私政策');
      }));
    };
    setState(() {});
  }

  register() async {
    if (!isPhone) {
      Fluttertoast.showToast(msg: '请输入正确的手机号');
      return false;
    }
    if (!isCode) {
      Fluttertoast.showToast(msg: '请输入验证码');
      return false;
    }
    if (!isPw) {
      Fluttertoast.showToast(msg: '请输入密码');
      return false;
    }
    if (!isRePw) {
      Fluttertoast.showToast(msg: '请再次输入密码');
      return false;
    }
    if (_pwdController.text != _rePwdController.text) {
      Fluttertoast.showToast(msg: '密码不一致，请检查');
      return false;
    }
    if (!isCheck) {
      Fluttertoast.showToast(msg: '请勾选服务协议和隐私政策');
      return false;
    }

    if (!isCourier()) {
      if (!isShopCode) {
        Fluttertoast.showToast(msg: '请输入门店编码');
        return false;
      }
      registerStation();
    } else {
      if (brandCode == '') {
        Fluttertoast.showToast(msg: '请选择快递公司');
        return false;
      }
      registerCourier();
    }
  }

  /// userType=4  快递员 6 门店
  registerStation() async {
    LoadingUtil(
      status: '数据请求中...',
    ).show(context);
    Map<String, dynamic> info = {
      'loginName': phoneController.text,
      'password': _pwdController.text,
      'smsCode': _codeController.text,
      'shopCode': _shopCodeController.text.trim(),
    };
    if (info['shopCode'] == '') {
      Fluttertoast.showToast(msg: '请输入门店编码');
       LoadingUtil.dismiss(context);
      return false;
    }
    var res = await UserDao.shopRegister(info);
    LoadingUtil.dismiss(context);
    if (res != null && res.result) {
      LoadingUtil(
        status: '数据请求中...',
      ).show(context);
      Store<AppState> store = StoreProvider.of(context);
      UserDao.login({'loginName': phoneController.text, 'password': _pwdController.text}, store).then((res) async {
        LoadingUtil.dismiss(context);
        if (res != null && res.result) {
          var _res = await UserDao.getUserInfo(store);
          if (_res != null && _res.result) {
            if (_res.data is UserEntity) {
              UserEntity user = _res.data;
              if (user.hasReal == 0) {
                NavigatorUtils.goRealName(context, user.userType!);
              } else {
                NavigatorUtils.goHome(context);
              }
            }
          }
        } else {
          NavigatorUtils.goLoginNew(context);
        }
      });
    }
  }

  /// userType=4  快递员 6 门店
  registerCourier() async {
    LoadingUtil(
      status: '数据请求中...',
    ).show(context);
    Map<String, dynamic> info = {
      'loginName': phoneController.text,
      'password': _pwdController.text,
      'smsCode': _codeController.text,
      'brandCode': brandCode,
    };
    var res = await UserDao.courierRegister(info);
    LoadingUtil.dismiss(context);
    if (res != null && res.result) {
      LoadingUtil(
        status: '数据请求中...',
      ).show(context);
      Store<AppState> store = StoreProvider.of(context);
      UserDao.login(
              {'loginName': phoneController.text, 'password': _pwdController.text, 'userType': widget.userType}, store)
          .then((res) async {
        LoadingUtil.dismiss(context);
        if (res != null && res.result) {
          var _res = await UserDao.getUserInfo(store);
          if (_res != null && _res.result) {
            if (_res.data is UserEntity) {
              UserEntity user = _res.data;
              if (user.hasReal == 0) {
                NavigatorUtils.goRealName(context, user.userType!);
              } else if (user.hasReal == 1) {
                if (user.brands?.length == 0) {
                  NavigatorUtils.goBrandBind(context);
                } else {
                  NavigatorUtils.goHome(context);
                }
              }
            }
          }
        } else {
          NavigatorUtils.goLoginNew(context);
        }
      });
    }
  }

  /// 品牌选择
  selectBrand() {
    if (showBrandSelectModal) {
      return false;
    }
    SoundUtils.audioPushFn(SoundUtils.CHOOSE_COMPANY);
    showBrandSelectModal = true;
    brandSelectNode.unfocus();
    CommonUtils.showBottomBrandSelectModal(context, brandMap, (item) async {
      SoundUtils.audioPushFn(SoundUtils.BRAND_SOUND[item]!);
      if (item != null) {
        brandCode = item;
        _brandCodeController.value = TextEditingValue(text: brandMap[item]);
        showBrandSelectModal = false;
      }
    }, showText: 'show', isForce: true, bindCompany: brandCode);
  }

  isCourier() {
    return widget.userType == 4;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: new AppCustomerBar(
          color: Colors.white,
          iconColor: Colors.black,
          title: new AppbarTitle(
            title: '',
            isCenter: true,
          ),
          actions: <Widget>[
            Container(
              width: 50,
            )
          ],
        ),
        body: SingleChildScrollView(
            child: Container(
          child: Stack(
            children: [
              Container(
                height: MediaQuery.of(context).size.height - 80,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: EdgeInsets.fromLTRB(20, 5, 0, 0),
                      child: Text(
                        '填写信息可快速注册',
                        style: TextStyle(color: Colors.black, fontSize: 20),
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.fromLTRB(20, 0, 25, 0),
                      child: Form(
                        key: _formKey,
                        autovalidateMode: AutovalidateMode.onUserInteraction,
                        child: Column(
                          children: <Widget>[
                            SizedBox(height: 12),
                            buildLoginName(),
                            SizedBox(height: 8),
                            buildCode(),
                            SizedBox(height: 8),
                            buildPassword(),
                            SizedBox(height: 8),
                            buildRePassword(),
                            SizedBox(height: 8),
                            Offstage(
                              offstage: isCourier(),
                              child: buildShopCode(),
                            ),
                            Offstage(
                              offstage: !isCourier(),
                              child: buildBrandSelect(),
                            ),
                            Container(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Container(
                                    margin: EdgeInsets.only(top: 14),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: <Widget>[
                                        InkWell(
                                          onTap: () {
                                            setState(() {
                                              isCheck = !isCheck;
                                            });
                                          },
                                          child: Container(
                                            height: 30.0,
                                            child: Container(
                                              width: 10.0,
                                              height: 10.0,
                                              margin: EdgeInsets.fromLTRB(5, 0.0, 10.0, 0.0),
                                              child: Checkbox(
                                                value: isCheck,
                                                activeColor: Theme.of(context).primaryColor,
                                                onChanged: (bool? value) {
                                                  setState(() {
                                                    isCheck = value ?? false;
                                                  });
                                                },
                                              ),
                                            ),
                                          ),
                                        ),
                                        Container(
                                          width: MediaQuery.of(context).size.width - 70,
                                          child: RichText(
                                              maxLines: 2,
                                              text: TextSpan(children: <TextSpan>[
                                                TextSpan(
                                                    text: '我已阅读并同意',
                                                    style: TextStyle(color: Colors.grey.shade600, fontSize: 13.0)),
                                                TextSpan(
                                                    text: '《${DefaultConfig().configs.APP_NAME}协议》',
                                                    recognizer: recognizer,
                                                    style: TextStyle(
                                                        color: Theme.of(context).primaryColor,
                                                        fontSize: 13.0,
                                                        fontWeight: FontWeight.w600)),
                                                TextSpan(
                                                    text: '和',
                                                    style: TextStyle(color: Colors.grey.shade600, fontSize: 13.0)),
                                                TextSpan(
                                                    text: '《隐私政策》',
                                                    recognizer: recognizer2,
                                                    style: TextStyle(
                                                        color: Theme.of(context).primaryColor,
                                                        fontSize: 13.0,
                                                        fontWeight: FontWeight.w600)),
                                              ])),
                                        )
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              width: double.infinity,
                              margin: EdgeInsets.only(top: 40),
                              height: 47,
                              child: MaterialButton(
                                color: isCode && isPw && isRePw && isCheck && isPhone
                                    ? Theme.of(context).primaryColor
                                    : Colors.grey.shade500,
                                textColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20.0),
                                ),
                                onPressed: () {
                                  register();
                                },
                                child: Text(CommonUtils.getLocale(context).loginAndRegister,
                                    style: TextStyle(fontSize: 18)),
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
              Positioned(
                left: 0,
                right: 0,
                bottom: 15,
                child: CopyrightWidget(),
              )
            ],
          ),
        )));
  }

  /// 验证码输入框
  Widget buildCode() {
    return MyTextFormField(
      labelText: "请输入验证码",
      inputFormatters: [LengthLimitingTextInputFormatter(6)],
      suffixIcon: TextButton(
        child: Text(countTime <= 0 ? '发送验证码' : "$countTime秒后重试"),
        style: ButtonStyle(foregroundColor: MaterialStateProperty.all<Color>(Theme.of(context).primaryColor)),
        onPressed: countTime <= 0 ? sendCode : null,
      ),
      onChanged: (value) {},
      controller: _codeController,
      validator: (value) {
        if (value == '') {
          return "请输入验证码";
        }
        return null;
      },
    );
  }

  /// 发送验证码
  Future<void> sendCode() async {
    bool matched = RegExpUtil.isPhone(phoneController.text);
    if (!matched) {
      Fluttertoast.showToast(msg: '手机号格式错误');
      return;
    }
    Map<String, dynamic> data = {"loginName": phoneController.text, 'smsTplCode': 'REGISTER_SMS'};
    var res = await UserDao.sendSms(data);
    if (res != null && res.result) {
      if (res.data) {
        Fluttertoast.showToast(msg: '验证码发送成功,请查收');
        startTimer();
      } else {
        Fluttertoast.showToast(msg: '验证码发送失败请重试');
      }
    }
  }

  var countTime = 0;
  Timer? timer;

  /// 计时器
  void startTimer() {
    countTime = 60;
    if (timer != null) {
      return;
    }
    timer = Timer.periodic(Duration(seconds: 1), (time) {
      setState(() {
        if (countTime > 0) {
          countTime--;
        } else {
          timer?.cancel();
          timer = null;
        }
      });
    });
  }

  /// 密码输入框
  MyTextFormField buildPassword() => MyTextFormField(
        labelText: "请输入密码",
        isPassword: true,
        inputFormatters: [LengthLimitingTextInputFormatter(20)],
        keyboardType: TextInputType.text,
        onChanged: (value) {},
        controller: _pwdController,
      );

  /// 手机号输入框
  MyTextFormField buildLoginName() {
    return MyTextFormField(
      labelText: "请输入手机号",
      isPassword: false,
      inputFormatters: [LengthLimitingTextInputFormatter(11)],
      keyboardType: TextInputType.number,
      controller: phoneController,
      onChanged: (value) {},
      validator: (value) => FormValidateUtil.phoneValidate2(value!, context),
    );
  }

  /// 确认密码输入框
  MyTextFormField buildRePassword() {
    return MyTextFormField(
      labelText: "请再次输入密码",
      isPassword: true,
      inputFormatters: [LengthLimitingTextInputFormatter(20)],
      keyboardType: TextInputType.text,
      controller: _rePwdController,
      onChanged: (value) {},
      // hintText: "请再次输入密码",
      // leftIcon: Icon(Icons.lock),
      validator: (value) {
        if (value != null) {
          if (value.trim().length < 6) {
            return "请输入至少6位长度的密码";
          } else if (_rePwdController.text != _pwdController.text) {
            return "两次输入不正确";
          }
          return null;
        } else {
          return "请输入至少6位长度的密码";
        }
      },
    );
  }

  scanQr() async {
    String? res = await NavigatorUtils.goCabinetBindPage(context, scanAction: ScanAction.SCANQR);
    if (res != null) {
      DataResult result = await CabinetDao.decryptShopCode(res);
      if (result.result) {
        if (result.data != null) {
          _shopCodeController.value = TextEditingValue(text: result.data);
        }
      }
    }
    setState(() {});
  }

  /// 确认密码输入框
  MyTextFormField buildShopCode() {
    return MyTextFormField(
      
      labelText: "请输入或扫码获取门店编码",
      inputFormatters: [
        FilteringTextInputFormatter.deny(RegExp(r'\s')), // 禁止所有空格字符
        LengthLimitingTextInputFormatter(20)
        ],
      keyboardType: TextInputType.text,
      controller: _shopCodeController,
      onChanged: (value) {},
      suffixIcon: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _shopCodeController.text.isNotEmpty
              ? IconButton(
                  icon: Icon(Icons.clear),
                  onPressed: () {
                    _shopCodeController.value = TextEditingValue(text: '');
                  },
                )
              : Text(''),
          Offstage(
            offstage: false,
            child: IconButton(
              icon: Icon(Icons.qr_code_scanner, size: 22),
              onPressed: () => scanQr(),
            ),
          )
        ],
      ),
      validator: (value) {
        // print('value = w${value}s'); 
        if (value != null && value.trim() != '') {
          return null;
        }
        return "请输入门店编码";
      },
    );
  }

  /// 确认密码输入框
  MyTextFormField buildBrandSelect() {
    return MyTextFormField(
      labelText: "请选择快递公司",
      readOnly: true,
      keyboardType: TextInputType.text,
      focusNode: brandSelectNode,
      controller: _brandCodeController,
      suffixIcon: Icon(Icons.arrow_drop_down_outlined, size: 22),
      onChanged: (value) {},
      validator: (value) {
        return null;
      },
    );
  }

  @override
  void dispose() {
    super.dispose();
    recognizer.dispose();
    recognizer2.dispose();
    timer?.cancel();
    _timer?.cancel();
    timer = null;
  }
}
