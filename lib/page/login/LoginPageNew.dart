import 'dart:async';
import 'dart:io';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/FormValidateUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/utils/RegExpUtil.dart';
import 'package:cabinet_flutter_app/common/utils/SoundUtils.dart';
import 'package:cabinet_flutter_app/common/utils/ThrottleUtil.dart';
import 'package:cabinet_flutter_app/widget/MyTextFormField.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CopyrightWdiget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:redux/src/store.dart';
import 'package:scan/scan.dart';

import 'AgreementPage.dart';

class LoginPageNew extends StatefulWidget {
  static final String sName = "loginNew";

  final String mobile;

  LoginPageNew(this.mobile, {Key? key}) : super(key: key);

  @override
  State createState() {
    return _LoginPageNew();
  }
}

class _LoginPageNew extends State<LoginPageNew> {
  var _userName = "";
  var _password = "";
  String deviceId = "";
  String deviceName = "";
  String brand = "";
  String ip = "";
  String lastPwd = "";
  Timer? t;
  Timer? settingTimer;
  int count = 5;
  int settingCount = 3;
  String version = "";
  String title = "";
  bool isCheck = false;
  final TextEditingController passwordController = TextEditingController();
  FocusNode passwordTextFieldNode = FocusNode();

  final TextEditingController loginNameController = TextEditingController();
  FocusNode loginNameTextFieldNode = FocusNode();
  ThrottleUtil throttleUtil = ThrottleUtil();
  final TapGestureRecognizer recognizer = TapGestureRecognizer();
  final TapGestureRecognizer recognizer2 = TapGestureRecognizer();

  Scan scan = Scan();
  bool isPw = false;
  bool isUserName = false;
  bool isOptimizeNetwork = true;

  Map<dynamic, dynamic> brandMap = {
    'ANE': '安能',
    'DBKD': '德邦',
    'EMS': 'EMS',
    'FW': '丰网',
    'HTKY': '百世',
    'JT': '极兔',
    'POSTB': '邮政',
    'SNWL': '苏宁',
    'STO': '申通',
    'UC56': '优速',
    'YTO': '圆通',
    'YUNDA': '韵达',
    'ZTO': '中通'
  };

  @override
  void initState() {
    recognizer.onTap = () {
      Navigator.push(context, new MaterialPageRoute(builder: (context) {
        return AgreementPage(
            url: DefaultConfig().configs.WEB_URL + DefaultConfig().configs.USER_AGREE,
            isLocalUrl: false,
            title: '${DefaultConfig().configs.APP_NAME}协议');
      }));
    };
    recognizer2.onTap = () {
      Navigator.push(context, new MaterialPageRoute(builder: (context) {
        return AgreementPage(
            url: DefaultConfig().configs.WEB_URL + DefaultConfig().configs.USER_AGREE1,
            isLocalUrl: false,
            title: '隐私政策');
      }));
    };
    super.initState();
    initParams();
    showPrivacyModal();
    getCheckUser();
  }

  getCheckUser() async {
    var res = await LocalStorage.get<Map>(DefaultConfig().configs.LOGIN_USER);
    if (res != null) {
      Store<AppState> store = StoreProvider.of(context);
      Future.delayed(Duration(seconds: 1), () async {
        setState(() {
          _userName = res['loginName'];
          _password = res['password'];
          loginNameController.value = TextEditingValue(text: _userName);
          passwordController.value = TextEditingValue(text: _password);
        });
        await login(store);
      });
    }
  }

  showPrivacyModal() async {
    var res = await LocalStorage.get(DefaultConfig().configs.IS_NEW);
    if (res == null) {
      CommonUtils.privacyModal(context, '', title: '温馨提示', actions: <Widget>[
        Column(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          Container(
            width: 300,
            decoration: BoxDecoration(
              color: DefaultConfig().configs.PRIMARY_COLOR,
              borderRadius: BorderRadius.all(
                Radius.circular(10.0),
              ),
            ),
            child: TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                  setState(() {
                    isCheck = true;
                  });
                  LocalStorage.save(DefaultConfig().configs.IS_NEW, true);
                },
                child: Text('同意并继续', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
                style: ButtonStyle(foregroundColor: MaterialStateProperty.resolveWith((states) {
                  return Colors.white;
                }), backgroundColor: MaterialStateProperty.resolveWith((states) {
                  return DefaultConfig().configs.PRIMARY_COLOR;
                }))),
          ),
          Padding(padding: EdgeInsets.only(top: 10)),
          Container(
            width: 300,
            decoration: BoxDecoration(
              border: Border.all(width: 1, color: Color(0xFFEEEEEE)),
              borderRadius: BorderRadius.all(
                Radius.circular(10.0),
              ),
            ),
            child: TextButton(
                onPressed: () async {
                  exit(0);
                },
                child: Text('不同意', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
                style: ButtonStyle(foregroundColor: MaterialStateProperty.resolveWith((states) {
                  return Colors.black;
                }), backgroundColor: MaterialStateProperty.resolveWith((states) {
                  return DefaultConfig().configs.WHITE_COLOR;
                }))),
          )
        ])
      ], contentList: [
        Container(
          height: 250,
          child: SingleChildScrollView(
            child: IntrinsicHeight(
              child: Column(
                children: [
                  Container(
                    child: RichText(
                        text: TextSpan(children: <TextSpan>[
                      TextSpan(
                          text: '欢迎使用本产品，本产品非常重视您的隐私和个人信息。在您使用本产品前请认真阅读',
                          style: TextStyle(color: Colors.grey.shade600, fontSize: 16.0)),
                      TextSpan(
                          text: '《${DefaultConfig().configs.APP_NAME}协议》',
                          recognizer: recognizer,
                          style: TextStyle(
                              color: Theme.of(context).primaryColor, fontSize: 16.0, fontWeight: FontWeight.w600)),
                      TextSpan(text: '及', style: TextStyle(color: Colors.grey.shade600, fontSize: 16.0)),
                      TextSpan(
                          text: '《隐私政策》',
                          recognizer: recognizer2,
                          style: TextStyle(
                              color: Theme.of(context).primaryColor, fontSize: 16.0, fontWeight: FontWeight.w600)),
                      TextSpan(
                          text: '您接受并同意条款后即可开始使用本产品。另外为了更好地使用产品功能，为了保障产品的核心功能以及其他功能正常运行，需征求您的允许，我们会收集您的部分必要信息和权限:',
                          style: TextStyle(color: Colors.grey.shade600, fontSize: 16.0)),
                    ])),
                  ),
                  Padding(padding: EdgeInsets.only(top: 10)),
                  Text(
                      '1.当您使用账号注册及登录时，我们会收集由您主动提供给我们的一些单独或者结合识别您实名身份的信息，包括：手机号码、验证码匹配结果、账号密码。我们收集这些信息是用以完成注册程序、为您持续稳定提供专属于注册用户的产品与/或服务，并保护您的账号安全。您应知悉，手机号码、验证码匹配结果和账号密码属于您的个人敏感信息，我们收集该类信息是为了满足相关法律法规的要求，如您拒绝提供可能导致您无法使用我们的此功能，请您谨慎考虑后再提供。同时，您也可以在注册时或后续使用过程中填写或补充您的昵称、头像、所在城市。我们收集这些信息是用于为您提供账号登录服务以及保障您的账号安全，防范安全风险。如您拒绝授权此类信息的，您将无法使用需登录的产品和服务。',
                      style: TextStyle(fontSize: 16.0)),
                  Padding(padding: EdgeInsets.only(top: 10)),
                  Text(
                      '2.设备信息：我们会根据您在软件安装及/或使用中的具体操作，接收并记录您所使用的设备相关信息（包括设备型号、操作系统版本、设备MAC地址、软件列表、设备设置、唯一设备标识符 、设备环境）、设备所在位置相关信息（包括您授权的GPS位置、WLAN接入点、蓝牙和基站信息获取到的精确地理位置信息及通过网络位置信息（例如基站、IP及WLAN）获取的大致地理位置信息。',
                      style: TextStyle(fontSize: 16.0)),
                  Padding(padding: EdgeInsets.only(top: 10)),
                  Text(
                      '3.服务日志信息：当您使用我们的网站或客户端提供的产品或服务时，我们会自动收集您对我们服务的详细使用情况，作为服务日志保存，包括浏览、点击查看、搜索查询、收藏、添加至购物车、交易、售后、关注分享信息、发布信息，以及IP地址、浏览器类型、电信运营商、使用语言、访问日期和时间。',
                      style: TextStyle(fontSize: 16.0)),
                  Padding(padding: EdgeInsets.only(top: 10)),
                  Text(
                      '4.当您使用身份认证功能或服务时，为满足相关法律规定及监管要求、确保用户身份真实性、实现反欺诈、反恐怖主义等风险控制、保障系统和服务安全、国家安全或提供服务之需要，我们将需要收集您的真实身份信息（可能包括姓名、身份证相关信息、面部特征信息（静态或动态）、手机号码、所属公司及工作凭证）以完成实名认证，相关业务场景可能包括：注册产品时、熊猫智能柜助手认证、使用物流末端派送等服务时。',
                      style: TextStyle(fontSize: 16.0)),
                  Padding(padding: EdgeInsets.only(top: 10)),
                  Text(
                      '5.当您使用投递服务时，我们会收集您的手机号码、派送的运单号，来保障您正常使用我们的服务。 您可以通过熊猫智能柜为其他人派送快件，您需要提供该实际收货人的前述个人信息，并确保已取得该实际收货人的同意',
                      style: TextStyle(fontSize: 16.0)),
                  Padding(padding: EdgeInsets.only(top: 10)),
                  Text(
                      '6.当您使用熊猫智能柜附近点位、收件、派件、租用箱格等功能时，我们会在获得您的自主选择同意后，获得您的地理位置授权，目的是为了向您提供该服务。地理位置权限是您设备上的一项设置，您可以通过设备设置页面进行管理，拒绝提供该信息仅会使您无法使用上述功能，但不影响您正常使用熊猫智能柜的其他功能。',
                      style: TextStyle(fontSize: 16.0)),
                  Padding(padding: EdgeInsets.only(top: 10)),
                  Text(
                      '7.当您使用APP设置头像、图片上传、扫一扫、拍摄功能时，我们会在获得您的自主选择同意后，获取您的相册信息或申请相机权限，目的是为了向您提供该服务。相机/相册权限是您设备上的一项设置，您可以通过设备设置页面进行管理，拒绝提供该信息仅会使您无法使用上述功能，但不影响您正常使用熊猫智能柜的其他功能。',
                      style: TextStyle(fontSize: 16.0)),
                  Padding(padding: EdgeInsets.only(top: 10)),
                  Text(
                      '8.当您使用熊猫智能柜或APP语音输入时，我们会在获得您的自主选择同意后，获取您的麦克风权限，我们会收集您的语音内容、待转换的语音或文字信息、待翻译的文本信息，目的是为了向您提供该服务。麦克风权限是您设备上的一项设置，您可以通过设备设置页面进行管理,拒绝提供该信息仅会使您无法使用上述功能，但不影响您正常使用熊猫智能柜的其他功能。',
                      style: TextStyle(fontSize: 16.0)),
                  Padding(padding: EdgeInsets.only(top: 10)),
                  Text(
                      '9.当您通过熊猫智能柜或服务平台使用服务时，我们会根据相关法律法规的要求，对包裹、用户寄递操作过程、相关工作人员的活动过程以及周围环境进行有效监控，其中会包含您的形体、轮廓以及面容图像等可识别或不可识别为特定自然人的外貌特征。',
                      style: TextStyle(fontSize: 16.0)),
                ],
              ),
            ),
          ),
        )
      ]);
    }
  }

  @override
  void dispose() {
    super.dispose();
    recognizer.dispose();
    recognizer2.dispose();
    loginNameTextFieldNode.dispose();
    passwordTextFieldNode.dispose();
  }

  initParams() async {
    isOptimizeNetwork = await CheckUtils.isOptimizeNetwork();
    passwordController.addListener(() {
      setState(() {
        if (passwordController.text != '') {
          isPw = true;
        } else {
          isPw = false;
        }
      });
    });
    loginNameController.addListener(() {
      setState(() {
        if (loginNameController.text.length == 11) {
          if (RegExpUtil.isPhone(loginNameController.text)) {
            isUserName = true;
          } else {
            isUserName = false;
          }
        } else {
          isUserName = false;
        }
      });
    });
    String? userName_ = await LocalStorage.get(DefaultConfig().configs.USER_NAME_KEY);
    _userName = userName_ ?? '';
    String? lastPwd_ = await LocalStorage.get(DefaultConfig().configs.PW_KEY, isPrivate: true);
    lastPwd = lastPwd_ ?? '';
    String? _check = await LocalStorage.get(DefaultConfig().configs.USER_PERMIT);
    if (_check != null) {
      isCheck = _check == 'Y';
    }
    _password = lastPwd;
    passwordController.value = TextEditingValue(text: lastPwd);
    loginNameController.value = TextEditingValue(text: _userName);
    String sn = await scan.getIMEI();
    version = sn;
    setState(() {});
  }

  sysnc() async {}

  preLogin(store) {
    passwordTextFieldNode.unfocus();
    loginNameTextFieldNode.unfocus();
    SystemChannels.textInput.invokeMethod('TextInput.hide');
    if (isPw && isUserName) {
      if (isCheck) {
        login(store);
      } else {
        Fluttertoast.showToast(msg: '请阅读并同意下方协议和政策');
      }
    }
  }

  login(store) async {
    if (_password.length == 0) {
      Fluttertoast.showToast(msg: '请输入密码');
      return;
    }
    LoadingUtil(
      status: '正在登录...',
    ).show(context);
    Map<String, dynamic> info = {
      'loginName': _userName,
      'password': _password.trim(),
      'userType': '',
    };
    var res = await UserDao.login(info, store);
    LoadingUtil.dismiss(context);
    if (res != null && res.result) {
      await CheckUtils.addUserList(_userName, _password.trim());
      await LocalStorage.save(DefaultConfig().configs.LOGIN_USER, null);
      var _res = await UserDao.getUserInfo(store);
      if (_res != null && _res.result) {
        if (_res.data is UserEntity) {
          UserEntity user = _res.data;
          if (user.userType == 4 && (user.brandCode == '' || user.brandCode == null)) {
            /// 快递员端设置所属快递品牌
            selectBrand(user);
          } else {
            if (user.hasReal == 0) {
              NavigatorUtils.goRealName(context, user.userType!);
            } else if (user.hasReal == 1) {
              if (user.brands?.length == 0) {
                NavigatorUtils.goBrandBind(context);
              } else {
                NavigatorUtils.goHome(context);
              }
            }
          }
        }
      }
    }
  }

  /// 品牌选择
  selectBrand(user) {
    SoundUtils.audioPushFn(SoundUtils.CHOOSE_COMPANY);
    CommonUtils.showBottomBrandSelectModal(context, brandMap, (item) async {
      print(item);
      SoundUtils.audioPushFn(item);
      if (item != null) {
        Map<String, dynamic> info = {'brandCode': item};
        var res = await UserDao.setBindCompanyBrand(info);
        if (res != null && res.result) {
          if (res.data) {
            Fluttertoast.showToast(msg: '所属快递公司设置成功');
            Store<AppState> store = StoreProvider.of(context);
            UserDao.getUserInfo(store);
            if (user.hasReal == 0) {
              NavigatorUtils.goRealName(context, user.userType);
            } else if (user.hasReal == 1) {
              if (user.brands?.length == 0) {
                NavigatorUtils.goBrandBind(context);
              } else {
                NavigatorUtils.goHome(context);
              }
            }
          }
        }
      }
    }, showText: 'show');
  }

  buildLoginName() {
    return TextFormField(
      cursorColor: Theme.of(context).primaryColor,
      maxLength: 11,
      inputFormatters: [LengthLimitingTextInputFormatter(11)],
      validator: (value) => FormValidateUtil.phoneValidate2(value!, context),
      decoration: InputDecoration(
          labelText: '请输入您的手机号',
          errorMaxLines: 1,
          suffixIcon: CheckUtils.isNotNull(_userName)
              ? Container(
                  width: 20.0,
                  height: 20.0,
                  child: new IconButton(
                    alignment: Alignment.center,
                    padding: const EdgeInsets.all(0.0),
                    iconSize: 18.0,
                    icon: Icon(Icons.cancel),
                    onPressed: () {
                      setState(() {
                        _userName = '';
                        loginNameController.value = TextEditingValue(text: '');
                      });
                    },
                  ),
                )
              : Text("")),
      onChanged: (String value) {
        _userName = value;
        setState(() {});
      },
      keyboardType: TextInputType.phone,
      obscureText: false,
      controller: loginNameController,
      textInputAction: TextInputAction.next,
      focusNode: loginNameTextFieldNode,
      onEditingComplete: () {
        bool validator = _userName.length == 11 && RegExpUtil.isPhone(_userName);
        if (!validator) {
          Fluttertoast.showToast(msg: '请输入正确的手机号', gravity: ToastGravity.CENTER);
        } else {
          FocusScope.of(context).requestFocus(passwordTextFieldNode);
        }
      },
    );
  }

  /// 密码输入框
  MyTextFormField buildPassword(store) => MyTextFormField(
        labelText: "请输入密码",
        isPassword: true,
        controller: passwordController,
        focusNode: passwordTextFieldNode,
        keyboardType: TextInputType.text,
        onChanged: (value) {
          this.setState(() {
            _password = value;
          });
        },
        onEditingComplete: () {
          if (isPw && isUserName) {
            preLogin(store);
          }
        },
      );

  @override
  Widget build(BuildContext context) {
    bool smallScreen = MediaQuery.of(context).size.width * MediaQuery.of(context).devicePixelRatio < 480;
    var height = MediaQuery.of(context).size.height * .3;
    return StoreBuilder<AppState>(builder: (context, store) {
      return Scaffold(
        backgroundColor: Colors.white,
        body: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            FocusScope.of(context).requestFocus(FocusNode());
          },
          child: SingleChildScrollView(
              child: Container(
            child: Stack(
              children: [
                Container(
                  height: MediaQuery.of(context).size.height,
                  child: Column(
                    children: <Widget>[
                      Offstage(
                        offstage: smallScreen,
                        child: LocalImageUtil.getImageAsset('logo',
                            isChannel: true,
                            width: MediaQuery.of(context).size.width * 0.7,
                            height: height,
                            fit: BoxFit.fitWidth),
                      ),
                      Container(
                        height: smallScreen
                            ? MediaQuery.of(context).size.height * .9
                            : MediaQuery.of(context).size.height * .7,
                        padding: EdgeInsets.only(bottom: smallScreen ? 0 : 70, top: smallScreen ? 80 : 0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            Expanded(
                              child: Padding(
                                padding: EdgeInsets.only(left: 30.0, top: 10.0, right: 30.0, bottom: 15.0),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.max,
                                  children: <Widget>[
                                    buildLoginName(),
                                    Padding(padding: EdgeInsets.only(top: 0)),
                                    buildPassword(store),
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Container(
                                          margin: EdgeInsets.only(top: 30, left: 5),
                                          child: GestureDetector(
                                            child: Text(
                                              '忘记密码',
                                              style: TextStyle(fontSize: 16),
                                            ),
                                            onTap: () {
                                              NavigatorUtils.goForGetPw(context, loginNameController.text);
                                            },
                                          ),
                                        ),
                                        Container(
                                          margin: EdgeInsets.only(top: 30, right: 5),
                                          child: GestureDetector(
                                            child: Text(
                                              '注册',
                                              style: TextStyle(fontSize: 16, color: Theme.of(context).primaryColor),
                                            ),
                                            onTap: () {
                                              NavigatorUtils.goChoicePolePage(context, loginNameController.text);
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            Container(
                              width: double.infinity,
                              margin: EdgeInsets.only(left: 20, top: smallScreen ? 20 : 40, right: 20),
                              height: 47,
                              child: MaterialButton(
                                color: Theme.of(context).primaryColor,
                                disabledColor: Colors.grey.shade500,
                                textColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(25.0),
                                ),
                                onPressed: isPw && isUserName
                                    ? () => throttleUtil.throttle(() {
                                          preLogin(store);
                                        })
                                    : null,
                                child: Text(CommonUtils.getLocale(context).loginText, style: TextStyle(fontSize: 18)),
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(left: 30, top: 14),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: <Widget>[
                                  InkWell(
                                    onTap: () {
                                      setState(() {
                                        isCheck = !isCheck;
                                      });
                                    },
                                    child: Container(
                                      height: 30.0,
                                      child: Container(
                                        width: 10.0,
                                        height: 10.0,
                                        margin: EdgeInsets.fromLTRB(0, 0.0, 10.0, 0.0),
                                        child: Checkbox(
                                          value: isCheck,
                                          activeColor: Theme.of(context).primaryColor,
                                          onChanged: (bool? value) {
                                            setState(() {
                                              isCheck = value!;
                                            });
                                          },
                                        ),
                                      ),
                                    ),
                                  ),
                                  Container(
                                    width: MediaQuery.of(context).size.width - 70,
                                    child: RichText(
                                        maxLines: 2,
                                        text: TextSpan(children: <TextSpan>[
                                          TextSpan(
                                              text: '我已阅读并同意',
                                              style: TextStyle(color: Colors.grey.shade600, fontSize: 13.0)),
                                          TextSpan(
                                              text: '《${DefaultConfig().configs.APP_NAME}协议》',
                                              recognizer: recognizer,
                                              style: TextStyle(
                                                  color: Theme.of(context).primaryColor,
                                                  fontSize: 13.0,
                                                  fontWeight: FontWeight.w600)),
                                          TextSpan(
                                              text: '和', style: TextStyle(color: Colors.grey.shade600, fontSize: 13.0)),
                                          TextSpan(
                                              text: '《隐私政策》',
                                              recognizer: recognizer2,
                                              style: TextStyle(
                                                  color: Theme.of(context).primaryColor,
                                                  fontSize: 13.0,
                                                  fontWeight: FontWeight.w600)),
                                        ])),
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(left: 10, top: 14),
                              child: InkWell(
                                onTap: () async {
                                  setState(() {
                                    isOptimizeNetwork = !isOptimizeNetwork;
                                  });
                                  await CheckUtils.saveIsOptimizeNetwork(isOptimizeNetwork, isForce: true);
                                },
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    Container(
                                      width: MediaQuery.of(context).size.width - 70,
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        children: <Widget>[
                                          Container(
                                            height: 30.0,
                                            child: Container(
                                              width: 10.0,
                                              height: 10.0,
                                              margin: EdgeInsets.fromLTRB(0, 0.0, 10.0, 0.0),
                                              child: Checkbox(
                                                value: isOptimizeNetwork,
                                                activeColor: Theme.of(context).primaryColor,
                                                onChanged: (bool? value) {
                                                  // setState(() {
                                                  //   isOptimizeNetwork = value!;
                                                  // });
                                                },
                                              ),
                                            ),
                                          ),
                                          Text(
                                            '网络优化',
                                            style: TextStyle(fontSize: 16),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            Padding(padding: EdgeInsets.only(top: 20.0)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: Offstage(
                    offstage: version == '1',
                    child: InkWell(
                      onTap: () async {
                        settingCount--;
                        if (settingCount == 0) {
                          if (kDebugMode) {
                            NavigatorUtils.goRequestSetting(context);
                          } else {
                            CommonUtils.passwordDialog(context, (password) {
                              if (password == 'zlxx365') {
                                Navigator.pop(context);
                                NavigatorUtils.goRequestSetting(context);
                              } else {
                                Fluttertoast.showToast(msg: '密码错误');
                              }
                            });
                          }
                        }
                        if (settingTimer != null) {
                          settingTimer?.cancel();
                        }
                        settingTimer = new Timer(Duration(milliseconds: 1000), () {
                          settingCount = 3;
                        });
                      },
                      child: CopyrightWidget(),
                    ),
                  ),
                )
              ],
            ),
          )),
        ),
      );
    });
  }
}
