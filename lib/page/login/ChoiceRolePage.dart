import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:flutter/material.dart';



class ChoiceRolePage extends StatefulWidget {
  final String mobile;

  ChoiceRolePage(this.mobile, {Key? key}) : super(key: key);

  @override
  _ChoiceRolePageState createState() => _ChoiceRolePageState();
}

class _ChoiceRolePageState extends State<ChoiceRolePage> {


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppCustomerBar(
        color: Colors.white,
        iconColor: Colors.black,
        title: AppbarTitle(
          title: '',
          isCenter: true,
        ),
        actions: <Widget>[
          Container(
            width: 50,
          )
        ],
      ),
      body: SingleChildScrollView(
        child: Container(
          child: Stack(
            children: [
              Container(
                height: MediaQuery.of(context).size.height - 80,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: EdgeInsets.fromLTRB(20, 5, 0, 0),
                      child: Text(
                        '请选择您的使用角色',
                        style: TextStyle(color: Colors.black, fontSize: 20),
                      ),
                    ),
                    SizedBox(
                      height: 91,
                    ),
                    Container(
                        child: Row(mainAxisAlignment: MainAxisAlignment.spaceEvenly, children: [
                      Expanded(
                        child: Container(
                            margin: EdgeInsets.only(left: 6, right: 6),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                InkWell(
                                  child: LocalImageUtil.getImageAsset(
                                    'c_courier',
                                  ),
                                  onTap: () {
                                    NavigatorUtils.goRegister(context,widget.mobile, 4);
                                  },
                                ),
                                Column(mainAxisAlignment: MainAxisAlignment.spaceEvenly, children: [
                                  Text('我是快递员'),
                                  Container(
                                    height: 10,
                                    width: 1,
                                  ),
                                  Text(
                                    '如果您是快递员，常用于柜机投递，寄件等，请选择此角色',
                                    style: TextStyle(
                                      fontSize: 11,
                                      color: DefaultConfig().configs.GREY_LIGHT1_COLOR,
                                      height: 1.6,
                                    ),
                                    textAlign: TextAlign.center,
                                  )
                                ]),
                              ],
                            )),
                      ),
                      Expanded(
                        child: Container(
                          margin: EdgeInsets.only(left: 6, right: 6),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              InkWell(
                                child: LocalImageUtil.getImageAsset(
                                  'c_station',
                                ),
                                onTap: () {
                                  NavigatorUtils.goRegister(context,widget.mobile, 6);
                                },
                              ),
                              Column(
                                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                children: [
                                  Text('我是驿站'),
                                  Container(
                                    height: 10,
                                    width: 1,
                                  ),
                                  Text(
                                    '如果您是驿站或室内柜，常用于投递和管理柜机，请选择此角色',
                                    style: TextStyle(
                                      fontSize: 11,
                                      color: DefaultConfig().configs.GREY_LIGHT1_COLOR,
                                      height: 1.6,
                                    ),
                                    textAlign: TextAlign.center,
                                  )
                                ],
                              )
                            ],
                          ),
                        ),
                      )
                    ])),
                  ],
                ),
              ),
//               Positioned(
//                   left: 0,
//                   right: 0,
//                   bottom: 15,
//                   child: Container(
//                     padding: EdgeInsets.fromLTRB(40, 10, 40, 10),
// //                          height: 40.0,
//                     alignment: Alignment.center,
//                     child: Row(
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       children: <Widget>[
//                         Expanded(
//                             child: Text(
//                           '不同角色的APP功能不同，数据也不相通，请谨慎选择可在我的设置-选择角色处进行修改',
//                           style: TextStyle(
//                             color: Color(0xFF999999),
//                             fontSize: 12.0,
//                             height: 1.6,
//                           ),
//                           textAlign: TextAlign.center,
//                         ))
//                       ],
//                     ),
//                   ))
            ],
          ),
        ),
      ),
    );
  }
}
