import 'dart:ui';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/redux/UserRedux.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/FormValidateUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/MyTextFormField.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CopyrightWdiget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';

class RealNamePage extends StatefulWidget {
  final int userType;

  RealNamePage(this.userType, {Key? key}) : super(key: key);

  @override
  _RealNamePageState createState() => _RealNamePageState();
}

class _RealNamePageState extends State<RealNamePage> {
  GlobalKey<FormState> _realNameKey = new GlobalKey<FormState>();

  //姓名
  TextEditingController realNameController = TextEditingController();

  //身份证号码
  TextEditingController idNumberController = TextEditingController();
  bool isName = false;
  bool isIdNumber = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  /// 姓名输入框
  buildRealName() {
    return MyTextFormField(
        labelText: "请输入您的姓名",
        isPassword: false,
        keyboardType: TextInputType.text,
        controller: realNameController,
        onChanged: (value) {},
        validator: (value) {
          return FormValidateUtil.realNameValidate(value);
        });
  }

  /// 身份证号码
  buildIdNumber() {
    return MyTextFormField(
        labelText: "请输入您的身份证号",
        isPassword: false,
        controller: idNumberController,
        keyboardType: TextInputType.name,
        onChanged: (value) {},
        validator: (value) {
          return FormValidateUtil.idNumberValidate(value);
        });
  }

  realNameUpload() async {
    Map<String, dynamic> info = {'realName': realNameController.text, 'idNumber': idNumberController.text};
    bool? validator = _realNameKey.currentState?.validate();
    if (validator != null && validator) {
      LoadingUtil(
        status: '实名认证中...',
      ).show(context);
      var res = await UserDao.realNameAuth(info);
      LoadingUtil.dismiss(context);
      if (res != null && res.result) {
        if (res.data) {
          Store<AppState> store = StoreProvider.of(context);
          UserEntity? userEntity = store.state.userInfo;
          userEntity!.hasReal = 1;
          LocalStorage.save(DefaultConfig().configs.USER_INFO, userEntity);
          store.dispatch(new UpdateUserAction(userEntity));
          if (widget.userType == 6) {
            NavigatorUtils.goHome(context);
          } else if (userEntity.brands!.length == 0) {
            NavigatorUtils.goBrandBind(context);
          } else {
            NavigatorUtils.goHome(context);
          }
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    var mediaHeight = MediaQuery.of(context).size.height;
    var appBarHeight = MediaQueryData.fromWindow(window).padding.top;
    return Scaffold(
      appBar: new AppCustomerBar(
        color: Colors.white,
        iconColor: Colors.black,
        title: new AppbarTitle(
          title: '',
          isCenter: true,
        ),
        actions: <Widget>[
          Container(
            width: 50,
          )
        ],
      ),
      body: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());
        },
        child: SingleChildScrollView(
          child: Container(
              child: Stack(
            children: [
              Container(
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height - 80,
                child: Form(
                  key: _realNameKey,
                  child: Column(
                    children: <Widget>[
                      Container(
                        height: 60,
                        width: MediaQuery.of(context).size.width,
                        padding: EdgeInsets.fromLTRB(30, 10, 10, 10),
                        child: Text(
                          '实名认证',
                          style: TextStyle(fontSize: 22),
                        ),
                      ),
                      Container(
                        height: mediaHeight - 100 - 100 - 35 - appBarHeight,
                        margin: EdgeInsets.fromLTRB(0, 50, 0, 40),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            Padding(
                              padding: EdgeInsets.only(left: 30.0, top: 10.0, right: 40.0, bottom: 20.0),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.max,
                                children: <Widget>[],
                              ),
                            ),
                            Container(
                              padding: EdgeInsets.fromLTRB(25, 20, 25, 0),
                              child: Column(
                                children: <Widget>[
                                  buildRealName(),
                                  SizedBox(height: 8),
                                  buildIdNumber(),
                                  Container(
                                    width: double.infinity,
                                    margin: EdgeInsets.only(top: 40),
                                    height: 47,
                                    child: MaterialButton(
                                      color: CheckUtils.isNotNull(realNameController.text) &&
                                              CheckUtils.isNotNull(idNumberController.text)
                                          ? Theme.of(context).primaryColor
                                          : Colors.grey.shade500,
                                      textColor: Colors.white,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(25.0),
                                      ),
                                      onPressed: () {
                                        realNameUpload();
                                      },
                                      child: Text("确定"),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Padding(padding: EdgeInsets.only(top: 20.0)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Positioned(
                left: 0,
                bottom: 0,
                child: CopyrightWidget(),
              )
            ],
          )),
        ),
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }
}
