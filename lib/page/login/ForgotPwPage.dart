import 'dart:async';

import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/FormValidateUtil.dart';
import 'package:cabinet_flutter_app/common/utils/RegExpUtil.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/myTextFormField.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';

class ForgotPwPage extends StatefulWidget {
  final String mobile;

  ForgotPwPage(this.mobile, {Key? key}) : super(key: key);

  @override
  _ForgotPwPage createState() => _ForgotPwPage();
}

class _ForgotPwPage extends State<ForgotPwPage> {
  TextEditingController phoneController = TextEditingController();

  //验证码的控制器
  TextEditingController _codeController = TextEditingController();

  //密码的控制器
  TextEditingController _pwdController = TextEditingController();

  //确认密码的控制器
  TextEditingController _rePwdController = TextEditingController();

  bool isCode = false;
  bool isPw = false;
  bool isRePw = false;
  bool isPhone = false;

  Timer? _timer;

  var phone;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    phone = widget.mobile;

    phoneController.addListener(() {
      if (phoneController.text != '') {
        if (CheckUtils.isMobile(phoneController.text)) {
          isPhone = true;
        } else {
          isPhone = false;
        }
      } else {
        isPhone = false;
      }
      setState(() {});
    });
    _codeController.addListener(() {
      if (_codeController.text != '') {
        isCode = true;
      } else {
        isCode = false;
      }
      setState(() {});
    });
    _pwdController.addListener(() {
      if (_pwdController.text != '') {
        isPw = true;
      } else {
        isPw = false;
      }
      setState(() {});
    });
    _rePwdController.addListener(() {
      if (_rePwdController.text != '') {
        isRePw = true;
      } else {
        isRePw = false;
      }
      setState(() {});
    });

    phoneController.value = TextEditingValue(text: widget.mobile ?? '');
    setState(() {});
  }

  resetPwd() async {
    if (!isPhone) {
      Fluttertoast.showToast(msg: '请输入正确的手机号');
      return false;
    }
    if (!isCode) {
      Fluttertoast.showToast(msg: '请输入验证码');
      return false;
    }
    if (!isPw) {
      Fluttertoast.showToast(msg: '请输入密码');
      return false;
    }
    if (!isRePw) {
      Fluttertoast.showToast(msg: '请再次输入密码');
      return false;
    }
    if (_pwdController.text != _rePwdController.text) {
      Fluttertoast.showToast(msg: '密码不一致，请检查');
      return false;
    }
    Map<String, dynamic> info = {
      'loginName': phoneController.text,
      'password': _pwdController.text,
      'smsCode': _codeController.text
    };
    var res = await UserDao.resetPwd(info);
    if (res != null && res.result) {
      if (res.data) {
        Fluttertoast.showToast(msg: '重置密码成功,请重新登录');
        Navigator.pop(context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: new AppCustomerBar(
        color: Colors.white,
        iconColor: Colors.black,
        title: new AppbarTitle(
          title: '',
          isCenter: true,
        ),
        actions: <Widget>[
          Container(
            width: 50,
          )
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.fromLTRB(30, 20, 0, 0),
              child: Text(
                '重置您的密码',
                style: TextStyle(color: Colors.black, fontSize: 22),
              ),
            ),
            Container(
              padding: EdgeInsets.fromLTRB(30, 20, 30, 0),
              child: Form(
                child: Column(
                  children: <Widget>[
                    SizedBox(height: 12),
                    buildLoginName(),
                    SizedBox(height: 15),
                    buildCode(),
                    SizedBox(height: 15),
                    buildPassword(),
                    SizedBox(height: 15),
                    buildRePassword(),
                    Container(
                      width: double.infinity,
                      margin: EdgeInsets.only(top: 40),
                      height: 47,
                      child: MaterialButton(
                        color:
                            isCode && isPw && isRePw && isPhone ? Theme.of(context).primaryColor : Colors.grey.shade500,
                        textColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20.0),
                        ),
                        onPressed: () {
                          resetPwd();
                        },
                        child: Text("确定", style: TextStyle(fontSize: 18)),
                      ),
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  /// 手机号输入框
  MyTextFormField buildLoginName() {
    return MyTextFormField(
      labelText: "请输入手机号",
      isPassword: false,
      inputFormatters: [LengthLimitingTextInputFormatter(11)],
      keyboardType: TextInputType.number,
      controller: phoneController,
      onChanged: (value) {
        if (value != null) {
          phone = value;
        }
      },
      validator: (value) => FormValidateUtil.phoneValidate2(value!, context),
    );
  }

  /// 验证码输入框
  Widget buildCode() {
    return MyTextFormField(
      labelText: "请输入验证码",
      maxLength: 8,
      suffixIcon: TextButton(
        child: Text(countTime <= 0 ? '发送验证码' : "$countTime秒后重试"),
        style: ButtonStyle(foregroundColor: MaterialStateProperty.all<Color>(Theme.of(context).primaryColor)),
        onPressed: countTime <= 0 ? sendCode : null,
      ),
      onChanged: (value) {},
      controller: _codeController,
//      validator: (value) {
//        if(value != code) {
//          return "验证码不正确";
//        }
//        return null;
//      },
    );
  }

  /// 发送验证码
  Future<void> sendCode() async {
    bool isPhone = RegExpUtil.isPhone(phone);
    if (!isPhone) {
      Fluttertoast.showToast(msg: '手机号格式错误');
      return;
    }
    Map<String, dynamic> data = {"loginName": phone, 'smsTplCode': 'RESET_PASSWORD_SMS'};
    var res = await UserDao.sendSms(data);
    if (res != null && res.result) {
      if (res.data) {
        Fluttertoast.showToast(msg: '验证码发送成功,请查收');
        startTimer();
      } else {
        Fluttertoast.showToast(msg: '验证码发送失败请重试');
      }
    }
  }

  var countTime = 0;
  Timer? timer;
  late String code;

  /// 计时器
  void startTimer() {
    countTime = 60;
    if (timer != null) {
      return;
    }
    timer = Timer.periodic(Duration(seconds: 1), (time) {
      setState(() {
        if (countTime > 0) {
          countTime--;
        } else {
          timer?.cancel();
          timer = null;
        }
      });
    });
  }

  /// 密码输入框
  MyTextFormField buildPassword() => MyTextFormField(
        labelText: "请输入密码",
        isPassword: true,
        keyboardType: TextInputType.text,
        controller: _pwdController,
        onChanged: (value) {},
        // hintText: "请输入密码",
        // leftIcon: Icon(Icons.lock),
      );

  /// 确认密码输入框
  MyTextFormField buildRePassword() {
    return MyTextFormField(
      labelText: "请再次输入密码",
      isPassword: true,
      keyboardType: TextInputType.text,
      controller: _rePwdController,
      onChanged: (value) {},
      // hintText: "请再次输入密码",
      // leftIcon: Icon(Icons.lock),
      validator: (value) {
        if (value == null) {
          value = "";
        }
        if (value.trim().length < 6) {
          return "需要大于等于6个字符";
        } else if (_rePwdController.text != _pwdController.text) {
          return "两次输入不正确";
        }
        return '';
      },
    );
  }

  @override
  void dispose() {
    super.dispose();
    timer?.cancel();
    _timer?.cancel();
    timer = null;
  }
}
