import 'dart:io';

import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';

class AgreementPage extends StatefulWidget {
  String url;
  String title;
  final bool isLocalUrl;

  AgreementPage({required this.url, this.isLocalUrl = false, required this.title});

  @override
  _AgreementPageState createState() => _AgreementPageState();
}

class _AgreementPageState extends State<AgreementPage> {
  @override
  void initState() {
    super.initState();
    launchUrl(Uri.parse(widget.url));
    Navigator.pop(context);
    // if (Platform.isAndroid) {
    //   WebView.platform = AndroidWebView();
    // } else {
    //   launchUrl(Uri.parse(widget.url));
    //   Navigator.pop(context);
    // }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          backgroundColor: Theme.of(context).primaryColor,
          title: Text(widget.title),
          centerTitle: true,
        ),
        body: Builder(
          builder: (BuildContext context) {
            // return WebView(
            //   initialUrl: widget.url,
            //   javascriptMode: JavascriptMode.unrestricted,
            //   // onWebViewCreated: (WebViewController webViewController) {
            //   //   _webViewController = webViewController;
            //   //   _loadHtmlFromAssets();
            //   // },
            // );
            return Container();
          },
        ));
  }
}
