import 'dart:convert';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/entitys/notify_entity.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart' as md;

class NoticeDetailPage extends StatefulWidget {
  final NotifyEntity notice;

  NoticeDetailPage(this.notice, {Key? key}) : super(key: key);

  @override
  _NoticeDetailPageState createState() => _NoticeDetailPageState();
}

class _NoticeDetailPageState extends State<NoticeDetailPage> {
  var htmlData = '';

  @override
  void initState() {
    super.initState();
    initData();
  }

  initData() {
    NotifyEntity item = widget.notice;
    String content = '';
    if (item.content != null) {
      var res = base64.decode(item.content!);
      var bytes2 = Utf8Decoder().convert(res);
      content = Uri.decodeComponent(bytes2);
    }
    htmlData = content;
    setState(() {});
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DefaultConfig().configs.BG_COLOR,
      appBar: new AppCustomerBar(
        title: AppbarTitle(
          title: '${widget.notice.noticeTitle ?? ''}',
          subtitle: '${widget.notice.noticeSubtitle ?? ''}',
          isCenter: true,
        ),
        actions: [
          Container(
            width: 60,
          )
        ],
      ),
      body: Container(
        alignment: AlignmentDirectional.centerStart,
        color: Colors.white,
        child: Column(
          children: <Widget>[
            Expanded(
                child: md.MarkdownBody(
              data: htmlData,
            ))
          ],
        ),
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }
}
