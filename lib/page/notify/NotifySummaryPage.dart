
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/notify_entity.dart';
import 'package:cabinet_flutter_app/common/page_life_cycle.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/page/notify/NoticeListPage.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/NoResult.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class NotifySummaryPage extends StatefulWidget {
  @override
  _NotifySummaryPageState createState() => _NotifySummaryPageState();
}

class _NotifySummaryPageState extends State<NotifySummaryPage>
    with AutomaticKeepAliveClientMixin, PageLifeCycle<NotifySummaryPage> {
  final GlobalKey<ScaffoldState> notifyPageKey = new GlobalKey<ScaffoldState>();

  List<dynamic> data = [];
  List<NotifyEntity?> list = [];

  @override
  void initState() {
    super.initState();
    this.init();
  }

  init() async {
    this.handleRefresh();
  }

  getData() async {
    list.clear();
    Map<String, dynamic> info = {
      'mediaNoticeTerminal': '1',
    };
    var res = await UserDao.myNotifyList(info);
    if (res != null && res.result) {
      list = jsonConvert.convertList<NotifyEntity>(res.data)!;
    }
    if (mounted) {
      setState(() {});
    }
  }

  Future<Null> handleRefresh() async {
    new Future.delayed(const Duration(microseconds: 200), () {
      this.getData();
    });
  }

  buildNoticeList() {
    List<Widget> widgets = <Widget>[];
    Map<int, dynamic> iconMap = {1: 'xx', 2: 'zl', 3: 'hd', 4: 'yy'};
    list.forEach((item) {
      if (item != null) {
        // String? time = item.onlineTime ?? '';
        // if (time != '') {
        //   item.onlineTime = DateFormat('HH:mm').format(DateTime.parse(time));
        // }
        widgets.add(InkWell(
          onTap: () {
            Navigator.push(
                    context,
                    new CupertinoPageRoute(
                        builder: (context) => new NoticeListPage(item.noticeType),
                        settings: RouteSettings(name: 'noticeListPage')))
                .then((value) => {getData()});
          },
          child: Container(
            height: 83,
            width: MediaQuery.of(context).size.width - 20,
            padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
            margin: EdgeInsets.only(bottom: 10),
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(5.0), color: Colors.white),
            child: Row(
              children: [
                Container(
                  child: LocalImageUtil.getImageAsset(iconMap[item.noticeType], width: 48),
                  margin: EdgeInsets.only(right: 10),
                ),
                Expanded(
                    child: Column(
                  children: [
                    Padding(padding: EdgeInsets.only(top: 12)),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(DefaultConfig().configs.NOTICE_TYPE_MAP[item.noticeType] ?? '',
                            style: TextStyle(fontSize: 16, color: Colors.black)),
                        Text(item.onlineTime ?? '',
                            style: TextStyle(fontSize: AppConstant.smallTextSize, color: Color(0xFF999999)))
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(item.noticeTitle ?? '',
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(fontSize: AppConstant.smallTextSize, color: Color(0xFF999999))),
                        ),
                        Offstage(
                          offstage: item.unReadCount == 0,
                          child: Container(
                            padding: EdgeInsets.fromLTRB(5, 1, 5, 2),
                            decoration:
                                BoxDecoration(borderRadius: BorderRadius.circular(10.0), color: Theme.of(context).primaryColor),
                            child: Text('${item.unReadCount > 99 ? '99+' : item.unReadCount}',
                                style: TextStyle(color: Colors.white, fontSize: 12)),
                          ),
                        )
                      ],
                    )
                  ],
                ))
              ],
            ),
          ),
        ));
      }
    });
    return widgets;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: notifyPageKey,
      backgroundColor: DefaultConfig().configs.BG_COLOR,
      appBar: AppCustomerBar(
        showLeading: false,
        title: AppbarTitle(
          title: '我的消息',
          isCenter: true,
        ),
        actions: <Widget>[Container(width: 60)],
      ),
      body: RefreshIndicator(
        onRefresh: handleRefresh,
        child: Column(
          children: [
            Expanded(
                child: SingleChildScrollView(
              physics: AlwaysScrollableScrollPhysics(),
              child: list.length > 0
                  ? Container(
                      width: MediaQuery.of(context).size.width,
                      padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
                      child: Column(
                        children: buildNoticeList(),
                      ),
                    )
                  : Container(
                      height: 300,
                      child: NoResult(
                          size: 64,
                          type: 'box',
                          subWidget: Container(
                              padding: EdgeInsets.only(top: 10), child: Text('', style: AppConstant.smallSubText))),
                    ),
            ))
          ],
        ),
      ),
    );
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;
}
