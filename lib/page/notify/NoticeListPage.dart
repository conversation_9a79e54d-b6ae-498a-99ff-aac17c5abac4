
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/notify_entity.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppListState.dart';
import 'package:cabinet_flutter_app/widget/AppPullLoadWidget.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class NoticeListPage extends StatefulWidget {
  final int? noticeType;

  NoticeListPage(this.noticeType, {Key? key}) : super(key: key);

  @override
  _NoticeListPageState createState() => _NoticeListPageState();
}

class _NoticeListPageState extends State<NoticeListPage>
    with
        AutomaticKeepAliveClientMixin<NoticeListPage>,
        AppListState<NoticeListPage>,
        WidgetsBindingObserver,
        SingleTickerProviderStateMixin {
  List<dynamic> list = [];
  int total = 0;

  @override
  void initState() {
    handleRefresh();
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    EasyLoading.dismiss();
  }

  _getList({isRefresh = false}) async {
    // EasyLoading.show(status: '数据加载中...');
    Map<String, dynamic> info = {
      'current': page.toString(),
      'size': 20,
      'mediaNoticeTerminal': 1,
      'noticeType': widget.noticeType,
    };
    DataResult res = await UserDao.myNotifyDetailList(info);
    EasyLoading.dismiss();
    total = res.total!;
    return res;
  }

  readOneNotice(String id) async {
    Map<String, dynamic> info = {
      'mediaNoticeId': id,
    };
    var res = await UserDao.readOneNotice(info);
    if (res != null && res.result) {
      handleRefresh();
    }
  }

  readAllNotice() async {
    Map<String, dynamic> info = {
      'noticeType': widget.noticeType,
    };
    var res = await UserDao.readAllNotice(info);
    if (res != null && res.result) {
      handleRefresh();
    }
  }

  Future<Null> onLoadMore() async {
    if (isLoading) {
      return null;
    }
    isLoading = true;
    page++;
    var res = await requestLoadMore();
    if (res != null && res.result) {
      setState(() {
        pullLoadWidgetControl.dataList.addAll(res.data);
      });
    }
    resolveDataResult(res);
    isLoading = false;
    return null;
  }

  @protected
  Future<Null> handleRefresh() async {
    if (isLoading) {
      return null;
    }
    refreshIndicatorKey.currentState?.show();
    isLoading = true;
    page = 1;
    var res = await requestRefresh();
    resolveRefreshResult(res);
    resolveDataResult(res);
    if (res.next != null) {
      var resNext = await res.next;
      resolveRefreshResult(resNext);
      resolveDataResult(resNext);
    }
    isLoading = false;
    return null;
  }

  // Future<Null> handleRefresh() async {
  //   await Future.delayed(Duration(microseconds: 200)).then((value) async {
  //     await _getList();
  //   });
  // }

  Widget _listWidget(int index) {
    NotifyEntity child = NotifyEntity.fromJson(dataList[index]);
    return InkWell(
        onTap: () {
          /// todo;
          if (child.readStatus != 1) {
            readOneNotice(child.id!);
            // NavigatorUtils.goNoticeDetailPage(context, child);
            setState(() {
              child.readStatus = 1;
            });
          }
        },
        child: Container(
          margin: EdgeInsets.only(top: index == 0 ? 10 : 1),
          padding: EdgeInsets.fromLTRB(12, 8, 12, 8),
          color: Colors.white,
          height: 78,
          child: Column(
            children: [
              Padding(padding: EdgeInsets.only(top: 4)),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(child.noticeTitle!,
                        overflow: TextOverflow.ellipsis, style: TextStyle(fontSize: 16, color: Colors.black)),
                  ),
                  Text(child.onlineTime!, style: TextStyle(fontSize: AppConstant.smallTextSize, color: Colors.black))
                ],
              ),
              Padding(padding: EdgeInsets.only(top: 8)),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(child.noticeSubtitle ?? '',
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(fontSize: AppConstant.smallTextSize, color: Color(0xFF999999))),
                  ),
                  Offstage(
                    offstage: child.readStatus == 0 ? false : true,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), color: Theme.of(context).primaryColor),
                      child: Text(''),
                    ),
                  )
                ],
              )
            ],
          ),
        ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: DefaultConfig().configs.BG_COLOR,
        appBar: new AppCustomerBar(
          title: AppbarTitle(
            title: DefaultConfig().configs.NOTICE_TYPE_MAP[widget.noticeType] ?? '',
            isCenter: true,
          ),
          actions: [
            InkWell(
              onTap: () {
                readAllNotice();
              },
              child: new Container(
                  alignment: Alignment.centerLeft,
                  child: new Padding(
                    padding: EdgeInsets.only(right: 10.0),
                    child: Text(
                      '全部已读',
                      style: TextStyle(color: Colors.black),
                    ),
                  )),
            ),
          ],
        ),
        body: Container(
          child: Column(
            children: [
              Expanded(
                child: RefreshIndicator(
                  onRefresh: onFresh,
                  child: AppPullLoadWidget(
                    pullLoadWidgetControl,
                    (BuildContext context, int index) => _listWidget(index),
                    handleRefresh,
                    onLoadMore,
                    refreshKey: refreshIndicatorKey,
                  ),
                ),
              ),
            ],
          ),
        ) // This trailing comma makes auto-formatting nicer for build methods.
        );
  }

  // TODO: implement wantKeepAlive
  @override
  bool get wantKeepAlive => true;

  // TODO: implement isRefreshFirst
  @override
  bool get isRefreshFirst => true;

  @override
  requestLoadMore() async {
    return await _getList();
  }

  //
  Future<Null> onFresh() async {
    _getList(isRefresh: true);
  }

  @override
  requestRefresh() async {
    return await _getList();
  }
}
