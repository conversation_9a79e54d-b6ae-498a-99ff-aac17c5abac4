import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/page_life_cycle.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/utils/SoundUtils.dart';
import 'package:cabinet_flutter_app/page/data/PackageSearchPage.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:scan/device_info.dart';
import 'package:scan/scan.dart';

class CabinetBindPage extends StatefulWidget {
  final ScanAction scanAction;
 
  CabinetBindPage({required this.scanAction, Key? key}) : super(key: key);

  @override
  _CabinetBindPageState createState() => _CabinetBindPageState();
}

class _CabinetBindPageState extends State<CabinetBindPage>
    with TickerProviderStateMixin, WidgetsBindingObserver, PageLifeCycle<CabinetBindPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = new GlobalKey<ScaffoldState>();
  late StreamSubscription<dynamic>? _scanSubscription;
  Scan scanPlugin = Scan();
  ScanView? scanView;
  bool isInProcess = false;
  ScanViewController? scanViewController;
  int lastScanTime = 0;
  String lastScanUrl = '';
  bool checkFlash = false;
  bool isPda = false;

  @override
  void initState() {
    super.initState();
    // 初始化生命周期监听 用于解决扫码库在生命周期变动时候被杀死
   
    WidgetsBinding.instance.addObserver(this);
    checkIsPda();
    checkPermission();
    initScan();
  }

  void checkPermission() async {
    PermissionStatus cameraStatus = await Permission.camera.status;
    List<Permission> permissions = [Permission.camera];
    DeviceInfo deviceInfo = await scanPlugin.getDeviceInfo();
    var msg = '请开启相机权限,用于扫描条码';
    if (int.tryParse(deviceInfo.osVersion!, radix: 10)! < 29) {
      permissions = [Permission.camera, Permission.storage];
      msg = '请开启相机权限用于扫描条码，存储权限用于保存扫描图片';
    }
    if (cameraStatus.isDenied) {
      CheckUtils.showPermission(permissions, this.context, msg);
    }
  }

  @override
  void dispose() {
    super.dispose();
    _scanSubscription?.cancel();
    this.stopSpot();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    ///通过state判断App前后台切换
    switch (state) {
      case AppLifecycleState.hidden:
      case AppLifecycleState.inactive: // 处于这种状态的应用程序应该假设它们可能在任何时候暂停。
        break;
      case AppLifecycleState.resumed: // 应用程序可见，前台
        this.initScan();
        break;
      case AppLifecycleState.paused: // 应用程序不可见，后台
        _scanSubscription?.cancel();
        this.stopSpot();
        break;
      case AppLifecycleState.detached: // 申请将暂时暂停
        break;
    }
  }

  // 路由显示触发
  @override
  void onShow() {
    this.initScan();
    this.startSpot();
  }

  @override
  void onHide() {
    _scanSubscription?.cancel();
    this.stopSpot();
  }

  stopSpot() {
    isInProcess = false;
    scanViewController?.stopSpot();
  }

  checkIsPda() async {
    isPda = await CheckUtils.isPda();
  }

  ///开始扫描识别
  startSpot() {
    Timer(const Duration(seconds: 1), () async {
      scanViewController?.hidePhoto();
      scanViewController?.startSpot();
      scanViewController?.setQrScan();
      isInProcess = false;
      if (mounted) {
        setState(() {});
      }
    });
  }

  void initScan() {
    _scanSubscription = scanPlugin.onScanChanged.listen((result) {
      int nowtTime = new DateTime.now().millisecondsSinceEpoch;
      if (((nowtTime - lastScanTime) / 1000) < 3 && lastScanUrl != '' && lastScanUrl == result['text']) {
        startSpot();
      } else {
        lastScanTime = nowtTime;
        lastScanUrl = result['text'];
        if (result['text'] != '' && result['text'] != null) {
          String scanText = result['text'];
          
          // 对于SCANTRACKING模式，优先处理OCR识别的文本
          // if (widget.scanAction == ScanAction.SCANTRACKING) {
          //   String ocrTexts = result['ocrTexts'] ?? '';
          //   print('SCANTRACKING模式 - 原始扫描结果: $scanText');
          //   print('SCANTRACKING模式 - OCR文本: $ocrTexts');
          //   if (ocrTexts.isNotEmpty) {
          //     // 从OCR结果中提取快递单号
          //     String extractedTrackingNumber = _extractTrackingNumberFromOcr(ocrTexts);
          //     print('SCANTRACKING模式 - 提取的订单号: $extractedTrackingNumber');
          //     if (extractedTrackingNumber.isNotEmpty) {
          //       scanText = extractedTrackingNumber;
          //       print('SCANTRACKING模式 - 使用OCR提取的订单号: $scanText');
          //     } else {
          //       print('SCANTRACKING模式 - 未能从OCR中提取有效订单号，使用原始扫描结果');
          //     }
          //   } else {
          //     print('SCANTRACKING模式 - OCR文本为空，使用原始扫描结果');
          //   }
          // }
          
          checkScanResult(scanText);
        }
      }
    });
// 初始化摄像头
    Timer(const Duration(milliseconds: 500), () {
      this.scanView = ScanView(
        onScanViewCreated: (ScanViewController controller) async {
          scanViewController = controller;
          if (widget.scanAction != ScanAction.SCANSEARCH && widget.scanAction != ScanAction.SCANTRACKING) {
            scanViewController?.setQrScan();
          }
          
          // 对于SCANTRACKING模式，启用OCR功能以识别快递单号
          // if (widget.scanAction == ScanAction.SCANTRACKING) {
          //   bool isOCREnabled = await CheckUtils.checkOCRIsOpen();
          //   print('SCANTRACKING模式 - OCR是否启用: $isOCREnabled');
          //   if (isOCREnabled) {
          //     scanViewController?.enableOcr();
          //     print('SCANTRACKING模式 - OCR已启用');
          //   } else {
          //     print('SCANTRACKING模式 - OCR未启用，请检查设置');
          //   }
          // }
          
          scanViewController?.setCornerColor(DefaultConfig().configs.PRIMARY_COLOR_TEXT);
          scanViewController?.setScanTopOffset(
              60 + int.parse(MediaQuery.of(context).padding.top.toStringAsFixed(0)), 0);
        },
        onScanViewError: () {
          CommonUtils.showMessage(context, msg: "调用摄像头失败", success: false, duration: 2);
        },
      );
      if (mounted) {
        setState(() {});
      }
    });
  }

  checkScanResult(String url) async {
    if (isInProcess) {
      return false;
    }
    isInProcess = true;

    switch (widget.scanAction) {
      case ScanAction.BINDCABINET:
        String text = url.substring(url.indexOf('?c=') + 3);
        DataResult? res = await CourierDao.bindQr({'qrCode': url});
        if (res != null) {
          if (res.result) {
            stopSpot();
            SoundUtils.audioPushFn(SoundUtils.SUCCESS_OK);
            String hostIndex = '';
            if (text.split("|").length == 4) {
              if (text.split("|")[3] != 'null') {
                hostIndex = text.split("|")[3];
              }
            }
            Future.delayed(Duration(milliseconds: 200), () {
              NavigatorUtils.goCabinetMangerPage(context, hostIndex: hostIndex, code: res.data);
            });
          } else {
            SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_READ);
            Fluttertoast.showToast(msg: res.data, gravity: ToastGravity.CENTER);
            startSpot();
          }
        } else {
          Fluttertoast.showToast(msg: '二维码错误，请重新扫描', gravity: ToastGravity.CENTER);
          startSpot();
        }
        break;
      case ScanAction.SCANSEARCH:
        SoundUtils.audioPushFn(SoundUtils.SUCCESS_OK);
        Navigator.pushReplacement(
            context,
            new CupertinoPageRoute(
                builder: (context) => new PackageSearchPage(url, false),
                settings: RouteSettings(name: 'packageSearchPage')));
        break;
      case ScanAction.SCANQR:
        SoundUtils.audioPushFn(SoundUtils.SUCCESS_OK);
        // 扫描二维码，直接返回扫描结果
        Navigator.of(context).pop(url);
        break;
      case ScanAction.SCANTRACKING:
      print('扫描到的内容：$url');
        SoundUtils.audioPushFn(SoundUtils.SUCCESS_OK);
        // 专门用于订单号识别的扫描模式
        // 优先返回OCR识别的订单号，如果没有则返回原始扫描结果
        Navigator.of(context).pop(url);
        break;
    }
  }

  // 打开、关闭闪光灯
  setFlash() {
    checkFlash = !checkFlash;
    if (checkFlash) {
      scanViewController?.openFlashlight();
    } else {
      scanViewController?.closeFlashlight();
    }
    setState(() {});
  }

  buildScanView() {
    if (this.scanView != null) {
      return this.scanView;
    }
    return Container();
  }

  getTitle() {
    String title = '扫码';
    if (widget.scanAction == ScanAction.BINDCABINET) {
      title = '选择点位';
    }
    return title;
  }

  getDesc() {
    String title = '';
    if (widget.scanAction == ScanAction.BINDCABINET) {
      title = '扫描柜机上的快递员二维码';
    }
    return title;
  }

  _buildScanOrInputArea() {
    double _navHeight = 55.0 + paddingSizeTop(context);
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Stack(
        children: <Widget>[
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: MediaQuery.of(context).size.height - 0,
            child: buildScanView(),
          ),

          /// 标题部分
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              width: MediaQuery.of(context).size.width,
              height: _navHeight,
              padding: EdgeInsets.only(top: paddingSizeTop(context, isPlatform: true)),
              child: Container(
                child: Row(
                  children: <Widget>[
                    Offstage(
                      offstage: false,
                      child: InkWell(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: Container(
                          width: 60.0,
                          alignment: Alignment.center,
                          child: Icon(
                            Icons.arrow_back,
                            size: 25.0,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          SizedBox(
                            width: 10,
                          ),
                          Text(
                            getTitle(),
                            style: TextStyle(fontSize: 20.0, color: Colors.white),
                          ),
                          InkWell(
                            onTap: () async {
                              setFlash();
                            },
                            child: Padding(
                                padding: EdgeInsets.all(0.0),
                                child: LocalImageUtil.getImageAsset(checkFlash ? 'flashOpen' : 'flashClose',
                                    width: 20, height: 20)),
                          ),
                          // Padding(
                          //   padding: EdgeInsets.only(right: 10.0),
                          // )
                        ],
                      ),
                    ),
                    Offstage(
                        offstage: false,
                        child: Container(
                          width: 60.0,
                        )),
                  ],
                ),
              ),
            ),
          ),
          // Positioned(
          //     top: 0,
          //     left: 0,
          //     right: 0,
          //     bottom: 80,
          //     child: Center(
          //         child: Text(
          //       getDesc(),
          //       style: TextStyle(color: Colors.white, fontSize: 16),
          //     )))
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        bool canBack = true;
        if (isInProcess) {
          isInProcess = false;
          canBack = false;
        }
        Navigator.of(context).pop(true);
        return canBack;
      },
      child: StoreBuilder<AppState>(
        builder: (context, store) {
          return Scaffold(
            resizeToAvoidBottomInset: false,
            key: _scaffoldKey,
            backgroundColor: Colors.black,
            body: _buildScanOrInputArea(),
          );
        },
      ),
    );
  }

  double paddingSizeTop(BuildContext context, {bool isPlatform = false}) {
    final MediaQueryData data = MediaQuery.of(context);
    EdgeInsets padding = data.padding;
    padding = padding.copyWith(bottom: data.viewPadding.top);
    if (isPlatform) {
      if (Platform.isIOS) {
        return padding.top > 0 ? 5 : padding.top;
      }
    }
    return padding.top;
  }

  /// 从OCR结果中提取快递单号
  String _extractTrackingNumberFromOcr(String ocrTexts) {
    try {
      print('开始解析OCR结果: $ocrTexts');
      // 解析OCR结果JSON
      var ocrData = json.decode(ocrTexts);
      List<dynamic> texts = ocrData['texts'] ?? [];
      print('OCR识别到的文本数量: ${texts.length}');
      
      // 常见快递单号格式的正则表达式
      List<RegExp> trackingPatterns = [
        RegExp(r'[0-9]{10,20}'), // 10-20位数字
        RegExp(r'[A-Z]{2}[0-9]{9,15}[A-Z]{2}'), // 字母+数字+字母格式
        RegExp(r'[0-9]{4}[A-Z]{4}[0-9]{8}'), // 数字+字母+数字格式
        RegExp(r'[A-Z0-9]{10,20}'), // 字母数字混合10-20位
      ];
      
      // 遍历OCR识别的文本
      for (int i = 0; i < texts.length; i++) {
        var textItem = texts[i];
        String text = textItem['text']?.toString() ?? '';
        print('OCR文本[$i]: $text');
        text = text.replaceAll(' ', '').replaceAll('-', ''); // 移除空格和横线
        print('处理后的文本[$i]: $text');
        
        // 尝试匹配快递单号格式
        for (int j = 0; j < trackingPatterns.length; j++) {
          RegExp pattern = trackingPatterns[j];
          Iterable<Match> matches = pattern.allMatches(text);
          for (Match match in matches) {
            String candidate = match.group(0) ?? '';
            print('匹配到候选订单号: $candidate (模式$j)');
            if (_isValidTrackingNumber(candidate)) {
              print('验证通过，返回订单号: $candidate');
              return candidate;
            } else {
              print('验证失败: $candidate');
            }
          }
        }
      }
      print('未找到有效的订单号');
    } catch (e) {
      print('解析OCR结果失败: $e');
    }
    return '';
  }
  
  /// 验证是否为有效的快递单号
  bool _isValidTrackingNumber(String candidate) {
    if (candidate.isEmpty) return false;
    
    // 长度检查：快递单号通常在8-20位之间
    if (candidate.length < 8 || candidate.length > 20) return false;
    
    // 不能全是相同字符
    if (RegExp(r'^(.)\1*$').hasMatch(candidate)) return false;
    
    // 至少包含一些数字
    if (!RegExp(r'[0-9]').hasMatch(candidate)) return false;
    
    return true;
  }
}
