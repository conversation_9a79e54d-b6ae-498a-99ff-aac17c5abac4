import 'dart:async';
import 'dart:io';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/mixins/ScanMixin.dart';
import 'package:cabinet_flutter_app/common/page_life_cycle.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/DebounceUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/SoundUtils.dart';
import 'package:cabinet_flutter_app/page/scan/widget/CustomerScanForm.dart';
import 'package:cabinet_flutter_app/widget/keyborad/MyKeyboard.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:fluttertoast/fluttertoast.dart';

class BatchEntryScanCabinet extends StatefulWidget {
  final String cabinetLocationCode;
  final String cabinetName;
  final String hostIndex;
  final bool isBlind;

  BatchEntryScanCabinet(this.cabinetLocationCode, this.cabinetName, this.hostIndex, this.isBlind, {key})
      : super(key: key);

  @override
  _BatchEntryScanCabinetState createState() => _BatchEntryScanCabinetState();
}

class _BatchEntryScanCabinetState extends State<BatchEntryScanCabinet>
    with TickerProviderStateMixin, WidgetsBindingObserver, PageLifeCycle<BatchEntryScanCabinet>, ScanMixin {
  @override
  void initState() {
    super.initState();
    // 初始化生命周期监听 用于解决扫码库在生命周期变动时候被杀死
    WidgetsBinding.instance.addObserver(this);
    initData();
    initListener();
    checkFuc(height: 181);
    getBrandList();
  }

  initData() async {
    isBatch = true;
    isBlind = widget.isBlind;
    cabinetLocationCode = widget.cabinetLocationCode;
    cabinetName = widget.cabinetName;
    await getUseableCabinetBoxList(hostIndex: widget.hostIndex);
    initScan(350);
    showScanDb = buildNewScan();
    initMqtt();
  }

  // 路由显示触发
  @override
  void onShow() {
    this.initScan(350);
    this.getBrandList();
    this.startSpot();
  }

  @override
  void onHide() {
    scanSubscription_?.cancel();
    scanViewController?.stopSpot();
    timer_?.cancel();
    this.stopSpot();
  }

  @override
  Future<void> didChangeDependencies() async {
    super.didChangeDependencies();
    initFunc();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    ///通过state判断App前后台切换
    switch (state) {
      case AppLifecycleState.hidden:
      case AppLifecycleState.inactive: // 处于这种状态的应用程序应该假设它们可能在任何时候暂停。
        break;
      case AppLifecycleState.resumed: // 应用程序可见，前台
        initScan(350, isContinue: true);
        break;
      case AppLifecycleState.paused: // 应用程序不可见，后台
        scanSubscription_?.cancel();
        this.stopSpot();
        setState(() {
          this.scanView = null;
        });
        break;
      case AppLifecycleState.detached: // 申请将暂时暂停
        break;
    }
  }

  @override
  void dispose() {
    super.dispose();
    scanSubscription_?.cancel();
    timer_?.cancel();
    countDownTimer?.cancel();
    isChooseBrandCode = false;
    scanViewController?.stopSpot();
    WidgetsBinding.instance.removeObserver(this);
    controllerWidth?.dispose();
    controllerHeight?.dispose();
    mqttClient?.disconnect();
  }

  @override
  void deactivate() {
    super.deactivate();
  }

  /// 输入框 扫描框
  _buildScanOrInputArea() {
    return buildRKScan();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        bool canBack = true;
        if (isInProcess || isChooseBrandCode || isPause) {
          isInProcess = false;
          isChooseBrandCode = false;
          isPause = false;
          canBack = false;
        }
        // if (scanInputModelHeight > 190) {
        //   resetScan();
        //   canBack = false;
        //   return canBack;
        // }
        return canBack;
      },
      child: StoreBuilder<AppState>(
        builder: (context, store) {
          return Scaffold(
            key: scaffoldKey_,
            backgroundColor: Colors.black,
            body: _buildScanOrInputArea(),
          );
        },
      ),
    );
  }

  buildScanView() {
    if (this.scanView != null) {
      return this.scanView;
    }
    return Container();
  }

  /// 扫码界面
  buildRKScan() {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Stack(
        children: <Widget>[
          /// 扫码界面
          Positioned(
            top: 70,
            left: 0,
            right: 0,
            height: MediaQuery.of(context).size.height,
            child: buildScanView(),
          ),

          /// 标题部分
          buildTitle(),

          /// 格口信息部分
          widget.isBlind ? Container() : buildCabinet(),

          // /// 默认扫描框
          buildScanInputModel(),
        ],
      ),
    );
  }

  /// 标题部分
  buildTitle() {
    double _navHeight = 55.0 + paddingSizeTop(context);
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: new Container(
        width: MediaQuery.of(context).size.width,
        height: _navHeight,
        padding: EdgeInsets.only(top: paddingSizeTop(context, isPlatform: true)),
        child: new Container(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              InkWell(
                onTap: () {
                  Navigator.of(context).pop(true);
                },
                child: Container(
                  width: leadingWidth,
                  padding: EdgeInsets.only(left: 12),
                  alignment: Alignment.centerLeft,
                  child: Icon(
                    Icons.arrow_back,
                    size: 25.0,
                    color: Colors.white,
                  ),
                ),
              ),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Text(
                      widget.isBlind ? '盲投入柜' : '批量入柜',
                      style: TextStyle(fontSize: 20.0, color: Colors.white),
                    ),
                    InkWell(
                      onTap: () async {
                        setFlash();
                      },
                      child: Padding(
                          padding: EdgeInsets.all(10.0),
                          child: LocalImageUtil.getImageAsset(checkFlash ? 'flashOpen' : 'flashClose',
                              width: 20, height: 20)),
                    ),
                  ],
                ),
              ),
              InkWell(
                onTap: () => throttleUtil.throttle(() {
                  selectCabinetInfo(hostIndex: widget.hostIndex);
                }),
                child: Container(
                  width: leadingWidth,
                  alignment: Alignment.center,
                  child: Text('选择柜机', style: TextStyle(color: Theme.of(context).primaryColor)),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  /// 格口信息
  buildCabinet() {
    double _navHeight = 55.0 + MediaQuery.of(context).padding.top;
    return Positioned(
      top: 70,
      left: 0,
      right: 0,
      child: new Container(
        width: MediaQuery.of(context).size.width,
        height: _navHeight,
        child: new Container(
            margin: EdgeInsets.only(left: 20, right: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                  padding: EdgeInsets.only(bottom: 6),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(right: 10, bottom: 6),
                        child: LocalImageUtil.getImageAsset('box', width: 22, isChannel: true),
                      ),
                      RichText(
                          text: TextSpan(children: <TextSpan>[
                        TextSpan(
                            text: '${currentBoxItem?.boxLabel ?? '?'}',
                            style: TextStyle(
                                color: Theme.of(context).primaryColor, fontSize: 36, fontWeight: FontWeight.w600)),
                        TextSpan(
                            text: '号格口',
                            style: TextStyle(
                                color: Theme.of(context).primaryColor, fontSize: 18, fontWeight: FontWeight.w500)),
                      ])),
                      Padding(
                        padding: EdgeInsets.only(left: 10, bottom: 4),
                        child: InkWell(
                          onTap: () => throttleUtil.throttle(() {
                            selectBoxItem();
                          }),
                          child: Text('切换格口',
                              style: TextStyle(
                                  color: Theme.of(context).primaryColor,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  decoration: TextDecoration.underline)),
                        ),
                      ),
                    ],
                  ),
                ),
                InkWell(
                  onTap: () => throttleUtil.throttle(() {
                    skipBoxItem(playSound: true);
                  }),
                  child: Text('跳过格口',
                      style: TextStyle(
                          color: Theme.of(context).primaryColor,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          decoration: TextDecoration.underline)),
                )
              ],
            )),
      ),
    );
  }

  /// 品牌选择
  selectBrand({String? brand, bool isForce = false}) {
    SoundUtils.audioPushFn(SoundUtils.CHOOSE_COMPANY);
    CommonUtils.showBottomBrandSelectModal(context, brandMap, (item) {
      if (item != '') {
        SoundUtils.audioPushFn(SoundUtils.BRAND_SOUND[item]!);
        brandCode = item;
        showScanDb.brandCode = item;
        waybillRemoteGuss(isScan: false);
      } else {
        if (brand != null && brand != '') {
          brandCode = brand;
          showScanDb.brandCode = brand;
        }
      }
      setState(() {});
    }, bindCompany: brand, isForce: isForce);
  }

  getButtonStatus() {
    bool status = (showScanDb.receiverMobile != '' && showScanDb.receiverMobile.length >= 11) &&
        showScanDb.waybillNo != '' &&
        showScanDb.cabinetBoxId != '';
    return status;
  }

  openBox() async {
    if (isCourier) {
      openBoxCourier();
    } else {
      openBoxShop();
    }
  }

  openBoxShop() async {
    isCountDown = false;
    isPause = true;
    LoadingUtil(
      status: '正在创建订单...',
    ).show(context);
    if (imagePath != '') {
      var savePhoto = await CheckUtils.checkPhotoUpload(
          imagePath, showScanDb.waybillNo, showScanDb.brandCode, cabinetLocationCode, 'inbound');
      showScanDb.inboundImageUrl = savePhoto;
    }
    var res = await CourierDao.courierShopOpenDoor(
        cabinetLocationCode,
        showScanDb.cabinetId,
        showScanDb.cabinetBoxId,
        showScanDb.waybillNo,
        showScanDb.brandCode,
        showScanDb.receiverName,
        showScanDb.receiverMobile,
        showScanDb.virtualPhone,
        gussEntity.hasSecret ? '1' : '0',
        gussEntity.hasSubstituteSms ? '1' : '0',
        '0',
        showScanDb.newCustomer.toString(),
        '0',
        '0',
        platform,
        gussEntity.yzSopAccountId,
        gussEntity.yzChannel,
        gussEntity.virtualNumber,
        showScanDb.inboundImageUrl,
        addInCabinet: showScanDb.addInCabinet,
        deliveryWaybill: isDelivery ? 1 : 0,
        hasDp: isBDP ? 1 : 0,
        isBatch: !showScanDb.addInCabinet!);
    LoadingUtil.dismiss(context);
    isPause = false;
    if (res != null && res.result) {
      handleSuccessResult(res.data);
    } else if (res.data != '操作太频繁' && !res.result) {
        print('开门失败${res.data}');
        Fluttertoast.showToast(msg: '存件失败');
        SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
      }
  }

  openBoxCourier() async {
    try {
      LoadingUtil(
        status: '正在创建订单...',
      ).show(context);
      isPause = true;

      showScanDb.cabinetLocationCode = cabinetLocationCode;
      showScanDb.platform = platform;
      showScanDb.virtualNumber = gussEntity.virtualNumber.toString();
      showScanDb.secretWaybill = gussEntity.hasSecret ? '1' : '0';
      showScanDb.hasSubstituteSms = gussEntity.hasSubstituteSms ? '1' : '0';
      showScanDb.boxType = boxType;
      if (imagePath != '') {
        var savePhoto = await CheckUtils.checkPhotoUpload(
            imagePath, showScanDb.waybillNo, showScanDb.brandCode, cabinetLocationCode, 'inbound');
        showScanDb.inboundImageUrl = savePhoto;
      }
      showScanDb.deliveryWaybill = isDelivery ? 1 : 0;
      var res = await CourierDao.courierOrderWaybillCreate(
          showScanDb, lastCabinetBoxId, isBDP ? 1 : 0, gussEntity.yzSopAccountId, gussEntity.yzChannel,
          isBatch: !showScanDb.addInCabinet!, hostIndex: widget.hostIndex);
      LoadingUtil.dismiss(context);
      isPause = false;
      if (res.result && res.data != null) {
        handleSuccessResult(res.data);
      } else if (res.data != '操作太频繁' && !res.result) {
        print('开门失败${res.data}');
        Fluttertoast.showToast(msg: '存件失败');
        SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
      }
    } catch (e) {
      LoadingUtil.dismiss(context);
      isPause = false;
    }
  }

  // 抽取成功处理逻辑
  void handleSuccessResult(Map<String, dynamic> data) {
    orderId = data['id'];
    inboundTime = data['inboundTime'];
    isCountDown = true;
    if (isBlind) {
      SoundUtils.audioPushFn(SoundUtils.INBOUND_SUCCESS);
    } else {
      if (Platform.isIOS) {
        SoundUtils.audioPushFn(SoundUtils.INBOUND_SUCCESS);
      }
      scanPlugin.playSound("请将包裹放入${currentBoxItem!.boxLabel!}号格口");
    }
    cancelCountDown();
    resetScan(isSkipNext: true);
  }

  /// 默认扫描框
  buildScanInputModel() {
    return Stack(
      children: [
        Positioned(
            top: 180,
            right: 22,
            child: Column(
              children: [
                ...buildDeliveryWaybill(),
                ...buildBDP(isLabel: true),
                ...buildAddCabinet(isLabel: true),
                ...buildOpenOCR(isLabel: true),
                ...buildSingleCustomerNotice(isLabel: true),
                widget.isBlind ? Container() : Column(children: [...buildBatchFastInto(isLabel: true)])
              ],
            )),
        Positioned(
            bottom: 221,
            right: 22,
            child: Row(
              children: [
                Offstage(
                  offstage: !isCountDown!,
                  child: inboundTime != null ? cancelCabinet(inboundTime!) : Container(),
                )
              ],
            )),
        Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Column(
              children: [
                Container(
                  height: scanInputModelHeight,
                  // padding: EdgeInsets.only(bottom: 15),
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(topLeft: Radius.circular(8), topRight: Radius.circular(8))),
                  child: CustomerScanForm(
                    brandCode: brandCode,
                    selectBrand: () {
                      if (!hasChangeBrand) {
                        Fluttertoast.showToast(msg: '该点位不允许修改快递品牌');
                        return;
                      }
                      selectBrand(brand: brandCode);
                    },
                    waybillNoEditingController: waybillNoEditingController,
                    waybillNoFocusNode_: focusNode_,
                    waybillNoChange: (name) {
                      setState(() {
                        showScanDb.waybillNo = name;
                      });
                    },
                    waybillNoClear: () => throttleUtil.throttle(() {
                      resetScan();
                    }),
                    waybillNoInputFunc: () {
                      stopSpot();
                      CommonUtils.inputWaybillNo(context, showScanDb.waybillNo, (waybillNo) {
                        if (waybillNo != '') {
                          isInProcess = false;
                          addScan(waybillNo, isScan: false);
                        } else {
                          startSpot();
                        }
                      });
                    },
                    nameFocusNode_: nameFocusNode_,
                    nameEditingController: nameEditingController,
                    nameChange: (name) {
                      showScanDb.receiverName = name;
                    },
                    nameClear: () {
                      setState(() {
                        nameEditingController.value = TextEditingValue(text: '');
                        showScanDb.receiverName = '';
                      });
                    },
                    mobileFocusNode_: mobileFocusNode_,
                    mobileEditingController: mobileEditingController,
                    mobileInputTap: () {
                      showKeyBoard(TextInputType.number);
                      FocusScope.of(context).requestFocus(mobileFocusNode_);
                    },
                    mobileChange: (phone) {
                      checkMobileInput(phone);
                    },
                    mobileClear: () {
                      clearPhoneController();
                    },
                    showMobileFitButton: showMobileGussByLast4Button,
                    mobileFitFunc: () => throttleUtil.throttle(() {
                      mobileGussByLast4();
                    }),
                    showNewCustomer: showScanDb.newCustomer == null || showScanDb.newCustomer == 0,
                    speechRecogntionFunc: () {
                      setState(() {
                        voiceStatus = true;
                        openVoiceCountDown();
                        Fluttertoast.showToast(msg: '请读出手机号码');
                      });
                    },
                    voiceStatus: voiceStatus,
                    resetScan: () => throttleUtil.throttle(() {
                      resetScan();
                    }),
                    confirmNextStep: () => throttleUtil.throttle(() {
                      confirmNextStep(isInbound: true);
                    }),
                    buttonStatus: getButtonStatus(),
                    showGKSelection: false,
                    confirmButtonLabel: '确认入柜',
                    addInCabinet: showScanDb.addInCabinet ?? false,
                    isBlind: widget.isBlind,
                    isPda: false,
                  ),
                ),
                MyKeyboard(showKeyboard, onKeyDown,
                    textInputType: textInputType,
                    textInputAction:
                        mobileEditingController.text.length == 4 ? TextInputAction.search : TextInputAction.done)
              ],
            ))
      ],
    );
  }

  cancelCabinet(String inTime) {
    return Offstage(
      offstage: countTime < 1,
      child: Container(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              height: 32,
              margin: EdgeInsets.only(bottom: 10, right: 10),
              child: OutlinedButton(
                onPressed: () {
                  countDownTimer?.cancel();

                  /// 取消入柜
                  cancelCabinetInbound(cabinetLocationCode, orderId!, hostIndex: widget.hostIndex);
                },
                child: Text(
                  '取消入柜${countTime}s',
                  style: TextStyle(color: DefaultConfig().configs.WHITE_COLOR),
                ),
                style: ButtonStyle(
                    shape: MaterialStateProperty.all(StadiumBorder()),
                    side: MaterialStateProperty.all(BorderSide(color: Color(0xFFFFFFFF), width: 2))),
              ),
            )
          ],
        ),
      ),
    );
  }
}
