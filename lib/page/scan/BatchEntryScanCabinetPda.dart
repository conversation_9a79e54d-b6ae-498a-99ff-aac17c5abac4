import 'dart:async';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/mixins/ScanMixin.dart';
import 'package:cabinet_flutter_app/common/page_life_cycle.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/SoundUtils.dart';
import 'package:cabinet_flutter_app/page/scan/widget/CustomerScanForm.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/keyborad/MyKeyboard.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:fluttertoast/fluttertoast.dart';

class BatchEntryScanCabinetPda extends StatefulWidget {
  final String cabinetLocationCode;
  final String cabinetName;
  final String hostIndex;
  final bool isBlind;

  BatchEntryScanCabinetPda(this.cabinetLocationCode, this.cabinetName, this.hostIndex, this.isBlind, {key})
      : super(key: key);

  @override
  _BatchEntryScanCabinetPdaState createState() => _BatchEntryScanCabinetPdaState();
}

class _BatchEntryScanCabinetPdaState extends State<BatchEntryScanCabinetPda>
    with TickerProviderStateMixin, WidgetsBindingObserver, PageLifeCycle<BatchEntryScanCabinetPda>, ScanMixin {
  @override
  void initState() {
    super.initState();
    // 初始化生命周期监听 用于解决扫码库在生命周期变动时候被杀死
    WidgetsBinding.instance.addObserver(this);
    initData();
    initListener();
    checkFuc();
    getBrandList();
    initScan(320);
  }

  initData() async {
    isBatch = true;
    isBlind = widget.isBlind;
    cabinetLocationCode = widget.cabinetLocationCode;
    cabinetName = widget.cabinetName;
    await getUseableCabinetBoxList(hostIndex: widget.hostIndex);
    showScanDb = buildNewScan();
    // 连接mqtt
    initMqtt();
  }

  // 路由显示触发
  @override
  void onShow() {
    this.initScan(320);
    this.getBrandList();
    this.startSpot();
  }

  @override
  void onHide() {
    scanSubscription_?.cancel();
    scanViewController?.stopSpot();
    timer_?.cancel();
    this.stopSpot();
  }

  @override
  Future<void> didChangeDependencies() async {
    super.didChangeDependencies();
    initFunc();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    ///通过state判断App前后台切换
    switch (state) {
      case AppLifecycleState.hidden:
      case AppLifecycleState.inactive: // 处于这种状态的应用程序应该假设它们可能在任何时候暂停。
        break;
      case AppLifecycleState.resumed: // 应用程序可见，前台
        initScan(320, isContinue: true);
        break;
      case AppLifecycleState.paused: // 应用程序不可见，后台
        scanSubscription_?.cancel();
        this.stopSpot();
        setState(() {
          this.scanView = null;
        });
        break;
      case AppLifecycleState.detached: // 申请将暂时暂停
        break;
    }
  }

  @override
  void dispose() {
    super.dispose();
    scanSubscription_?.cancel();
    timer_?.cancel();
    countDownTimer?.cancel();
    isChooseBrandCode = false;
    scanViewController?.stopSpot();
    WidgetsBinding.instance.removeObserver(this);
    controllerWidth?.dispose();
    controllerHeight?.dispose();
    mqttClient?.disconnect();
  }

  @override
  void deactivate() {
    super.deactivate();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        bool canBack = true;
        if (isInProcess || isChooseBrandCode || isPause) {
          isInProcess = false;
          isChooseBrandCode = false;
          isPause = false;
          canBack = false;
        }
        return canBack;
      },
      child: StoreBuilder<AppState>(
        builder: (context, store) {
          return Scaffold(
            key: scaffoldKey_,
            backgroundColor: DefaultConfig().configs.BG_COLOR,
            appBar: new AppCustomerBar(
              leadingWidth: leadingWidth,
              title: AppbarTitle(
                title: widget.isBlind ? '盲投入柜' : '批量入柜',
                isCenter: true,
              ),
              actions: [
                InkWell(
                  onTap: () => throttleUtil.throttle(() {
                    // selectCabinetInfo();
                  }),
                  child: Container(
                    width: leadingWidth,
                    alignment: Alignment.center,
                    child: Text('', style: TextStyle(color: Theme.of(context).primaryColor)),
                  ),
                )
              ],
            ),
            body: buildRKScan(),
          );
        },
      ),
    );
  }

  buildInputView() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Offstage(
              offstage: isIData,
              child: Offstage(
                offstage: !isCountDown!,
                child: inboundTime != null
                    ? Container(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [cancelCabinet()],
                        ),
                      )
                    : Container(),
              ),
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Container(
              padding: EdgeInsets.only(bottom: 5),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  ...buildDeliveryWaybill(colors: Colors.black),
                  ...buildBDP(colors: Colors.black),
                  ...buildAddCabinet(colors: Colors.black),
                  ...buildOpenOCR(colors: Colors.black),
                  ...buildSingleCustomerNotice(colors: Colors.black),
                  widget.isBlind ? Container() : Column(children: [...buildBatchFastInto(colors: Colors.black)])
                ],
              ),
            ),
          ],
        ),
        Container(
          color: Colors.white,
          margin: EdgeInsets.only(top: 10),
          padding: EdgeInsets.only(bottom: 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: EdgeInsets.only(left: 20, top: 5, bottom: 5),
                    child: Text('${cabinetName == '' ? '?' : cabinetName}号柜',
                        style: TextStyle(color: Colors.black, fontSize: 17, fontWeight: FontWeight.w700)),
                  ),
                  InkWell(
                    onTap: () => throttleUtil.throttle(() {
                      selectCabinetInfo(hostIndex: widget.hostIndex);
                    }),
                    child: Container(
                      width: leadingWidth,
                      alignment: Alignment.center,
                      child: Text('选择柜机', style: TextStyle(color: Theme.of(context).primaryColor)),
                    ),
                  ),
                  Offstage(
                    offstage: !isIData,
                    child: Offstage(
                      offstage: !isCountDown!,
                      child: inboundTime != null
                          ? Container(
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [cancelCabinet()],
                              ),
                            )
                          : Container(),
                    ),
                  ),
                ],
              ),
              widget.isBlind
                  ? Container()
                  : Container(
                      margin: EdgeInsets.only(left: 15, right: 15),
                      width: MediaQuery.of(context).size.width - 30,
                      height: 1,
                      color: DefaultConfig().configs.BG_COLOR,
                    ),

              /// 格口号输入框
              widget.isBlind
                  ? Container()
                  : Row(
                      children: [
                        Expanded(
                          child: Row(
                            children: [
                              Container(
                                width: 30,
                                height: 30,
                                padding: EdgeInsets.fromLTRB(8, 3, 3, 3),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(2),
                                  child: LocalImageUtil.getImageAsset('box3'),
                                ),
                                margin: EdgeInsets.only(left: 10, right: 10),
                              ),
                              Container(
                                width: 90,
                                padding: EdgeInsets.only(left: 10),
                                child: Text('格口号',
                                    style: TextStyle(fontSize: 16, color: Colors.black87, fontWeight: FontWeight.w400)),
                              ),
                              Expanded(
                                child: Container(
                                  height: 64,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      Padding(
                                        padding: EdgeInsets.only(bottom: 10),
                                        child: Row(
                                          crossAxisAlignment: CrossAxisAlignment.end,
                                          children: [
                                            RichText(
                                                text: TextSpan(children: <TextSpan>[
                                              TextSpan(
                                                  text: '${currentBoxItem?.boxLabel ?? '?'}',
                                                  style: TextStyle(
                                                      color: Theme.of(context).primaryColor,
                                                      fontSize: 36,
                                                      fontWeight: FontWeight.w600)),
                                            ])),
                                            Padding(
                                              padding: EdgeInsets.only(left: 10, bottom: 4),
                                              child: InkWell(
                                                  onTap: () => throttleUtil.throttle(() {
                                                        selectBoxItem();
                                                      }),
                                                  child: Row(
                                                    children: [
                                                      Icon(Icons.cached_rounded, color: Theme.of(context).primaryColor),
                                                      Text('切换',
                                                          style: TextStyle(
                                                              color: Theme.of(context).primaryColor,
                                                              fontSize: 16,
                                                              fontWeight: FontWeight.w500,
                                                              decoration: TextDecoration.underline)),
                                                    ],
                                                  )),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Padding(
                                        padding: EdgeInsets.only(right: 15),
                                        child: InkWell(
                                          onTap: () => throttleUtil.throttle(() {
                                            skipBoxItem(playSound: true);
                                          }),
                                          child: Row(
                                            children: [
                                              Icon(Icons.skip_next, color: Theme.of(context).primaryColor),
                                              Text('下一个',
                                                  style: TextStyle(
                                                      color: Theme.of(context).primaryColor,
                                                      fontSize: 16,
                                                      fontWeight: FontWeight.w500,
                                                      decoration: TextDecoration.underline)),
                                            ],
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

              Container(
                margin: EdgeInsets.only(left: 15, right: 15),
                width: MediaQuery.of(context).size.width - 30,
                height: 1,
                color: DefaultConfig().configs.BG_COLOR,
              ),

              CustomerScanForm(
                brandCode: brandCode,
                selectBrand: () {
                  if (!hasChangeBrand) {
                    Fluttertoast.showToast(msg: '该点位不允许修改快递品牌');
                    return;
                  }
                  selectBrand(brand: brandCode);
                },
                waybillNoEditingController: waybillNoEditingController,
                waybillNoFocusNode_: focusNode_,
                waybillNoChange: (name) {
                  setState(() {
                    showScanDb.waybillNo = name;
                  });
                },
                waybillNoClear: () => throttleUtil.throttle(() {
                  resetScan();
                }),
                waybillNoInputFunc: () {
                  stopSpot();
                  CommonUtils.inputWaybillNo(context, showScanDb.waybillNo, (waybillNo) {
                    if (waybillNo != '') {
                      isInProcess = false;
                      addScan(waybillNo, isScan: false);
                    } else {
                      startSpot();
                    }
                  });
                },
                nameFocusNode_: nameFocusNode_,
                nameEditingController: nameEditingController,
                nameChange: (name) {
                  showScanDb.receiverName = name;
                },
                nameClear: () {
                  setState(() {
                    nameEditingController.value = TextEditingValue(text: '');
                    showScanDb.receiverName = '';
                  });
                },
                mobileFocusNode_: mobileFocusNode_,
                mobileEditingController: mobileEditingController,
                mobileInputTap: () {
                  showKeyBoard(TextInputType.number);
                  FocusScope.of(context).requestFocus(mobileFocusNode_);
                },
                mobileChange: (phone) {
                  checkMobileInput(phone);
                },
                mobileClear: () {
                  clearPhoneController();
                },
                showMobileFitButton: showMobileGussByLast4Button,
                mobileFitFunc: () => throttleUtil.throttle(() {
                  mobileGussByLast4();
                }),
                showNewCustomer: showScanDb.newCustomer == null || showScanDb.newCustomer == 0,
                speechRecogntionFunc: () {
                  setState(() {
                    voiceStatus = true;
                    openVoiceCountDown();
                    Fluttertoast.showToast(msg: '请读出手机号码');
                  });
                },
                voiceStatus: voiceStatus,
                resetScan: () => throttleUtil.throttle(() {
                  resetScan();
                }),
                confirmNextStep: () => throttleUtil.throttle(() {
                  confirmNextStep(isInbound: true, flag: 5);
                }),
                buttonStatus: getButtonStatus(),
                showGKSelection: false,
                confirmButtonLabel: '确认入柜',
                addInCabinet: showScanDb.addInCabinet ?? false,
                isBlind: widget.isBlind,
                isPda: true,
              ),
            ],
          ),
        ),
        MyKeyboard(showKeyboard, onKeyDown,
            textInputType: textInputType,
            textInputAction: mobileEditingController.text.length == 4 ? TextInputAction.search : TextInputAction.done)
      ],
    );
  }

  /// 扫码界面
  buildRKScan() {
    return Container(
      height: MediaQuery.of(context).size.height,
      child: Stack(
        children: <Widget>[
          // ScanNotice(content: '柜机小助手：重复扫描单号可确认入柜哦~'),
          // /// 默认扫描框
          Positioned(left: 0, right: 0, bottom: 0, child: buildInputView())
        ],
      ),
    );
  }

  /// 品牌选择
  selectBrand({String? brand, bool isForce = false}) {
    SoundUtils.audioPushFn(SoundUtils.CHOOSE_COMPANY);
    CommonUtils.showBottomBrandSelectModal(context, brandMap, (item) {
      if (item != '') {
        SoundUtils.audioPushFn(SoundUtils.BRAND_SOUND[item]!);
        brandCode = item;
        showScanDb.brandCode = item;
        waybillRemoteGuss(isScan: false);
      } else {
        if (brand != null && brand != '') {
          brandCode = brand;
          showScanDb.brandCode = brand;
        }
      }
      setState(() {});
    }, bindCompany: brand, isForce: isForce);
  }

  getButtonStatus() {
    bool status = (showScanDb.receiverMobile != '' && showScanDb.receiverMobile.length >= 11) &&
        showScanDb.waybillNo != '' &&
        showScanDb.cabinetBoxId != '';
    return status;
  }

  openBox() async {
    if (isCourier) {
      openBoxCourier();
    } else {
      openBoxShop();
    }
  }

  openBoxShop() async {
    isCountDown = false;
    isPause = true;
    LoadingUtil(
      status: '正在创建订单...',
    ).show(context);
    if (imagePath != '') {
      var savePhoto = await CheckUtils.checkPhotoUpload(
          imagePath, showScanDb.waybillNo, showScanDb.brandCode, cabinetLocationCode, 'inbound');
      showScanDb.inboundImageUrl = savePhoto;
    }
    var res = await CourierDao.courierShopOpenDoor(
        cabinetLocationCode,
        showScanDb.cabinetId,
        showScanDb.cabinetBoxId,
        showScanDb.waybillNo,
        showScanDb.brandCode,
        showScanDb.receiverName,
        showScanDb.receiverMobile,
        showScanDb.virtualPhone,
        gussEntity.hasSecret ? '1' : '0',
        gussEntity.hasSubstituteSms ? '1' : '0',
        '0',
        showScanDb.newCustomer.toString(),
        '0',
        '0',
        platform,
        gussEntity.yzSopAccountId,
        gussEntity.yzChannel,
        gussEntity.virtualNumber,
        showScanDb.inboundImageUrl,
        addInCabinet: showScanDb.addInCabinet,
        deliveryWaybill: isDelivery ? 1 : 0,
        hasDp: isBDP ? 1 : 0,
        isBatch: !showScanDb.addInCabinet!);
    LoadingUtil.dismiss(context);
    isPause = false;
    if (res != null && res.result) {
      handleSuccessResult(res.data);
    } else if (res.data != '操作太频繁' && !res.result) {
        print('开门失败${res.data}');
        Fluttertoast.showToast(msg: '存件失败');
        SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
    }
  }

  openBoxCourier() async {
    LoadingUtil(
      status: '正在创建订单...',
    ).show(context);
    isPause = true;

    showScanDb.cabinetLocationCode = cabinetLocationCode;
    showScanDb.platform = platform;
    showScanDb.virtualNumber = gussEntity.virtualNumber.toString();
    showScanDb.secretWaybill = gussEntity.hasSecret ? '1' : '0';
    showScanDb.hasSubstituteSms = gussEntity.hasSubstituteSms ? '1' : '0';
    showScanDb.boxType = boxType;
    if (imagePath != '') {
      var savePhoto = await CheckUtils.checkPhotoUpload(
          imagePath, showScanDb.waybillNo, showScanDb.brandCode, cabinetLocationCode, 'inbound');
      showScanDb.inboundImageUrl = savePhoto;
    }
    showScanDb.deliveryWaybill = isDelivery ? 1 : 0;
    var res = await CourierDao.courierOrderWaybillCreate(
        showScanDb, lastCabinetBoxId, isBDP ? 1 : 0, gussEntity.yzSopAccountId, gussEntity.yzChannel,
        isBatch: !showScanDb.addInCabinet!, hostIndex: widget.hostIndex);
    LoadingUtil.dismiss(context);
    isPause = false;
    if (res! != null && res.result) {
      handleSuccessResult(res.data);
    } else if (res.data != '操作太频繁' && !res.result) {
      Fluttertoast.showToast(msg: '存件失败');
      SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
    }
  }

  // 抽取成功处理逻辑
  void handleSuccessResult(Map<String, dynamic> data) {
    orderId = data['id'];
    inboundTime = data['inboundTime'];
    isCountDown = true;
    if (isBlind) {
      SoundUtils.audioPushFn(SoundUtils.INBOUND_SUCCESS);
    } else {
      scanPlugin.playSound("请将包裹放入${currentBoxItem!.boxLabel!}号格口");
    }
    cancelCountDown();
    resetScan(isSkipNext: true);
  }

  cancelCabinet() {
    return Offstage(
      offstage: countTime < 1,
      // offstage: false,
      child: Container(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              height: 42,
              margin: EdgeInsets.only(bottom: 10, right: 10),
              child: OutlinedButton(
                onPressed: () {
                  countDownTimer?.cancel();

                  /// 取消入柜
                  cancelCabinetInbound(cabinetLocationCode, orderId!, hostIndex: widget.hostIndex);
                },
                child: Text(
                  '取消入柜${countTime}s',
                  style: TextStyle(color: Theme.of(context).primaryColor),
                ),
                style: ButtonStyle(
                    shape: MaterialStateProperty.all(StadiumBorder()),
                    side: MaterialStateProperty.all(BorderSide(color: Theme.of(context).primaryColor, width: 2))),
              ),
            )
          ],
        ),
      ),
    );
  }
}
