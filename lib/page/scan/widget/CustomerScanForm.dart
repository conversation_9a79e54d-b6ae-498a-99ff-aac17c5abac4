import 'dart:io';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class CustomerScanForm extends StatefulWidget {
  final double marginTop;

  final String? brandCode;

  /// brand selection
  final VoidCallback? selectBrand;

  /// waybillNo controller
  final TextEditingController? waybillNoEditingController;

  /// waybill FocusNode
  final FocusNode? waybillNoFocusNode_;

  /// waybillNo change
  final ValueChanged<String>? waybillNoChange;

  /// waybillNo clear
  final VoidCallback? waybillNoClear;

  /// waybillNo input function
  final VoidCallback? waybillNoInputFunc;

  /// name focusnode
  final FocusNode? nameFocusNode_;

  /// name controller
  final TextEditingController? nameEditingController;

  /// name change
  final ValueChanged<String>? nameChange;

  /// name clear
  final VoidCallback? nameClear;

  /// mobile focusnode
  final FocusNode? mobileFocusNode_;

  /// mobile controller
  final TextEditingController? mobileEditingController;

  /// mobile change
  final ValueChanged<String>? mobileChange;

  /// mobile clear
  final VoidCallback? mobileClear;

  /// mobile clear
  final VoidCallback? mobileInputTap;

  /// 是否显示手机号匹配按钮
  final bool showMobileFitButton;

  /// 手机号匹配方法
  final VoidCallback? mobileFitFunc;

  /// 是否显示新用户标志
  final bool showNewCustomer;

  /// speech  recogntion function
  final VoidCallback? speechRecogntionFunc;

  final bool voiceStatus;

  /// resetScan
  final VoidCallback? resetScan;

  /// confirmNextStep
  final VoidCallback? confirmNextStep;

  /// 确认按钮状态
  final bool buttonStatus;

  /// 是否显示可用格口列表
  final bool showGKSelection;

  /// 可用格口列表
  final List boxAvaliableList;

  /// 是否上门 true 不显示取件码
  final bool isDelivery;

  /// 是否盲投
  final bool isBlind;

  /// 是否追加
  final bool addInCabinet;

  /// 选择格口类型
  final ValueChanged? chooseGK;

  /// 格口类型
  final int boxType;
  final bool? isPda;
  final String confirmButtonLabel;

  /// 取件码
  final String checkCode;
  final VoidCallback? clickCheckCode;
  final VoidCallback? goCheckCodePage;
  final VoidCallback? refreshCheckCode;

  /// 是否显示价格 快递员派件入柜显示
  final bool? showPrice;
  final Widget? appendWidget;

  CustomerScanForm(
      {this.marginTop = 8,
      this.brandCode,
      this.selectBrand,
      this.waybillNoEditingController,
      this.waybillNoFocusNode_,
      this.waybillNoChange,
      this.waybillNoClear,
      this.waybillNoInputFunc,
      this.nameFocusNode_,
      this.nameEditingController,
      this.nameChange,
      this.nameClear,
      this.mobileFocusNode_,
      this.mobileEditingController,
      this.mobileChange,
      this.mobileClear,
      this.mobileInputTap,
      this.showMobileFitButton = false,
      this.mobileFitFunc,
      this.showNewCustomer = false,
      this.speechRecogntionFunc,
      this.voiceStatus = false,
      this.resetScan,
      this.confirmNextStep,
      this.buttonStatus = false,
      this.showGKSelection = true,
      this.boxAvaliableList = const [],
      this.isDelivery = true,
      this.isBlind = false,
      this.addInCabinet = false,
      this.chooseGK,
      this.boxType = 0,
      this.isPda = false,
      this.confirmButtonLabel = '打开柜门',
      this.checkCode = '',
      this.clickCheckCode,
      this.goCheckCodePage,
      this.refreshCheckCode,
      this.showPrice = false,
      this.appendWidget});

  @override
  _CustomerScanFormState createState() => _CustomerScanFormState();
}

class _CustomerScanFormState extends State<CustomerScanForm> {
  bool isPda = false;
  bool isCustomerKeyborad = true;

  @override
  void initState() {
    super.initState();
    isPda = widget.isPda!;
    this.init();
  }

  init() async {
    isCustomerKeyborad = await CheckUtils.isCustomerKeyboard();
  }

  @override
  Widget build(BuildContext context) {
    double? itemHeight = isPda ? 64 : 48;
    return Container(
      padding: EdgeInsets.only(top: widget.marginTop),
      child: Column(
        children: [
          /// 单号输入框
          Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    InkWell(
                      onTap: () {
                        widget.selectBrand!();
                      },
                      child: Container(
                        child: Row(
                          children: [
                            Container(
                              width: isPda ? 36 : 30,
                              height: isPda ? 36 : 30,
                              child: CircleAvatar(
                                radius: 10,
                                backgroundColor: Colors.white,
                                backgroundImage: AssetImage(CommonUtils.getExpressLogo(widget.brandCode)),
                              ),
                              margin: EdgeInsets.only(left: 10, right: 5),
                            ),
                            Container(
                              width: isPda ? 60 : 55,
                              padding: EdgeInsets.only(left: 10),
                              child: Text(DefaultConfig().configs.EXPRESS2[widget.brandCode],
                                  style: TextStyle(
                                      fontSize: isPda ? 16 : 14, color: Colors.black87, fontWeight: FontWeight.w400)),
                            ),
                            Icon(
                              Icons.keyboard_arrow_down,
                              size: 24,
                              color: Colors.black,
                            )
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: Container(
                          height: itemHeight,
                          margin: EdgeInsets.only(left: 10),
                          padding: EdgeInsets.fromLTRB(0, isPda ? 10 : 0, 0, isPda ? 10 : 0),
                          child: TextField(
                            keyboardType: TextInputType.number,
                            controller: widget.waybillNoEditingController,
                            style: TextStyle(
                              fontSize: isPda ? 18 : 15,
                              color: Colors.black87,
                            ),
                            focusNode:widget.waybillNoFocusNode_,
                            enabled: false,
                            decoration: new InputDecoration(
                                border: InputBorder.none,
                                contentPadding: EdgeInsets.all(0),
                                hintText: '输入快递运单号',
                                hintStyle: new TextStyle(fontSize: isPda ? 18 : 15, color: Colors.grey.shade500)),
                            onChanged: (name) {
                              widget.waybillNoChange!(name);
                            },
                            onSubmitted: (name) async {},
                          )),
                    ),
                    Offstage(
                      offstage: (widget.waybillNoEditingController?.text.length ?? 0) <= 0,
                      child: new IconButton(
                        alignment: Alignment.center,
                        padding: const EdgeInsets.all(0),
                        iconSize: isPda ? 22 : 18,
                        color: Colors.grey,
                        icon: Icon(Icons.cancel),
                        onPressed: () {
                          widget.waybillNoClear!();
                        },
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        widget.waybillNoInputFunc!();
                      },
                      child: Container(
                        width: 40,
                        alignment: Alignment.centerLeft,
                        child: Icon(
                          Icons.mode_edit_sharp,
                          size: 22,
                          color: DefaultConfig().configs.GREY_COLOR,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Offstage(
                offstage: true,
                child: Container(
                  margin: EdgeInsets.only(right: 5),
                  child: LocalImageUtil.getImageAsset('isNew', width: 24),
                ),
              )
            ],
          ),
          Container(
            margin: EdgeInsets.only(left: 15, right: 15),
            width: MediaQuery.of(context).size.width - 30,
            height: 1,
            color: DefaultConfig().configs.BG_COLOR,
          ),

          /// 手机号输入框
          Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 120,
                          height: itemHeight,
                          margin: EdgeInsets.only(left: 15),
                          padding: EdgeInsets.fromLTRB(0, isPda ? 10 : 0, 0, isPda ? 10 : 0),
                          child: TextField(
                            keyboardType: TextInputType.name,
                            textInputAction: TextInputAction.send,
                            controller: widget.nameEditingController,
                            focusNode: widget.nameFocusNode_,
                            style: TextStyle(
                              height: isPda ? 2 : 2.4,
                              fontSize: isPda ? 18 : 16,
                              color: Colors.black87,
                            ),
                            decoration: new InputDecoration(
                                border: InputBorder.none,
                                contentPadding: EdgeInsets.all(0),
                                hintText: '姓名',
                                hintStyle: new TextStyle(fontSize: isPda ? 18 : 15, color: Colors.grey.shade500),
                                suffixIcon: (widget.nameEditingController?.text.length ?? 0) > 0
                                    ? IconButton(
                                        alignment: Alignment.center,
                                        padding: const EdgeInsets.all(0),
                                        iconSize: isPda ? 22 : 18,
                                        icon: Icon(Icons.cancel),
                                        onPressed: () {
                                          widget.nameClear!();
                                        },
                                      )
                                    : new Text("")),
                            onChanged: (name) {
                              widget.nameChange!(name);
                            },
                            onSubmitted: (name) async {},
                          ),
                        )
                      ],
                    ),
                    Expanded(
                      child: Container(
                          child: Stack(
                        children: [
                          GestureDetector(
                            onTap: () {
                              widget.mobileInputTap!();
                            },
                            child: isCustomerKeyborad
                                ? Container(
                                    height: itemHeight,
                                    margin: EdgeInsets.only(
                                        left: isPda ? 10.0 : 0, top: isPda ? 0 : 4, bottom: isPda ? 0 : 4),
                                    padding: EdgeInsets.fromLTRB(0, isPda ? 10 : 0, 0, isPda ? 10 : 0),
                                    child: RawKeyboardListener(
                                      focusNode: FocusNode(),
                                      onKey: (RawKeyEvent event) {},
                                      child: AbsorbPointer(
                                        child: TextField(
                                          keyboardType: TextInputType.phone,
                                          textInputAction: TextInputAction.send,
                                          controller: widget.mobileEditingController,
                                          focusNode: widget.mobileFocusNode_,
                                          style: TextStyle(
                                            height: isPda ? 1.2 : 1.8,
                                            fontSize: isPda ? 18 : 16,
                                            color: Colors.black87,
                                          ),
                                          readOnly: true,
                                          showCursor: true,
                                          decoration: InputDecoration(
                                            border: InputBorder.none,
                                            contentPadding: EdgeInsets.all(0),
                                            hintText: '输入收件人手机号',
                                            hintStyle:
                                                new TextStyle(fontSize: isPda ? 18 : 15, color: Colors.grey.shade500),
                                          ),
                                          // onChanged: (phone) {
                                          //   widget.mobileChange!(phone);
                                          // },
                                          // onSubmitted: (name) async {},
                                        ),
                                      ),
                                    ))
                                : Container(
                                    height: itemHeight,
                                    margin: EdgeInsets.only(
                                        left: isPda ? 10.0 : 0, top: isPda ? 0 : 4, bottom: isPda ? 0 : 4),
                                    padding: EdgeInsets.fromLTRB(0, isPda ? 10 : 0, 0, isPda ? 10 : 0),
                                    child: TextField(
                                      keyboardType: TextInputType.phone,
                                      textInputAction: TextInputAction.send,
                                      controller: widget.mobileEditingController,
                                      focusNode: widget.mobileFocusNode_,
                                      style: TextStyle(
                                        height: isPda ? 1.2 : 1.8,
                                        fontSize: isPda ? 18 : 16,
                                        color: Colors.black87,
                                      ),
                                      decoration: InputDecoration(
                                        border: InputBorder.none,
                                        contentPadding: EdgeInsets.all(0),
                                        hintText: '输入收件人手机号',
                                        hintStyle:
                                            new TextStyle(fontSize: isPda ? 18 : 15, color: Colors.grey.shade500),
                                      ),
                                      onChanged: (phone) {
                                        widget.mobileChange!(phone);
                                      },
                                      // onSubmitted: (name) async {},
                                    ),
                                  ),
                          ),
                          widget.showMobileFitButton
                              ? Positioned(
                                  left: 60,
                                  top: 0,
                                  child: InkWell(
                                    onTap: () {
                                      widget.mobileFitFunc!();
                                    },
                                    child: Container(
                                      height: itemHeight,
                                      width: 60,
                                      child: Center(
                                        child: Container(
                                            padding: EdgeInsets.fromLTRB(5, 1, 5, 1),
                                            decoration: BoxDecoration(
                                                border: Border.all(width: 1, color: Theme.of(context).primaryColor),
                                                borderRadius: BorderRadius.all(Radius.circular(4))),
                                            child: Text('查询',
                                                style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 10))),
                                      ),
                                    ),
                                  ),
                                )
                              : Container()
                        ],
                      )),
                    ),
                  ],
                ),
              ),
              Offstage(
                offstage: widget.showNewCustomer,
                child: Container(
                    padding: EdgeInsets.fromLTRB(1, 1, 1, 1),
                    decoration: BoxDecoration(
                        border: Border.all(width: 1, color: Theme.of(context).primaryColor),
                        borderRadius: BorderRadius.all(Radius.circular(4))),
                    child: Text('新用户', style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 10))),
              ),
              (widget.mobileEditingController?.text.length ?? 0) > 0
                  ? IconButton(
                      alignment: Alignment.center,
                      padding: const EdgeInsets.all(0),
                      iconSize: isPda ? 22 : 18,
                      icon: Icon(Icons.cancel,
                          color: (widget.mobileFocusNode_?.hasFocus ?? false) ? Colors.red : Colors.grey),
                      onPressed: () {
                        widget.mobileClear!();
                      },
                    )
                  : new Text(""),
              // Offstage(
              //   offstage: Platform.isIOS,
              //   child: InkWell(
              //     onTap: () {
              //       widget.speechRecogntionFunc!();
              //     },
              //     child: Container(
              //       width: 40,
              //       alignment: Alignment.centerLeft,
              //       child: Icon(
              //         Icons.keyboard_voice,
              //         size: 25,
              //         color: widget.voiceStatus ? Theme.of(context).primaryColor : DefaultConfig().configs.GREY_COLOR,
              //       ),
              //     ),
              //   ),
              // ),
            ],
          ),
          Container(
            margin: EdgeInsets.only(left: 15, right: 15),
            width: MediaQuery.of(context).size.width - 30,
            height: 1,
            color: DefaultConfig().configs.BG_COLOR,
          ),

          /// 格口列表入框
          Offstage(
            offstage: !widget.showGKSelection,
            child: Container(
              margin: EdgeInsets.only(left: 10, right: 10, top: 5, bottom: 5),
              child: Column(
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    margin: EdgeInsets.only(bottom: 5),
                    padding: EdgeInsets.only(bottom: 5),
                    child: Text('请选择格口类型', style: TextStyle(color: Colors.black)),
                  ),
                  Column(
                    children: buildGKList(context, widget),
                  )
                ],
              ),
            ),
          ),
          Offstage(
              offstage: widget.isDelivery,
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Row(
                          children: [
                            Container(
                              width: 36,
                              height: 36,
                              padding: EdgeInsets.fromLTRB(8, 3, 3, 3),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(2),
                                child: LocalImageUtil.getImageAsset('code'),
                              ),
                              margin: EdgeInsets.only(left: 10, right: 45),
                            ),
                            Container(width: isPda ? 60 : 45),
                            Row(
                              children: [
                                Container(
                                  child: InkWell(
                                    child: Text(widget.checkCode,
                                        style:
                                            TextStyle(fontSize: 18, color: Colors.black, fontWeight: FontWeight.bold)),
                                    onTap: () {
                                      widget.clickCheckCode!();
                                    },
                                  ),
                                  margin: EdgeInsets.only(top: 5),
                                )
                              ],
                            ),
                            Expanded(
                                child: InkWell(
                              onTap: () {
                                widget.goCheckCodePage!();
                              },
                              child: Container(
                                padding: EdgeInsets.only(left: 10.0, top: 5.0, right: 15),
                                height: 48,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    InkWell(
                                      onTap: () {
                                        widget.refreshCheckCode!();
                                      },
                                      child: Icon(
                                        Icons.cached,
                                        size: 28.0,
                                        color: Color(0xFF999999),
                                      ),
                                    ),
                                    SizedBox(
                                      width: 20,
                                    ),
                                    InkWell(
                                      onTap: () {},
                                      child: Icon(
                                        Icons.arrow_forward_ios_sharp,
                                        size: 18.0,
                                        color: Color(0xFF999999),
                                      ),
                                    )
                                  ],
                                ),
                              ),
                            )),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              )),
          Offstage(
            offstage: widget.appendWidget == null,
            child: widget.appendWidget,
          ),
          SizedBox(
            height: 15,
          ),
          widget.isBlind && !widget.addInCabinet
              ? Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        child: Container(
                          height: isPda ? 55 : 45,
                          width: 90,
                          margin: EdgeInsets.only(right: 10, left: 10),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            borderRadius: BorderRadius.all(Radius.circular(40)),
                          ),
                          child: Text(
                            "重置",
                            style: TextStyle(
                              fontSize: isPda ? 18 : 15,
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        onTap: () {
                          widget.resetScan!();
                        },
                      ),
                    )
                  ],
                )
              : Row(
                  children: [
                    InkWell(
                      child: Container(
                        height: isPda ? 55 : 45,
                        width: 90,
                        margin: EdgeInsets.only(left: 10),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          borderRadius:
                              BorderRadius.only(topLeft: Radius.circular(40), bottomLeft: Radius.circular(40)),
                        ),
                        child: Text(
                          "重置",
                          style: TextStyle(
                            fontSize: isPda ? 18 : 15,
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      onTap: () {
                        widget.resetScan!();
                      },
                    ),
                    Padding(padding: EdgeInsets.only(left: 1.5)),
                    Expanded(
                        child: InkWell(
                      onTap: () {
                        widget.confirmNextStep!();
                      },
                      child: Container(
                        margin: EdgeInsets.only(right: 10),
                        height: isPda ? 55 : 45,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: widget.buttonStatus
                              ? Theme.of(context).primaryColor
                              : DefaultConfig().configs.PRIMARY_COLOR_LIGHT,
                          borderRadius:
                              BorderRadius.only(topRight: Radius.circular(40), bottomRight: Radius.circular(40)),
                        ),
                        child: Text(
                          widget.confirmButtonLabel,
                          style: TextStyle(
                            fontSize: isPda ? 18 : 15,
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ))
                  ],
                ),
        ],
      ),
    );
  }
}

buildGKList(BuildContext context, widget) {
  double width = (MediaQuery.of(context).size.width - 45) / 5;
  List<Widget> widgets = <Widget>[];
  if (widget.boxAvaliableList.length != 7) {
    return widgets;
  }
  Map<String, dynamic> microItem = widget.boxAvaliableList.firstWhere((item) => item['type'] == 6);
  Map<String, dynamic> miniItem = widget.boxAvaliableList.firstWhere((item) => item['type'] == 5);
  Map<String, dynamic> smItem = widget.boxAvaliableList.firstWhere((item) => item['type'] == 4);
  Map<String, dynamic> midItem = widget.boxAvaliableList.firstWhere((item) => item['type'] == 3);
  Map<String, dynamic> bigItem = widget.boxAvaliableList.firstWhere((item) => item['type'] == 2);
  Map<String, dynamic> bigerItem = widget.boxAvaliableList.firstWhere((item) => item['type'] == 1);
  Map<String, dynamic> superItem = widget.boxAvaliableList.firstWhere((item) => item['type'] == 0);
  widgets.add(SingleChildScrollView(
    scrollDirection: Axis.horizontal,
    child: Row(
      children: [
        Offstage(
            offstage: microItem['availableNum'] == 0,
            child: Row(
              children: [
                Container(
                  width: width,
                  child: buildBox1(context, widget, microItem),
                ),
                Padding(padding: EdgeInsets.only(left: 4)),
              ],
            )),
        Container(width: width, child: buildBox1(context, widget, miniItem)),
        Padding(padding: EdgeInsets.only(left: 4)),
        Container(width: width, child: buildBox1(context, widget, smItem)),
        Padding(padding: EdgeInsets.only(left: 4)),
        Container(width: width, child: buildBox1(context, widget, midItem)),
        Padding(padding: EdgeInsets.only(left: 4)),
        Container(width: width, child: buildBox1(context, widget, bigItem)),
        Padding(padding: EdgeInsets.only(left: 4)),
        Container(width: width, child: buildBox1(context, widget, bigerItem)),
        Offstage(
            offstage: superItem['availableNum'] == 0,
            child: Row(
              children: [
                Padding(padding: EdgeInsets.only(left: 4)),
                Container(
                  width: width,
                  child: buildBox1(context, widget, superItem),
                ),
              ],
            )),
      ],
    ),
  ));
  return widgets;
}

buildBox1(BuildContext context, widget, item) => InkWell(
      onTap: () {
        print(item);
        if ((item['availableNum'] ?? 0) > 0) {
          widget.chooseGK(item);
        }
      },
      child: Container(
          padding: EdgeInsets.only(top: 5, bottom: 5),
          decoration: BoxDecoration(
              color: widget.boxType == item['type'] ? Color(0xFFFFF0EA) : Color(0xFFF5F5F5),
              borderRadius: BorderRadius.all(Radius.circular(4))),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('${item['availableNum']}',
                      style: TextStyle(
                          fontSize: 25,
                          color: (item['availableNum'] ?? 0) > 0
                              ? Theme.of(context).primaryColor
                              : DefaultConfig().configs.GREY_COLOR))
                ],
              ),
              Text(DefaultConfig().configs.CABINET_BOX_TYPE[item['type']] ?? '',
                  style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      color: (item['availableNum'] ?? 0) > 0 ? Colors.black : DefaultConfig().configs.GREY_COLOR)),
              Offstage(
                offstage: !widget.showPrice,
                child: Text('${(item['price'] ?? 0) / 1000}元',
                    style: TextStyle(
                        fontSize: 10,
                        color: item['availableNum'] > 0 ? Colors.black : DefaultConfig().configs.GREY_COLOR)),
              )
            ],
          )),
    );
