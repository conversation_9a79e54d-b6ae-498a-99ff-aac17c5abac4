import 'dart:async';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CabinetDao.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/dao/ScanDao.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/scan_item_entity.dart';
import 'package:cabinet_flutter_app/common/mixins/ScanMixin.dart';
import 'package:cabinet_flutter_app/common/page_life_cycle.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CabinetBoxUtil.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/utils/SoundUtils.dart';
import 'package:cabinet_flutter_app/page/scan/widget/CustomerScanForm.dart';
import 'package:cabinet_flutter_app/widget/CountDownWidget.dart';
import 'package:cabinet_flutter_app/widget/keyborad/MyKeyboard.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:fluttertoast/fluttertoast.dart';

class EntryScan extends StatefulWidget {
  final String cabinetLocationCode;
  final String hostIndex;

  EntryScan(this.cabinetLocationCode, this.hostIndex, {Key? key}) : super(key: key);

  @override
  _EntryScanState createState() => _EntryScanState();
}

class _EntryScanState extends State<EntryScan>
    with TickerProviderStateMixin, WidgetsBindingObserver, PageLifeCycle<EntryScan>, ScanMixin<EntryScan> {
  Timer? boxStatusTimer;
  CabinetEntity cabinetEntity = new CabinetEntity();

  Map<dynamic, dynamic> brandMap = {};

  bool isCheckBox = true;

  @override
  void initState() {
    // 初始化生命周期监听 用于解决扫码库在生命周期变动时候被杀死
    WidgetsBinding.instance.addObserver(this);
    initData();
    initListener();
    getBrandList();
    getCabinetInfo();
    checkFuc(height: 320);
    initScan(320);
    super.initState();
  }

  initData() {
    isBlind = false;
    cabinetLocationCode = widget.cabinetLocationCode;
    showScanDb = buildNewScan();
  }

  @override
  Future<void> didChangeDependencies() async {
    super.didChangeDependencies();
    initFunc();
  }

  getCabinetInfo() async {
    var res = await CabinetDao.getCabinetInfo(widget.cabinetLocationCode);
    if (res != null && res.result) {
      cabinetEntity = CabinetEntity.fromJson(res.data);
      getCabinetUsableBox(widget.cabinetLocationCode);
    }
    setState(() {});
  }

  /// 获取点位可用盒子汇总
  getCabinetUsableBox(code) async {
    DataResult res = await CabinetDao.getCabinetUsableBox(code);
    if (res.result) {
      Map<String, dynamic> countMap = {
        'microCount': res.data['microCount'],
        'miniCount': res.data['miniCount'],
        'smallCount': res.data['smallCount'],
        'mediumCount': res.data['mediumCount'],
        'largeCount': res.data['largeCount'],
        'hugeCount': res.data['hugeCount'],
        'superCount': res.data['superCount'],
      };
      boxMapPriceData = CabinetBoxUtil.getCabinetBoxAvailableCount(cabinetEntity.dispatchJson, countMap, isDefault: false);
      boxData = boxMapPriceData['DEFAULT']!;
    }
    if (mounted) {
      setState(() {});
    }
  }

  // 路由显示触发
  @override
  void onShow() {
    this.initScan(320);
    this.getBrandList();
    this.startSpot();
  }

  @override
  void onHide() {
    scanSubscription_?.cancel();
    boxStatusTimer?.cancel();
    scanViewController?.stopSpot();
    boxStatusTimer?.cancel();
    timer?.cancel();
    timer_?.cancel();
    this.stopSpot();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    ///通过state判断App前后台切换
    switch (state) {
      case AppLifecycleState.hidden:
      case AppLifecycleState.inactive: // 处于这种状态的应用程序应该假设它们可能在任何时候暂停。
        break;
      case AppLifecycleState.resumed: // 应用程序可见，前台
        initScan(320, isContinue: true);
        break;
      case AppLifecycleState.paused: // 应用程序不可见，后台
        scanSubscription_?.cancel();
        this.stopSpot();
        setState(() {
          this.scanView = null;
        });
        break;
      case AppLifecycleState.detached: // 申请将暂时暂停
        break;
    }
  }

  @override
  void dispose() {
    super.dispose();
    scanSubscription_?.cancel();
    timer_?.cancel();
    timer?.cancel();
    boxStatusTimer?.cancel();
    isChooseBrandCode = false;
    scanViewController?.stopSpot();
    WidgetsBinding.instance.removeObserver(this);
    countDownKey3.currentState?.dispose();
  }

  @override
  void deactivate() {
    super.deactivate();
  }

  @override
  resetTimer({bool isResetBoxCount = false}) {
    boxStatusTimer?.cancel();
    if (isResetBoxCount) {
      getCabinetUsableBox(cabinetLocationCode);
    }
  }

  /// 输入框 扫描框
  _buildScanOrInputArea() {
    return buildRKScan();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        bool canBack = true;

        if (isInProcess || isChooseBrandCode) {
          isInProcess = false;
          isChooseBrandCode = false;
          canBack = false;
        }
        return canBack;
      },
      child: StoreBuilder<AppState>(
        builder: (context, store) {
          return Scaffold(
            key: scaffoldKey_,
            backgroundColor: Colors.black,
            body: _buildScanOrInputArea(),
          );
        },
      ),
    );
  }

  buildScanView() {
    if (this.scanView != null) {
      return this.scanView;
    }
    return Container();
  }

  /// 扫码界面
  buildRKScan() {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Stack(
        children: <Widget>[
          /// 扫码界面
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: MediaQuery.of(context).size.height,
            child: buildScanView(),
          ),

          /// 标题部分
          buildTitle(),

          // /// 默认扫描框
          buildScanInputModel(),
        ],
      ),
    );
  }

  /// 标题部分
  buildTitle() {
    double _navHeight = 55.0 + paddingSizeTop(context);
    return Positioned(
      top: 0,
      child: new Container(
        width: MediaQuery.of(context).size.width,
        height: _navHeight,
        padding: EdgeInsets.only(top: paddingSizeTop(context, isPlatform: true)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    width: 40.0,
                    padding: EdgeInsets.only(left: 12),
                    alignment: Alignment.centerLeft,
                    child: Icon(
                      Icons.arrow_back,
                      size: 25.0,
                      color: Colors.white,
                    ),
                  ),
                ),
                // InkWell(   //下周开放
                //   onTap: () {
                //     NavigatorUtils.goDeliveryPage(
                //       context,
                //       widget.cabinetLocationCode,
                //       isDelivery: false,
                //     );
                //   },
                //   child: Container(
                //     padding: EdgeInsets.symmetric(horizontal: 8, vertical: 5),
                //     margin: EdgeInsets.only(left: 8),
                //     decoration: BoxDecoration(
                //       border: Border.all(color: Colors.white, width: 1),
                //       borderRadius: BorderRadius.circular(4),
                //     ),
                //     child: Text(
                //       '柜机模型',
                //       style: TextStyle(color: Colors.white, fontSize: 14),
                //     ),
                //   ),
                // ),
              ],
            ),
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  SizedBox(
                    width: 30,
                  ),
                  Text(
                    '派件入柜',
                    style: TextStyle(fontSize: 20.0, color: Colors.white),
                  ),
                  InkWell(
                    onTap: () async {
                      setFlash();
                    },
                    child: Padding(
                        padding: EdgeInsets.all(10.0),
                        child: LocalImageUtil.getImageAsset(checkFlash ? 'flashOpen' : 'flashClose', width: 20, height: 20)),
                  ),
                  Padding(
                    padding: EdgeInsets.only(right: 10.0),
                  )
                ],
              ),
            ),
            new CountDownWidget(
              countDownKey3,
              isBlack: false,
              onTimerFinish: () {
                ScanDao.clearScan('RK');
                NavigatorUtils.replaceHome(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  resetCountDown() {
    countDownKey3.currentState?.resetCountDown();
  }

  chooseGK(item) {
    setState(() {
      boxType = item['type'];
      showScanDb.boxType = item['type'];
      lastCabinetBoxId = '';

      /// 重置追加入柜状态
      showScanDb.cabinetId = null;
      showScanDb.cabinetBoxId = null;
      showScanDb.addInCabinet = false;
      cabinetId = null;
      cabinetBoxId = null;
    });
  }

  /// 品牌选择
  selectBrand({String? brand, bool isForce = false}) {
    SoundUtils.audioPushFn(SoundUtils.CHOOSE_COMPANY);
    CommonUtils.showBottomBrandSelectModal(context, brandMap, (item) {
      if (item != '') {
        SoundUtils.audioPushFn(SoundUtils.BRAND_SOUND[item]!);
        brandCode = item;
        showScanDb.brandCode = item;
        waybillRemoteGuss(isScan: false);
      } else {
        if (brand != null && brand != '') {
          brandCode = brand;
          showScanDb.brandCode = brand;
        }
      }
      this.setBoxPrice(brandCode);
      setState(() {});
    }, bindCompany: brand, isForce: isForce);
  }

  closeCabinetBox() async {
    resetCountDown();
    if (showScanDb.cabinetBoxId != null) {
      // var res = await CabinetDao.cabinetBoxCloseDoor(showScanDb.cabinetBoxId!);
      // if (res.result) {
      isCheckBox = false;
      boxStatusTimer?.cancel();
      Navigator.pop(context);
      resetCountDown();
      resetScan(isResetBoxCount: true);
      // }
    }
  }

  buildGKList() {
    List<Widget> widgets = <Widget>[];
    if (boxData.length != 5) {
      return widgets;
    }
    Map<String, dynamic> miniItem = boxData.firstWhere((item) => item['type'] == 5);
    Map<String, dynamic> smItem = boxData.firstWhere((item) => item['type'] == 4);
    Map<String, dynamic> midItem = boxData.firstWhere((item) => item['type'] == 3);
    Map<String, dynamic> bigItem = boxData.firstWhere((item) => item['type'] == 2);
    Map<String, dynamic> bigerItem = boxData.firstWhere((item) => item['type'] == 1);
    widgets.add(
      Row(
        children: [
          Expanded(child: buildBox1(miniItem)),
          Padding(padding: EdgeInsets.only(left: 4)),
          Expanded(child: buildBox1(smItem)),
          Padding(padding: EdgeInsets.only(left: 4)),
          Expanded(child: buildBox1(midItem)),
          Padding(padding: EdgeInsets.only(left: 4)),
          Expanded(child: buildBox1(bigItem)),
          Padding(padding: EdgeInsets.only(left: 4)),
          Expanded(child: buildBox1(bigerItem))
        ],
      ),
    );
    return widgets;
  }

  buildBox1(item) => InkWell(
        onTap: () {
          if (item['availableNum'] > 0) {
            chooseGK(item);
          } else {
            // Fluttertoast.showToast(msg: '无该类型格口可用');
            // SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_READ);
          }
        },
        child: Container(
            padding: EdgeInsets.only(top: 10, bottom: 10),
            decoration: BoxDecoration(
                color: boxType == item['type'] ? Color(0xFFFFF0EA) : Color(0xFFF5F5F5),
                borderRadius: BorderRadius.all(Radius.circular(4.0))),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text('${item['availableNum']}',
                        style: TextStyle(
                            fontSize: 25,
                            color: item['availableNum'] > 0 ? Theme.of(context).primaryColor : DefaultConfig().configs.GREY_COLOR))
                  ],
                ),
                Text(DefaultConfig().configs.CABINET_BOX_TYPE[item['type']] ?? '',
                    style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                        color: item['availableNum'] > 0 ? Colors.black : DefaultConfig().configs.GREY_COLOR)),
                Text('${item['price'] / 1000}元',
                    style: TextStyle(fontSize: 10, color: item['availableNum'] > 0 ? Colors.black : DefaultConfig().configs.GREY_COLOR))
              ],
            )),
      );

  getButtonStatus() {
    bool status = boxType > -1 && showScanDb.receiverMobile != '' && showScanDb.receiverMobile.length >= 11 && showScanDb.waybillNo != '';
    return status;
  }

  /// 默认扫描框
  buildScanInputModel() {
    return Stack(
      children: [
        Positioned(
            top: 110,
            right: 22,
            child: Column(
              children: [
                // ...buildDeliveryWaybill(),
                ...buildBDP(isLabel:true),
                ...buildAddCabinet(isLabel:true),
                ...buildOpenOCR(isLabel:true),
                ...buildSingleCustomerNotice(isLabel:true),
                ...buildBatchFastInto(isLabel:true)
              ],
            )),
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Column(
            children: [
              Container(
                height: scanInputModelHeight,
                // padding: EdgeInsets.only(bottom: 15),
                decoration: BoxDecoration(
                    color: Colors.white, borderRadius: BorderRadius.only(topLeft: Radius.circular(8), topRight: Radius.circular(8))),
                child: CustomerScanForm(
                  brandCode: brandCode,
                  selectBrand: () {
                    if (!hasChangeBrand) {
                      Fluttertoast.showToast(msg: '该点位不允许修改快递品牌');
                      return;
                    }
                    selectBrand(brand: brandCode);
                  },
                  waybillNoEditingController: waybillNoEditingController,
                  waybillNoFocusNode_: focusNode_,
                  waybillNoChange: (name) {
                    setState(() {
                      showScanDb.waybillNo = name;
                    });
                  },
                  waybillNoClear: () => throttleUtil.throttle(() {
                    resetScan();
                  }),
                  waybillNoInputFunc: () {
                    stopSpot();
                    CommonUtils.inputWaybillNo(context, showScanDb.waybillNo, (waybillNo) {
                      if (waybillNo != '') {
                        isInProcess = false;
                        addScan(waybillNo, isScan: false);
                      } else {
                        startSpot();
                      }
                    });
                  },
                  nameFocusNode_: nameFocusNode_,
                  nameEditingController: nameEditingController,
                  nameChange: (name) {
                    showScanDb.receiverName = name;
                  },
                  nameClear: () {
                    setState(() {
                      nameEditingController.value = TextEditingValue(text: '');
                      showScanDb.receiverName = '';
                    });
                  },
                  mobileFocusNode_: mobileFocusNode_,
                  mobileEditingController: mobileEditingController,
                  mobileInputTap: () {
                    showKeyBoard(TextInputType.number);
                    FocusScope.of(context).requestFocus(mobileFocusNode_);
                  },
                  mobileChange: (phone) {
                    checkMobileInput(phone);
                  },
                  mobileClear: () {
                    clearPhoneController();
                  },
                  showMobileFitButton: showMobileGussByLast4Button,
                  mobileFitFunc: () => throttleUtil.throttle(() {
                    mobileGussByLast4();
                  }),
                  showNewCustomer: showScanDb.newCustomer == null || showScanDb.newCustomer == 0,
                  speechRecogntionFunc: () {
                    setState(() {
                      voiceStatus = true;
                      openVoiceCountDown();
                      Fluttertoast.showToast(msg: '请读出手机号码');
                    });
                  },
                  voiceStatus: voiceStatus,
                  resetScan: () => throttleUtil.throttle(() {
                    resetScan();
                  }),
                  confirmNextStep: () => throttleUtil.throttle(() {
                    confirmNextStep(isInbound: true, flag: 7);
                  }),
                  boxAvaliableList: boxData,
                  buttonStatus: getButtonStatus(),
                  chooseGK: (item) {
                    chooseGK(item);
                  },
                  boxType: boxType,
                  showPrice: true,
                ),
              ),
              MyKeyboard(showKeyboard, onKeyDown,
                  textInputType: textInputType,
                  textInputAction: mobileEditingController.text.length == 4 ? TextInputAction.search : TextInputAction.done)
            ],
          ),
        )
      ],
    );
  }

  openBox() async {
    resetCountDown();
    LoadingUtil(
      status: '正在开门...',
    ).show(context);
    showScanDb.cabinetLocationCode = cabinetLocationCode;
    showScanDb.platform = platform;
    showScanDb.virtualNumber = gussEntity.virtualNumber.toString();
    showScanDb.secretWaybill = gussEntity.hasSecret ? '1' : '0';
    showScanDb.hasSubstituteSms = gussEntity.hasSubstituteSms ? '1' : '0';
    showScanDb.boxType = boxType;
    if (imagePath != '') {
      var savePhoto =
          await CheckUtils.checkPhotoUpload(imagePath, showScanDb.waybillNo, showScanDb.brandCode, cabinetEntity.id!, 'inbound');
      showScanDb.inboundImageUrl = savePhoto;
    }
    bool isAddInCabinet = showScanDb.addInCabinet!;
    showScanDb.deliveryWaybill = isDelivery ? 1 : 0;
    var res = await CourierDao.courierOrderWaybillCreate(
        showScanDb, lastCabinetBoxId, isBDP ? 1 : 0, gussEntity.yzSopAccountId, gussEntity.yzChannel,
        hostIndex: widget.hostIndex);
    LoadingUtil.dismiss(context);
    if (res != null && res.result) {
      resetRes();
      ScanItemEntity? scan = ScanItemEntity.fromJson(res.data);
      showScanDb = scan;
      scanPlugin.playSound("${res.data['cabinetBoxLabel']}号格口");
      scan.type = 'RK';
      if (!isAddInCabinet) {
        lastCabinetBoxId = scan.cabinetBoxId!;
      }
      showBoxOpen(context, scan, (item) {});
    } else if (res.data != '操作太频繁' && !res.result) {
      print('开门失败${res.data}');
      Fluttertoast.showToast(msg: '存件失败');
      SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
    }
  }

  /// 格口打开
  showBoxOpen(BuildContext context, ScanItemEntity showScanDb, onPressed(item)) {
    int countTime = 60;
    String cancelReason = '投错包裹';
    return showModalBottomSheet(
      context: context,
      isDismissible: false,
      enableDrag: false,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
        topLeft: Radius.circular(10.0),
        topRight: Radius.circular(10.0),
      )),
      builder: (BuildContext _context) {
        return StatefulBuilder(
          builder: (_context, state) {
            timer?.cancel();
            timer = Timer.periodic(Duration(seconds: 1), (timer) {
              if (countTime < 1) {
                timer.cancel();
              } else {
                state(() {
                  countTime--;
                });
              }
            });

            return Stack(
              children: [
                Container(
                  height: 30.0,
                  width: double.infinity,
                  color: Colors.black54,
                ),
                Container(
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      )),
                ),
                Container(
                  height: 550,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  LocalImageUtil.getImageAsset('boxOpen', width: MediaQuery.of(context).size.width * 0.20),
                                  Padding(padding: EdgeInsets.only(right: 20)),
                                  Container(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        RichText(
                                            text: TextSpan(children: <TextSpan>[
                                          TextSpan(
                                            text: '${showScanDb.cabinetBoxLabel ?? ''}',
                                            style: TextStyle(
                                              color: Theme.of(context).primaryColor,
                                              fontSize: 48,
                                              fontWeight: FontWeight.w400,
                                            ),
                                          ),
                                          TextSpan(
                                            text: ' 号',
                                            style: TextStyle(color: Colors.black, fontSize: 18),
                                          ),
                                        ])),
                                        Padding(padding: EdgeInsets.only(bottom: 10)),
                                        Offstage(
                                          offstage: hasFeedBack,
                                          child: Text('格口已开', style: TextStyle(color: Color(0xFF999999), fontSize: 18, wordSpacing: 4)),
                                        ),
                                      ],
                                    ),
                                  )
                                ],
                              ),
                            ),
                            Padding(padding: EdgeInsets.only(bottom: 30)),
                            Offstage(
                              offstage: hasFeedBack,
                              child: Text('放入包裹后请关门', style: TextStyle(color: Color(0xFF999999), fontSize: 18, wordSpacing: 4)),
                            ),
                            Padding(padding: EdgeInsets.only(bottom: 30)),
                            OutlinedButton(
                              child: Text("柜门未开,再次开门",
                                  style: TextStyle(fontSize: AppConstant.smallTextSize, color: Theme.of(context).primaryColor)),
                              onPressed: () => throttleUtil.throttle(() async {
                                await CourierDao.courierBoxOpenCheck(showScanDb.cabinetBoxId, hostIndex: widget.hostIndex);
                              }),
                              style: ButtonStyle(
                                  minimumSize: MaterialStateProperty.all(Size(100, 40)),
                                  shape: MaterialStateProperty.all(StadiumBorder()),
                                  side: MaterialStateProperty.all(BorderSide(color: Theme.of(context).primaryColor))),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.fromLTRB(10, 0, 15, 10),
                        child: Row(
                          children: [
                            countTime < 1
                                ? Container()
                                : Expanded(
                                    flex: 1,
                                    child: cancelCabinet(showScanDb, countTime, cancelReason),
                                  ),
                            Expanded(
                                flex: 2,
                                child: InkWell(
                                  onTap: () => throttleUtil.throttle(() {
                                    if (onPressed != null) {
                                      closeCabinetBox();
                                    }
                                  }),
                                  child: Container(
                                    height: 48,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).primaryColor,
                                      border: Border.all(width: 1.0, color: Theme.of(context).primaryColor),
                                      borderRadius: countTime > 0
                                          ? BorderRadius.only(topRight: Radius.circular(40), bottomRight: Radius.circular(40))
                                          : BorderRadius.all(Radius.circular(40)),
                                    ),
                                    child: Text('我已存件', style: TextStyle(color: DefaultConfig().configs.WHITE_COLOR)),
                                  ),
                                )),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 10,
                      )
                    ],
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  cancelCabinet(ScanItemEntity showScanDb, int countTime, String cancelReason) {
    return Offstage(
      offstage: countTime < 1,
      child: Container(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            InkWell(
              child: Container(
                height: 48,
                width: 110,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: DefaultConfig().configs.WHITE_COLOR,
                  border: Border.all(width: 1.0, color: Theme.of(context).primaryColor),
                  borderRadius: BorderRadius.only(topLeft: Radius.circular(40), bottomLeft: Radius.circular(40)),
                ),
                child: Text(
                  '取消入柜${countTime}s',
                  style: TextStyle(color: DefaultConfig().configs.BLACK_COLOR),
                ),
              ),
              onTap: () {
                cancelCabinetInbound_(showScanDb.cabinetLocationCode, showScanDb.id!, cancelReason);
              },
            ),
            // Container(
            //   height: 42,
            //   child: OutlinedButton(
            //     onPressed: () {
            //       /// 取消入柜
            //       cancelCabinetInbound_(showScanDb.cabinetLocationCode, showScanDb.id!, cancelReason);
            //     },
            //     child: Text(
            //       '取消入柜${countTime}s',
            //       style: TextStyle(color: DefaultConfig().configs.BLACK_COLOR),
            //     ),
            //     style: ButtonStyle(
            //         minimumSize: MaterialStateProperty.all(Size(110, 40)),
            //         shape: MaterialStateProperty.all(StadiumBorder()),
            //         side: MaterialStateProperty.all(BorderSide(color: Theme.of(context).primaryColor, width: 2))),
            //   ),
            // )
          ],
        ),
      ),
    );
  }

  /// 取消入柜
  cancelCabinetInbound_(String cabinetLocationCode, String id, String cancelReason) async {
    String? reason = await CommonUtils.radioSelect(context, title: '取消入库', content: '请选择取消原因', selectValue: cancelReason);
    if (reason != null) {
      var _res = await CourierDao.courierOrderCancel(cabinetLocationCode, id, cancelReason: reason, hostIndex: widget.hostIndex);
      if (_res != null && _res.result) {
        timer?.cancel();
        boxStatusTimer?.cancel();
        isCheckBox = false;
        resetCountDown();
        resetScan(isResetBoxCount: true);
        Fluttertoast.showToast(msg: '取消入柜成功');
        Navigator.pop(context);
      }
    }
  }
}
