import 'dart:async';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CabinetDao.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/entitys/shop_cabinet_location_entity.dart';
import 'package:cabinet_flutter_app/common/mixins/ScanMixin.dart';
import 'package:cabinet_flutter_app/common/page_life_cycle.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/SoundUtils.dart';
import 'package:cabinet_flutter_app/page/scan/widget/CustomerScanForm.dart';
import 'package:cabinet_flutter_app/widget/keyborad/MyKeyboard.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:fluttertoast/fluttertoast.dart';

class EntryScanCabinet extends StatefulWidget {
  final String cabinetLocationCode;
  final CabinetItemEntity cabinetItem; //柜子名称

  final bool isDelivery; // 是否送货上门
  final CabinetBoxItem boxItem;
  final String hostIndex;

  EntryScanCabinet(this.cabinetItem, this.cabinetLocationCode, this.boxItem, this.isDelivery, this.hostIndex, {key})
      : super(key: key);

  @override
  _EntryScanCabinetState createState() => _EntryScanCabinetState();
}

class _EntryScanCabinetState extends State<EntryScanCabinet>
    with TickerProviderStateMixin, WidgetsBindingObserver, PageLifeCycle<EntryScanCabinet>, ScanMixin {
  @override
  void initState() {
    super.initState();
    // 初始化生命周期监听 用于解决扫码库在生命周期变动时候被杀死
    WidgetsBinding.instance.addObserver(this);
    initData();
    initListener();
    checkFuc(height: 310);
    getBrandList();
    initScan(320);
  }

  initData() {
    isBlind = false;
    isDelivery = widget.isDelivery;
    cabinetName = widget.cabinetItem.name ?? '';
    currentCabinetId = widget.cabinetItem.id;
    cabinetBoxId = widget.boxItem.id;
    currentBoxItem = widget.boxItem;
    boxType = widget.boxItem.type!;
    cabinetLocationCode = widget.cabinetLocationCode;
    lastCabinetBoxId = cabinetBoxId!;
    showScanDb = buildNewScan();
    getCabinetUsableBox(cabinetLocationCode);
  }

  List boxAvaliableList = [];

  getCabinetUsableBox(String cabinetLocationCode) async {
    DataResult res = await CabinetDao.getCabinetUsableBox(cabinetLocationCode);
    if (res.result) {
      boxAvaliableList = [
        {'type': 0, 'title': '极大', 'availableNum': res.data['superCount']},
        {'type': 1, 'title': '超大', 'availableNum': res.data['hugeCount']},
        {'type': 2, 'title': '大格', 'availableNum': res.data['largeCount']},
        {'type': 3, 'title': '中格', 'availableNum': res.data['mediumCount']},
        {'type': 4, 'title': '小格', 'availableNum': res.data['smallCount']},
        {'type': 5, 'title': '超小', 'availableNum': res.data['miniCount']},
        {'type': 6, 'title': '极小', 'availableNum': res.data['microCount']},
      ];
    }
    if (mounted) {
      setState(() {});
    }
  }

  // 路由显示触发
  @override
  void onShow() {
    this.initScan(320);
    this.getBrandList();
    this.startSpot();
  }

  @override
  void onHide() {
    scanSubscription_?.cancel();
    scanViewController?.stopSpot();
    timer_?.cancel();
    this.stopSpot();
  }

  @override
  Future<void> didChangeDependencies() async {
    super.didChangeDependencies();
    initFunc();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    ///通过state判断App前后台切换
    switch (state) {
      case AppLifecycleState.hidden: // 处于这种状态的应用程序应该假设它们可能在任何时候暂停。
        break;
      case AppLifecycleState.resumed: // 应用程序可见，前台
        initScan(320, isContinue: true);
        break;
      case AppLifecycleState.paused: // 应用程序不可见，后台
        scanSubscription_?.cancel();
        this.stopSpot();
        setState(() {
          this.scanView = null;
        });
        break;
      case AppLifecycleState.detached: // 申请将暂时暂停
        break;
      case AppLifecycleState.inactive:
      // TODO: Handle this case.
    }
  }

  @override
  void dispose() {
    super.dispose();
    scanSubscription_?.cancel();
    timer_?.cancel();
    countDownTimer?.cancel();
    isChooseBrandCode = false;
    scanViewController?.stopSpot();
    WidgetsBinding.instance.removeObserver(this);
    controllerWidth?.dispose();
    controllerHeight?.dispose();
    countDownKey3.currentState?.dispose();
  }

  @override
  void deactivate() {
    super.deactivate();
  }

  /// 输入框 扫描框
  _buildScanOrInputArea() {
    return buildRKScan();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        bool canBack = true;
        if (isInProcess || isChooseBrandCode) {
          isInProcess = false;
          isChooseBrandCode = false;
          canBack = false;
        }
        return canBack;
      },
      child: StoreBuilder<AppState>(
        builder: (context, store) {
          return Scaffold(
            key: scaffoldKey_,
            backgroundColor: Colors.black,
            body: _buildScanOrInputArea(),
          );
        },
      ),
    );
  }

  buildScanView() {
    if (this.scanView != null) {
      return this.scanView;
    }
    return Container();
  }

  /// 扫码界面
  buildRKScan() {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Stack(
        children: <Widget>[
          /// 扫码界面
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: MediaQuery.of(context).size.height,
            child: buildScanView(),
          ),

          /// 标题部分
          buildTitle(),

          /// 格口信息部分
          buildCabinet(),

          // /// 默认扫描框
          buildScanInputModel(),
        ],
      ),
    );
  }

  /// 标题部分
  buildTitle() {
    double _navHeight = 60.0 + paddingSizeTop(context);
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: new Container(
        width: MediaQuery.of(context).size.width,
        height: _navHeight,
        padding: EdgeInsets.only(top: paddingSizeTop(context, isPlatform: true)),
        child: new Container(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              InkWell(
                onTap: () {
                  Navigator.of(context).pop(true);
                },
                child: Container(
                  width: 60.0,
                  padding: EdgeInsets.only(left: 12),
                  alignment: Alignment.centerLeft,
                  child: Icon(
                    Icons.arrow_back,
                    size: 25.0,
                    color: Colors.white,
                  ),
                ),
              ),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Text(
                      '派件入柜${isDelivery ? '-仅入库' : ''}',
                      style: TextStyle(fontSize: 20.0, color: Colors.white),
                    ),
                    InkWell(
                      onTap: () async {
                        setFlash();
                      },
                      child: Padding(
                          padding: EdgeInsets.all(10.0),
                          child: LocalImageUtil.getImageAsset(checkFlash ? 'flashOpen' : 'flashClose',
                              width: 20, height: 20)),
                    ),
                    Padding(
                      padding: EdgeInsets.only(right: 10.0),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 格口信息
  buildCabinet() {
    double _navHeight = 100.0;
    return Positioned(
      top: 60,
      left: 0,
      right: 0,
      child: new Container(
        width: MediaQuery.of(context).size.width,
        height: _navHeight,
        color: Colors.transparent,
        margin: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
        child: new Container(
          margin: EdgeInsets.only(left: 22),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text('柜机号：$cabinetName',
                      style: TextStyle(color: Colors.white, fontSize: 17, fontWeight: FontWeight.w700)),
                  InkWell(
                    onTap: () => throttleUtil.throttle(() async {
                      await getUseableCabinetBoxList(isShowDialog: false, hostIndex: widget.hostIndex);
                      await checkCabinet(hostIndex: widget.hostIndex);
                    }),
                    child: Container(
                      width: leadingWidth,
                      alignment: Alignment.center,
                      child: Text('选择柜机',
                          style: TextStyle(
                              color: Theme.of(context).primaryColor, fontSize: 17, fontWeight: FontWeight.w700)),
                    ),
                  )
                ],
              ),
              Row(
                children: [
                  RichText(
                      text: TextSpan(children: <TextSpan>[
                    TextSpan(
                      text: '格口号：',
                      style: TextStyle(color: Colors.white, fontSize: 17, fontWeight: FontWeight.w700),
                    ),
                    TextSpan(
                      text: '${currentBoxItem?.boxLabel}',
                      style:
                          TextStyle(color: Theme.of(context).primaryColor, fontSize: 17, fontWeight: FontWeight.w700),
                    ),
                    TextSpan(
                      text: '号（${cabinetTypeMap[currentBoxItem?.type]!}）',
                      style: TextStyle(color: Colors.white, fontSize: 17, fontWeight: FontWeight.w700),
                    ),
                  ])),
                  Offstage(
                    offstage: availableCount == 0,
                    child: Text(
                      '【 可用$availableCount个】',
                      style: TextStyle(color: Colors.white, fontSize: 17, fontWeight: FontWeight.w700),
                    ),
                  )
                ],
              ),
              // Row(
              //   crossAxisAlignment: CrossAxisAlignment.center,
              //   children: [
              //     Text(
              //       '切换格口类型：',
              //       style: TextStyle(color: Colors.white, fontSize: 17, fontWeight: FontWeight.w700),
              //     ),
              //     OutlinedButton(
              //       onPressed: () {
              //         showBoxTypeRadioSelect();
              //       },
              //       child: Text(
              //         '${cabinetTypeMap[currentBoxItem?.type]!}格口',
              //         style:
              //             TextStyle(color: Theme.of(context).primaryColor, fontSize: 17, fontWeight: FontWeight.w700),
              //       ),
              //       style: ButtonStyle(
              //           shape: MaterialStateProperty.all(
              //               RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(5)))),
              //           side: MaterialStateProperty.all(BorderSide(color: Theme.of(context).primaryColor,width: 2))),
              //     )
              //   ],
              // )
            ],
          ),
        ),
      ),
    );
  }

  /// 品牌选择
  selectBrand({String? brand, bool isForce = false}) {
    SoundUtils.audioPushFn(SoundUtils.CHOOSE_COMPANY);
    CommonUtils.showBottomBrandSelectModal(context, brandMap, (item) {
      if (item != '') {
        SoundUtils.audioPushFn(SoundUtils.BRAND_SOUND[item]!);
        brandCode = item;
        showScanDb.brandCode = item;
        waybillRemoteGuss(isScan: false);
      } else {
        if (brand != null && brand != '') {
          brandCode = brand;
          showScanDb.brandCode = brand;
        }
      }
      setState(() {});
    }, bindCompany: brand, isForce: isForce);
  }

  getButtonStatus() {
    bool status =
        (showScanDb.receiverMobile != '' && showScanDb.receiverMobile.length >= 11) && showScanDb.waybillNo != '';
    return status;
  }

  openBox() async {
    isCountDown = false;
    LoadingUtil(
      status: '正在开门...',
    ).show(context);
    if (imagePath != '') {
      var savePhoto = await CheckUtils.checkPhotoUpload(
          imagePath, showScanDb.waybillNo, showScanDb.brandCode, cabinetLocationCode, 'inbound');
      showScanDb.inboundImageUrl = savePhoto;
    }
    var res = await CourierDao.courierShopOpenDoor(
      cabinetLocationCode,
      showScanDb.cabinetId,
      showScanDb.cabinetBoxId,
      showScanDb.waybillNo,
      showScanDb.brandCode,
      showScanDb.receiverName,
      showScanDb.receiverMobile,
      showScanDb.virtualPhone,
      gussEntity.hasSecret ? '1' : '0',
      gussEntity.hasSubstituteSms ? '1' : '0',
      '0',
      showScanDb.newCustomer.toString(),
      '0',
      '0',
      platform,
      gussEntity.yzSopAccountId,
      gussEntity.yzChannel,
      gussEntity.virtualNumber,
      showScanDb.inboundImageUrl,
      addInCabinet: showScanDb.addInCabinet,
      deliveryWaybill: isDelivery ? 1 : 0,
      hasDp: isBDP ? 1 : 0,
    );
    LoadingUtil.dismiss(context);
    if (res != null && res.result) {
      orderId = res.data['id'];
      inboundTime = res.data['inboundTime'];
      isCountDown = true;
      scanPlugin.playSound("${res.data['cabinetBoxLabel']}号格口");
      cancelCountDown();
      if (showScanDb.addInCabinet == false) {
        getCabinetUsableBox(cabinetLocationCode);
        openNextBox();
      } else {
        resetScan();
      }
    } else if (res.data != '操作太频繁' && !res.result) {
        print('开门失败${res.data}');
        Fluttertoast.showToast(msg: '存件失败');
        SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
      }
  }

  // openNextBox({int? cabinetBoxType}) async {
  //   LoadingUtil(
  //     status: '获取下一个格口...',
  //   ).show(context);
  //   var res = await CourierDao.courierShopOpenNextDoor(currentBoxItem?.id, cabinetBoxType: cabinetBoxType);
  //   LoadingUtil.dismiss(context);
  //   if (res != null && res.result) {
  //     setState(() {
  //       lastCabinetBoxId = res.data['cabinetBoxId'];
  //       currentCabinetId = res.data['cabinetId'];
  //       cabinetName = res.data['cabinetName'];
  //       cabinetBoxId = res.data['cabinetBoxId'];
  //       currentBoxItem = $CabinetBoxItemFromJson(
  //           {'id': res.data['cabinetBoxId'], 'type': res.data['boxType'], 'boxLabel': res.data['boxLabel']});
  //       availableCount = res.data['availableCount'];
  //       boxType = res.data['boxType'];
  //     });
  //     resetScan();
  //   } else {
  //     stopSpot();
  //     SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
  //     Navigator.of(context).pop(true);
  //   }
  // }

  /// 默认扫描框
  buildScanInputModel() {
    return Stack(
      children: [
        Positioned(
            top: 110,
            right: 22,
            child: Column(
              children: [
                ...buildDeliveryWaybill(),
                ...buildBDP(isLabel: true),
                ...buildAddCabinet(isLabel: true),
                ...buildOpenOCR(isLabel: true),
                ...buildSingleCustomerNotice(isLabel: true),
                ...buildBatchFastInto(isLabel: true)
              ],
            )),
        Positioned(
            bottom: 350,
            right: 5,
            child: Row(
              children: [
                Offstage(
                  offstage: !isCountDown!,
                  child: inboundTime != null ? cancelCabinet(inboundTime!) : Container(),
                )
              ],
            )),
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Column(children: [
            Container(
              height: scanInputModelHeight,
              // padding: EdgeInsets.only(bottom: 15),
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(topLeft: Radius.circular(8), topRight: Radius.circular(8))),
              child: CustomerScanForm(
                brandCode: brandCode,
                selectBrand: () {
                  if (!hasChangeBrand) {
                    Fluttertoast.showToast(msg: '该点位不允许修改快递品牌');
                    return;
                  }
                  selectBrand(brand: currentChooseBrandCode);
                },
                waybillNoEditingController: waybillNoEditingController,
                waybillNoFocusNode_: focusNode_,
                waybillNoChange: (name) {
                  setState(() {
                    showScanDb.waybillNo = name;
                  });
                },
                waybillNoClear: () => throttleUtil.throttle(() {
                  resetScan();
                }),
                waybillNoInputFunc: () {
                  stopSpot();
                  CommonUtils.inputWaybillNo(context, showScanDb.waybillNo, (waybillNo) {
                    if (waybillNo != '') {
                      isInProcess = false;
                      addScan(waybillNo, isScan: false);
                    } else {
                      startSpot();
                    }
                  });
                },
                nameFocusNode_: nameFocusNode_,
                nameEditingController: nameEditingController,
                nameChange: (name) {
                  showScanDb.receiverName = name;
                },
                nameClear: () {
                  setState(() {
                    nameEditingController.value = TextEditingValue(text: '');
                    showScanDb.receiverName = '';
                  });
                },
                mobileFocusNode_: mobileFocusNode_,
                mobileEditingController: mobileEditingController,
                mobileInputTap: () {
                  showKeyBoard(TextInputType.number);
                  FocusScope.of(context).requestFocus(mobileFocusNode_);
                },
                mobileChange: (phone) {
                  checkMobileInput(phone);
                },
                mobileClear: () {
                  clearPhoneController();
                },
                showMobileFitButton: showMobileGussByLast4Button,
                mobileFitFunc: () => throttleUtil.throttle(() {
                  mobileGussByLast4();
                }),
                showNewCustomer: showScanDb.newCustomer == null || showScanDb.newCustomer == 0,
                speechRecogntionFunc: () {
                  setState(() {
                    voiceStatus = true;
                    openVoiceCountDown();
                    Fluttertoast.showToast(msg: '请读出手机号码');
                  });
                },
                voiceStatus: voiceStatus,
                resetScan: () => throttleUtil.throttle(() {
                  resetScan();
                }),
                confirmNextStep: () => throttleUtil.throttle(() {
                  confirmNextStep(isInbound: true, flag: 9);
                }),
                boxAvaliableList: boxAvaliableList,
                buttonStatus: getButtonStatus(),
                chooseGK: (item) {
                  chooseGK(item);
                },
                boxType: boxType,
              ),
            ),
            MyKeyboard(showKeyboard, onKeyDown,
                textInputType: textInputType,
                textInputAction:
                    mobileEditingController.text.length == 4 ? TextInputAction.search : TextInputAction.done)
          ]),
        )
      ],
    );
  }

  chooseGK(item) {
    if (currentBoxItem?.type != item['type']) {
      openNextBox(cabinetBoxType: item['type']);
    }
  }

  cancelCabinet(String inTime) {
    return Offstage(
      offstage: countTime < 1,
      child: Container(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              height: 32,
              margin: EdgeInsets.only(bottom: 10, right: 22),
              child: OutlinedButton(
                onPressed: () {
                  countDownTimer?.cancel();

                  /// 取消入柜
                  cancelCabinetInbound(cabinetLocationCode, orderId!);
                },
                child: Text(
                  '取消入柜${countTime}s',
                  style: TextStyle(color: DefaultConfig().configs.WHITE_COLOR),
                ),
                style: ButtonStyle(
                    shape: MaterialStateProperty.all(StadiumBorder()),
                    side: MaterialStateProperty.all(BorderSide(color: Color(0xFFFFFFFF), width: 2))),
              ),
            )
          ],
        ),
      ),
    );
  }
}
