import 'dart:async';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_shop_entity.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/mixins/ScanMixin.dart';
import 'package:cabinet_flutter_app/common/page_life_cycle.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/utils/SoundUtils.dart';
import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/page/scan/widget/CustomerScanForm.dart';
import 'package:cabinet_flutter_app/widget/keyborad/MyKeyboard.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:scan/scan.dart';

import '../../index/shop/CheckCodePage.dart';

class InboundPage extends StatefulWidget {
  final String type;
  final bool isDelivery; // 是否送货上门

  InboundPage(this.type, this.isDelivery, {Key? key}) : super(key: key);

  @override
  _InboundPageState createState() => _InboundPageState();
}

class _InboundPageState extends State<InboundPage>
    with TickerProviderStateMixin, WidgetsBindingObserver, PageLifeCycle<InboundPage>, ScanMixin {
  double scanInputModelHeight = 231;

  CabinetShopEntity? cabinetShopEntity;

  String checkCode = ''; // 取件码

  @override
  void initState() {
    super.initState();
    // 初始化生命周期监听 用于解决扫码库在生命周期变动时候被杀死
    WidgetsBinding.instance.addObserver(this);

    if (widget.isDelivery) {
      scanInputModelHeight = 181;
    }
    checkFuc(height: scanInputModelHeight);
    initListener();
    getBrandList();
    initScan(320);
    initShopList();
    initCheckCode();
  }

  initShopList() async {
    LoadingUtil(
      status: '获取点位...',
    ).show(context);
    var res = await CourierDao.shopCabinetLocation();
    LoadingUtil.dismiss(context);
    List<CabinetShopEntity?> cabinetList = [];
    if (res != null && res.result) {
      cabinetList = jsonConvert.convertList<CabinetShopEntity>(res.data)!;
      if (cabinetList.length >= 1) {
        String? code = cabinetList[0]?.code!;
        cabinetLocationCode = code!;
        showScanDb = buildNewScan(checkCode: checkCode);
      } else {
        Fluttertoast.showToast(msg: '无可用点位');
      }
    }
  }

  /// 初始化取件码
  initCheckCode() async {
    ///获取本地存储取件码
    try {
      Map<String, dynamic>? takeCode = await LocalStorage.getJson(DefaultConfig().configs.TAKE_CODE, isPrivate: true);
      if (takeCode != null) {
        cabinetShopEntity = CabinetShopEntity.fromJson(takeCode);
      }
      getCheckCode(cabinetShopEntity?.id);
    } catch (e) {}
  }

  // 路由显示触发
  @override
  void onShow() {
    this.initScan(320);
    this.getBrandList();
    this.startSpot(flag: 14);
  }

  @override
  void onHide() {
    timer_?.cancel();
    timer?.cancel();
    scanSubscription_?.cancel();
    scanViewController?.stopSpot();
    this.stopSpot();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    ///通过state判断App前后台切换
    switch (state) {
      case AppLifecycleState.hidden:
      case AppLifecycleState.inactive: // 处于这种状态的应用程序应该假设它们可能在任何时候暂停。
        break;
      case AppLifecycleState.resumed: // 应用程序可见，前台
        initScan(320, isContinue: true);
        // startSpot();
        break;
      case AppLifecycleState.paused: // 应用程序不可见，后台
        scanSubscription_?.cancel();
        this.stopSpot();
        setState(() {
          this.scanView = null;
        });
        break;
      case AppLifecycleState.detached: // 申请将暂时暂停
        break;
    }
  }

  @override
  void dispose() {
    super.dispose();
    timer_?.cancel();
    timer?.cancel();
    countDownTimer?.cancel();
    scanSubscription_?.cancel();
    isChooseBrandCode = false;
    scanViewController?.stopSpot();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void deactivate() {
    super.deactivate();
  }

  @override
  Future<void> didChangeDependencies() async {
    super.didChangeDependencies();
    initFunc(isCabinet: false);
  }

  /// 获取取件码
  getCheckCode(id, {bool isBuildNewScan = true}) async {
    String? id_ = id;
    if (widget.isDelivery) {
      id_ = '0';
    }
    if (id_ == null) {
      return;
    }
    LoadingUtil(
      status: '获取取件码...',
    ).show(context);
    DataResult res = await CourierDao.courierUserCode(id_, widget.isDelivery);
    LoadingUtil.dismiss(context);
    if (res.result) {
      checkCode = res.data;
      if (isBuildNewScan) {
        showScanDb = buildNewScan(checkCode: checkCode);
      }
      setState(() {});
    }
  }

  /// 品牌选择
  selectBrand({String? brand, bool isForce = false}) {
    SoundUtils.audioPushFn(SoundUtils.CHOOSE_COMPANY);
    CommonUtils.showBottomBrandSelectModal(context, brandMap, (item) {
      if (item != '') {
        SoundUtils.audioPushFn(SoundUtils.BRAND_SOUND[item]!);
        brandCode = item;
        showScanDb.brandCode = item;
      } else {
        if (brand != null && brand != '') {
          brandCode = brand;
          showScanDb.brandCode = brand;
        }
      }
      setState(() {});
    }, bindCompany: brand, isForce: isForce);
  }

  getButtonStatus() {
    bool status =
        showScanDb.receiverMobile != '' && showScanDb.receiverMobile.length >= 11 && showScanDb.waybillNo != '';
    return status && checkCode != '';
  }

  /// 入仓扫描
  inbound() async {
    isCountDown = false;
    if (checkCode == '') {
      Fluttertoast.showToast(msg: '请选择取件码');
      return;
    }
    // TODO隐私面单与vip面单处理
    LoadingUtil(
      status: '正在入仓...',
    ).show(context);
    if (imagePath != '') {
      var savePhoto = await CheckUtils.checkPhotoUpload(
          imagePath, showScanDb.waybillNo, showScanDb.brandCode, cabinetShopEntity?.id ?? '0', 'inboud');
      showScanDb.inboundImageUrl = savePhoto;
    }
    var res = await CourierDao.courierShopIntoDoor(
        widget.isDelivery ? '0' : cabinetShopEntity!.id,
        widget.isDelivery ? '送货' : cabinetShopEntity!.name,
        checkCode,
        showScanDb.waybillNo,
        '',
        showScanDb.brandCode,
        showScanDb.receiverMobile,
        gussEntity.hasSecret ? '1' : '0',
        gussEntity.hasSubstituteSms ? '1' : '0',
        '0',
        showScanDb.newCustomer.toString(),
        '0',
        '0',
        platform,
        gussEntity.yzSopAccountId,
        gussEntity.yzChannel,
        gussEntity.virtualNumber,
        showScanDb.inboundImageUrl,
        isSynchronizedLogistics ? 0 : 1,
        1);
    LoadingUtil.dismiss(context);
    if (res != null && res.result) {
      Fluttertoast.showToast(msg: '入仓成功');
      showScanDb.cabinetId = res.data['cabinetId'];
      showScanDb.boxLabel = res.data['boxLabel'];
      showScanDb.cabinetNo = res.data['cabinetNo'];
      showScanDb.cabinetBoxId = res.data['cabinetBoxId'];
      showScanDb.orderNo = res.data['orderNo'];
      orderId = res.data['id'];
      inboundTime = res.data['inboundTime'];
      isCountDown = true;
      cancelCountDown();
      resetScan();

      /// 刷新取件码
      if (cabinetShopEntity != null) {
        getCheckCode(cabinetShopEntity!.id);
      }
    } else {
      SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
    }
  }

  /// 页面UI相关

  buildScanView() {
    if (this.scanView != null) {
      return this.scanView;
    }
    return Container();
  }

  /// 扫码界面
  buildRKScan() {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Stack(
        children: <Widget>[
          /// 扫码界面
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: MediaQuery.of(context).size.height,
            child: buildScanView(),
          ),

          /// 标题部分
          buildTitle(),

          // /// 默认扫描框
          buildScanInputModel(),
        ],
      ),
    );
  }

  /// 标题部分
  buildTitle() {
    double _navHeight = 55.0 + MediaQuery.of(context).padding.top;
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: new Container(
        width: MediaQuery.of(context).size.width,
        height: _navHeight,
        padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
        child: new Container(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Container(
                  width: 60.0,
                  padding: EdgeInsets.only(left: 12),
                  alignment: Alignment.centerLeft,
                  child: Icon(
                    Icons.arrow_back,
                    size: 25.0,
                    color: Colors.white,
                  ),
                ),
              ),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Text(
                      widget.isDelivery ? '送货上门' : '入仓扫描',
                      style: TextStyle(fontSize: 20.0, color: Colors.white),
                    ),
                    InkWell(
                      onTap: () async {
                        setFlash();
                      },
                      child: Padding(
                          padding: EdgeInsets.all(10.0),
                          child: LocalImageUtil.getImageAsset(checkFlash ? 'flashOpen' : 'flashClose',
                              width: 20, height: 20)),
                    ),
                    Padding(
                      padding: EdgeInsets.only(right: 10.0),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 默认扫描框
  buildScanInputModel() {
    return Stack(
      children: [
        Positioned(
            bottom: scanInputModelHeight,
            left: 10,
            child: Column(
              children: [...buildSynchronizedLogistics(isLabel: true)],
            )),
        Positioned(
            bottom: scanInputModelHeight,
            right: 0,
            child: Row(
              children: [
                Offstage(
                  offstage: !isCountDown!,
                  child: inboundTime != null ? cancelCabinet(inboundTime!) : Container(),
                )
              ],
            )),
        Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Column(
              children: [
                Container(
                  height: scanInputModelHeight,
                  // padding: EdgeInsets.only(bottom: 15),
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(topLeft: Radius.circular(8), topRight: Radius.circular(8))),
                  child: CustomerScanForm(
                    brandCode: brandCode,
                    selectBrand: () {
                      selectBrand(brand: currentChooseBrandCode);
                    },
                    waybillNoEditingController: waybillNoEditingController,
                    waybillNoFocusNode_: focusNode_,
                    waybillNoChange: (name) {
                      setState(() {
                        showScanDb.waybillNo = name;
                      });
                    },
                    waybillNoClear: () => throttleUtil.throttle(() {
                      resetScan();
                    }),
                    waybillNoInputFunc: () {
                      stopSpot();
                      CommonUtils.inputWaybillNo(context, showScanDb.waybillNo, (waybillNo) {
                        if (waybillNo != '') {
                          isInProcess = false;
                          addScan(waybillNo, isScan: false);
                        } else {
                          startSpot(flag: 18);
                        }
                      });
                    },
                    nameFocusNode_: nameFocusNode_,
                    nameEditingController: nameEditingController,
                    nameChange: (name) {
                      showScanDb.receiverName = name;
                    },
                    nameClear: () {
                      setState(() {
                        nameEditingController.value = TextEditingValue(text: '');
                        showScanDb.receiverName = '';
                      });
                    },
                    mobileFocusNode_: mobileFocusNode_,
                    mobileEditingController: mobileEditingController,
                    mobileInputTap: () {
                      showKeyBoard(TextInputType.number);
                      FocusScope.of(context).requestFocus(mobileFocusNode_);
                    },
                    mobileChange: (phone) {
                      checkMobileInput(phone);
                    },
                    mobileClear: () {
                      clearPhoneController();
                    },
                    showMobileFitButton: showMobileGussByLast4Button,
                    mobileFitFunc: () => throttleUtil.throttle(() {
                      mobileGussByLast4();
                    }),
                    showNewCustomer: showScanDb.newCustomer == null || showScanDb.newCustomer == 0,
                    speechRecogntionFunc: () {
                      setState(() {
                        voiceStatus = true;
                        openVoiceCountDown();
                        Fluttertoast.showToast(msg: '请读出手机号码');
                      });
                    },
                    voiceStatus: voiceStatus,
                    resetScan: () => throttleUtil.throttle(() {
                      resetScan();
                    }),
                    confirmNextStep: () => throttleUtil.throttle(() {
                      confirmNextStep(isInbound: true, flag: 6);
                    }),
                    buttonStatus: getButtonStatus(),
                    showGKSelection: false,
                    confirmButtonLabel: widget.isDelivery ? '确定送货' : "确定入仓",
                    checkCode: checkCode,
                    clickCheckCode: () => throttleUtil.throttle(() async {
                      final res = await NavigatorUtils.goCheckCodePage(context);
                      if (res != null) {
                        setState(() {
                          getCheckCode(res.id, isBuildNewScan: false);
                          cabinetShopEntity = res;
                          LocalStorage.save(DefaultConfig().configs.TAKE_CODE, cabinetShopEntity, isPrivate: true);
                        });
                      }
                    }),
                    refreshCheckCode: () {
                      getCheckCode(cabinetShopEntity?.id, isBuildNewScan: false);
                    },
                    goCheckCodePage: () {
                      Navigator.push(context, MaterialPageRoute(builder: (BuildContext context) {
                        return CheckCodePage();
                      })).then((item) {
                        setState(() {
                          if (item != null) {
                            setState(() {
                              getCheckCode(item.id, isBuildNewScan: false);
                              cabinetShopEntity = item;
                              LocalStorage.save(DefaultConfig().configs.TAKE_CODE, cabinetShopEntity, isPrivate: true);
                            });
                          }
                        });
                      });
                    },
                    isDelivery: widget.isDelivery,
                  ),
                ),
                MyKeyboard(showKeyboard, onKeyDown,
                    textInputType: textInputType,
                    textInputAction:
                        mobileEditingController.text.length == 4 ? TextInputAction.search : TextInputAction.done)
              ],
            ))
      ],
    );
  }

  cancelCabinet(String inTime) {
    return Offstage(
      offstage: countTime < 1,
      child: Container(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              height: 32,
              margin: EdgeInsets.only(bottom: 10, right: 10),
              child: OutlinedButton(
                onPressed: () {
                  countDownTimer?.cancel();

                  /// 取消入柜
                  cancelCabinetInbound('0', orderId!, showInput: false);
                },
                child: Text(
                  '取消入仓${countTime}s',
                  style: TextStyle(color: DefaultConfig().configs.WHITE_COLOR),
                ),
                style: ButtonStyle(
                    shape: MaterialStateProperty.all(StadiumBorder()),
                    side: MaterialStateProperty.all(BorderSide(color: Color(0xFFFFFFFF), width: 2))),
              ),
            )
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        bool canBack = true;
        if (isInProcess || isChooseBrandCode) {
          isInProcess = false;
          isChooseBrandCode = false;
          canBack = false;
        }
        return canBack;
      },
      child: StoreBuilder<AppState>(
        builder: (context, store) {
          return Scaffold(
            key: scaffoldKey_,
            backgroundColor: Colors.black,
            body: buildRKScan(),
          );
        },
      ),
    );
  }
}
