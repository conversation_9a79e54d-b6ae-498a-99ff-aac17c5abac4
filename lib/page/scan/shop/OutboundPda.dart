import 'dart:async';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/page_life_cycle.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/ScanUtils.dart';
import 'package:cabinet_flutter_app/common/utils/SoundUtils.dart';
import 'package:cabinet_flutter_app/common/utils/ThrottleUtil.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/ScanNotice.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:scan/scan.dart';

class OutboundPda extends StatefulWidget {
  OutboundPda({Key? key}) : super(key: key);

  @override
  _OutboundPdaState createState() => _OutboundPdaState();
}

class _OutboundPdaState extends State<OutboundPda>
    with TickerProviderStateMixin, WidgetsBindingObserver, PageLifeCycle<OutboundPda> {
  final GlobalKey<ScaffoldState> _scaffoldKey = new GlobalKey<ScaffoldState>();
  TextEditingController waybillNoEditingController = TextEditingController();
  FocusNode focusNode_ = FocusNode();
  String waybillNo = "";
  ThrottleUtil throttleUtil = ThrottleUtil();
  bool isInProcess = false;
  Scan scanPlugin = Scan();
  ScanView? scanView;
  StreamSubscription<dynamic>? _scanSubscription;
  ScanViewController? scanViewController;
  bool checkFlash = false;
  String lastScanWaybillNo = '';
  int lastScanTime = 0;

  String pickRule = 'end_four_sheetNo'; // 取件码规则，默认编号自然累加
  String brandCode = '';
  String imagePath = '';
  String shopId = '';

  @override
  void initState() {
    super.initState();
    // 初始化生命周期监听 用于解决扫码库在生命周期变动时候被杀死
    WidgetsBinding.instance.addObserver(this);
    initData();
    initScan();
  }

  initData() async {
    shopId = await LocalStorage.get(DefaultConfig().configs.SHOP_ID, isPrivate: true);
    setState(() {});
  }

  // 路由显示触发
  @override
  void onShow() {
    this.initScan();
    this.startSpot();
  }

  @override
  void onHide() {
    _scanSubscription?.cancel();
    scanViewController?.stopSpot();
    this.stopSpot();
  }

  void initScan() {
    _scanSubscription = scanPlugin.onScanChanged.listen((result) {
      if (result.containsKey('fileName')) {
        CheckUtils.savePhotoLocal(
            result['path'], result['fileName'], result['company'], result['status'], result['msg'], result['type']);
      }
      stopSpot();

      int nowtTime = new DateTime.now().millisecondsSinceEpoch;
      if (((nowtTime - lastScanTime) / 1000) < 3 && lastScanWaybillNo != '' && lastScanWaybillNo == result['text']) {
        if (result['text'] == waybillNo) {
          outbound();
        }
      } else {
        lastScanTime = nowtTime;
        lastScanWaybillNo = result['text'] ?? '';
        if (result['text'] != '' && result['text'] != null) {
          addScan(result['text'], img: result['img'], brandCode: result['company']);
        }
      }
    });

    // 初始化摄像头
    Timer(const Duration(milliseconds: 500), () {
      this.scanView = ScanView(
        onScanViewCreated: (ScanViewController controller) {
          scanViewController = controller;
          scanViewController?.changeScanType(type: 'ck');
          scanViewController?.enableOcr();
        },
        onScanViewError: () {
          CommonUtils.showMessage(context, msg: "调用摄像头失败", success: false, duration: 2);
        },
      );
      setState(() {});
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    ///通过state判断App前后台切换
    switch (state) {
      case AppLifecycleState.hidden:
      case AppLifecycleState.inactive: // 处于这种状态的应用程序应该假设它们可能在任何时候暂停。
        break;
      case AppLifecycleState.resumed: // 应用程序可见，前台
        initScan();
        break;
      case AppLifecycleState.paused: // 应用程序不可见，后台
        _scanSubscription?.cancel();
        this.stopSpot();
        setState(() {
          this.scanView = null;
        });
        break;
      case AppLifecycleState.detached: // 申请将暂时暂停
        break;
    }
  }

  @override
  void dispose() {
    super.dispose();
    _scanSubscription?.cancel();
    scanViewController?.stopSpot();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void deactivate() {
    super.deactivate();
  }

  ///开始识别
  startSpot() {
    scanViewController?.startSpot();
    isInProcess = false;
    setState(() {});
  }

  /// 停止识别
  stopSpot() {
    scanViewController?.stopSpot();
    isInProcess = false;
  }

  checkISOutOrBack(waybill) {
    bool isCanNext = true;
    if (waybill['isOutbound'] == 'Y' || waybill['isBack'] == 'Y') {
      String mess = '此快件已出库，不可重复出库';
      if (waybill['isBack'] == 'Y') {
        mess = '此快件已退回，不可出库';
      }
      Fluttertoast.showToast(msg: mess);
      SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
      isCanNext = false;
    }
    startSpot();
    return isCanNext;
  }

  bool? secretWaybill;
  bool? vipWaybill;

  addScan(String? waybillNo, {brandCode = '', engine = '', img = '', ocrImg = '', name = ''}) async {
    if (waybillNo == null) {
      startSpot();
      return false;
    }
    if (isInProcess) {
      return false;
    }
    isInProcess = true;
    if (waybillNo.startsWith('R02Z') || waybillNo.startsWith('R02T')) {
      waybillNo = waybillNo.substring(4);
    }
    if (img != '' && img != null) {
      imagePath = img;
    }

    /// 本地猜单
    brandCode = await ScanUtils.getCompany(context, waybillNo);
    SoundUtils.audioPushFn(SoundUtils.SUCCESS_OK);
    stopSpot();
    this.waybillNo = waybillNo;
    waybillNoEditingController.value = TextEditingValue(text: waybillNo);
    this.brandCode = brandCode;
    getMessageConfirm();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        bool canBack = true;
        if (isInProcess) {
          isInProcess = false;
          canBack = false;
        }
        return canBack;
      },
      child: Scaffold(
        key: _scaffoldKey,
        backgroundColor: DefaultConfig().configs.BG_COLOR,
        appBar: AppCustomerBar(
          title: AppbarTitle(
            title: '出仓扫描',
            isCenter: true,
          ),
          actions: [
            Container(
              width: 60,
            )
          ],
        ),
        body: buildCKScan(),
      ),
    );
  }

  /// 扫码界面
  buildCKScan() {
    return Container(
      height: MediaQuery.of(context).size.height,
      child: Stack(
        children: <Widget>[
          // ScanNotice(content: '柜机小助手：重复扫描单号可确认出库哦~'),
          // /// 默认扫描框
          Positioned(left: 0, right: 0, bottom: 0, child: buildScanInputModel())
        ],
      ),
    );
  }

  // 单号录入
  openInputDialog() {
    stopSpot();
    CommonUtils.inputWaybillNo(context, '', (waybillNo) {
      if (waybillNo != '') {
        addScan(waybillNo);
      } else {
        startSpot();
      }
    });
  }

  resetScan() {
    waybillNo = '';
    brandCode = '';
    waybillNoEditingController.value = TextEditingValue(text: '');
    startSpot();
  }

  getButtonStatus() {
    bool status = false;
    if (waybillNo != '') {
      status = true;
    } else {
      status = false;
    }
    return status;
  }

  var savePhoto;

  outbound() async {
    stopSpot();
    LoadingUtil(
      status: '正在出仓...',
    ).show(context);
    if (imagePath != '') {
      savePhoto = await CheckUtils.checkPhotoUpload(imagePath, waybillNo, brandCode, shopId, 'outbound', type: 'ck');
    }
    var res = await CourierDao.courierShopOutDoor(waybillNo, outboundImageUrl: savePhoto);
    LoadingUtil.dismiss(context);
    if (res != null && res.result) {
      Fluttertoast.showToast(msg: '出库成功');
      resetScan();
    } else {
      SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
    }
  }

  /// 默认扫描框
  buildScanInputModel() {
    return Container(
      color: Colors.white,
      margin: EdgeInsets.only(top: 10),
      padding: EdgeInsets.only(bottom: 15),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    InkWell(
                      onTap: () {},
                      child: Container(
                        child: Row(
                          children: [
                            Container(
                              width: 36,
                              height: 36,
                              child: CircleAvatar(
                                radius: 10.0,
                                backgroundColor: Colors.white,
                                backgroundImage: AssetImage(CommonUtils.getExpressLogo(brandCode)),
                              ),
                              margin: EdgeInsets.only(left: 10, right: 5),
                            ),
                            Icon(
                              Icons.keyboard_arrow_down,
                              size: 24,
                              color: Colors.black,
                            )
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: Container(
                          height: 64,
                          margin: EdgeInsets.only(left: 10.0),
                          padding: EdgeInsets.fromLTRB(0, 10, 0, 10),
                          child: TextField(
                            keyboardType: TextInputType.number,
                            controller: waybillNoEditingController,
                            style: TextStyle(
                              fontSize: 18.0,
                              color: Colors.black87,
                            ),
                            focusNode: focusNode_,
                            enabled: false,
                            decoration: new InputDecoration(
                                border: InputBorder.none,
                                contentPadding: EdgeInsets.all(0.0),
                                hintText: '输入快递运单号',
                                hintStyle: new TextStyle(fontSize: 18.0, color: Colors.grey.shade500)),
                            onChanged: (name) {
                              setState(() {
                                waybillNo = name;
                              });
                            },
                            onSubmitted: (name) async {},
                          )),
                    ),
                    Offstage(
                      offstage: waybillNoEditingController.text.length <= 0,
                      child: new IconButton(
                        alignment: Alignment.center,
                        padding: const EdgeInsets.all(0.0),
                        iconSize: 22.0,
                        color: Colors.grey,
                        icon: Icon(Icons.cancel),
                        onPressed: () {
                          setState(() {
                            waybillNoEditingController.value = TextEditingValue(text: '');
                            waybillNo = '';
                          });
                          startSpot();
                        },
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        openInputDialog();
                      },
                      child: Container(
                        width: 40.0,
                        alignment: Alignment.centerLeft,
                        child: Icon(
                          Icons.mode_edit_sharp,
                          size: 22.0,
                          color: DefaultConfig().configs.GREY_COLOR,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          Container(
            margin: EdgeInsets.only(left: 15, right: 15),
            width: MediaQuery.of(context).size.width - 30,
            height: 1,
            color: DefaultConfig().configs.BG_COLOR,
          ),
          SizedBox(
            height: 10,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              InkWell(
                child: Container(
                  height: 55,
                  width: 90,
                  margin: EdgeInsets.only(left: 10),
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    borderRadius: BorderRadius.only(topLeft: Radius.circular(40), bottomLeft: Radius.circular(40)),
                  ),
                  child: Text(
                    "重置",
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                onTap: () => throttleUtil.throttle(() {
                  resetScan();
                }),
              ),
              Expanded(
                  child: InkWell(
                onTap: () {
                  if (getButtonStatus()) {
                    outbound();
                  }
                },
                child: Container(
                  margin: EdgeInsets.only(right: 10),
                  height: 55,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: getButtonStatus() ? Theme.of(context).primaryColor : Color.fromRGBO(255, 231, 223, 1),
                    borderRadius: BorderRadius.only(topRight: Radius.circular(40), bottomRight: Radius.circular(40)),
                  ),
                  child: Text(
                    "确定出仓",
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ))
            ],
          )
        ],
      ),
    );
  }

  /// 单号和手机号输入框监听
  getMessageConfirm() {
    if (CheckUtils.isNotNull(waybillNo)) {
      stopSpot();
      setState(() {});
    }
  }
}
