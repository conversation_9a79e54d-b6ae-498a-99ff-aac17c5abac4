import 'dart:async';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/page_life_cycle.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/ScanUtils.dart';
import 'package:cabinet_flutter_app/common/utils/SoundUtils.dart';
import 'package:cabinet_flutter_app/common/utils/ThrottleUtil.dart';
import 'package:cabinet_flutter_app/common/utils/text_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:scan/scan.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

class Outbound extends StatefulWidget {

  Outbound({Key? key}) : super(key: key);

  @override
  _OutboundState createState() => _OutboundState();
}

class _OutboundState extends State<Outbound>
    with TickerProviderStateMixin, WidgetsBindingObserver, PageLifeCycle<Outbound> {
  final GlobalKey<ScaffoldState> _scaffoldKey = new GlobalKey<ScaffoldState>();
  String waybillNo = "";
  bool isInProcess = false;
  Scan scanPlugin = Scan();
  ThrottleUtil throttleUtil = ThrottleUtil();
  ScanView? scanView;
  StreamSubscription<dynamic>? _scanSubscription;
  PanelController scanPanelController = new PanelController();
  late ScanViewController scanViewController;
  bool checkFlash = false;

  String pickRule = 'end_four_sheetNo'; // 取件码规则，默认编号自然累加
  String brandCode = '';
  double scanInputModelHeight = 0;
  double scanListModelHeight = 62;
  String imagePath = '';
  String shopId = '';

  @override
  void initState() {
    super.initState();
    // 初始化生命周期监听 用于解决扫码库在生命周期变动时候被杀死
    WidgetsBinding.instance.addObserver(this);
    initData();
    initScan();
  }

  initData() async {
    shopId = await LocalStorage.get(DefaultConfig().configs.SHOP_ID, isPrivate: true);
  }



  // 路由显示触发
  @override
  void onShow() {
    this.initScan();
    this.startSpot();
  }

  @override
  void onHide() {
    _scanSubscription?.cancel();
    scanViewController.stopSpot();
    this.stopSpot();
  }

  void initScan() {
    _scanSubscription = scanPlugin.onScanChanged.listen((result) {
      if (result.containsKey('fileName')) {
        CheckUtils.savePhotoLocal(
            result['path'], result['fileName'], result['company'], result['status'], result['msg'], result['type']);
      }
      stopSpot();
      if (result['text'] != '' && result['text'] != null) {
        addScan(
          result['text'],
          img: result['img'],
          brandCode: result['company'],
          engine: result['engine'],
        );
      }
    });

    // 初始化摄像头
    Timer(const Duration(milliseconds: 500), () {
      this.scanView = ScanView(
        onScanViewCreated: (ScanViewController controller) {
          scanViewController = controller;
          scanViewController.setScanTopOffset(
              60 + int.parse(MediaQuery.of(context).padding.top.toStringAsFixed(0)), 70);
          scanViewController.changeScanType(type: 'ck');
          scanViewController.setCornerColor(DefaultConfig().configs.PRIMARY_COLOR_TEXT);
          scanViewController.enableOcr();
          scanViewController.showPhoto();
        },
        onScanViewError: () {
          CommonUtils.showMessage(context, msg: "调用摄像头失败", success: false, duration: 2);
        },
      );
      setState(() {});
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    ///通过state判断App前后台切换
    switch (state) {
      case AppLifecycleState.hidden:
      case AppLifecycleState.inactive: // 处于这种状态的应用程序应该假设它们可能在任何时候暂停。
        break;
      case AppLifecycleState.resumed: // 应用程序可见，前台
        initScan();
        break;
      case AppLifecycleState.paused: // 应用程序不可见，后台
        _scanSubscription?.cancel();
        this.stopSpot();
        setState(() {
          this.scanView = null;
        });
        break;
      case AppLifecycleState.detached: // 申请将暂时暂停
        break;
    }
  }

  @override
  void dispose() {
    super.dispose();
    _scanSubscription?.cancel();
    scanViewController.stopSpot();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void deactivate() {
    super.deactivate();
  }



  ///开始识别
  startSpot() {
    scanViewController.startSpot();
    isInProcess = false;
    setState(() {});
  }

  /// 停止识别
  stopSpot() {
    scanViewController.stopSpot();
    isInProcess = false;
  }

  checkISOutOrBack(waybill) {
    bool isCanNext = true;
    if (waybill['isOutbound'] == 'Y' || waybill['isBack'] == 'Y') {
      String mess = '此快件已出库，不可重复出库';
      if (waybill['isBack'] == 'Y') {
        mess = '此快件已退回，不可出库';
      }
      Fluttertoast.showToast(msg: mess);
      SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
      isCanNext = false;
    }
    startSpot();
    return isCanNext;
  }

  bool? secretWaybill;
  bool? vipWaybill;

  addScan(String? waybillNo, {brandCode = '', engine = '', img = '', ocrImg = '', name = ''}) async {
    if (waybillNo == null) {
      startSpot();
      return false;
    }
    if (isInProcess) {
      return false;
    }
    isInProcess = true;
    if (waybillNo.startsWith('R02Z') || waybillNo.startsWith('R02T')) {
      waybillNo = waybillNo.substring(4);
    }
    if (img != '' && img != null) {
      imagePath = img;
    }

    /// 本地猜单
    brandCode = await ScanUtils.getCompany(context, waybillNo);
    SoundUtils.audioPushFn(SoundUtils.SUCCESS_OK);
    stopSpot();
    this.waybillNo = waybillNo;
    this.brandCode = brandCode;
    getMessageConfirm();
    setState(() {});
  }

  /// 输入框 扫描框
  _buildScanOrInputArea() {
    return buildCKScan();
  }

  // 打开、关闭闪光灯
  setFlash() {
    checkFlash = !checkFlash;
    if (checkFlash) {
      scanViewController.openFlashlight();
    } else {
      scanViewController.closeFlashlight();
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        bool canBack = true;

        if (isInProcess) {
          isInProcess = false;
          canBack = false;
        }
        if (scanInputModelHeight > 120) {
          resetScan();
          canBack = false;
          return canBack;
        }
        return canBack;
      },
      child: StoreBuilder<AppState>(
        builder: (context, store) {
          return Scaffold(
            key: _scaffoldKey,
            backgroundColor: Colors.black,
            body: _buildScanOrInputArea(),
          );
        },
      ),
    );
  }

  buildScanView() {
    if (this.scanView != null) {
      return this.scanView;
    }
    return Container();
  }

  /// 扫码界面
  buildCKScan() {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Stack(
        children: <Widget>[
          /// 扫码界面
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: MediaQuery.of(context).size.height,
            child: buildScanView(),
          ),

          /// 标题部分
          buildTitle(),

          // /// 默认扫描框
          buildScanInputModel(),
        ],
      ),
    );
  }

  /// 标题部分
  buildTitle() {
    double _navHeight = 55.0 + MediaQuery.of(context).padding.top;
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: new Container(
        width: MediaQuery.of(context).size.width,
        height: _navHeight,
        padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
        child: new Container(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Container(
                  width: 60.0,
                  padding: EdgeInsets.only(left: 12),
                  alignment: Alignment.centerLeft,
                  child: Icon(
                    Icons.arrow_back,
                    size: 25.0,
                    color: Colors.white,
                  ),
                ),
              ),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Text(
                      '出仓扫描',
                      style: TextStyle(fontSize: 20.0, color: Colors.white),
                    ),
                    InkWell(
                      onTap: () async {
                        setFlash();
                      },
                      child: Padding(
                          padding: EdgeInsets.only(left: 10),
                          child: LocalImageUtil.getImageAsset(checkFlash ? 'flashOpen' : 'flashClose',
                              width: 20, height: 20)),
                    ),
                    Padding(
                      padding: EdgeInsets.only(right: 10.0),
                    )
                  ],
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  InkWell(
                    onTap: () {
                      openInputDialog();
                    },
                    child: Container(
                      width: 35.0,
                      height: 35.0,
                      margin: EdgeInsets.only(right: 10),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(20.0)), color: Theme.of(context).primaryColor),
                      child: Image.asset(
                        LocalImageUtil.getImagePath('editCk'),
                        width: 20.0,
                      ),
                    ),
                  )
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 单号录入
  openInputDialog() {
    stopSpot();
    CommonUtils.inputWaybillNo(context, '', (waybillNo) {
      if (waybillNo != '') {
        setState(() {
          addScan(waybillNo);
        });
      } else {
        startSpot();
      }
    });
  }

  resetScan() {
    scanInputModelHeight = 0;
    scanListModelHeight = 62;
    waybillNo = '';
    // brandCode = '';
    scanPanelController.close();
    startSpot();
  }

  getButtonStatus() {
    bool status = false;
    if (waybillNo != '') {
      status = true;
    } else {
      status = false;
    }
    return status;
  }

  var savePhoto;

  outbound() async {
    stopSpot();
    LoadingUtil(
      status: '正在出仓...',
    ).show(context);
    if (imagePath != '') {
      savePhoto = await CheckUtils.checkPhotoUpload(imagePath, waybillNo, brandCode, shopId, 'outbound', type: 'ck');
    }
    var res = await CourierDao.courierShopOutDoor(waybillNo, outboundImageUrl: savePhoto);
    LoadingUtil.dismiss(context);
    if (res != null && res.result) {
      
      Fluttertoast.showToast(msg: '出仓成功');
      resetScan();
    } else {
      SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
    }
  }

  /// 默认扫描框
  buildScanInputModel() {
    return SlidingUpPanel(
      controller: scanPanelController,
      minHeight: scanInputModelHeight,
      maxHeight: scanInputModelHeight,
      borderRadius: BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16)),
      // maxHeight: MediaQuery.of(context).size.height - 50,
      onPanelOpened: () {
        stopSpot();
      },
      onPanelClosed: () {
        startSpot();
      },
      isDraggable: false,

      panel: Container(
        padding: EdgeInsets.only(top: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            /// panel close
            Offstage(
              offstage: scanInputModelHeight <= 120,
              child: Column(
                children: [
                  /// 单号输入框
                  Row(
                    children: [
                      Expanded(
                        child: Row(
                          children: [
                            InkWell(
                              onTap: () {},
                              child: Container(
                                child: Row(
                                  children: [
                                    Container(
                                      width: 30,
                                      height: 30,
                                      child: CircleAvatar(
                                        radius: 10.0,
                                        backgroundColor: Colors.white,
                                        backgroundImage: AssetImage(CommonUtils.getExpressLogo(brandCode)),
                                      ),
                                      margin: EdgeInsets.only(left: 10, right: 5),
                                    ),
                                    Container(
                                        width: 65,
                                        child: Text(
                                          DefaultConfig().configs.EXPRESS2[brandCode] ?? '',
                                          style: TextStyle(fontSize: 16, color: Colors.black),
                                        )),
                                  ],
                                ),
                              ),
                            ),
                            Expanded(
                              child: Container(
                                  height: 48,
                                  padding: EdgeInsets.only(left: 10.0, top: 5.0),
                                  child: Row(
                                    children: [
                                      Text('${TextUtil.formatDigitPattern(waybillNo, digit: 5, pattern: '  ')}',
                                          style:
                                              TextStyle(fontSize: 18, color: Colors.black, fontWeight: FontWeight.bold))
                                    ],
                                  )),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  Container(
                    margin: EdgeInsets.only(left: 15, right: 15),
                    width: MediaQuery.of(context).size.width - 30,
                    height: 1,
                    color: DefaultConfig().configs.BG_COLOR,
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      InkWell(
                        child: Container(
                          height: 45,
                          width: 90,
                          margin: EdgeInsets.only(left: 10),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            borderRadius:
                                BorderRadius.only(topLeft: Radius.circular(40), bottomLeft: Radius.circular(40)),
                          ),
                          child: Text(
                            "重置",
                            style: TextStyle(
                              fontSize: 15,
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        onTap: () => throttleUtil.throttle(() {
                          resetScan();
                        }),
                      ),
                      Padding(padding: EdgeInsets.only(left: 1.5)),
                      Expanded(
                          child: InkWell(
                        onTap: () {
                          if (getButtonStatus()) {
                            outbound();
                          }
                        },
                        child: Container(
                          margin: EdgeInsets.only(right: 10),
                          height: 45,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color:
                                getButtonStatus()  ? Theme.of(context).primaryColor
        : DefaultConfig().configs.PRIMARY_COLOR_LIGHT,
                            borderRadius:
                                BorderRadius.only(topRight: Radius.circular(40), bottomRight: Radius.circular(40)),
                          ),
                          child: Text(
                            "确定出仓",
                            style: TextStyle(
                              fontSize: 15,
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ))
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      ),
      body: Stack(
        children: [
          // Column(
          //   children: [
          //     Padding(padding: EdgeInsets.only(top: MediaQuery.of(context).size.height * .3)),
          //     Center(
          //       child: Container(
          //         width: MediaQuery.of(context).size.width - 100,
          //         height: 1,
          //         color: Colors.red,
          //       ),
          //     ),
          //     Padding(padding: EdgeInsets.only(top: 40)),
          //     Text(
          //       '请将红线对准收件人手机号',
          //       style: TextStyle(color: Colors.white, fontSize: 14),
          //     ),
          //   ],
          // ),
        ],
      ),
    );
  }

  /// 单号和手机号输入框监听
  getMessageConfirm() {
    if (CheckUtils.isNotNull(waybillNo)) {
      scanInputModelHeight = 121;
      scanListModelHeight = 0;
      scanPanelController.open();
      stopSpot();
      setState(() {});
    }
  }
}
