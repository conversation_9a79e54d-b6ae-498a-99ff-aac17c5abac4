import 'dart:async';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/wallet_summary_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/withdrawal_record_entity.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppListState.dart';
import 'package:cabinet_flutter_app/widget/AppPullLoadWidget.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/NoResult.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CommonRowWidget.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CustomerDropDownSelectWidget.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class CapitalQueryForm {
  late String startTime;
  late String endTime;
  late String dateRangeType;
  String? type;
}

class CashOutRecordPage extends StatefulWidget {
  CashOutRecordPage({Key? key}) : super(key: key);

  @override
  _CashOutRecordPage createState() => _CashOutRecordPage();
}

class _CashOutRecordPage extends State<CashOutRecordPage>
    with
        AutomaticKeepAliveClientMixin<CashOutRecordPage>,
        AppListState<CashOutRecordPage>,
        WidgetsBindingObserver,
        SingleTickerProviderStateMixin {
  ScrollController _controller = new ScrollController();

  final GlobalKey<ScaffoldState> capitalFlowPageKey = GlobalKey<ScaffoldState>();
  CapitalQueryForm queryForm = new CapitalQueryForm();
  int total = 0;
  late WalletSummaryEntity data = new WalletSummaryEntity();

  void initState() {
    super.initState();
    init();
  }

  init() {
    queryForm.dateRangeType = 'today';
    queryForm.startTime = DateFormat('yyyy-MM-dd').format(DateTime.now());
    queryForm.endTime = DateFormat("yyyy-MM-dd").format(DateTime.now());
  }

  // 上拉加载更多
  _getData() async {
    Map<String, dynamic> info = {
      'status': queryForm.type,
      'current': page.toString(),
      'size': 10,
    };
    var res = await UserDao.goWithdrawalDetails(info);
    if (res != null && res.result) {
      total = int.parse(res.data['total']);
      return new DataResult(res.data['records'], true, total: int.parse(res.data['total']));
    }
  }

  Future<Null> onLoadMore() async {
    if (isLoading) {
      return null;
    }
    isLoading = true;
    page++;
    var res = await requestLoadMore();
    if (res != null && res.result) {
      setState(() {
        pullLoadWidgetControl.dataList.addAll(res.data);
      });
    }
    resolveDataResult(res);
    isLoading = false;
    return null;
  }

  @protected
  Future<Null> handleRefresh() async {
    if (isLoading) {
      return null;
    }
    refreshIndicatorKey.currentState?.show();
    isLoading = true;
    page = 1;
    var res = await requestRefresh();
    resolveRefreshResult(res);
    resolveDataResult(res);
    if (res.next != null) {
      var resNext = await res.next;
      resolveRefreshResult(resNext);
      resolveDataResult(resNext);
    }
    isLoading = false;
    return null;
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  _renderEventItem(index) {
    WithdrawalRecordEntity item = WithdrawalRecordEntity.fromJson(dataList[index]);
    return InkWell(
      onTap: () {},
      child: Container(
        color: Colors.white,
        padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
        child: Container(
          padding: EdgeInsets.only(bottom: 10),
          decoration: BoxDecoration(border: Border(bottom: BorderSide(width: 1, color: Color(0xFFEEEEEE)))),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('${item.taskTargetName}',
                      style: TextStyle(fontSize: 16, color: Colors.black, fontWeight: FontWeight.w400)),
                  Padding(padding: EdgeInsets.only(top: 6)),
                  Text('${item.applyTime ?? ''}',
                      style: TextStyle(fontSize: 13, color: Color(0xFF999999), fontWeight: FontWeight.w400)),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                      item.status == 3
                          ? '+ ${((item.withdrawalMoney)! / 1000).toStringAsFixed(2)}'
                          : '${((item.withdrawalMoney)! / 1000).toStringAsFixed(2)}',
                      style: TextStyle(fontSize: 16, color: Colors.black, fontWeight: FontWeight.w400)),
                  Padding(padding: EdgeInsets.only(top: 6)),
                  Text('${DefaultConfig().configs.WITHDRAWAL_TYPE_MAP[item.status.toString()]}',
                      style: TextStyle(fontSize: 13, color: Color(0xFF999999), fontWeight: FontWeight.w400)),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String formatData(String number) {
    return number.substring(number.length - 1);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      backgroundColor: Colors.grey.shade100,
      key: capitalFlowPageKey,
      appBar: AppCustomerBar(
        title: AppbarTitle(
          title: '提现记录',
          isCenter: true,
        ),
        actions: <Widget>[Container(width: 60)],
      ),
      body: Stack(
        children: <Widget>[
          Column(
            children: <Widget>[
              Container(
                  margin: EdgeInsets.only(top: 10),
                  padding: EdgeInsets.only(left: 10, right: 10),
                  alignment: Alignment.centerLeft,
                  decoration: BoxDecoration(
                      color: Colors.white, border: Border(bottom: BorderSide(width: 1, color: Color(0xFFEEEEEE)))),
                  child: CommonRowWidget(
                    leftWidget: Row(
                      children: [
                        // Container(
                        //   constraints: BoxConstraints(maxWidth: 70),
                        //   child: Container(
                        //       constraints: BoxConstraints(maxWidth: 80),
                        //       child: DateTimeRangeWidget(
                        //         value: DateTime.parse(queryForm.startTime) - DateTime.parse(queryForm.startTime),
                        //         cb: (date) {
                        //           setState(() {
                        //             queryForm.startTime = DateFormat("yyyy-MM-dd").format(
                        //                 DateTime.parse(DateFormat("yyyy-MM-dd HH:mm:ss").format(DateTime.now())));
                        //             queryForm.endTime = DateFormat("yyyy-MM-dd").format(
                        //                 DateTime.parse(DateFormat("yyyy-MM-dd HH:mm:ss").format(DateTime.now())));
                        //           });
                        //         },
                        //       ))
                        // ),
                        Padding(padding: EdgeInsets.only(left: 5)),
                        Container(
                          constraints: BoxConstraints(maxWidth: 100),
                          child: CustomerDropDownSelectWidget(
                              hintText: '提现类型',
                              value: '${queryForm.type}',
                              map: DefaultConfig().configs.WITHDRAWAL_TYPE_MAP,
                              cb: (item) {
                                setState(() {
                                  queryForm.type = item;
                                });
                                handleRefresh();
                              }),
                        ),
                      ],
                    ),
                    rightWidget: Row(
                      children: [Text('${total.toString()} 条')],
                    ),
                  )),
              Expanded(
                  child: dataList.length > 0
                      ? AppPullLoadWidget(
                          pullLoadWidgetControl,
                          (BuildContext context, int index) => _renderEventItem(index),
                          handleRefresh,
                          onLoadMore,
                          refreshKey: refreshIndicatorKey,
                        )
                      : RefreshIndicator(
                          key: refreshIndicatorKey,
                          child: SingleChildScrollView(
                            physics: AlwaysScrollableScrollPhysics(),
                            child: Container(
                              height: 300,
                              child: NoResult(
                                  size: 90,
                                  subWidget: Container(
                                      padding: EdgeInsets.only(top: 10),
                                      child: Text('', style: AppConstant.smallSubText))),
                            ),
                          ),
                          onRefresh: handleRefresh)),
            ],
          ),
        ],
      ),
    );
  }

  // TODO: implement wantKeepAlive
  @override
  bool get wantKeepAlive => true;

  // TODO: implement isRefreshFirst
  @override
  bool get isRefreshFirst => true;

  @override
  requestLoadMore() async {
    return await _getData();
  }

  @override
  requestRefresh() async {
    return await _getData();
  }
}
