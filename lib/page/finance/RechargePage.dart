import 'dart:io';

import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/recharge_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/utils/ThrottleUtil.dart';
import 'package:cabinet_flutter_app/page/finance/CustimJPasswordField.dart';
import 'package:cabinet_flutter_app/page/finance/MyKeyEvent.dart';
import 'package:cabinet_flutter_app/page/finance/MyKeyboard.dart';
import 'package:cabinet_flutter_app/page/login/AgreementPage.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/BottomBtnWidget.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CommonRowWidget.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:fluwx/fluwx.dart' as fluwx;
import 'package:fluwx/fluwx.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';
import 'package:tobias/tobias.dart';
import 'package:url_launcher/url_launcher.dart';

class RechargePage extends StatefulWidget {
  final String balanceNum;

  RechargePage(this.balanceNum, {Key? key}) : super(key: key);

  @override
  _RechargePageState createState() => _RechargePageState();
}

class RechargeFrom {
  late String money;
  late String moneyYuan;
  late String payMethod;
  late bool isAgree;
}

class _RechargePageState extends State<RechargePage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final TapGestureRecognizer recognizer = TapGestureRecognizer();
  ThrottleUtil throttleUtil = ThrottleUtil();
  double scanInputModelHeight = 0;
  late VoidCallback _showBottomSheetCallback;
  String payPassword = '';
  bool isNativePay = true;
  PanelController scanPanelController = new PanelController();
  Tobias tobias = Tobias();
  List<Map<String, dynamic>> chargeList = [
    {'code': '1', 'money_yuan': '10', 'money_li': '10000', 'selected': false},
    {'code': '2', 'money_yuan': '20', 'money_li': '20000', 'selected': false},
    {'code': '3', 'money_yuan': '50', 'money_li': '50000', 'selected': false},
    {'code': '4', 'money_yuan': '100', 'money_li': '100000', 'selected': false},
    {'code': '5', 'money_yuan': '500', 'money_li': '500000', 'selected': false},
    {'code': '6', 'money_yuan': '1000', 'money_li': '1000000', 'selected': false}
  ];
  List<Map<String, dynamic>> payMethodList = [
    {'code': 'alipay', 'name': '支付宝', 'logo': 'alipay'}
    // {'code': 'wx', 'name': '微信', 'logo': 'wx'}
    // {'code': 'wallet', 'name': '可提现转入', 'logo': 'cash'},
  ];
  RechargeFrom form = new RechargeFrom();
  bool isShow = false;
  bool showMore = true;
  UserEntity? user;

  @override
  void initState() {
    super.initState();
    _showBottomSheetCallback = _showBottomSheet;
    recognizer.onTap = () {
      print('协议');
      Navigator.push(context, new MaterialPageRoute(builder: (context) {
        return AgreementPage(
            url: DefaultConfig().configs.WEB_URL + DefaultConfig().configs.USER_AGREE2,
            isLocalUrl: false,
            title: '充值协议');
      }));
    };
    getIsNativePay();
    // 监听支付结果
    fluwx.weChatResponseEventHandler.listen((event) async {
      if (isNativePay) {
        // 支付成功
        if (event.errCode == 0) {
          Fluttertoast.showToast(msg: '支付成功', gravity: ToastGravity.CENTER);
        } else if (event.errCode == -2) {
          Fluttertoast.showToast(msg: "取消支付", gravity: ToastGravity.CENTER);
        } else {
          Fluttertoast.showToast(msg: "充值失败", gravity: ToastGravity.CENTER);
        }
      } else {
        if (event is fluwx.WeChatLaunchMiniProgramResponse) {
          // // 支付成功
          if (event.extMsg == 'SUCCESS') {
            Fluttertoast.showToast(msg: '支付成功', gravity: ToastGravity.CENTER);
          } else if (event.extMsg == 'FAIL') {
            Fluttertoast.showToast(msg: "取消支付", gravity: ToastGravity.CENTER);
          } else if (event.extMsg == 'ERROR') {
            Fluttertoast.showToast(msg: "充值失败", gravity: ToastGravity.CENTER);
          }
        }
      }
    });

    init();
  }

  getIsNativePay() async {
    var res = await UserDao.sysConfig({
      'configKey': 'is_native_pay',
    });
    setState(() {
      isNativePay = res.data == 1;
    });
  }

  init() async {
    form.isAgree = true;
    form.money = '50000';
    form.moneyYuan = '50';
    form.payMethod = 'alipay';
    if (kDebugMode) {
      chargeList.insert(0, {'code': '0', 'money_yuan': '0.01', 'money_li': '10', 'selected': false});
    }
    var userRes = await UserDao.getUserInfoLocal();
    if (userRes != null && userRes.result) {
      UserEntity? userEntity = userRes.data;
      user = userEntity;
    }
    setState(() {});
  }

  @override
  void dispose() {
    recognizer.dispose();
    super.dispose();
  }

  recharge() async {
    if (!form.isAgree) {
      Fluttertoast.showToast(msg: '请阅读并同意充值协议', gravity: ToastGravity.CENTER);
      return false;
    }
    var res;
    Map<String, dynamic> info;
    if ('wx' == form.payMethod) {
      var resultWx = await fluwx.isWeChatInstalled;
      if (!resultWx) {
        return Fluttertoast.showToast(msg: '请安装微信客户端');
      }
      if (isNativePay) {
        info = {
          'appId': DefaultConfig().configs.WX_APP_ID,
          'money': form.money,
        };
        res = await UserDao.rechargeWx(info);
      } else {
        if (Platform.isAndroid) {
          fluwx.launchWeChatMiniProgram(
              username: DefaultConfig().configs.WXMP_SOURCE_ID,
              path: "homePages/wxRecharge/wxRechargeCreate/wxRechargeCreate?id=${user?.id}&money=${form.money}",
              miniProgramType: WXMiniProgramType.RELEASE);
        } else {
          var data = await UserDao.wxmpJumpSchema({
            'path': 'homePages/wxRecharge/wxRechargeCreate/wxRechargeCreate',
            'query': 'id=${user?.id}&money=${form.money}&showBack=1'
          });
          var uri = Uri.parse(data.data);
          launchUrl(uri, mode: LaunchMode.externalApplication);
        }
      }
    } else if ('wallet' == form.payMethod) {
      // CommonUtils.passwordInput(context,
      //     content: buildContent(), title: '请输入支付密码', buttonLabel: '忘记密码');
      if (double.parse(widget.balanceNum) <= 0) {
        setState(() {
          form.isAgree = false;
        });
        return Fluttertoast.showToast(msg: '无提现转入金额');
      }
      _showBottomSheetCallback();
      setState(() {
        isShow = true;
      });
    } else if ('alipay' == form.payMethod) {
      var resultAli = await tobias.isAliPayInstalled;
      if (!resultAli) {
        return Fluttertoast.showToast(msg: '请安装支付宝客户端');
      }
      info = {
        'appId': DefaultConfig().configs.ALI_APP_ID,
        'money': form.money,
      };
      res = await UserDao.rechargeAli(info);
    }
    if (res != null && res.result) {
      if ('wx' == form.payMethod) {
        toWxPay(res.data);
      } else if ('wallet' == form.payMethod) {
      } else if ('alipay' == form.payMethod) {
        toAlipay(res.data);
      }
    }
  }

  void toWxPay(orderInfo) async {
    RechargeEntity priceEntity = RechargeEntity.fromJson(orderInfo);
    fluwx
        .payWithWeChat(
          appId: priceEntity.appId!,
          partnerId: priceEntity.partnerid!,
          prepayId: priceEntity.prepayid!,
          packageValue: priceEntity.packageValue!,
          nonceStr: priceEntity.noncestr!,
          timeStamp: int.parse(priceEntity.timestamp!),
          sign: priceEntity.sign!,
        )
        .then((data) {})
        .catchError((e) {
      Fluttertoast.showToast(msg: e);
    });
  }

  void toAlipay(orderInfo) async {
    tobias.pay(orderInfo).then((payResult) {
      if (payResult['resultStatus'] == '9000') {
        Fluttertoast.showToast(msg: '充值成功', gravity: ToastGravity.CENTER);
      } else {
        Fluttertoast.showToast(msg: payResult['memo']);
      }
    });
  }

  void toBalance(String payPassword) async {
    var money = double.parse(widget.balanceNum);
    double endMoney = money * 1000;
    Map<String, dynamic> info = {'payPassword': payPassword, 'money': endMoney};
    var res = UserDao.rechargeBal(info);
    if (res != null && res.result) {
      goPayResult();
    }
  }

  buildContent() {
    List<Widget> widgets = [];
    widgets.add(Container());
    return widgets;
  }

  buildButtonLabel() {
    return TextButton(onPressed: () {}, child: Text('忘记密码'));
  }

  buildChargeList() {
    List<Widget> widgets = <Widget>[];
    widgets = List.generate(chargeList.length, (index) {
      return _renderCard(chargeList[index], index);
    });
    return widgets;
  }

  _renderCard(item, index) {
    int rowNumber = 3;
    double width = (MediaQuery.of(context).size.width - 60) / rowNumber;
    return InkWell(
      onTap: () {
        setState(() {
          form.money = item['money_li'];
          form.moneyYuan = item['money_yuan'];
        });
      },
      child: Container(
        width: width,
        height: 48,
        padding: EdgeInsets.fromLTRB(10, 6, 10, 6),
        margin: EdgeInsets.only(right: (index + 1) % rowNumber == 0 ? 0 : 10, top: 10),
        decoration: BoxDecoration(
            color: item['money_li'] == form.money ? DefaultConfig().configs.PRIMARY_COLOR_LIGHT : Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(7)),
            border:
                Border.all(color: item['money_li'] == form.money ? Theme.of(context).primaryColor : Color(0xFFB5B5B5))),
        child: Center(
          child: Text('${item['money_yuan']}元',
              style: TextStyle(
                  color: item['money_li'] == form.money ? Theme.of(context).primaryColor : Colors.black, fontSize: 15)),
        ),
      ),
    );
  }

  buildPayMethod() {
    List<Widget> widgets = <Widget>[];
    widgets = List.generate(payMethodList.length, (index) {
      return _renderList(payMethodList[index], index);
    });
    widgets.add(InkWell(
        onTap: () {
          int index = payMethodList.indexWhere((item) => item['code'] == 'wx');
          if (index < 0) {
            payMethodList.add({'code': 'wx', 'name': '微信', 'logo': 'wx'});
            showMore = false;
            setState(() {});
          }
        },
        child: Offstage(
          offstage: !showMore,
          child: Container(
            padding: EdgeInsets.only(bottom: 10, top: 10),
            width: double.infinity,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('更多支付方式', style: TextStyle(color: Theme.of(context).primaryColor)),
                Icon(Icons.keyboard_arrow_down_sharp, color: Theme.of(context).primaryColor)
              ],
            ),
          ),
        )));
    return widgets;
  }

  _renderList(item, index) {
    return InkWell(
      onTap: () {
        setState(() {
          form.payMethod = item['code'];
        });
      },
      child: Container(
        padding: EdgeInsets.only(bottom: 10, top: 10),
        decoration: BoxDecoration(
            color: Color(0xFFF5F5F5),
            border: Border(
                bottom: BorderSide(
                    color: (index == payMethodList.length - 1 && index != 0) ? Colors.transparent : Color(0xFFDCDCDC),
                    width: 1.0))),
        child: CommonRowWidget(
          leftWidget: Row(
            children: [
              LocalImageUtil.getImageAsset(item['logo'], width: 20),
              Padding(padding: EdgeInsets.only(left: 6)),
              item['code'] == 'wallet'
                  ? RichText(
                      maxLines: 1,
                      text: TextSpan(children: <TextSpan>[
                        TextSpan(text: '${item['name']}', style: TextStyle(fontSize: 14, color: Colors.black)),
                        TextSpan(text: '(可使用', style: TextStyle(fontSize: 12, color: Colors.black)),
                        TextSpan(
                            text: widget.balanceNum,
                            style: TextStyle(
                                color: Theme.of(context).primaryColor, fontSize: 13.0, fontWeight: FontWeight.w600)),
                        TextSpan(text: '元)', style: TextStyle(fontSize: 12, color: Colors.black)),
                      ]))
                  : RichText(
                      text: TextSpan(children: <TextSpan>[
                      TextSpan(text: '${item['name']}', style: TextStyle(fontSize: 14, color: Colors.black))
                    ]))
            ],
          ),
          rightWidget: form.payMethod == item['code']
              ? Icon(Icons.check, size: 20, color: Theme.of(context).primaryColor)
              : Container(),
        ),
      ),
    );
  }

  _onKeyDown(MyKeyEvent data) {
    if (data.isDelete()) {
      // 如果点击了删除按钮，则将密码进行修改
      if (payPassword.length > 0) {
        payPassword = payPassword.substring(0, payPassword.length - 1);
        setState(() {});
      }
    } else if (data.isCommit()) {
      // 点击了确定按钮时
      if (payPassword.length != 6) {
        return;
      }
      confirmPay();
    } else {
      //点击了数字按钮时  将密码进行完整的拼接
      if (payPassword.length < 6) {
        payPassword += data.key;
      }
      setState(() {});
    }
  }

  confirmPay() {
    toBalance(payPassword);
    closePassword();
  }

  closePassword() {
    Navigator.of(context).pop();
    setState(() {
      isShow = false;
      payPassword = '';
    });
  }

  goPayResult() {
    NavigatorUtils.goPayResultPage(context, PayResult.SUCCESS, double.parse(form.money));
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        Navigator.of(context).pop(true);
        return true;
      },
      child: Scaffold(
        key: _scaffoldKey,
        backgroundColor: DefaultConfig().configs.BG_COLOR,
        appBar: new AppCustomerBar(
          title: AppbarTitle(
            title: '账户充值',
            isCenter: true,
          ),
          actions: [
            Container(
              width: 60,
            )
          ],
        ),
        body: Stack(
          children: [
            Column(
              children: <Widget>[
                Expanded(
                    child: SingleChildScrollView(
                  child: Container(
                    child: Column(
                      children: [
                        /// 金额选择
                        Container(
                          margin: EdgeInsets.all(10),
                          padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
                          decoration:
                              BoxDecoration(color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(7))),
                          child: Column(
                            children: [
                              Container(
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  '请选择充值金额',
                                  style: TextStyle(fontSize: 14, color: Colors.black),
                                ),
                              ),
                              Wrap(
                                children: buildChargeList(),
                              )
                            ],
                          ),
                        ),

                        /// 支付方式
                        Container(
                          margin: EdgeInsets.fromLTRB(10, 0, 10, 10),
                          padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
                          decoration:
                              BoxDecoration(color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(7))),
                          child: Column(
                            children: [
                              Container(
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  '请选择支付方式',
                                  style: TextStyle(fontSize: 14, color: Colors.black),
                                ),
                              ),
                              Padding(padding: EdgeInsets.only(top: 10)),
                              Container(
                                decoration: BoxDecoration(
                                    color: DefaultConfig().configs.BG_COLOR,
                                    borderRadius: BorderRadius.all(Radius.circular(7))),
                                padding: EdgeInsets.only(left: 10, right: 10),
                                child: Column(
                                  children: buildPayMethod(),
                                ),
                              ),
                              GestureDetector(
                                behavior: HitTestBehavior.opaque,
                                onTap: () {
                                  setState(() {
                                    form.isAgree = !form.isAgree;
                                  });
                                },
                                child: Container(
                                  padding: EdgeInsets.only(left: 5, top: 10),
                                  alignment: Alignment.centerLeft,
                                  child: Row(
                                    children: [
                                      Container(
                                        height: 30.0,
                                        child: Container(
                                            width: 10.0,
                                            height: 10.0,
                                            margin: EdgeInsets.fromLTRB(0, 0.0, 10.0, 0.0),
                                            child: IgnorePointer(
                                              child: Radio(
                                                value: true,
                                                groupValue: form.isAgree,
                                                activeColor: Theme.of(context).primaryColor,
                                                onChanged: (bool? value) {
                                                  setState(() {
                                                    form.isAgree = !form.isAgree;
                                                  });
                                                },
                                              ),
                                            )),
                                      ),
                                      RichText(
                                          maxLines: 2,
                                          text: TextSpan(children: <TextSpan>[
                                            TextSpan(
                                                text: '我已阅读并同意',
                                                style: TextStyle(color: Colors.grey.shade600, fontSize: 13.0)),
                                            TextSpan(
                                                text: '《充值协议》',
                                                recognizer: recognizer,
                                                style: TextStyle(
                                                    color: Theme.of(context).primaryColor,
                                                    fontSize: 13.0,
                                                    fontWeight: FontWeight.w600)),
                                          ]))
                                    ],
                                  ),
                                ),
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                )),
                BottomBtnWidget(
                    showShadow: false,
                    // title: form.isAgree ? '充值${form.moneyYuan}元' : '请选择充值金额',
                    title: form.isAgree ? '确认充值' : '请选择充值金额',
                    type: form.isAgree ? ButtonType.primary : ButtonType.primaryDisabled,
                    action: () => throttleUtil.throttle(() {
                          recharge();
                        })),
              ],
            ),
            Positioned(
                top: isShow ? 0 : MediaQuery.of(context).size.height,
                left: 0,
                child: Container(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height,
                  color: Color(0x21000000),
                )),
            Positioned(
                left: isShow ? 30 : MediaQuery.of(context).size.width,
                top: 100,
                child: Stack(
                  children: [
                    Container(
                      width: MediaQuery.of(context).size.width - 60,
                      height: 186,
                      decoration:
                          BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(7.0)), color: Colors.white),
                      child: new Column(
                        children: <Widget>[
                          new Padding(
                            padding: const EdgeInsets.only(top: 20.0, bottom: 20),
                            child: new Text(
                              '请输入支付密码',
                              style:
                                  new TextStyle(fontSize: 17.0, color: Color(0xff000000), fontWeight: FontWeight.w400),
                            ),
                          ),

                          ///密码框
                          new Padding(
                            padding: const EdgeInsets.only(top: 15.0, bottom: 15),
                            child: _buildPwd(payPassword),
                          ),
                          TextButton(
                            style: ButtonStyle(
                              minimumSize: MaterialStateProperty.all(Size(200, 40)),
                            ),
                            onPressed: () {
                              NavigatorUtils.goPayForGetPwdPage(context);
                            },
                            child: Text(
                              '忘记密码',
                              style: TextStyle(fontSize: 16),
                            ),
                          )
                        ],
                      ),
                    ),
                    Positioned(
                        left: 7,
                        top: 7,
                        child: InkWell(
                          onTap: () {
                            closePassword();
                          },
                          child: Container(
                            width: 30,
                            height: 30,
                            child: Icon(Icons.close_outlined),
                          ),
                        ))
                  ],
                ))
          ],
        ), // This trailing comma makes auto-formatting nicer for build methods.
      ),
    );
  }

  Widget _buildPwd(var pwd) {
    return new GestureDetector(
      child: new Container(
        width: 250.0,
        height: 40.0,
//      color: Colors.white,
        child: new CustomJPasswordField(pwd),
      ),
      onTap: () {
        _showBottomSheetCallback();
      },
    );
  }

  void _showBottomSheet() {
    setState(() {
      // disable the button
      _showBottomSheetCallback = () {};
    });
    _scaffoldKey.currentState
        ?.showBottomSheet((BuildContext context) {
          return new MyKeyboard(_onKeyDown);
        })
        .closed
        .whenComplete(() {
          if (mounted) {
            setState(() {
              // re-enable the button
              _showBottomSheetCallback = _showBottomSheet;
            });
          }
        });
  }
}
