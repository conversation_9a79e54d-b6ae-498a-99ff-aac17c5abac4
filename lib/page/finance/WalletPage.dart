
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/wallet_entity.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/utils/ThrottleUtil.dart';
import 'package:cabinet_flutter_app/page/finance/CashOutPage.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CommonRowWidget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class WalletPage extends StatefulWidget {
  WalletPage({Key? key}) : super(key: key);

  @override
  _WalletPageState createState() => _WalletPageState();
}

class _WalletPageState extends State<WalletPage> {
  ThrottleUtil throttleUtil = ThrottleUtil();
  late WalletEntity data;
  int balance = 0;
  String balanceNum = '';
  int totalWithdrawalMoney = 0;
  String totalWithdrawalMoneyNum = '';
  int frozenMoney = 0;
  String frozenMoneyNum = '';
  bool isCourier = true;

  @override
  void initState() {
    super.initState();
    getWealthInfo();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Future<void> didChangeDependencies() async {
    super.didChangeDependencies();
    isCourier = CheckUtils.isCourier(context);
    if(!isCourier){
      var userRes = await UserDao.getUserInfoLocal();
      if (userRes != null) {
        UserEntity? userEntity = userRes.data;
        if (userEntity?.hasAdmin == 1 && userEntity?.hasWallet == 1) {
          isCourier = true;
        }
      }
    }
  }

  getWealthInfo() async {
    var res = await UserDao.getWealthInfo();
    if (res != null && res.result) {
      data = WalletEntity.fromJson(res.data);
      setState(() {
        balance = data.balance!;
        balanceNum = (balance / 1000).toStringAsFixed(2);
        totalWithdrawalMoney = data.totalWithdrawalMoney!;
        totalWithdrawalMoneyNum = (data.totalWithdrawalMoney! / 1000).toStringAsFixed(2);
        frozenMoney = data.frozenMoney!;
        frozenMoneyNum = (data.frozenMoney! / 1000).toStringAsFixed(2);
      });
    }
  }

  /// 充值
  recharge() async {
    final result = await NavigatorUtils.goRechargePage(context, '');
    if (result) {
      getWealthInfo();
    }
    // Navigator.push(
    //         context,
    //         new CupertinoPageRoute(
    //             builder: (context) => new RechargePage(waitWithdrawalMoneyNum),
    //             settings: RouteSettings(name: 'rechargePage')))
    //     .then((value) => {
    //       getWealthInfo()
    //     }
    //       );
  }

  /// 提现
  cashOut() {
    Navigator.push(
            context,
            new CupertinoPageRoute(
                builder: (context) => new CashOutPage(), settings: RouteSettings(name: 'cashOutPage')))
        .then((value) => {getWealthInfo()});
  }

  @override
  Widget build(BuildContext context) {
    double width = (MediaQuery.of(context).size.width - 25) / 2;
    return Scaffold(
      backgroundColor: DefaultConfig().configs.BG_COLOR,
      appBar: new AppCustomerBar(
        title: AppbarTitle(
          title: '我的钱包',
          isCenter: true,
        ),
        actions: [
          Container(
            width: 60,
          )
        ],
      ),
      body: Container(
        margin: EdgeInsets.fromLTRB(10, 15, 10, 10),
        child: Column(
          children: <Widget>[
            Row(
              children: [
                Container(
                  width: width,
                  height: 110,
                  padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
                  decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(7))),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '余额（元）',
                        style: TextStyle(fontSize: AppConstant.smallTextSize, fontWeight: FontWeight.w800),
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            '$balanceNum',
                            style: TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
                          ),
                          Offstage(
                            offstage: frozenMoney == 0,
                            child: Text(
                              '(冻结$frozenMoneyNum)',
                              style: TextStyle(
                                fontSize: AppConstant.tinyTextSize,
                              ),
                            ),
                          )
                        ],
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                              height: 32,
                              child: OutlinedButton(
                                child: Text('充值'),
                                style: ButtonStyle(
                                  minimumSize: MaterialStateProperty.all(Size(55, 32)),
                                  shape: MaterialStateProperty.all(StadiumBorder()),
                                  foregroundColor: MaterialStateProperty.resolveWith((states) {
                                    return Colors.white;
                                  }),
                                  backgroundColor: MaterialStateProperty.resolveWith((states) {
                                    if (states.contains(MaterialState.pressed)) {
                                      return DefaultConfig().configs.PRIMARY_COLOR_LIGHT;
                                    }
                                    return Theme.of(context).primaryColor;
                                  }),
                                  side: MaterialStateProperty.all(BorderSide(color: Theme.of(context).primaryColor)),
                                ),
                                onPressed: () => throttleUtil.throttle(() {
                                  NavigatorUtils.goRechargePage(context, balanceNum);
                                }),
                              )),
                          Offstage(
                            offstage: !isCourier,
                            child: Row(
                              children: [
                                SizedBox(
                                  width: 20,
                                ),
                                Container(
                                    height: 32,
                                    child: OutlinedButton(
                                      child: Text('提现'),
                                      style: ButtonStyle(
                                        minimumSize: MaterialStateProperty.all(Size(55, 32)),
                                        shape: MaterialStateProperty.all(StadiumBorder()),
                                        foregroundColor: MaterialStateProperty.resolveWith((states) {
                                          return Colors.white;
                                        }),
                                        backgroundColor: MaterialStateProperty.resolveWith((states) {
                                          if (states.contains(MaterialState.pressed)) {
                                            return DefaultConfig().configs.PRIMARY_COLOR_LIGHT;
                                          }
                                          return Theme.of(context).primaryColor;
                                        }),
                                        side: MaterialStateProperty.all(BorderSide(color: Theme.of(context).primaryColor)),
                                      ),
                                      onPressed: () => throttleUtil.throttle(() {
                                        cashOut();
                                      }),
                                    ))
                              ],
                            ),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
                Padding(padding: EdgeInsets.only(left: 5)),
                Container(
                  width: width,
                  height: 110,
                  padding: EdgeInsets.fromLTRB(10, 10, 10, 5),
                  decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(7))),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text('提现总金额 (元)', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w800)),
                      Padding(padding: EdgeInsets.only(top: 4)),
                      Text('$totalWithdrawalMoneyNum', style: TextStyle(fontSize: 20, fontWeight: FontWeight.w500)),
                    ],
                  ),
                ),
              ],
            ),
            Container(
              margin: EdgeInsets.only(top: 15),
              padding: EdgeInsets.fromLTRB(10, 10, 10, 15),
              decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(7))),
              child: Column(
                children: [
                  InkWell(
                    onTap: () {
                      NavigatorUtils.goCapitalFlowPage(context);
                    },
                    child: Container(
                      padding: EdgeInsets.only(bottom: 10),
                      decoration:
                          BoxDecoration(border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0))),
                      child: CommonRowWidget(
                        label: '资金明细',
                        labelStyle: TextStyle(fontSize: 15, color: Colors.black),
                        rightWidget: Icon(Icons.arrow_forward_ios_sharp, size: 15),
                      ),
                    ),
                  ),
                  Offstage(
                    offstage: !isCourier,
                    child: InkWell(
                      onTap: () {
                        NavigatorUtils.goCashOutRecordPage(context);
                      },
                      child: Container(
                        padding: EdgeInsets.only(top: 8, bottom: 12),
                        decoration:
                            BoxDecoration(border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0))),
                        child: CommonRowWidget(
                          label: '提现记录',
                          labelStyle: TextStyle(fontSize: 15, color: Colors.black),
                          rightWidget: Icon(Icons.arrow_forward_ios_sharp, size: 15),
                        ),
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      NavigatorUtils.goPaymentSettingPage(context);
                    },
                    child: Container(
                      padding: EdgeInsets.only(top: 10),
                      child: CommonRowWidget(
                        label: '支付设置',
                        labelStyle: TextStyle(fontSize: 15, color: Colors.black),
                        rightWidget: Icon(Icons.arrow_forward_ios_sharp, size: 15),
                      ),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }
}
