
import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:flutter/material.dart';

class PayResultPage extends StatefulWidget {
  final PayResult result;
  final double money;

  PayResultPage(this.result, this.money, {Key? key}) : super(key: key);

  @override
  _PayResultPageState createState() => _PayResultPageState();
}

class _PayResultPageState extends State<PayResultPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DefaultConfig().configs.BG_COLOR,
      appBar: new AppCustomerBar(
        title: AppbarTitle(
          title: '充值结果',
          isCenter: true,
        ),
        actions: [
          Container(
            width: 60,
          )
        ],
      ),
      body: Container(
        alignment: AlignmentDirectional.center,
        height: 240,
        decoration: BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(7)), color: Colors.white),
        margin: EdgeInsets.all(10),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            LocalImageUtil.getImageAsset(widget.result == PayResult.SUCCESS ? 'paySuccess' : 'payFail', width: 50),
            Padding(padding: EdgeInsets.only(top: 18)),
            Text(widget.result == PayResult.SUCCESS ? '成功充值${widget.money}元' : '充值失败!',
                style: TextStyle(color: Colors.black, fontWeight: FontWeight.w400, fontSize: 17)),
            Padding(padding: EdgeInsets.only(top: 24)),
            MaterialButton(
              minWidth: 128,
              height: 32,
              color: Theme.of(context).primaryColor,
              textColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(28.0),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                widget.result == PayResult.SUCCESS ? '确定' : '重新充值',
                style: TextStyle(fontSize: 16),
              ),
            )
          ],
        ),
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }
}
