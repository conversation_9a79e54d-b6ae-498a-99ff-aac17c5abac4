import 'dart:async';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/FormValidateUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/RegExpUtil.dart';
import 'package:cabinet_flutter_app/common/utils/ThrottleUtil.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/BottomBtnWidget.dart';
import 'package:cabinet_flutter_app/widget/BottomFormInputWidget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:fluttertoast/fluttertoast.dart';

class PayForGetPwdPage extends StatefulWidget {
  PayForGetPwdPage({Key? key}) : super(key: key);

  @override
  _PayForGetPwdPageState createState() => _PayForGetPwdPageState();
}

class _PayForGetPwdPageState extends State<PayForGetPwdPage> {
  GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  TextEditingController passwordController = new TextEditingController();
  TextEditingController passwordNextController = new TextEditingController();
  TextEditingController passwordAgainController = new TextEditingController();
  ThrottleUtil throttleUtil = ThrottleUtil();
  var code = '', passwordNext = '', passwordAgain = '';

  bool isButtonEnable = true; //按钮状态  是否可点击
  String buttonText = '发送验证码'; //初始文本
  int count = 60; //初始倒计时时间
  Timer? timer; //倒计时的计时器
  TextEditingController mController = TextEditingController();
  late UserEntity user;
  var _userName = '';

  void _buttonClickListen() {
    setState(() {
      if (isButtonEnable) {
        //当按钮可点击时
        isButtonEnable = false; //按钮状态标记
        _initTimer();
        return null; //返回null按钮禁止点击
      } else {
        //当按钮不可点击时
//        debugPrint('false');
        return null; //返回null按钮禁止点击
      }
    });
  }

  void _initTimer() {
    timer = new Timer.periodic(Duration(seconds: 1), (Timer timer) {
      count--;
      setState(() {
        if (count == 0) {
          timer.cancel(); //倒计时结束取消定时器
          isButtonEnable = true; //按钮可点击
          count = 60; //重置时间
          buttonText = '发送验证码'; //重置按钮文本
        } else {
          buttonText = '重新发送($count)'; //更新文本内容
        }
      });
    });
  }

  /// 发送验证码
  Future<void> sendCode() async {
    bool matched = RegExpUtil.isPhone(_userName);
    if (!matched) {
      Fluttertoast.showToast(msg: '手机号格式错误');
      return;
    }
    Map<String, dynamic> data = {"loginName": _userName, 'smsTplCode': 'RESET_PAY_SMS'};
    var res = await UserDao.sendSms(data);
    if (res != null && res.result) {
      if (res.data) {
        Fluttertoast.showToast(msg: '验证码发送成功,请查收');
        setState(() {
          _buttonClickListen();
        });
      } else {
        Fluttertoast.showToast(msg: '验证码发送失败请重试');
      }
    }
  }

  @override
  void initState() {
    super.initState();
    init();
  }

  init() async {
    _userName = await LocalStorage.get<String>(DefaultConfig().configs.USER_NAME_KEY);
    setState(() {});
  }

  @override
  void dispose() {
    super.dispose();
    timer?.cancel();
    //销毁计时器
    mController.dispose();
  }

  buildList() {
    List<Widget> widgets = [];
    widgets.add(Container(
      margin: EdgeInsets.only(top: 1, bottom: 1),
      color: Colors.white,
      child: Row(
        children: [
          Expanded(
            child: Container(
              margin: EdgeInsets.only(top: 1, bottom: 1),
              child: BottomFormInputWidget(
                value: code,
                labelWidth: 0,
                hintText: '请输入验证码',
                textInputType: TextInputType.number,
                controller: passwordNextController,
                padding: EdgeInsets.fromLTRB(5, 5, 0, 5),
                onChanged: (String? value) {
                  setState(() {
                    code = value!;
                  });
                },
              ),
            ),
          ),
          Container(
            width: 120,
            child: TextButton(
              // disabledColor: Colors.grey.withOpacity(0.1),
              // //按钮禁用时的颜色
              // disabledTextColor: Colors.deepOrange,
              // //按钮禁用时的文本颜色
              // textColor: isButtonEnable
              //     ? Colors.deepOrange
              //     : Colors.black.withOpacity(0.2),
              // //文本颜色
              // color: isButtonEnable ? Colors.white : Colors.white,
              // //按钮的颜色
              // splashColor: isButtonEnable
              //     ? Colors.white.withOpacity(0.1)
              //     : Colors.transparent,
              onPressed: () {
                sendCode();
              },
//                        child: Text('重新发送 (${secondSy})'),
              child: Text(
                '$buttonText',
                style: TextStyle(
                  fontSize: 13,
                ),
              ),
            ),
          )
        ],
      ),
    ));
    widgets.add(Container(
      child: BottomFormInputWidget(
        value: passwordNext,
        labelWidth: 0,
        hintText: '请输入6位支付密码',
        validator: (value) => FormValidateUtil.payPasswordValidate(value!),
        textInputType: TextInputType.number,
        controller: passwordController,
        padding: EdgeInsets.fromLTRB(5, 5, 0, 5),
        onChanged: (String? value) {
          setState(() {
            passwordNext = value!;
          });
        },
      ),
    ));
    widgets.add(Container(
      child: BottomFormInputWidget(
        value: passwordAgain,
        labelWidth: 0,
        hintText: '请再次输入6位支付密码',
        validator: (value) => FormValidateUtil.payPasswordValidate(value!),
        textInputType: TextInputType.number,
        controller: passwordAgainController,
        padding: EdgeInsets.fromLTRB(5, 5, 0, 5),
        onChanged: (String? value) {
          setState(() {
            passwordAgain = value!;
          });
        },
      ),
    ));
    return widgets;
  }

  sureUpload() async {
    if (passwordAgain != passwordNext) {
      Fluttertoast.showToast(msg: '两次密码不一致');
      return;
    }
    Map<String, dynamic> info = {'smsCode': code, 'payPassword': passwordAgain};
    bool validator = _formKey.currentState?.validate() ?? false;
    if (validator) {
      LoadingUtil(
        status: '设置中...',
      ).show(context);
      var res = await UserDao.forgetPayPassword(info);
      LoadingUtil.dismiss(context);
      if (res != null && res.result) {
        Fluttertoast.showToast(msg: '修改成功');
        Navigator.pop(context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return new StoreBuilder<AppState>(builder: (context, store) {
      return Scaffold(
          backgroundColor: DefaultConfig().configs.BG_COLOR,
          appBar: new AppCustomerBar(
            title: new AppbarTitle(
              title: '支付设置',
              isCenter: true,
            ),
            actions: <Widget>[
              new Container(
                width: 60.0,
              )
            ],
          ),
          body: new Column(
            children: <Widget>[
              Container(
                child: Text('手机号：$_userName'),
                width: double.infinity,
                color: Colors.white,
                padding: EdgeInsets.only(left: 10, top: 15, bottom: 15),
                margin: EdgeInsets.only(top: 15),
              ),
              Expanded(
                  child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(children: buildList()),
                ),
              )),
              Offstage(
                offstage: false,
                child: Container(
                  child: Column(
                    children: [
                      BottomBtnWidget(
                          showShadow: false,
                          title: '确定',
                          type: CheckUtils.isNotNull(code) &&
                                  CheckUtils.isNotNull(passwordNext) &&
                                  CheckUtils.isNotNull(passwordAgain)
                              ? ButtonType.primary
                              : ButtonType.primaryDisabled,
                          action: () => throttleUtil.throttle(() {
                                sureUpload();
                              })),
                    ],
                  ),
                ),
              )
            ],
          ));
    });
  }
}
