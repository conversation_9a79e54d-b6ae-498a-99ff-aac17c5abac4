import 'dart:async';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/trade_record_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/wallet_summary_entity.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppListState.dart';
import 'package:cabinet_flutter_app/widget/AppPullLoadWidget.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/NoResult.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CommonRowWidget.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CustomerDropDownSelectWidget.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class CapitalQueryForm {
  late String startTime;
  late String endTime;
  late String dateRangeType;
  late String type;
}

class CapitalFlowPage extends StatefulWidget {
  CapitalFlowPage({Key? key}) : super(key: key);

  @override
  _CapitalFlowPage createState() => _CapitalFlowPage();
}

class _CapitalFlowPage extends State<CapitalFlowPage>
    with
        AutomaticKeepAliveClientMixin<CapitalFlowPage>,
        AppListState<CapitalFlowPage>,
        WidgetsBindingObserver,
        SingleTickerProviderStateMixin {
  ScrollController _controller = new ScrollController();

  final GlobalKey<ScaffoldState> capitalFlowPageKey = GlobalKey<ScaffoldState>();
  CapitalQueryForm queryForm = new CapitalQueryForm();
  int total = 0;
  Map<String, dynamic> dateRangeTypeMap = {
    'today': '今日',
    'yesterday': '昨日',
    'lastOneWeek': '近一周',
    'lastOneMonth': '近一月',
    'lastThreeMonth': '近三月',
    'lastOneYear': '近一年',
  };
  late WalletSummaryEntity data = new WalletSummaryEntity();

  void initState() {
    super.initState();
    init();
  }

  init() {
    queryForm.dateRangeType = 'today';
    queryForm.startTime = DateFormat('yyyy-MM-dd').format(DateTime.now());
    queryForm.endTime = DateFormat("yyyy-MM-dd").format(DateTime.now());
    queryForm.type = '0';
    _getTotalData();
  }

  // 上拉加载更多
  _getData() async {
    Map<String, dynamic> info = {
      'tradeType': queryForm.type,
      'beginDate': queryForm.startTime,
      'endDate': queryForm.endTime,
      'current': page.toString(),
      'size': 10,
    };
    var res = await UserDao.fundDetails(info);
    if (res != null && res.result) {
      total = int.parse(res.data['total']);
      return new DataResult(res.data['records'], true, total: int.parse(res.data['total']));
    }
  }

  _getTotalData() async {
    Map<String, dynamic> info = {
      'beginDate': queryForm.startTime,
      'endDate': queryForm.endTime,
    };
    var res = await UserDao.totalData(info);
    if (res != null && res.result) {
      data = WalletSummaryEntity.fromJson(res.data);
    }
  }

  Future<Null> onLoadMore() async {
    if (isLoading) {
      return null;
    }
    isLoading = true;
    page++;
    var res = await requestLoadMore();
    if (res != null && res.result) {
      setState(() {
        pullLoadWidgetControl.dataList.addAll(res.data);
      });
    }
    resolveDataResult(res);
    isLoading = false;
    return null;
  }

  @protected
  Future<Null> handleRefresh() async {
    if (isLoading) {
      return null;
    }
    refreshIndicatorKey.currentState?.show();
    isLoading = true;
    page = 1;
    var res = await requestRefresh();
    resolveRefreshResult(res);
    resolveDataResult(res);
    if (res.next != null) {
      var resNext = await res.next;
      resolveRefreshResult(resNext);
      resolveDataResult(resNext);
    }
    isLoading = false;
    return null;
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  _renderEventItem(index) {
    TradeRecordEntity item = TradeRecordEntity.fromJson(dataList[index]);
    bool isIncome = item.tradeType == 1 || item.tradeType == 3;
    item.isIncome = isIncome;
    return InkWell(
      onTap: () {
        // NavigatorUtils.goCapitalFlowDetailPage(context, item);
        NavigatorUtils.goCapitalFlowDetailListPage(context, item);
      },
      child: Container(
        color: Colors.white,
        padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
        child: Container(
          padding: EdgeInsets.only(bottom: 10),
          decoration: BoxDecoration(border: Border(bottom: BorderSide(width: 1, color: Color(0xFFEEEEEE)))),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                      '${DefaultConfig().configs.TRADE_SUBJECT_MAP[item.tradeSubject] ?? ''}' +
                          '${(item.remark ?? '') != '' ? '（${item.remark ?? ''}）' : ''}',
                      style: TextStyle(fontSize: 16, color: Colors.black, fontWeight: FontWeight.w400)),
                  Padding(padding: EdgeInsets.only(top: 6)),
                  Text('${item.updateTime ?? ''}',
                      style: TextStyle(fontSize: 13, color: Color(0xFF999999), fontWeight: FontWeight.w400)),
                ],
              ),
              // Container(
              //   child: Text('${(item['tradeMoney'] / 1000).toStringAsFixed(2) ?? ''}',
              //       style: TextStyle(
              //           fontSize: 16, color: isIncome ? Theme.of(context).primaryColor : Colors.black, fontWeight: FontWeight.w400)),
              // )
              Container(
                  child: RichText(
                text: TextSpan(
                    style: TextStyle(
                        fontSize: 16,
                        color: isIncome ? Theme.of(context).primaryColor : Colors.black,
                        fontWeight: FontWeight.w400),
                    children: <TextSpan>[
                      TextSpan(text: isIncome ? '+' : '-'),
                      TextSpan(
                          text: formatData((item.tradeMoney / 1000).toStringAsFixed(3)) == '0'
                              ? '${(item.tradeMoney / 1000).toStringAsFixed(2)}'
                              : '${(item.tradeMoney / 1000).toStringAsFixed(3)}'),
                    ]),
              ))
            ],
          ),
        ),
      ),
    );
  }

  String formatData(String number) {
    return number.substring(number.length - 1);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      backgroundColor: Colors.grey.shade100,
      key: capitalFlowPageKey,
      appBar: AppCustomerBar(
        title: AppbarTitle(
          title: '资金明细',
          isCenter: true,
        ),
        actions: <Widget>[Container(width: 60)],
      ),
      body: Stack(
        children: <Widget>[
          Column(
            children: <Widget>[
              Container(
                  margin: EdgeInsets.only(top: 10),
                  padding: EdgeInsets.only(left: 10, right: 10),
                  alignment: Alignment.centerLeft,
                  decoration: BoxDecoration(
                      color: Colors.white, border: Border(bottom: BorderSide(width: 1, color: Color(0xFFEEEEEE)))),
                  child: CommonRowWidget(
                    leftWidget: Row(
                      children: [
                        Container(
                          constraints: BoxConstraints(maxWidth: 70),
                          child: CustomerDropDownSelectWidget(
                              hintText: '日期',
                              value: queryForm.dateRangeType,
                              map: dateRangeTypeMap,
                              cb: (item) {
                                queryForm.dateRangeType = item;
                                int durationStart = 0;
                                int durationEnd = 0;
                                if (item == 'today') {
                                } else if (item == 'yesterday') {
                                  durationStart = 1;
                                  durationEnd = 1;
                                } else if (item == 'lastOneWeek') {
                                  durationStart = 7;
                                } else if (item == 'lastOneMonth') {
                                  durationStart = 30;
                                } else if (item == 'lastThreeMonth') {
                                  durationStart = 90;
                                } else if (item == 'lastOneYear') {
                                  durationStart = 365;
                                }
                                queryForm.startTime = DateFormat("yyyy-MM-dd").format(
                                    DateTime.parse(DateFormat("yyyy-MM-dd HH:mm:ss").format(DateTime.now()))
                                        .subtract(Duration(days: durationStart)));
                                queryForm.endTime = DateFormat("yyyy-MM-dd").format(
                                    DateTime.parse(DateFormat("yyyy-MM-dd HH:mm:ss").format(DateTime.now()))
                                        .subtract(Duration(days: durationEnd)));
                                setState(() {});
                                handleRefresh();
                                _getTotalData();
                              }),
                        ),
                        Padding(padding: EdgeInsets.only(left: 5)),
                        Container(
                          constraints: BoxConstraints(maxWidth: 75),
                          child: CustomerDropDownSelectWidget(
                              hintText: '资金类型',
                              value: '${queryForm.type}',
                              map: DefaultConfig().configs.TRADE_TYPE_MAP,
                              cb: (item) {
                                setState(() {
                                  queryForm.type = item;
                                });
                                handleRefresh();
                              }),
                        )
                      ],
                    ),
                    rightWidget: Row(
                      children: [
                        Column(
                          children: [
                            Text(
                                formatData(((data.outcomeAmount ?? 0) / 1000).toStringAsFixed(3)) == '0'
                                    ? '  消费: ${((data.outcomeAmount ?? 0) / 1000).toStringAsFixed(2)}元  '
                                    : '  消费: ${((data.outcomeAmount ?? 0) / 1000).toStringAsFixed(3)}元  ',
                                style: TextStyle(fontSize: 12, color: Color(0xFF858585))),
                            Text(
                                formatData(((data.incomeAmount ?? 0) / 1000).toStringAsFixed(3)) == '0'
                                    ? '  收入: ${((data.incomeAmount ?? 0) / 1000).toStringAsFixed(2)}元  '
                                    : '  收入: ${((data.incomeAmount ?? 0) / 1000).toStringAsFixed(3)}元  ',
                                style: TextStyle(fontSize: 12, color: Color(0xFF858585))),
                          ],
                        ),
                        Column(
                          children: [
                            Text('  充值: ${((data.rechargeAmount ?? 0) / 1000).toStringAsFixed(2)}元  ',
                                style: TextStyle(fontSize: 12, color: Color(0xFF858585))),
                            Text('提现: ${((data.withdrawAmount ?? 0) / 1000).toStringAsFixed(2)}元',
                                style: TextStyle(fontSize: 12, color: Color(0xFF858585)))
                          ],
                        ),
                      ],
                    ),
                  )),
              Expanded(
                  child: dataList.length > 0
                      ? AppPullLoadWidget(
                          pullLoadWidgetControl,
                          (BuildContext context, int index) => _renderEventItem(index),
                          handleRefresh,
                          onLoadMore,
                          refreshKey: refreshIndicatorKey,
                        )
                      : RefreshIndicator(
                          key: refreshIndicatorKey,
                          child: SingleChildScrollView(
                            physics: AlwaysScrollableScrollPhysics(),
                            child: Container(
                              height: 300,
                              child: NoResult(
                                  size: 90,
                                  subWidget: Container(
                                      padding: EdgeInsets.only(top: 10),
                                      child: Text('', style: AppConstant.smallSubText))),
                            ),
                          ),
                          onRefresh: handleRefresh)),
            ],
          ),
        ],
      ),
    );
  }

  // TODO: implement wantKeepAlive
  @override
  bool get wantKeepAlive => true;

  // TODO: implement isRefreshFirst
  @override
  bool get isRefreshFirst => true;

  @override
  requestLoadMore() async {
    return await _getData();
  }

  @override
  requestRefresh() async {
    return await _getData();
  }
}
