import 'dart:io';

import 'package:cabinet_flutter_app/page/finance/MyKeyEvent.dart';
import 'package:flutter/material.dart';

import 'CustomKbBtn.dart';

/// 自定义密码 键盘

class MyKeyboard extends StatefulWidget {
  final callback;

  MyKeyboard(this.callback);

  @override
  State<StatefulWidget> createState() {
    return new MyKeyboardStat();
  }
}

class MyKeyboardStat extends State<MyKeyboard> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  /// 定义 确定 按钮 接口  暴露给调用方
  ///回调函数执行体
  var backMethod;

  void onCommitChange() {
    widget.callback(MyKeyEvent("commit"));
  }

  void onOneChange(BuildContext cont) {
    widget.callback(MyKeyEvent("1"));
  }

  void onTwoChange(BuildContext cont) {
    widget.callback(new MyKeyEvent("2"));
  }

  void onThreeChange(BuildContext cont) {
    widget.callback(new MyKeyEvent("3"));
  }

  void onFourChange(BuildContext cont) {
    widget.callback(new MyKeyEvent("4"));
  }

  void onFiveChange(BuildContext cont) {
    widget.callback(new MyKeyEvent("5"));
  }

  void onSixChange(BuildContext cont) {
    widget.callback(new MyKeyEvent("6"));
  }

  void onSevenChange(BuildContext cont) {
    widget.callback(new MyKeyEvent("7"));
  }

  void onEightChange(BuildContext cont) {
    widget.callback(new MyKeyEvent("8"));
  }

  void onNineChange(BuildContext cont) {
    widget.callback(new MyKeyEvent("9"));
  }

  void onZeroChange(BuildContext cont) {
    widget.callback(new MyKeyEvent("0"));
  }

  /// 点击删除
  void onDeleteChange() {
    widget.callback(new MyKeyEvent("del"));
  }

  @override
  Widget build(BuildContext context) {
    return new Container(
      key: _scaffoldKey,
      width: double.infinity,
      height: Platform.isIOS ? 250.0 : 230,
      color: Colors.white,
      child: new Column(
        children: <Widget>[
          new Container(
            height: 30.0,
            color: Colors.white,
            alignment: Alignment.center,
            child: new Text(
              '下滑隐藏',
              style: new TextStyle(fontSize: 12.0, color: Color(0xff999999)),
            ),
          ),

          ///  键盘主体
          new Column(
            children: <Widget>[
              ///  第一行
              new Row(
                children: <Widget>[
                  CustomKbBtn('1', callback: (val) => onOneChange(context)),
                  CustomKbBtn('2', callback: (val) => onTwoChange(context)),
                  CustomKbBtn('3', callback: (val) => onThreeChange(context)),
                ],
              ),

              ///  第二行
              new Row(
                children: <Widget>[
                  CustomKbBtn('4', callback: (val) => onFourChange(context)),
                  CustomKbBtn('5', callback: (val) => onFiveChange(context)),
                  CustomKbBtn('6', callback: (val) => onSixChange(context)),
                ],
              ),

              ///  第三行
              new Row(
                children: <Widget>[
                  CustomKbBtn('7', callback: (val) => onSevenChange(context)),
                  CustomKbBtn('8', callback: (val) => onEightChange(context)),
                  CustomKbBtn('9', callback: (val) => onNineChange(context)),
                ],
              ),

              ///  第四行
              new Row(
                children: <Widget>[
                  CustomKbBtn('删除', callback: (val) => onDeleteChange()),
                  CustomKbBtn('0', callback: (val) => onZeroChange(context)),
                  CustomKbBtn('确定', callback: (val) => onCommitChange()),
                ],
              ),
            ],
          )
        ],
      ),
    );
  }
}
