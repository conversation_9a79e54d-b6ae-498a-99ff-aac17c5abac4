
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/entitys/trade_record_entity.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CommonRowWidget.dart';
import 'package:flutter/material.dart';

class CapitalFlowDetailPage extends StatefulWidget {
  final TradeRecordEntity trade;

  CapitalFlowDetailPage(this.trade, {Key? key}) : super(key: key);

  @override
  _CapitalFlowDetailPageState createState() => _CapitalFlowDetailPageState();
}

class _CapitalFlowDetailPageState extends State<CapitalFlowDetailPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  String formatData(String number) {
    return number.substring(number.length - 1);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DefaultConfig().configs.BG_COLOR,
      appBar: new AppCustomerBar(
        title: AppbarTitle(
          title: '交易详情',
          isCenter: true,
        ),
        actions: [
          Container(
            width: 60,
          )
        ],
      ),
      body: Container(
        alignment: AlignmentDirectional.centerStart,
        child: Column(
          children: <Widget>[
            Container(
              margin: EdgeInsets.only(top: 10),
              padding: EdgeInsets.all(10),
              color: Colors.white,
              child: Column(
                children: [
                  CommonRowWidget(
                    label: '交易类型',
                    labelStyle: TextStyle(fontSize: 15, color: Color(0xFF585858), fontWeight: FontWeight.w400),
                    value: '${DefaultConfig().configs.TRADE_TYPE_MAP[widget.trade.tradeType.toString()] ?? ''}',
                    valueStyle: TextStyle(fontSize: 15, color: Colors.black, fontWeight: FontWeight.w400),
                  ),
                  Padding(padding: EdgeInsets.only(top: 8)),
                  CommonRowWidget(
                    label: '金额',
                    labelStyle: TextStyle(fontSize: 15, color: Color(0xFF585858), fontWeight: FontWeight.w400),
                    rightWidget: RichText(
                      text: TextSpan(
                          style: TextStyle(fontSize: 15, color: Colors.black, fontWeight: FontWeight.w400),
                          children: <TextSpan>[
                            TextSpan(text: widget.trade.isIncome ? '+' : '-'),
                            TextSpan(
                              text: formatData((widget.trade.tradeMoney / 1000).toStringAsFixed(3)) == '0'
                                  ? '${(widget.trade.tradeMoney / 1000).toStringAsFixed(2)}'
                                  : '${(widget.trade.tradeMoney / 1000).toStringAsFixed(3)}',
                            ),
                          ]),
                    ),
                  ),
                  Padding(padding: EdgeInsets.only(top: 8)),
                  CommonRowWidget(
                    label: '时间',
                    labelStyle: TextStyle(fontSize: 15, color: Color(0xFF585858), fontWeight: FontWeight.w400),
                    value: '${widget.trade.createTime ?? ''}',
                    valueStyle: TextStyle(fontSize: 15, color: Colors.black, fontWeight: FontWeight.w400),
                  ),
                  Padding(padding: EdgeInsets.only(top: 8)),
                  CommonRowWidget(
                    label: '流水号',
                    labelStyle: TextStyle(fontSize: 15, color: Color(0xFF585858), fontWeight: FontWeight.w400),
                    value: '${widget.trade.tradeNo ?? ''}',
                    valueStyle: TextStyle(fontSize: 15, color: Colors.black, fontWeight: FontWeight.w400),
                  ),
                  Padding(padding: EdgeInsets.only(top: 8)),
                  CommonRowWidget(
                    label: '交易渠道',
                    labelStyle: TextStyle(fontSize: 15, color: Color(0xFF585858), fontWeight: FontWeight.w400),
                    value: '${DefaultConfig().configs.PAY_TYPE_MAP[widget.trade.payType]}',
                    valueStyle: TextStyle(fontSize: 15, color: Colors.black, fontWeight: FontWeight.w400),
                  ),
                  Offstage(
                      offstage: (widget.trade.tradeSubject != 1 &&
                          widget.trade.tradeSubject != 2 &&
                          widget.trade.tradeSubject != 3 &&
                          widget.trade.tradeSubject != 7 &&
                          widget.trade.tradeSubject != 10 &&
                          widget.trade.tradeSubject != 11),
                      child: Container(
                        child: OutlinedButton(
                          child: Text("查看明细", style: TextStyle(fontSize: AppConstant.smallTextSize)),
                          onPressed: () {
                            NavigatorUtils.goCapitalFlowDetailListPage(context, widget.trade);
                          },
                          style: ButtonStyle(
                              minimumSize: MaterialStateProperty.all(Size(80, 32)),
                              shape: MaterialStateProperty.all(StadiumBorder()),
                              side: MaterialStateProperty.all(BorderSide(color: Theme.of(context).primaryColor))),
                        ),
                      ))
                ],
              ),
            )
          ],
        ),
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }
}
