import 'dart:async';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/user_pay_account_entity.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/utils/FormValidateUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/RegExpUtil.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/BottomBtnWidget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:redux/redux.dart';

class AccountForm {
  String account = '';
  String name = '';
  String idNo = '';
  String captcha = '';
}

class AccountBindPage extends StatefulWidget {
  AccountBindPage({Key? key}) : super(key: key);

  @override
  _AccountBindPageState createState() => _AccountBindPageState();
}

class _AccountBindPageState extends State<AccountBindPage> {
  UserPayAccountEntity? accountEntity = new UserPayAccountEntity();
  GlobalKey<FormState> _formKey = new GlobalKey<FormState>();
  TextEditingController accountEditingController = TextEditingController();
  TextEditingController nameEditingController = TextEditingController();
  TextEditingController idNoEditingController = TextEditingController();
  TextEditingController captchaEditingController = TextEditingController();
  AccountForm account = new AccountForm();
  var countTime = 0;
  Timer? timer;
  UserEntity? user = new UserEntity();
  bool hasBindAlipay = false;

  /// 计时器
  void startTimer() {
    countTime = 60;
    if (timer != null) {
      return;
    }
    timer = Timer.periodic(Duration(seconds: 1), (time) {
      setState(() {
        if (countTime > 0) {
          countTime--;
        } else {
          timer?.cancel();
          timer = null;
        }
      });
    });
  }

  @override
  void initState() {
    super.initState();
    account.account = '';
    init();
  }

  init() async {
    var userRes = await UserDao.getUserInfoLocal();
    if (userRes != null && userRes.result) {
      UserEntity? userEntity = userRes.data;
      setState(() {
        user = userEntity;
        hasBindAlipay = user?.hasBindAlipay == 1;
      });
    }
    var res = await UserDao.getWealthInfo();
    if (res != null && res.result) {
      accountEntity = UserPayAccountEntity.fromJson(res.data);
    }
    setState(() {});
  }

  @override
  void dispose() {
    timer?.cancel();
    super.dispose();
  }

  /// 发送验证码
  Future<void> sendCode() async {
    bool isPhone = RegExpUtil.isPhone(user!.name);
    if (!isPhone) {
      Fluttertoast.showToast(msg: '手机号格式错误');
      return;
    }
    Map<String, dynamic> data = {"loginName": user!.name, 'smsTplCode': 'BIND_ALIPAY_SMS'};
    var res = await UserDao.sendSms(data);
    if (res != null && res.result) {
      if (res.data) {
        Fluttertoast.showToast(msg: '验证码发送成功,请查收');
        startTimer();
      } else {
        Fluttertoast.showToast(msg: '验证码发送失败请重试');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DefaultConfig().configs.BG_COLOR,
      appBar: new AppCustomerBar(
        title: AppbarTitle(
          title: '账户绑定',
          isCenter: true,
        ),
        actions: [
          Container(
            width: 60,
          )
        ],
      ),
      body: Container(
        // decoration: BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(7)), color: Colors.white),
        child: Column(
          children: <Widget>[
            Expanded(
                child: SingleChildScrollView(
              physics: AlwaysScrollableScrollPhysics(),
              child: Column(
                children: [
                  hasBindAlipay
                      ? Container(
                          margin: EdgeInsets.only(left: 10, right: 10, top: 15),
                          padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
                          decoration:
                              BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(7)), color: Colors.white),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('已绑定提现账户',
                                  style: TextStyle(fontSize: 15, color: Colors.black, fontWeight: FontWeight.w400)),
                              Padding(padding: EdgeInsets.only(top: 10)),
                              Row(
                                children: [
                                  LocalImageUtil.getImageAsset('alipay', width: 17),
                                  SizedBox(
                                    width: 10,
                                  ),
                                  Text('${accountEntity?.alipayId??''}', style: TextStyle(fontSize: 14, color: Colors.black))
                                ],
                              )
                            ],
                          ))
                      : Container(
                          margin: EdgeInsets.only(left: 10, right: 10, top: 15),
                          padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
                          alignment: Alignment.topLeft,
                          decoration:
                              BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(7)), color: Colors.white),
                          child: Form(
                            key: _formKey,
                            child: Column(
                              children: [
                                Container(
                                    decoration: BoxDecoration(
                                        border: Border(bottom: BorderSide(width: 1, color: Color(0xFFEEEEEE)))),
                                    child: TextFormField(
                                      validator: (value) => FormValidateUtil.phoneValidate(value!, context),
                                      keyboardType: TextInputType.phone,
                                      controller: accountEditingController,
                                      style: TextStyle(
                                        height: 1.5,
                                        fontSize: 16.0,
                                        color: Colors.black,
                                      ),
                                      decoration: new InputDecoration(
                                          border: InputBorder.none,
                                          contentPadding: EdgeInsets.symmetric(vertical: 10),
                                          hintText: '请输入支付宝账号',
                                          hintStyle: new TextStyle(fontSize: 16.0, color: Colors.grey.shade500),
                                          suffixIcon: accountEditingController.text.length > 0
                                              ? Container(
                                                  width: 20,
                                                  child: new IconButton(
                                                    alignment: Alignment.center,
                                                    padding: const EdgeInsets.all(0.0),
                                                    iconSize: 18.0,
                                                    icon: Icon(Icons.cancel),
                                                    onPressed: () {
                                                      setState(() {
                                                        accountEditingController.value = TextEditingValue(text: '');
                                                      });
                                                    },
                                                  ),
                                                )
                                              : new Text("")),
                                      onChanged: (value) {
                                        setState(() {
                                          account.account = value;
                                        });
                                      },
                                    )),
                                Container(
                                    decoration: BoxDecoration(
                                        border: Border(bottom: BorderSide(width: 1, color: Color(0xFFEEEEEE)))),
                                    child: TextFormField(
                                      keyboardType: TextInputType.text,
                                      controller: nameEditingController,
                                      style: TextStyle(
                                        height: 1.5,
                                        fontSize: 16.0,
                                        color: Colors.black,
                                      ),
                                      decoration: new InputDecoration(
                                          border: InputBorder.none,
                                          contentPadding: EdgeInsets.symmetric(vertical: 10),
                                          hintText: '请输入姓名',
                                          hintStyle: new TextStyle(fontSize: 16.0, color: Colors.grey.shade500),
                                          suffixIcon: nameEditingController.text.length > 0
                                              ? Container(
                                                  width: 20,
                                                  child: new IconButton(
                                                    alignment: Alignment.center,
                                                    padding: const EdgeInsets.all(0.0),
                                                    iconSize: 18.0,
                                                    icon: Icon(Icons.cancel),
                                                    onPressed: () {
                                                      setState(() {
                                                        nameEditingController.value = TextEditingValue(text: '');
                                                        account.name = '';
                                                      });
                                                    },
                                                  ),
                                                )
                                              : new Text("")),
                                      onChanged: (value) {
                                        setState(() {
                                          account.name = value;
                                        });
                                      },
                                    )),
                                Container(
                                    decoration: BoxDecoration(
                                        border: Border(bottom: BorderSide(width: 1, color: Color(0xFFEEEEEE)))),
                                    child: TextFormField(
                                      validator: (value) => FormValidateUtil.idNumberValidate(value!),
                                      keyboardType: TextInputType.name,
                                      controller: idNoEditingController,
                                      style: TextStyle(
                                        height: 1.5,
                                        fontSize: 16.0,
                                        color: Colors.black,
                                      ),
                                      decoration: new InputDecoration(
                                          border: InputBorder.none,
                                          contentPadding: EdgeInsets.symmetric(vertical: 10),
                                          hintText: '请输入身份证号',
                                          hintStyle: new TextStyle(fontSize: 16.0, color: Colors.grey.shade500),
                                          suffixIcon: idNoEditingController.text.length > 0
                                              ? Container(
                                                  width: 20,
                                                  child: new IconButton(
                                                    alignment: Alignment.center,
                                                    padding: const EdgeInsets.all(0.0),
                                                    iconSize: 18.0,
                                                    icon: Icon(Icons.cancel),
                                                    onPressed: () {
                                                      setState(() {
                                                        idNoEditingController.value = TextEditingValue(text: '');
                                                        account.idNo = '';
                                                      });
                                                    },
                                                  ),
                                                )
                                              : new Text("")),
                                      onChanged: (value) {
                                        setState(() {
                                          account.idNo = value;
                                        });
                                      },
                                    )),
                                Container(
                                    child: Row(
                                  children: [
                                    Expanded(
                                        child: TextFormField(
                                      keyboardType: TextInputType.number,
                                      validator: (value) => FormValidateUtil.captchaValid(value!),
                                      controller: captchaEditingController,
                                      style: TextStyle(
                                        height: 1.5,
                                        fontSize: 16.0,
                                        color: Colors.black,
                                      ),
                                      decoration: new InputDecoration(
                                          border: InputBorder.none,
                                          contentPadding: EdgeInsets.symmetric(vertical: 10),
                                          hintText: '请输入验证码',
                                          hintStyle: new TextStyle(fontSize: 16.0, color: Colors.grey.shade500),
                                          suffixIcon: captchaEditingController.text.length > 0
                                              ? Container(
                                                  width: 20,
                                                  child: new IconButton(
                                                    alignment: Alignment.center,
                                                    padding: const EdgeInsets.all(0.0),
                                                    iconSize: 18.0,
                                                    icon: Icon(Icons.cancel),
                                                    onPressed: () {
                                                      setState(() {
                                                        captchaEditingController.value = TextEditingValue(text: '');
                                                        account.captcha = '';
                                                      });
                                                    },
                                                  ),
                                                )
                                              : new Text("")),
                                      onChanged: (value) {
                                        setState(() {
                                          account.captcha = value;
                                        });
                                      },
                                    )),
                                    TextButton(
                                      child: Text(countTime <= 0 ? '获取验证码' : "$countTime秒后重试"),
                                      style: ButtonStyle(
                                          foregroundColor:
                                              MaterialStateProperty.all<Color>(Theme.of(context).primaryColor)),
                                      onPressed: countTime <= 0 ? sendCode : null,
                                    )
                                  ],
                                )),
                              ],
                            ),
                          ))
                ],
              ),
            )),
            BottomBtnWidget(
              title: hasBindAlipay ? '换绑' : '绑定',
              type: hasBindAlipay ? ButtonType.info : ButtonType.primary,
              showShadow: false,
              action: () {
                if (hasBindAlipay) {
                  changeBind();
                } else {
                  bindAccount();
                }
              },
            )
          ],
        ),
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }

  changeBind() async {
    var res = await UserDao.getWealthInfo();
    if (res != null && res.result) {
      accountEntity = UserPayAccountEntity.fromJson(res.data);
      accountEditingController.value = TextEditingValue(text: '${accountEntity!.alipayId}');
      account.account = accountEntity!.alipayId!;
      nameEditingController.value = TextEditingValue(text: '${accountEntity!.realName}');
      account.name = accountEntity!.realName!;
      idNoEditingController.value = TextEditingValue(text: '${accountEntity!.alipayIdNumber}');
      account.idNo = accountEntity!.alipayIdNumber!;
    }
    print('$accountEntity');
    setState(() {
      hasBindAlipay = false;
    });
  }

  Future<void> bindAccount() async {
    Map<String, dynamic> info = {
      'alipayAccount': account.account,
      'realName': account.name,
      'idNumber': account.idNo,
      'smsCode': account.captcha
    };
    bool validator = _formKey.currentState?.validate() ?? false;
    if (validator) {
      var res = await UserDao.bindAccount(info);
      EasyLoading.dismiss();
      if (res != null && res.result) {
        Fluttertoast.showToast(msg: '绑定成功');
        hasBindAlipay = true;
        Store<AppState> store = StoreProvider.of(context);
        UserDao.updateUserInfo(store);
        Navigator.of(context).pop();
      }
    }
  }
}
