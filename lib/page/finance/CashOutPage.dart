
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/user_pay_account_entity.dart';
import 'package:cabinet_flutter_app/common/page_life_cycle.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/DebounceUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/BottomBtnWidget.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CommonRowWidget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';

class CashOutPage extends StatefulWidget {
  CashOutPage({Key? key}) : super(key: key);

  @override
  _CashOutPageState createState() => _CashOutPageState();
}

class _CashOutPageState extends State<CashOutPage> with PageLifeCycle<CashOutPage> {
  TextEditingController moneyEditingController = TextEditingController();
  String money = '';
  UserPayAccountEntity? accountEntity = new UserPayAccountEntity();
  bool isBindAccount = false;
  int balance = 0;
  String balanceNum = '';
  String minWithdrawMoney = '100.0';
  String maxWithdrawMoney = '1000.0';
  int frozenMoney = 0;
  String frozenMoneyNum = '';

  @override
  void initState() {
    super.initState();
    getUserAccount();
  }

  @override
  void dispose() {
    moneyEditingController.dispose();
    super.dispose();
  }

  @override
  void onShow() {
    getUserAccount();
  }

  getUserAccount() async {
    var res = await UserDao.getWealthInfo();
    if (res != null && res.result) {
      accountEntity = UserPayAccountEntity.fromJson(res.data);
      setState(() {
        balance = accountEntity!.balance!;
        balanceNum = (balance / 1000).toStringAsFixed(2);
        minWithdrawMoney = (accountEntity!.minWithdrawMoney! / 1000).toStringAsFixed(1);
        maxWithdrawMoney = (accountEntity!.maxWithdrawMoney! / 1000).toStringAsFixed(1);
        frozenMoney = accountEntity!.frozenMoney!;
        frozenMoneyNum = (accountEntity!.frozenMoney! / 1000).toStringAsFixed(2);
      });
    }
    if (CheckUtils.isNotNull(accountEntity?.wechatId) || CheckUtils.isNotNull(accountEntity?.alipayId)) {
      isBindAccount = true;
    }
    setState(() {});
  }

  cacheOut() async {
    if(money == "") {
      Fluttertoast.showToast(msg: "请输入提现金额");
      return false;
    }
// 下周修改********
    if (double.parse(money) * 1000 > balance) {
      Fluttertoast.showToast(msg: '可提现金额不足', gravity: ToastGravity.CENTER);
      return false;
    } else if (double.parse(money) * 1000 < accountEntity!.minWithdrawMoney! || double.parse(money) * 1000 > accountEntity!.maxWithdrawMoney!) {
       Fluttertoast.showToast(msg: '提现金额不能小于$minWithdrawMoney元且不能大于$maxWithdrawMoney元', gravity: ToastGravity.CENTER);
      return false;
    } else {
      Map<String, dynamic> info = {
        'payType': '2',
        'amount': double.parse(money) * 1000,
      };
      var res = await UserDao.goWithdrawalCreate(info);
      if (res != null && res.result) {
        Fluttertoast.showToast(msg: '提现成功');
        Navigator.of(context).pop(true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: DefaultConfig().configs.BG_COLOR,
        appBar: new AppCustomerBar(
          title: AppbarTitle(
            title: '提现',
            isCenter: true,
          ),
          actions: [
            Container(
              width: 60,
            )
          ],
        ),
        body: Container(
          child: Column(
            children: <Widget>[
              Expanded(
                  child: SingleChildScrollView(
                physics: AlwaysScrollableScrollPhysics(),
                child: Column(
                  children: [
                    Container(
                      margin: EdgeInsets.only(left: 10, right: 10, top: 15),
                      padding: EdgeInsets.fromLTRB(10, 15, 10, 15),
                      alignment: Alignment.topLeft,
                      decoration:
                          BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(7)), color: Colors.white),
                      child: Column(
                        children: [
                          Container(
                            decoration:
                                BoxDecoration(border: Border(bottom: BorderSide(width: 1, color: Color(0xFFEEEEEE)))),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('提现账户',
                                    style: TextStyle(fontSize: 15, color: Colors.black, fontWeight: FontWeight.w400)),
                                isBindAccount
                                    ? Container(
                                        height: 36,
                                        alignment: Alignment.bottomLeft,
                                        child: CommonRowWidget(
                                          leftWidget: Row(
                                            children: [
                                              LocalImageUtil.getImageAsset(
                                                  CheckUtils.isNotNull(accountEntity?.alipayId) ? 'alipay' : 'wx',
                                                  width: 20),
                                              Padding(padding: EdgeInsets.only(left: 5)),
                                              Text(
                                                '${CheckUtils.isNotNull(accountEntity?.alipayId) ? accountEntity?.alipayId : accountEntity?.wechatId}',
                                                style: TextStyle(
                                                    color: Colors.black, fontSize: 14, fontWeight: FontWeight.w400),
                                              )
                                            ],
                                          ),
                                          value: '请确认提现账户是否正确',
                                          valueStyle: TextStyle(fontSize: 14, color: Color(0xFF858585)),
                                        ),
                                      )
                                    : Container(
                                        height: 36,
                                        alignment: Alignment.bottomLeft,
                                        child: CommonRowWidget(
                                          label: '未绑定提现账户，不能发起提现',
                                          labelStyle: TextStyle(fontSize: 15, color: Color(0xFF999999)),
                                          value: '去绑定',
                                          valueStyle: TextStyle(fontSize: 14, color: Theme.of(context).primaryColor),
                                          actionRight: () {
                                            NavigatorUtils.goAccountBindPage(context);
                                          },
                                        ),
                                      ),
                                Padding(padding: EdgeInsets.only(top: 8))
                              ],
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.only(top: 8),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('提现金额',
                                    style: TextStyle(fontSize: 15, color: Colors.black, fontWeight: FontWeight.w400)),
                                Row(
                                  children: [
                                    Expanded(
                                      child: TextFormField(
                                        inputFormatters: [FilteringTextInputFormatter.allow(RegExp("[0-9.]"))],
                                        keyboardType: TextInputType.text,
                                        controller: moneyEditingController,
                                        style: TextStyle(
                                          height: 1.8,
                                          fontSize: 22.0,
                                          color: Colors.black,
                                        ),
                                        decoration: new InputDecoration(
                                            border: InputBorder.none,
                                            contentPadding: EdgeInsets.all(0.0),
                                            hintText: '提现金额≥$minWithdrawMoney元且≤$maxWithdrawMoney元',
                                            hintStyle: new TextStyle(fontSize: 15.0, color: Colors.grey.shade500),
                                            suffixIcon: moneyEditingController.text.length > 0
                                                ? Container(
                                                    width: 20,
                                                    child: new IconButton(
                                                      alignment: Alignment.center,
                                                      padding: const EdgeInsets.all(0.0),
                                                      iconSize: 18.0,
                                                      icon: Icon(Icons.cancel),
                                                      onPressed: () {
                                                        setState(() {
                                                          moneyEditingController.value = TextEditingValue(text: '');
                                                          money = '';
                                                        });
                                                      },
                                                    ),
                                                  )
                                                : new Text("")),
                                        onChanged: (money_) {
                                          setState(() {
                                            money = money_;
                                          });
                                        },
                                      ),
                                    ),
                                    InkWell(
                                      onTap: () {
                                        moneyEditingController.value = TextEditingValue(text: balanceNum);
                                        money = balanceNum;
                                        setState(() {});
                                      },
                                      child: Text('全部提现', style: TextStyle(fontSize: 14, color: Theme.of(context).primaryColor)),
                                    )
                                  ],
                                ),
                                Text(
                                  '可提现金额￥$balanceNum' + (frozenMoney != 0 ? '（冻结金额$frozenMoneyNum）' : ''),
                                  style: TextStyle(fontSize: 13, color: Color(0xFF585858)),
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                    )
                  ],
                ),
              )),
              BottomBtnWidget(
                title: '确认提现',
                type: double.parse(CheckUtils.isNum(money) ? money : '0') > 0
                    ? ButtonType.primary
                    : ButtonType.primaryDisabled,
                showShadow: false,
                action: ()  {
                   DebounceUtils.throttle(() {
                    cacheOut();
                  },2500); 
                },
              )
            ],
          ),
        ) // This trailing comma makes auto-formatting nicer for build methods.
        );
  }
}
