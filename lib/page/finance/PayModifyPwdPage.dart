
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/FormValidateUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/ThrottleUtil.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/BottomBtnWidget.dart';
import 'package:cabinet_flutter_app/widget/BottomFormInputWidget.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class ModifyPayPwdPage extends StatefulWidget {
  ModifyPayPwdPage({Key? key}) : super(key: key);

  @override
  _ModifyPayPwdPageState createState() => _ModifyPayPwdPageState();
}

class _ModifyPayPwdPageState extends State<ModifyPayPwdPage> {
  GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  TextEditingController passwordController = new TextEditingController();
  TextEditingController passwordNextController = new TextEditingController();
  TextEditingController passwordAgainController = new TextEditingController();
  ThrottleUtil throttleUtil = ThrottleUtil();
  var password = '', passwordNext = '', passwordAgain = '';

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  buildList() {
    List<Widget> widgets = [];
    widgets.add(Container(
      margin: EdgeInsets.only(top: 15, bottom: 1),
      child: BottomFormInputWidget(
        value: password,
        labelWidth: 0,
        hintText: '请输入旧密码',
        textInputType: TextInputType.number,
        controller: passwordNextController,
        padding: EdgeInsets.fromLTRB(5, 5, 0, 5),
        onChanged: (String? value) {
          setState(() {
            password = value!;
          });
        },
      ),
    ));
    widgets.add(Container(
      margin: EdgeInsets.only(top: 0, bottom: 1),
      child: BottomFormInputWidget(
        value: passwordNext,
        labelWidth: 0,
        hintText: '请输入6位支付密码',
        validator: (value) => FormValidateUtil.payPasswordValidate(value!),
        textInputType: TextInputType.number,
        controller: passwordController,
        padding: EdgeInsets.fromLTRB(5, 5, 0, 5),
        onChanged: (String? value) {
          setState(() {
            passwordNext = value!;
          });
        },
      ),
    ));
    widgets.add(Container(
      child: BottomFormInputWidget(
        value: passwordAgain,
        labelWidth: 0,
        hintText: '请再次输入6位支付密码',
        validator: (value) => FormValidateUtil.payPasswordValidate(value!),
        textInputType: TextInputType.number,
        controller: passwordAgainController,
        padding: EdgeInsets.fromLTRB(5, 5, 0, 5),
        onChanged: (String? value) {
          setState(() {
            passwordAgain = value!;
          });
        },
      ),
    ));
    return widgets;
  }

  sureUpload() async {
    if (passwordAgain != passwordNext) {
      Fluttertoast.showToast(msg: '两次密码不一致');
      return;
    }
    Map<String, dynamic> info = {'oldPayPassword': password, 'payPassword': passwordAgain};
    bool validator = _formKey.currentState?.validate() ?? false;
    if (validator) {
      LoadingUtil(
        status: '设置中...',
      ).show(context);
      var res = await UserDao.modifyPayPassword(info);
      LoadingUtil.dismiss(context);
      if (res != null && res.result) {
        Fluttertoast.showToast(msg: '修改成功');
        Navigator.pop(context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return new Material(
      child: new Scaffold(
          backgroundColor: DefaultConfig().configs.BG_COLOR,
          appBar: new AppCustomerBar(
            title: new AppbarTitle(
              title: '支付设置',
              isCenter: true,
            ),
            actions: <Widget>[
              new Container(
                width: 60.0,
              )
            ],
          ),
          body: new Column(
            children: <Widget>[
              Expanded(
                  child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(children: buildList()),
                ),
              )),
              Offstage(
                offstage: false,
                child: Container(
                  child: Column(
                    children: [
                      BottomBtnWidget(
                          showShadow: false,
                          title: '确定',
                          type: CheckUtils.isNotNull(password) &&
                                  CheckUtils.isNotNull(passwordNext) &&
                                  CheckUtils.isNotNull(passwordAgain)
                              ? ButtonType.primary
                              : ButtonType.primaryDisabled,
                          action: () => throttleUtil.throttle(() {
                                sureUpload();
                              })),
                    ],
                  ),
                ),
              )
            ],
          )),
    );
  }
}
