import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/entitys/trade_record_entity.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppListState.dart';
import 'package:cabinet_flutter_app/widget/AppPullLoadWidget.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/NoResult.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CommonRowWidget.dart';
import 'package:flutter/material.dart';

class CapitalFlowDetailListPage extends StatefulWidget {
  final TradeRecordEntity trade;

  CapitalFlowDetailListPage(this.trade, {Key? key}) : super(key: key);

  @override
  _CapitalFlowDetailListPageState createState() => _CapitalFlowDetailListPageState();
}

class _CapitalFlowDetailListPageState extends State<CapitalFlowDetailListPage>
    with AutomaticKeepAliveClientMixin<CapitalFlowDetailListPage>, AppListState<CapitalFlowDetailListPage> {
  int total = 0;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<Null> onFresh() async {
    handleRefresh();
  }

  // 上拉加载更多
  _getData({isRefresh = false}) async {
    Map<String, dynamic> info = {
      'tradeSubject': widget.trade.tradeSubject,
      'tradeYmd': widget.trade.tradeYmd,
      'tradeType': widget.trade.tradeType,
      'shopId': widget.trade.shopId,
      'current': page.toString(),
      'size': 10,
    };
    DataResult result = await CourierDao.getTradeRecordPage(info);
    total = result.total!;
    return result;
  }

  String formatData(String number) {
    return number.substring(number.length - 1);
  }

  _renderEventItem(index) {
    var item = dataList[index];
    bool isIncome = item['tradeType'] == 1 || item['tradeType'] == 3;
    return Container(
      padding: EdgeInsets.only(left: 10.0, right: 10),
      decoration: BoxDecoration(color: Colors.white),
      child: Container(
        padding: EdgeInsets.fromLTRB(0.0, 10.0, 0.0, 10.0),
        width: MediaQuery.of(context).size.width - 10,
        decoration: BoxDecoration(border: Border(bottom: BorderSide(width: 1.0, color: Colors.grey.shade100))),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            CommonRowWidget(
              label:
                  '${item['hasOffset'] == 1 ? '取消投柜' : DefaultConfig().configs.TRADE_SUBJECT_MAP[item['tradeSubject']] ?? ''}',
              rightWidget: RichText(
                text: TextSpan(
                    style: TextStyle(
                        fontSize: 16,
                        color: (item['hasOffset'] == 0 && isIncome) || (item['hasOffset'] == 1 && !isIncome)
                            ? Theme.of(context).primaryColor
                            : Colors.black,
                        fontWeight: FontWeight.w400),
                    children: <TextSpan>[
                      TextSpan(
                          text: (item['hasOffset'] == 0 && isIncome) || (item['hasOffset'] == 1 && !isIncome)
                              ? '+'
                              : '-'),
                      TextSpan(
                          text: formatData((item['tradeMoney'] / 1000).toStringAsFixed(3)) == '0'
                              ? '${(item['tradeMoney'] / 1000).toStringAsFixed(2)}'
                              : '${(item['tradeMoney'] / 1000).toStringAsFixed(3)}'),
                    ]),
              ),
            ),
            CommonRowWidget(
              label: '时间',
              value: '${item['tradeTime']}',
            ),
            CommonRowWidget(
              label: '流水号',
              value: '${item['tradeNo']}',
            ),
            CommonRowWidget(
              label: '交易渠道',
              value: DefaultConfig().configs.PAY_TYPE_MAP[item['payType']],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DefaultConfig().configs.BG_COLOR,
      appBar: new AppCustomerBar(
        title: AppbarTitle(
          title: '资金订单明细',
          isCenter: true,
        ),
        actions: [
          Container(
            width: 60,
          )
        ],
      ),
      body: Container(
        child: Column(
          children: [
            CommonRowWidget(
              leftWidget: Row(
                children: [
                  Padding(padding: EdgeInsets.only(left: 10)),
                  Container(
                    child: RichText(
                      text: TextSpan(
                          style: TextStyle(fontSize: 16, color: Colors.black, fontWeight: FontWeight.w400),
                          children: <TextSpan>[
                            TextSpan(
                                text: '${widget.trade.tradeYmd}',
                                style: TextStyle(fontSize: 16, color: Color(0xFF585858), fontWeight: FontWeight.w400)),
                            TextSpan(text: ' (', style: TextStyle(fontSize: 14)),
                            TextSpan(
                                text:
                                    '${DefaultConfig().configs.TRADE_TYPE_MAP[widget.trade.tradeType.toString()] ?? ''}',
                                style: TextStyle(fontSize: 14)),
                            TextSpan(
                                text: formatData((widget.trade.tradeMoney / 1000).toStringAsFixed(3)) == '0'
                                    ? '${(widget.trade.tradeMoney / 1000).toStringAsFixed(2)}'
                                    : '${(widget.trade.tradeMoney / 1000).toStringAsFixed(3)}',
                                style: TextStyle(fontSize: 14)),
                            TextSpan(text: '元)', style: TextStyle(fontSize: 14)),
                          ]),
                    ),
                  ),
                ],
              ),
              rightWidget: Container(
                  padding: EdgeInsets.only(right: 10),
                  child: Text('共计:  ${total.toString()}', style: TextStyle(color: Color(0xFF999999)))),
            ),
            Expanded(
                child: dataList.length > 0
                    ? AppPullLoadWidget(
                        pullLoadWidgetControl,
                        (BuildContext context, int index) => _renderEventItem(index),
                        handleRefresh,
                        onLoadMore,
                        refreshKey: refreshIndicatorKey,
                      )
                    : RefreshIndicator(
                        key: refreshIndicatorKey,
                        child: SingleChildScrollView(
                          physics: AlwaysScrollableScrollPhysics(),
                          child: Container(
                            height: 300,
                            child: NoResult(
                                size: 90,
                                subWidget: Container(
                                    padding: EdgeInsets.only(top: 10),
                                    child: Text('', style: AppConstant.smallSubText))),
                          ),
                        ),
                        onRefresh: handleRefresh))
          ],
        ),
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }

  // TODO: implement wantKeepAlive
  @override
  bool get wantKeepAlive => true;

  // TODO: implement isRefreshFirst
  @override
  bool get isRefreshFirst => true;

  @override
  requestLoadMore() async {
    return await _getData();
  }

  @override
  requestRefresh() async {
    return await _getData(isRefresh: true);
  }
}
