
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:cabinet_flutter_app/common/page_life_cycle.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/page/finance/AccountBindPage.dart';
import 'package:cabinet_flutter_app/page/finance/PaySetPwdPage.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CommonRowWidget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';

class PaymentSettingPage extends StatefulWidget {
  PaymentSettingPage({Key? key}) : super(key: key);

  @override
  _PaymentSettingPageState createState() => _PaymentSettingPageState();
}

class _PaymentSettingPageState extends State<PaymentSettingPage> with PageLifeCycle<PaymentSettingPage> {
  bool isSettingPayPassword = true; // 是否设置了支付密码
  bool isBindAccount = false; // 是否绑定账户
  UserEntity? user = new UserEntity();

  @override
  void initState() {
    super.initState();
    this.initData();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void onShow() {
    this.initData();
  }

  initData() async {
    var userRes = await UserDao.getUserInfoLocal();
    if (userRes != null && userRes.result) {
      UserEntity? userEntity = userRes.data;
      setState(() {
        user = userEntity;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return StoreBuilder<AppState>(builder: (context, store) {
      // user = store.state.userInfo;
      // if (user == null) {
      //   initData(store);
      // }
      return Scaffold(
        backgroundColor: DefaultConfig().configs.BG_COLOR,
        appBar: new AppCustomerBar(
          title: AppbarTitle(
            title: '支付设置',
            isCenter: true,
          ),
          actions: [
            Container(
              width: 60,
            )
          ],
        ),
        body: Container(
          alignment: AlignmentDirectional.centerStart,
          child: Column(
            children: <Widget>[
              Container(
                margin: EdgeInsets.only(top: 10),
                padding: EdgeInsets.all(10),
                color: Colors.white,
                child: Column(
                  children: [
                    InkWell(
                      onTap: () {
                        Navigator.push(
                                context,
                                new CupertinoPageRoute(
                                    builder: (context) => new AccountBindPage(),
                                    settings: RouteSettings(name: 'accountBindPage')))
                            .then((value) => {Navigator.of(context).pop()});
                        // NavigatorUtils.goAccountBindPage(context);
                      },
                      child: Container(
                          padding: EdgeInsets.only(bottom: 10),
                          decoration:
                              BoxDecoration(border: Border(bottom: BorderSide(width: 1, color: Color(0xFFEEEEEE)))),
                          child: CommonRowWidget(
                            leftWidget: Row(
                              children: [
                                Text('账户绑定',
                                    style: TextStyle(fontSize: 15, color: Colors.black, fontWeight: FontWeight.w400)),
                                Padding(padding: EdgeInsets.only(left: 5)),
                                Container(
                                  padding: EdgeInsets.fromLTRB(7, 2, 7, 2),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.all(Radius.circular(10)), color: Theme.of(context).primaryColor),
                                  child: Text('提现', style: TextStyle(color: Colors.white, fontSize: 10)),
                                )
                              ],
                            ),
                            rightWidget: Row(
                              children: [
                                Text('${user?.hasBindAlipay == 1 ? '已绑定' : '未绑定'}',
                                    style: TextStyle(
                                        color: user?.hasBindAlipay == 1 ? Color(0xFF585858) : Theme.of(context).primaryColor,
                                        fontWeight: FontWeight.w400,
                                        fontSize: 14)),
                                Offstage(
                                  offstage: user?.hasBindAlipay == 1,
                                  child: Row(
                                    children: [
                                      Padding(padding: EdgeInsets.only(right: 5)),
                                      Icon(Icons.arrow_forward_ios_sharp, size: 15, color: Colors.black)
                                    ],
                                  ),
                                )
                              ],
                            ),
                          )),
                    ),
                    InkWell(
                      onTap: () {
                        if (user?.hasSetPayPwd == 0) {
                          Navigator.push(
                                  context,
                                  new CupertinoPageRoute(
                                      builder: (context) => new SetPayPasswordPage(),
                                      settings: RouteSettings(name: 'setPayPasswordPage')))
                              .then((value) => {Navigator.of(context).pop()});
                          // NavigatorUtils.goSetPayPassword(context);
                        }
                      },
                      child: Container(
                          padding: EdgeInsets.only(bottom: 10, top: 10),
                          child: CommonRowWidget(
                            leftWidget: Row(
                              children: [
                                Text('支付密码',
                                    style: TextStyle(fontSize: 15, color: Colors.black, fontWeight: FontWeight.w400)),
                              ],
                            ),
                            rightWidget: Row(
                              children: [
                                Text('${user?.hasSetPayPwd == 1 ? '已设置' : '未设置'}',
                                    style: TextStyle(
                                        color: user?.hasSetPayPwd == 1 ? Color(0xFF585858) : Theme.of(context).primaryColor,
                                        fontWeight: FontWeight.w400,
                                        fontSize: 14)),
                                Offstage(
                                  offstage: user?.hasSetPayPwd == 1,
                                  child: Row(
                                    children: [
                                      Padding(padding: EdgeInsets.only(right: 5)),
                                      Icon(Icons.arrow_forward_ios_sharp, size: 15, color: Colors.black)
                                    ],
                                  ),
                                )
                              ],
                            ),
                          )),
                    ),
                    Offstage(
                      offstage: user?.hasSetPayPwd == 0,
                      child: Container(
                        padding: EdgeInsets.all(10),
                        decoration:
                            BoxDecoration(color: DefaultConfig().configs.BG_COLOR, borderRadius: BorderRadius.all(Radius.circular(7))),
                        child: Column(
                          children: [
                            InkWell(
                              onTap: () {
                                NavigatorUtils.goModifyPayPwdPage(context);
                              },
                              child: Container(
                                padding: EdgeInsets.only(bottom: 10),
                                decoration: BoxDecoration(
                                    border: Border(bottom: BorderSide(width: 1, color: Color(0xFFEEEEEE)))),
                                child: CommonRowWidget(
                                  label: '修改支付密码',
                                  rightWidget: Icon(Icons.arrow_forward_ios_sharp, size: 15, color: Colors.black),
                                ),
                              ),
                            ),
                            InkWell(
                              onTap: () {
                                NavigatorUtils.goPayForGetPwdPage(context);
                              },
                              child: Container(
                                padding: EdgeInsets.only(top: 6, bottom: 5),
                                child: CommonRowWidget(
                                  label: '忘记支付密码',
                                  rightWidget: Icon(Icons.arrow_forward_ios_sharp, size: 15, color: Colors.black),
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
        ), // This trailing comma makes auto-formatting nicer for build methods.
      );
    });
  }
}
