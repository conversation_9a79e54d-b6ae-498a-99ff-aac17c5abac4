import 'dart:convert';

import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CabinetDao.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_entity.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/net/Address.dart';
import 'package:cabinet_flutter_app/common/net/HttpManager.dart';
import 'package:cabinet_flutter_app/common/net/ResultData.dart';
import 'package:cabinet_flutter_app/common/page_life_cycle.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CabinetBoxUtil.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/utils/ThrottleUtil.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/BottomBtnWidget.dart';
import 'package:cabinet_flutter_app/widget/CabinetBoxListUseWidget.dart';
import 'package:cabinet_flutter_app/widget/CollectIconButton.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class CabinetBoxUseCreate extends StatefulWidget {
  final CabinetEntity cabinet;
  CabinetBoxUseType boxUseType;

  CabinetBoxUseCreate(this.cabinet, this.boxUseType, {Key? key}) : super(key: key);

  @override
  _CabinetBoxUseCreateState createState() => _CabinetBoxUseCreateState();
}

class _CabinetBoxUseCreateState extends State<CabinetBoxUseCreate>
    with WidgetsBindingObserver, PageLifeCycle<CabinetBoxUseCreate>, AutomaticKeepAliveClientMixin {
  final GlobalKey<ScaffoldState> cabinetBoxCreatePage = GlobalKey<ScaffoldState>();

  ThrottleUtil throttleUtil = ThrottleUtil();
  bool isPda = false;
  double marginValue = 10;
  CabinetEntity cabinetEntity = new CabinetEntity();
  List<dynamic> emptyList = [];
  String selectValue = '150';
  bool checkedValue = true;
  List minuteMapData = [];
  String boxJson = '';

  void initState() {
    super.initState();
    this.init();
  }

  init() {
    getCabinetInfo();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void onShow() async {}

  Future<Null> onFresh() async {
    init();
  }

  /// 单选列表
  getRadioList() {
    List<Widget> list = <Widget>[];
    minuteMapData.forEach((item) {
      list.add(Container(
        margin: EdgeInsets.fromLTRB(10, 0, 10, 0),
        decoration: BoxDecoration(
            border: minuteMapData[minuteMapData.length - 1]['value'] == item['value']
                ? Border()
                : Border(
                    bottom: BorderSide(color: Color(0xFFE5E5E5), width: 1.0),
                  )),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${widget.boxUseType == CabinetBoxUseType.YYGK ? "预约${item['value']}分钟" : "租用${(item['value'] / 60 / 24).toStringAsFixed(0)}天"}',
                  style: TextStyle(fontSize: 16.0, color: Colors.black87),
                ),
              ],
            ),
            Radio(value: '${item['value']}', groupValue: selectValue, onChanged: changeCancelReason),
          ],
        ),
      ));
    });
    return Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(
            Radius.circular(7),
          ),
          border: Border.all(
            width: 1,
            color: Colors.white,
          ),
        ),
        margin: EdgeInsets.fromLTRB(10, 10, 10, 0),
        width: double.infinity,
        child: Column(
          children: <Widget>[...list],
        ));
  }

  /// 设置单选选中
  changeCancelReason(value) {
    selectValue = value;
    if (widget.boxUseType == CabinetBoxUseType.YYGK) {
      getBoxJson(cabinetEntity.bookJson, value: value);
    } else if (widget.boxUseType == CabinetBoxUseType.XGZY) {
      getBoxJson(cabinetEntity.rentJson, value: value);
    }
    getCabinetUsableBox();
    setState(() {});
  }

  /// 获取展示格口价格JSON
  getBoxJson(json, {String? value}) {
    minuteMapData = [];
    Map obj = jsonDecode(json);
    obj.keys.toList().forEach((e) {
      minuteMapData.add({'value': int.parse(e)});
    });
    if (value != null) {
      boxJson = jsonEncode(obj['$value']);
    } else {
      selectValue = '${minuteMapData[0]['value']}';
      boxJson = jsonEncode(obj['${minuteMapData[0]['value']}']);
    }
  }

  getTitle() {
    Map<CabinetBoxUseType, String> titleMap = {
      CabinetBoxUseType.XGZY: '租用详情',
      CabinetBoxUseType.YYGK: '预约详情',
    };
    return titleMap[widget.boxUseType];
  }

  /// 获取点位可用盒子汇总
  getCabinetUsableBox() async {
    Map<String, dynamic> countMap = {
      'microCount': widget.cabinet.microCount,
      'miniCount': widget.cabinet.miniCount,
      'smallCount': widget.cabinet.smallCount,
      'mediumCount': widget.cabinet.mediumCount,
      'largeCount': widget.cabinet.largeCount,
      'hugeCount': widget.cabinet.hugeCount,
      'superCount':widget.cabinet.superCount,
    };
    emptyList = CabinetBoxUtil.getCabinetBoxAvailableCount(boxJson, countMap);
    for (var i in emptyList) {
      i['checked'] = '0';
    }
    setState(() {});
  }

  /// 获取点位信息
  getCabinetInfo() async {
    var res = await CabinetDao.getCabinetInfo('${widget.cabinet.code}');
    if (res != null && res.result) {
      cabinetEntity = CabinetEntity.fromJson(res.data);
      await LocalStorage.save(DefaultConfig().configs.HAS_SITE_OPEN_ADD_CABINET, cabinetEntity.hasAddCabinet,
          isPrivate: true);
      await LocalStorage.save(DefaultConfig().configs.HAS_CHANGE_BRAND, cabinetEntity.hasChangeBrand,
          isPrivate: true);
      if (widget.boxUseType == CabinetBoxUseType.YYGK) {
        getBoxJson(cabinetEntity.bookJson);
      } else if (widget.boxUseType == CabinetBoxUseType.XGZY) {
        getBoxJson(cabinetEntity.rentJson);
      }
      getCabinetUsableBox();
      getCourierCabinetFullInfo();
    }
    setState(() {});
  }

  /// 获取快递员与点位关系
  getCourierCabinetFullInfo() async {
    var res = await CourierDao.getCourierCabinetFullInfo('${widget.cabinet.code}');
    if (res != null && res.result) {
      cabinetEntity.hasCollected = res.data['hasCollected'] ? 1 : 0;
      cabinetEntity.hasUsed = res.data['hasUsed'] ? 1 : 0;
      setState(() {});
    }
  }

  /// 收藏点位
  setCabinetCollect() async {
    var res = await CourierDao.setCabinetCollected('${widget.cabinet.code}', !(cabinetEntity.hasCollected == 1));
    if (res != null && res.result) {
      int isCollect = res.data['hasCollect'];
      cabinetEntity.hasCollected = isCollect;
      Fluttertoast.showToast(msg: isCollect == 1 ? '收藏成功' : '取消收藏成功', gravity: ToastGravity.CENTER);
      setState(() {});
    }
  }

  /// 点位信息
  buildCabinetInfo() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(
          Radius.circular(7),
        ),
        border: Border.all(
          width: 1,
          color: Colors.white,
        ),
      ),
      margin: EdgeInsets.all(marginValue),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.fromLTRB(10, 4, 10, 4),
            child: Row(
              children: [
                Text(
                  '点位',
                  style: TextStyle(fontSize: AppConstant.smallTextSize, color: DefaultConfig().configs.GREY_COLOR),
                ),
                Expanded(
                    child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text(
                      '${cabinetEntity.name}',
                      style: TextStyle(fontSize: 14),
                    )
                  ],
                ))
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.fromLTRB(10, 4, 10, 4),
            child: Row(
              children: [
                Text(
                  '编号',
                  style: TextStyle(fontSize: AppConstant.smallTextSize, color: DefaultConfig().configs.GREY_COLOR),
                ),
                Expanded(
                    child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text(
                      '${cabinetEntity.code}',
                      style: TextStyle(fontSize: 14),
                    )
                  ],
                ))
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.fromLTRB(10, 4, 10, 4),
            child: Row(
              children: [
                Text(
                  '地址',
                  style: TextStyle(fontSize: AppConstant.smallTextSize, color: DefaultConfig().configs.GREY_COLOR),
                ),
                Padding(padding: EdgeInsets.only(left: 10)),
                Expanded(
                  child: Container(
                    alignment: Alignment.centerRight,
                    child: Text(
                      '${cabinetEntity.address}',
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                )
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.fromLTRB(10, 4, 10, 4),
            child: Row(
              children: [
                CollectIconButtonWidget(
                    value: cabinetEntity.hasCollected == 1,
                    onPress: () {
                      setCabinetCollect();
                    }),
                Expanded(
                    child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Offstage(
                      offstage: !(cabinetEntity.hasUsed == 1),
                      child: Container(
                        padding: EdgeInsets.fromLTRB(8, 2, 8, 2),
                        decoration: BoxDecoration(
                            border: Border.all(color: Theme.of(context).primaryColor),
                            borderRadius: BorderRadius.all(Radius.circular(20))),
                        child: Text(
                          '常用柜',
                          style: TextStyle(
                            color: Theme.of(context).primaryColor,
                            fontSize: AppConstant.tinyTextSize,
                          ),
                        ),
                      ),
                    )
                  ],
                ))
              ],
            ),
          )
        ],
      ),
    );
  }

  /// 格口信息
  buildGKInfo() {
    return Container(
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(
            Radius.circular(7),
          ),
          border: Border.all(width: 1, color: Colors.white)),
      margin: EdgeInsets.only(left: marginValue, right: marginValue),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            child: Text(
              '可下单格口',
              style: TextStyle(fontSize: AppConstant.smallTextSize),
            ),
            margin: EdgeInsets.fromLTRB(10, 6, 10, 6),
          ),
          Container(
            margin: EdgeInsets.only(left: 10, right: 10, bottom: 20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(5)),
            ),
            // child: Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   children: buildAvailableGK(),
            // ),
            child: CabinetBoxListUseWidget(emptyList, () {
              setState(() {});
            }),
          )
        ],
      ),
    );
  }

  /// 判断是否选中有
  getChecked() {
    bool isChecked = false;
    emptyList.forEach((item) {
      if (item['checked'] != '0' && checkedValue) {
        isChecked = true;
      }
    });
    return isChecked;
  }

  /// 获取下单价格
  getMoney() {
    num money = 0;
    emptyList.forEach((item) {
      if (item['checked'] != '0' && item['checked'] is String) {
        num data = int.parse(item['checked']) * (item['price'] / 1000);
        money += data;
      }
    });
    return '${money.toStringAsFixed(2)}';
  }

  /// 立即下单
  goCreateOrder() async {
    bool result = await CommonUtils.confirm(context, '下单后将自动从余额扣除金额，请保证余额充足?');
    if (result) {
      Map<String, dynamic> info = {
        'cabinetLocationCode': widget.cabinet.code,
        'minute': int.parse(selectValue),
        'superBoxCount': emptyList[6]['checked'],
        'hugeBoxCount': emptyList[5]['checked'],
        'largeBoxCount': emptyList[4]['checked'],
        'mediumBoxCount': emptyList[3]['checked'],
        'smallBoxCount': emptyList[2]['checked'],
        'miniBoxCount': emptyList[1]['checked'],
        'microBoxCount': emptyList[0]['checked'],
        'orderBookType': widget.boxUseType == CabinetBoxUseType.YYGK ? 1 : 2
      };
      Map maps = {
        0: Courier.courierOrderBoolCreate,
      };
      ResultData res = await HttpManager.post(maps[0]!, info, version: ApiVersion.V2);
      if (res.result) {
        Fluttertoast.showToast(msg: '下单成功');
        Navigator.pop(context);
      }
    }
  }

  /// 底部按钮
  buildBtn() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
          ),
          width: MediaQuery.of(context).size.width / 2,
          height: 60,
          padding: EdgeInsets.fromLTRB(20, 2, 20, 2),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [Text('￥${getMoney()}', style: TextStyle(fontSize: 14))],
          ),
        ),
        BottomBtnWidget(
            showShadow: false,
            title: '立即下单',
            width: MediaQuery.of(context).size.width / 2 - 20,
            type: !getChecked() ? ButtonType.primaryDisabled : ButtonType.primary,
            action: () => throttleUtil.throttle(() async {
                  if (getChecked()) {
                    goCreateOrder();
                  }
                }))
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      backgroundColor: DefaultConfig().configs.BG_COLOR,
      key: cabinetBoxCreatePage,
      appBar: new AppCustomerBar(
        title: AppbarTitle(
          title: getTitle(),
          isCenter: true,
        ),
        actions: [
          InkWell(
            onTap: () async {
              await NavigatorUtils.goCabinetBoxRulesPage(context);
            },
            child: Row(
              children: [
                Text(
                  '规则',
                  style: TextStyle(fontSize: 16.0, color: Colors.black87),
                ),
                Padding(padding: EdgeInsets.only(right: 10)),
              ],
            ),
          )
        ],
      ),
      body: RefreshIndicator(
        onRefresh: onFresh,
        child: Column(
          children: [
            Expanded(
                child: SingleChildScrollView(
                    physics: AlwaysScrollableScrollPhysics(),
                    child: Column(
                      children: [
                        ///点位信息
                        buildCabinetInfo(),

                        /// 格口信息
                        buildGKInfo(),

                        /// 选项信息
                        getRadioList(),

                        // Container(
                        //   decoration: BoxDecoration(
                        //     borderRadius: BorderRadius.all(Radius.circular(5)),
                        //   ),
                        //   child: Row(
                        //     children: [
                        //       Checkbox(
                        //         value: checkedValue,
                        //         onChanged: (bool) {
                        //           checkedValue = bool!;
                        //           setState(() {});
                        //         },
                        //       ),
                        //       Text('我已阅读并同意协议')
                        //     ],
                        //   ),
                        // )
                      ],
                    ))),
            buildBtn()
          ],
        ),
      ),
    );
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;
}
