import 'dart:async';
import 'dart:io';

import 'package:amap_flutter_location/amap_flutter_location.dart';
import 'package:amap_flutter_location/amap_location_option.dart';
import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_book_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_entity.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/net/Address.dart';
import 'package:cabinet_flutter_app/common/net/HttpManager.dart';
import 'package:cabinet_flutter_app/common/net/ResultData.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CabinetBoxUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppListState.dart';
import 'package:cabinet_flutter_app/widget/AppPullLoadWidget.dart';
import 'package:cabinet_flutter_app/widget/AppSearchAdressInputWidget.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/CabinetBoxListWidget.dart';
import 'package:cabinet_flutter_app/widget/DateDownWidget.dart';
import 'package:cabinet_flutter_app/widget/NoResult.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CommonRowWidget.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/ContainerItemWidget.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/MyUnderlineTabIndicator.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

class CabinetBoxUsePage extends StatefulWidget {
  CabinetBoxUseType pageType;

  CabinetBoxUsePage(this.pageType, {Key? key}) : super(key: key);

  @override
  _CabinetBoxUsePage createState() => _CabinetBoxUsePage();
}

class _CabinetBoxUsePage extends State<CabinetBoxUsePage>
    with
        AutomaticKeepAliveClientMixin<CabinetBoxUsePage>,
        AppListState<CabinetBoxUsePage>,
        WidgetsBindingObserver,
        TickerProviderStateMixin {
  ScrollController _controller = new ScrollController();

  double marginValue = 10;
  CabinetEntity cabinetEntity = new CabinetEntity();
  CabinetBookEntity cabinetBookEntity = new CabinetBookEntity();
  late TabController middleTabController;
  bool isFirstRender = true;
  List<Map<String, dynamic>> middleTabs = [
    {'label': '附近快递柜', 'value': 'list'},
    {'label': '我的预约', 'value': CabinetBoxUseType.YYGK},
  ];
  int middleTabIndex = 0;
  Map<int, String> orderStateMap = DefaultConfig().configs.orderStateMap;
  List<dynamic> emptyList = [];

  final GlobalKey<ScaffoldState> cabinetBoxUsePage = GlobalKey<ScaffoldState>();

  String keyword = '';

  int total = 0;
  Map<String, Object>? _locationResult;
  Map<String, dynamic> cabinetLocationMap = new Map<String, dynamic>();
  StreamSubscription<Map<String, Object>>? _locationListener;

  AMapFlutterLocation _locationPlugin = new AMapFlutterLocation();
  TextEditingController controller = TextEditingController();
  bool isCheckNull = true;

  void initState() {
    super.initState();
    getLocation();
    handleRefresh();
    middleTabController = TabController(length: middleTabs.length, vsync: this);
  }
  getLocation() {
    /// [hasShow] 隐私权政策是否弹窗展示告知用户
    AMapFlutterLocation.updatePrivacyShow(true, true);

    /// 设置是否已经取得用户同意，如果未取得用户同意，高德定位SDK将不会工作
    ///
    /// 高德SDK合规使用方案请参考官网地址：https://lbs.amap.com/news/sdkhgsy
    ///
    /// <b>必须保证在调用定位功能之前调用, 建议首次启动App时弹出《隐私政策》并取得用户同意</b>
    ///
    /// [hasAgree] 隐私权政策是否已经取得用户同意
    AMapFlutterLocation.updatePrivacyAgree(true);

    /// 动态申请定位权限
    requestPermission();

    ///设置Android和iOS的apiKey<br>
    ///key的申请请参考高德开放平台官网说明<br>
    ///Android: https://lbs.amap.com/api/android-location-sdk/guide/create-project/get-key
    ///iOS: https://lbs.amap.com/api/ios-location-sdk/guide/create-project/get-key
    AMapFlutterLocation.setApiKey(DefaultConfig().configs.GAO_MAP_KEY, DefaultConfig().configs.GAO_MAP_KEY);

    ///iOS 获取native精度类型
    if (Platform.isIOS) {
      requestAccuracyAuthorization();
    }

    ///注册定位结果监听
    _locationListener = _locationPlugin.onLocationChanged().listen((Map<String, Object> result) {
      if (result.containsKey('latitude')) {
        setState(() {
          _locationResult = result;
          LocalStorage.save(const DefaultConfig().configs.DEFALUT_LOC, _locationResult);
          handleRefresh();
        });
      }
    });
    _startLocation();
  }

  ///获取iOS native的accuracyAuthorization类型
  void requestAccuracyAuthorization() async {
    AMapAccuracyAuthorization currentAccuracyAuthorization = await _locationPlugin.getSystemAccuracyAuthorization();
    if (currentAccuracyAuthorization == AMapAccuracyAuthorization.AMapAccuracyAuthorizationFullAccuracy) {
      print("精确定位类型");
    } else if (currentAccuracyAuthorization == AMapAccuracyAuthorization.AMapAccuracyAuthorizationReducedAccuracy) {
      print("模糊定位类型");
    } else {
      print("未知定位类型");
    }
  }

  /// 动态申请定位权限
  void requestPermission() async {
    // 申请权限
    bool hasLocationPermission = await requestLocationPermission();
    if (hasLocationPermission) {
      print("定位权限申请通过");
    } else {
      print("定位权限申请不通过");
    }
  }

  /// 申请定位权限
  /// 授予定位权限返回true， 否则返回false
  Future<bool> requestLocationPermission() async {
    //获取当前的权限
    var status = await Permission.location.status;
    if (status == PermissionStatus.granted) {
      //已经授权
      return true;
    } else {
      //未授权则发起一次申请
      status = await Permission.location.request();
      if (status == PermissionStatus.granted) {
        return true;
      } else {
        return false;
      }
    }
  }

  ///开始定位
  void _startLocation() {
    ///开始定位之前设置定位参数
    _setLocationOption();
    _locationPlugin.startLocation();
  }

  ///停止定位
  void _stopLocation() {
    _locationPlugin.stopLocation();
  }

  ///设置定位参数
  void _setLocationOption() {
    AMapLocationOption locationOption = new AMapLocationOption();

    ///是否单次定位
    locationOption.onceLocation = true;

    ///是否需要返回逆地理信息
    locationOption.needAddress = true;

    ///逆地理信息的语言类型
    locationOption.geoLanguage = GeoLanguage.DEFAULT;

    locationOption.desiredLocationAccuracyAuthorizationMode = AMapLocationAccuracyAuthorizationMode.ReduceAccuracy;

    locationOption.fullAccuracyPurposeKey = "AMapLocationScene";

    ///设置Android端连续定位的定位间隔
    locationOption.locationInterval = 2000;

    ///设置Android端的定位模式<br>
    ///可选值：<br>
    ///<li>[AMapLocationMode.Battery_Saving]</li>
    ///<li>[AMapLocationMode.Device_Sensors]</li>
    ///<li>[AMapLocationMode.Hight_Accuracy]</li>
    locationOption.locationMode = AMapLocationMode.Hight_Accuracy;

    ///设置iOS端的定位最小更新距离<br>
    locationOption.distanceFilter = -1;

    ///设置iOS端期望的定位精度
    /// 可选值：<br>
    /// <li>[DesiredAccuracy.Best] 最高精度</li>
    /// <li>[DesiredAccuracy.BestForNavigation] 适用于导航场景的高精度 </li>
    /// <li>[DesiredAccuracy.NearestTenMeters] 10米 </li>
    /// <li>[DesiredAccuracy.Kilometer] 1000米</li>
    /// <li>[DesiredAccuracy.ThreeKilometers] 3000米</li>
    locationOption.desiredAccuracy = DesiredAccuracy.Best;

    ///设置iOS端是否允许系统暂停定位
    locationOption.pausesLocationUpdatesAutomatically = false;

    ///将定位参数设置给定位插件
    _locationPlugin.setLocationOption(locationOption);
  }

  // 上拉加载更多
  _getData() async {
    var location = await LocalStorage.getJson(const DefaultConfig().configs.DEFALUT_LOC);

    Map<String, dynamic> info = {
      'longitude': _locationResult?['longitude'],
      'latitude': _locationResult?['latitude'],
      'current': page.toString(),
      'size': 10,
    };
    if (location != null) {
      info['longitude'] = location['longitude'];
      info['latitude'] = location['latitude'];
    }
    if (middleTabIndex == 1) {
      info = {
        'orderBookType': widget.pageType == CabinetBoxUseType.YYGK ? 1 : 2,
        'current': page.toString(),
        'size': 10,
      };
    }
    if (keyword != '') {
      info.putIfAbsent('keyword', () => keyword);
    }
    Map maps = {
      0: Courier.getCourierCabinetLocation,
      1: Courier.courierOrderBookPage,
    };
    ResultData res = await HttpManager.post(maps[middleTabIndex]!, info);

    if (res.result) {
      List list = res.data['records'];
      print(list);
      return new DataResult(list, true, total: int.parse(res.data['total']));
    }
  }

  Future<Null> onLoadMore() async {
    if (isLoading) {
      return null;
    }
    isLoading = true;
    page++;
    var res = await requestLoadMore();
    if (res != null && res.result) {
      setState(() {
        pullLoadWidgetControl.dataList.addAll(res.data);
      });
    }
    resolveDataResult(res);
    isLoading = false;
    return null;
  }

  @protected
  Future<Null> handleRefresh() async {
    if (isLoading) {
      return null;
    }
    refreshIndicatorKey.currentState?.show();
    isLoading = true;
    page = 1;
    var res = await requestRefresh();
    resolveRefreshResult(res);
    resolveDataResult(res);
    if (res.next != null) {
      var resNext = await res.next;
      resolveRefreshResult(resNext);
      resolveDataResult(resNext);
    }
    isLoading = false;
    return null;
  }

  @override
  void dispose() {
    _controller.dispose();

    ///移除定位监听
    if (null != _locationListener) {
      _locationListener?.cancel();
    }

    ///销毁定位
    _locationPlugin.destroy();
    super.dispose();
  }

  @override
  void deactivate() {
    super.deactivate();
  }

  onSearchChanged(String value) {
    keyword = value;
    setState(() {
      if (value == '') {
        isCheckNull = true;
      } else {
        isCheckNull = false;
      }
    });
  }

  onSearchSubmit(String value) {
    handleRefresh();
  }

  setPackageType(int index) {
    pullLoadWidgetControl.dataList.clear();
    setState(() {
      middleTabIndex = index;
    });
    handleRefresh();
  }

  /// 包裹类型
  buildMiddleTabRender(TabController ctrl, List<Map<String, dynamic>> tabs) {
    return Container(
      color: Colors.white,
      child: TabBar(
        indicator: MyUnderlineTabIndicator(borderSide: BorderSide(width: 2, color: Theme.of(context).primaryColor)),
        labelColor: Theme.of(context).primaryColor,
        unselectedLabelColor: Colors.black,
        onTap: setPackageType,
        controller: ctrl,
        tabs: tabs
            .map((e) => new Container(
                  height: 32.0,
                  child: Tab(text: e['label']),
                ))
            .toList(),
      ),
    );
  }

  buildMiddleTab() {
    return buildMiddleTabRender(middleTabController, middleTabs);
  }

  ///  附近快递柜
  _renderEventItem(index) {
    CabinetEntity cabinet = CabinetEntity.fromJson(dataList[index]);
    return ContainerItemWidget(
      boxUseType: widget.pageType,
      ListType.SEARCH,
      cabinet: cabinet,
      bindTap: () {
        // NavigatorUtils.goEmptyLookupPage(context);
      },
      cbIsFresh: (bool isFresh) {
        if (isFresh) {
          handleRefresh();
        }
      },
      cb: (int value) {
        cabinet.hasCollected = value;
        dataList[index] = cabinet.toJson();
        setState(() {});
      },
      bottom: CommonRowWidget(
        label: '包裹状态',
        value: '${cabinet.status}',
        valueColor: Theme.of(context).primaryColor,
      ),
    );
  }

  /// 去订单详情
  goOrderDetail(CabinetBookEntity cabinetBook ,CabinetBoxUseType boxType) async {
    await NavigatorUtils.goCabinetBoxUseDetail(context, (cabinetBook.id).toString(),boxType);
  }

  ///  我的预约
  _renderListItem(index) {
    CabinetBookEntity cabinetBook = CabinetBookEntity.fromJson(dataList[index]);
    return Container(
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(
              Radius.circular(7),
            ),
            border: Border.all(width: 1, color: Colors.white)),
        margin: EdgeInsets.all(marginValue),
        width: double.infinity,
        child: InkWell(
          onTap: () {
            goOrderDetail(cabinetBook,widget.pageType);
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: EdgeInsets.fromLTRB(10, 14, 10, 4),
                child: Row(
                  children: [
                    Text(
                      '${cabinetBook.cabinetLocationName}',
                      style: TextStyle(
                          fontSize: AppConstant.middleTextWhiteSize, color: Colors.black, fontWeight: FontWeight.w600),
                    ),
                    Expanded(
                        child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          '${cabinetBook.cabinetLocationCode}',
                          style: TextStyle(fontSize: 14),
                        )
                      ],
                    ))
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.fromLTRB(10, 0, 10, 4),
                child: CommonRowWidget(
                  leftWidget: Row(
                    children: [
                      Padding(padding: EdgeInsets.only(left: 3)),
                      Text(
                        '下单时间：${cabinetBook.createTime}',
                        style: TextStyle(
                            fontSize: AppConstant.minTextSize, color: DefaultConfig().configs.GREY_LIGHT_COLOR),
                      ),
                      Padding(padding: EdgeInsets.only(left: 3)),
                    ],
                  ),
                ),
              ),
              buildGKInfo(cabinetBook),
              Container(
                margin: EdgeInsets.fromLTRB(10, 0, 0, 4),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    DateDownWidget(cabinetBook.bookEndTime, '保留时间 ', () {}),
                  ],
                ),
              ),
            ],
          ),
        ));
  }

  /// 格口信息
  buildGKInfo(cabinetBook) {
    Map<String, dynamic> countMap = {
      'microCount': cabinetBook.microBoxCount,
      'miniCount': cabinetBook.miniBoxCount,
      'smallCount': cabinetBook.smallBoxCount,
      'mediumCount': cabinetBook.mediumBoxCount,
      'largeCount': cabinetBook.largeBoxCount,
      'hugeCount': cabinetBook.hugeBoxCount,
      'superCount': cabinetBook.superBoxCount,
    };
    emptyList = CabinetBoxUtil.getCabinetBoxAvailableCount(null, countMap);
    return Container(
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(
            Radius.circular(7),
          ),
          border: Border.all(width: 1, color: Colors.white)),
      margin: EdgeInsets.only(left: marginValue, right: marginValue),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(top: 15, bottom: 20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(5)),
              color: Color(0xFFF5F5F5),
            ),
            // child: Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   children: buildAvailableGK(),
            // ),
            child: CabinetBoxListWidget(emptyList, showPrice: false),
          )
        ],
      ),
    );
  }

  /// 获取订单状态颜色
  getOrderStatusColor(status) {
    if (status == 1) {
      return DefaultConfig().configs.ERROR_COLOR;
    } else if (status == 11) {
      return DefaultConfig().configs.GREY_LIGHT_COLOR;
    } else if (status == 12) {
      return DefaultConfig().configs.SUCCESS_COLOR;
    }
  }

  getTitle() {
    Map<CabinetBoxUseType, String> titleMap = {
      CabinetBoxUseType.XGZY: '箱柜租用',
      CabinetBoxUseType.YYGK: '预约格口',
    };
    if (widget.pageType == CabinetBoxUseType.XGZY) {
      this.middleTabs = [
        {'label': '附近快递柜', 'value': 'list'},
        {'label': '我的租用', 'value': CabinetBoxUseType.XGZY},
      ];
    }
    return titleMap[widget.pageType];
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      backgroundColor: Colors.grey.shade100,
      key: cabinetBoxUsePage,
      appBar: AppCustomerBar(
        title: AppbarTitle(
          title: getTitle(),
          isCenter: true,
        ),
        actions: <Widget>[Container(width: 60)],
      ),
      body: Stack(
        children: <Widget>[
          Column(
            children: <Widget>[
              Stack(
                children: [
                  Container(
                    padding: EdgeInsets.fromLTRB(15.0, 0.0, 55.0, 10.0),
                    decoration: BoxDecoration(color: DefaultConfig().configs.WHITE_COLOR),
                    child: AppSearchAdressInputWidget(
                        prefixIcon: Icon(Icons.search_rounded, size: 24),
                        borderRadius: 20,
                        bgColor: DefaultConfig().configs.BG_COLOR,
                        textInputAction: TextInputAction.search,
                        controller: controller,
                        onChanged: onSearchChanged,
                        onSubmitted: onSearchSubmit,
                        hitText: "请输入点位名称或地址进行搜索"),
                  ),
                  Positioned(
                      right: 65,
                      top: 10,
                      child: Offstage(
                        offstage: isCheckNull,
                        child: InkWell(
                          child: Icon(
                            Icons.clear,
                            size: 20,
                          ),
                          onTap: () {
                            setState(() {
                              keyword = '';
                              controller.value = TextEditingValue(text: '');
                              isCheckNull = true;
                            });
                          },
                        ),
                      )),
                  Positioned(
                    right: 15,
                    top: 8,
                    child: InkWell(
                      child: Text(
                        '搜索',
                        style: TextStyle(color: Theme.of(context).primaryColor),
                      ),
                      onTap: () {
                        onSearchSubmit(controller.text);
                      },
                    ),
                  ),
                ],
              ),
              Container(
                padding: EdgeInsets.fromLTRB(10, 5, 10, 0),
                height: 48,
                color: Colors.white,
                child: Column(
                  children: [
                    /// 包裹类型
                    buildMiddleTab(),
                  ],
                ),
              ),
              Expanded(
                  child: dataList.length > 0
                      ? AppPullLoadWidget(
                          pullLoadWidgetControl,
                          (BuildContext context, int index) =>
                              middleTabIndex == 0 ? _renderEventItem(index) : _renderListItem(index),
                          handleRefresh,
                          onLoadMore,
                          refreshKey: refreshIndicatorKey,
                        )
                      : RefreshIndicator(
                          key: refreshIndicatorKey,
                          child: SingleChildScrollView(
                            physics: AlwaysScrollableScrollPhysics(),
                            child: Container(
                              height: 300,
                              child: NoResult(
                                  size: 64,
                                  type: 'box',
                                  subWidget: Container(
                                      padding: EdgeInsets.only(top: 10),
                                      child: Text('', style: AppConstant.smallSubText))),
                            ),
                          ),
                          onRefresh: handleRefresh)),
            ],
          ),
        ],
      ),
    );
  }

// TODO: implement wantKeepAlive
  @override
  bool get wantKeepAlive => true;

// TODO: implement isRefreshFirst
  @override
  bool get isRefreshFirst => false;

  @override
  requestLoadMore() async {
    return await _getData();
  }

  @override
  requestRefresh() async {
    return await _getData();
  }
}
