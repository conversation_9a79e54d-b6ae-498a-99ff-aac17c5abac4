import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CabinetDao.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_entity.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/page_life_cycle.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CabinetBoxUtil.dart';
import 'package:cabinet_flutter_app/common/utils/ThrottleUtil.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/BottomBtnWidget.dart';
import 'package:cabinet_flutter_app/widget/CabinetBoxListUseWidget.dart';
import 'package:cabinet_flutter_app/widget/CollectIconButton.dart';
import 'package:cabinet_flutter_app/widget/CountDownWidget.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class CabinetBoxRulesPage extends StatefulWidget {
  CabinetBoxRulesPage({Key? key}) : super(key: key);

  @override
  _CabinetBoxRulesPageState createState() => _CabinetBoxRulesPageState();
}

class _CabinetBoxRulesPageState extends State<CabinetBoxRulesPage>
    with
        WidgetsBindingObserver,
        PageLifeCycle<CabinetBoxRulesPage>,
        AutomaticKeepAliveClientMixin {
  final GlobalKey<ScaffoldState> cabinetBoxCreatePage =
      GlobalKey<ScaffoldState>();

  ThrottleUtil throttleUtil = ThrottleUtil();
  bool isPda = false;
  double marginValue = 10;
  CabinetEntity cabinetEntity = new CabinetEntity();
  List<dynamic> emptyList = [];
  String selectValue = '150';
  bool checkedValue = false;

  void initState() {
    super.initState();
  }

  init() {
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void onShow() async {}

  Future<Null> onFresh() async {
    init();
  }


  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      backgroundColor: DefaultConfig().configs.BG_COLOR,
      key: cabinetBoxCreatePage,
      appBar: new AppCustomerBar(
        title: AppbarTitle(
          title: '订单规则',
          isCenter: true,
        ),
      ),
      body: RefreshIndicator(
        onRefresh: onFresh,
        child: Column(
          children: [
            Expanded(
                child: SingleChildScrollView(
                    physics: AlwaysScrollableScrollPhysics(),
                    child: Column(
                      children: [
                        Container(
                          padding: EdgeInsets.fromLTRB(10, 5, 10, 0),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.all(Radius.circular(5)),
                          ),
                          child: Column(
                            children: [
                              Text('1、订单支付后，无法取消订单，是否预约成功以APP返回的订单状态为准'),
                              Padding(padding: EdgeInsets.only(top: 10)),
                              Text('2、订单成功后，需在预约保留时间内使用格口(在订单详情可查看剩余保留时间)'),
                              Padding(padding: EdgeInsets.only(top: 10)),
                              Text('3、订单失败或部分失败，将原路退还预约失败格口对应金额'),
                              Padding(padding: EdgeInsets.only(top: 10)),
                              Text('4、预约成功后，未在保留时间内使用则订单失效，不予退款'),
                              Padding(padding: EdgeInsets.only(top: 10)),
                              Text('5、如遇格口故障或有其他疑问，可联系柜机管理员'),
                            ],
                          ),
                        )
                      ],
                    ))),
          ],
        ),
      ),
    );
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;
}
