import 'dart:convert';

import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CabinetDao.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_book_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_entity.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/net/Address.dart';
import 'package:cabinet_flutter_app/common/net/HttpManager.dart';
import 'package:cabinet_flutter_app/common/net/ResultData.dart';
import 'package:cabinet_flutter_app/common/page_life_cycle.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CabinetBoxUtil.dart';
import 'package:cabinet_flutter_app/common/utils/ThrottleUtil.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/CabinetBoxListUseWidget.dart';
import 'package:cabinet_flutter_app/widget/CollectIconButton.dart';
import 'package:cabinet_flutter_app/widget/DateDownWidget.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class CabinetBoxUseDetail extends StatefulWidget {
  final String id;
  CabinetBoxUseType boxType;

  CabinetBoxUseDetail(this.id, this.boxType, {Key? key}) : super(key: key);

  @override
  _CabinetBoxUseDetailState createState() => _CabinetBoxUseDetailState();
}

class _CabinetBoxUseDetailState extends State<CabinetBoxUseDetail>
    with WidgetsBindingObserver, PageLifeCycle<CabinetBoxUseDetail>, AutomaticKeepAliveClientMixin {
  final GlobalKey<ScaffoldState> cabinetBoxCreatePage = GlobalKey<ScaffoldState>();
  CabinetBookEntity cabinetBook = new CabinetBookEntity();
  Map<int, String> orderStateMap = DefaultConfig().configs.orderStateMap;
  Map<int, String> messageStateMap = DefaultConfig().configs.orderListStatus;
  ThrottleUtil throttleUtil = ThrottleUtil();
  bool isPda = false;
  double marginValue = 10;
  CabinetEntity cabinetEntity = new CabinetEntity();
  List<dynamic> emptyList = [];
  bool checkedValue = false;

  void initState() {
    super.initState();
    this.init();
  }

  init() {
    getDetailInfo();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void onShow() async {}

  Future<Null> onFresh() async {
    init();
  }

  getTitle() {
    Map<CabinetBoxUseType, String> titleMap = {
      CabinetBoxUseType.XGZY: '租用详情',
      CabinetBoxUseType.YYGK: '预约详情',
    };
    return titleMap[widget.boxType];
  }

  /// 获取点位可用盒子汇总
  getCabinetUsableBox() async {
    Map<String, dynamic> countMap = {
      'microCount': cabinetBook.microBoxCount,
      'miniCount': cabinetBook.miniBoxCount,
      'smallCount': cabinetBook.smallBoxCount,
      'mediumCount': cabinetBook.mediumBoxCount,
      'largeCount': cabinetBook.largeBoxCount,
      'hugeCount': cabinetBook.hugeBoxCount,
      'superCount': cabinetBook.superBoxCount,
    };
    emptyList = CabinetBoxUtil.getCabinetBoxAvailableCount(
        getBoxJson(cabinetBook.bookJson, value: '${cabinetBook.minute}'), countMap);
    for (var i in emptyList) {
      i['checked'] = '0';
    }
    setState(() {});
  }

  /// 获取展示格口价格JSON
  getBoxJson(json, {String? value}) {
    Map obj = jsonDecode(json);
    if (value != null) {
      return jsonEncode(obj['$value']);
    }
  }

  /// 获取订单信息
  getOrderDetail() async {
    ResultData result = await HttpManager.post(Courier.courierOrderBookDetail, {'id': widget.id});
    if (result.result) {
      cabinetBook = CabinetBookEntity.fromJson(result.data);
    }
    setState(() {});
  }

  /// 获取点位信息
  getDetailInfo() async {
    await getOrderDetail();
    var res = await CabinetDao.getCabinetInfo(cabinetBook.cabinetLocationCode.toString());
    if (res != null && res.result) {
      cabinetEntity = CabinetEntity.fromJson(res.data);
      await LocalStorage.save(DefaultConfig().configs.HAS_SITE_OPEN_ADD_CABINET, cabinetEntity.hasAddCabinet,
          isPrivate: true);
      await LocalStorage.save(DefaultConfig().configs.HAS_CHANGE_BRAND, cabinetEntity.hasChangeBrand,
          isPrivate: true);
      await getCourierCabinetFullInfo();
    }
    await getCabinetUsableBox();
    setState(() {});
  }

  /// 获取快递员与点位关系
  getCourierCabinetFullInfo() async {
    var res = await CourierDao.getCourierCabinetFullInfo(cabinetBook.cabinetLocationCode.toString());
    if (res != null && res.result) {
      cabinetEntity.hasCollected = res.data['hasCollected'] ? 1 : 0;
      cabinetEntity.hasUsed = res.data['hasUsed'] ? 1 : 0;
      setState(() {});
    }
  }

  /// 收藏点位
  setCabinetCollect() async {
    var res = await CourierDao.setCabinetCollected(
        cabinetBook.cabinetLocationCode.toString(), !(cabinetEntity.hasCollected == 1));
    if (res != null && res.result) {
      int isCollect = res.data['hasCollect'];
      cabinetEntity.hasCollected = isCollect;
      Fluttertoast.showToast(msg: isCollect == 1 ? '收藏成功' : '取消收藏成功', gravity: ToastGravity.CENTER);
      setState(() {});
    }
  }

  /// 获取订单状态颜色
  getOrderStatusColor() {
    String? status = orderStateMap[cabinetBook.orderStatus];
    if (status == '已完成') {
      return DefaultConfig().configs.ERROR_COLOR;
    } else if (status == '使用中') {
      return DefaultConfig().configs.SUCCESS_COLOR;
    }
  }

  /// 获取订单格口状态颜色
  getOrderBoxStatusColor(type) {
    if (type == '待使用') {
      return DefaultConfig().configs.SUCCESS_COLOR;
    } else if (type == '已使用') {
      return DefaultConfig().configs.ERROR_COLOR;
    } else if (type == '已过期') {
      return DefaultConfig().configs.ERROR_COLOR;
    }
  }

  /// 点位信息
  buildCabinetInfo() {
    return Container(
      decoration:
      BoxDecoration(color: Colors.white, border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0))),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.fromLTRB(10, 4, 10, 4),
            child: Row(
              children: [
                Text(
                  '点位',
                  style: TextStyle(fontSize: AppConstant.smallTextSize, color: DefaultConfig().configs.GREY_COLOR),
                ),
                Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          '${cabinetEntity.name}',
                          style: TextStyle(fontSize: 14),
                        )
                      ],
                    ))
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.fromLTRB(10, 4, 10, 4),
            child: Row(
              children: [
                Text(
                  '编号',
                  style: TextStyle(fontSize: AppConstant.smallTextSize, color: DefaultConfig().configs.GREY_COLOR),
                ),
                Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          '${cabinetEntity.code}',
                          style: TextStyle(fontSize: 14),
                        )
                      ],
                    ))
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.fromLTRB(10, 4, 10, 4),
            child: Row(
              children: [
                Text(
                  '地址',
                  style: TextStyle(fontSize: AppConstant.smallTextSize, color: DefaultConfig().configs.GREY_COLOR),
                ),
                Padding(padding: EdgeInsets.only(left: 10)),
                Expanded(
                  child: Container(
                    alignment: Alignment.centerRight,
                    child: Text(
                      '${cabinetEntity.address}',
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                )
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.fromLTRB(10, 4, 10, 4),
            child: Row(
              children: [
                CollectIconButtonWidget(
                    value: cabinetEntity.hasCollected == 1,
                    onPress: () {
                      setCabinetCollect();
                    }),
                Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Offstage(
                          offstage: !(cabinetEntity.hasUsed == 1),
                          child: Container(
                            padding: EdgeInsets.fromLTRB(8, 2, 8, 2),
                            decoration: BoxDecoration(
                                border: Border.all(color: Theme
                                    .of(context)
                                    .primaryColor),
                                borderRadius: BorderRadius.all(Radius.circular(20))),
                            child: Text(
                              '常用柜',
                              style: TextStyle(
                                color: Theme
                                    .of(context)
                                    .primaryColor,
                                fontSize: AppConstant.tinyTextSize,
                              ),
                            ),
                          ),
                        )
                      ],
                    ))
              ],
            ),
          )
        ],
      ),
    );
  }

  /// 格口信息
  buildGKInfo() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(left: 10, right: 10, bottom: 20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(5)),
            ),
            child: CabinetBoxListUseWidget(
              emptyList,
                  () {
                setState(() {});
              },
              type: 'details',
            ),
          )
        ],
      ),
    );
  }

  /// 获取格口类型
  getType(type) {
    const boxAvaliableList = {
      0: '极大',
      1: '超大',
      2: '大格',
      3: '中格',
      4: '小格',
      5: '超小',
      6: '极小',
    };
    return boxAvaliableList[type];
  }

  /// 获取使用详情
  List<Widget> getUseDetail() {
    List<Widget> list = [];
    cabinetBook.list?.forEach((item) {
      list.add(Container(
        margin: EdgeInsets.fromLTRB(10, 4, 10, 4),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Container(
              width: 80,
              child: Text('${item.cabinetBoxLabel}号格口'),
            ),
            Container(
              child: Text('${getType(item.cabinetBoxType)}'),
            ),
            Container(
              child: Text('￥${item.fee / 1000}'),
            ),
            Container(
              child: Text(
                '${messageStateMap[item.status]}',
                style: TextStyle(color: getOrderBoxStatusColor(messageStateMap[item.status])),
              ),
            ),
          ],
        ),
      ));
    });
    return list;
  }

  /// 获取使用详情
  getUseDetails() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.fromLTRB(marginValue, 0, marginValue, marginValue),
            decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.all(
                  Radius.circular(7),
                ),
                border: Border.all(width: 1, color: Colors.white)),
            child: Column(children: getUseDetail()),
          ),
        ],
      ),
    );
  }

  getDays(min) {
    if (min != null) {
      min = (min / 24 / 60).toStringAsFixed(0);
    } else {
      min = 0;
    }
    return min;
  }

  /// 获取下单时间
  getTimes() {
    return Container(
      margin: EdgeInsets.fromLTRB(marginValue, 0, marginValue, marginValue),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(
            Radius.circular(7),
          ),
          border: Border.all(width: 1, color: Colors.white)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.fromLTRB(10, 4, 10, 4),
            child: Row(
              children: [
                Text(
                  '下单时间',
                  style: TextStyle(fontSize: AppConstant.smallTextSize, color: DefaultConfig().configs.GREY_COLOR),
                ),
                Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          '${cabinetBook.createTime}',
                          style: TextStyle(fontSize: 14),
                        )
                      ],
                    ))
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.fromLTRB(10, 4, 10, 4),
            child: Row(
              children: [
                Text(
                  '到期时间',
                  style: TextStyle(fontSize: AppConstant.smallTextSize, color: DefaultConfig().configs.GREY_COLOR),
                ),
                Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          '${cabinetBook.bookEndTime}',
                          style: TextStyle(fontSize: 14),
                        )
                      ],
                    ))
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.fromLTRB(10, 4, 10, 4),
            child: Row(
              children: [
                Text(
                  '${widget.boxType == CabinetBoxUseType.YYGK ? "预约时段" : "租用时段"}',
                  style: TextStyle(fontSize: AppConstant.smallTextSize, color: DefaultConfig().configs.GREY_COLOR),
                ),
                Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          '${widget.boxType == CabinetBoxUseType.YYGK ? '${cabinetBook.minute != null ? cabinetBook
                              .minute : 0}分钟' : '${getDays(cabinetBook.minute)}天'}',
                          style: TextStyle(fontSize: 14),
                        )
                      ],
                    ))
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.fromLTRB(10, 4, 10, 4),
            child: Row(
              children: [
                Text(
                  '支付金额',
                  style: TextStyle(fontSize: AppConstant.smallTextSize, color: DefaultConfig().configs.GREY_COLOR),
                ),
                Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          '￥${cabinetBook.totalFee != null ? (cabinetBook.totalFee! / 1000) : 0}',
                          style: TextStyle(fontSize: 14),
                        )
                      ],
                    ))
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      backgroundColor: DefaultConfig().configs.BG_COLOR,
      key: cabinetBoxCreatePage,
      appBar: new AppCustomerBar(
        title: AppbarTitle(
          title: getTitle(),
          isCenter: true,
        ),
        actions: [
          Container(
            width: 60,
          )
        ],
      ),
      body: RefreshIndicator(
        onRefresh: onFresh,
        child: Column(
          children: [
            Expanded(
                child: SingleChildScrollView(
                    physics: AlwaysScrollableScrollPhysics(),
                    child: Column(
                      children: [
                        Container(
                          margin: EdgeInsets.all(marginValue),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.all(
                                Radius.circular(7),
                              ),
                              border: Border.all(width: 1, color: Colors.white)),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [

                              /// 订单信息
                              Container(
                                padding: EdgeInsets.fromLTRB(10, 4, 10, 4),
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0))),
                                child: Row(
                                  children: [
                                    Text(
                                      '订单号：${cabinetBook.orderNo}',
                                      style: TextStyle(
                                          fontSize: AppConstant.smallTextSize,
                                          color: DefaultConfig().configs.GREY_COLOR),
                                    ),
                                    Expanded(
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.end,
                                          children: [
                                            Text('${orderStateMap[cabinetBook.orderStatus]}',
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  color: getOrderStatusColor(),
                                                ))
                                          ],
                                        ))
                                  ],
                                ),
                              ),

                              ///点位信息
                              buildCabinetInfo(),

                              /// 格口信息
                              buildGKInfo(),

                              /// 订单金额
                              Container(
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    border: Border(top: BorderSide(width: 1, color: Color(0xFFE5E5E5))),
                                  ),
                                  width: double.infinity,
                                  child: Container(
                                      margin: EdgeInsets.fromLTRB(10, 4, 10, 0),
                                      child: Row(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text('合计支付金额'),
                                          Container(
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.end,
                                              children: [
                                                Text(
                                                    '￥${cabinetBook.totalFee != null
                                                        ? (cabinetBook.totalFee! / 1000)
                                                        : 0}'),
                                                DateDownWidget(cabinetBook.bookEndTime, '剩余保留时间:', () {
                                                  getOrderDetail();
                                                }),
                                              ],
                                            ),
                                          )
                                        ],
                                      )))
                            ],
                          ),
                        ),

                        /// 获取使用详情
                        getUseDetails(),

                        /// 获取下单时间
                        getTimes(),
                      ],
                    ))),
          ],
        ),
      ),
    );
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;
}
