import 'dart:async';
import 'dart:io';

import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CabinetDao.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/dao/WaybillDao.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/package_search_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/package_view_entity.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppListState.dart';
import 'package:cabinet_flutter_app/widget/AppPullLoadWidget.dart';
import 'package:cabinet_flutter_app/widget/AppSearchAdressInputWidget.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/BottomBtnWidget.dart';
import 'package:cabinet_flutter_app/widget/CountDownWidget.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/PackageItemWidget.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class CabinetPackagePage extends StatefulWidget {
  final CabinetEntity cabinetInfo; // 点位信息
  final PackageType type; // 列表类型
  final int overDays; // 超时取出时间
  final String hostIndex;

  CabinetPackagePage(this.cabinetInfo, this.type, this.overDays, this.hostIndex, {Key? key}) : super(key: key);

  @override
  _CabinetPackagePageState createState() => _CabinetPackagePageState();
}

class _CabinetPackagePageState extends State<CabinetPackagePage>
    with
        AutomaticKeepAliveClientMixin<CabinetPackagePage>,
        AppListState<CabinetPackagePage> {
  final GlobalKey<ScaffoldState> _cabinetWaitListKey =
      GlobalKey<ScaffoldState>();
  int total = 0;
  GlobalKey<CountDownWidgetState> countDownKey = GlobalKey();
  bool hasFeedBack = false;
  Timer? boxStatusTimer;
  bool isCheckBox = true;
  TextEditingController controller = TextEditingController();
  String keyword = '';
  bool isCheckNull = true;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    boxStatusTimer?.cancel();
    countDownKey.currentState?.dispose();
  }

  Future<Null> onFresh() async {
    _getData(isRefresh: true);
  }

  onSearchChanged(String value) {
    keyword = value;
    setState(() {
      if (value == '') {
        isCheckNull = true;
      } else {
        isCheckNull = false;
      }
    });
  }

  onSearchSubmit(String value) {
    handleRefresh();
  }

  // 上拉加载更多
  _getData({isRefresh = false}) async {
    String code = widget.cabinetInfo.code!;
    PackageSearchEntity packageSearchEntity = PackageSearchEntity();
    packageSearchEntity.cabinetLocationCode = code;
    packageSearchEntity.outbound = 0;
    packageSearchEntity.beginYmd = '';
    packageSearchEntity.size = 20;
    packageSearchEntity.keyword = keyword;
    packageSearchEntity.current = page.toString();
    packageSearchEntity.keepEffectStatus =
        DefaultConfig().configs.listTypeState[widget.type];
    DataResult _res = await CourierDao.courierCabinetOrderPagList(
        packageSearchEntity.toJson());
    if (_res.result) {
      total = _res.total!;
    }
    return _res;
  }

  /// 开门检查
  openCabinetCheck(PackageViewEntity item) {
    CommonUtils.customConfirm(context, '是否打开柜门检查包裹？',
        title: '开门检查',
        showClose: false,
        actions: <Widget>[
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                },
                child: Text('取消',
                    style:
                        TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
                style: ButtonStyle(foregroundColor:
                    MaterialStateProperty.resolveWith((states) {
                  return Colors.black;
                }), backgroundColor:
                    MaterialStateProperty.resolveWith((states) {
                  return DefaultConfig().configs.WHITE_COLOR;
                }))),
            TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                  LoadingUtil(
                    status: '正在开门...',
                  ).show(context);
                  DataResult res =
                      await CourierDao.courierBoxOpenCheck(item.cabinetBoxId, hostIndex: widget.hostIndex);
                  LoadingUtil.dismiss(context);
                  if (res.result) {
                    showBoxOpen(context, item, (item) {});
                    // boxStatusTimer = Timer.periodic(Duration(milliseconds: 2000), (timer) async {
                    //   var result = await CabinetDao.cabinetBoxOpenStatus(item.cabinetBoxId!);
                    //   if (result != null && result.result) {
                    //     if (!result.data) {
                    //       if (!isCheckBox) {
                    //         return;
                    //       }
                    //       resetCountDown();
                    //       boxStatusTimer?.cancel();
                    //       Navigator.of(context).pop();
                    //     }
                    //   }
                    // });
                  }
                },
                child: Text('是的',
                    style:
                        TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
                style: ButtonStyle(foregroundColor:
                    MaterialStateProperty.resolveWith((states) {
                  return Colors.black;
                }), backgroundColor:
                    MaterialStateProperty.resolveWith((states) {
                  return Colors.white;
                }))),
          ])
        ]);
  }

  /// 格口打开
  showBoxOpen(BuildContext context, PackageViewEntity item, onPressed(item)) {
    return showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
        topLeft: Radius.circular(10.0),
        topRight: Radius.circular(10.0),
      )),
      builder: (BuildContext _context) {
        return StatefulBuilder(
          builder: (_context, state) {
            return Stack(
              children: [
                Container(
                  height: 30.0,
                  width: double.infinity,
                  color: Colors.black54,
                ),
                Container(
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      )),
                ),
                Container(
                  height: 550,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            LocalImageUtil.getImageAsset('boxOpen',
                                width:
                                    MediaQuery.of(context).size.width * 0.28),
                            Padding(padding: EdgeInsets.only(bottom: 10)),
                            RichText(
                                text: TextSpan(children: <TextSpan>[
                              TextSpan(
                                text: '${item.cabinetName ?? ''}-',
                                style: TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontSize: 16),
                              ),
                              TextSpan(
                                text: '${item.cabinetBoxLabel}号',
                                style: TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontSize: 16),
                              ),
                              TextSpan(
                                text: ' 格口已开 ',
                                style: TextStyle(
                                    color: Colors.black, fontSize: 14),
                              ),
                            ])),
                            Padding(padding: EdgeInsets.only(bottom: 10)),
                            Offstage(
                              offstage: hasFeedBack,
                              child: Text('放入包裹后请关门',
                                  style: TextStyle(
                                      color: Color(0xFF999999), fontSize: 14)),
                            ),
                          ],
                        ),
                      ),
                      Offstage(
                          offstage: hasFeedBack,
                          child: BottomBtnWidget(
                              showShadow: false,
                              title: '我已关门',
                              action: () {
                                closeCabinetBox(item.cabinetBoxId!);
                              }))
                    ],
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  closeCabinetBox(String cabinetBoxId) async {
    resetCountDown();
    LoadingUtil(
      status: '关门请求中...',
    ).show(context);
    var res = await CabinetDao.cabinetBoxCloseDoor(cabinetBoxId);
    LoadingUtil.dismiss(context);
    if (res.result) {
      isCheckBox = false;
      boxStatusTimer?.cancel();
      Navigator.pop(context);
      resetCountDown();
    }
  }

  /// 一键重投
  reDispatch(PackageViewEntity item) async {
    resetCountDown();
    bool result = await CommonUtils.confirm(context, '重投会自动重置计费，不做任何物流轨迹变更',
        title: '确认重投');
    if (result) {
      DataResult res =
          await WaybillDao.reCalculateFee(item.cabinetLocationCode, item.id);
      if (res.result) {
        handleRefresh();
        Fluttertoast.showToast(msg: '重投成功');
      }
    }
  }

  /// 异常出库
  unusualOut(PackageViewEntity item) {
    CommonUtils.customConfirm(context, '是否进行异常出库操作？', title: '出库提示', showClose: false, actions: <Widget>[
      Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
            },
            child: Text('取消', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
            style: ButtonStyle(foregroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.black;
            }), backgroundColor: MaterialStateProperty.resolveWith((states) {
              return DefaultConfig().configs.WHITE_COLOR;
            }))),
        TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              var cabinetLocationCode = item.storeType == 1 ? item.cabinetLocationCode : '0';
              var backReason = '取出包裹退回';
              var res = await CourierDao.courierUnusualOutWaybill(cabinetLocationCode, item.id, backReason, hostIndex:widget.hostIndex);
              if (res.result) {
                handleRefresh();
                Fluttertoast.showToast(msg: '出库成功');
              }
            },
            child: Text('是的', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
            style: ButtonStyle(foregroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.black;
            }), backgroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.white;
            }))),
      ])
    ]);
    // CommonUtils.customConfirmByReason(context, '是否进行异常出库操作？', (backReason) async {
    //   Navigator.of(context).pop();
    //   LoadingUtil(
    //     status: '正在出库...',
    //   ).show(context);
    //   var cabinetLocationCode = item.storeType == 1 ? item.cabinetLocationCode : '0';
    //   LoadingUtil.dismiss(context);
    //   var res = await CourierDao.courierUnusualOutWaybill(cabinetLocationCode, item.id, backReason);
    //   if (res.result) {
    //     handleRefresh();
    //     Fluttertoast.showToast(msg: '出库成功');
    //   }
    // }, title: '出库提示', changeText: 'unusualOut', showClose: false, showInput: true);
  }

  /// 取出
  pickOut(PackageViewEntity item) {
    resetCountDown();
    CommonUtils.customConfirm(context, '是否打开柜门取出包裹？',
        title: '开门提示',
        showClose: false,
        actions: <Widget>[
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                },
                child: Text('取消',
                    style:
                        TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
                style: ButtonStyle(foregroundColor:
                    MaterialStateProperty.resolveWith((states) {
                  return Colors.black;
                }), backgroundColor:
                    MaterialStateProperty.resolveWith((states) {
                  return DefaultConfig().configs.WHITE_COLOR;
                }))),
            TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                  LoadingUtil(
                    status: '正在取出...',
                  ).show(context);
                  var res = await CourierDao.courierOutboundWaybill(
                      item.cabinetLocationCode, item.id);
                  LoadingUtil.dismiss(context);
                  if (res.result) {
                    handleRefresh();
                    Fluttertoast.showToast(msg: '取出成功');
                  }
                },
                child: Text('是的',
                    style:
                        TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
                style: ButtonStyle(foregroundColor:
                    MaterialStateProperty.resolveWith((states) {
                  return Colors.black;
                }), backgroundColor:
                    MaterialStateProperty.resolveWith((states) {
                  return Colors.white;
                }))),
          ])
        ]);
  }

  buildBottom(PackageViewEntity item) {
    bool isOver7 = CheckUtils.checkEffectTimeIsOverTime(item.keepEffectTime,
        overDueDay: widget.overDays);
    return Container(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Offstage(
            offstage: false,
            child: OutlinedButton(
              child: Text("开门检查",
                  style: TextStyle(
                      fontSize: AppConstant.smallTextSize,
                      color: Theme.of(context).primaryColor)),
              onPressed: () {
                openCabinetCheck(item);
              },
              style: ButtonStyle(
                  minimumSize: MaterialStateProperty.all(Size(80, 32)),
                  shape: MaterialStateProperty.all(StadiumBorder()),
                  side: MaterialStateProperty.all(
                      BorderSide(color: Theme.of(context).primaryColor))),
            ),
          ),
          Container(
              child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Offstage(
                offstage: !isOver7,
                child: Container(
                  height: 32,
                  child: OutlinedButton(
                    child: Text("一键重投",
                        style: TextStyle(
                            fontSize: AppConstant.smallTextSize,
                            color: Theme.of(context).primaryColor)),
                    onPressed: () {
                      reDispatch(item);
                    },
                    style: ButtonStyle(
                        minimumSize: MaterialStateProperty.all(Size(80, 32)),
                        shape: MaterialStateProperty.all(StadiumBorder()),
                        side: MaterialStateProperty.all(
                            BorderSide(color: Theme.of(context).primaryColor))),
                  ),
                ),
              ),
              Padding(padding: EdgeInsets.only(left: 10)),
              Container(
                  height: 32,
                  child: OutlinedButton(
                    child: Text("取出退回",
                        style: TextStyle(
                            fontSize: AppConstant.smallTextSize,
                            color: Theme.of(context).primaryColor)),
                    onPressed: () {
                      unusualOut(item);
                    },
                    style: ButtonStyle(
                        minimumSize: MaterialStateProperty.all(Size(80, 32)),
                        shape: MaterialStateProperty.all(StadiumBorder()),
                        side: MaterialStateProperty.all(
                            BorderSide(color: Theme.of(context).primaryColor))),
                  ))
            ],
          ))
        ],
      ),
      margin: EdgeInsets.only(top: 7),
    );
  }

  _renderEventItem(int index) {
    PackageViewEntity item = PackageViewEntity.fromJson(dataList[index]);
      return PackageItemWidget(widget.type,
          indexNo: dataList.length - index, package: item, bindTap: () {
        NavigatorUtils.goPackageDetailPage(context, item);
      }, bottom: buildBottom(item), overDueDays: widget.overDays);
  }

  getTitle() {
    Map<PackageType, dynamic> titleMap = {
      PackageType.ZL: '滞留列表',
      PackageType.DQ: '待取列表',
      PackageType.DS: '待收列表'
    };
    return titleMap[widget.type];
  }

  resetCountDown() {
    countDownKey.currentState?.resetCountDown();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    String title = getTitle();
    return Scaffold(
      backgroundColor: DefaultConfig().configs.BG_COLOR,
      key: _cabinetWaitListKey,
      appBar: new AppCustomerBar(
        title: AppbarTitle(
          title: title,
          isCenter: true,
        ),
        actions: [
          CountDownWidget(
            countDownKey,
            isBlack: true,
            onTimerFinish: () {
              NavigatorUtils.goHome(context);
            },
          ),
        ],
      ),
      // appBar: AppBar(backgroundColor: Theme.of(context).primaryColor, elevation: 1, title: buildTitle(title)),
      body: new Stack(
        children: <Widget>[
          new Column(
            children: <Widget>[
              Stack(
                children: [
                  Container(
                    padding: EdgeInsets.fromLTRB(15.0, 0.0, 55.0, 10.0),
                    decoration: BoxDecoration(
                        color: DefaultConfig().configs.WHITE_COLOR),
                    child: AppSearchAdressInputWidget(
                        prefixIcon: Icon(Icons.search_rounded, size: 24),
                        borderRadius: 20,
                        bgColor: DefaultConfig().configs.BG_COLOR,
                        textInputAction: TextInputAction.search,
                        controller: controller,
                        onChanged: onSearchChanged,
                        keyboardType: Platform.isAndroid ? TextInputType.number : TextInputType.text,
                        onSubmitted: onSearchSubmit,
                        hitText: "请输入单号/手机号/取件码进行搜索"),
                  ),
                  Positioned(
                      right: 65,
                      top: 10,
                      child: Offstage(
                        offstage: isCheckNull,
                        child: InkWell(
                          child: Icon(
                            Icons.clear,
                            size: 20,
                          ),
                          onTap: () {
                            setState(() {
                              keyword = '';
                              controller.value = TextEditingValue(text: '');
                              isCheckNull = true;
                            });
                          },
                        ),
                      )),
                  Positioned(
                    right: 15,
                    top: 8,
                    child: InkWell(
                      child: Text(
                        '搜索',
                        style: TextStyle(color: Theme.of(context).primaryColor),
                      ),
                      onTap: () {
                        onSearchSubmit(controller.text);
                      },
                    ),
                  ),
                ],
              ),
              Container(
                padding: EdgeInsets.fromLTRB(10, 8, 10, 2),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      widget.cabinetInfo.name ?? '',
                      style:
                          TextStyle(color: DefaultConfig().configs.GREY_COLOR),
                    ),
                    Text('$total'),
                  ],
                ),
              ),
              new Expanded(
                child: new RefreshIndicator(
                  onRefresh: onFresh,
                  child: AppPullLoadWidget(
                    pullLoadWidgetControl,
                    (BuildContext context, int index) =>
                        _renderEventItem(index),
                    handleRefresh,
                    onLoadMore,
                    refreshKey: refreshIndicatorKey,
                  ),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }

  // TODO: implement wantKeepAlive
  @override
  bool get wantKeepAlive => true;

  // TODO: implement isRefreshFirst
  @override
  bool get isRefreshFirst => true;

  @override
  requestLoadMore() async {
    return await _getData();
  }

  @override
  requestRefresh() async {
    return await _getData(isRefresh: true);
  }
}
