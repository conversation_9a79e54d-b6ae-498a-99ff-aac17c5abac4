import 'dart:async';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/utils/text_util.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppListState.dart';
import 'package:cabinet_flutter_app/widget/AppPullLoadWidget.dart';
import 'package:cabinet_flutter_app/widget/AppSearchAdressInputWidget.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/BottomBtnWidget.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CommonRowWidget.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class InterceptListPage extends StatefulWidget {
  InterceptListPage({Key? key}) : super(key: key);

  @override
  _InterceptListPageState createState() => _InterceptListPageState();
}

class _InterceptListPageState extends State<InterceptListPage>
    with
        AutomaticKeepAliveClientMixin<InterceptListPage>,
        AppListState<InterceptListPage>,
        WidgetsBindingObserver,
        SingleTickerProviderStateMixin {
  ScrollController _controller = new ScrollController();

  final GlobalKey<ScaffoldState> interceptListPageKey =
      GlobalKey<ScaffoldState>();

  String? keyword;

  int total = 0;
  bool showOperats = true;

  TextEditingController textEditingController = new TextEditingController();
  bool isCheckNull = true;

  void initState() {
    super.initState();
    this.initData();
  }

  initData() async {
    // 初始化数据，AppListState会自动调用刷新
    // 由于isRefreshFirst为true，页面会自动获取拦截单列表
  }

  // 上拉加载更多
  _getData({isRefresh = false}) async {
    if (isRefresh) {
      page = 1;
    }

    Map<String, dynamic> info = {
      'current': page.toString(),
      'size': 10,
    };
    
    // 根据搜索关键词添加搜索参数
    if (keyword != null && keyword!.isNotEmpty) {
      if (keyword!.length == 5) {
        // 如果输入的是5位数，使用waybillNoLast5参数
        info['waybillNoLast5'] = keyword;
      } else {
        // 否则使用waybillNo参数
        info['waybillNo'] = keyword;
      }
    }
    
    DataResult res = await CourierDao.getInterceptList(info);
    total = res.total!;
    return res;
  }

  Future<Null> onLoadMore() async {
    if (isLoading) {
      return null;
    }
    isLoading = true;
    page++;
    var res = await requestLoadMore();
    if (res != null && res.result) {
      setState(() {
        pullLoadWidgetControl.dataList.addAll(res.data);
      });
    }
    resolveDataResult(res);
    isLoading = false;
    return null;
   
  }

  onSearchChanged(String value) {
    if (value.length == 0) {
      setState(() {
        isCheckNull = true;
      });
    } else {
      setState(() {
        isCheckNull = false;
      });
    }
  }

  onSearchSubmit(String value) {
    if (value.trim().length == 0) {
      setState(() {
        keyword = null;
      });
    } else {
      setState(() {
        keyword = value.trim();
      });
    }
    handleRefresh();
  }

  toDetail(BuildContext context, dynamic item) async {
    // await NavigatorUtils.goInterceptDetailPage(context, item);
    handleRefresh();
  }

  // 显示删除确认对话框
  Future<bool?> _showDeleteConfirmDialog(dynamic item) async {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('确认删除'),
          content: Text('确定要删除运单号 "${item['waybillNo'] ?? ''}" 的拦截单吗？'),
          actions: <Widget>[
            TextButton(
              child: Text('取消'),
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
            TextButton(
              child: Text(
                '确定',
                style: TextStyle(color: Colors.red),
              ),
              onPressed: () {
                Navigator.of(context).pop(true);
              },
            ),
          ],
        );
      },
    ).then((confirmed) async {
      if (confirmed == true) {
        await _deleteIntercept(item);
        return false; // 返回false防止Dismissible自动删除，我们手动处理
      }
      return false;
    });
  }

  // 删除拦截单
  Future<void> _deleteIntercept(dynamic item) async {
    try {
      String waybillNo = item['waybillNo'] ?? '';
      if (waybillNo.isEmpty) {
        Fluttertoast.showToast(msg: '运单号不能为空');
        return;
      }

      DataResult result = await CourierDao.removeInterceptNo(waybillNo);

      if (result.result) {
        Fluttertoast.showToast(msg: '删除成功');
        // 刷新列表
        handleRefresh();
      } else {
        Fluttertoast.showToast(msg: '删除失败');
      }
    } catch (e) {
      print('删除拦截单错误: $e');
      Fluttertoast.showToast(msg: '删除失败，请重试');
    }
  }

  Widget _renderEventItem(int index) {
    if (pullLoadWidgetControl.dataList.length == 0) {
      return Container();
    }
    var item = pullLoadWidgetControl.dataList[index];
    return Dismissible(
      key: Key(item['waybillNo'] ?? index.toString()),
      direction: DismissDirection.endToStart,
      background: Container(
        margin: EdgeInsets.only(left: 10, right: 10, top: 5, bottom: 5),
        decoration: BoxDecoration(
          color: Colors.red,
          borderRadius: BorderRadius.circular(8),
        ),
        alignment: Alignment.centerRight,
        padding: EdgeInsets.only(right: 20),
        child: Icon(
          Icons.delete,
          color: Colors.white,
          size: 24,
        ),
      ),
      confirmDismiss: (direction) async {
        return await _showDeleteConfirmDialog(item);
      },
      child: InkWell(
        onTap: () => toDetail(context, item),
        child: Container(
          margin: EdgeInsets.only(left: 10, right: 10, top: 5, bottom: 5),
          padding: EdgeInsets.all(15),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 3,
                offset: Offset(0, 1),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    item['waybillNo'] ?? '',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getStatusColor(_isActive(item['expireTime']) ? 'ACTIVE' : 'CANCELLED'),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _getStatusText(_isActive(item['expireTime']) ? 'ACTIVE' : 'CANCELLED'),
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8),
              Text(
                '快递公司: ${_getExpressCompanyName(item['brandCode'])}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
              SizedBox(height: 4),
              Text(
                '拦截信息: ${item['message'] ?? ''}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
              SizedBox(height: 4),
              Text(
                    '到期时间: ${item['expireTime'] ?? ''}',
                    style: TextStyle(
                      fontSize: 12,
                      color: const Color.fromARGB(255, 153, 111, 14),
                    ),
                  ),
                  Text(
                    '创建时间: ${item['createTime'] ?? ''}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                  ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String? status) {
    switch (status) {
      case 'ACTIVE':
        return Colors.orange;
      case 'COMPLETED':
        return Colors.green;
      case 'CANCELLED':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String? status) {
    switch (status) {
      case 'ACTIVE':
        return '拦截中';
      case 'CANCELLED':
        return '已失效';
      default:
        return '未知';
    }
  }

 bool _isActive(String? expireTimeStr) {
  if (expireTimeStr == null || expireTimeStr.isEmpty) {
    return false;
  }
  try {
    // 解析时间字符串
    // 注意：这里假设格式固定为 "yyyy-M-d H:m:s"
    final expireTime = DateTime.parse(expireTimeStr);
    return expireTime.isAfter(DateTime.now());
  } catch (e) {
    print('时间解析错误: $e');
    return false;
  }
}

  String _getExpressCompanyName(String? code) {
    Map<String, String> companyMap = {
    "SF": "顺丰速运",
    "ZTO": "中通快递",
    "YTO": "圆通速递",
    "STO": "申通快递",
    "YUNDA": "韵达速递",
    "HTKY": "百世快递",
    "DBKD": "德邦快递",
    "JD": "京东快递",
    "EMS": "邮政EMS",
    "POSTB": "邮政",
    "TTKDEX": "天天快递",
    "FW": "丰网",
    "GTO": "国通快递",
    'JT': '极兔速递',
    "QFKD": "全峰快递",
    "UC": "优速快递",
    "FAST": "快捷快递",
    "TM": "天猫",
    "SNWL": "苏宁物流",
    "CAINIAO": "菜鸟",
    "UNKNOW": "其他"
    };
    
    return companyMap[code] ?? code ?? '未知';
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      backgroundColor: Colors.grey.shade100,
      key: interceptListPageKey,
      appBar: AppCustomerBar(
        title: AppbarTitle(
          title: '拦截单号',
          isCenter: true,
        ),
        actions: <Widget>[
          Container(
            width: 60,
          )
        ],
      ),
      body: Stack(
        children: <Widget>[
          Column(
            children: <Widget>[
              Stack(alignment: Alignment.topRight, children: [
                Container(
                  padding: EdgeInsets.fromLTRB(15.0, 0.0, 55.0, 10.0),
                  decoration:
                      BoxDecoration(color: DefaultConfig().configs.WHITE_COLOR),
                  child: AppSearchAdressInputWidget(
                      prefixIcon: Icon(Icons.search_rounded, size: 24),
                      borderRadius: 20,
                      bgColor: DefaultConfig().configs.BG_COLOR,
                      textInputAction: TextInputAction.search,
                      controller: textEditingController,
                      onChanged: onSearchChanged,
                      onSubmitted: onSearchSubmit,
                      hitText: "请输入快递单号查询"),
                ),
                Positioned(
                    right: 65,
                    top: 10,
                    child: Offstage(
                      offstage: isCheckNull,
                      child: InkWell(
                        child: Icon(
                          Icons.clear,
                          size: 20,
                        ),
                        onTap: () {
                          setState(() {
                            keyword = '';
                            textEditingController.value =
                                TextEditingValue(text: '');
                            isCheckNull = true;
                          });
                        },
                      ),
                    )),
                Positioned(
                  right: 15,
                  top: 8,
                  child: InkWell(
                    child: Text(
                      '搜索',
                      style: TextStyle(color: Theme.of(context).primaryColor),
                    ),
                    onTap: () {
                      onSearchSubmit(textEditingController.text);
                    },
                  ),
                ),
              ]),
              CommonRowWidget(
                leftWidget: Container(
                  padding: EdgeInsets.only(left: 10),
                  child: Text(
                    '拦截单号列表',
                    style: TextStyle(color: Color(0xFF999999), fontSize: 14),
                  ),
                ),
                rightWidget: Container(
                    padding: EdgeInsets.only(right: 10),
                    child: Text('共计:  ${total.toString()}',
                        style: TextStyle(color: Color(0xFF999999)))),
              ),
              Expanded(
                child: RefreshIndicator(
                  onRefresh: onFresh,
                  child: AppPullLoadWidget(
                    pullLoadWidgetControl,
                    (BuildContext context, int index) =>
                        _renderEventItem(index),
                    handleRefresh,
                    onLoadMore,
                    refreshKey: refreshIndicatorKey,
                  ),
                ),
              ),
              BottomBtnWidget(
                showShadow: true,
                type: ButtonType.primary,
                title: '添加拦截单号',
                action: () async {
                  await NavigatorUtils.goAddInterceptPage(context);
                  handleRefresh();
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;

  @override
  bool get isRefreshFirst => true;

  @override
  requestLoadMore() async {
    return await _getData();
  }

  Future<Null> onFresh() async {
    await _getData(isRefresh: true);
    return null;
  }

  @override
  requestRefresh() async {
    return await _getData();
  }
}
