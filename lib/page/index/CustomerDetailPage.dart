import 'dart:math' as math;

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/entitys/customer_entity.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/RegExpUtil.dart';
import 'package:cabinet_flutter_app/common/utils/SoundUtils.dart';
import 'package:cabinet_flutter_app/widget/BottomBtnWidget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';

//主页
class CustomerDetailPage extends StatefulWidget {
  final CustomerEntity? customerEntity;

  CustomerDetailPage(this.customerEntity, {Key? key}) : super(key: key);

  @override
  _CustomerDetailPageState createState() => _CustomerDetailPageState();
}

class _CustomerDetailPageState extends State<CustomerDetailPage> with SingleTickerProviderStateMixin {
  late Animation<double> animation;
  late AnimationController? controller;
  CustomerEntity customer = new CustomerEntity();

  TextEditingController nameEditingController = TextEditingController();
  TextEditingController mobileEditingController = TextEditingController();

  // TextEditingController addressEditingController = TextEditingController();
  // TextEditingController remarksEditingController = TextEditingController();

  String focusName = ''; // 当前聚焦输入框

  List<String> labels = [];
  int serviceLabelLength = 0;
  bool showDeleteButton = false;
  bool isCourier = true;

  @override
  void initState() {
    super.initState();
    init();
  }

  init() async {
    setCustomer();
    await getUserLabels();
    isCourier = CheckUtils.isCourier(context);
    controller = AnimationController(duration: const Duration(milliseconds: 2000), vsync: this);
    animation = TweenSequence<double>([
      //使用TweenSequence进行多组补间动画
      TweenSequenceItem<double>(tween: Tween(begin: 0, end: 15), weight: 1),
      TweenSequenceItem<double>(tween: Tween(begin: 15, end: 0), weight: 2),
      TweenSequenceItem<double>(tween: Tween(begin: 0, end: -15), weight: 3),
      TweenSequenceItem<double>(tween: Tween(begin: -15, end: 0), weight: 4),
    ]).animate(controller!)
      ..addListener(() {
        setState(() {});
      })
      ..addStatusListener((s) {
        if (s == AnimationStatus.completed) {
          setState(() {});
        }
      });
  }

  @override
  void deactivate() {
    super.deactivate();
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }

  getUserLabels() async {
    DataResult res = await CourierDao.getCustomerLabelList();
    if (res.result) {
      labels = res.data.cast<String>();
      serviceLabelLength = res.data.length;
    }
    if (mounted) {
      setState(() {});
    }
  }

  // // 选择省市区
  // changeArea(_locationCode) async {
  //   if (_locationCode == '') {
  //     _locationCode = '110101';
  //   }
  //   Result result = await CityPickers.showCityPicker(context: context, locationCode: _locationCode);
  //   if (result != null) {
  //     setState(() {
  //       customer.provinceName = result.provinceName;
  //       customer.provinceCode = result.provinceId;
  //       customer.cityName = result.cityName;
  //       customer.cityCode = result.cityId;
  //       customer.countyName = result.areaName;
  //       customer.countyCode = result.areaId;
  //     });
  //   }
  // }

  setCustomer() async {
    if (widget.customerEntity != null) {
      customer = widget.customerEntity!;
      nameEditingController.value = TextEditingValue(text: customer.name);
      mobileEditingController.value = TextEditingValue(text: customer.mobile);
      // remarksEditingController.value = TextEditingValue(text: customer.address ?? '');
    }
  }

  // buildLabelItem() {
  //   List<Widget> widgets = labels.map((label) {
  //     return new InkWell(
  //       onTap: () {
  //         if (label != '无') {
  //           customer.isSensitiveConsumer = 'Y';
  //         } else {
  //           customer.isSensitiveConsumer = 'N';
  //         }
  //         setState(() {
  //           customer.label = label;
  //         });
  //       },
  //       child: new Container(
  //         width: 50.0,
  //         height: 30.0,
  //         margin: EdgeInsets.only(right: 10.0),
  //         alignment: Alignment.center,
  //         decoration: new BoxDecoration(
  //             color: customer.label == label ? Theme.of(context).primaryColor : Colors.grey.shade300,
  //             borderRadius: BorderRadius.all(Radius.circular(2.0))),
  //         child: new Text(
  //           '$label',
  //           style: new TextStyle(color: customer.label == label ? Colors.white : Colors.grey, fontSize: 14.0),
  //         ),
  //       ),
  //     );
  //   }).toList();
  //   return widgets;
  // }

  save() async {
    if (customer.mobile != '') {
      if (customer.mobile.length == 4 && !isCourier) {
        customer.mobile = '*******' + customer.mobile;
      }
      bool matched = RegExpUtil.checkPhone(customer.mobile);
      if (matched) {
        /// 如果当前选择的标签被删除 清除label字段
        if (CheckUtils.isNotNull(customer.label)) {
          if (labels.indexWhere((e) => e == customer.label) == -1) {
            customer.label = null;
          }
        }
        Map<String, dynamic> params = customer.toJson();
        LoadingUtil(
          status: '正在保存...',
        ).show(context);
        DataResult res = await CourierDao.saveCustomer(params);
        LoadingUtil.dismiss(context);
        if (res.result) {
          Fluttertoast.showToast(msg: '客户信息保存成功');
          SoundUtils.audioPushFn(SoundUtils.SUCCESS_OK);
          Navigator.pop(context, true);
        }
      } else {
        Fluttertoast.showToast(msg: '手机号格式错误');
        SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_READ);
      }
    } else {
      SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_READ);
      Fluttertoast.showToast(msg: '手机号不可为空');
    }
  }

  showLabelAdd() {
    CommonUtils.inputCommonDialog(context, (value) {
      if (value != null && value != '' && value.length <= 7) {
        if (labels.indexWhere((item) => item == value) <= -1) {
          labels.add(value);
          customer.label = value;
          setState(() {});
        } else {
          Fluttertoast.showToast(msg: '已有相同标签');
          SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_READ);
        }
      } else if (value.length > 7) {
        Fluttertoast.showToast(msg: '标签长度不能超过7位');
        SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_READ);
      }
    }, '标签', hintText: '请输入不超过7位的标签');
  }

  deleteLabelItem(item) async {
    bool res = await CommonUtils.confirm(context, '删除该标签后，那么标记该标签的客户都将取消该标签',
        title: '是否删除该标签', cancelText: '我在想想', confirmText: '是的');
    if (res) {
      int index = labels.indexWhere((e) {
        return e == item;
      });
      if (index > serviceLabelLength - 1) {
        labels.removeAt(index);
        Fluttertoast.showToast(msg: '标签删除成功');
      } else {
        DataResult res = await CourierDao.customerLabelRemove(item);
        if (res.result) {
          await getUserLabels();
          Fluttertoast.showToast(msg: '标签删除成功');
        }
      }
    }
  }

  deleteCustomer(id) async {
    bool result = await CommonUtils.confirm(context, '是否删除客户信息', title: '删除提醒', cancelText: '我在想想', confirmText: '是的');
    if (result) {
      DataResult res = await CourierDao.customerRemove(id);
      if (res.result) {
        Navigator.pop(context);
        Fluttertoast.showToast(msg: '删除成功');
      }
    }
  }

  buildLabel(context) {
    List<Widget> widgets = labels.map((String item) {
      return Material(
          color: Colors.white,
          child: Stack(
            children: [
              Container(
                padding: EdgeInsets.fromLTRB(12, 4, 12, 4),
                margin: EdgeInsets.only(right: 12, bottom: 8),
                decoration: BoxDecoration(
                    color: customer.label == item ? DefaultConfig().configs.PRIMARY_COLOR_LIGHT : Colors.white,
                    borderRadius: BorderRadius.circular(7),
                    border: Border.all(
                        width: 1, color: customer.label == item ? Theme.of(context).primaryColor : Color(0xFFD2D2D2))),
                child: InkWell(
                  onTap: () {
                    setState(() {
                      if (customer.label == item) {
                        customer.label = '';
                      } else {
                        customer.label = item;
                      }
                    });
                  },
                  onLongPress: () {
                    print('long press');
                    setState(() {
                      showDeleteButton = !showDeleteButton;
                    });
                    controller?.forward();
                  },
                  child: Container(
                      child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(item,
                          style: TextStyle(
                              fontSize: 16,
                              color: customer.label == item ? Theme.of(context).primaryColor : Colors.black)),
                      Offstage(
                        offstage: !showDeleteButton,
                        child: InkWell(
                          onTap: () {
                            deleteLabelItem(item);
                          },
                          child: AnimatedBuilder(
                              animation: animation,
                              builder: (context, child) {
                                return Transform(
                                    transform: Matrix4.rotationZ(animation.value * math.pi / 180),
                                    child: Icon(Icons.close_outlined, size: 16, color: Color(0xFF999999)));
                              }),
                        ),
                      )
                    ],
                  )),
                ),
              ),
            ],
          ));
    }).toList();

    if (labels.length < 10) {
      widgets.add(Material(
        child: InkWell(
          onTap: () {
            showLabelAdd();
          },
          child: Container(
            constraints: BoxConstraints(maxWidth: 120),
            padding: EdgeInsets.fromLTRB(12, 4, 12, 4),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                border: Border.all(width: 1, color: Theme.of(context).primaryColor)),
            child: Row(
              children: [
                Icon(
                  Icons.add_circle_outline,
                  color: Theme.of(context).primaryColor,
                  size: 16,
                ),
                Text(
                  '添加标签',
                  style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 16),
                )
              ],
            ),
          ),
        ),
      ));
    }
    return widgets;
  }

  getButtonType() {
    if (mobileEditingController.text.length == 11) {
      bool matched = RegExpUtil.checkPhone(mobileEditingController.text);
      if (matched) {
        return ButtonType.primary;
      }
    }
    if (mobileEditingController.text.length == 4 && !isCourier) {
      return ButtonType.primary;
    }
    return ButtonType.primaryDisabled;
  }

  /// 标题部分
  buildTitle() {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: 85,
      color: Colors.white,
      padding: EdgeInsets.only(top: 40),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          InkWell(
            onTap: () {
              Navigator.pop(context);
            },
            child: Container(
              width: 60.0,
              padding: EdgeInsets.only(left: 12),
              alignment: Alignment.centerLeft,
              child: Icon(
                Icons.arrow_back,
                size: 25.0,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Text(
                  '客户详情',
                  style: TextStyle(fontSize: 18.0, fontWeight: FontWeight.w500, color: Colors.black),
                ),
              ],
            ),
          ),
          Container(
              margin: EdgeInsets.only(right: 15),
              child: InkWell(
                child: Text(
                  '删除',
                  style: TextStyle(fontSize: 16),
                ),
                onTap: () {
                  deleteCustomer(customer.id);
                },
              ))
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
      child: Scaffold(
        backgroundColor: DefaultConfig().configs.BG_COLOR,
        body: Container(
          child: Column(
            children: [
              Expanded(
                  child: new SingleChildScrollView(
                child: new Column(
                  children: <Widget>[
                    buildTitle(),
                    Container(
                      margin: EdgeInsets.only(top: 10),
                      padding: EdgeInsets.fromLTRB(10.0, 2.0, 10.0, 2.0),
                      decoration: new BoxDecoration(color: Colors.white),
                      child: new Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          new Container(
                            padding: EdgeInsets.only(top: 4, bottom: 5),
                            child: new Text(
                              '姓名',
                              style: new TextStyle(color: Color(0xFF000000), fontSize: 15.0),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.only(left: 8, right: 8),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(7),
                                border: Border.all(width: 1, color: Color(0xFFD2D2D2))),
                            child: Row(
                              children: [
                                new Expanded(
                                  child: new Container(
                                    child: new TextField(
                                      maxLengthEnforcement: MaxLengthEnforcement.none,
                                      controller: nameEditingController,
                                      style: TextStyle(
                                        fontSize: 16.0,
                                        color: Colors.black,
                                      ),
                                      maxLength: 10,
                                      maxLines: 1,
                                      inputFormatters: [LengthLimitingTextInputFormatter(10)],
                                      decoration: new InputDecoration(
                                          counterText: '',
                                          border: InputBorder.none,
                                          contentPadding: EdgeInsets.all(0.0),
                                          hintText: '请输入客户姓名, 不超过10个字',
                                          hintStyle: new TextStyle(fontSize: 16.0, color: Colors.grey.shade300)),
                                      onChanged: (value) {
                                        setState(() {
                                          customer.name = value;
                                        });
                                      },
                                      onTap: () {
                                        setState(() {
                                          focusName = 'name';
                                        });
                                      },
                                    ),
                                  ),
                                ),
                                new Offstage(
                                  offstage: focusName != 'name' || customer.name == '',
                                  child: new InkWell(
                                    onTap: () {
                                      nameEditingController.value = TextEditingValue(text: '');
                                      setState(() {
                                        customer.name = '';
                                      });
                                    },
                                    child: Padding(
                                      padding: EdgeInsets.fromLTRB(5.0, 10.0, 0.0, 10.0),
                                      child: new Icon(
                                        Icons.cancel,
                                        size: 20.0,
                                        color: Colors.grey.shade400,
                                      ),
                                    ),
                                  ),
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                    new Container(
                      padding: EdgeInsets.fromLTRB(10.0, 2.0, 10.0, 2.0),
                      decoration: new BoxDecoration(color: Colors.white),
                      child: new Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          new Container(
                            padding: EdgeInsets.only(top: 4, bottom: 5),
                            child: new Text(
                              '手机号',
                              style: new TextStyle(color: Color(0xFF000000), fontSize: 15.0),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.only(left: 8, right: 8),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(7),
                                border: Border.all(width: 1, color: Color(0xFFD2D2D2))),
                            child: Row(
                              children: [
                                new Expanded(
                                  child: new Container(
                                    child: new TextField(
                                      maxLengthEnforcement: MaxLengthEnforcement.none,
                                      controller: mobileEditingController,
                                      style: TextStyle(
                                        fontSize: 16.0,
                                        color: Colors.black87,
                                      ),
                                      maxLength: 11,
                                      maxLines: 1,
                                      keyboardType: TextInputType.phone,
                                      inputFormatters: [LengthLimitingTextInputFormatter(11)],
                                      decoration: new InputDecoration(
                                          counterText: '',
                                          border: InputBorder.none,
                                          contentPadding: EdgeInsets.all(0.0),
                                          hintText: '请输入客户手机号',
                                          hintStyle: new TextStyle(fontSize: 16.0, color: Colors.grey.shade300)),
                                      onChanged: (value) {
                                        if (value.length == 11) {
                                          bool matched = RegExpUtil.checkPhone(value);
                                          if (matched) {
                                            setState(() {
                                              customer.mobile = value;
                                            });
                                          } else {
                                            Fluttertoast.showToast(msg: '手机号格式错误');
                                            SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_READ);
                                          }
                                        }
                                        if (value.length == 4 && !isCourier) {
                                          setState(() {
                                            customer.mobile = value;
                                          });
                                        }
                                      },
                                      onTap: () {
                                        setState(() {
                                          focusName = 'mobile';
                                        });
                                      },
                                    ),
                                  ),
                                ),
                                new Offstage(
                                  offstage: focusName != 'mobile' || customer.mobile == '',
                                  child: new InkWell(
                                    onTap: () {
                                      mobileEditingController.value = TextEditingValue(text: '');
                                      setState(() {
                                        customer.mobile = '';
                                      });
                                    },
                                    child: Padding(
                                      padding: EdgeInsets.fromLTRB(5.0, 10.0, 0.0, 10.0),
                                      child: new Icon(
                                        Icons.cancel,
                                        size: 20.0,
                                        color: Colors.grey.shade400,
                                      ),
                                    ),
                                  ),
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                    new Offstage(
                      offstage: isCourier,
                      child: new Container(
                        height: 50,
                        padding: EdgeInsets.fromLTRB(10.0, 2.0, 10.0, 2.0),
                        decoration: new BoxDecoration(color: Colors.white),
                        child: new Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            new Container(
                              alignment: Alignment.centerLeft,
                              child: new Text('是否黑名单', style: new TextStyle(color: Color(0xFF000000), fontSize: 15.0)),
                            ),
                            new Row(
                              children: <Widget>[
                                Transform.scale(
                                  scale: 0.8,
                                  child: CupertinoSwitch(
                                    value: customer.isBlacklist == 1,
                                    trackColor: Colors.grey[200],
                                    activeColor: Theme.of(context).primaryColor,
                                    onChanged: (bool value) {
                                      setState(() {
                                        customer.isBlacklist = value ? 1 : 0;
                                      });
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    new Container(
                      padding: EdgeInsets.fromLTRB(10.0, 2.0, 10.0, 2.0),
                      width: double.infinity,
                      decoration: new BoxDecoration(color: Colors.white),
                      child: new Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          new Container(
                            padding: EdgeInsets.only(top: 4, bottom: 5),
                            child: new Text(
                              '标签',
                              style: new TextStyle(color: Color(0xFF000000), fontSize: 15.0),
                            ),
                          ),
                          Container(
                            color: Colors.white,
                            padding: EdgeInsets.only(left: 0, right: 8),
                            child: Wrap(
                              children: buildLabel(context),
                            ),
                          )
                        ],
                      ),
                    ),
                    new Container(
                      height: 50,
                      padding: EdgeInsets.fromLTRB(10.0, 2.0, 10.0, 2.0),
                      decoration: new BoxDecoration(color: Colors.white),
                      child: new Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: <Widget>[
                          new Container(
                            alignment: Alignment.centerLeft,
                            child:
                                new Text('入柜该手机号提醒标签', style: new TextStyle(color: Color(0xFF000000), fontSize: 15.0)),
                          ),
                          new Row(
                            children: <Widget>[
                              Transform.scale(
                                scale: 0.8,
                                child: CupertinoSwitch(
                                  value: customer.labelNotice == 1,
                                  trackColor: Colors.grey[200],
                                  activeColor: Theme.of(context).primaryColor,
                                  onChanged: (bool value) {
                                    setState(() {
                                      customer.labelNotice = value ? 1 : 0;
                                    });
                                  },
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    // new Container(
                    //   padding: EdgeInsets.fromLTRB(10.0, 2.0, 10.0, 2.0),
                    //   decoration: new BoxDecoration(color: Colors.white),
                    //   child: new Column(
                    //     crossAxisAlignment: CrossAxisAlignment.start,
                    //     children: <Widget>[
                    //       new Container(
                    //         alignment: Alignment.centerLeft,
                    //         padding: EdgeInsets.only(bottom: 5),
                    //         child: new Text('备注', style: new TextStyle(color: Color(0xFF000000), fontSize: 15.0)),
                    //       ),
                    //       Container(
                    //         padding: EdgeInsets.only(left: 8, right: 8),
                    //         decoration: BoxDecoration(
                    //             borderRadius: BorderRadius.circular(7),
                    //             border: Border.all(width: 1, color: Color(0xFFD2D2D2))),
                    //         child: Row(
                    //           children: [
                    //             new Expanded(
                    //               child: new Container(
                    //                 child: new TextField(
                    //                   controller: remarksEditingController,
                    //                   style: TextStyle(
                    //                     fontSize: 16.0,
                    //                     color: Colors.black87,
                    //                   ),
                    //                   maxLength: 100,
                    //                   minLines: 5,
                    //                   maxLines: 5,
                    //                   maxLengthEnforced: false,
                    //                   inputFormatters: [LengthLimitingTextInputFormatter(100)],
                    //                   decoration: new InputDecoration(
                    //                       counterText: '',
                    //                       border: InputBorder.none,
                    //                       contentPadding: EdgeInsets.all(0.0),
                    //                       hintText: '请输入内容',
                    //                       hintStyle: new TextStyle(fontSize: 16.0, color: Colors.grey.shade300)),
                    //                   onChanged: (value) {
                    //                     setState(() {
                    //                       customer.remarks = value;
                    //                     });
                    //                   },
                    //                   onTap: () {
                    //                     setState(() {
                    //                       focusName = 'remarks';
                    //                     });
                    //                   },
                    //                 ),
                    //               ),
                    //             ),
                    //             new Offstage(
                    //               offstage:
                    //                   focusName != 'remarks' || customer.remarks == '' || customer.remarks == null,
                    //               child: new InkWell(
                    //                 onTap: () {
                    //                   remarksEditingController.value = TextEditingValue(text: '');
                    //                   setState(() {
                    //                     customer.remarks = '';
                    //                   });
                    //                 },
                    //                 child: Padding(
                    //                   padding: EdgeInsets.fromLTRB(5.0, 10.0, 0.0, 10.0),
                    //                   child: new Icon(
                    //                     Icons.cancel,
                    //                     size: 20.0,
                    //                     color: Colors.grey.shade400,
                    //                   ),
                    //                 ),
                    //               ),
                    //             )
                    //           ],
                    //         ),
                    //       )
                    //     ],
                    //   ),
                    // ),
                    // new Container(
                    //   height: 50.0,
                    //   padding: EdgeInsets.fromLTRB(10.0, 2.0, 10.0, 2.0),
                    //   decoration: new BoxDecoration(
                    //       color: Colors.white,
                    //       border: Border(bottom: BorderSide(width: 1.0, color: Colors.grey.shade100))),
                    //   child: new Row(
                    //     children: <Widget>[
                    //       new Container(
                    //         width: 70.0,
                    //         alignment: Alignment.centerLeft,
                    //         child: new Text('标签', style: new TextStyle(color: Color(0xFF656565), fontSize: 15.0)),
                    //       ),
                    //       new Padding(padding: EdgeInsets.only(left: 10.0)),
                    //       new Expanded(
                    //         child: new Container(
                    //           child: new Row(
                    //             children: buildLabelItem(),
                    //           ),
                    //         ),
                    //       ),
                    //     ],
                    //   ),
                    // ),
                  ],
                ),
              )),
              BottomBtnWidget(
                title: '确定',
                type: getButtonType(),
                showShadow: false,
                action: () {
                  if (getButtonType() == ButtonType.primary) {
                    save();
                  }
                },
              )
            ],
          ),
        ),
      ),
    );
  }
}
