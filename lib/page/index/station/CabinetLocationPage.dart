import 'dart:async';
import 'dart:io';

import 'package:amap_flutter_base/amap_flutter_base.dart';
import 'package:amap_flutter_location/amap_flutter_location.dart';
import 'package:amap_flutter_location/amap_location_option.dart';
import 'package:amap_flutter_map/amap_flutter_map.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CabinetDao.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_location_entity.dart';
import 'package:cabinet_flutter_app/common/page_life_cycle.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CabinetBoxUtil.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/MapUtils.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/CabinetBoxListWidget.dart';
import 'package:cabinet_flutter_app/widget/CollectIconButton.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CommonRowWidget.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

class CabinetLocationPage extends StatefulWidget {
  final String cabinetCode;

  CabinetLocationPage(this.cabinetCode, {Key? key}) : super(key: key);

  @override
  _CabinetLocationPageState createState() => _CabinetLocationPageState();
}

class _CabinetLocationPageState extends State<CabinetLocationPage> with PageLifeCycle<CabinetLocationPage> {
  List<Widget> _approvalNumberWidget = [];
  late AMapController _mapController;
  late CabinetLocationEntity cabinet = new CabinetLocationEntity();
  List<dynamic> emptyList = [];
  CabinetEntity cabinetEntity = new CabinetEntity();
  static const AMapPrivacyStatement amapPrivacyStatement =
      AMapPrivacyStatement(hasContains: true, hasShow: true, hasAgree: true);

  static AMapApiKey amapApiKeys =
      AMapApiKey(iosKey: DefaultConfig().configs.GAO_MAP_KEY, androidKey: DefaultConfig().configs.GAO_MAP_KEY);

  LatLng swLatLng = LatLng(30.52, 110.40);
  LatLng neLatLng = LatLng(39.909187, 116.397451);
  final Map<String, Marker> _initMarkerMap = <String, Marker>{};
  Map<String, dynamic>? _locationResult;
  LatLng posCenter = LatLng(0, 0);
  StreamSubscription<Map<String, Object>>? _locationListener;

  AMapFlutterLocation _locationPlugin = new AMapFlutterLocation();

  @override
  void initState() {
    super.initState();
    this.init();
  }

  init() async {
    // getLocation();
    await getCabinetLocationInfo();
    await getCabinetStatus();
  }

  getLocation() {
    /// [hasShow] 隐私权政策是否弹窗展示告知用户
    AMapFlutterLocation.updatePrivacyShow(true, true);

    /// 设置是否已经取得用户同意，如果未取得用户同意，高德定位SDK将不会工作
    ///
    /// 高德SDK合规使用方案请参考官网地址：https://lbs.amap.com/news/sdkhgsy
    ///
    /// <b>必须保证在调用定位功能之前调用, 建议首次启动App时弹出《隐私政策》并取得用户同意</b>
    ///
    /// [hasAgree] 隐私权政策是否已经取得用户同意
    AMapFlutterLocation.updatePrivacyAgree(true);

    /// 动态申请定位权限
    requestPermission();

    ///设置Android和iOS的apiKey<br>
    ///key的申请请参考高德开放平台官网说明<br>
    ///Android: https://lbs.amap.com/api/android-location-sdk/guide/create-project/get-key
    ///iOS: https://lbs.amap.com/api/ios-location-sdk/guide/create-project/get-key
    AMapFlutterLocation.setApiKey(DefaultConfig().configs.GAO_MAP_KEY, DefaultConfig().configs.GAO_MAP_KEY);

    ///iOS 获取native精度类型
    if (Platform.isIOS) {
      requestAccuracyAuthorization();
    }

    ///注册定位结果监听
    _locationListener = _locationPlugin.onLocationChanged().listen((Map<String, dynamic> result) async {
      _locationResult = result;
      if (_locationResult?['longitude'] != null && _locationResult?['latitude'] != null) {
        double lat = result['latitude'] is double ? result['latitude'] : double.parse(result['latitude']);
        double lng = result['longitude'] is double ? result['longitude'] : double.parse(result['longitude']);
        posCenter = LatLng(lat, lng);
        if (Platform.isIOS && (cabinetEntity.latitude ?? 0) > 0) {
          await getMapRanger();
        }
      }
      setState(() {});
    });
    _startLocation();
  }

  ///获取iOS native的accuracyAuthorization类型
  void requestAccuracyAuthorization() async {
    AMapAccuracyAuthorization currentAccuracyAuthorization = await _locationPlugin.getSystemAccuracyAuthorization();
    if (currentAccuracyAuthorization == AMapAccuracyAuthorization.AMapAccuracyAuthorizationFullAccuracy) {
      print("精确定位类型");
    } else if (currentAccuracyAuthorization == AMapAccuracyAuthorization.AMapAccuracyAuthorizationReducedAccuracy) {
      print("模糊定位类型");
    } else {
      print("未知定位类型");
    }
  }

  /// 动态申请定位权限
  void requestPermission() async {
    // 申请权限
    bool hasLocationPermission = await requestLocationPermission();
    if (hasLocationPermission) {
      print("定位权限申请通过");
    } else {
      print("定位权限申请不通过");
    }
  }

  /// 申请定位权限
  /// 授予定位权限返回true， 否则返回false
  Future<bool> requestLocationPermission() async {
    //获取当前的权限
    var status = await Permission.location.status;
    Permission.location.isDenied;
    bool isDenied = await Permission.locationWhenInUse.isDenied;
    if (status == PermissionStatus.granted) {
      //已经授权
      return true;
    } else {
      //未授权则发起一次申请
      status = await Permission.location.request();
      if (status == PermissionStatus.granted) {
        return true;
      } else {
        return false;
      }
    }
  }

  ///开始定位
  void _startLocation() {
    ///开始定位之前设置定位参数
    _setLocationOption();
    _locationPlugin.startLocation();
  }

  ///停止定位
  void _stopLocation() {
    _locationPlugin.stopLocation();
  }

  ///设置定位参数
  void _setLocationOption() {
    AMapLocationOption locationOption = new AMapLocationOption();

    ///是否单次定位
    locationOption.onceLocation = true;

    ///是否需要返回逆地理信息
    locationOption.needAddress = true;

    ///逆地理信息的语言类型
    locationOption.geoLanguage = GeoLanguage.DEFAULT;

    locationOption.desiredLocationAccuracyAuthorizationMode = AMapLocationAccuracyAuthorizationMode.ReduceAccuracy;

    locationOption.fullAccuracyPurposeKey = "AMapLocationScene";

    ///设置Android端连续定位的定位间隔
    locationOption.locationInterval = 2000;

    ///设置Android端的定位模式<br>
    ///可选值：<br>
    ///<li>[AMapLocationMode.Battery_Saving]</li>
    ///<li>[AMapLocationMode.Device_Sensors]</li>
    ///<li>[AMapLocationMode.Hight_Accuracy]</li>
    locationOption.locationMode = AMapLocationMode.Hight_Accuracy;

    ///设置iOS端的定位最小更新距离<br>
    locationOption.distanceFilter = -1;

    ///设置iOS端期望的定位精度
    /// 可选值：<br>
    /// <li>[DesiredAccuracy.Best] 最高精度</li>
    /// <li>[DesiredAccuracy.BestForNavigation] 适用于导航场景的高精度 </li>
    /// <li>[DesiredAccuracy.NearestTenMeters] 10米 </li>
    /// <li>[DesiredAccuracy.Kilometer] 1000米</li>
    /// <li>[DesiredAccuracy.ThreeKilometers] 3000米</li>
    locationOption.desiredAccuracy = DesiredAccuracy.Best;

    ///设置iOS端是否允许系统暂停定位
    locationOption.pausesLocationUpdatesAutomatically = false;

    ///将定位参数设置给定位插件
    _locationPlugin.setLocationOption(locationOption);
  }

  /// 获取快递员与点位关系
  getCabinetStatus() async {
    var res = await CourierDao.getCourierCabinetStatus(widget.cabinetCode);
    if (res != null && res.result) {
      cabinetEntity.hasCollected = res.data['hasCollected'] ? 1 : 0;
      cabinetEntity.hasUsed = res.data['hasUsed'] ? 1 : 0;
      setState(() {});
    }
  }

  getCabinetLocationInfo() async {
    var res = await CabinetDao.getCabinetInfo(widget.cabinetCode);
    if (res != null && res.result) {
      cabinetEntity = CabinetEntity.fromJson(res.data);
      // _addMarker();
      await getCabinetUsableBox();
      // await getMapRanger();
    }
    setState(() {});
  }

  getMapRanger() async {
    double maxLat = 0;
    double minLat = 0;
    double maxLng = 0;
    double minLng = 0;
    if (cabinetEntity.latitude != null) {
      maxLat = cabinetEntity.latitude!;
      minLat = cabinetEntity.latitude!;
      minLng = cabinetEntity.longitude!;
      maxLng = cabinetEntity.longitude!;
      if (posCenter.longitude > 0 && posCenter.latitude > 0) {
        if (posCenter.latitude - cabinetEntity.latitude! > 0) {
          maxLat = posCenter.latitude;
          minLat = cabinetEntity.latitude!;
        } else {
          maxLat = cabinetEntity.latitude!;
          minLat = posCenter.latitude;
        }
      }
    }
    if (cabinetEntity.longitude != null) {
      if (posCenter.longitude > 0 && posCenter.latitude > 0) {
        if (posCenter.longitude - cabinetEntity.longitude! > 0) {
          maxLng = posCenter.longitude;
          minLng = cabinetEntity.longitude!;
        } else {
          maxLng = cabinetEntity.longitude!;
          minLng = posCenter.longitude;
        }
      }
    }
    swLatLng = LatLng(minLat - 0.02, minLng - 0.01);
    neLatLng = LatLng(maxLat + 0.01, maxLng + 0.01);
    setState(() {});
  }

  /// 获取点位可用盒子汇总
  getCabinetUsableBox() async {
    DataResult res = await CabinetDao.getCabinetUsableBox(widget.cabinetCode);
    if (res.result) {
      Map<String, dynamic> countMap = {
        'microCount': res.data['microCount'],
        'miniCount': res.data['miniCount'],
        'smallCount': res.data['smallCount'],
        'mediumCount': res.data['mediumCount'],
        'largeCount': res.data['largeCount'],
        'hugeCount': res.data['hugeCount'],
        'superCount': res.data['superCount'],
      };
      emptyList = CabinetBoxUtil.getCabinetBoxAvailableCount(cabinetEntity.dispatchJson, countMap);
    }
    setState(() {});
  }

  @override
  void dispose() {
    ///移除定位监听
    if (null != _locationListener) {
      _locationListener?.cancel();
    }

    ///销毁定位
    _locationPlugin.destroy();
    super.dispose();
  }

  void onMapCreated(AMapController controller) {
    setState(() {
      _mapController = controller;
      getApprovalNumber();
    });
  }

  void getApprovalNumber() async {
    //普通地图审图号
    String? mapContentApprovalNumber = await _mapController.getMapContentApprovalNumber();
    //卫星地图审图号
    String? satelliteImageApprovalNumber = await _mapController.getSatelliteImageApprovalNumber();
    setState(() {
      if (null != mapContentApprovalNumber) {
        _approvalNumberWidget.add(Text(mapContentApprovalNumber));
      }
      if (null != satelliteImageApprovalNumber) {
        _approvalNumberWidget.add(Text(satelliteImageApprovalNumber));
      }
    });
    print('地图审图号（普通地图）: $mapContentApprovalNumber');
    print('地图审图号（卫星地图): $satelliteImageApprovalNumber');
  }

  chooseMap() {
    Map<String, dynamic> mapMap = {'QQ': '腾讯地图', 'AMap': '高德地图', 'Baidu': '百度地图'};
    if (Platform.isIOS) {
      mapMap.addAll({'Apple': 'Apple地图'});
    }
    CommonUtils.showBottomModal(context, mapMap, (map) async {
      if (map != null) {
        if (map == 'QQ') {
          MapUtils.gotoTencentMap(cabinetEntity.longitude, cabinetEntity.latitude);
        }
        if (map == 'Baidu') {
          MapUtils.gotoBaiduMap(cabinetEntity.longitude, cabinetEntity.latitude);
        }
        if (map == 'AMap') {
          MapUtils.gotoAMap(cabinetEntity.longitude, cabinetEntity.latitude);
        }
        if (map == 'Apple') {
          MapUtils.gotoAppleMap(cabinetEntity.longitude, cabinetEntity.latitude);
        }
      }
    });
  }

  ///添加一个marker
  void _addMarker() {
    final _markerPosition = LatLng(cabinetEntity.latitude!, cabinetEntity.longitude!);
    final Marker marker = Marker(
      position: _markerPosition,
      infoWindow: InfoWindow(title: cabinetEntity.name),
      infoWindowEnable: true,
      //使用默认hue的方式设置Marker的图标
      // icon: BitmapDescriptor.fromIconPath('static/images/logo-avatar-circle.png'),
      icon: BitmapDescriptor.fromIconPath(LocalImageUtil.getImagePath('logoAvatarCircleSmall', isChannel: true)),
    );
    //调用setState触发AMapWidget的更新，从而完成marker的添加
    setState(() {
      //将新的marker添加到map里
      _initMarkerMap[marker.id] = marker;
    });
  }

  /// 收藏点位
  setCabinetCollect() async {
    var res = await CourierDao.setCabinetCollected(widget.cabinetCode, !(cabinetEntity.hasCollected == 1));
    if (res != null && res.result) {
      int isCollect = res.data['hasCollect'];
      cabinetEntity.hasCollected = isCollect;
      Fluttertoast.showToast(msg: isCollect == 1 ? '收藏成功' : '取消收藏成功', gravity: ToastGravity.CENTER);
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    // final AMapWidget map = AMapWidget(
    //   initialCameraPosition: CameraPosition(target: posCenter, zoom: 14),
    //   apiKey: amapApiKeys,
    //   myLocationStyleOptions: MyLocationStyleOptions(true,
    //       circleFillColor: Colors.transparent,
    //       circleStrokeColor: Colors.transparent,
    //       icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure)),
    //   privacyStatement: amapPrivacyStatement,
    //   onMapCreated: onMapCreated,
    //   limitBounds: LatLngBounds(southwest: swLatLng, northeast: neLatLng),
    //   markers: Set<Marker>.of(_initMarkerMap.values),
    // );

    return WillPopScope(
      onWillPop: () async {
        Navigator.of(context).pop(true);
        return true;
      },
      child: Scaffold(
        appBar: new AppCustomerBar(
          title: AppbarTitle(
            title: '查看地址',
            isCenter: true,
          ),
          actions: [
            Container(
              width: 60,
            )
          ],
        ),
        body: ConstrainedBox(
          constraints: BoxConstraints.expand(),
          child: Stack(
            alignment: Alignment.center,
            children: [
              SlidingUpPanel(
                  defaultPanelState: PanelState.OPEN,
                  borderRadius: BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16)),
                  minHeight: 40,
                  maxHeight: 300,
                  collapsed: Center(
                    child: Container(
                        height: 40,
                        child: Column(
                          children: [
                            Container(
                              margin: EdgeInsets.only(top: 12),
                              height: 3,
                              color: Colors.transparent,
                              child: Row(
                                children: [
                                  Container(
                                    width: MediaQuery.of(context).size.width / 2 - 24,
                                  ),
                                  Container(
                                    width: 48,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.all(Radius.circular(4.0)),
                                      color: Theme.of(context).primaryColor,
                                    ),
                                  ),
                                  Container(
                                    width: MediaQuery.of(context).size.width / 2 - 24,
                                  )
                                ],
                              ),
                            ),
                            Container(
                              // padding: EdgeInsets.fromLTRB(0, 10, 0, 15),
                              color: Colors.transparent,
                              alignment: Alignment.center,
                            ),
                          ],
                        )),
                  ),
                  panel: Container(
                      padding: EdgeInsets.only(left: 16, top: 16, right: 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '点位信息',
                            style: TextStyle(
                                fontSize: AppConstant.middleTextWhiteSize,
                                color: Colors.black,
                                fontWeight: FontWeight.w500),
                          ),
                          Padding(padding: EdgeInsets.only(top: 5)),
                          Text(
                            '识别码: ' + '${cabinetEntity.code}',
                            style: TextStyle(
                                fontSize: AppConstant.smallTextSize, color: DefaultConfig().configs.GREY_COLOR),
                          ),
                          Text(
                            '名称: ' + '${cabinetEntity.name}',
                            style: TextStyle(
                                fontSize: AppConstant.smallTextSize, color: DefaultConfig().configs.GREY_COLOR),
                          ),
                          Text(
                            '地址: ' + '${cabinetEntity.address}',
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                fontSize: AppConstant.smallTextSize, color: DefaultConfig().configs.GREY_COLOR),
                          ),
                          Padding(padding: EdgeInsets.only(top: 5)),
                          Text(
                            '格口信息',
                            style: TextStyle(
                                fontSize: AppConstant.middleTextWhiteSize,
                                color: Colors.black,
                                fontWeight: FontWeight.w500),
                          ),
                          Padding(padding: EdgeInsets.only(top: 5)),
                          Container(
                            margin: EdgeInsets.only(left: 0, right: 0, bottom: 20),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.all(Radius.circular(5)),
                              color: Color(0xFFF5F5F5),
                            ),
                            // child: Row(
                            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            //   children: buildAvailableGK(),
                            // ),
                            child: CabinetBoxListWidget(emptyList),
                          ),
                          Container(
                            child: CommonRowWidget(
                              leftWidget: CollectIconButtonWidget(
                                  value: cabinetEntity.hasCollected == 1,
                                  onPress: () {
                                    setCabinetCollect();
                                  }),
                              rightWidget: Row(
                                children: [
                                  Row(
                                    children: [
                                      Icon(AppICons.LOCATION, size: 18, color: Color(0xFF999999)),
                                      Padding(padding: EdgeInsets.only(right: 3)),
                                      Text(CabinetBoxUtil.getCabinetDistanceByLocation(posCenter.latitude,
                                          posCenter.longitude, cabinetEntity.latitude, cabinetEntity.longitude))
                                    ],
                                  ),
                                  Padding(padding: EdgeInsets.only(right: 5)),
                                  MaterialButton(
                                    minWidth: MediaQuery.of(context).size.width * .25,
                                    color: Theme.of(context).primaryColor,
                                    textColor: Colors.white,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(28.0),
                                    ),
                                    onPressed: () {
                                      chooseMap();
                                    },
                                    child: Text(
                                      '导航',
                                      style: TextStyle(fontSize: AppConstant.smallTextSize),
                                    ),
                                  )
                                ],
                              ),
                            ),
                          )
                        ],
                      )),
                  body: Container(
                    height: MediaQuery.of(context).size.height,
                    width: MediaQuery.of(context).size.width,
                    // child: map,
                  )),
            ],
          ),
        ), // This trailing comma makes auto-formatting nicer for build methods.
      ),
    );
  }
}
