import 'dart:async';
import 'dart:ui';

import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CabinetDao.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/package_view_sj_entity.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppListState.dart';
import 'package:cabinet_flutter_app/widget/AppPullLoadWidget.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/BottomBtnWidget.dart';
import 'package:cabinet_flutter_app/widget/CountDownWidget.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/PackageStayWidget.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

import '../../HomePage.dart';

class CabinetTakeOutPage extends StatefulWidget {
  final CabinetEntity cabinetInfo; // 点位信息
  final PackageType type; // 列表类型
  final String hostIndex;

  CabinetTakeOutPage(this.cabinetInfo, this.type, this.hostIndex, {Key? key}) : super(key: key);

  @override
  _CabinetTakeOutPageState createState() => _CabinetTakeOutPageState();
}

class _CabinetTakeOutPageState extends State<CabinetTakeOutPage>
    with AutomaticKeepAliveClientMixin<CabinetTakeOutPage>, AppListState<CabinetTakeOutPage> {
  final GlobalKey<ScaffoldState> _cabinetWaitListKey = GlobalKey<ScaffoldState>();
  int total = 0;
  GlobalKey<CountDownWidgetState> countDownKey = GlobalKey();
  bool hasFeedBack = false;
  Timer? boxStatusTimer;
  bool isCheckBox = true;
  bool isCancel = true;
  bool isSure = true;
  double minHeight = 200.0;
  double maxHeight = 200.0;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    boxStatusTimer?.cancel();
    countDownKey.currentState?.dispose();
  }

  Future<Null> onFresh() async {
    _getData(isRefresh: true);
  }

  /// 取消订单
  pickOut(PackageViewSjEntity item) {
    resetCountDown();
    CommonUtils.customConfirmByReason(context, '是否确认取消订单？', (backReason) async {
      print(backReason);
      LoadingUtil(
        status: '订单正在取消...',
      ).show(context);
      var res = await CourierDao.courierOutboundWaybillSj(item.cabinetLocationCode!, item.id!, backReason, true);
      LoadingUtil.dismiss(context);
      if (res.result) {
        Navigator.of(context).pop();
        Navigator.of(context).pop();
        handleRefresh();
        Fluttertoast.showToast(msg: ' 订单取消成功');
      }
    }, changeText: 'changeText', title: '取消订单', showClose: false, showInput: true);
  }

  /// 确认取出
  _takeOut(PackageViewSjEntity item) {
    resetCountDown();
    CommonUtils.customConfirm(context, '包裹取出后请在已收件列表中进行单号回填！',
        title: '是否确认取走寄件包裹？',
        showClose: false,
        actions: <Widget>[
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                },
                child: Text('取消', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
                style: ButtonStyle(foregroundColor: MaterialStateProperty.resolveWith((states) {
                  return Colors.black;
                }), backgroundColor: MaterialStateProperty.resolveWith((states) {
                  return DefaultConfig().configs.WHITE_COLOR;
                }))),
            TextButton(
                onPressed: () async {
                  LoadingUtil(
                    status: '包裹正在取出...',
                  ).show(context);
                  var res = await CourierDao.courierTakeOrderSend(item.cabinetLocationCode!, item.id!);
                  LoadingUtil.dismiss(context);
                  if (res.result) {
                    resetCountDown();
                    handleRefresh();
                    Fluttertoast.showToast(msg: ' 包裹取出成功');
                    Navigator.of(context).pop();
                    Navigator.of(context).pop();
                  }
                },
                child: Text('是的', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
                style: ButtonStyle(foregroundColor: MaterialStateProperty.resolveWith((states) {
                  return Colors.black;
                }), backgroundColor: MaterialStateProperty.resolveWith((states) {
                  return Colors.white;
                }))),
          ])
        ]);
  }

  /// 格口打开
  showBoxOpen(BuildContext context, PackageViewSjEntity item, onPressed(item)) {
    return showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
        topLeft: Radius.circular(10.0),
        topRight: Radius.circular(10.0),
      )),
      builder: (BuildContext _context) {
        return StatefulBuilder(
          builder: (_context, state) {
            return Stack(
              children: [
                Container(
                  height: 30.0,
                  width: double.infinity,
                  color: Colors.black54,
                ),
                Container(
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      )),
                ),
                Container(
                  height: 550,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            LocalImageUtil.getImageAsset('boxOpen', width: MediaQuery.of(context).size.width * 0.28),
                            Padding(padding: EdgeInsets.only(bottom: 10)),
                            RichText(
                                text: TextSpan(children: <TextSpan>[
                                  TextSpan(
                                    text: '${item.cabinetName ?? ''}-',
                                    style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 16),
                                  ),
                                  TextSpan(
                                    text: '${item.cabinetBoxLabel}号',
                                    style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 16),
                                  ),
                                  TextSpan(
                                    text: ' 格口已开 ',
                                    style: TextStyle(color: Colors.black, fontSize: 14),
                                  ),
                                ])),
                            Padding(padding: EdgeInsets.only(bottom: 10)),
                            Offstage(
                              offstage: hasFeedBack,
                              child: Text('放入包裹后请关门', style: TextStyle(color: Color(0xFF999999), fontSize: 14)),
                            )
                          ],
                        ),
                      ),
                      Offstage(
                          offstage: hasFeedBack,
                          child: BottomBtnWidget(
                              showShadow: false,
                              title: '我已关门',
                              action: () {
                                closeCabinetBox(item.cabinetBoxId!);
                              }))
                    ],
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  closeCabinetBox(String cabinetBoxId) async {
    resetCountDown();
    LoadingUtil(
      status: '关门请求中...',
    ).show(context);
    var res = await CabinetDao.cabinetBoxCloseDoor(cabinetBoxId);
    LoadingUtil.dismiss(context);
    if (res.result) {
      isCheckBox = false;
      boxStatusTimer?.cancel();
      Navigator.pop(context);
      resetCountDown();
    }
  }

  // 上拉加载更多
  _getData({isRefresh = false}) async {
    String code = widget.cabinetInfo.code!;
    // 待收件
    DataResult result = await CourierDao.courierSjCabinetOrderPagList(page.toString(), '20', cabinetLocationCode: code);
    List<dynamic> list = [];
    if (result.data.length > 0) {
      result.data.forEach((item) {
        list.add(item);
      });
    } else {
      list = result.data;
    }
    total = result.total!;
    return new DataResult(list, true, total: total);
  }

  _openDoor(PackageViewSjEntity item) async {
    LoadingUtil(
      status: '正在开门...',
    ).show(context);
    DataResult res = await CourierDao.courierBoxOpenCheck(item.cabinetBoxId, hostIndex: widget.hostIndex);
    LoadingUtil.dismiss(context);
    if (res.result) {
      showCard(item);
    }
  }

  buildBottom(PackageViewSjEntity item) {
    return Container(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Offstage(
            offstage: false,
            child: ElevatedButton(
              child: Text("开门检查",
                  style: TextStyle(fontSize: AppConstant.smallTextSize, color: DefaultConfig().configs.WHITE_COLOR)),
              onPressed: () {
                _openDoor(item);
              },
              style: ButtonStyle(
                  minimumSize: MaterialStateProperty.all(Size(80, 32)),
                  shape: MaterialStateProperty.all(StadiumBorder()),
                  side: MaterialStateProperty.all(BorderSide(color: Theme.of(context).primaryColor)),
                  backgroundColor: MaterialStateProperty.all(Theme.of(context).primaryColor)),
            ),
          ),
        ],
      ),
      margin: EdgeInsets.only(top: 7),
    );
  }

  _renderEventItem(int index) {
    PackageViewSjEntity item = dataList[index];
    return PackageStayWidget(widget.type, indexNo: dataList.length - index, package: item, bottom: buildBottom(item));
  }

  getTitle() {
    return '寄件待取出';
  }

  /// 标题部分
  buildTitle(String title) {
    return Row(
      children: <Widget>[
        //因为它是左右占比结构,所以使用Expanded 的flex
        Expanded(
          flex: 1,
          child: Container(
            //所有组件垂直居中
            alignment: Alignment.center,
            height: 40,
            child: Stack(
              alignment: Alignment.center,
              children: <Widget>[
                Row(
                  //常用于Row和Column控件
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Text(
                      '$title',
                      style: TextStyle(color: Colors.white, fontSize: 18),
                    )
                  ],
                ),
              ],
            ),
          ),
        ),
        new CountDownWidget(
          countDownKey,
          isBlack: true,
          onTimerFinish: () {
            NavigatorUtils.goHome(context);
          },
        ),
      ],
    );
  }

  resetCountDown() {
    countDownKey.currentState?.resetCountDown();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    String title = getTitle();
    var appHeight = MediaQueryData.fromWindow(window).padding.top;
    return WillPopScope(
      onWillPop: () async {
        Navigator.pop(context);
        return true;
      },
      child: Scaffold(
        backgroundColor: DefaultConfig().configs.BG_COLOR,
        key: _cabinetWaitListKey,
        appBar: new AppCustomerBar(
          title: AppbarTitle(
            title: title,
            isCenter: true,
          ),
          actions: [
            CountDownWidget(
              countDownKey,
              isBlack: true,
              onTimerFinish: () {
                // resetCountDown();
                  NavigatorUtils.goHome(context);
              },
            ),
          ],
        ),
        // appBar: AppBar(backgroundColor: Theme.of(context).primaryColor, elevation: 1, title: buildTitle(title)),
        body: new Stack(
          children: <Widget>[
            new Column(
              children: <Widget>[
                /// 通知
                Container(
                    padding: EdgeInsets.only(left: 12),
                    decoration: new BoxDecoration(
                      color: Color(0xFFFFEDE7),
                      borderRadius: BorderRadius.all(
                        Radius.circular(10.0),
                      ),
                      border: Border.all(width: 1.0, color: Color(0xFFFFEDE7)),
                    ),
                    child: Row(
                      children: [
                        LocalImageUtil.getImageAsset('tz', width: 16),
                        Container(
                          margin: EdgeInsets.only(left: 10, top: 5, bottom: 5),
                          color: Color(0xFFFFEDE7),
                          child: Text(
                            "客户所付金额为首重费用，如超重请联系客户线下\n补差价",
                            style: TextStyle(color: Color(0XFFFF5E24), fontSize: 14),
                          ),
                        )
                      ],
                    )),
                Container(
                  padding: EdgeInsets.fromLTRB(12, 5, 10, 2),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        widget.cabinetInfo.name ?? '',
                        style: TextStyle(color: DefaultConfig().configs.GREY_COLOR),
                      ),
                      Text('$total'),
                    ],
                  ),
                ),
                new Expanded(
                  child: new RefreshIndicator(
                    onRefresh: onFresh,
                    child: AppPullLoadWidget(
                      pullLoadWidgetControl,
                      (BuildContext context, int index) => _renderEventItem(index),
                      handleRefresh,
                      onLoadMore,
                      refreshKey: refreshIndicatorKey,
                    ),
                  ),
                )
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 卡片弹出
  showCard(PackageViewSjEntity item) {
    CommonUtils.showBottomCardModal(context, item, () async {
      pickOut(item);
    }, () async {
      _takeOut(item);
    }, showText: 'show');
  }

  // TODO: implement wantKeepAlive
  @override
  bool get wantKeepAlive => true;

  // TODO: implement isRefreshFirst
  @override
  bool get isRefreshFirst => true;

  @override
  requestLoadMore() async {
    return await _getData();
  }

  @override
  requestRefresh() async {
    return await _getData(isRefresh: true);
  }
}
