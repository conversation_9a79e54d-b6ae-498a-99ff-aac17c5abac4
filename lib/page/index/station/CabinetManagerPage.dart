import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CabinetDao.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_entity.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/page_life_cycle.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CabinetBoxUtil.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/utils/ThrottleUtil.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/BottomBtnWidget.dart';
import 'package:cabinet_flutter_app/widget/CabinetBoxListWidget.dart';
import 'package:cabinet_flutter_app/widget/CollectIconButton.dart';
import 'package:cabinet_flutter_app/widget/CountDownWidget.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

import 'CabinetTakeOutPage.dart';

class CabinetManagerPage extends StatefulWidget {
  final String code;
  final String hostIndex;

  CabinetManagerPage(this.code, this.hostIndex, {Key? key}) : super(key: key);

  @override
  _CabinetManagerPageState createState() => _CabinetManagerPageState();
}

class _CabinetManagerPageState extends State<CabinetManagerPage>
    with WidgetsBindingObserver, PageLifeCycle<CabinetManagerPage>, AutomaticKeepAliveClientMixin {
  final GlobalKey<ScaffoldState> _cabinetManagerWaitKey = GlobalKey<ScaffoldState>();
  GlobalKey<CountDownWidgetState> countDownKey = GlobalKey();
  ThrottleUtil throttleUtil = ThrottleUtil();
  bool isPda = false;
  double marginValue = 10;
  CabinetEntity cabinetEntity = new CabinetEntity();
  int keepEffectCount = 0; // 总滞留件数量
  int unSignCount = 0; // 待取件数量不包含滞留件
  int gt3dayKeepEffectCount = 0; // 大于3天滞留件数量
  int gt7dayKeepEffectCount = 0; // 大于7天滞留件数量
  int forceOutboundDays = 0; // 强制取出天数
  bool canInbound = false; // 能否派件
  int waitTakenCount = 0; // 寄件待取
  List<dynamic> emptyList = [];

  void initState() {
    super.initState();
    this.init();
  }

  init() {
    checkIsPda();
    getCabinetInfo();
  }

  checkIsPda() async {
    isPda = await CheckUtils.isPda();
  }

  @override
  void dispose() {
    super.dispose();
    countDownKey.currentState?.dispose();
  }

  @override
  void onShow() async {}

  Future<Null> onFresh() async {
    init();
  }

  /// 获取点位可用盒子汇总
  getCabinetUsableBox() async {
    DataResult res = await CabinetDao.getCabinetUsableBox(widget.code);
    if (res.result) {
      Map<String, dynamic> countMap = {
        'microCount': res.data['microCount'],
        'miniCount': res.data['miniCount'],
        'smallCount': res.data['smallCount'],
        'mediumCount': res.data['mediumCount'],
        'largeCount': res.data['largeCount'],
        'hugeCount': res.data['hugeCount'],
        'superCount': res.data['superCount'],
      };
      emptyList = CabinetBoxUtil.getCabinetBoxAvailableCount(cabinetEntity.dispatchJson, countMap);
    }
    setState(() {});
  }

  /// 获取点位信息
  getCabinetInfo() async {
    var res = await CabinetDao.getCabinetInfo(widget.code);
    if (res != null && res.result) {
      cabinetEntity = CabinetEntity.fromJson(res.data);
      await LocalStorage.save(DefaultConfig().configs.HAS_SITE_OPEN_ADD_CABINET, cabinetEntity.hasAddCabinet, isPrivate: true);
      await LocalStorage.save(DefaultConfig().configs.HAS_CHANGE_BRAND, cabinetEntity.hasChangeBrand,
          isPrivate: true);
      getCourierCabinetFullInfo();
      getCabinetUsableBox();
    }
    setState(() {});
  }

  /// 获取快递员与点位关系
  getCourierCabinetFullInfo() async {
    var res = await CourierDao.getCourierCabinetFullInfo(widget.code);
    if (res != null && res.result) {
      cabinetEntity.hasCollected = res.data['hasCollected'] ? 1 : 0;
      cabinetEntity.hasUsed = res.data['hasUsed'] ? 1 : 0;
      unSignCount = res.data['unSignCount'];
      keepEffectCount = res.data['keepEffectCount'];
      gt3dayKeepEffectCount = res.data['gt3dayKeepEffectCount'];
      gt7dayKeepEffectCount = res.data['gt7dayKeepEffectCount'];
      forceOutboundDays = res.data['forceOutboundDays'];
      canInbound = res.data['canInbound'];
      waitTakenCount = res.data['waitTakenCount'];
      setState(() {});
      print(res.data['canInbound']);
      print(res.data['forceOutboundDays']);
    }
    checkPackage();
  }

  /// 检查包裹是否存在超过3或超时提醒时间滞留件
  checkPackage() {
    if (!canInbound) {
      CommonUtils.customConfirm(context, '该柜机内包裹已经滞留超过${forceOutboundDays}天,请尽快处理', title: '滞留件未处理', actions: <Widget>[
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  goPackagePage(context, PackageType.ZL);
                },
                child: Text('去处理', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
                style: ButtonStyle(
                    foregroundColor: MaterialStateProperty.resolveWith((states) {
                      if (states.contains(MaterialState.focused) && !states.contains(MaterialState.pressed)) {
                        return Theme.of(context).primaryColorLight;
                      } else if (states.contains(MaterialState.pressed)) {
                        return Colors.white;
                      }
                      return Colors.white;
                    }),
                    backgroundColor: MaterialStateProperty.resolveWith((states) {
                      if (states.contains(MaterialState.pressed)) {
                        return Theme.of(context).primaryColorLight;
                      }
                      return Theme.of(context).primaryColor;
                    }),
                    minimumSize: MaterialStateProperty.all(Size(90, 40)))),
          ],
        )
      ]);
    }
    // else if (haveOverDayPackage(3)) {
    //   CommonUtils.showMessage(context, msg: "有滞留超过三天包裹,请尽快处理", success: false, duration: 2);
    // }
  }

  /// 收藏点位
  setCabinetCollect() async {
    var res = await CourierDao.setCabinetCollected(widget.code, !(cabinetEntity.hasCollected == 1));
    if (res != null && res.result) {
      int isCollect = res.data['hasCollect'];
      cabinetEntity.hasCollected = isCollect;
      Fluttertoast.showToast(msg: isCollect == 1 ? '收藏成功' : '取消收藏成功', gravity: ToastGravity.CENTER);
      setState(() {});
    }
  }

  /// 点位信息
  buildCabinetInfo() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(
          Radius.circular(7),
        ),
        border: Border.all(
          width: 1,
          color: Colors.white,
        ),
      ),
      margin: EdgeInsets.all(marginValue),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
              child: Text(
                '点位信息',
                style: TextStyle(fontSize: AppConstant.middleTextWhiteSize, fontWeight: FontWeight.w600),
              ),
              margin: EdgeInsets.fromLTRB(10, 6, 10, 6)),
          Container(
            margin: EdgeInsets.fromLTRB(10, 4, 10, 4),
            child: Row(
              children: [
                Text(
                  '点位',
                  style: TextStyle(fontSize: AppConstant.smallTextSize, color: DefaultConfig().configs.GREY_COLOR),
                ),
                Expanded(
                    child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text(
                      '${cabinetEntity.name}',
                      style: TextStyle(fontSize: 14),
                    )
                  ],
                ))
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.fromLTRB(10, 4, 10, 4),
            child: Row(
              children: [
                Text(
                  '编号',
                  style: TextStyle(fontSize: AppConstant.smallTextSize, color: DefaultConfig().configs.GREY_COLOR),
                ),
                Expanded(
                    child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text(
                      '${cabinetEntity.code}',
                      style: TextStyle(fontSize: 14),
                    )
                  ],
                ))
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.fromLTRB(10, 4, 10, 4),
            child: Row(
              children: [
                Text(
                  '地址',
                  style: TextStyle(fontSize: AppConstant.smallTextSize, color: DefaultConfig().configs.GREY_COLOR),
                ),
                Padding(padding: EdgeInsets.only(left: 10)),
                Expanded(
                  child: Container(
                    alignment: Alignment.centerRight,
                    child: Text(
                      '${cabinetEntity.address}',
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                )
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.fromLTRB(10, 4, 10, 4),
            child: Row(
              children: [
                CollectIconButtonWidget(
                    value: cabinetEntity.hasCollected == 1,
                    onPress: () {
                      setCabinetCollect();
                    }),
                Expanded(
                    child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Offstage(
                      offstage: !(cabinetEntity.hasUsed == 1),
                      child: Container(
                        padding: EdgeInsets.fromLTRB(8, 2, 8, 2),
                        decoration: BoxDecoration(
                            border: Border.all(color: Theme.of(context).primaryColor), borderRadius: BorderRadius.all(Radius.circular(20))),
                        child: Text(
                          '常用柜',
                          style: TextStyle(
                            color: Theme.of(context).primaryColor,
                            fontSize: AppConstant.tinyTextSize,
                          ),
                        ),
                      ),
                    )
                  ],
                ))
              ],
            ),
          )
        ],
      ),
    );
  }

  /// 格口信息
  buildGKInfo() {
    return Container(
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(
            Radius.circular(7),
          ),
          border: Border.all(width: 1, color: Colors.white)),
      margin: EdgeInsets.only(left: marginValue, right: marginValue),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            child: Text(
              '格口信息',
              style: TextStyle(fontSize: AppConstant.middleTextWhiteSize, fontWeight: FontWeight.w600),
            ),
            margin: EdgeInsets.fromLTRB(10, 6, 10, 6),
          ),
          Container(
            margin: EdgeInsets.only(left: 10, right: 10, bottom: 20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(5)),
              color: Color(0xFFF5F5F5),
            ),
            // child: Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   children: buildAvailableGK(),
            // ),
            child: CabinetBoxListWidget(emptyList),
          )
        ],
      ),
    );
  }

  /// 检测滞留件数
  haveOverDayPackage(int day) {
    if (day == 3) {
      return gt3dayKeepEffectCount > 0;
    } else if (day == 7) {
      return gt7dayKeepEffectCount > 0;
    }
  }

  /// 我的在柜包裹
  buildPackageInfo() {
    return Container(
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(
              Radius.circular(7),
            ),
            border: Border.all(width: 1, color: Colors.white)),
        width: double.infinity,
        margin: EdgeInsets.only(left: marginValue, top: marginValue, right: marginValue),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: EdgeInsets.fromLTRB(10, 6, 10, 6),
              child: Text(
                '我的在柜包裹',
                style: TextStyle(fontSize: AppConstant.middleTextWhiteSize, fontWeight: FontWeight.w600),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Column(
                  children: [
                    Container(
                      padding: EdgeInsets.only(top: 10),
                      child: Text(
                        '$keepEffectCount',
                        style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 25),
                      ),
                    ),
                    Text(
                      '滞留件',
                      style: TextStyle(color: Colors.black, fontSize: 14),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        goPackagePage(context, PackageType.ZL);
                      },
                      child: Text(
                        '查看',
                        style: TextStyle(fontSize: 12),
                      ),
                      style: ElevatedButton.styleFrom(
                          shadowColor: Theme.of(context).primaryColor,
                          backgroundColor: Theme.of(context).primaryColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(5),
                          ),
                          minimumSize: Size(50, 25)),
                    ),
                  ],
                ),
                Container(
                  width: 1,
                  height: 40,
                  color: Colors.grey.shade200,
                ),
                Column(
                  children: [
                    Container(
                      padding: EdgeInsets.only(top: 10),
                      child: Text(
                        '$unSignCount',
                        style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 25),
                      ),
                    ),
                    Text(
                      '待取件',
                      style: TextStyle(color: Colors.black, fontSize: 14),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        goPackagePage(context, PackageType.DQ);
                      },
                      child: Text(
                        '查看',
                        style: TextStyle(fontSize: 12),
                      ),
                      style: ElevatedButton.styleFrom(
                          shadowColor: Theme.of(context).primaryColor,
                          backgroundColor: Theme.of(context).primaryColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(5),
                          ),
                          minimumSize: Size(50, 25)),
                    ),
                  ],
                ),
                Container(
                  width: 1,
                  height: 40,
                  color: Colors.grey.shade200,
                ),
                Column(
                  children: [
                    Container(
                      padding: EdgeInsets.only(top: 10),
                      child: Text(
                        '$waitTakenCount',
                        style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 25),
                      ),
                    ),
                    Text(
                      '寄件待取',
                      style: TextStyle(color: Colors.black, fontSize: 14),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        goPackagePage(context, PackageType.DS);
                      },
                      child: Text(
                        '查看',
                        style: TextStyle(fontSize: 12),
                      ),
                      style: ElevatedButton.styleFrom(
                          shadowColor: Theme.of(context).primaryColor,
                          backgroundColor: Theme.of(context).primaryColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(5),
                          ),
                          minimumSize: Size(50, 25)),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ));
  }

  /// 我的在柜包裹
  buildPackageInfo1() {
    return Container(
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(
            Radius.circular(7),
          ),
          border: Border.all(width: 1, color: Colors.white)),
      width: double.infinity,
      margin: EdgeInsets.only(left: marginValue, top: marginValue, right: marginValue),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.fromLTRB(10, 6, 10, 6),
            child: Text(
              '我的在柜包裹',
              style: TextStyle(fontSize: AppConstant.middleTextWhiteSize, fontWeight: FontWeight.w600),
            ),
          ),
          Container(
              margin: EdgeInsets.only(left: 10, top: 8, right: 10, bottom: 10),
              child: Row(
                children: [
                  Text(
                    '滞留件',
                    style: TextStyle(color: Colors.grey, fontSize: 14),
                  ),
                  Expanded(
                      child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text('$keepEffectCount', style: TextStyle(color: Colors.grey, fontSize: 14)),
                      Offstage(
                          offstage: keepEffectCount == 0,
                          child: Row(
                            children: [
                              SizedBox(
                                width: 20,
                              ),
                              InkWell(
                                child: Text('去处理', style: TextStyle(color: Colors.deepOrangeAccent, fontSize: 14)),
                                onTap: () {
                                  goPackagePage(context, PackageType.ZL);
                                },
                              ),
                            ],
                          ))
                    ],
                  ))
                ],
              )),
          Container(
              margin: EdgeInsets.only(left: 10, top: 6, right: 10, bottom: 20),
              child: Row(
                children: [
                  Text(
                    '待取件',
                    style: TextStyle(color: Colors.grey, fontSize: 14),
                  ),
                  Expanded(
                      child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text('$unSignCount', style: TextStyle(color: Colors.grey, fontSize: 14)),
                      Offstage(
                          offstage: unSignCount == 0,
                          child: Row(
                            children: [
                              SizedBox(
                                width: 20,
                              ),
                              InkWell(
                                child: Text('去处理', style: TextStyle(color: Colors.deepOrangeAccent, fontSize: 14)),
                                onTap: () {
                                  goPackagePage(context, PackageType.DS);
                                },
                              ),
                            ],
                          ))
                    ],
                  ))
                ],
              ))
        ],
      ),
    );
  }

  goPackagePage(BuildContext context, PackageType type) async {
    stopCountDown();
    if (type == PackageType.DS) {
      //直接转跳
      Navigator.of(context)
          .push(MaterialPageRoute(
        builder: (context) => CabinetTakeOutPage(cabinetEntity, type, widget.hostIndex),
      ))
          .then((value) {
        ///刷新数据
        getCourierCabinetFullInfo();
      });
    } else {
      await NavigatorUtils.goCabinetPackagePage(context, cabinetEntity, type,
          overDays: cabinetEntity.overdueOutboundDays, hostIndex: widget.hostIndex);
    }
    resetCountDown();
    getCourierCabinetFullInfo();
    getCabinetUsableBox();
  }

  buildInboundBtn() {
    List<Widget> list = [buildBtn()];
    if (cabinetEntity.switchBatch == 1) {
      list.add(buildBtn(isBatch: true));
    }
    return Row(children: list);
  }

  buildBtn({bool isBatch = false}) {
    return BottomBtnWidget(
        showShadow: true,
        width: cabinetEntity.switchBatch == 0 ? null : MediaQuery.of(context).size.width / 2 - 20,
        title: isBatch ? '批量入柜' : '派件入柜',
        type: !canInbound ? ButtonType.primaryDisabled : ButtonType.primary,
        action: () => throttleUtil.throttle(() async {
              if (canInbound) {
                stopCountDown();
                if (isBatch) {
                  if (isPda) {
                    await NavigatorUtils.goBatchEntryScanCabinetPda(context, widget.code, '', hostIndex: widget.hostIndex);
                  } else {
                    await NavigatorUtils.goBatchEntryScanCabinet(context, widget.code, '', hostIndex: widget.hostIndex);
                  }
                } else {
                  if (isPda) {
                    await NavigatorUtils.goEntryScanPda(context, widget.code, hostIndex: widget.hostIndex);
                  } else {
                    await NavigatorUtils.goEntryScan(context, widget.code, hostIndex: widget.hostIndex);
                  }
                }
                resetCountDown();
                getCourierCabinetFullInfo();
                getCabinetUsableBox();
              }
            }));
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      backgroundColor: DefaultConfig().configs.BG_COLOR,
      key: _cabinetManagerWaitKey,
      appBar: new AppCustomerBar(
        title: AppbarTitle(
          title: '派件入柜',
          isCenter: true,
        ),
        actions: [
          CountDownWidget(
            countDownKey,
            isBlack: true,
            onTimerFinish: () {
              NavigatorUtils.replaceHome(context);
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: onFresh,
        child: Column(
          children: [
            Expanded(
                child: SingleChildScrollView(
                    physics: AlwaysScrollableScrollPhysics(),
                    child: Column(
                      children: [
                        /// 我的在柜包裹
                        buildPackageInfo(),

                        ///点位信息
                        buildCabinetInfo(),

                        /// 格口信息
                        buildGKInfo(),
                      ],
                    ))),
            buildInboundBtn()
          ],
        ),
      ),
    );
  }

  resetCountDown() {
    countDownKey.currentState?.resetCountDown();
  }

  stopCountDown() {
    countDownKey.currentState?.stopCountDown();
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;
}
