// ignore_for_file: unnecessary_null_comparison

import 'dart:async';
import 'dart:io';
import 'dart:ui';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/PhotoDao.dart';
import 'package:cabinet_flutter_app/common/entitys/photo_entity.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/utils/SoundUtils.dart';
import 'package:cabinet_flutter_app/widget/NoResult.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:scan/scan.dart';

class PhotoUpload extends StatefulWidget {
  PhotoUpload({Key? key}) : super(key: key);

  @override
  _PhotoUploadState createState() => _PhotoUploadState();
}

class _PhotoUploadState extends State<PhotoUpload> with SingleTickerProviderStateMixin {
  ScrollController scrollControllerH = new ScrollController();
  List<PhotoEntity?> photoList = [];
  Scan scanPlugin = Scan();
  StreamSubscription<dynamic>? photoSubscription;
  int page = 0;
  int total = 0;
  String loadingStatus = '1'; // 0-加载中  1-上拉加载更多  2-没有更多
  String keyword = '';
  bool isInput = false;
  List typeList = [
    {"label": '全部', "value": ''},
    {"label": '成功', "value": 'OK'},
    {"label": '失败', "value": 'FAIL'},
  ];

  String status = 'FAIL';
  TabController? _tabController;
  List tabs = ['全部', '成功', '失败'];
  List typeTabs = ['', 'OK', 'FAIL'];

  @override
  void dispose() {
    super.dispose();
    photoSubscription?.cancel();
  }

  @override
  void initState() {
    super.initState();
    listenScroll();
    initPhoto();
    initTab();
  }

  initTab() {
    _tabController = TabController(length: tabs.length, vsync: this);

    _tabController?.index = typeTabs.indexOf(status);

    _tabController?.addListener(() {
      status = typeTabs[_tabController!.index];
      getPhotoList();
    });
  }

  listenScroll() {
    if (scrollControllerH != null) {
      scrollControllerH.addListener(() async {
        /// 判断当前滑动位置是不是到达底部，触发加载更多回调
        if (scrollControllerH.position.pixels == scrollControllerH.position.maxScrollExtent) {
          if (page * 10 < total) {
            setState(() {
              loadingStatus = '0';
              page++;
            });
            await getPhotoList();
          }
        }
      });
    }
  }

  initPhoto() {
    photoSubscription = scanPlugin.onScanChanged.listen((result) {
      if (result.containsKey('path')) {
        if (result['status'] == 'OK' && status != 'OK') {
          int index = photoList.indexWhere((item) => item!.path == result['path']);
          if (index > -1) {
            photoList.removeAt(index);
          }
          if (status == 'FAIL' && total > 0) {
            total--;
          }
        }
        setState(() {});
      }
    });
  }

  Future<Null> onFresh() async {
    page = 0;
    getPhotoList();
  }

  getPhotoList() async {
    getTotal();
    List<PhotoEntity> list = await PhotoDao.getPhotoList(status: status, page: page, sheetNo: keyword);
    if (page == 0) {
      photoList = list;
    } else {
      photoList.addAll(list);
    }
    if (photoList.length == total) {
      loadingStatus = '2';
    } else {
      loadingStatus = '1';
    }
    setState(() {});
  }

  getTotal() async {
    total = (status == '' || status == null)
        ? await PhotoDao.getPhotoAllCount(keyword)
        : await PhotoDao.getPhotoCount(status: status, sheetNo: keyword);
    setState(() {});
  }

  backTypeText(String type) {
    String title = '';
    switch (type) {
      case "rk":
        title = "入库";
        break;
      case "ck":
        title = "出库";
        break;
    }
    return title;
  }

  uploadImage() async {
    CommonUtils.isSaveDialog(
      context,
      (isTrue) async {
        if (isTrue) {
          await PhotoDao.uploadAllPhotoByStatus('FAIL');
        }
      },
      contentWidget: Container(
        padding: EdgeInsets.fromLTRB(20, 30, 20, 30),
        child: RichText(
          text: TextSpan(
            children: <TextSpan>[
              TextSpan(
                text: '确认',
                style: TextStyle(color: Colors.black87, fontSize: 15),
              ),
              TextSpan(
                text: ' 重传失败图片 ',
                style: TextStyle(color: Colors.redAccent, fontWeight: FontWeight.w600, fontSize: 14),
              ),
              TextSpan(
                text: '吗?',
                style: TextStyle(color: Colors.black87, fontSize: 15),
              ),
            ],
          ),
        ),
      ),
      successText: '重传',
      successColor: Theme.of(context).primaryColor,
      cancelColor: Colors.grey.shade500,
    );
  }

  deleteImage() async {
    CommonUtils.isSaveDialog(
      context,
      (isTrue) async {
        if (isTrue) {
          await PhotoDao.deleteAllPhotoByStatus('OK');
          await getTotal();
          page = 0;
          getPhotoList();
          setState(() {});
        }
      },
      contentWidget: Container(
        padding: EdgeInsets.fromLTRB(20, 30, 20, 30),
        child: RichText(
          text: TextSpan(
            children: <TextSpan>[
              TextSpan(
                text: '删除后，本地上传成功照片记录将被',
                style: TextStyle(color: Colors.black87, fontSize: 14),
              ),
              TextSpan(
                text: '清空',
                style: TextStyle(color: Colors.redAccent, fontWeight: FontWeight.w600, fontSize: 14),
              ),
              TextSpan(
                text: '，',
                style: TextStyle(color: Colors.black87, fontSize: 14),
              ),
              TextSpan(
                text: '数据无法恢复',
                style: TextStyle(color: Colors.redAccent, fontWeight: FontWeight.w600, fontSize: 14),
              ),
              TextSpan(
                text: '，是否确认删除?',
                style: TextStyle(color: Colors.black87, fontSize: 14),
              ),
            ],
          ),
        ),
      ),
      successText: '删除',
      successColor: Colors.redAccent,
      bgColor: Colors.redAccent,
      cancelColor: Colors.grey.shade500,
    );
  }

  toPreview(String path) async {
    List<File> fileList = [];
    for (int i = 0; i < photoList.length; i++) {
      PhotoEntity? v = photoList[i];
      File imgFile = File(v!.path!);
      if (await imgFile.exists()) {
        fileList.add(File(v.path!));
      }
    }
    if (fileList.length > 0) {
      int index = fileList.indexWhere((item) => item.path == path);
      Timer(const Duration(milliseconds: 500), () {
        NavigatorUtils.goPhotoViewGalleryScreen(context, fileList, index);
      });
    } else {
      Fluttertoast.showToast(msg: '暂无可预览图片');
    }
  }

  Future<bool> checkIsExist(String path) async {
    File imgFile = File(path);
    return await imgFile.exists();
  }

  setStatus(String value) {
    status = value;
    page = 0;
    getPhotoList();
    setState(() {});
  }

  onSearchChanged(String value) {
    keyword = value;
  }

  onSearchSubmit(String value) {
    page = 0;
    getPhotoList();
  }

  buildItem() {
    double height = 140;
    double width = 70;
    double minFont = 14;
    double bigFont = 15;
    Color labelColor = Colors.grey.shade500;
    List<Widget> widgets = [];

    photoList.forEach((item) {
      File imageFile = new File(item!.path!);
      List<Widget> actions = [
        SlidableAction(
          onPressed: (context) {
            CommonUtils.isSaveDialog(
              context,
              (isTrue) async {
                if (isTrue) {
                  await PhotoDao.deletePhoto(item);
                  try {
                    imageFile.deleteSync();
                    print("删除文件成功");
                  } catch (e) {
                    print("删除文件出错了：$e");
                  }
                  int index = photoList.indexWhere((v) => v!.path == item.path);
                  if (index > -1) {
                    photoList.removeAt(index);
                  }
                  SoundUtils.audioPushFn(SoundUtils.SUCCESS_OK);
                  await getTotal();
                  setState(() {});
                }
              },
              contentWidget: Container(
                padding: EdgeInsets.fromLTRB(20, 30, 20, 30),
                child: RichText(
                  text: TextSpan(
                    children: <TextSpan>[
                      TextSpan(
                        text: '删除后，本地上传照片记录将被',
                        style: TextStyle(color: Colors.black87, fontSize: 14),
                      ),
                      TextSpan(
                        text: '清空',
                        style: TextStyle(color: Colors.redAccent, fontWeight: FontWeight.w600, fontSize: 14),
                      ),
                      TextSpan(
                        text: '，',
                        style: TextStyle(color: Colors.black87, fontSize: 14),
                      ),
                      TextSpan(
                        text: '数据无法恢复',
                        style: TextStyle(color: Colors.redAccent, fontWeight: FontWeight.w600, fontSize: 14),
                      ),
                      TextSpan(
                        text: '，是否确认删除此记录?',
                        style: TextStyle(color: Colors.black87, fontSize: 14),
                      ),
                    ],
                  ),
                ),
              ),
              successText: '删除',
              successColor: Colors.redAccent,
              bgColor: Colors.redAccent,
              cancelColor: Colors.grey.shade500,
            );
          },
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
          label: '删除',
        )
      ];
      if (item.status == 'FAIL') {
        actions.insert(
            0,
            SlidableAction(
              onPressed: (context) {
                CommonUtils.isSaveDialog(
                  context,
                  (isTrue) async {},
                  content: item.msg!,
                  showCancel: false,
                  successText: '关闭',
                  successColor: Colors.redAccent,
                  bgColor: Colors.redAccent,
                );
              },
              backgroundColor: Colors.orangeAccent,
              foregroundColor: Colors.white,
              label: '详情',
            ));
      }
      widgets.add(
        Slidable(
          child: Container(
            color: Colors.white,
            child: Row(
              children: [
                Expanded(
                  child: FutureBuilder(
                    future: checkIsExist(item.path!),
                    builder: (BuildContext context, AsyncSnapshot<bool> snapshot) {
                      return Container(
                        height: height,
                        padding: EdgeInsets.fromLTRB(5, 10, 5, 10),
                        decoration: BoxDecoration(
                          color: item.status == 'OK' ? Colors.white : Color(0xFFFEF0F0),
                          border: Border(
                            bottom: BorderSide(
                              width: 1,
                              color: item.status == 'OK' ? Colors.grey.shade100 : Colors.white,
                            ),
                          ),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            snapshot.data == true
                                ? InkWell(
                                    onTap: () {
                                      toPreview(item.path!);
                                    },
                                    child: Container(
                                      width: width,
                                      height: 110,
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(5),
                                        child: Image.file(
                                          imageFile,
                                          fit: BoxFit.fitWidth,
                                        ),
                                      ),
                                    ),
                                  )
                                : Container(
                                    width: 80,
                                    height: 110,
                                    padding: EdgeInsets.only(left: 5, right: 5),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(5),
                                      child: Image.asset(
                                        'static/images/pic-loss.png',
                                        fit: BoxFit.fitWidth,
                                      ),
                                    ),
                                  ),
                            Expanded(
                              child: Container(
                                margin: EdgeInsets.only(left: 10),
                                child: Stack(
                                  children: [
                                    Container(
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              Text('单号:', style: TextStyle(fontSize: minFont, color: labelColor)),
                                              Padding(padding: EdgeInsets.only(left: 5)),
                                              Text('${item.sheetNo}',
                                                  style: TextStyle(
                                                      fontSize: bigFont, color: Theme.of(context).primaryColor)),
                                            ],
                                          ),
                                          Padding(padding: EdgeInsets.only(top: 2)),
                                          Row(
                                            children: [
                                              Text('品牌:', style: TextStyle(fontSize: minFont, color: labelColor)),
                                              Padding(padding: EdgeInsets.only(left: 5)),
                                              Text('${DefaultConfig().configs.EXPRESS2[item.company]}',
                                                  style: TextStyle(fontSize: minFont, color: Colors.black87)),
                                            ],
                                          ),
                                          Padding(padding: EdgeInsets.only(top: 2)),
                                          Row(
                                            children: [
                                              Text('类型:', style: TextStyle(fontSize: minFont, color: labelColor)),
                                              Padding(padding: EdgeInsets.only(left: 5)),
                                              Text('${backTypeText(item.type!)}',
                                                  style: TextStyle(fontSize: minFont, color: Colors.black87)),
                                            ],
                                          ),
                                          Padding(padding: EdgeInsets.only(top: 2)),
                                          Row(
                                            children: [
                                              Text('时间:', style: TextStyle(fontSize: minFont, color: labelColor)),
                                              Padding(padding: EdgeInsets.only(left: 5)),
                                              Padding(
                                                padding: EdgeInsets.only(top: 4),
                                                child: Text('${item.createDate}',
                                                    style: TextStyle(fontSize: bigFont, color: Colors.black87)),
                                              ),
                                            ],
                                          ),
                                          Padding(padding: EdgeInsets.only(top: 2)),
                                          Row(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text('结果:', style: TextStyle(fontSize: minFont, color: labelColor)),
                                              Expanded(
                                                child: Container(
                                                  margin: EdgeInsets.only(left: 5),
                                                  padding: EdgeInsets.only(top: item.status == 'OK' ? 0 : 3),
                                                  child: Text(
                                                    '${item.msg}',
                                                    style: TextStyle(
                                                      fontSize: minFont,
                                                      color: item.status == 'OK' ? Colors.green : Colors.red,
                                                    ),
                                                    overflow: TextOverflow.ellipsis,
                                                    maxLines: 1,
                                                  ),
                                                ),
                                              )
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    Positioned(
                                      top: 20,
                                      right: 40,
                                      child: Offstage(
                                        offstage: item.status != 'FAIL',
                                        child: LocalImageUtil.getImageAsset('uploadFail', width: 40),
                                      ),
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          endActionPane: ActionPane(
            motion: ScrollMotion(),
            children: actions,
          ),
        ),
      );
    });

    if (loadingStatus == '0') {
      widgets.add(Row(mainAxisAlignment: MainAxisAlignment.center, children: <Widget>[
        ///loading框
        SpinKitChasingDots(color: Theme.of(context).primaryColor),
        Container(
          width: 5.0,
        ),

        ///加载中文本
        Text(
          CommonUtils.getLocale(context).loadMoreText,
          style: TextStyle(
            color: Color(0xFF121917),
            fontSize: 14.0,
            fontWeight: FontWeight.bold,
          ),
        )
      ]));
    } else {
      widgets.add(Container(
        padding: EdgeInsets.all(5),
        child: Center(
          child: Text(
            loadingStatus == '1' ? '亲，上拉加载更多哦~' : '亲，已经到底啦~',
            style: TextStyle(fontSize: 12.0, color: Colors.grey.shade400),
          ),
        ),
      ));
    }
    return Column(
      children: widgets,
    );
  }

  /// 头部导航
  buildNavbar() {
    double statusHeight = MediaQuery.of(context).padding.top + 5;
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
      ),
      padding: EdgeInsets.fromLTRB(10, statusHeight, 0, 10),
      child: isInput
          ? Row(
              children: [
                Material(
                  color: Colors.white,
                  child: InkWell(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Container(
                      width: 40,
                      height: 40,
                      child: Icon(
                        Icons.arrow_back_sharp,
                        color: Colors.black87,
                        size: 28,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    height: 40,
                    padding: EdgeInsets.only(left: 15),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.all(Radius.circular(30)),
                    ),
                    child: Row(
                      children: [
                        Padding(
                          padding: EdgeInsets.only(right: 5),
                          child: Image.asset(
                            'static/images/common/search.png',
                            width: 23,
                          ),
                        ),
                        Expanded(
                          child: TextField(
                            cursorWidth: 1,
                            cursorColor: Colors.grey,
                            style: TextStyle(
                              fontSize: 15,
                              color: Colors.black87,
                            ),
                            controller: TextEditingController.fromValue(
                              TextEditingValue(
                                // 设置内容
                                text: '${keyword}',
                                // 保持光标在最后
                                selection: TextSelection.fromPosition(
                                  TextPosition(
                                    affinity: TextAffinity.downstream,
                                    offset: keyword.length,
                                  ),
                                ),
                              ),
                            ),
                            decoration: InputDecoration.collapsed(
                              hintText: '输入单号(支持后6位)',
                              hintStyle: TextStyle(fontSize: 15, color: Colors.grey),
                            ),
                            onChanged: onSearchChanged,
                            onSubmitted: onSearchSubmit,
                          ),
                        ),
                        Offstage(
                          offstage: keyword == '' || keyword == null,
                          child: InkWell(
                            onTap: () {
                              setState(() {
                                keyword = '';
                              });
                            },
                            child: Container(
                              padding: EdgeInsets.all(10),
                              child: Icon(
                                Icons.clear,
                                size: 20,
                                color: Colors.grey,
                              ),
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                ),
                Container(
                  child: Material(
                    color: Colors.white,
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          isInput = false;
                          keyword = '';
                        });
                      },
                      child: Container(
                        width: 40,
                        height: 40,
                        padding: EdgeInsets.all(8),
                        child: Container(
                          child: LocalImageUtil.getImageAsset('reBack', width: 20),
                        ),
                      ),
                    ),
                  ),
                )
              ],
            )
          : Row(
              children: [
                Material(
                  color: Colors.white,
                  child: InkWell(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Container(
                      width: 40,
                      height: 40,
                      child: Icon(
                        Icons.arrow_back_sharp,
                        color: Colors.black87,
                        size: 28,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    child: Text(
                      '上传图片',
                      style: TextStyle(fontSize: 20, color: Colors.black87),
                    ),
                  ),
                ),
                Container(
                  child: Material(
                    color: Colors.white,
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          isInput = true;
                        });
                      },
                      child: Container(
                        width: 40,
                        height: 40,
                        padding: EdgeInsets.all(8),
                        child: Container(
                          child: LocalImageUtil.getImageAsset('search', width: 20),
                        ),
                      ),
                    ),
                  ),
                )
              ],
            ),
    );
  }

  /// 头部tab切换
  buildTab() {
    List<Widget> tabWidgets = [];
    tabs.forEach((item) {
      tabWidgets.add(Tab(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Text(
                  item,
                  style: TextStyle(
                    fontSize: 14.0,
                    color: tabs[_tabController!.index] == item ? Colors.black87 : Colors.grey.shade500,
                  ),
                ),
                Offstage(
                  offstage: tabs[_tabController!.index] != item,
                  child: Text(
                    '(' + total.toString() + ')',
                    style: TextStyle(
                      fontSize: 13.0,
                      color: tabs[_tabController!.index] == item ? Colors.black87 : Colors.grey.shade500,
                    ),
                  ),
                )
              ],
            ),
            Container(
              width: 40,
              height: 2,
              margin: EdgeInsets.only(top: 10),
              decoration: BoxDecoration(
                color: tabs[_tabController!.index] == item ? Theme.of(context).primaryColor : Colors.white,
              ),
            )
          ],
        ),
      ));
    });
    return Container(
      height: 50,
      decoration: BoxDecoration(
        color: Colors.white,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          TabBar(
            isScrollable: true,
            controller: _tabController,
            indicatorColor: Colors.white,
            tabs: tabWidgets,
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return StoreBuilder<AppState>(builder: (context, store) {
      return Scaffold(
        backgroundColor: Colors.grey.shade100,
        body: RefreshIndicator(
          onRefresh: onFresh,
          child: Column(
            children: [
              buildNavbar(),
              buildTab(),
              Expanded(
                child: photoList.length > 0
                    ? SingleChildScrollView(
                        physics: AlwaysScrollableScrollPhysics(),
                        child: buildItem(),
                        controller: scrollControllerH,
                      )
                    : Center(
                        child: NoResult(),
                      ),
              ),
              Container(
                padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
                color: Colors.white,
                child: Row(
                  children: [
                    status == 'FAIL'
                        ? Container()
                        : Expanded(
                            child: Material(
                              color: Colors.redAccent,
                              borderRadius: BorderRadius.all(Radius.circular(5)),
                              child: InkWell(
                                onTap: () {
                                  deleteImage();
                                },
                                child: Container(
                                  padding: EdgeInsets.all(15.0),
                                  alignment: Alignment.center,
                                  child: Text(
                                    '删除成功图片',
                                    style: TextStyle(color: Colors.white, fontSize: 14),
                                  ),
                                ),
                              ),
                            ),
                          ),
                    Padding(padding: EdgeInsets.only(left: 10)),
                    status == 'OK'
                        ? Container()
                        : Expanded(
                            child: Material(
                              color: Theme.of(context).primaryColor,
                              borderRadius: BorderRadius.all(Radius.circular(5)),
                              child: InkWell(
                                onTap: () async {
                                  uploadImage();
                                },
                                child: Container(
                                  padding: EdgeInsets.all(15.0),
                                  alignment: Alignment.center,
                                  child: Text(
                                    '重传失败图片',
                                    style: TextStyle(color: Colors.white, fontSize: 16.0),
                                  ),
                                ),
                              ),
                            ),
                          )
                  ],
                ),
              )
            ],
          ),
        ),
      );
    });
  }
}
