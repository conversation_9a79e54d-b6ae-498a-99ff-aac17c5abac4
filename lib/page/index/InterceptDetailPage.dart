// import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
// import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
// import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
// import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
// import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
// import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
// import 'package:cabinet_flutter_app/widget/BottomBtnWidget.dart';
// import 'package:flutter/material.dart';
// import 'package:fluttertoast/fluttertoast.dart';

// class InterceptDetailPage extends StatefulWidget {
//   final dynamic interceptData;
  
//   InterceptDetailPage({Key? key, this.interceptData}) : super(key: key);

//   @override
//   _InterceptDetailPageState createState() => _InterceptDetailPageState();
// }

// class _InterceptDetailPageState extends State<InterceptDetailPage> {
//   final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  
//   Map<String, dynamic> interceptInfo = {};
//   bool isLoading = false;
  
//   @override
//   void initState() {
//     super.initState();
//     _loadInterceptDetail();
//   }
  
//   void _loadInterceptDetail() {
//     if (widget.interceptData != null) {
//       setState(() {
//         interceptInfo = Map<String, dynamic>.from(widget.interceptData);
//       });
//     }
//   }
  
//   String _getStatusText(String? status) {
//     switch (status) {
//       case 'ACTIVE':
//         return '拦截中';
//       case 'COMPLETED':
//         return '已完成';
//       case 'CANCELLED':
//         return '已取消';
//       default:
//         return '未知';
//     }
//   }
  
//   Color _getStatusColor(String? status) {
//     switch (status) {
//       case 'ACTIVE':
//         return Colors.orange;
//       case 'COMPLETED':
//         return Colors.green;
//       case 'CANCELLED':
//         return Colors.red;
//       default:
//         return Colors.grey;
//     }
//   }
  
//   String _getExpressCompanyName(String? code) {
//     Map<String, String> companyMap = {
//       'SF': '顺丰速运',
//       'ZTO': '中通快递',
//       'YTO': '圆通速递',
//       'STO': '申通快递',
//       'YUNDA': '韵达速递',
//       'HTKY': '百世快递',
//       'DBL': '德邦快递',
//       'JD': '京东快递',
//       'EMS': '邮政EMS',
//       'HHTT': '天天快递',
//       'GTO': '国通快递',
//       'QFKD': '全峰快递',
//       'UC': '优速快递',
//       'FAST': '快捷快递',
//       'OTHER': '其他',
//     };
//     return companyMap[code] ?? code ?? '未知';
//   }
  
//   Future<void> _updateStatus(String newStatus) async {
//     setState(() {
//       isLoading = true;
//     });
    
//     try {
//       // TODO: 实现更新拦截状态的API调用
//       // DataResult result = await CourierDao.updateInterceptStatus(interceptInfo['id'], newStatus);
      
//       // 模拟API调用
//       await Future.delayed(Duration(seconds: 1));
//       DataResult result = DataResult();
//       result.result = true;
//       result.msg = '状态更新成功';
      
//       if (result.result) {
//         setState(() {
//           interceptInfo['status'] = newStatus;
//         });
//         Fluttertoast.showToast(msg: result.msg ?? '状态更新成功');
//       } else {
//         Fluttertoast.showToast(msg: result.msg ?? '状态更新失败');
//       }
//     } catch (e) {
//       Fluttertoast.showToast(msg: '状态更新失败: $e');
//     } finally {
//       setState(() {
//         isLoading = false;
//       });
//     }
//   }
  
//   void _showStatusDialog() {
//     showDialog(
//       context: context,
//       builder: (BuildContext context) {
//         return AlertDialog(
//           title: Text('更新状态'),
//           content: Column(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               ListTile(
//                 title: Text('拦截中'),
//                 leading: Radio<String>(
//                   value: 'ACTIVE',
//                   groupValue: interceptInfo['status'],
//                   onChanged: (String? value) {
//                     Navigator.of(context).pop();
//                     if (value != null) {
//                       _updateStatus(value);
//                     }
//                   },
//                 ),
//               ),
//               ListTile(
//                 title: Text('已完成'),
//                 leading: Radio<String>(
//                   value: 'COMPLETED',
//                   groupValue: interceptInfo['status'],
//                   onChanged: (String? value) {
//                     Navigator.of(context).pop();
//                     if (value != null) {
//                       _updateStatus(value);
//                     }
//                   },
//                 ),
//               ),
//               ListTile(
//                 title: Text('已取消'),
//                 leading: Radio<String>(
//                   value: 'CANCELLED',
//                   groupValue: interceptInfo['status'],
//                   onChanged: (String? value) {
//                     Navigator.of(context).pop();
//                     if (value != null) {
//                       _updateStatus(value);
//                     }
//                   },
//                 ),
//               ),
//             ],
//           ),
//           actions: [
//             TextButton(
//               onPressed: () {
//                 Navigator.of(context).pop();
//               },
//               child: Text('取消'),
//             ),
//           ],
//         );
//       },
//     );
//   }
  
//   Widget _buildInfoCard(String title, String content, {Widget? trailing}) {
//     return Container(
//       margin: EdgeInsets.only(bottom: 12),
//       padding: EdgeInsets.all(16),
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(8),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.grey.withOpacity(0.1),
//             spreadRadius: 1,
//             blurRadius: 3,
//             offset: Offset(0, 1),
//           ),
//         ],
//       ),
//       child: Row(
//         children: [
//           Expanded(
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(
//                   title,
//                   style: TextStyle(
//                     fontSize: 14,
//                     color: Colors.grey[600],
//                   ),
//                 ),
//                 SizedBox(height: 8),
//                 Text(
//                   content,
//                   style: TextStyle(
//                     fontSize: 16,
//                     fontWeight: FontWeight.w500,
//                     color: Colors.black87,
//                   ),
//                 ),
//               ],
//             ),
//           ),
//           if (trailing != null) trailing,
//         ],
//       ),
//     );
//   }
  
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       key: _scaffoldKey,
//       backgroundColor: Colors.grey.shade100,
//       appBar: AppCustomerBar(
//         title: AppbarTitle(
//           title: '拦截详情',
//           isCenter: true,
//         ),
//         actions: [
//           IconButton(
//             icon: Icon(Icons.edit),
//             onPressed: () async {
//               final result = await NavigatorUtils.goAddInterceptPage(context, interceptInfo);
//               if (result == true) {
//                 // 刷新数据
//                 _loadInterceptDetail();
//               }
//             },
//           ),
//         ],
//       ),
//       body: Column(
//         children: [
//           Expanded(
//             child: SingleChildScrollView(
//               padding: EdgeInsets.all(16),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   // 状态卡片
//                   _buildInfoCard(
//                     '当前状态',
//                     _getStatusText(interceptInfo['status']),
//                     trailing: Container(
//                       padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
//                       decoration: BoxDecoration(
//                         color: _getStatusColor(interceptInfo['status']),
//                         borderRadius: BorderRadius.circular(16),
//                       ),
//                       child: Text(
//                         _getStatusText(interceptInfo['status']),
//                         style: TextStyle(
//                           color: Colors.white,
//                           fontSize: 12,
//                           fontWeight: FontWeight.bold,
//                         ),
//                       ),
//                     ),
//                   ),
                  
//                   // 快递单号
//                   _buildInfoCard(
//                     '快递单号',
//                     interceptInfo['trackingNumber'] ?? '',
//                   ),
                  
//                   // 快递公司
//                   _buildInfoCard(
//                     '快递公司',
//                     _getExpressCompanyName(interceptInfo['expressCompany']),
//                   ),
                  
//                   // 拦截信息
//                   _buildInfoCard(
//                     '拦截信息',
//                     interceptInfo['interceptInfo'] ?? '',
//                   ),
                  
//                   // 创建时间
//                   _buildInfoCard(
//                     '创建时间',
//                     interceptInfo['createTime'] ?? '',
//                   ),
                  
//                   // 更新时间
//                   if (interceptInfo['updateTime'] != null)
//                     _buildInfoCard(
//                       '更新时间',
//                       interceptInfo['updateTime'],
//                     ),
                  
//                   // 操作记录
//                   Container(
//                     margin: EdgeInsets.only(bottom: 12),
//                     padding: EdgeInsets.all(16),
//                     decoration: BoxDecoration(
//                       color: Colors.white,
//                       borderRadius: BorderRadius.circular(8),
//                       boxShadow: [
//                         BoxShadow(
//                           color: Colors.grey.withOpacity(0.1),
//                           spreadRadius: 1,
//                           blurRadius: 3,
//                           offset: Offset(0, 1),
//                         ),
//                       ],
//                     ),
//                     child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         Text(
//                           '操作记录',
//                           style: TextStyle(
//                             fontSize: 16,
//                             fontWeight: FontWeight.bold,
//                             color: Colors.black87,
//                           ),
//                         ),
//                         SizedBox(height: 12),
//                         // TODO: 这里可以添加操作记录列表
//                         Text(
//                           '暂无操作记录',
//                           style: TextStyle(
//                             fontSize: 14,
//                             color: Colors.grey[600],
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
          
//           // 底部按钮
//           if (interceptInfo['status'] == 'ACTIVE')
//             Container(
//               padding: EdgeInsets.all(16),
//               child: Row(
//                 children: [
//                   Expanded(
//                     child: ElevatedButton(
//                       onPressed: isLoading ? null : _showStatusDialog,
//                       style: ElevatedButton.styleFrom(
//                         backgroundColor: Theme.of(context).primaryColor,
//                         padding: EdgeInsets.symmetric(vertical: 12),
//                         shape: RoundedRectangleBorder(
//                           borderRadius: BorderRadius.circular(8),
//                         ),
//                       ),
//                       child: Text(
//                         isLoading ? '处理中...' : '更新状态',
//                         style: TextStyle(
//                           fontSize: 16,
//                           fontWeight: FontWeight.bold,
//                           color: Colors.white,
//                         ),
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//         ],
//       ),
//     );
//   }
// }