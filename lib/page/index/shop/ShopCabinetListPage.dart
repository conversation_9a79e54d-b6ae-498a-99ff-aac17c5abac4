import 'dart:async';

import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_shop_entity.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppListState.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/NoResult.dart';
import 'package:flutter/material.dart';

import '../../../generated/json/base/json_convert_content.dart';

class ShopCabinetListPage extends StatefulWidget {
  ShopCabinetListPage({Key? key}) : super(key: key);

  @override
  _ShopCabinetListPage createState() => _ShopCabinetListPage();
}

class _ShopCabinetListPage extends State<ShopCabinetListPage>
    with
        AutomaticKeepAliveClientMixin<ShopCabinetListPage>,
        AppListState<ShopCabinetListPage>,
        WidgetsBindingObserver,
        SingleTickerProviderStateMixin {
  List<CabinetShopEntity?> list = [];

  void initState() {
    handleRefresh();
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  // 上拉加载更多
  _getData({isRefresh = false}) async {
    LoadingUtil(
      status: '数据加载中...',
    ).show(context);
    var res = await CourierDao.shopCabinetLocation();
    LoadingUtil.dismiss(context);
    if (res != null && res.result) {
      list = jsonConvert.convertList<CabinetShopEntity>(res.data)!;
      return DataResult(res.data, true);
    }
  }

  _renderEventItem() {
    List<Widget> widgets = <Widget>[];
    list.forEach((item) {
      if (item != null) {
        // String? time = item.onlineTime ?? '';
        // if (time != '') {
        //   item.onlineTime = DateFormat('HH:mm').format(DateTime.parse(time));
        // }
        widgets.add(InkWell(
          onTap: () {
            NavigatorUtils.goDeliveryPage(context, item.code);
          },
          child: Container(
            height: 43,
            padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
            margin: EdgeInsets.only(bottom: 10),
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(5.0), color: Colors.white),
            child: Row(
              children: [
                Text(item.name ?? '',
                    style: TextStyle(
                        fontSize: AppConstant.smallTextSize, color: Colors.black, fontWeight: FontWeight.w600)),
                Expanded(
                    child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Icon(
                      Icons.arrow_forward_ios_sharp,
                      size: 18.0,
                      color: Color(0xFF999999),
                    )
                  ],
                ))
              ],
            ),
          ),
        ));
      }
    });
    return widgets;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      backgroundColor: Colors.grey.shade100,
      appBar: AppCustomerBar(
        title: AppbarTitle(
          title: '门店点位',
          isCenter: true,
        ),
        actions: <Widget>[Container(width: 60)],
      ),
      body: Stack(
        children: <Widget>[
          Column(
            children: <Widget>[
              Expanded(
                  child: SingleChildScrollView(
                physics: AlwaysScrollableScrollPhysics(),
                child: list.length > 0
                    ? Container(
                        width: MediaQuery.of(context).size.width,
                        padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
                        child: Column(
                          children: _renderEventItem(),
                        ),
                      )
                    : Container(
                        height: 300,
                        child: NoResult(
                            size: 64,
                            type: 'box',
                            subWidget: Container(
                                padding: EdgeInsets.only(top: 10), child: Text('', style: AppConstant.smallSubText))),
                      ),
              ))
            ],
          ),
        ],
      ),
    );
  }

  // TODO: implement wantKeepAlive
  @override
  bool get wantKeepAlive => true;

  // TODO: implement isRefreshFirst
  @override
  bool get isRefreshFirst => true;

  @override
  requestLoadMore() async {
    return await _getData();
  }

  //
  Future<Null> onFresh() async {
    _getData(isRefresh: true);
  }

  @override
  requestRefresh() async {
    return await _getData();
  }
}
