import 'package:flutter/material.dart';

import '../../../widget/AppCustomBar.dart';
import '../../../widget/AppbarTitle.dart';
import '../../../widget/NoResult.dart';

class UnbindCabinetPage extends StatefulWidget {
  const UnbindCabinetPage({Key? key}) : super(key: key);

  @override
  _UnbindCabinetPageState createState() => _UnbindCabinetPageState();
}

class _UnbindCabinetPageState extends State<UnbindCabinetPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Scaffold(
        backgroundColor: Colors.grey.shade100,
        appBar: AppCustomerBar(
          title: AppbarTitle(
            title: '派件入柜',
            isCenter: true,
          ),
          actions: <Widget>[Container(width: 60)],
        ),
        body: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.max,
          children: [
            Expanded(
              child: InkWell(
                child: Center(
                  child: new NoResult(),
                ),
                onTap: () {
                  // NavigatorUtils.goDeliveryPage(context);
                },
              ),
            ),
            InkWell(
              onTap: () {},
              child: Container(
                width: MediaQuery.of(context).size.width,
//                          height: 40.0,
                alignment: Alignment.center,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Text(
                      '如果您是管理员，请联系智能柜客服绑定店长账号\n即可操作入柜\n如果您已绑定店长账号，要新增团队成员，请至团队\n管理中新增账号',
                      // '${version ?? '欢迎使用熊猫管家'}',
                      style: TextStyle(color: Colors.grey, fontSize: 14.0),
                    ),
                  ],
                ),
              ),
            ),
            Padding(padding: EdgeInsets.only(top: 20.0)),
          ],
        ),
      ),
    );
  }
}
