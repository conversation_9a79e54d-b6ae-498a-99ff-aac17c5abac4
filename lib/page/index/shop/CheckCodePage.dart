
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_shop_entity.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:flutter/material.dart';

class CheckCodePage extends StatefulWidget {
  CheckCodePage({Key? key}) : super(key: key);

  @override
  _CheckCodePage createState() => _CheckCodePage();
}

class _CheckCodePage extends State<CheckCodePage> {
  TextEditingController codeEditingController = TextEditingController();

  List<CabinetShopEntity?> list = [];

  List<Widget> getWidgetList() {
    return list.map((item) => getItemContainer(item!)).toList();
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _getShelfList();
  }

  _getShelfList() async {
    DataResult res = await CourierDao.courierShopShelfList();
    if (res.result) {
      list = [];
      res.data.forEach((item) {
        list.add(CabinetShopEntity.fromJson(item));
      });
      setState(() {});
    }
  }

  _getShelfSave(name) async {
    LoadingUtil(
      status: '数据加载中...',
    ).show(context);
    DataResult res = await CourierDao.courierShopShelfSave(name);
    LoadingUtil.dismiss(context);
    if (res.result) {
      _getShelfList();
    }
  }

  _getShelfDelete(id) async {
    LoadingUtil(
      status: '数据加载中...',
    ).show(context);
    DataResult res = await CourierDao.courierShopShelfDelete(id);
    LoadingUtil.dismiss(context);
    if (res.result) {
      _getShelfList();
    }
  }

  /// 删除常用区域号
  deleteCode(CabinetShopEntity item) {
    CommonUtils.customConfirm(context, '是否删除此区域号？', title: '删除', showClose: false, actions: <Widget>[
      Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
            },
            child: Text('取消', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
            style: ButtonStyle(foregroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.black;
            }), backgroundColor: MaterialStateProperty.resolveWith((states) {
              return DefaultConfig().configs.WHITE_COLOR;
            }))),
        TextButton(
            onPressed: () async {
              _getShelfDelete(item.id);
              Navigator.of(context).pop();
            },
            child: Text('是的', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
            style: ButtonStyle(foregroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.black;
            }), backgroundColor: MaterialStateProperty.resolveWith((states) {
              return Colors.white;
            }))),
      ])
    ]);
  }

  Widget getItemContainer(CabinetShopEntity item) {
    return InkWell(
        onTap: () {
          Navigator.of(context).pop(item);
        },
        onLongPress: () {
          deleteCode(item);
        },
        child: Container(
          alignment: Alignment.center,
          width: 72,
          height: 27,
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(20)),
              border: Border.all(width: 1, color: Color(0xFFB5B5B5))),
          child: Text(
            item.name!,
            style: TextStyle(color: Colors.black, fontSize: 14),
          ),
        ));
  }

  @override
  Widget build(BuildContext context) {
    return new Material(
      child: new Scaffold(
          resizeToAvoidBottomInset: false,
          backgroundColor: Colors.grey.shade100,
          appBar: AppCustomerBar(
            title: AppbarTitle(
              title: '取件码设置',
              isCenter: true,
            ),
            actions: <Widget>[Container(width: 60)],
          ),
          body: Stack(
            children: [
              Container(
                  color: Colors.white,
                  margin: EdgeInsets.only(top: 20),
                  child: Container(
                    margin: EdgeInsets.only(left: 15, top: 15, right: 15),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '取件码规则（区域号+随机8位数字）',
                          style: TextStyle(fontSize: 15),
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(
                              child: Text('区域号设置'),
                              height: 50,
                              alignment: Alignment.center,
                            ),
                            Expanded(
                                child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Container(
                                  child: new TextField(
                                    controller: codeEditingController,
                                    style: TextStyle(
                                      fontSize: 15.0,
                                      color: Colors.black87,
                                    ),
                                    // autofocus: true,
                                    maxLength: 4,
                                    decoration: new InputDecoration(
                                        border: InputBorder.none,
                                        contentPadding: EdgeInsets.all(0.0),
                                        hintText: '最多输入4个字符',
                                        hintStyle: new TextStyle(fontSize: 15.0, color: Colors.grey.shade500)),

                                    onChanged: (name) {},
                                    onSubmitted: (name) async {},
                                  ),
                                  alignment: Alignment.center,
                                  height: 50,
                                  width: 150,
                                ),
                              ],
                            ))
                          ],
                        ),
                        Container(
                          height: 1,
                          color: Color(0xFFEEEEED),
                          margin: EdgeInsets.only(bottom: 15, right: 15),
                        ),
                        Text(
                          '常用区域号',
                          style: TextStyle(fontSize: 15),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        Expanded(
                            child: GridView.count(
                          //水平子Widget之间间距
                          crossAxisSpacing: 10.0,
                          //垂直子Widget之间间距
                          mainAxisSpacing: 30.0,
                          //GridView内边距
                          padding: EdgeInsets.all(10.0),
                          //一行的Widget数量
                          crossAxisCount: 4,
                          //子Widget宽高比例
                          childAspectRatio: 2.0,
                          //子Widget列表
                          children: getWidgetList(),
                        )),
                        Container(
                          width: double.infinity,
                          margin: EdgeInsets.only(top: 10, bottom: 10),
                          height: 47,
                          child: MaterialButton(
                            color: Theme.of(context).primaryColor,
                            textColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20.0),
                            ),
                            onPressed: () async {
                              if (codeEditingController.text != '') {
                                _getShelfSave(codeEditingController.text);
                              }
                            },
                            child: Text("确定"),
                          ),
                        ),
                      ],
                    ),
                  ))
            ],
          )),
    );
  }
}
