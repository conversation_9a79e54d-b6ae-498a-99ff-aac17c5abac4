import 'dart:async';
import 'dart:convert';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CabinetDao.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/dao/WaybillDao.dart';
import 'package:cabinet_flutter_app/common/entitys/package_view_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/shop_cabinet_location_entity.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/utils/ThrottleUtil.dart';
import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/BottomBtnWidget.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CommonRowWidget.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

class DeliveryPage extends StatefulWidget {
  final String cabinetLocationCode;
  final bool isDelivery;
  final String hostIndex;

  const DeliveryPage(this.cabinetLocationCode, this.isDelivery,
      {this.hostIndex = '', Key? key})
      : super(key: key);

  @override
  _DeliveryPageState createState() => _DeliveryPageState();
}

class _DeliveryPageState extends State<DeliveryPage> {
  ThrottleUtil throttleUtil = ThrottleUtil();
  double boxW = 80.0;
  double maxBoxH = 100.0;

  int? scIndex;
  int? swIndex;
  double minHeight = 340.0;
  double maxHeight = 340.0;
  ShopCabinetLocationEntity? cabinetInfo = new ShopCabinetLocationEntity();
  List<PackageViewEntity> keepInfo = [];
  PackageViewEntity? orderDetail = new PackageViewEntity();
  bool isShow = true;
  bool hasFeedBack = false;
  Timer? boxStatusTimer;
  bool isCourierUser = false; // 是否是快递员用户

  Map<int, String> messageTypeSelectMap = {1: '短信', 2: '微信优先', 3: '短信+微信'};
  Map<int, String> messageStateMap = {
    0: '无',
    1: '待投递',
    2: '处理中',
    3: '成功',
    4: '失败',
  };
  bool isCheckBox = true;
  bool isPda = false;
  bool showCabinetTips = false;

  /// 执行一键异常出库操作
  _performUnusualOutAll(CabinetItemEntity? cabinetItem) async {
    try {
      LoadingUtil(
        status: '正在处理异常出库...',
      ).show(context);

      // 获取当前柜机下所有有件的格口
      List<CabinetBoxItem> boxesWithPackages = [];

      if (cabinetItem?.boxList != null) {
        List<CabinetBoxItem>? boxList =
            cabinetItem!.boxList?.cast<CabinetBoxItem>();
        if (boxList != null) {
          for (var boxItem in boxList) {
            if (boxItem.orderId != null && boxItem.orderId!.isNotEmpty) {
              boxesWithPackages.add(boxItem);
            }
          }
        }
      }

      if (boxesWithPackages.isEmpty) {
        LoadingUtil.dismiss(context);
        Fluttertoast.showToast(msg: '当前柜机没有派件可异常出库！');
        return;
      }

      // 批量调用异常出库接口 - 添加延迟控制
      List<String> successBoxes = [];
      List<String> failedBoxes = [];

      for (int i = 0; i < boxesWithPackages.length; i++) {
        var boxItem = boxesWithPackages[i];
        try {
          var res = await CourierDao.courierUnusualOutWaybill(
              widget.cabinetLocationCode, boxItem.orderId!, '一键异常出库',
              hostIndex: widget.hostIndex.isNotEmpty ? widget.hostIndex : '1');

          if (res.result) {
            successBoxes.add(cabinetInfo?.cabinetNoType == 2
                ? boxItem.pcbNo?.toString() ?? ''
                : boxItem.boxLabel ?? boxItem.id ?? '');
          } else {
            failedBoxes.add(cabinetInfo?.cabinetNoType == 2
                ? boxItem.pcbNo?.toString() ?? ''
                : boxItem.boxLabel ?? boxItem.id ?? '');
          }

          // 添加延迟，避免并发问题优化判
          if (i < boxesWithPackages.length - 1) {
            await Future.delayed(Duration(milliseconds: 100));
          }
        } catch (e) {
          print('异常出库失败: ${boxItem.boxLabel}, 错误: $e');
          failedBoxes.add(cabinetInfo?.cabinetNoType == 2
              ? boxItem.pcbNo?.toString() ?? ''
              : boxItem.boxLabel ?? boxItem.id ?? '');
        }
      }

      LoadingUtil.dismiss(context);

      // 显示处理结果
      String resultMessage = '';
      if (successBoxes.isNotEmpty) {
        resultMessage += '成功异常出库格口: ${successBoxes.join(', ')}\n';
      }
      if (failedBoxes.isNotEmpty) {
        resultMessage += '异常出库失败格口: ${failedBoxes.join(', ')}';
      }

      Fluttertoast.showToast(
          msg: resultMessage.isNotEmpty ? resultMessage : '异常出库完成');

      // 刷新页面数据
      handleRefresh();
    } catch (e) {
      LoadingUtil.dismiss(context);
      print('一键异常出库异常: $e');
      Fluttertoast.showToast(msg: '一键异常出库失败: $e');
    }
  }

  // 如果有成功的异常出库，开始轮询打开格口
  // if (successBoxes.isNotEmpty) {
  //   await _batchOpenBoxes(boxesWithPackages.where((box) =>
  //     successBoxes.contains(box.boxLabel ?? box.id ?? '')).toList());
  // }

  // 批量打开格口
  // _batchOpenBoxes(List<CabinetBoxItem> boxes) async {
  //   if (boxes.isEmpty) return;
  //   LoadingUtil(
  //     status: '正在批量开门...',
  //   ).show(context);
  //   List<String> successBoxes = [];
  //   List<String> failedBoxes = [];
  //   for (var boxItem in boxes) {
  //     try {
  //       // 调用开门接口
  //       DataResult res = await CourierDao.boxOpenDoor(
  //           widget.cabinetLocationCode, boxItem.pcbNo ?? 0);
  //       if (res.result) {
  //         successBoxes.add(cabinetInfo?.cabinetNoType == 2
  //             ? boxItem.pcbNo?.toString() ?? ''
  //             : boxItem.boxLabel ?? boxItem.id ?? '');
  //         // 每次开门后稍微延迟，避免并发问题
  //         await Future.delayed(Duration(milliseconds: 500));
  //       } else {
  //         failedBoxes.add(cabinetInfo?.cabinetNoType == 2
  //             ? boxItem.pcbNo?.toString() ?? ''
  //             : boxItem.boxLabel ?? boxItem.id ?? '');
  //       }
  //     } catch (e) {
  //       print('开门失败: ${boxItem.boxLabel}, 错误: $e');
  //       failedBoxes.add(cabinetInfo?.cabinetNoType == 2
  //           ? boxItem.pcbNo?.toString() ?? ''
  //           : boxItem.boxLabel ?? boxItem.id ?? '');
  //     }
  //   }
  //   LoadingUtil.dismiss(context);
  //   // 显示开门结果
  //   String resultMessage = '';
  //   if (successBoxes.isNotEmpty) {
  //     resultMessage += '成功开门格口: ${successBoxes.join(', ')}\n';
  //   }
  //   if (failedBoxes.isNotEmpty) {
  //     resultMessage += '开门失败格口: ${failedBoxes.join(', ')}';
  //   }
  //   Fluttertoast.showToast(
  //       msg: resultMessage.isNotEmpty ? resultMessage : '批量开门完成');
  // }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    scIndex = -1;
    swIndex = -1;
    checkIsPda();
    // checkUserType();
    this.handleRefresh();
  }

  // // 检查用户类型
  // checkUserType() {
  //   isCourierUser = CheckUtils.isCourier(context);
  //   setState(() {});
  // }

  // @override
  // void didChangeDependencies() async {
  //   super.didChangeDependencies();
  //   isCourierUser = CheckUtils.isCourier(context);
  //   print('isCourierUser: ${isCourierUser}');
  // }

  checkIsPda() async {
    isPda = await CheckUtils.isPda();
  }

  @override
  void dispose() {
    super.dispose();
    boxStatusTimer?.cancel();
  }

  getCabinetInfo({bool isRefresh = false}) async {
    LoadingUtil(
      status: isRefresh ? '刷新格口状态，请稍后...' : '数据获取中...',
    ).show(context);
    cabinetInfo = new ShopCabinetLocationEntity();
    var res = await CabinetDao.getCabinetDetail(widget.cabinetLocationCode);
    LoadingUtil.dismiss(context);
    if (res != null && res.result) {
      cabinetInfo = ShopCabinetLocationEntity.fromJson(res.data);
      // 设置网点是否开启追加入柜
      await LocalStorage.save(DefaultConfig().configs.HAS_SITE_OPEN_ADD_CABINET,
          cabinetInfo?.hasAddCabinet ?? 0,
          isPrivate: true);
      await LocalStorage.save(DefaultConfig().configs.HAS_CHANGE_BRAND,
          cabinetInfo?.hasChangeBrand ?? 1,
          isPrivate: true);
      if (isRefresh) {
        isShow = true;
      }
    }
    setState(() {});
  }

  _getOderDetail(String cabinetBoxId, int index, int index2) async {
    LoadingUtil(
      status: '格口请求中...',
    ).show(context);
    DataResult res = await CabinetDao.getCourierOderDetail(
        widget.cabinetLocationCode, cabinetBoxId);
    LoadingUtil.dismiss(context);
    if (res.result) {
      orderDetail = JsonConvert.fromJsonAsT(res.data);
      setState(() {
        // scIndex = index;
        // swIndex = index2;
        isShow = false;
      });
    }
  }

  /// 手机号修改按钮 重新通知按钮状态
  /// 若已取出 按钮为false 禁用状态
  phoneEditBtnStatus() {
    return orderDetail?.hasOutbound == 0;
  }

  /// 重新通知
  /// @param [receiverMobile]  手机号
  /// @param [isForce]  是否显示弹窗确认 从修改手机号勾选的发送则直接发送 否则弹窗提示
  reNotice(String receiverMobile, {bool isForce = false}) {
    WaybillDao.reNotice(context, receiverMobile, orderDetail!.id,
        isForce: isForce);
  }

  /// 获取通知状态
  getNoticeStatus(String type) {
    if (type == 'sms') {
      return orderDetail?.messageSmsStatus == null
          ? '无'
          : messageStateMap[orderDetail?.messageSmsStatus] ?? '无';
    } else {
      return orderDetail?.messageWxStatus == null
          ? '无'
          : messageStateMap[orderDetail?.messageWxStatus] ?? '无';
    }
  }

  /// 时间格式化
  getTimeFormat(String time) {
    if (CheckUtils.isNotNull(time)) {
      return DateFormat('yyyy-MM-dd HH:mm').format(DateTime.parse(time));
    }
    return null;
  }

  /// 取出
  pickOut(PackageViewEntity item) {
    CommonUtils.customConfirm(context, '是否打开柜门取出包裹？',
        title: '开门提示',
        showClose: false,
        actions: <Widget>[
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                },
                child: Text('取消',
                    style:
                        TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
                style: ButtonStyle(foregroundColor:
                    MaterialStateProperty.resolveWith((states) {
                  return Colors.black;
                }), backgroundColor:
                    MaterialStateProperty.resolveWith((states) {
                  return DefaultConfig().configs.WHITE_COLOR;
                }))),
            TextButton(
                onPressed: () async {
                  var res = await CourierDao.courierOutboundWaybill(
                      item.cabinetLocationCode, item.id);
                  if (res.result) {
                    Navigator.of(context)..pop();
                    isShow = true;
                    handleRefresh();
                    Fluttertoast.showToast(msg: '取出成功');
                  }
                },
                child: Text('是的',
                    style:
                        TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
                style: ButtonStyle(foregroundColor:
                    MaterialStateProperty.resolveWith((states) {
                  return Colors.black;
                }), backgroundColor:
                    MaterialStateProperty.resolveWith((states) {
                  return Colors.white;
                }))),
          ])
        ]);
  }

  /// 开门检查
  openCabinetCheck(PackageViewEntity item) {
    CommonUtils.customConfirm(context, '是否打开柜门检查包裹？',
        title: '开门检查',
        showClose: false,
        actions: <Widget>[
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                },
                child: Text('取消',
                    style:
                        TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
                style: ButtonStyle(foregroundColor:
                    MaterialStateProperty.resolveWith((states) {
                  return Colors.black;
                }), backgroundColor:
                    MaterialStateProperty.resolveWith((states) {
                  return DefaultConfig().configs.WHITE_COLOR;
                }))),
            TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                  LoadingUtil(
                    status: '正在开门...',
                  ).show(context);
                  DataResult res =
                      await CourierDao.courierBoxOpenCheck(item.cabinetBoxId);
                  LoadingUtil.dismiss(context);
                  if (res.result) {
                    showBoxOpen(context, item, (item) {});
                    // boxStatusTimer = Timer.periodic(Duration(milliseconds: 2000), (timer) async {
                    //   var result = await CabinetDao.cabinetBoxOpenStatus(item.cabinetBoxId!);
                    //   if (result != null && result.result) {
                    //     if (!result.data) {
                    //       if (!isCheckBox) {
                    //         return;
                    //       }
                    //       boxStatusTimer?.cancel();
                    //       Navigator.of(context).pop();
                    //     }
                    //   }
                    // });
                  }
                },
                child: Text('是的',
                    style:
                        TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
                style: ButtonStyle(foregroundColor:
                    MaterialStateProperty.resolveWith((states) {
                  return Colors.black;
                }), backgroundColor:
                    MaterialStateProperty.resolveWith((states) {
                  return Colors.white;
                }))),
          ])
        ]);
  }

  /// 一键开门
  openDoor(int? serialNo) {
    CommonUtils.customConfirm(context, '是否一键开门？',
        title: '一键开门',
        showClose: false,
        actions: <Widget>[
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                },
                child: Text('取消',
                    style:
                        TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
                style: ButtonStyle(foregroundColor:
                    MaterialStateProperty.resolveWith((states) {
                  return Colors.black;
                }), backgroundColor:
                    MaterialStateProperty.resolveWith((states) {
                  return DefaultConfig().configs.WHITE_COLOR;
                }))),
            TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                  LoadingUtil(
                    status: '正在开门...',
                  ).show(context);
                  DataResult res = await CourierDao.boxOpenDoor(
                      cabinetInfo!.code ?? '', serialNo ?? 1);
                  LoadingUtil.dismiss(context);
                  if (res.result) {
                    Fluttertoast.showToast(msg: '开门成功');
                  }
                },
                child: Text('是的',
                    style:
                        TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
                style: ButtonStyle(foregroundColor:
                    MaterialStateProperty.resolveWith((states) {
                  return Colors.black;
                }), backgroundColor:
                    MaterialStateProperty.resolveWith((states) {
                  return Colors.white;
                }))),
          ])
        ]);
  }

  /// 一键异常出库
  unusualOutAll(CabinetItemEntity? cabinetItem) {
    print('一键异常出库');
    // 这里实现一键异常出库的具体逻辑
    // 例如遍历该柜机下的所有包裹，调用异常出库方法
    CommonUtils.customConfirm(context, '是否一键异常出库？',
        title: '一键异常出库',
        showClose: false,
        actions: <Widget>[
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                },
                child: Text('取消',
                    style:
                        TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
                style: ButtonStyle(
                    foregroundColor: WidgetStateProperty.resolveWith((states) {
                  return Colors.black;
                }), backgroundColor: WidgetStateProperty.resolveWith((states) {
                  return DefaultConfig().configs.WHITE_COLOR;
                }))),
            TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                  await _performUnusualOutAll(cabinetItem);
                },
                child: Text('是的',
                    style:
                        TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
                style: ButtonStyle(
                    foregroundColor: WidgetStateProperty.resolveWith((states) {
                  return Colors.black;
                }), backgroundColor: WidgetStateProperty.resolveWith((states) {
                  return Colors.white;
                }))),
          ])
        ]);
  }

  /// 格口打开
  showBoxOpen(BuildContext context, PackageViewEntity item, onPressed(item)) {
    return showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
        topLeft: Radius.circular(10.0),
        topRight: Radius.circular(10.0),
      )),
      builder: (BuildContext _context) {
        return StatefulBuilder(
          builder: (_context, state) {
            return Stack(
              children: [
                Container(
                  height: 30.0,
                  width: double.infinity,
                  color: Colors.black54,
                ),
                Container(
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      )),
                ),
                Container(
                  height: 550,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            LocalImageUtil.getImageAsset('boxOpen',
                                width: MediaQuery.of(context).size.width * 0.3),
                            Padding(padding: EdgeInsets.only(bottom: 10)),
                            RichText(
                                text: TextSpan(children: <TextSpan>[
                              TextSpan(
                                text: '${item.cabinetName ?? ''}-',
                                style: TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontSize: 16),
                              ),
                              TextSpan(
                                text: '${item.cabinetBoxLabel}号',
                                style: TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontSize: 16),
                              ),
                              TextSpan(
                                text: ' 格口已开 ',
                                style: TextStyle(
                                    color: Colors.black, fontSize: 14),
                              ),
                            ])),
                            Padding(padding: EdgeInsets.only(bottom: 10)),
                            Offstage(
                              offstage: hasFeedBack,
                              child: Text('放入包裹后请关门',
                                  style: TextStyle(
                                      color: Color(0xFF999999), fontSize: 14)),
                            )
                          ],
                        ),
                      ),
                      Offstage(
                          offstage: hasFeedBack,
                          child: BottomBtnWidget(
                              showShadow: false,
                              title: '我已关门',
                              action: () {
                                closeCabinetBox(item.cabinetBoxId!);
                              }))
                    ],
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// 异常出库
  unusualOut(PackageViewEntity item) {
    CommonUtils.customConfirmByReason(context, '是否进行异常出库操作？',
        (backReason) async {
      Navigator.of(context).pop();
      var cabinetLocationCode =
          item.storeType == 1 ? item.cabinetLocationCode : '0';
      EasyLoading.show(status: '正在取出...', maskType: EasyLoadingMaskType.black);
      var res = await CourierDao.courierUnusualOutWaybill(
          cabinetLocationCode, item.id, backReason);
      EasyLoading.dismiss();
      if (res.result) {
        isShow = true;
        handleRefresh();
        Fluttertoast.showToast(msg: '出库成功');
      }
    },
        title: '出库提示',
        changeText: 'unusualOut',
        showClose: false,
        showInput: true);
  }

  closeCabinetBox(String cabinetBoxId) async {
    LoadingUtil(
      status: '关门请求中...',
    ).show(context);
    var res = await CabinetDao.cabinetBoxCloseDoor(cabinetBoxId);
    LoadingUtil.dismiss(context);
    if (res.result) {
      isCheckBox = false;
      boxStatusTimer?.cancel();
      Navigator.pop(context);
    }
  }

  geMessageType() {
    return orderDetail?.messageType != null
        ? messageTypeSelectMap[orderDetail?.messageType]
        : '未知';
  }

  Future<Null> handleRefresh() async {
    new Future.delayed(const Duration(microseconds: 200), () {
      this.getCabinetInfo();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.grey.shade100,
        appBar: AppCustomerBar(
          title: AppbarTitle(
            title: '派件入柜${widget.isDelivery ? '-仅入库' : ''}',
            isCenter: true,
          ),
          actions: <Widget>[
            IconButton(
                onPressed: () => throttleUtil.throttle(() {
                      getCabinetInfo(isRefresh: true);
                    }),
                icon: Icon(Icons.refresh, size: 28),
                color: Colors.black),
            IconButton(
              onPressed: () {
                setState(() {
                  showCabinetTips = !showCabinetTips;
                });
              },
              icon: Icon(Icons.help_outline, size: 26),
              color: Colors.black,
              tooltip: '格口说明',
            ),
          ],
        ),
        body: Stack(
          children: [
            Offstage(
              offstage: !showCabinetTips,
              child: Container(
                padding: EdgeInsets.only(left: 15, right: 15),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          '格口状态说明:',
                          style: TextStyle(fontSize: 12),
                        ),
                        Padding(padding: EdgeInsets.only(left: 10)),
                        Expanded(
                            child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                                decoration: BoxDecoration(
                                  border: Border.all(
                                      color: Color.fromRGBO(255, 237, 230, 10),
                                      width: 1),
                                ),
                                child: Text(
                                  '空格口',
                                  style: TextStyle(fontSize: 12),
                                ),
                                padding: EdgeInsets.only(left: 7, right: 7)),
                            Container(
                                decoration: BoxDecoration(
                                    border: Border.all(
                                        color: Colors.lightGreenAccent.shade100,
                                        width: 1),
                                    color: Colors.lightGreenAccent.shade100),
                                child: Text(
                                  '待取包裹',
                                  style: TextStyle(fontSize: 12),
                                ),
                                padding: EdgeInsets.only(left: 7, right: 7)),
                            Container(
                                decoration: BoxDecoration(
                                    border: Border.all(
                                        color: Colors.redAccent.shade100,
                                        width: 1),
                                    color: Colors.redAccent.shade100),
                                child: Text(
                                  '滞留包裹',
                                  style: TextStyle(fontSize: 12),
                                ),
                                padding: EdgeInsets.only(left: 7, right: 7)),
                            Container(
                                decoration: BoxDecoration(
                                    border:
                                        Border.all(color: Colors.red, width: 1),
                                    color: Colors.red),
                                child: Text(
                                  '不可用',
                                  style: TextStyle(fontSize: 12),
                                ),
                                padding: EdgeInsets.only(left: 7, right: 7)),
                          ],
                        ))
                      ],
                    ),
                    SizedBox(
                      height: 2,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          '格口图标说明:',
                          style: TextStyle(fontSize: 12),
                        ),
                        Padding(padding: EdgeInsets.only(left: 10)),
                        Expanded(
                            child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.notification_important,
                                  size: 20,
                                  color: Colors.yellow,
                                ),
                                Container(
                                  child: Text(
                                    '通知失败',
                                    style: TextStyle(fontSize: 12),
                                  ),
                                ),
                              ],
                            ),
                            Row(
                              children: [
                                Icon(Icons.https, size: 20),
                                Container(
                                  child: Text(
                                    '格口锁定',
                                    style: TextStyle(fontSize: 12),
                                  ),
                                ),
                              ],
                            ),
                            Row(
                              children: [
                                Icon(
                                  Icons.warning_rounded,
                                  size: 20,
                                  color: Theme.of(context).primaryColor,
                                ),
                                Container(
                                  child: Text(
                                    '格口故障',
                                    style: TextStyle(fontSize: 12),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ))
                      ],
                    ),
                    SizedBox(
                      height: 5,
                    ),
                  ],
                ),
                color: DefaultConfig().configs.WHITE_COLOR,
              ),
            ),
            FutureBuilder(
              future:
                  DefaultAssetBundle.of(context).loadString("assets/data.json"),
              builder: (context, snap) {
                if (snap.connectionState == ConnectionState.done) {
                  return ListView.builder(
                    padding: EdgeInsets.only(left: 10, right: 10, bottom: 20),
                    scrollDirection: Axis.horizontal,
                    itemCount: cabinetInfo?.cabinetList?.length ?? 0,
                    itemBuilder: (context, index) {
                      CabinetItemEntity? cabinetItem =
                          cabinetInfo?.cabinetList![index];
                      return buildCabinetItem(cabinetItem, index);
                    },
                  );
                } else {
                  return CircularProgressIndicator();
                }
              },
            ),
            Offstage(
              offstage: isShow,
              child: SlidingUpPanel(
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16)),
                minHeight: minHeight,
                maxHeight: maxHeight,
                panel: Center(child: _buildGridInfo()),
                body: Container(
                  child: InkWell(
                    onTap: () {
                      setState(() {
                        isShow = !isShow;
                      });
                    },
                    child: Container(
                      color: Color(0x72000000),
                    ),
                  ),
                ),
              ),
            )
          ],
        ));
  }

  Container buildCabinetItem(CabinetItemEntity? cabinetItem, int hostIndex) =>
      Container(
        width: json.decode(cabinetItem!.columns!).length * 112.0,
        margin: EdgeInsets.only(top: showCabinetTips ? 50 : 10),
        // padding: EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Container(
          // padding: EdgeInsets.symmetric(vertical: 5),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: Colors.orange,
              width: 1,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Container(
                height: 38, //38
                padding: EdgeInsets.symmetric(horizontal: 5),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      width: 1,
                      color: Colors.orange,
                    ),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "${cabinetItem.name}",
                      // "${listInfo.code}号柜",
                      style: TextStyle(
                        fontSize: 15,
                        color: Colors.black,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    PopupMenuButton<String>(
                      icon: Icon(
                        Icons.menu,
                        color: Colors.orange,
                        size: 24,
                      ),
                      onSelected: (String value) {
                        if (value == 'openDoor') {
                          openDoor(cabinetItem.serialNo);
                        } else if (value == 'unusualOut') {
                          unusualOutAll(cabinetItem);
                        }
                      },
                      itemBuilder: (BuildContext context) =>
                          <PopupMenuEntry<String>>[
                        PopupMenuItem<String>(
                          value: 'openDoor',
                          child: Row(
                            children: [
                              Icon(Icons.lock_open,
                                  color: Colors.orange, size: 20),
                              SizedBox(width: 8),
                              Text(
                                '一键开门',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.black87,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                        PopupMenuItem<String>(
                          value: 'unusualOut',
                          child: Row(
                            children: [
                              Icon(Icons.output,
                                  color: Colors.orange, size: 20),
                              SizedBox(width: 8),
                              Text(
                                '一键异常出库',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.black87,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    )
                  ],
                ),
              ),
              Expanded(
                child: cabinetItem.type == 1
                    ? buildHostCabinet(cabinetItem, hostIndex)
                    : buildDeputyCabinet(cabinetItem, hostIndex),
              ),
            ],
          ),
        ),
      );

  /// 主柜
  Widget buildHostCabinet(CabinetItemEntity? cabinetItem, int hostIndex) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: EdgeInsets.only(top: 5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: List.generate(
          json.decode(cabinetItem!.columns!)?.length ?? 0,
          (index) {
            final String? hostBoxArrayString = cabinetItem.hostBoxArray;
            final int horizontalLength =
                json.decode(cabinetItem.columns!).length;
            final String? columnsString =
                json.decode(cabinetItem.columns!)[index];
            final List<String> columns = columnsString!.split(",");
            final int verticaLength = columns.length;
            final List<String> hostBoxArray = hostBoxArrayString!.contains(",")
                ? hostBoxArrayString.split(",")
                : [hostBoxArrayString];
            List<CabinetBoxItem>? boxList =
                cabinetItem.boxList?.cast<CabinetBoxItem>();
            final int startIndex = int.parse(hostBoxArray.first) - 1;
            final int endIndex = int.parse(hostBoxArray.last) - 1;

            return SingleChildScrollView(
              scrollDirection: Axis.vertical,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: List.generate(
                  columns.length,
                  (index) {
                    final int cIndex = int.parse(columns[index]) - 1;
                    if (cIndex < startIndex) {
                      return buildItem(
                          boxList![cIndex],
                          index,
                          cIndex,
                          cabinetItem,
                          horizontalLength,
                          verticaLength,
                          hostIndex);
                    } else if (cIndex == startIndex) {
                      return buildDisplay(hostBoxArray.length, columns.length,
                          hostBoxArray.length);
                    } else if (cIndex <= endIndex) {
                      return Container();
                    } else {
                      return buildItem(
                          (cIndex - hostBoxArray.length < boxList!.length)
                              ? boxList[cIndex - hostBoxArray.length]
                              : boxList[boxList.length - 1],
                          index,
                          cIndex - hostBoxArray.length,
                          cabinetItem,
                          horizontalLength,
                          verticaLength,
                          hostIndex);
                    }
                  },
                ),
              ),
            );
          },
        ),
        // ),
      ),
    );
  }

  /// 副柜
  Widget buildDeputyCabinet(CabinetItemEntity? cabinetItem, int hostIndex) {
    return SingleChildScrollView(
      scrollDirection: json.decode(cabinetItem!.columns!)?.length > 2
          ? Axis.vertical
          : Axis.vertical,
      padding: EdgeInsets.only(top: 5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: List.generate(
          json.decode(cabinetItem.columns!)?.length ?? 0,
          (index) {
            final int horizontalLength =
                json.decode(cabinetItem.columns!).length;
            final String? columnsString =
                json.decode(cabinetItem.columns!)[index];
            final List<String> columns = columnsString!.split(",");
            List<CabinetBoxItem>? boxList =
                cabinetItem.boxList?.cast<CabinetBoxItem>();
            List<CabinetBoxItem> cBoxList = [];
            columns.forEach((item) {
              final int index = int.parse(item) - 1;
              cBoxList.add(boxList![index]);
            });
            return buildColumn(
                cBoxList, index, horizontalLength, cabinetItem, hostIndex);
          },
        ),
      ),
      // ),
    );
  }

  Widget buildColumn(
          List<CabinetBoxItem> cBoxList,
          int scIndex,
          int horizontalLength,
          CabinetItemEntity? cabinetItem,
          int hostIndex) =>
      SingleChildScrollView(
        scrollDirection: Axis.vertical,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: List.generate(
            cBoxList.length,
            (index) => buildItem(cBoxList[index], scIndex, index, cabinetItem,
                horizontalLength, cBoxList.length, hostIndex),
          ),
        ),
      );

  Widget buildDisplay(int count, int length, int hostLength) => InkWell(
        child: Container(
          width: 110,
          // height: length < 9 ? 76 : 30.0 * count + (count - 1) * 10.0,
          height: ((MediaQuery.of(context).size.height - 175) / length) *
              hostLength,
          decoration: BoxDecoration(
            color: Color(0xFFFFEEE8),
            borderRadius: BorderRadius.circular(0),
            border: Border.all(
              width: 0.5,
              color: Color.fromRGBO(245, 245, 245, 1),
            ),
          ),
          child: LocalImageUtil.getImageAsset('cabinet_monitor'),
        ),
        onTap: () {
          Fluttertoast.showToast(msg: '柜机显示屏', gravity: ToastGravity.CENTER);
        },
      );

  Widget buildItem(
      CabinetBoxItem boxItem,
      int index,
      int index1,
      CabinetItemEntity? cabinetItem,
      int horizontalLength,
      int verticaLength,
      int hostIndex) {
    // 假设 boxItem.inboundTime 是 13 位时间戳
    String? stayTimeText;
    if (boxItem.inboundTime != null && boxItem.inboundTime!.isNotEmpty) {
      try {
        // 将 13 位时间戳转换为毫秒数，再创建 DateTime 对象
        final inbound = DateTime.fromMillisecondsSinceEpoch(
            int.parse(boxItem.inboundTime!));
        final now = DateTime.now();
        final diff = now.difference(inbound);
        if (diff.inDays > 0) {
          stayTimeText = '${diff.inDays}天';
        } else if (diff.inHours > 0) {
          stayTimeText = '${diff.inHours}小时';
        } else if (diff.inMinutes > 0) {
          stayTimeText = '${diff.inMinutes}分钟';
        } else {
          stayTimeText = '刚刚';
        }
      } catch (e) {
        // 处理解析时间戳时可能出现的异常
        print('解析时间戳出错: $e');
        stayTimeText = '时间未知';
      }
    }
    // print("verticaLength: $verticaLength, index: $index, index1: $index1 ,cabinetItem: ${cabinetItem?.name}, boxItem: ${boxItem.boxLabel}");
    return GestureDetector(
        onTap: () {
          int workStatus = boxItem.workStatus!;
          if (boxItem.status != 1) {
            Fluttertoast.showToast(msg: '格口不可用', gravity: ToastGravity.CENTER);
            return;
          }
          if (boxItem.specialStatus == 5) {
            Fluttertoast.showToast(msg: '格口故障', gravity: ToastGravity.CENTER);
            return;
          }
          if (workStatus == 1) {
            PackageViewEntity item = new PackageViewEntity();
            item.cabinetBoxId = boxItem.id;
            item.cabinetBoxLabel = cabinetInfo?.cabinetNoType == 2
                ? boxItem.pcbNo?.toString()
                : boxItem.boxLabel;
            item.cabinetName = cabinetItem!.name;
            openCabinetCheck(item);
          } else {
            _getOderDetail(boxItem.id!, index, index1);
          }
        },
        onDoubleTap: () async {
          int workStatus = boxItem.workStatus!;
          if (boxItem.status != 1) {
            Fluttertoast.showToast(msg: '格口不可用', gravity: ToastGravity.CENTER);
            return;
          }
          if (boxItem.specialStatus == 5) {
            Fluttertoast.showToast(msg: '格口故障', gravity: ToastGravity.CENTER);
            return;
          }
          if (workStatus == 1) {
            setState(() {
              isShow = true;
            });
            bool flag = true;
            if (widget.isDelivery) {
              flag = await CommonUtils.confirm(
                  context, '“仅入库”模式将不回传物流信息至快递公司，请先【签收】包裹后再入柜');
            }
            if (cabinetInfo?.cabinetNoType == 2) {
              boxItem.boxLabel = '${cabinetItem?.name}-${boxItem.pcbNo}';
            }
            if (isPda) {
              if (flag)
                await NavigatorUtils.goEntryScanCabinetPda(
                        context,
                        cabinetItem!,
                        widget.cabinetLocationCode,
                        boxItem,
                        widget.isDelivery,
                        hostIndex: '${hostIndex}')
                    .then((res) => {getCabinetInfo()});
            } else {
              if (flag)
                await NavigatorUtils.goEntryScanCabinet(context, cabinetItem!,
                        widget.cabinetLocationCode, boxItem, widget.isDelivery,
                        hostIndex: '${hostIndex}')
                    .then((res) => {getCabinetInfo()});
            }
          } else {
            _getOderDetail(boxItem.id!, index, index1);
          }
        },
        child: Container(
          width: 110,
          height: (MediaQuery.of(context).size.height - 175) /
              verticaLength, // 计算每个格口的高度之前是/verticaLength    10
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: boxItem.status == 1 //可用不可用
                ? boxItem.hasKeepEffect == 1
                    ? DefaultConfig().configs.KEEP_EFFECT_COLOR
                    : DefaultConfig()
                        .configs
                        .CABINET_BOX_WORK_STATUS_COLOR[boxItem.workStatus]
                : Colors.red, //其它类型之外
            borderRadius: BorderRadius.circular(0),
            border: Border.all(
              width: 0.5,
              color: Color.fromRGBO(255, 237, 230, 10),
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 3),
                    child: Text(
                      "${cabinetInfo?.cabinetNoType == 2 ? boxItem.pcbNo : boxItem.boxLabel}",
                      style: TextStyle(fontSize: 16, color: Colors.black),
                    ),
                  ),
                  Text(
                    DefaultConfig().configs.CABINET_BOX_TYPE2[boxItem.type] ??
                        '',
                    style: TextStyle(fontSize: 14, color: Colors.black),
                  ),
                  if (stayTimeText != null)
                    Text(
                      '$stayTimeText',
                      style: TextStyle(fontSize: 12, color: Colors.redAccent),
                    ),
                  Offstage(
                    offstage: boxItem.specialStatus == 5
                        ? false
                        : boxItem.specialStatus == 4
                            ? false
                            : boxItem.hasSmsError == 1
                                ? false
                                : true,
                    // offstage: false,
                    child: Icon(
                      boxItem.hasSmsError == 1
                          ? Icons.notification_important
                          : DefaultConfig()
                                      .configs
                                      .CABINET_BOX_SPECIAL_STATUS_ICON[
                                  boxItem.specialStatus] ??
                              null,
                      size: 20,
                      color: boxItem.specialStatus == 5
                          ? Theme.of(context).primaryColor
                          : boxItem.hasSmsError == 1
                              ? Colors.yellow
                              : Colors.black,
                    ),
                  )
                ],
              ),
              // Offstage(
              //   offstage: stayTimeText == null,
              //   child: Row(
              //   mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              //   children: [
              //     Text(
              //         '$stayTimeText',
              //         style: TextStyle(fontSize: 12, color: Colors.redAccent),
              //       ),
              //   ],
              // )
              // )
            ],
          ),
        ));
  }

  Widget _buildGridInfo() {
    return InkWell(
      onTap: () {},
      child: Container(
        margin: EdgeInsets.only(left: 15, top: 13, right: 15),
        child: Column(
          children: [
            Row(
              children: [
                Text(
                  '格口：${orderDetail?.cabinetBoxLabel}号-${DefaultConfig().configs.ORDER_TYPE[orderDetail?.orderType]}',
                  style: TextStyle(
                      fontSize: 19,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFFFF5E24)),
                ),
                Expanded(
                    child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                      margin: EdgeInsets.only(top: 5),
                      child: InkWell(
                          onTap: () {
                            setState(() {
                              // scIndex = index;
                              // swIndex = index1;
                              isShow = true;
                            });
                          },
                          child: Text(
                            '关闭',
                            style: TextStyle(
                                fontSize: 18,
                                color: Theme.of(context).primaryColor),
                          )),
                    )
                  ],
                ))
              ],
            ),
            Offstage(
                offstage: orderDetail?.orderType == 2,
                child: Column(
                  children: [
                    CommonRowWidget(
                        labelFontSize: 15,
                        valueFontSize: 15,
                        label: '快递品牌',
                        value: orderDetail?.brandName ?? '暂无',
                        valueColor: DefaultConfig().configs.DISABLED_COLOR),
                    CommonRowWidget(
                        labelFontSize: 15,
                        valueFontSize: 15,
                        label: '单号',
                        value: orderDetail?.waybillNo ?? '暂无',
                        valueColor: DefaultConfig().configs.DISABLED_COLOR)
                  ],
                )),
            Offstage(
              offstage: orderDetail?.orderType != 2,
              child: Column(
                children: [
                  CommonRowWidget(
                      labelFontSize: 15,
                      valueFontSize: 15,
                      label: '存件人',
                      value: orderDetail?.inboundUserName,
                      valueColor: DefaultConfig().configs.DISABLED_COLOR),
                  CommonRowWidget(
                      labelFontSize: 15,
                      valueFontSize: 15,
                      label: '手机号',
                      value: orderDetail?.inboundUserMobile,
                      valueColor: DefaultConfig().configs.DISABLED_COLOR)
                ],
              ),
            ),
            Offstage(
              offstage: orderDetail?.orderType == 2,
              child: Column(
                children: [
                  Offstage(
                    offstage: orderDetail?.orderType != 3,
                    child: CommonRowWidget(
                      labelFontSize: 15,
                      valueFontSize: 15,
                      label: '寄件人',
                      valueColor: Color(0xFF0066FF),
                      rightWidget: InkWell(
                          onTap: () async {
                            await CommonUtils.makePhoneCall(
                                context, orderDetail?.inboundUserMobile);
                          },
                          child: Row(
                            children: [
                              Icon(Icons.phone_in_talk,
                                  size: 16,
                                  color: DefaultConfig().configs.BLUE_COLOR),
                              Text(orderDetail?.inboundUserMobile ?? '',
                                  style: TextStyle(
                                      fontSize: AppConstant.smallTextSize,
                                      color:
                                          DefaultConfig().configs.BLUE_COLOR)),
                            ],
                          )),
                    ),
                  ),
                  CommonRowWidget(
                    labelFontSize: 15,
                    valueFontSize: 15,
                    label: '收件人',
                    valueColor: Color(0xFF0066FF),
                    rightWidget: InkWell(
                        onTap: () async {
                          await CommonUtils.makePhoneCall(
                              context, orderDetail?.receiverMobile);
                        },
                        child: Row(
                          children: [
                            Icon(Icons.phone_in_talk,
                                size: 16,
                                color: DefaultConfig().configs.BLUE_COLOR),
                            Text(orderDetail?.receiverMobile ?? '',
                                style: TextStyle(
                                    fontSize: AppConstant.smallTextSize,
                                    color: DefaultConfig().configs.BLUE_COLOR)),
                          ],
                        )),
                  ),
                ],
              ),
            ),
            CommonRowWidget(
              labelFontSize: 15,
              valueFontSize: 15,
              leftWidget: Row(
                children: [
                  Text('通知方式',
                      style: TextStyle(
                          fontSize: 15,
                          color: DefaultConfig().configs.GREY_COLOR)),
                ],
              ),
              rightWidget: InkWell(
                  onTap: () async {},
                  child: Row(
                    children: [
                      Text(geMessageType(),
                          style: TextStyle(
                              fontSize: AppConstant.smallTextSize,
                              color: DefaultConfig().configs.DISABLED_COLOR)),
                    ],
                  )),
            ),
            CommonRowWidget(
              labelFontSize: 15,
              valueFontSize: 15,
              leftWidget: Row(
                children: [
                  Text('短信通知',
                      style: TextStyle(
                          fontSize: 15,
                          color: DefaultConfig().configs.GREY_COLOR)),
                  Offstage(
                    offstage: false,
                    child: InkWell(
                      onTap: () {
                        if (phoneEditBtnStatus()) {
                          reNotice(orderDetail!.receiverMobile);
                        } else {
                          Fluttertoast.showToast(msg: '包裹已取出');
                        }
                      },
                      child: Row(
                        children: [
                          Padding(padding: EdgeInsets.only(left: 5)),
                          Image.asset(
                            LocalImageUtil.getImagePath('again'),
                            width: 15,
                          ),
                          Padding(padding: EdgeInsets.only(left: 5)),
                          Text('重新通知',
                              style: TextStyle(
                                  fontSize: AppConstant.smallTextSize,
                                  color: phoneEditBtnStatus()
                                      ? Theme.of(context).primaryColor
                                      : Colors.grey))
                        ],
                      ),
                    ),
                  )
                ],
              ),
              rightWidget: InkWell(
                  onTap: () async {},
                  child: Row(
                    children: [
                      Text(getNoticeStatus('sms'),
                          style: TextStyle(
                              fontSize: AppConstant.smallTextSize,
                              color: Theme.of(context).primaryColor)),
                    ],
                  )),
            ),
            CommonRowWidget(
              labelFontSize: 15,
              valueFontSize: 15,
              leftWidget: Row(
                children: [
                  Text('微信通知',
                      style: TextStyle(
                          fontSize: 15,
                          color: DefaultConfig().configs.GREY_COLOR)),
                  Offstage(
                    offstage: false,
                    child: InkWell(
                      onTap: () {
                        if (phoneEditBtnStatus()) {
                          reNotice(orderDetail!.receiverMobile);
                        } else {
                          Fluttertoast.showToast(msg: '包裹已取出');
                        }
                      },
                      child: Row(
                        children: [
                          Padding(padding: EdgeInsets.only(left: 5)),
                          Image.asset(
                            LocalImageUtil.getImagePath('again'),
                            width: 15,
                          ),
                          Padding(padding: EdgeInsets.only(left: 5)),
                          Text('重新通知',
                              style: TextStyle(
                                  fontSize: AppConstant.smallTextSize,
                                  color: phoneEditBtnStatus()
                                      ? Theme.of(context).primaryColor
                                      : Colors.grey))
                        ],
                      ),
                    ),
                  )
                ],
              ),
              rightWidget: InkWell(
                  onTap: () async {},
                  child: Row(
                    children: [
                      Text(getNoticeStatus('wx'),
                          style: TextStyle(
                              fontSize: AppConstant.smallTextSize,
                              color: Theme.of(context).primaryColor)),
                    ],
                  )),
            ),
            CommonRowWidget(
                labelFontSize: 15,
                valueFontSize: 15,
                label: orderDetail?.orderType == 2 ? '存件时间' : '入柜时间',
                value:
                    '${getTimeFormat(orderDetail?.inboundTime ?? '') ?? ''}'),
            Offstage(
              offstage: orderDetail?.orderType == 2,
              child: CommonRowWidget(
                isShow: true,
                label: '滞留时间',
                value: orderDetail?.keepEffect == true
                    ? CheckUtils.countDay(orderDetail!.keepEffectTime) + '天'
                    : '未滞留',
                valueColor: orderDetail?.keepEffect == true
                    ? Theme.of(context).primaryColor
                    : Color(0xFF000000),
              ),
            ),
            SizedBox(
              height: 4,
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Offstage(
                  offstage: false,
                  child: OutlinedButton(
                    child: Text("开门检查",
                        style: TextStyle(
                            fontSize: AppConstant.smallTextSize,
                            color: Theme.of(context).primaryColor)),
                    onPressed: () {
                      openCabinetCheck(orderDetail!);
                    },
                    style: ButtonStyle(
                        minimumSize: MaterialStateProperty.all(Size(80, 32)),
                        shape: MaterialStateProperty.all(StadiumBorder()),
                        side: MaterialStateProperty.all(
                            BorderSide(color: Theme.of(context).primaryColor))),
                  ),
                ),
                Padding(padding: EdgeInsets.only(left: 3)),
                Offstage(
                  offstage: false,
                  child: OutlinedButton(
                    child: Text("异常出库",
                        style: TextStyle(
                            fontSize: AppConstant.smallTextSize,
                            color: Theme.of(context).primaryColor)),
                    onPressed: () {
                      unusualOut(orderDetail!);
                    },
                    style: ButtonStyle(
                        minimumSize: MaterialStateProperty.all(Size(80, 32)),
                        shape: MaterialStateProperty.all(StadiumBorder()),
                        side: MaterialStateProperty.all(
                            BorderSide(color: Theme.of(context).primaryColor))),
                  ),
                ),
                Padding(padding: EdgeInsets.only(left: 3)),
                Offstage(
                  offstage: orderDetail?.orderType != 1,
                  child: OutlinedButton(
                    child: Text("详情",
                        style: TextStyle(
                            fontSize: AppConstant.smallTextSize,
                            color: Theme.of(context).primaryColor)),
                    onPressed: () {
                      NavigatorUtils.goPackageDetailPage(context, orderDetail!);
                    },
                    style: ButtonStyle(
                        minimumSize: MaterialStateProperty.all(Size(55, 32)),
                        shape: MaterialStateProperty.all(StadiumBorder()),
                        side: MaterialStateProperty.all(
                            BorderSide(color: Theme.of(context).primaryColor))),
                  ),
                ),
                Expanded(
                    child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Offstage(
                      offstage: orderDetail?.orderType != 1,
                      child: Container(
                        width: 110,
                        margin: EdgeInsets.only(left: 6),
                        height: 37,
                        child: MaterialButton(
                          color: Theme.of(context).primaryColor,
                          textColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20.0),
                          ),
                          onPressed: () async {
                            pickOut(orderDetail!);
                          },
                          child: Text(
                            "开门取出",
                          ),
                        ),
                      ),
                    )
                  ],
                ))
              ],
            )
          ],
        ),
      ),
    );
  }
}
