import 'dart:async';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/entitys/customer_entity.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/utils/text_util.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppListState.dart';
import 'package:cabinet_flutter_app/widget/AppPullLoadWidget.dart';
import 'package:cabinet_flutter_app/widget/AppSearchAdressInputWidget.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/BottomBtnWidget.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CommonRowWidget.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CustomerDropDownSelectWidget.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class CustomerListPage extends StatefulWidget {
  CustomerListPage({Key? key}) : super(key: key);

  @override
  _CustomerListPage createState() => _CustomerListPage();
}

class _CustomerListPage extends State<CustomerListPage>
    with
        AutomaticKeepAliveClientMixin<CustomerListPage>,
        AppListState<CustomerListPage>,
        WidgetsBindingObserver,
        SingleTickerProviderStateMixin {
  ScrollController _controller = new ScrollController();

  final GlobalKey<ScaffoldState> customerListPageKey = GlobalKey<ScaffoldState>();

  String? keyword;

  int total = 0;
  Map<dynamic, dynamic> labelMap = {};
  bool showOperats = true;

  List topRightMenu = [
    {'label': '添加客户', 'value': 'add'},
  ];

  TextEditingController textEditingController = new TextEditingController();
  bool isCheckNull = true;

  // List typeList = [
  //   {"label": '全部', "value": 'ALL'},
  //   {'value': 'VIP', 'label': 'VIP'},
  //   {'value': 'RED', 'label': '标红'},
  //   {'value': 'YELLOW', 'label': '标黄'},
  //   {'value': 'SENSITIVE', 'label': '敏感'},
  //   {'value': 'BLOCK', 'label': '拉黑'}
  // ];

  String searchType = 'ALL';

  void initState() {
    super.initState();
    this.initData();
  }

  initData() async {
    labelMap.addAll({'ALL': '全部客户'});
    await getUserLabels();
  }

  getUserLabels() async {
    DataResult res = await CourierDao.getCustomerLabelList();
    if (res.result) {
      Map<dynamic, dynamic> map = {'ALL': '全部客户'};
      if (res.data.length > 0) {
        res.data.forEach((item) {
          map.addAll({item: item});
        });
      }
      labelMap = map;
    }
    if (mounted) {
      setState(() {});
    }
  }

  // 上拉加载更多
  _getData({isRefresh = false}) async {
    Map<String, dynamic> info = {
      'current': page.toString(),
      'size': 20,
      'keyword': keyword,
      'label': searchType == 'ALL' ? null : searchType,
    };
    DataResult res = await CourierDao.getCustomerList(info);
    total = res.total!;
    return res;
  }

  Future<Null> onLoadMore() async {
    if (isLoading) {
      return null;
    }
    isLoading = true;
    page++;
    var res = await requestLoadMore();
    if (res != null && res.result) {
      setState(() {
        pullLoadWidgetControl.dataList.addAll(res.data);
      });
    }
    resolveDataResult(res);
    isLoading = false;
    return null;
  }

  @protected
  Future<Null> handleRefresh() async {
    if (isLoading) {
      return null;
    }
    refreshIndicatorKey.currentState?.show();
    isLoading = true;
    page = 1;
    var res = await requestRefresh();
    resolveRefreshResult(res);
    resolveDataResult(res);
    if (res.next != null) {
      var resNext = await res.next;
      resolveRefreshResult(resNext);
      resolveDataResult(resNext);
    }
    isLoading = false;
    return null;
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  void deactivate() {
    super.deactivate();
  }

  onSearchChanged(String value) {
    keyword = value;
    setState(() {
      if (value == '') {
        isCheckNull = true;
      } else {
        isCheckNull = false;
      }
    });
  }

  onSearchSubmit(String? value) {
    if (value != null) {
      handleRefresh();
    } else {
      Fluttertoast.showToast(msg: '请输入关键字', gravity: ToastGravity.CENTER);
    }
  }

  toNext(type) async {
    setState(() {
      showOperats = !showOperats;
    });
    switch (type) {
      case 'add':
        toDetail(context, null);
        break;
    }
  }

  toDetail(BuildContext context, CustomerEntity? customerEntity) async {
    await NavigatorUtils.goCustomerDetailPage(context, customerEntity);
    getUserLabels();
    handleRefresh();
  }

  setSearchType(String value) {
    searchType = value;
    page = 1;
    handleRefresh();
    setState(() {});
  }

  renderOperates() {
    List<Widget> widgets = [];
    topRightMenu.forEach((item) {
      widgets.add(InkWell(
        onTap: () {
          toNext(item['value']);
        },
        child: Container(
          height: 45.0,
          decoration: BoxDecoration(border: Border(bottom: BorderSide(color: Colors.grey.shade100))),
          margin: EdgeInsets.fromLTRB(5.0, 0.0, 5.0, 0.0),
          alignment: Alignment.center,
          child: Text(
            '${item['label']}',
            style: TextStyle(fontSize: 14.0),
          ),
        ),
      ));
    });
    return widgets;
  }

  _renderEventItem(index) {
    CustomerEntity customer = CustomerEntity.fromJson(dataList[index]);
    return InkWell(
      onTap: () async {
        await toDetail(context, customer);
      },
      child: Stack(
        children: [
          Container(
            padding: EdgeInsets.only(left: 10.0, right: 10),
            decoration: BoxDecoration(color: Colors.white),
            child: Container(
              padding: EdgeInsets.fromLTRB(0.0, 10.0, 0.0, 10.0),
              width: MediaQuery.of(context).size.width - 10,
              decoration: BoxDecoration(border: Border(bottom: BorderSide(width: 1.0, color: Colors.grey.shade100))),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        (customer.name == '') ? '老客户' : customer.name,
                        style: TextStyle(color: Colors.black87, fontSize: 16.0),
                      ),
                      Row(
                        children: [
                          Text(
                            TextUtil.formatDigitPatternEnd(customer.mobile),
                            style: TextStyle(color: Colors.black87, fontSize: 16.0),
                          ),
                          Padding(
                              padding: EdgeInsets.only(left: 4),
                              child: Icon(
                                Icons.arrow_forward_ios_sharp,
                                size: 15,
                              ))
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          // Positioned(
          //   top: 0,
          //   right: 0,
          //   child: getIcon(customer),
          // )
        ],
      ),
    );
  }

  // buildSearchType() {
  //   List<Widget> list = [];
  //   typeList.forEach((item) {
  //     list.add(
  //       searchType == item['value']
  //           ? Material(
  //               color: Theme.of(context).primaryColor,
  //               borderRadius: BorderRadius.circular(4),
  //               child: InkWell(
  //                 onTap: () {
  //                   setSearchType(item['value']);
  //                 },
  //                 child: Container(
  //                   padding: EdgeInsets.fromLTRB(10, 5, 10, 5),
  //                   child: Text(
  //                     '${item['label']}',
  //                     style: TextStyle(color: Colors.white, fontSize: 13),
  //                   ),
  //                 ),
  //               ),
  //             )
  //           : Material(
  //               color: Colors.grey.shade100,
  //               child: InkWell(
  //                 onTap: () {
  //                   setSearchType(item['value']);
  //                 },
  //                 child: Container(
  //                   padding: EdgeInsets.fromLTRB(10, 5, 10, 5),
  //                   child: Text(
  //                     '${item['label']}',
  //                     style: TextStyle(color: Colors.black54, fontSize: 14),
  //                   ),
  //                 ),
  //               ),
  //             ),
  //     );
  //   });
  //   return Container(
  //     height: 45,
  //     padding: EdgeInsets.only(left: 10, right: 10),
  //     child: Row(
  //       mainAxisAlignment: MainAxisAlignment.start,
  //       children: [
  //         // Text(
  //         //   '类型:',
  //         //   style: TextStyle(color: Colors.grey.shade500, fontSize: 14),
  //         // ),
  //         Container(
  //           // padding: EdgeInsets.only(left: 0),
  //           child: Row(
  //             children: list,
  //           ),
  //         )
  //       ],
  //     ),
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      backgroundColor: Colors.grey.shade100,
      key: customerListPageKey,
      appBar: AppCustomerBar(
        title: AppbarTitle(
          title: '客户管理',
          isCenter: true,
        ),
        actions: <Widget>[
          Container(
            width: 60,
          )
          // IconButton(
          //     tooltip: '添加客户',
          //     icon: Icon(
          //       Icons.add_circle_outline_rounded,
          //       color: Colors.black
          //     ),
          //     onPressed: () async {
          //       await NavigatorUtils.goCustomerDetailPage(context, null);
          //       // handleRefresh();
          //     })
        ],
      ),
      body: Stack(
        children: <Widget>[
          Column(
            children: <Widget>[
              Stack(alignment: Alignment.topRight, children: [
                Container(
                  padding: EdgeInsets.fromLTRB(15.0, 0.0, 55.0, 10.0),
                  decoration: BoxDecoration(color: DefaultConfig().configs.WHITE_COLOR),
                  child: AppSearchAdressInputWidget(
                      prefixIcon: Icon(Icons.search_rounded, size: 24),
                      borderRadius: 20,
                      bgColor: DefaultConfig().configs.BG_COLOR,
                      textInputAction: TextInputAction.search,
                      controller: textEditingController,
                      onChanged: onSearchChanged,
                      onSubmitted: onSearchSubmit,
                      hitText: "请输入手机号查询(支持后4位)"),
                ),
                Positioned(
                    right: 65,
                    top: 10,
                    child: Offstage(
                      offstage: isCheckNull,
                      child: InkWell(
                        child: Icon(
                          Icons.clear,
                          size: 20,
                        ),
                        onTap: () {
                          setState(() {
                            keyword = '';
                            textEditingController.value = TextEditingValue(text: '');
                            isCheckNull = true;
                          });
                        },
                      ),
                    )),
                Positioned(
                  right: 15,
                  top: 8,
                  child: InkWell(
                    child: Text(
                      '搜索',
                      style: TextStyle(color: Theme.of(context).primaryColor),
                    ),
                    onTap: () {
                      onSearchSubmit(textEditingController.text);
                    },
                  ),
                ),
              ]),
              CommonRowWidget(
                leftWidget: Row(
                  children: [
                    Padding(padding: EdgeInsets.only(left: 10)),
                    Container(
                      constraints: BoxConstraints(maxWidth: 100, minWidth: 80),
                      child: CustomerDropDownSelectWidget(
                        value: searchType,
                        labelStyle: TextStyle(color: Color(0xFF999999), fontSize: 14),
                        map: labelMap,
                        hintText: '标签',
                        cb: (item) {
                          setState(() {
                            searchType = item;
                          });
                          handleRefresh();
                        },
                      ),
                    ),
                  ],
                ),
                rightWidget: Container(
                    padding: EdgeInsets.only(right: 10),
                    child: Text('共计:  ${total.toString()}', style: TextStyle(color: Color(0xFF999999)))),
              ),
              Expanded(
                child: RefreshIndicator(
                  onRefresh: onFresh,
                  child: AppPullLoadWidget(
                    pullLoadWidgetControl,
                    (BuildContext context, int index) => _renderEventItem(index),
                    handleRefresh,
                    onLoadMore,
                    refreshKey: refreshIndicatorKey,
                  ),
                ),
              ),
              BottomBtnWidget(
                showShadow: true,
                type: ButtonType.primary,
                title: '添加客户',
                action: () async {
                  await toDetail(context, null);
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  // TODO: implement wantKeepAlive
  @override
  bool get wantKeepAlive => true;

  // TODO: implement isRefreshFirst
  @override
  bool get isRefreshFirst => true;

  @override
  requestLoadMore() async {
    return await _getData();
  }

  //
  Future<Null> onFresh() async {
    _getData(isRefresh: true);
  }

  @override
  requestRefresh() async {
    return await _getData();
  }
}
