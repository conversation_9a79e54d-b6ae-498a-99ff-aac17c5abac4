import 'dart:async';
import 'dart:io';
import 'dart:ui';

import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/dao/PhotoDao.dart';
import 'package:cabinet_flutter_app/common/dao/ReposDao.dart';
import 'package:cabinet_flutter_app/common/dao/SyncDao.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/banner_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_shop_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/home_data_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/home_summary_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/photo_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/wallet_entity.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/page_life_cycle.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/redux/CurrentTabIndexRedux.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/BrandUtil.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/utils/SoundUtils.dart';
import 'package:cabinet_flutter_app/common/utils/ThrottleUtil.dart';
import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:card_swiper/card_swiper.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:scan/device_info.dart';
import 'package:scan/scan.dart';

GlobalKey<_IndexPageState> indexGlobalKey = GlobalKey();

enum MenuType {
  PJRG, // 派件入柜
  KXCX, // 空箱查询
  BGCX, // 包裹查询
  XGZY, // 箱格租用
  YYGK, // 预约格口
  FJGZ, // 附近点位
  SJZX, // 数据中心
  KHGL, // 客户管理
  CZ, // 充值
  TPSC, // 图片上传
  PJJRK, // 派件仅入库
  PLRG, // 批量入柜
  MTRG, // 盲投入柜
  RCSM, // 入仓扫描
  JRK, // 仅入库
  // JJYW, // 寄件业务
  CCSM, // 出仓扫描
  YCGL,
  LJDH, // 拦截单号
}

//主页
class IndexPage extends StatefulWidget {
  IndexPage({Key? key}) : super(key: key);

  @override
  _IndexPageState createState() => _IndexPageState();
}

class _IndexPageState extends State<IndexPage> with AutomaticKeepAliveClientMixin, PageLifeCycle<IndexPage> {
  final GlobalKey<ScaffoldState> key = new GlobalKey<ScaffoldState>();
  ThrottleUtil throttleUtil = ThrottleUtil();
  FocusNode searchFocusNode = FocusNode();
  var keyword = '';
  TextEditingController textEditingController = new TextEditingController();
  HomeSummaryEntity homeSummary = new HomeSummaryEntity();

  /// 室外柜
  List operationsOut = [
    {"label": "派件入柜", "icon": '15', "type": MenuType.PJRG, num: 0},
    {"label": "包裹查询", "icon": '3', "type": MenuType.BGCX, num: 0},
    {"label": "空箱查询", "icon": '2', "type": MenuType.FJGZ, num: 0},
    {"label": "数据中心", "icon": '6', "type": MenuType.SJZX, num: 0},
    {"label": "客户管理", "icon": '7', "type": MenuType.KHGL, num: 0},
    {"label": "充值", "icon": '9', "type": MenuType.CZ, num: 0},
    {"label": "图片上传", "icon": '14', "type": MenuType.TPSC, num: 0},
    {"label": "箱柜租用", "icon": '4', "type": MenuType.XGZY, num: 0},
    {"label": "预约格口", "icon": '5', "type": MenuType.YYGK, num: 0},
   
    

  ];

  /// 室内柜
  List operationsIn = [
    {"label": "派件入柜", "icon": '15', "type": MenuType.PJRG, num: 0},
    {"label": "盲投入柜", "icon": '18', "type": MenuType.MTRG, num: 0},
    {"label": "批量入柜", "icon": '16', "type": MenuType.PLRG, num: 0},
    {"label": "派件仅入库", "icon": '1', "type": MenuType.PJJRK, num: 0},
    {"label": "充值", "icon": '9', "type": MenuType.CZ, num: 0},
    // {"label": "入仓扫描", "icon": '10', "type": MenuType.RCSM, num: 0},
    {"label": "送货上门", "icon": '17', "type": MenuType.JRK, num: 0},
    // {"label": "出仓扫描", "icon": '12', "type": MenuType.CCSM, num: 0},
    {"label": "客户管理", "icon": '13', "type": MenuType.KHGL, num: 0},
    {"label": "数据中心", "icon": '6', "type": MenuType.SJZX, num: 0},
    {"label": "图片上传", "icon": '14', "type": MenuType.TPSC, num: 0},
    {"label": "异常管理", "icon": '8', "type": MenuType.YCGL, num: 0},
    {"label": "拦截单号", "icon": '19', "type": MenuType.LJDH, num: 0},
  ];
  List operations = [];
  Scan scanPlugin = Scan();
  int outboundNum = 0; // 出库
  int keepNum = 0; // 滞留
  int stockNum = 0; // 库存
  int ztInboundNum = 0; // 入库
  int inboundNum = 0; // 入库汇总
  int smInboundNum = 0; // 外送
  bool isShowVoiceDialog = false;
  bool isPda = false;
  String lastFileName = '';
  PhotoEntity lastPhoto = new PhotoEntity();
  UserEntity? user;
  Timer? timer;
  String beginTime = new DateFormat("yyyy-MM-dd").format(DateTime.now());
  String endTime = new DateFormat("yyyy-MM-dd").format(DateTime.now());
  List<BannerEntity?>? bannerList = [];
  var waitPickUpNum = 0, keepEffectNum = 0;
  var shelfWaitPickUpNum = 0, shelfKeepEffectNum = 0;
  var waitTakenNum = 0, takenNum = 0;
  bool isCourier = true;
  List<CabinetShopEntity?> cabinetShopList = [];
  WalletEntity? data;
  bool isCheckNull = true;
  late StreamSubscription<dynamic>? _scanSubscription;
  String lastScanUrl = '';
  int lastScanTime = 0;
  bool isInProcess = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      init();
    });
    checkIsPda();
    initScan();
    checkedLogin();
  }

  checkedLogin() async {
    DateTime dateTime = DateTime.now();
    String now = dateTime.toString().substring(0, 10);
    var res = await LocalStorage.get(DefaultConfig().configs.NOW_DAY, isPrivate: true);
    if (res != now && !isCourier) {
      Map<String, dynamic> info = {};
      var res = await UserDao.accountList(info);
      if (res != null && res.result && res.data.length > 0) {
        await LocalStorage.save(DefaultConfig().configs.NOW_DAY, now, isPrivate: true);
        bool toSolve = false;
        await CommonUtils.accountTestLogin(context, res.data, (value) {
          if (value) {
            toSolve = value;
          }
        });
        if (toSolve) {
          await NavigatorUtils.goUserListPage(context);
        }
      }
    }
  }

  void initScan() {
    scanPlugin.initOcr();
    _scanSubscription = scanPlugin.onScanChanged.listen((result) {
      int nowtTime = new DateTime.now().millisecondsSinceEpoch;
      if (((nowtTime - lastScanTime) / 1000) < 3 && lastScanUrl != '' && lastScanUrl == result['text']) {
      } else {
        lastScanTime = nowtTime;
        lastScanUrl = result['text'];
        if (result['text'] != '' && result['text'] != null) {
          checkScanResult(
            result['text'],
          );
        }
      }
    });
  }

  checkScanResult(String text) async {
    if (isInProcess) {
      return false;
    }
    isInProcess = true;
    RegExp waybillReg = RegExp(r'^[A-Za-z1-9]{2}\d{8,16}$');
    if (waybillReg.hasMatch(text)) {
      onSearchSubmit(text);
    } else {
      if (isPda && isCourier) {
        checkUrl(text);
      } else {
        isInProcess = false;
      }
    }
  }

  cancelScanSubscription() {
    _scanSubscription?.cancel();
  }

  checkUrl(text) async {
    isInProcess = true;
    DataResult? res = await CourierDao.bindQr({'qrCode': text});
    isInProcess = false;
    if (res != null) {
      if (res.result) {
        SoundUtils.audioPushFn(SoundUtils.SUCCESS_OK);
        Future.delayed(Duration(milliseconds: 200), () async {
          await NavigatorUtils.goCabinetMangerPage2(context, code: res.data);
        });
      }
    }
  }

  ///获取快递员派件汇总
  getHomeData() async {
    var res = await CourierDao.courierHomeInCabinet();
    if (res.result) {
      HomeDataEntity data = HomeDataEntity.fromJson(res.data);
      setState(() {
        waitPickUpNum = data.waitPickUpNum ?? 0;
        keepEffectNum = data.keepEffectNum ?? 0;
        shelfKeepEffectNum = data.shelfKeepEffectNum ?? 0;
        shelfWaitPickUpNum = data.shelfWaitPickUpNum ?? 0;
        waitTakenNum = data.waitTakenNum ?? 0;
        takenNum = data.takenNum ?? 0;
      });
    }
  }

  ///获取首页数据汇总汇总
  getHomeSummary() async {
    var res = await CourierDao.getHomeSummary();
    if (res.result) {
      setState(() {
        homeSummary = HomeSummaryEntity.fromJson(res.data);
      });
    }
  }

  getBanner() async {
    Map<String, dynamic> info = {'adTerminal': isCourier ? 1 : 1};
    var res = await UserDao.getBannerList(info);
    if (res.result) {
      bannerList = jsonConvert.convertList<BannerEntity>(res.data);
      if (bannerList!.length == 0) {
        BannerEntity bean = new BannerEntity();
        bean.fileUrl = '';
        bannerList!.add(bean);
      }
    }
    setState(() {});
  }

  _getShopData(MenuType label) async {
    LoadingUtil(
      status: '点位获取中...',
    ).show(context);
    var res = await CourierDao.shopCabinetLocation();
    LoadingUtil.dismiss(context);
    if (res != null && res.result) {
      cabinetShopList = jsonConvert.convertList<CabinetShopEntity>(res.data)!;
      if (cabinetShopList.length > 1) {
        selectShopCabinet(label);
      } else if (cabinetShopList.length == 1) {
        if (label == MenuType.PLRG || label == MenuType.MTRG) {
          if (isPda) {
            await NavigatorUtils.goBatchEntryScanCabinetPda(context, cabinetShopList[0]!.code!, '',
                isBlind: label == MenuType.MTRG);
          } else {
            await NavigatorUtils.goBatchEntryScanCabinet(context, cabinetShopList[0]!.code!, '',
                isBlind: label == MenuType.MTRG);
          }
        } else {
          await NavigatorUtils.goDeliveryPage(context, cabinetShopList[0]!.code, isDelivery: label == MenuType.PJJRK);
        }
      } else {
        await NavigatorUtils.goUnbindCabinetPage(context);
        Fluttertoast.showToast(msg: '无可用点位');
      }
    } else {
      Fluttertoast.showToast(msg: res.data);
    }
  }

  getWealthInfo(MenuType label) async {}

  goRecharge() {
    CommonUtils.customConfirm(context, '余额不足，请前往充值', title: '充值提醒', showClose: false, actions: <Widget>[
      Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
            },
            child: Text('取消', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
            style: ButtonStyle(
                foregroundColor: WidgetStateProperty.resolveWith((states) {
                  return Colors.black;
                }),
                backgroundColor: WidgetStateProperty.resolveWith((states) {
                  return DefaultConfig().configs.WHITE_COLOR;
                }),
                minimumSize: WidgetStateProperty.all(Size(90, 40)))),
        TextButton(
            onPressed: () async {
              await NavigatorUtils.goWalletPage(context);
            },
            child: Text('前往', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
            style: ButtonStyle(
                foregroundColor: WidgetStateProperty.resolveWith((states) {
                  return Theme.of(context).primaryColor;
                }),
                backgroundColor: WidgetStateProperty.resolveWith((states) {
                  return Colors.white;
                }),
                minimumSize: WidgetStateProperty.all(Size(90, 40)))),
      ])
    ]);
  }

  @override
  void deactivate() {
    super.deactivate();
  }

  // 路由显示触发
  @override
  void onShow() async {
    var isLogout = await LocalStorage.get(DefaultConfig().configs.IS_LOGOUT);
    await PhotoDao.deletePhoto7DaysBefore();

    initScan();

    /// 每次退回主界面重新初始化，防止语音监听失败
    SoundUtils.initSounds(isInit: true);
    if (!isLogout) {
      checkIsPda();
    }
    handleRefresh();
  }

  @override
  Future<void> didChangeDependencies() async {
    super.didChangeDependencies();
  }

  void didChangeAppLifecycleState(AppLifecycleState state) {
    ///通过state判断App前后台切换
    switch (state) {
      case AppLifecycleState.hidden:
      case AppLifecycleState.inactive: // 处于这种状态的应用程序应该假设它们可能在任何时候暂停。
        break;
      case AppLifecycleState.resumed: // 应用程序可见，前台
        initScan();
        break;
      case AppLifecycleState.paused: // 应用程序不可见，后台
        _scanSubscription?.cancel();
        break;
      case AppLifecycleState.detached: // 申请将暂时暂停
        break;
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  List<String> deviceBrands = ["Newland", "IWRIST", "kaicom", "iData"];

  List<String> deviceNames = [
    "I6200S",
    "I6200",
    "I6310",
    "I6310B",
    "M7(M)",
    "M7(H)",
    "I6310HB",
    "I6310C",
    "I6292S",
    "T1",
    "i3",
    'K3Pro',
    'T1Pro'
  ];

  checkIsPda() async {
    if (Platform.isAndroid) {
      DeviceInfo info = await scan.getDeviceInfo();
      int nameIndex = deviceNames.indexWhere((item) => item.toUpperCase() == info.model?.toUpperCase());
      isPda = nameIndex > -1;
      int brandIndex = deviceBrands.indexWhere((item) => item.toUpperCase() == info.brand?.toUpperCase());
      isPda = brandIndex > -1;
      await CheckUtils.saveIsPda(isPda);
    } else {
      await CheckUtils.saveIsPda(false);
      isPda = false;
    }
  }

  init() async {
    ///4 室外柜  6室内柜
    isCourier = CheckUtils.isCourier(context);
    if (!isCourier) {
      operations = operationsIn;
    } else {
      operations = operationsOut;
    }
    setState(() {});
    if (!isCourier) {
      [
        Permission.manageExternalStorage,
        Permission.camera,
        Permission.storage,
      ].request();
    }

    /// 初始化语音
    scanPlugin.initTts();
    // scanPlugin.playSound("欢迎使用熊猫智能柜");

    /// 删除过期图片
    await PhotoDao.deletePhoto7DaysBefore();
    SoundUtils.initSounds(isInit: true);
    CommonUtils.clearCache(context);
    var _isAi = await LocalStorage.get(DefaultConfig().configs.IS_AI);
    if (_isAi == null) {
      await LocalStorage.save(DefaultConfig().configs.IS_AI, true);
    }
    // 获取banner
    getBanner();
    // 获取所有可入柜品牌
    this.handleRefresh();
  }

  needRefresh() async {
    bool isRefresh = await BrandUtils.needRefresh();
    if (isRefresh) {
      BrandUtils.saveHomePageRefresh(isRefresh: false);
      this.handleRefresh();
    }
  }

  Future<Null> handleRefresh() async {
    BrandUtils.getBrandBindList(needRefresh: true);
    // 获取所有快递公司规则
    SyncDao.fetchCompanyRule();
    getHomeData();
    getHomeSummary();
    checkIsPda();
    ReposDao.getNewsVersion(context);
  }

  /// 鉴权 和判断是否实名
  checkUserRole(MenuType label) async {
    /// 判断用户是否实名
    bool hasReal = await CheckUtils.checkUserHasReal();
    if (hasReal) {
      String hasPerms = 'WAIT';
      if (label == MenuType.PJRG || label == MenuType.PJJRK || label == MenuType.PLRG || label == MenuType.MTRG) {
        hasPerms = await CheckUtils.checkUserHasInStorePermission();
      } else if (label == MenuType.XGZY) {
        hasPerms = await CheckUtils.checkUserHasRentPermission();
      } else if (label == MenuType.YYGK) {
        hasPerms = await CheckUtils.checkUserHasOrderPermission();
      }
      if (hasPerms == 'PASS') {
        if (isCourier) {
          await NavigatorUtils.goCabinetBindPage(context);
          this.needRefresh();
        } else {
          _getShopData(label);
        }
      } else {
        CommonUtils.alert(context,
            dialogContent: '该点位需管理员审核后才可进行操作',
            content: buildContent(hasPerms),
            title: '点位操作申请',
            buttonLabel: buildButtonLabel(hasPerms),
            buttonDisable: buildButtonStatus(hasPerms));
        SoundUtils.audioPushFn(SoundUtils.ADMINISTRATOR_REVIEW);
      }
    } else {
      Fluttertoast.showToast(msg: '请先至我的设置进行实名认证！');
      SoundUtils.audioPushFn(SoundUtils.REAL_NAME_AUTHENTICATION);
    }
  }

  selectShopCabinet(MenuType label) async {
    SoundUtils.audioPushFn(SoundUtils.CHOOSE_POINT);
    CabinetShopEntity? shopEntity = await CommonUtils.showBottomShopSelectModal(context, cabinetShopList);
    if (shopEntity != null) {
      if (label == MenuType.PLRG || label == MenuType.MTRG) {
        if (isPda) {
          await NavigatorUtils.goBatchEntryScanCabinetPda(context, shopEntity.code!, '',
              isBlind: label == MenuType.MTRG);
        } else {
          await NavigatorUtils.goBatchEntryScanCabinet(context, shopEntity.code!, '', isBlind: label == MenuType.MTRG);
        }
      } else {
        await NavigatorUtils.goDeliveryPage(context, shopEntity.code, isDelivery: label == MenuType.PJJRK);
      }
    }
  }

  buildButtonStatus(String hasPerms) {
    return hasPerms == 'WAIT' || hasPerms == 'REJECT';
  }

  buildButtonLabel(String hasPerms) {
    Map<String, dynamic> labelMap = {'WAIT': '审核中', 'NEW': '提交审核', 'REJECT': '好的'};
    return labelMap[hasPerms];
  }

  buildContent(String hasPerms) {
    List<Widget> widgets = [];
    if (hasPerms == 'WAIT' || hasPerms == 'NEW') {
      widgets.add(Text('该点位需管理员审核后才可进行操作', style: TextStyle(color: Colors.black, fontSize: 15)));
    } else if (hasPerms == 'REJECT') {
      widgets.add(Text('审核未通过，无权使用该点位', style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 15)));
    }
    widgets.add(Row(
      children: [
        Text('管理员联系方式: ', style: TextStyle(color: Colors.black, fontSize: 15)),
        InkWell(
          onTap: () async {
            await CommonUtils.makePhoneCall(context, '18545956235');
          },
          child: Text('18545956235', style: TextStyle(color: Colors.blue, fontSize: 15)),
        )
      ],
    ));
    return widgets;
  }

  toNext(MenuType label) async {
    hideKeyboard();
    switch (label) {
      case MenuType.PJRG:
      case MenuType.PJJRK:
      case MenuType.PLRG:
      case MenuType.MTRG:
        BrandUtils.saveHomePageRefresh();
        checkUserRole(label);
        break;
      case MenuType.KXCX:
      case MenuType.FJGZ:
        Map<MenuType, dynamic> typeMap = {
          MenuType.KXCX: CabinetBoxListPageType.KXCX,
          MenuType.FJGZ: CabinetBoxListPageType.FJGZ
        };
        BrandUtils.saveHomePageRefresh();
        await NavigatorUtils.goEmptyContainerPage(context, typeMap[label]);
        this.needRefresh();
        break;
      case MenuType.BGCX:
        BrandUtils.saveHomePageRefresh();
        await NavigatorUtils.goPackageSearchPage(context);
        this.needRefresh();
        break;
      case MenuType.XGZY:
      case MenuType.YYGK:
        Map<MenuType, dynamic> typeMap = {MenuType.XGZY: CabinetBoxUseType.XGZY, MenuType.YYGK: CabinetBoxUseType.YYGK};
        await NavigatorUtils.goCabinetBoxUsePage(context, typeMap[label]);
        break;
      case MenuType.SJZX:
        await NavigatorUtils.goDataSummeryPage(context, isCourier);
        break;
      case MenuType.KHGL:
        await NavigatorUtils.goCustomerListPage(context);
        break;
      case MenuType.TPSC:
        await NavigatorUtils.goPhotoUpload(context);
        break;
      case MenuType.CZ:
        await NavigatorUtils.goWalletPage(context);
        break;
      case MenuType.RCSM:
        BrandUtils.saveHomePageRefresh();
        if (!isPda) {
          await NavigatorUtils.goInboundPage(context, "RK", false);
        } else {
          await NavigatorUtils.goInboundPdaPage(context, "RK", false);
        }
        this.needRefresh();
        break;
      case MenuType.JRK:
        BrandUtils.saveHomePageRefresh();
        if (!isPda) {
          await NavigatorUtils.goInboundPage(context, "RK", true);
        } else {
          await NavigatorUtils.goInboundPdaPage(context, "RK", true);
        }
        this.needRefresh();
        break;
      case MenuType.CCSM:
        BrandUtils.saveHomePageRefresh();
        if (!isPda) {
          await NavigatorUtils.goOutboundPage(context);
        } else {
          await NavigatorUtils.goOutboundPda(context);
        }
        this.needRefresh();
      case MenuType.YCGL:
        await NavigatorUtils.goOrderAbnormalPage(context);
        break;
      case MenuType.LJDH:
        await NavigatorUtils.goInterceptListPage(context);
        break; 
    }
  }

  hideKeyboard() {
    FocusScope.of(context).requestFocus(FocusNode());
  }

  buildOperations(int index) {
    List<Widget> widgets = <Widget>[];
    int rowNumber = 4;
    double width = (MediaQuery.of(context).size.width - 22) / rowNumber;
    width = width > 0 ? width : 0;
    List _list = [];
    int start = rowNumber * 3 * index + 0;
    int end = rowNumber * 3 * (index + 1);
    if (end > operations.length) {
      _list = operations.sublist(start);
    } else {
      _list = operations.sublist(start, end);
    }
    _list.forEach((item) {
      widgets.add(InkWell(
        onTap: () => throttleUtil.throttle(() {
          toNext(item['type']);
        }),
        child: Stack(
          children: <Widget>[
            Container(
              width: width,
              padding: EdgeInsets.fromLTRB(0, 3.0, 0, 12.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  LocalImageUtil.getImageAsset(item['icon'], isChannel: true, width: 44.0),
                  Padding(padding: EdgeInsets.only(top: 4.0)),
                  Text(
                    item['label'],
                    style: new TextStyle(fontSize: 13.0, color: Color(0xFF858585)),
                  )
                ],
              ),
            ),
            Positioned(
              top: 12,
              right: 15,
              child: Offstage(
                offstage: item['num'] == 0 || item['num'] == null,
                child: Container(
                  width: 18,
                  height: 18,
                  decoration: BoxDecoration(color: Colors.red, borderRadius: BorderRadius.all(Radius.circular(30))),
                  alignment: Alignment.center,
                  child: Text(
                    '${(item['num'] != null && item['num'] > 99) ? '99+' : item['num']}',
                    style: TextStyle(color: Colors.white, fontSize: 10),
                  ),
                ),
              ),
            ),
          ],
        ),
      ));
    });
    return widgets;
  }

  Widget buildCountRender(String name, String label, int number, cb) {
    return InkWell(
      onTap: cb,
      child: Container(
        margin: EdgeInsets.only(top: 3),
        child: Column(
          children: [
            Text(
              '$number',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.white, letterSpacing: 1),
            ),
            Text(
              label,
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: Colors.white, letterSpacing: 0.5),
            ),
          ],
        ),
      ),
    );
  }

  onSearchSubmit(String value) async {
    isInProcess = false;
    if (value.length >= 4) {
      await NavigatorUtils.goPackageSearchPage(context, value: value);
    }
  }

  initData(store) async {
    user = await UserDao.getUserInfo(store);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    ScreenUtil.init(context, designSize: Size(375, 667));
    var appHeight = MediaQueryData.fromView(window).padding.top;
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.dark,
      child: StoreBuilder<AppState>(builder: (context, store) {
        user = store.state.userInfo;
        if (user == null) {
          initData(store);
        }
        return GestureDetector(
            onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
            child: Scaffold(
              backgroundColor: DefaultConfig().configs.BG_COLOR,
              key: key,
              body: RefreshIndicator(
                  onRefresh: handleRefresh,
                  child: SingleChildScrollView(
                    physics: AlwaysScrollableScrollPhysics(),
                    child: Column(
                      children: <Widget>[
                        Container(
                          height: appHeight,
                          width: double.infinity,
                          decoration: new BoxDecoration(color: Colors.white),
                        ),

                        /// srarch bar
                        _buildSearchBar(),

                        /// banner
                        _buildBanner(),

                        Container(
                          margin: EdgeInsets.only(top: 0),
                          decoration: BoxDecoration(color: Colors.white),
                          child: Column(
                            children: [
                              /// 今日数据
                              _buildTopic(store),

                              ///九宫菜单按钮
                              _buildMenu()
                            ],
                          ),
                        ),
                        _buildTodaySummary()
                      ],
                    ),
                  )),
            ));
      }),
    );
  }

  // 搜索框
  Widget _buildSearchBar() {
    return Container(
      color: Colors.white,
      child: Container(
        margin: EdgeInsets.fromLTRB(15.0, 5.0, 15.0, 5.0),
        height: 40,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(24), color: Color(0xFFF1F1F1)),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            InkWell(
              onTap: () async {
                await NavigatorUtils.goPackageSearchPage(context, autoFocus: true);
              },
              child: Container(
                  width: 48,
                  height: 48,
                  padding: EdgeInsets.all(12),
                  child: LocalImageUtil.getImageAsset('searchGrey')),
            ),
            Expanded(
              child: InkWell(
                onTap: () async {
                  await NavigatorUtils.goPackageSearchPage(context, autoFocus: true);
                },
                child: Text(
                  '取件码/运单号或手机号后四位',
                  style: TextStyle(fontSize: AppConstant.smallTextSize, color: Colors.grey),
                ),
              ),
            ),
            Container(
              width: 40,
              height: 40,
              child: IconButton(
                alignment: Alignment.center,
                padding: EdgeInsets.all(6),
                icon: LocalImageUtil.getImageAsset('scan'),
                onPressed: () async {
                  await NavigatorUtils.goCabinetBindPage(context, scanAction: ScanAction.SCANSEARCH);
                },
              ),
            )
          ],
        ),
      ),
    );
  }

  // 构建Banner
  Widget _buildBanner() {
    return Container(
      color: Colors.white,
      child: Container(
          width: MediaQuery.of(context).size.width,
          margin: EdgeInsets.only(left: 15, top: 5, right: 15, bottom: 15),
          child: bannerList!.length > 0
              ? SizedBox(
                  height: 110,
                  child: Swiper(
                    autoplay: true,
                    fade: 0.1,
                    duration: 800,
                    autoplayDelay: 5000,
                    layout: SwiperLayout.DEFAULT,
                    itemHeight: 110,
                    itemWidth: MediaQuery.of(context).size.width,
                    itemCount: bannerList!.length,
                    itemBuilder: (BuildContext context, int index) {
                      bool isNetPic = false;
                      String? url;
                      if (bannerList?[index]?.fileUrl != null) {
                        url = bannerList?[index]?.fileUrl;
                        if (url != null) {
                          isNetPic = url.startsWith('http');
                        }
                      }
                      return Container(
                          decoration: BoxDecoration(borderRadius: BorderRadius.circular(6)),
                          child: isNetPic
                              ? Image.network(url!, fit: BoxFit.fill)
                              : Image.asset(LocalImageUtil.getImagePath('banner', isChannel: true), fit: BoxFit.fill));
                    },
                    pagination: SwiperPagination(
                        builder: DotSwiperPaginationBuilder(
                            size: 7, activeSize: 7, space: 3, color: Color.fromRGBO(255, 255, 255, 0.5))),
                  ))
              : Container(
                  width: double.infinity,
                  height: 110,
                )),
    );
  }

  // 数据汇总
  Widget _buildTopic(store) {
    if (isCourier) {
      return _buildTopicCourier(store);
    } else if (!isCourier) {
      return _buildTopicStation(store);
    }
    return Container();
  }

  /// 快递员数据
  _buildTopicCourier(store) {
    return Container(
        margin: EdgeInsets.fromLTRB(15, 0, 15, 10),
        child: Row(mainAxisAlignment: MainAxisAlignment.spaceEvenly, children: [
          TopicItem(
            store,
            '派件',
            Color(0xFFF6AF3D),
            Color(0xFFF68C25),
            children: Row(mainAxisAlignment: MainAxisAlignment.spaceEvenly, children: [
              buildCountRender('pj', '待取件', waitPickUpNum, () {
                searchFocusNode.unfocus();
                LocalStorage.save(DefaultConfig().configs.INDEX_QUERY, {'top': 'PJ', 'middle': 'DQ'});
                tabController.animateTo(1);
                store.dispatch(new UpdateCurrentTabIndexAction(1));
              }),
              Container(
                height: 36,
                child: VerticalDivider(
                  color: Colors.white,
                  thickness: 1,
                  indent: 3,
                  endIndent: 3,
                ),
              ),
              // Container(
              //   height: 30,
              //   width: 1,
              //   color: Theme.of(context).primaryColorDark,
              // ),
              buildCountRender('pj', '滞留件', keepEffectNum, () {
                searchFocusNode.unfocus();
                LocalStorage.save(DefaultConfig().configs.INDEX_QUERY, {'top': 'PJ', 'middle': 'ZL'});
                tabController.animateTo(1);
                store.dispatch(new UpdateCurrentTabIndexAction(1));
              })
            ]),
          ),
          Padding(padding: EdgeInsets.only(left: 10)),
          TopicItem(store, '收件', Color(0xFF7DC82A), Color(0xFF84BF41),
              children: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  buildCountRender('sj', '待收件', waitTakenNum, () {
                    searchFocusNode.unfocus();
                    LocalStorage.save(DefaultConfig().configs.INDEX_QUERY, {'top': 'SJ', 'middle': 'DS'});
                    tabController.animateTo(1);
                    store.dispatch(new UpdateCurrentTabIndexAction(1));
                  }),
                  Container(
                    height: 36,
                    child: VerticalDivider(
                      color: Colors.white,
                      thickness: 1,
                      indent: 3,
                      endIndent: 3,
                    ),
                  ),
                  buildCountRender('sj', '已收件', takenNum, () {
                    searchFocusNode.unfocus();
                    LocalStorage.save(DefaultConfig().configs.INDEX_QUERY, {'top': 'SJ', 'middle': 'YS'});
                    tabController.animateTo(1);
                    store.dispatch(new UpdateCurrentTabIndexAction(1));
                  })
                ],
              )),
        ]));
  }

  /// 驿站数据
  _buildTopicStation(store) {
    return Container(
        margin: EdgeInsets.fromLTRB(15, 0, 15, 10),
        child: Row(mainAxisAlignment: MainAxisAlignment.spaceEvenly, children: [
          TopicItem(
            store,
            '点位',
            Color(0xFFF6AF3D),
            Color(0xFFF68C25),
            children: Row(mainAxisAlignment: MainAxisAlignment.spaceEvenly, children: [
              buildCountRender('pj', '待取件', waitPickUpNum, () {
                searchFocusNode.unfocus();
                LocalStorage.save(DefaultConfig().configs.INDEX_QUERY, {'top': 'GJ', 'middle': 'DQ'});
                tabController.animateTo(1);
                store.dispatch(new UpdateCurrentTabIndexAction(1));
              }),
              Container(
                height: 36,
                child: VerticalDivider(
                  color: Colors.white,
                  thickness: 1,
                  indent: 3,
                  endIndent: 3,
                ),
              ),
              buildCountRender('pj', '滞留件', keepEffectNum, () {
                searchFocusNode.unfocus();
                LocalStorage.save(DefaultConfig().configs.INDEX_QUERY, {'top': 'GJ', 'middle': 'ZL'});
                tabController.animateTo(1);
                store.dispatch(new UpdateCurrentTabIndexAction(1));
              })
            ]),
          ),
          Padding(padding: EdgeInsets.only(left: 10)),
          TopicItem(
            store,
            '货架',
            Color(0xFF7DC82A),
            Color(0xFF84BF41),
            children: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                buildCountRender('sj', '待取件', shelfWaitPickUpNum, () {
                  searchFocusNode.unfocus();
                  LocalStorage.save(DefaultConfig().configs.INDEX_QUERY, {'top': 'HJ', 'middle': 'DQ'});
                  tabController.animateTo(1);
                  store.dispatch(new UpdateCurrentTabIndexAction(1));
                }),
                Container(
                  height: 36,
                  child: VerticalDivider(
                    color: Colors.white,
                    thickness: 1,
                    indent: 3,
                    endIndent: 3,
                  ),
                ),
                buildCountRender('sj', '滞留件', shelfKeepEffectNum, () {
                  searchFocusNode.unfocus();
                  LocalStorage.save(DefaultConfig().configs.INDEX_QUERY, {'top': 'HJ', 'middle': 'ZL'});
                  tabController.animateTo(1);
                  store.dispatch(new UpdateCurrentTabIndexAction(1));
                })
              ],
            ),
          ),
        ]));
  }

  // ignore: non_constant_identifier_names
  Widget TopicItem(store, String title, Color bgColor, Color tagColor, {Widget? children}) {
    return Expanded(
      child: Container(
          height: 72,
          decoration: new BoxDecoration(
            color: bgColor,
            borderRadius: BorderRadius.all(
              Radius.circular(10.0),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 48,
                height: 20,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    color: tagColor,
                    borderRadius:
                        BorderRadius.only(topLeft: Radius.circular(10.0), bottomRight: Radius.circular(10.0))),
                child: Text(title,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      fontStyle: FontStyle.italic,
                      color: Colors.white,
                      letterSpacing: 2,
                    ),
                    textAlign: TextAlign.center),
              ),
              children ?? Container()
            ],
          )),
    );
  }

  /// 菜单
  Widget _buildMenu() {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.fromLTRB(10, 5, 10, 0),
      child: SizedBox(
          height: 270,
          child: Swiper(
            autoplay: false,
            duration: 800,
            loop: (operations.length / 12).ceil() > 1,
            itemWidth: MediaQuery.of(context).size.width,
            itemCount: (operations.length / 12).ceil(),
            itemBuilder: (BuildContext context, int index) {
              return Wrap(
                children: buildOperations(index),
              );
            },
            // pagination:
            // const SwiperPagination(builder: SwiperPagination.fraction),
            // pagination: SwiperPagination(
            //     builder: DotSwiperPaginationBuilder(
            //         size: 7, activeSize: 7, space: 3, color: Color.fromRGBO(0, 0, 0, 1))),
            pagination: SwiperPagination(
                alignment: Alignment.bottomCenter,
                builder: RectSwiperPaginationBuilder(
                    size: Size(12, 4),
                    activeSize: (operations.length / 12).ceil() > 1 ? Size(15, 4) : Size(0, 0),
                    space: 0,
                    color: Colors.grey.shade200,
                    activeColor: Theme.of(context).primaryColor)),
          )),
    );
  }

  Widget _buildTodaySummary() {
    return Container(
      margin: EdgeInsets.only(top: 10),
      padding: EdgeInsets.fromLTRB(15, 13, 15, 13),
      decoration: BoxDecoration(
        color: Colors.white,
      ),
      child: Column(
        children: [
          Row(
            children: [
              buildItem('通知失败', (homeSummary.smsErrorCount ?? 0).toStringAsFixed(0),
                  isUnderline: true, cb: () => goNext('noticeFail')),
              buildItem('物流同步失败', (homeSummary.expressGateWayErrorCount ?? 0).toStringAsFixed(0),
                  isUnderline: true, cb: () => goNext('syncFail')),
              buildItem('超3天滞留', (homeSummary.gt3dayKeepEffectCount ?? 0).toStringAsFixed(0),
                  isUnderline: true, cb: () => goNext('3Day')),
              buildItem('超7天滞留', (homeSummary.gt7dayKeepEffectCount ?? 0).toStringAsFixed(0),
                  isUnderline: true, cb: () => goNext('7Day'))
            ],
          ),
          Row(
            children: [
              buildItem('今日投递', (homeSummary.inboundCount ?? 0).toStringAsFixed(0)),
              buildItem('今日取出', (homeSummary.outboundCount ?? 0).toStringAsFixed(0)),
              !isCourier && user?.hasAdmin != 1
                  ? Expanded(
                      child: Text(''),
                    )
                  : buildItem('今日消费', ((homeSummary.outcome ?? 0) / 1000).toStringAsFixed(2)),
              !isCourier && user?.hasAdmin != 1
                  ? Expanded(
                      child: Text(''),
                    )
                  : buildItem('今日收入', ((homeSummary.income ?? 0) / 1000).toStringAsFixed(2))
            ],
          ),
        ],
      ),
    );
  }

  goNext(String type) {
    int? day = type == '3Day'
        ? 3
        : type == '7Day'
            ? 7
            : null;
    String? query = (type == 'syncFail' || type == 'noticeFail') ? type : null;
    NavigatorUtils.goPackageListPage(context, query != null ? PackageType.ALL : PackageType.ZL, null,
        keepEffectDay: day, query: query);
  }

  buildItem(String title, String count, {VoidCallback? cb, color, bool isUnderline = false}) {
    return Expanded(
        flex: 1,
        child: InkWell(
          onTap: () {
            if (cb != null) {
              cb();
            }
          },
          child: Container(
            height: 60,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(count,
                    style: TextStyle(
                        decoration: isUnderline ? TextDecoration.underline : TextDecoration.none,
                        color: color == null ? Theme.of(context).primaryColor : color,
                        fontSize: 22,
                        letterSpacing: 1,
                        fontWeight: FontWeight.w600)),
                Text(title, style: TextStyle(color: Colors.black, fontSize: 12, fontWeight: FontWeight.w400))
              ],
            ),
          ),
        ));
  }

  @override
  bool get wantKeepAlive => true;
}
