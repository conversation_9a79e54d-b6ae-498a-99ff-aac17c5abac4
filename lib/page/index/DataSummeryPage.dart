import 'dart:async';
import 'dart:convert';

import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/entitys/keep_effect_summary_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/today_summary_entity.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/ExpandablePanel.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_echarts/flutter_echarts.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:intl/intl.dart';

class DataSummeryPage extends StatefulWidget {
  final bool isCourier;

  DataSummeryPage(this.isCourier, {Key? key}) : super(key: key);

  @override
  _DataSummeryPageState createState() => _DataSummeryPageState();
}

class _DataSummeryPageState extends State<DataSummeryPage> {
  static const channel = String.fromEnvironment("CHANNEL");
  TodaySummaryEntity todaySummary = new TodaySummaryEntity();
  List brandList = [];
  KeepEffectSummaryEntity keepEffectSummary = new KeepEffectSummaryEntity();
  DateType deliveryType = DateType.near15Day;
  DateType pickType = DateType.near15Day;
  List inboundList = [];
  List outboundList = [];
  bool switchInboundStatus = true; // 15天图标和按月切换接口请求标记 默认true 请求成功false
  bool switchOutboundStatus = true; // 15天图标和按月切换接口请求标记 默认true 请求成功false
  String brandTime = DateFormat("yyyy-MM-dd")
      .format(DateTime.parse(DateFormat("yyyy-MM-dd HH:mm:ss").format(DateTime.now())).subtract(Duration(days: 1)));
  Map<String, dynamic> dateRangeTypeMap = {
    'yesterday': '昨日',
    'lastThreeDay': '近三天',
    'lastOneWeek': '近一周',
  };
  String dateRangeType = 'yesterday';
  
  // 新增：日期选择相关变量
  int selectedTabIndex = 0; // 0: 当天, 1: 昨天, 2: 近七天, 3: 自定义
  DateTime startDate = DateTime.now();
  DateTime endDate = DateTime.now();
  String dateRangeText = DateFormat("yyyy-MM-dd").format(DateTime.now());

  @override
  void initState() {
    super.initState();
    // 初始化日期
    startDate = DateTime.now();
    endDate = DateTime.now();
    dateRangeText = DateFormat("yyyy-MM-dd").format(startDate);
    onFresh();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<Null> onFresh() async {
    // 根据选择的日期范围获取数据
    String startDateStr = DateFormat("yyyy-MM-dd").format(startDate);
    String endDateStr = DateFormat("yyyy-MM-dd").format(endDate);
    
    if (!widget.isCourier) {
      // 如果是当天，使用昨天的日期获取品牌汇总数据 这里为什么过去昨日的时间主要是因为品牌汇总数据没有今日的
      if (selectedTabIndex == 0) {
        brandTime = DateFormat("yyyy-MM-dd")
          .format(DateTime.parse(DateFormat("yyyy-MM-dd HH:mm:ss").format(DateTime.now())).subtract(Duration(days: 1)));
          print("brandTime: $brandTime");
      } else {
        // 否则使用选择的日期范围
        brandTime = startDateStr;
      }
      await getBrandSummary(startDateStr, endDateStr);
    }
    
    // 根据选择的日期范围获取数据
    await getTodaySummary(startDateStr, endDateStr);
    await getKeepEffectSummary();
    await getInboundList();
    await getOutboundList();
    
    if (mounted) {
      setState(() {});
    }
  }

  getInboundList() async {
    // 构建日期参数
    String startDateStr = DateFormat("yyyy-MM-dd").format(startDate);
    String endDateStr = DateFormat("yyyy-MM-dd").format(endDate);
    Map<String, dynamic> params = {
      'startDay': startDateStr,
      'endDay': endDateStr,
    };
    
    if (deliveryType == DateType.near15Day) {
      await getHalfMonthInboundSummary(params);
    } else {
      await getHalfYearInboundSummary(params);
    }
  }

  getOutboundList() async {
    // 构建日期参数
    String startDateStr = DateFormat("yyyy-MM-dd").format(startDate);
    String endDateStr = DateFormat("yyyy-MM-dd").format(endDate);
    Map<String, dynamic> params = {
      'startDay': startDateStr,
      'endDay': endDateStr,
    };
    
    if (pickType == DateType.near15Day) {
      await getHalfMonthOutboundSummary(params);
    } else {
      await getHalfYearOutboundSummary(params);
    }
  }

  getBrandSummary(String startDate, String endDate) async {
    Map<String, dynamic> params = {'startDay': brandTime, "endDay": brandTime};
    DataResult res = await CourierDao.getBrandSummary(params);
    if (res.result) {
      setState(() {
        brandList = res.data;
      });
    }
  }

  getTodaySummary(String startDate, String endDate) async {
    // 构建参数，包含日期范围
    Map<String, dynamic> params = {
      'startDay': startDate,
      'endDay': endDate,
    };
    
    // 调用API获取数据，传入日期参数   这里转了params原先没有
    DataResult res = await CourierDao.getTodaySummary(params);
    if (res.result) {
      setState(() {
        todaySummary = TodaySummaryEntity.fromJson(res.data);
      });
    }
  }

  getHalfMonthInboundSummary([Map<String, dynamic>? params]) async {
    switchInboundStatus = false;
    DataResult res = await CourierDao.getHalfMonthInboundSummary(params);
    switchInboundStatus = true;
    if (res.result) {
      inboundList =
          res.data.map((item) => {'name': getAxisValue(deliveryType, item['ymd']), 'value': item['num']}).toList();
    } else {
      inboundList = [];
    }
  }

  getAxisValue(DateType type, String dateStr) {
    String str = '';
    if (type == DateType.near15Day) {
      str = DateFormat("MM.dd").format(DateTime.parse(dateStr));
    } else {
      str = DateFormat("MM月").format(DateTime.parse(dateStr));
    }
    return str;
  }

  getHalfYearInboundSummary([Map<String, dynamic>? params]) async {
    switchInboundStatus = false;
    DataResult res = await CourierDao.getHalfYearInboundSummary(params);
    switchInboundStatus = true;
    if (res.result) {
      inboundList = res.data.map((item) {
        return {'name': getAxisValue(deliveryType, item['ymd']), 'value': item['num']};
      }).toList();
    } else {
      inboundList = [];
    }
  }

  getHalfMonthOutboundSummary([Map<String, dynamic>? params]) async {
    switchOutboundStatus = false;
    DataResult res = await CourierDao.getHalfMonthOutboundSummary(params);
    switchOutboundStatus = true;
    if (res.result) {
      outboundList = res.data.map((item) {
        return {'name': getAxisValue(pickType, item['ymd']), 'value': item['num']};
      }).toList();
      ;
    } else {
      outboundList = [];
    }
  }

  getHalfYearOutboundSummary([Map<String, dynamic>? params]) async {
    switchOutboundStatus = false;
    DataResult res = await CourierDao.getHalfYearOutboundSummary(params);
    switchOutboundStatus = true;
    if (res.result) {
      outboundList = res.data.map((item) {
        return {'name': getAxisValue(pickType, item['ymd']), 'value': item['num']};
      }).toList();
      ;
    } else {
      outboundList = [];
    }
  }

  getKeepEffectSummary() async {
    // 构建日期参数
    String startDateStr = DateFormat("yyyy-MM-dd").format(startDate);
    String endDateStr = DateFormat("yyyy-MM-dd").format(endDate);
    Map<String, dynamic> params = {
      'startDay': startDateStr,
      'endDay': endDateStr,
    };
    
    DataResult res = await CourierDao.getKeepEffectSummary(params);
    if (res.result) {
      keepEffectSummary = KeepEffectSummaryEntity.fromJson(res.data);
    }
  }

  goNext(String type) {
    int? day = type == '3Day'
        ? 3
        : type == '7Day'
            ? 7
            : 0;
    NavigatorUtils.goPackageListPage(context, PackageType.ZL, null, keepEffectDay: day);
  }

  // 显示日期范围选择器
  Future<void> _showDateRangePicker() async {
    DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2019),
      lastDate: DateTime(2026),
      initialDateRange: DateTimeRange(
        start: startDate,
        end: endDate,
      ),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.light().copyWith(
            primaryColor: Theme.of(context).primaryColor,
            colorScheme: ColorScheme.light(
              primary: Theme.of(context).primaryColor,
            ),
            buttonTheme: ButtonThemeData(textTheme: ButtonTextTheme.primary),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        startDate = picked.start;
        endDate = picked.end;
        dateRangeText = "${DateFormat("yyyy-MM-dd").format(startDate)} 至 ${DateFormat("yyyy-MM-dd").format(endDate)}";
      });
      // 这里可以添加根据选择的日期范围获取数据的逻辑
      onFresh();
    }
  }

  // 构建日期选择标签
  Widget _buildDateTabs() {
    return Container(
      margin: EdgeInsets.fromLTRB(15, 13, 15, 0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(10.0)),
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(vertical: 10),
            child: Text(
              selectedTabIndex == 3 ? dateRangeText : 
              selectedTabIndex == 2 ? "${DateFormat("yyyy-MM-dd").format(DateTime.now().subtract(Duration(days: 6)))} 至 ${DateFormat("yyyy-MM-dd").format(DateTime.now())}" :
              selectedTabIndex == 1 ? "${DateFormat("yyyy-MM-dd").format(DateTime.now().subtract(Duration(days: 1)))}" :
              DateFormat("yyyy-MM-dd").format(DateTime.now()),
              style: TextStyle(fontSize: 16),
            ),
          ),
          Row(
            children: [
              _buildTab("当天", 0),
              _buildTab("昨天", 1),
              _buildTab("近7天", 2),
              _buildTab("自定义", 3),
            ],
          ),
        ],
      ),
    );
  }

  // 构建单个标签
  Widget _buildTab(String title, int index) {
    bool isSelected = selectedTabIndex == index;
    return Expanded(
      child: InkWell(
        onTap: () {
          setState(() {
            selectedTabIndex = index;
            if (index == 0) {
              // 当天
              startDate = DateTime.now();
              endDate = DateTime.now();
              dateRangeText = DateFormat("yyyy-MM-dd").format(startDate);
            } else if (index == 1) {
              // 近7天
              endDate = DateTime.now();
              startDate = endDate.subtract(Duration(days: 6));
              dateRangeText = "${DateFormat("yyyy-MM-dd").format(startDate)} 至 ${DateFormat("yyyy-MM-dd").format(endDate)}";
            } else if (index == 3) {
              // 自定义
              _showDateRangePicker();
            }
          });
          // 根据选择的日期范围刷新数据
          onFresh();
        },
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected ? Theme.of(context).primaryColor : Colors.transparent,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(index == 0 ? 0 : 0),
              bottomLeft: Radius.circular(index == 0 ? 10 : 0),
              topRight: Radius.circular(index == 3 ? 0 : 0),
              bottomRight: Radius.circular(index == 3 ? 10 : 0),
            ),
          ),
          child: Center(
            child: Text(
              title,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.black,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DefaultConfig().configs.BG_COLOR,
      appBar: new AppCustomerBar(
        title: AppbarTitle(
          title: '数据中心',
          isCenter: true,
        ),
        actions: [
          Container(
            width: 60,
          )
        ],
      ),
      body: RefreshIndicator(
        onRefresh: onFresh,
        child: new SingleChildScrollView(
          child: Column(
            children: <Widget>[
              // 添加日期选择标签
              _buildDateTabs(),
              
              /// 数据总览
              buildSummeryInfo(),
              /// 收支情况
              widget.isCourier ? Container() : buildIncomeExpenseInfo(),

              /// 品牌出入库总览
              // widget.isCourier ? Container() : brandDataList(),
            
              /// 出入库数据总览（合并图表）
              buildCombinedDataInfo(),
           
              
            ],
          ),
        ),
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }

  /// 数据总览
  buildSummeryInfo() {
    return Container(
      margin: EdgeInsets.fromLTRB(15, 13, 15, 13),
      padding: EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(10.0)),
      ),
      child: Column(
        children: [
          // 新增：运营概况入口
          InkWell(
            onTap: () {
              NavigatorUtils.goBusinessStatisticsPage(context); // 跳转到业务统计页面
            },
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 4),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text('运营概况', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: Colors.black)),
                  Icon(Icons.chevron_right, color: Colors.grey, size: 22),
                ],
              ),
            ),
          ),
          // 原有内容
          
          Row(
            children: [
              buildItem('投递量', (todaySummary.inboundCount ?? 0).toStringAsFixed(0)),
              buildItem('取出量', (todaySummary.outboundCount ?? 0).toStringAsFixed(0)),
              buildItem('消费', ((todaySummary.outcome ?? 0) / 1000).toStringAsFixed(2)),
              buildItem('收入', ((todaySummary.income ?? 0) / 1000).toStringAsFixed(2))
            ],
          ),
          SizedBox(height: 10),
          Container(
            width: double.infinity,
            height: 1,
            color: Color(0xFFEEEEEE),
          ),
          Row(
            children: [
              buildItem('当前滞留件', keepEffectSummary.keepEffectCount!.toStringAsFixed(0),
                  cb: () => goNext('zl'), color: DefaultConfig().configs.BLUE_COLOR),
              buildItem('超三天滞留', keepEffectSummary.gt3dayKeepEffectCount!.toStringAsFixed(0),
                  cb: () => goNext('3Day'), color: DefaultConfig().configs.BLUE_COLOR),
              buildItem('超七天滞留', keepEffectSummary.gt7dayKeepEffectCount!.toStringAsFixed(0),
                  cb: () => goNext('7Day'), color: DefaultConfig().configs.BLUE_COLOR),
            ],
          )
        ],
      ),
    );
  }

  buildItem(String title, String count, {VoidCallback? cb, color}) {
    return Expanded(
        flex: 1,
        child: InkWell(
          onTap: () {
            if (cb != null) {
              cb();
            }
          },
          child: Container(
            height: 60,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(count,
                    style: TextStyle(
                        color: color == null ? Theme.of(context).primaryColor : color,
                        fontSize: 22,
                        fontWeight: FontWeight.w400)),
                Text(title, style: TextStyle(color: Colors.black, fontSize: 14, fontWeight: FontWeight.w400))
              ],
            ),
          ),
        ));
  }

  /// 出入库数据总览（合并图表）
  buildCombinedDataInfo() {
    return Container(
      margin: EdgeInsets.fromLTRB(15, 0, 15, 13),
      padding: EdgeInsets.fromLTRB(20, 10, 20, 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(10.0)),
      ),
      child: Column(
        children: [
          buildCombinedTitle('出入库数据总览', deliveryType, (item) async {
            deliveryType = item;
            pickType = item; // 同步两个图表的时间类型
            await getInboundList();
            await getOutboundList();
            setState(() {});
          }),
          Container(
            width: double.infinity,
            height: 270.0,
            padding: EdgeInsets.fromLTRB(0.0, 10.0, 0.0, 10.0),
            child: (switchInboundStatus && switchOutboundStatus) ? buildCombinedChart() : buildLoading(),
          )
        ],
      ),
    );
  }

  /// 投递数量总览
  buildDeliveryInfo() {
    return Container(
      margin: EdgeInsets.fromLTRB(15, 0, 15, 13),
      padding: EdgeInsets.fromLTRB(20, 10, 20, 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(10.0)),
      ),
      child: Column(
        children: [
          buildTitle('投递数据总览', deliveryType, (item) async {
            deliveryType = item;
            await getInboundList();
            setState(() {});
          }),
          Container(
            width: double.infinity,
            height: 220.0,
            padding: EdgeInsets.fromLTRB(0.0, 10.0, 0.0, 10.0),
            child: switchInboundStatus ? buildInboundChart() : buildLoading(),
          )
        ],
      ),
    );
  }

  static getChannelPrimaryColor() {
    String color = '#FF6600';
    switch (channel) {
      case 'bl':
        color = '#409eff';
        break;
      case 'xm':
        color = '#FF6600';
        break;
    }
    return color;
  }

  /// 入库折线图
  buildInboundChart() {
    EasyLoading.dismiss();
    String color = getChannelPrimaryColor();
    String options = '''
    {
                dataset: {
                  dimensions: ['name', 'value'],
                  source: ${jsonEncode(inboundList)}
                },
                grid: { 
                  left: '0%',
                  right: '0%',
                  bottom: '5%',
                  top: '7%',
                  height: '85%',
                  containLabel: true,
                  z: 22,
                },
                tooltip: {
                  trigger: 'axis',
                  formatter: function(params) {
                    let returnData = params[0].name + '  '
                    for (let i = 0; i < params.length; i++) {
                      if (params[i].seriesName !== '') {
                        let indexColor = params[i].color
                        let indexValue = params[i].value.value
                        returnData += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background:' + indexColor + '"></span>'
                        returnData += '投递数量:' + indexValue
                      }
                    }
                    return returnData
                  }
                },
                xAxis: [{
                  type: 'category',
                  gridIndex: 0,
                  axisTick: {
                    show: true
                  },
                  boundaryGap: true,
                  axisLabel: {
                    show: true,
                    color: 'rgb(170,170,170)',
                    formatter: function xFormatter(value, index) {
                      return value
                    }
                  }
                }],
                yAxis: {
                  type: 'value',
                  gridIndex: 0,
                  splitLine: {
                    show: true
                  },
                  axisTick: {
                    show: false
                  },
                  axisLine: {
                    lineStyle: {
                      color: '#0c3b71'
                    }
                  },
                  axisLabel: {
                    color: 'rgb(170,170,170)'
                  },
                  splitNumber: 3
                },
                series: [{
                  lineStyle: {
                    color: '$color'
                  },
                  itemStyle: {
                    normal: {
                      color: '$color',
                      label: {
                        show: true
                      }
                    }
                  },
                  type: 'line',
                  smooth: true,
                  zlevel: 11
                }]
              }
      ''';
    return Echarts(option: options);
  }

  brandDataList() {
    return Column(
      children: brandList.map((item) {
        return Container(
          margin: EdgeInsets.fromLTRB(15, 0, 15, 13),
          padding: EdgeInsets.fromLTRB(20, 10, 20, 10),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(10.0)),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 30,
                        height: 30,
                        child: CircleAvatar(
                          radius: 10.0,
                          backgroundColor: Colors.white,
                          backgroundImage: AssetImage(CommonUtils.getExpressLogo(item['brand'])),
                        ),
                        margin: EdgeInsets.fromLTRB(0, 0, 10, 0),
                      ),
                      Text(DefaultConfig().configs.EXPRESS2[item['brand']],
                          style: TextStyle(color: Colors.black, fontSize: 14, fontWeight: FontWeight.w400)),
                    ],
                  ),
                  Text('昨日入库：${item['inNum']}',
                      style: TextStyle(color: Colors.black, fontSize: 14, fontWeight: FontWeight.w400)),
                  Text('昨日出库：${item['outNum']}',
                      style: TextStyle(color: Colors.black, fontSize: 14, fontWeight: FontWeight.w400))
                ],
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  /// 取出数量总览
  buildPickInfo() {
    return Container(
      margin: EdgeInsets.fromLTRB(15, 0, 15, 13),
      padding: EdgeInsets.fromLTRB(20, 10, 20, 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(10.0)),
      ),
      child: Column(
        children: [
          buildTitle('取出数量总览', pickType, (item) async {
            pickType = item;
            await getOutboundList();
            setState(() {});
          }),
          Container(
            width: double.infinity,
            height: 220.0,
            padding: EdgeInsets.fromLTRB(0.0, 10.0, 0.0, 10.0),
            child: switchOutboundStatus ? buildOutboundChart() : buildLoading(),
          )
        ],
      ),
    );
  }

  /// 出库折线图
  buildOutboundChart() {
    EasyLoading.dismiss();
    String color = getChannelPrimaryColor();
    String options = '''
    {
                dataset: {
                  dimensions: ['name', 'value'],
                  source: ${jsonEncode(outboundList)}
                },
                grid: { 
                  left: '0%',
                  right: '0%',
                  bottom: '5%',
                  top: '7%',
                  height: '85%',
                  containLabel: true,
                  z: 22,
                },
                tooltip: {
                  trigger: 'axis',
                  formatter: function(params) {
                    let returnData = params[0].name + '  '
                    for (let i = 0; i < params.length; i++) {
                      if (params[i].seriesName !== '') {
                        let indexColor = params[i].color
                        let indexValue = params[i].value.value
                        returnData += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background:' + indexColor + '"></span>'
                        returnData += '取出数量:' + indexValue
                      }
                    }
                    return returnData
                  }
                },
                xAxis: [{
                  type: 'category',
                  gridIndex: 0,
                  axisTick: {
                    show: true
                  },
                  boundaryGap: true,
                  axisLabel: {
                    show: true,
                    color: 'rgb(170,170,170)',
                    formatter: function xFormatter(value, index) {
                      return value
                    }
                  }
                }],
                yAxis: {
                  type: 'value',
                  gridIndex: 0,
                  splitLine: {
                    show: true
                  },
                  axisTick: {
                    show: false
                  },
                  axisLine: {
                    lineStyle: {
                      color: '#0c3b71'
                    }
                  },
                  axisLabel: {
                    color: 'rgb(170,170,170)'
                  },
                  splitNumber: 3
                },
                series: [{
                  lineStyle: {
                    color: '$color'
                  },
                  itemStyle: {
                    normal: {
                      color: '$color',
                      label: {
                        show: true
                      }
                    }
                  },
                  type: 'line',
                  smooth: true,
                  zlevel: 11
                }]
              }
      ''';
    return Echarts(option: options);
  }

  /// 合并的出入库折线图
  buildCombinedChart() {
    EasyLoading.dismiss();

    // 合并数据，确保两个数据集有相同的x轴
    List<Map<String, dynamic>> combinedData = [];

    // 创建一个包含所有日期的集合
    Set<String> allDates = {};
    for (var item in inboundList) {
      allDates.add(item['name']);
    }
    for (var item in outboundList) {
      allDates.add(item['name']);
    }

    // 按日期排序
    List<String> sortedDates = allDates.toList()..sort();

    // 为每个日期创建数据点
    for (String date in sortedDates) {
      var inboundItem = inboundList.firstWhere(
        (item) => item['name'] == date,
        orElse: () => {'name': date, 'value': 0}
      );
      var outboundItem = outboundList.firstWhere(
        (item) => item['name'] == date,
        orElse: () => {'name': date, 'value': 0}
      );

      combinedData.add({
        'name': date,
        'inbound': inboundItem['value'],
        'outbound': outboundItem['value']
      });
    }

    String options = '''
    {
                dataset: {
                  dimensions: ['name', 'inbound', 'outbound'],
                  source: ${jsonEncode(combinedData)}
                },
                grid: {
                  left: '0%',
                  right: '2%',
                  bottom: '8%',
                  top: '20%',
                  height: '75%',
                  containLabel: true,
                  z: 22,
                },
                legend: {
                  data: ['入库', '出库'],
                  top: '0%',
                  right: '0%',
                  itemWidth: 10,
                  itemHeight: 10,
                  textStyle: {
                    fontSize: 12
                  }
                },
                tooltip: {
                  trigger: 'axis',
                  formatter: function(params) {
                    let returnData = params[0].name + '<br/>'
                    for (let i = 0; i < params.length; i++) {
                      let indexColor = params[i].color
                      let indexValue = params[i].value[params[i].seriesName === '入库' ? 'inbound' : 'outbound']
                      returnData += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background:' + indexColor + '"></span>'
                      returnData += params[i].seriesName + ':' + indexValue + '<br/>'
                    }
                    return returnData
                  }
                },
                xAxis: [{
                  type: 'category',
                  gridIndex: 0,
                  axisTick: {
                    show: true
                  },
                  boundaryGap: true,
                  axisLabel: {
                    show: true,
                    color: 'rgb(170,170,170)',
                    formatter: function xFormatter(value, index) {
                      return value
                    }
                  }
                }],
                yAxis: {
                  type: 'value',
                  gridIndex: 0,
                  splitLine: {
                    show: true
                  },
                  axisTick: {
                    show: false
                  },
                  axisLine: {
                    lineStyle: {
                      color: '#0c3b71'
                    }
                  },
                  axisLabel: {
                    color: 'rgb(170,170,170)'
                  },
                  splitNumber: 3
                },
                series: [{
                  name: '入库',
                  type: 'line',
                  smooth: true,
                  lineStyle: {
                    color: '#409eff'
                  },
                  itemStyle: {
                    normal: {
                      color: '#409eff',
                      label: {
                        show: true,
                        position: 'top',
                        fontSize: 10,
                        offset: [0, -5]
                      }
                    }
                  },
                  encode: {
                    x: 'name',
                    y: 'inbound'
                  },
                  zlevel: 11
                }, {
                  name: '出库',
                  type: 'line',
                  smooth: true,
                  lineStyle: {
                    color: '#FF6600'
                  },
                  itemStyle: {
                    normal: {
                      color: '#FF6600',
                      label: {
                        show: true,
                        position: 'top',
                        fontSize: 10,
                        offset: [0, -5]
                      }
                    }
                  },
                  encode: {
                    x: 'name',
                    y: 'outbound'
                  },
                  zlevel: 11
                }]
              }
      ''';
    return Echarts(option: options);
  }

  buildLoading() {
    return SpinKitFadingCircle(
      color: Theme.of(context).primaryColor,
      size: 55,
    );
  }

  /// 合并图表的标题
  buildCombinedTitle(String title, DateType type, ValueChanged cb) {
    return Container(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title, style: TextStyle(fontSize: 15, fontWeight: FontWeight.w400, color: Colors.black)),
          Container(
            child: Row(
              children: [
                InkWell(
                    onTap: () {
                      cb(DateType.near15Day);
                    },
                    child: Container(
                        child: Column(
                      children: [
                        Text('近15天'),
                        Container(
                            width: 30,
                            height: 2,
                            color: type == DateType.near15Day ? Theme.of(context).primaryColor : Colors.transparent)
                      ],
                    ))),
                Padding(padding: EdgeInsets.only(left: 10)),
                InkWell(
                  onTap: () {
                    cb(DateType.monthly);
                  },
                  child: Container(
                    child: Column(
                      children: [
                        Text('按月查看'),
                        Container(
                            width: 30,
                            height: 2,
                            color: type == DateType.monthly ? Theme.of(context).primaryColor : Colors.transparent)
                      ],
                    ),
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  buildTitle(String title, DateType type, ValueChanged cb) {
    return Container(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title, style: TextStyle(fontSize: 15, fontWeight: FontWeight.w400, color: Colors.black)),
          Container(
            child: Row(
              children: [
                InkWell(
                    onTap: () {
                      cb(DateType.near15Day);
                    },
                    child: Container(
                        child: Column(
                      children: [
                        Text('近15天'),
                        Container(
                            width: 30,
                            height: 2,
                            color: type == DateType.near15Day ? Theme.of(context).primaryColor : Colors.transparent)
                      ],
                    ))),
                Padding(padding: EdgeInsets.only(left: 10)),
                InkWell(
                  onTap: () {
                    cb(DateType.monthly);
                  },
                  child: Container(
                    child: Column(
                      children: [
                        Text('按月查看'),
                        Container(
                            width: 30,
                            height: 2,
                            color: type == DateType.monthly ? Theme.of(context).primaryColor : Colors.transparent)
                      ],
                    ),
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  /// 收支情况
  buildIncomeExpenseInfo() {
    return Container(
      margin: EdgeInsets.fromLTRB(15, 0, 15, 13),
      padding: EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(10.0)),
      ),
      child: ExpandablePanel(
        header: Text('今日收支情况',
            style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w400,
                color: Colors.black
            )
        ),
        body: Column(
          children: [
            _buildIncomeItem('暂存费收入', todaySummary.storeIncome ?? 0),
            _buildIncomeItem('寄件费收入', todaySummary.sendIncome ?? 0),
            _buildIncomeItem('投柜费收入', todaySummary.dispatchIncome ?? 0),
            _buildIncomeItem('用户超期费收入', todaySummary.dispatchOverTimeIncome ?? 0),
            _buildIncomeItem('暂存超期费收入', todaySummary.keepOverdueIncome ?? 0),
            _buildIncomeItem('预约格口费用收入', todaySummary.bookIncome ?? 0),
            _buildIncomeItem('租用格口收入', todaySummary.rentIncome ?? 0),
            Divider(height: 16),
            _buildExpenseItem('短信费支出', todaySummary.smsOutgo ?? 0),
            _buildExpenseItem('微信通知费支出', todaySummary.wxOutgo ?? 0),
            _buildExpenseItem('代发短信支出', todaySummary.virtualSmsOutgo ?? 0),
            _buildExpenseItem('服务费支出', todaySummary.serviceFeeOutgo ?? 0),
            _buildExpenseItem('补到派支出', todaySummary.bdpoutgo ?? 0),
            _buildExpenseItem('日服务费支出', todaySummary.dayServiceFeeOutgo ?? 0),
            _buildExpenseItem('代入库费支出', todaySummary.replInboundoutgo ?? 0),
            _buildExpenseItem('金额预警支出', todaySummary.balanceWarningOutgo ?? 0),
            _buildExpenseItem('流量卡充值支出', todaySummary.simCardRechargeOutgo ?? 0),
            _buildExpenseItem('流量卡续费支出', todaySummary.simcardRenewOutgo ?? 0),
          ],
        ),
      ),
    );
  }

  Widget _buildIncomeItem(String title, int amount) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title),
          Text('¥${(amount / 1000).toStringAsFixed(2)}',
            style: TextStyle(color: Colors.green),
          ),
        ],
      ),
    );
  }

  Widget _buildExpenseItem(String title, int amount) {
    // print('amount = $amount title = $title');
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title),
          Text('¥${(amount / 1000).toStringAsFixed(2)}',
            style: TextStyle(color: Colors.red),
          ),
        ],
      ),
    );
  }
}
