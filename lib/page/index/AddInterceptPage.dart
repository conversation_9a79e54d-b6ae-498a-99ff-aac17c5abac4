import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/DebounceUtils.dart';
import 'package:cabinet_flutter_app/common/utils/ScanUtils.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:cabinet_flutter_app/widget/BottomBtnWidget.dart';
import 'package:intl/intl.dart';
// import 'package:flutter/services.dart';
import 'package:cabinet_flutter_app/widget/AppDropdownWidget.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';

class AddInterceptPage extends StatefulWidget {
  final dynamic interceptData;

  AddInterceptPage({Key? key, this.interceptData}) : super(key: key);

  @override
  _AddInterceptPageState createState() => _AddInterceptPageState();
}

class _AddInterceptPageState extends State<AddInterceptPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  TextEditingController trackingNumberController = TextEditingController();
  TextEditingController interceptInfoController = TextEditingController();
  TextEditingController expireTimeController = TextEditingController();

  String? selectedExpressCompany;
  DateTime? selectedExpireTime;
  bool isLoading = false;
  bool isDetectingCompany = false;

  // 快递公司列表
  List<Map<String, String>> expressCompanies = [
    {'label': '顺丰速运', 'value': 'SF'},
    {'label': '中通快递', 'value': 'ZTO'},
    {'label': '圆通速递', 'value': 'YTO'},
    {'label': '申通快递', 'value': 'STO'},
    {'label': '韵达速递', 'value': 'YUNDA'},
    {'label': '极兔速递', 'value': 'JT'},
    {'label': '百世快递', 'value': 'HTKY'},
    {'label': '德邦快递', 'value': 'DBKD'},
    {'label': '京东快递', 'value': 'JD'},
    {'label': '邮政EMS', 'value': 'EMS'},
    {'label': '邮政', 'value': 'POSTB'},
    {'label': '天天快递', 'value': 'TTKDEX'},
    {'label': '百世快递', 'value': 'HTKY'},
    {'label': '丰网', 'value': 'FW'},
    {'label': '国通快递', 'value': 'GTO'},
    {'label': '全峰快递', 'value': 'QFKD'},
    {'label': '优速快递', 'value': 'UC'},
    {'label': '快捷快递', 'value': 'FAST'},
    {'label': '天猫', 'value': 'TM'},
    {'label': '苏宁物流', 'value': 'SNWL'},
    {'label': '菜鸟', 'value': 'CAINIAO'},
    {'label': '其他', 'value': 'UNKNOW'},
  ];

  @override
  void initState() {
    super.initState();
    if (widget.interceptData != null) {
      _initializeData();
    }
  }
  
  // 处理扫描结果
  void _handleScanResult(String scanResult) {
    print('扫描结果：$scanResult');
    setState(() {
      trackingNumberController.text = scanResult.trim();
    });
    
    // 自动识别快递公司
    _onTrackingNumberChanged(scanResult.trim());
    
    Fluttertoast.showToast(
      msg: '已扫描到单号：${scanResult.trim()}',
      toastLength: Toast.LENGTH_SHORT,
    );
  }
  
  // 触发扫描
  void _startScan() async {
    try {
      // 跳转到扫描页面
      String? result = await NavigatorUtils.goCabinetBindPage(context, scanAction: ScanAction.SCANTRACKING);
      if (result != null && result.isNotEmpty) {
        _handleScanResult(result);
      }
    } catch (e) {
      print('启动扫描失败: $e');
      Fluttertoast.showToast(
        msg: '启动扫描失败，请手动输入',
        toastLength: Toast.LENGTH_SHORT,
      );
    }
  }

  void _initializeData() {
    var data = widget.interceptData;
    trackingNumberController.text = data['trackingNumber'] ?? '';
    interceptInfoController.text = data['interceptInfo'] ?? '';
    selectedExpressCompany = data['expressCompany'];

    // 初始化过期时间
    if (data['expireTime'] != null) {
      try {
        selectedExpireTime = DateTime.parse(data['expireTime']);
        expireTimeController.text = _formatDateTime(selectedExpireTime!);
      } catch (e) {
        // 解析失败时忽略
      }
    }
  }

  String _formatDateTime(DateTime dateTime) {

    if (dateTime == null) return '';
    return DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime); //这个更方便
    // return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // 自动识别快递公司
  Future<void> _detectExpressCompany(String trackingNumber) async {
    if (trackingNumber.trim().isEmpty) {
      setState(() {
        selectedExpressCompany = null;
        isDetectingCompany = false;
      });
      return;
    }

    setState(() {
      isDetectingCompany = true;
    });

    try {
      String companyCode = await ScanUtils.getCompany(context, trackingNumber.trim());
      
      if (companyCode.isNotEmpty) {
        // 检查识别到的公司代码是否在我们的列表中
        bool found = expressCompanies.any((company) => company['value'] == companyCode);
        
        if (found) {
          setState(() {
            selectedExpressCompany = companyCode;
            isDetectingCompany = false;
          });
          
          // 找到对应的公司名称并显示提示
          String? companyName = expressCompanies.firstWhere(
            (company) => company['value'] == companyCode,
            orElse: () => {'label': '未知', 'value': ''}
          )['label'];
          
          if (companyName != null && companyName.isNotEmpty) {
            Fluttertoast.showToast(
              msg: '已自动识别为：$companyName',
              toastLength: Toast.LENGTH_SHORT,
            );
          }
        } else {
          setState(() {
            selectedExpressCompany = null;
            isDetectingCompany = false;
          });
          Fluttertoast.showToast(
            msg: '无法识别该快递公司，请手动选择',
            toastLength: Toast.LENGTH_SHORT,
          );
        }
      } else {
        setState(() {
          selectedExpressCompany = null;
          isDetectingCompany = false;
        });
      }
    } catch (e) {
      setState(() {
        selectedExpressCompany = null;
        isDetectingCompany = false;
      });
      print('快递公司识别失败: $e');
    }
  }

  // 处理快递单号输入变化
  void _onTrackingNumberChanged(String value) {
    DebounceUtils.debounce(() {
      _detectExpressCompany(value);
    }, 500);
  }

  Future<void> _selectExpireTime() async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: selectedExpireTime ?? DateTime.now().add(Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(Duration(days: 365)),
    );

    if (pickedDate != null) {
      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime:
            TimeOfDay.fromDateTime(selectedExpireTime ?? DateTime.now()),
      );

      if (pickedTime != null) {
        setState(() {
          selectedExpireTime = DateTime(
            pickedDate.year,
            pickedDate.month,
            pickedDate.day,
            pickedTime.hour,
            pickedTime.minute,
          );
          expireTimeController.text = _formatDateTime(selectedExpireTime!);
        });
      }
    }
  }

  bool _validateForm() {
    if (trackingNumberController.text.trim().isEmpty) {
      Fluttertoast.showToast(msg: '请输入快递单号');
      return false;
    }

    if (selectedExpressCompany == null || selectedExpressCompany!.isEmpty) {
      Fluttertoast.showToast(msg: '请选择快递公司');
      return false;
    }

    if (interceptInfoController.text.trim().isEmpty) {
      Fluttertoast.showToast(msg: '请输入拦截信息');
      return false;
    }

    return true;
  }

  Future<void> _saveIntercept() async {
    if (!_validateForm()) {
      return;
    }

    setState(() {
      isLoading = true;
    });
    
    try {
      // 根据ShopWaybillInterceptAddReq参数要求构建请求数据
      Map<String, dynamic> data = {
        'waybillNo': trackingNumberController.text.trim(), // 单号不能为空
        'brandCode': selectedExpressCompany, // 品牌编码不能为空
        'message': interceptInfoController.text.trim(), // 拦截提示信息
      };
        // 仅当 selectedExpireTime 有值时才添加 expireTime 字段
    if (selectedExpireTime != null) {
      data['expireTime'] = _formatDateTime(selectedExpireTime!);
    }
        print('提交数据: $data');
      // 调用shop.waybill.intercept.add接口
      DataResult result = await CourierDao.addInterceptNo(data);
      print('添加拦截件结果: ${result.result}');
      if (result.result) {
        Fluttertoast.showToast(msg: '添加成功');
        Navigator.of(context).pop(true);
      } else {
        Fluttertoast.showToast(msg: '添加失败');
      }
    } catch (e) {
      Fluttertoast.showToast(msg: '添加失败: $e');
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: Colors.grey.shade100,
      appBar: AppCustomerBar(
        title: AppbarTitle(
          title: widget.interceptData != null ? '编辑拦截件' : '添加拦截件',
          isCenter: true,
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 快递单号输入
                  Container(
                    height: 70,
                    margin: EdgeInsets.only(bottom: 16),
                    padding: EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.1),
                          spreadRadius: 1,
                          blurRadius: 3,
                          offset: Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          width: 80,
                          child: Text(
                            '快递单号：',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                        Expanded(
                          child: TextField(
                            textCapitalization: TextCapitalization.characters,
                            // inputFormatters: [
                            //   FilteringTextInputFormatter.allow(
                            //       RegExp(r'[A-Z0-9]')),
                            // ],
                            onChanged: _onTrackingNumberChanged,
                            controller: trackingNumberController,
                            decoration: InputDecoration(
                              hintText: isDetectingCompany ? '正在识别...' : '请输入快递单号',
                              hintStyle: TextStyle(
                                fontSize: 15,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(4),
                                borderSide:
                                    BorderSide(color: Colors.grey.shade300),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(4),
                                borderSide:
                                    BorderSide(color: Colors.grey.shade300),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(4),
                                borderSide: BorderSide(
                                    color: Theme.of(context).primaryColor),
                              ),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 16),
                              counterText: '',
                              suffixIcon: IconButton(
                                icon: Icon(
                                  Icons.qr_code_scanner,
                                  color: Theme.of(context).primaryColor,
                                  size: 24,
                                ),
                                onPressed: _startScan,
                                tooltip: '扫描快递单号',
                              ),
                            ),
                            maxLength: 50,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // 快递公司选择
                  Container(
                    height: 70,
                    margin: EdgeInsets.only(bottom: 16),
                    padding: EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.1),
                          spreadRadius: 1,
                          blurRadius: 3,
                          offset: Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          width: 80,
                          child: Row(
                            children: [
                              Text(
                                '快递公司',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                              ),
                              if (isDetectingCompany)
                                Container(
                                  margin: EdgeInsets.only(left: 4),
                                  width: 12,
                                  height: 12,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Theme.of(context).primaryColor,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 4),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey.shade300),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: DropdownButtonHideUnderline(
                              child: DropdownButton<String>(
                                value: selectedExpressCompany,
                                hint: Text(isDetectingCompany ? '正在识别快递公司...' : '请选择快递公司'),
                                style: TextStyle(
                                  fontSize: 15,
                                  color: Colors.black87,
                                ),
                                isExpanded: true,
                                items: expressCompanies.map((company) {
                                  return DropdownMenuItem<String>(
                                    value: company['value'],
                                    child: Text(company['label']!),
                                  );
                                }).toList(),
                                onChanged: (String? value) {
                                  setState(() {
                                    selectedExpressCompany = value;
                                  });
                                },
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // 拦截信息输入
                  Container(
                    height: 70,
                    margin: EdgeInsets.only(bottom: 16),
                    padding: EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.1),
                          spreadRadius: 1,
                          blurRadius: 3,
                          offset: Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 80,
                          padding: EdgeInsets.only(top: 16),
                          child: Text(
                            '拦截信息',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                        Expanded(
                          child: TextField(
                            controller: interceptInfoController,
                            decoration: InputDecoration(
                              hintText: '请输入拦截原因或备注信息',
                              hintStyle: TextStyle(
                                fontSize: 15,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(4),
                                borderSide:
                                    BorderSide(color: Colors.grey.shade300),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(4),
                                borderSide:
                                    BorderSide(color: Colors.grey.shade300),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(4),
                                borderSide: BorderSide(
                                    color: Theme.of(context).primaryColor),
                              ),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 16),
                              counterText: '',
                            ),
                            maxLength: 200,
                            maxLines: 1,
                            minLines: 1,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // 过期时间选择
                  Container(
                    height: 70,
                    margin: EdgeInsets.only(bottom: 16),
                    padding: EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.1),
                          spreadRadius: 1,
                          blurRadius: 3,
                          offset: Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          width: 80,
                          child: Text(
                            '过期时间',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                        Expanded(
                          child: GestureDetector(
                            onTap: _selectExpireTime,
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 13),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey.shade300),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    expireTimeController.text.isEmpty
                                        ? '请选择过期时间（可选）'
                                        : expireTimeController.text,
                                    style: TextStyle(
                                      color: expireTimeController.text.isEmpty
                                          ? Colors.grey.shade600
                                          : Colors.black87,
                                      fontSize: 15,
                                    ),
                                  ),
                                  Row(
                                    children: [
                                      if (expireTimeController.text.isNotEmpty)
                                        GestureDetector(
                                          onTap: () {
                                            setState(() {
                                              selectedExpireTime = null;
                                              expireTimeController.clear();
                                            });
                                          },
                                          child: Icon(
                                            Icons.clear,
                                            color: Colors.grey.shade600,
                                            size: 20,
                                          ),
                                        ),
                                      SizedBox(width: 8),
                                      Icon(
                                        Icons.access_time,
                                        color: Colors.grey.shade600,
                                        size: 20,
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // 使用说明
                  Container(
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: Colors.blue.shade600,
                              size: 20,
                            ),
                            SizedBox(width: 8),
                            Text(
                              '使用说明',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue.shade600,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 8),
                        Text(
                          '1. 请确保快递单号输入正确\n2. 选择对应的快递公司\n3. 详细填写拦截原因，便于后续处理\n4. 过期时间可选，默认为一天后自动失效',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.blue.shade700,
                            height: 1.4,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // 底部按钮
          BottomBtnWidget(
            showShadow: true,
            type: ButtonType.primary,
            title: isLoading ? '保存中...' : '保存',
            action: isLoading ? null : _saveIntercept,
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    trackingNumberController.dispose();
    interceptInfoController.dispose();
    expireTimeController.dispose();
    super.dispose();
  }
}
