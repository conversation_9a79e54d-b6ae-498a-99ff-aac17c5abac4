import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/utils/BrandUtil.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class BusinessStatisticsPage extends StatefulWidget {
  BusinessStatisticsPage({Key? key}) : super(key: key);

  @override
  _BusinessStatisticsPageState createState() => _BusinessStatisticsPageState();
}

class _BusinessStatisticsPageState extends State<BusinessStatisticsPage> with SingleTickerProviderStateMixin {
  // Tab控制器
  late TabController _tabController;

  // 当前选中的tab索引
  int _currentTabIndex = 0;

  // 时间选择相关
  DateTime _startDate = DateTime.now().subtract(Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String _selectedTimeRange = '近7天'; // 当前选中的时间范围

  // 筛选相关
  List<String> _selectedFilters = []; // 当前选中的筛选项列表
  Map<String, String> _brandMap = {}; // 快递公司列表
  List<Map<String, dynamic>> _staffList = []; // 业务员列表
  List<Map<String, dynamic>> _employeeList = []; // 员工列表

  // 统计数据
  Map<String, dynamic> _statisticsData = {
    'inboundCount': 1403,
    'waitInboundCount': 0,
    'signCount': 1524,
    'returnCount': 56,
    'problemCount': 0,
    'businessFailCount': 0,
  };

  // 每日明细数据
  List<Map<String, dynamic>> _dailyDetails = [
    {'date': '06/29', 'inbound': 1232, 'waitInbound': 0,  'problem': 0, 'return': 0},
    {'date': '06/28', 'inbound': 233, 'waitInbound': 0,  'problem': 2323, 'return': 0},
    {'date': '06/27', 'inbound': 3423, 'waitInbound': 0,  'problem': 0, 'return': 0},
    {'date': '06/26', 'inbound': 123, 'waitInbound': 0,  'problem': 0, 'return': 0},
    {'date': '06/25', 'inbound': 123, 'waitInbound': 0,  'problem': 0, 'return': 0},
  ];

  // 展开状态管理
  Set<int> _expandedRows = <int>{}; // 记录哪些行是展开的
  Map<int, List<Map<String, dynamic>>> _detailData = {}; // 存储每行的详细数据



  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        setState(() {
          _currentTabIndex = _tabController.index;
          _selectedFilters.clear();
        });
        _loadFilterData();
        _loadStatisticsData();
      }
    });
    _loadFilterData();
    _loadStatisticsData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFF5F5F5),
      appBar: AppCustomerBar(
        title: AppbarTitle(
          title: '业务统计',
          isCenter: true,
        ),
        actions: [
          Container(width: 60),
        ],
      ),
      body: Column(
        children: [
          // Tab切换栏
          _buildTabBar(),
          // 时间选择和筛选
          _buildTimeAndFilterSelector(),
          // 统计卡片
          _buildStatisticsCards(),
          // 每日明细
          Expanded(
            child: _buildDailyDetails(),
          ),
        ],
      ),
    );
  }

  /// 加载统计数据
  Future<void> _loadStatisticsData() async {
    try {
      // 构建API参数
      Map<String, dynamic> params = {
        'startDate': DateFormat('yyyy-MM-dd').format(_startDate),
        'endDate': DateFormat('yyyy-MM-dd').format(_endDate),
        'type': _getTabType(),
      };

      // 调用统计数据汇总接口
      DataResult summaryResult = await CourierDao.getBusinessStatisticsSummary(params);
      if (summaryResult.result) {
        setState(() {
          _statisticsData = summaryResult.data ?? _statisticsData;
        });
      }

      // 调用每日明细接口
      DataResult dailyResult = await CourierDao.getBusinessStatisticsDaily(params);
      if (dailyResult.result) {
        setState(() {
          _dailyDetails = dailyResult.data ?? _dailyDetails;
        });
      }

    } catch (e) {
      print('加载统计数据失败: $e');
      // 可以在这里显示错误提示给用户
    }
  }

  /// 获取当前tab类型
  String _getTabType() {
    switch (_currentTabIndex) {
      case 0:
        return 'courier'; // 按快递公司
      case 1:
        return 'staff'; // 按业务员
      case 2:
        return 'employee'; // 按员工
      default:
        return 'courier';
    }
  }

  /// 构建Tab切换栏
  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: DefaultConfig().configs.PRIMARY_COLOR,
        unselectedLabelColor: Colors.grey[600],
        indicatorColor: DefaultConfig().configs.PRIMARY_COLOR,
        indicatorWeight: 2.0,
        labelStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        unselectedLabelStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.normal),
        tabs: [
          Tab(text: '按快递公司'),
          Tab(text: '按业务员'),
          Tab(text: '按员工'),
        ],
      ),
    );
  }

  /// 加载筛选数据
  Future<void> _loadFilterData() async {
    switch (_currentTabIndex) {
      case 0: // 按快递公司
        await _loadBrandList();
        break;
      case 1: // 按业务员
        await _loadStaffList();
        break;
      case 2: // 按员工
        await _loadEmployeeList();
        break;
    }
  }

  /// 加载快递公司列表
  Future<void> _loadBrandList() async {
    try {
      var res = await BrandUtils.getBrandBindList();
      if (res != null) {
        setState(() {
          _brandMap = Map<String, String>.from(res);
          if (_selectedFilters.isEmpty && _brandMap.isNotEmpty) {
            _selectedFilters.add('ALL');
          }
        });
      }
    } catch (e) {
      print('加载快递公司列表失败: $e');
    }
  }

  /// 加载业务员列表
  Future<void> _loadStaffList() async {
    try {
      // TODO: 调用获取业务员列表的API
      // DataResult result = await CourierDao.getStaffList();
      setState(() {
        _staffList = [
          {'id': 'ALL', 'name': '全部业务员'},
          {'id': 'staff1', 'name': '张三'},
          {'id': 'staff2', 'name': '李四'},
          {'id': 'staff3', 'name': '王五'},
        ];
        if (_selectedFilters.isEmpty) {
          _selectedFilters.add('ALL');
        }
      });
    } catch (e) {
      print('加载业务员列表失败: $e');
    }
  }

  /// 加载员工列表
  Future<void> _loadEmployeeList() async {
    try {
      // TODO: 调用获取员工列表的API
      // DataResult result = await CourierDao.getEmployeeList();
      setState(() {
        _employeeList = [
          {'id': 'ALL', 'name': '全部员工'},
          {'id': 'emp1', 'name': '赵六'},
          {'id': 'emp2', 'name': '孙七'},
          {'id': 'emp3', 'name': '周八'},
        ];
        if (_selectedFilters.isEmpty) {
          _selectedFilters.add('ALL');
        }
      });
    } catch (e) {
      print('加载员工列表失败: $e');
    }
  }

  /// 构建时间和筛选选择器
  Widget _buildTimeAndFilterSelector() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          // 时间选择行
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                // 日期范围显示
                Expanded(
                  child: InkWell(
                    onTap: _showDateRangePicker,
                    child: Row(
                      children: [
                        Text(
                          '${DateFormat('yyyy-MM-dd').format(_startDate)} 至 ${DateFormat('yyyy-MM-dd').format(_endDate)}',
                          style: TextStyle(fontSize: 14, color: Colors.black87),
                        ),
                        SizedBox(width: 4),
                        Icon(Icons.keyboard_arrow_down, size: 16, color: Colors.grey[600]),
                      ],
                    ),
                  ),
                ),
                // 筛选选项显示
                InkWell(
                  onTap: _showFilterDialog,
                  child: Row(
                    children: [
                      Text(
                        _getFilterDisplayName(),
                        style: TextStyle(fontSize: 14, color: Colors.black87),
                      ),
                      SizedBox(width: 4),
                      Icon(Icons.keyboard_arrow_down, size: 16, color: Colors.grey[600]),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // 时间快捷选择按钮
          Container(
            padding: EdgeInsets.fromLTRB(16, 0, 16, 12),
            child: Row(
              children: [
                Expanded(child: _buildTimeButton('近7天')),
                SizedBox(width: 8),
                Expanded(child: _buildTimeButton('近30天')),
                SizedBox(width: 8),
                Expanded(child: _buildTimeButton('本月')),
                SizedBox(width: 8),
                Expanded(child: _buildTimeButton('上月')),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 获取筛选显示名称
  String _getFilterDisplayName() {
    if (_selectedFilters.isEmpty) {
      switch (_currentTabIndex) {
        case 0:
          return '全部快递公司';
        case 1:
          return '全部业务员';
        case 2:
          return '全部员工';
        default:
          return '全部';
      }
    }

    if (_selectedFilters.contains('ALL')) {
      switch (_currentTabIndex) {
        case 0:
          return '全部快递公司';
        case 1:
          return '全部业务员';
        case 2:
          return '全部员工';
        default:
          return '全部';
      }
    }

    // 显示选中的数量
    int count = _selectedFilters.length;
    switch (_currentTabIndex) {
      case 0:
        return count == 1 ? _getFilterNameById(_selectedFilters.first) : '已选择${count}个快递公司';
      case 1:
        return count == 1 ? _getFilterNameById(_selectedFilters.first) : '已选择${count}个业务员';
      case 2:
        return count == 1 ? _getFilterNameById(_selectedFilters.first) : '已选择${count}个员工';
      default:
        return '已选择${count}项';
    }
  }

  /// 根据ID获取筛选项名称
  String _getFilterNameById(String id) {
    switch (_currentTabIndex) {
      case 0: // 快递公司
        return _brandMap[id] ?? id;
      case 1: // 业务员
        var staff = _staffList.firstWhere((item) => item['id'] == id, orElse: () => {'name': id});
        return staff['name'] ?? id;
      case 2: // 员工
        var employee = _employeeList.firstWhere((item) => item['id'] == id, orElse: () => {'name': id});
        return employee['name'] ?? id;
      default:
        return id;
    }
  }

  /// 构建时间按钮
  Widget _buildTimeButton(String text) {
    bool isSelected = _selectedTimeRange == text;
    return InkWell(
      onTap: () => _selectTimeRange(text),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? DefaultConfig().configs.PRIMARY_COLOR : Colors.grey[200],
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              color: isSelected ? Colors.white : Colors.black87,
              fontSize: 12,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }

  /// 选择时间范围
  void _selectTimeRange(String range) {
    setState(() {
      _selectedTimeRange = range;
      DateTime now = DateTime.now();

      switch (range) {
        case '近7天':
          _startDate = now.subtract(Duration(days: 7));
          _endDate = now;
          break;
        case '近30天':
          _startDate = now.subtract(Duration(days: 30));
          _endDate = now;
          break;
        case '本月':
          _startDate = DateTime(now.year, now.month, 1);
          _endDate = now;
          break;
        case '上月':
          DateTime lastMonth = DateTime(now.year, now.month - 1, 1);
          _startDate = lastMonth;
          _endDate = DateTime(now.year, now.month, 0);
          break;
      }
    });
    _loadStatisticsData();
  }

  /// 显示筛选弹框
  void _showFilterDialog() {
    switch (_currentTabIndex) {
      case 0: // 快递公司
        _showBrandDialog();
        break;
      case 1: // 业务员
        _showStaffDialog();
        break;
      case 2: // 员工
        _showEmployeeDialog();
        break;
    }
  }

  /// 显示快递公司选择弹框
  void _showBrandDialog() {
    List<String> tempSelectedFilters = List.from(_selectedFilters); // 临时存储选择状态

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            return Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.7,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 标题栏
                  Container(
                    padding: EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
                    ),
                    child: Row(
                      children: [
                        Text(
                          '选择快递公司',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                        ),
                        Spacer(),
                        Text(
                          '已选择${tempSelectedFilters.length}项',
                          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ),
                  // 选项列表
                  Flexible(
                    child: ListView(
                      shrinkWrap: true,
                      children: [
                        _buildMultiSelectFilterItem('ALL', '全部快递公司', tempSelectedFilters, setModalState),
                        ..._brandMap.entries.map((entry) =>
                          _buildMultiSelectFilterItem(entry.key, entry.value, tempSelectedFilters, setModalState)
                        ).toList(),
                      ],
                    ),
                  ),
                  // 底部按钮
                  Container(
                    padding: EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      border: Border(top: BorderSide(color: Colors.grey[200]!)),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            child: Text('取消'),
                          ),
                        ),
                        SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              setState(() {
                                _selectedFilters = tempSelectedFilters;
                              });
                              Navigator.pop(context);
                              _loadStatisticsData();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: DefaultConfig().configs.PRIMARY_COLOR,
                              foregroundColor: Colors.white,
                            ),
                            child: Text('确定'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  /// 显示业务员选择弹框
  void _showStaffDialog() {
    List<String> tempSelectedFilters = List.from(_selectedFilters);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            return Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.7,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 标题栏
                  Container(
                    padding: EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
                    ),
                    child: Row(
                      children: [
                        Text(
                          '选择业务员',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                        ),
                        Spacer(),
                        Text(
                          '已选择${tempSelectedFilters.length}项',
                          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ),
                  // 选项列表
                  Flexible(
                    child: ListView(
                      shrinkWrap: true,
                      children: _staffList.map((staff) =>
                        _buildMultiSelectFilterItem(staff['id'], staff['name'], tempSelectedFilters, setModalState)
                      ).toList(),
                    ),
                  ),
                  // 底部按钮
                  Container(
                    padding: EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      border: Border(top: BorderSide(color: Colors.grey[200]!)),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            child: Text('取消'),
                          ),
                        ),
                        SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              setState(() {
                                _selectedFilters = tempSelectedFilters;
                              });
                              Navigator.pop(context);
                              _loadStatisticsData();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: DefaultConfig().configs.PRIMARY_COLOR,
                              foregroundColor: Colors.white,
                            ),
                            child: Text('确定'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  /// 显示员工选择弹框
  void _showEmployeeDialog() {
    List<String> tempSelectedFilters = List.from(_selectedFilters);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            return Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.7,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 标题栏
                  Container(
                    padding: EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
                    ),
                    child: Row(
                      children: [
                        Text(
                          '选择员工',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                        ),
                        Spacer(),
                        Text(
                          '已选择${tempSelectedFilters.length}项',
                          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ),
                  // 选项列表
                  Flexible(
                    child: ListView(
                      shrinkWrap: true,
                      children: _employeeList.map((employee) =>
                        _buildMultiSelectFilterItem(employee['id'], employee['name'], tempSelectedFilters, setModalState)
                      ).toList(),
                    ),
                  ),
                  // 底部按钮
                  Container(
                    padding: EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      border: Border(top: BorderSide(color: Colors.grey[200]!)),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            child: Text('取消'),
                          ),
                        ),
                        SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              setState(() {
                                _selectedFilters = tempSelectedFilters;
                              });
                              Navigator.pop(context);
                              _loadStatisticsData();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: DefaultConfig().configs.PRIMARY_COLOR,
                              foregroundColor: Colors.white,
                            ),
                            child: Text('确定'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }



  /// 显示日期范围选择器
  void _showDateRangePicker() async {
    DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
      locale: Locale('zh', 'CN'),
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
        _selectedTimeRange = ''; // 清除快捷选择的状态
      });
      _loadStatisticsData();
    }
  }

  /// 构建统计卡片
  Widget _buildStatisticsCards() {
    return Container(
      margin: EdgeInsets.all(16),
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: DefaultConfig().configs.PRIMARY_COLOR,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // 第一行统计数据
          Row(
            children: [
              Expanded(
                child: _buildStatItem('入库量', _statisticsData['inboundCount']?.toString() ?? '0'),
              ),
       
              Expanded(
                child: _buildStatItem('出库量', _statisticsData['signCount']?.toString() ?? '0'),
              ),
               Expanded(
                child: _buildStatItem('退件量', _statisticsData['returnCount']?.toString() ?? '0'),
              ),
              Expanded(
                child: _buildStatItem('问题件', _statisticsData['problemCount']?.toString() ?? '0'),
              ),
            ],
          ),
       
        ],
      ),
    );
  }

  /// 构建单个统计项
  Widget _buildStatItem(String title, String value, {bool showInfo = false}) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              value,
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (showInfo)
              Padding(
                padding: EdgeInsets.only(left: 4),
                child: Icon(
                  Icons.info_outline,
                  color: Colors.white70,
                  size: 16,
                ),
              ),
          ],
        ),
        SizedBox(height: 4),
        Text(
          title,
          style: TextStyle(
            color: Colors.white70,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  /// 构建每日明细
  Widget _buildDailyDetails() {
    return Container(
      margin: EdgeInsets.fromLTRB(16, 0, 16, 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Padding(
            padding: EdgeInsets.all(16),
            child: Text(
              '每日统计明细',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          ),
          // 表头
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              border: Border(
                top: BorderSide(color: Colors.grey[200]!),
                bottom: BorderSide(color: Colors.grey[200]!),
              ),
            ),
            child: Row(
              children: [
                Expanded(flex: 2, child: Text('日期', style: _tableHeaderStyle())),
                Expanded(flex: 2, child: Text('入库量', style: _tableHeaderStyle())),
                Expanded(flex: 2, child: Text('出库量', style: _tableHeaderStyle())),
                Expanded(flex: 2, child: Text('问题件', style: _tableHeaderStyle())),
                Expanded(flex: 2, child: Text('退件量', style: _tableHeaderStyle())),
                Container(width: 15, child: Text('', style: _tableHeaderStyle())), // 箭头列
              ],
            ),
          ),
          // 数据列表
          Expanded(
            child: ListView.builder(
              itemCount: _dailyDetails.length,
              itemBuilder: (context, index) {
                final item = _dailyDetails[index];
                bool isExpanded = _expandedRows.contains(index);
                return Column(
                  children: [
                    // 主数据行
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(color: Colors.grey[100]!),
                        ),
                      ),
                      child: Row(
                        children: [
                          Expanded(flex: 2, child: Text(item['date'] ?? '', style: _tableCellStyle())),
                          Expanded(flex: 2, child: Text(item['inbound']?.toString() ?? '0', style: _tableCellStyle())),
                          Expanded(flex: 2, child: Text(item['waitInbound']?.toString() ?? '0', style: _tableCellStyle())),
                          Expanded(flex: 2, child: Text(item['problem']?.toString() ?? '0', style: _tableCellStyle())),
                          Expanded(flex: 2, child: Text(item['return']?.toString() ?? '0', style: _tableCellStyle())),
                          // 箭头按钮
                          Container(
                            width: 15,
                            child: InkWell(
                              onTap: () => _toggleRowExpansion(index),
                              child: Icon(
                                isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                                color: Colors.grey[600],
                                size: 20,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    // 展开的详细数据
                    if (isExpanded) _buildExpandedContent(index),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 表头样式
  TextStyle _tableHeaderStyle() {
    return TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.w600,
      color: Colors.grey[700],
    );
  }

  /// 表格单元格样式
  TextStyle _tableCellStyle() {
    return TextStyle(
      fontSize: 12,
      color: Colors.black87,
    );
  }

  /// 构建多选筛选项
  Widget _buildMultiSelectFilterItem(String id, String name, List<String> tempSelectedFilters, StateSetter setModalState) {
    bool isSelected = tempSelectedFilters.contains(id);
    return InkWell(
      onTap: () {
        setModalState(() {
          if (id == 'ALL') {
            // 点击"全部"时的逻辑
            if (isSelected) {
              tempSelectedFilters.remove('ALL');
            } else {
              tempSelectedFilters.clear();
              tempSelectedFilters.add('ALL');
            }
          } else {
            // 点击具体项时的逻辑
            if (isSelected) {
              tempSelectedFilters.remove(id);
            } else {
              tempSelectedFilters.remove('ALL'); // 选择具体项时移除"全部"
              tempSelectedFilters.add(id);
            }
          }
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? DefaultConfig().configs.PRIMARY_COLOR.withValues(alpha: 0.1) : Colors.transparent,
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                name,
                style: TextStyle(
                  fontSize: 14,
                  color: isSelected ? DefaultConfig().configs.PRIMARY_COLOR : Colors.black87,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check,
                color: DefaultConfig().configs.PRIMARY_COLOR,
                size: 18,
              ),
          ],
        ),
      ),
    );
  }

  /// 切换行展开状态
  void _toggleRowExpansion(int index) {
    setState(() {
      if (_expandedRows.contains(index)) {
        _expandedRows.remove(index);
      } else {
        _expandedRows.add(index);
        // 如果没有详细数据，则加载
        if (!_detailData.containsKey(index)) {
          _loadDetailData(index);
        }
      }
    });
  }

  /// 加载详细数据
  Future<void> _loadDetailData(int index) async {
    try {
      final item = _dailyDetails[index];
      String date = item['date'] ?? '';

      // 构建API参数
      Map<String, dynamic> params = {
        'date': date,
        'type': _getTabType(),
        'startDate': DateFormat('yyyy-MM-dd').format(_startDate),
        'endDate': DateFormat('yyyy-MM-dd').format(_endDate),
      };

      // 调用详细数据接口
      DataResult result = await CourierDao.getBusinessStatisticsDetail(params);
      List<Map<String, dynamic>> detailList = [];

      if (result.result && result.data != null) {
        // 如果API返回成功，使用真实数据
        detailList = List<Map<String, dynamic>>.from(result.data);
      } else {
        // 如果API调用失败，使用模拟数据作为备选
        if (_currentTabIndex == 0) {
          // 按快递公司的详细数据
          detailList = [
            {'name': '中通快递', 'code': 'ZTO', 'inbound': 50, 'waitInbound': 0, 'problem': 0, 'return': 0},
            {'name': '圆通快递', 'code': 'YTO', 'inbound': 30, 'waitInbound': 0, 'problem': 0, 'return': 0},
            {'name': '申通快递', 'code': 'STO', 'inbound': 20, 'waitInbound': 0, 'problem': 0, 'return': 0},
          ];
        } else if (_currentTabIndex == 1) {
          // 按业务员的详细数据
          detailList = [
            {'name': '张三', 'inbound': 40, 'waitInbound': 0, 'problem': 0, 'return': 0},
            {'name': '李四', 'inbound': 35, 'waitInbound': 0, 'problem': 0, 'return': 0},
            {'name': '王五', 'inbound': 25, 'waitInbound': 0, 'problem': 0, 'return': 0},
          ];
        } else {
          // 按员工的详细数据
          detailList = [
            {'name': '赵六', 'inbound': 45, 'waitInbound': 0, 'problem': 0, 'return': 0},
            {'name': '孙七', 'inbound': 30, 'waitInbound': 0, 'problem': 0, 'return': 0},
            {'name': '周八', 'inbound': 25, 'waitInbound': 0, 'problem': 0, 'return': 0},
          ];
        }
      }

      setState(() {
        _detailData[index] = detailList;
      });

    } catch (e) {
      print('加载详细数据失败: $e');
      // 出现异常时也使用模拟数据
      List<Map<String, dynamic>> detailList = [];
      if (_currentTabIndex == 0) {
        detailList = [
          {'name': '中通快递', 'code': 'ZTO', 'inbound': 50, 'waitInbound': 0, 'problem': 0, 'return': 0},
          {'name': '圆通快递', 'code': 'YTO', 'inbound': 30, 'waitInbound': 0, 'problem': 0, 'return': 0},
        ];
      } else if (_currentTabIndex == 1) {
        detailList = [
          {'name': '张三', 'inbound': 40, 'waitInbound': 0, 'problem': 0, 'return': 0},
          {'name': '李四', 'inbound': 35, 'waitInbound': 0, 'problem': 0, 'return': 0},
        ];
      } else {
        detailList = [
          {'name': '赵六', 'inbound': 45, 'waitInbound': 0, 'problem': 0, 'return': 0},
          {'name': '孙七', 'inbound': 30, 'waitInbound': 0, 'problem': 0, 'return': 0},
        ];
      }
      setState(() {
        _detailData[index] = detailList;
      });
    }
  }

  /// 构建展开的内容
  Widget _buildExpandedContent(int index) {
    List<Map<String, dynamic>>? detailList = _detailData[index];

    if (detailList == null || detailList.isEmpty) {
      return Container(
        padding: EdgeInsets.all(20),
        child: Center(
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(DefaultConfig().configs.PRIMARY_COLOR),
          ),
        ),
      );
    }

    return Container(
      color: Colors.grey[50],
      child: Column(
        children: detailList.map((detail) {
          return Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.grey[200]!, width: 0.5),
              ),
            ),
            child: Row(
              children: [
                // 根据当前Tab类型显示不同的内容
                Expanded(
                  flex: 2,
                  child: _buildDetailNameCell(detail),
                ),
                Expanded(flex: 2, child: Text(detail['inbound']?.toString() ?? '0', style: _detailCellStyle())),
                Expanded(flex: 2, child: Text(detail['waitInbound']?.toString() ?? '0', style: _detailCellStyle())),
                Expanded(flex: 2, child: Text(detail['problem']?.toString() ?? '0', style: _detailCellStyle())),
                Expanded(flex: 2, child: Text(detail['return']?.toString() ?? '0', style: _detailCellStyle())),
                Container(width: 15), // 占位，对应箭头列
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 构建详细数据的名称单元格
  Widget _buildDetailNameCell(Map<String, dynamic> detail) {
    String name = detail['name'] ?? '';
    String? code = detail['code']; // 快递公司代码，如果有的话

    if (_currentTabIndex == 0) {
      // 按快递公司：只显示头像，不显示文字，与日期对齐
      return Align(
        alignment: Alignment.centerLeft,
        child: Container(
           margin: EdgeInsets.only(left: 5),
          width: 20,
          height: 20,
          child: CircleAvatar(
            radius: 10.0,
            backgroundColor: Colors.white,
            backgroundImage: AssetImage(_getExpressLogo(code ?? name)),
          ),
        ),
      );
    } else {
      // 按业务员或员工：只显示名称
      return Text(
        name,
        style: _detailCellStyle(),
        overflow: TextOverflow.ellipsis,
      );
    }
  }

  /// 获取快递公司头像
  String _getExpressLogo(String company) {
    // 使用项目中已有的getExpressLogo方法
    
    return CommonUtils.getExpressLogo(company);
  }

  /// 详细数据单元格样式
  TextStyle _detailCellStyle() {
    return TextStyle(
      fontSize: 11,
      color: Colors.grey[700],
    );
  }
}
