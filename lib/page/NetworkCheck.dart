import 'dart:async';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/widget/AppCustomBar.dart';
import 'package:cabinet_flutter_app/widget/AppbarTitle.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:scan/scan.dart';

class NetworkCheck extends StatefulWidget {
  NetworkCheck();

  @override
  State createState() {
    return new _NetworkCheckState();
  }
}

class _NetworkCheckState extends State<NetworkCheck> {
  _NetworkCheckState() : super();
  Scan scanPlugin = Scan();
  Map line1Map = {
    DefaultConfig().configs.PROD_URL.substring(8): -1,
  };

  Map otherLineMap = {
    "www.baidu.com": -1,
    "114.114.114.114": -1,
    "223.5.5.5": -1,
  };

  List<dynamic> fetchList = [];

  @override
  void initState() {
    super.initState();
    const timeout = const Duration(milliseconds: 1000);
    Timer(timeout, () {
      initParams();
    });
  }

  initParams() async {
    getPing(line1Map);
    getPing(otherLineMap);
  }

  // 请求线路一
  getPing(Map dataMap) async {
    dataMap.forEach((key, value) async {
      List<dynamic>? list = await scanPlugin.ping(key);
      if (list.length != 0) {
        list.forEach((item) {
          if (item.startsWith('rtt')) {
            List<String> arr1 = item.split('=');
            if (arr1.length > 1) {
              List<String> arr2 = arr1[1].split('/');
              if (arr2.length > 1) {
                dataMap[key] = arr2[1];
              } else {
                dataMap[key] = 0;
              }
            } else {
              dataMap[key] = 0;
            }
          } else {
            dataMap[key] = 0;
          }
        });
      } else {
        dataMap[key] = 0;
      }
      setState(() {});
    });
  }

  buildLine(Map dataMap, String title) {
    List<Widget> widgets = [];
    dataMap.forEach((key, value) {
      widgets.add(Container(
        padding: EdgeInsets.fromLTRB(15, 5, 5, 5),
        child: Row(
          children: [
            Expanded(
              child: Container(
                child: Text(
                  '$key',
                  style: TextStyle(color: Colors.black87, fontSize: 15),
                ),
              ),
            ),
            buildLineStatus(value),
          ],
        ),
      ));
    });

    return Container(
      color: Colors.white,
      margin: EdgeInsets.only(bottom: 10),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(15),
            decoration: BoxDecoration(
                border: Border(
              bottom: BorderSide(color: Colors.grey.shade100, width: 1),
            )),
            child: Row(
              children: [
                Text(
                  '$title',
                  style: TextStyle(color: Colors.black87, fontSize: 16),
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.fromLTRB(0, 10, 0, 10),
            child: Column(
              children: widgets,
            ),
          )
        ],
      ),
    );
  }

  buildLineStatus(value) {
    if (value == -1) {
      /// 正在ping中
      return Row(
        children: [
          SpinKitFadingCircle(
            color: Colors.grey.shade400,
            size: 18,
          ),
          Padding(
            padding: EdgeInsets.only(left: 2),
          ),
          Text(
            '连接中...',
            style: TextStyle(color: Colors.grey.shade400, fontSize: 14),
          )
        ],
      );
    } else if (value == 0) {
      /// ping失败
      return Text(
        '失败',
        style: TextStyle(color: Colors.redAccent, fontSize: 14),
      );
    } else {
      /// ping成功
      return Text(
        '成功: ${double.parse(value).toStringAsFixed(0)} ms',
        style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 14),
      );
    }
  }

  buildFetch() {
    List<Widget> widgets = [];
    fetchList.forEach((item) {
      widgets.add(Container(
        padding: EdgeInsets.fromLTRB(15, 5, 0, 5),
        alignment: Alignment.centerLeft,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  '[${item['type'] == true ? '成功' : '失败'}]',
                  style: TextStyle(color: item['type'] == true ? Colors.green : Colors.redAccent, fontSize: 13),
                ),
                Text(
                  '${item['url']} 用时:${item['time']}ms,响应:',
                  style: TextStyle(color: Colors.black54, fontSize: 13),
                ),
              ],
            ),
            Text(
              '${item['data']}',
              style: TextStyle(color: Colors.black54, fontSize: 13),
            ),
          ],
        ),
      ));
    });

    return Container(
      color: Colors.white,
      margin: EdgeInsets.only(bottom: 10),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(15),
            decoration: BoxDecoration(
                border: Border(
              bottom: BorderSide(color: Colors.grey.shade100, width: 1),
            )),
            child: Row(
              children: [
                Text(
                  '响应数据',
                  style: TextStyle(color: Colors.black87, fontSize: 16),
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.fromLTRB(0, 10, 0, 10),
            child: Column(
              children: widgets,
            ),
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.grey.shade200,
        appBar: AppCustomerBar(
          title: AppbarTitle(title: "网络测试"),
          actions: [
            Material(
              color: Theme.of(context).primaryColor,
              child: InkWell(
                onTap: () {
                  initParams();
                },
                child: Container(
                  padding: EdgeInsets.all(15),
                  child: Icon(
                    Icons.refresh,
                    color: Colors.white,
                    size: 25,
                  ),
                ),
              ),
            ),
          ],
        ),
        body: SingleChildScrollView(
          child: Column(
            children: <Widget>[
              buildLine(line1Map, '线路一'),
              buildLine(otherLineMap, '其他'),
              Offstage(
                offstage: fetchList.length == 0,
                child: buildFetch(),
              )
            ],
          ),
        ));
  }
}
