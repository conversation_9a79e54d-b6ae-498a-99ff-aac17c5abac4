import 'dart:async';

/**
 * 数据库表
 * Created by den<PERSON><PERSON><PERSON>
 * Date: 2018-08-03
 */
import 'package:cabinet_flutter_app/common/ab/SqlManager.dart';
import 'package:meta/meta.dart';
import 'package:sqflite/sqflite.dart';

///基类
abstract class BaseDbProvider {
  bool isTableExits = false;
  Map indexHash = {};

  tableSqlString();

  tableName();

  tableBaseString(String name, String columnId) {
    return '''
        create table $name (
        $columnId integer primary key autoincrement,
      ''';
  }

  List<Map> tableIndexArray() {
    return [];
  }

  Future<Database> getDataBase() async {
    return await open();
  }

  @mustCallSuper
  prepare(name, String createSql) async {
    isTableExits = await SqlManager.isTableExits(name);
    if (!isTableExits) {
      Database? db = await SqlManager.getCurrentDatabase();
      return await db?.execute(createSql);
    }
  }

  @mustCallSuper
  buildIndex(String tableName, String indexName, String code, bool isUnique) async {
    if (indexHash[indexName] == null) {
      indexHash[indexName] = await SqlManager.isIndexExits(indexName);
      if (!indexHash[indexName]) {
        Database? db = await SqlManager.getCurrentDatabase();
        if (isUnique) {
          await db?.execute('''
        CREATE UNIQUE INDEX $indexName ON $tableName($code);
      ''');
        } else {
          await db?.execute('''
        CREATE INDEX $indexName ON $tableName($code);
      ''');
        }

        indexHash[indexName] = true;
      }
    }
  }

  @mustCallSuper
  open() async {
    if (!isTableExits) {
      await prepare(tableName(), tableSqlString());
    }
//    CREATE INDEX IDX_$name\_code ON $name(code);
    if (tableIndexArray().length > 0) {
      for (Map f in tableIndexArray()) {
        await this.buildIndex(tableName(), f['name'], f['value'], f['isUnique']);
      }
    }
    return await SqlManager.getCurrentDatabase();
  }
}
