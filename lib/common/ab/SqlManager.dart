import 'dart:async';

import 'package:cabinet_flutter_app/common/ab/provider/task/PhotoDbProvider.dart';
import 'package:cabinet_flutter_app/common/ab/provider/task/ScanDbProvider.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:sqflite/sqflite.dart';

///数据库管理
class SqlManager {
  static const _VERSION = 2;

  static const _NAME = "cabinet_app.db";

  static Database? _database;

  ///初始化
  static init() async {
    // open the database
    var databasesPath = await getDatabasesPath();
    var userRes = await UserDao.getUserInfoLocal();
    String dbName = _NAME;
    if (userRes != null && userRes.result) {
      UserEntity? user = userRes.data;
      if (user != null && user.id != null) {
        dbName = "cabinet_" + user.id! + "_" + _NAME;
      }
    }
    String path = databasesPath + "/" + dbName;
//    if (Platform.isIOS) {
//      path = databasesPath + "/" + dbName;
//    }
    _database = await openDatabase(path, version: _VERSION, onCreate: (Database db, int version) async {
      // When creating the db, create the table
      //await db.execute("CREATE TABLE Test (id INTEGER PRIMARY KEY, name TEXT, value INTEGER, num REAL)");
    }, onUpgrade: (Database db, int oldVersion, int newVersion) async {
      // 此处写升级脚本
      if (newVersion == 2) {
        db.execute("DROP INDEX IDX_scan_waybillNo_type");
        db.execute("DROP INDEX IDX_scan_type");
        db.execute("DROP INDEX IDX_scan_createDate");
        db.execute("DROP INDEX IDX_scan_ymd");
        db.execute("ALTER TABLE Scan RENAME TO Scan_temp");
        ScanDbDbProvider scanDbDbProvider = new ScanDbDbProvider();
        String newSql = scanDbDbProvider.tableSqlString();
        db.execute(newSql);
        db.execute(
            "insert into Scan(id, status,company,createDate) " + "select id, status,company,createDate from Scan_temp");
        db.execute("DROP TABLE Scan_temp");
      }

      // /// 更新脚本
      // /// 清除成功上传的数据和重复的失败数据
      // if (newVersion == 3 && oldVersion == 2) {
      //   db.execute("ALTER TABLE Photo RENAME TO Photo_temp");
      //   db.execute("delete from Photo_temp where status ='OK'");
      //   PhotoDbDbProvider photoProvider = new PhotoDbDbProvider();
      //   String newSql = photoProvider.tableSqlString();
      //   db.execute(newSql);
      //   db.execute("insert into Photo(id, path, fileName,status,type,msg,ymd,sheetNo,company,createDate) " +
      //       "select id, path, fileName,status, type, msg,ymd,sheetNo,company,createDate from Photo_temp group by sheetNo,type,company having status = 'FAIL'");
      //   db.execute("DROP TABLE Photo_temp");
      // }

      /// 获取表字段，photo表
      var res2 = await db.rawQuery('PRAGMA table_info([Photo])');
      List keys2 = [];
      if (res2.length > 0) {
        res2.forEach((item) {
          keys2.add(item['name']);
        });
      }
      // 此处写升级脚本
      if (oldVersion < newVersion && newVersion < 3) {
        PhotoDbDbProvider photoDbDbProvider = new PhotoDbDbProvider();
        db.execute("ALTER TABLE Photo RENAME TO Photo_temp");
        String newSql = photoDbDbProvider.tableSqlString();
        db.execute(newSql);

        if (keys2.length > 0) {
          String sql = keys2.join(',');
          db.execute("insert into Photo($sql) " + "select $sql from Photo_temp");
          db.execute("DROP TABLE Photo_temp");
        }
      }
    });
  }

  ///表是否存在
  static isTableExits(String tableName) async {
    await getCurrentDatabase();
    var res = await _database?.rawQuery("select * from Sqlite_master where type = 'table' and name = '$tableName'");
    return res != null && res.length > 0;
  }

  ///索引是否存在
  static isIndexExits(String indexName) async {
    await getCurrentDatabase();
    var res = await _database?.rawQuery("select * from Sqlite_master where type = 'index' and name = '$indexName'");
    return res != null && res.length > 0;
  }

  ///获取当前数据库对象
  static Future<Database?> getCurrentDatabase() async {
    if (_database == null) {
      await init();
    }
    return _database;
  }

  ///关闭
  static close() {
    _database?.close();
    _database = null;
  }
}
