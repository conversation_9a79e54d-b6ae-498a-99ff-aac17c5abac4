import 'dart:async';

import 'package:cabinet_flutter_app/common/ab/SqlProvider.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/entitys/photo_entity.dart';
import 'package:intl/intl.dart';
import 'package:sqflite/sqflite.dart';

class PhotoDbDbProvider extends BaseDbProvider {
  final String name = 'Photo';

  PhotoDbDbProvider();

  @override
  tableSqlString() {
    /// status： OK， FAIL
    return tableBaseString(name, 'id') +
        '''
        path text not null,
        fileName text not null,
        status text not null,
        type text,
        msg text,
        ymd text,
        sheetNo text,
        company text,
        createDate text);
      ''';
  }

  @override
  tableName() {
    return name;
  }

  @override
  tableIndexArray() {
    return [
      {'name': 'IDX_photo_sheetNo_type', 'value': 'sheetNo,type', 'isUnique': true},
      {'name': 'IDX_photo_fileName_type', 'value': 'fileName,status', 'isUnique': false},
      {'name': 'IDX_photo_status', 'value': 'status', 'isUnique': false},
      {'name': 'IDX_photo_createDate', 'value': 'createDate', 'isUnique': false}
    ];
  }

  Future<List<PhotoEntity>> getPhotoList({String? status, int? page, String? sheetNo}) async {
    String sql = " 1=1 ";
    var whereArgs = [];
    if (status != null && status != '') {
      sql += "and status = ? ";
      whereArgs.add(status);
    }

    if (sheetNo != null && sheetNo != '') {
      sql += "and sheetNo LIKE '%$sheetNo' ";
      // whereArgs.add(sheetNo);
    }

    Database db = await getDataBase();
    List<Map<String, dynamic>> maps =
        await db.query(name, where: sql + 'order by status asc', whereArgs: whereArgs, offset: page! * 10, limit: 10);
    if (maps.length > 0) {
      List<PhotoEntity> list = [];

      maps.asMap().forEach((k, v) {
        PhotoEntity s = PhotoEntity.fromJson(v);
        list.add(s);
      });

      return list;
    }
    return [];
  }

  // Future<int?> getPhotoCount() async {
  //   Database db = await getDataBase();
  //   var res = await db.rawQuery('select count(id) as count from ' + name, []);
  //   if (res.length > 0) {
  //     return res[0]["count"];
  //   }
  //   return 0;
  // }

  static getValue(String fileName, String backType) {
    String value = '';
    List<String> strList = fileName.split('/');
    switch (backType) {
      case 'company':
        DefaultConfig().configs.EXPRESS.forEach((key, company) {
          if (fileName.indexOf(key) != -1) {
            value = company;
          }
        });
        break;
      case 'sheetNo':
        strList.forEach((item) {
          if (item.indexOf('.') > -1) {
            List<String> list = item.split('.');
            value = list.length > 0 ? list[0] : '';
          }
        });
        break;
    }
    return value;
  }

  /// 创建一个Photo
  static PhotoEntity buildPhoto(String path, String fileName, String company, String status, String msg, String type) {
    PhotoEntity photo = new PhotoEntity();
    photo.sheetNo = getValue(fileName, 'sheetNo');
    photo.company = company;
    photo.path = path;
    photo.fileName = fileName;
    photo.status = status;
    photo.type = type;
    photo.createDate = new DateFormat("yyyy-MM-dd HH:mm:ss").format(DateTime.now());
    photo.ymd = new DateFormat("yyyy-MM-dd").format(DateTime.now());
    photo.msg = msg;
    return photo;
  }

  /// 删除
  Future<int> deletePhoto(String sheetNo, String type) async {
    Database db = await getDataBase();
    return await db.delete(name, where: 'sheetNo = ? and type = ?', whereArgs: [sheetNo, type]);
  }

  /// 根据状态批量删除
  Future<int> deletePhotoByStatus(String status) async {
    Database db = await getDataBase();
    return await db.delete(name, where: 'status = ?', whereArgs: [status]);
  }

  /// 删除重复数据
  Future<bool> deleteDuplicateData() async {
    Database db = await getDataBase();
    db.execute("ALTER TABLE Photo RENAME TO Photo_temp");
    db.execute("delete from Photo_temp where status ='OK'");
    PhotoDbDbProvider photoProvider = new PhotoDbDbProvider();
    String newSql = photoProvider.tableSqlString();
    db.execute(newSql);
    db.execute("insert into Photo(id, path, fileName,status,type,msg,ymd,sheetNo,company,createDate) " +
        "select id, path, fileName,status, type, msg,ymd,sheetNo,company,createDate from Photo_temp group by sheetNo,type,company having status = 'FAIL'");
    db.execute("DROP TABLE Photo_temp");
    return true;
  }

  /// 根据状态批量删除
  Future<int> deletePhotoById(String id) async {
    Database db = await getDataBase();
    return await db.delete(name, where: 'id = ?', whereArgs: [id]);
  }

  /// 批量删除
  Future deletePhotoList(List fileNames) async {
    Database db = await getDataBase();
    var batch = db.batch();
    fileNames.forEach((fileName) {
      batch.rawDelete("DELETE FROM $name WHERE fileName = ?", [fileName]);
    });
    return batch.commit();
  }

  Future<List<PhotoEntity>> deletePhoto7DaysBefore() async {
    String status = 'OK';
    Database db = await getDataBase();
    var whereArgs = [status, new DateFormat("yyyy-MM-dd").format(DateTime.now().subtract(new Duration(days: 6)))];
    var maps = await db.rawQuery('select * from $name where status = ? and ymd < ?', whereArgs);
    if (maps.length > 0) {
      db.delete(name, where: 'status = ? and ymd < ?', whereArgs: whereArgs);

      List<PhotoEntity> list = [];

      maps.asMap().forEach((k, v) {
        PhotoEntity s = PhotoEntity.fromJson(v);
        list.add(s);
      });

      return list;
    }
    return [];
  }

  ///插入(创建)
  Future savePhoto(PhotoEntity? photo) async {
    Database db = await getDataBase();
    var batch = db.batch();
    // PhotoEntity oldPhoto = await getPhoto(photo.fileName);
    PhotoEntity? oldPhoto = await getPhotoByNoAndType(photo?.sheetNo, photo?.type);
    if (oldPhoto != null) {
      photo?.id = oldPhoto.id;
      batch.update(name, photo!.toJson(), where: "id = ?", whereArgs: [photo.id]);
    }
    if (photo?.id == null) {
      batch.insert(name, photo!.toJson());
    }
    await batch.commit();
    if (photo != null) {
      photo = await getPhotoByNoAndType(photo.sheetNo, photo.type);
    }
    return photo;
  }

  /// 查询一条Photo
  Future<PhotoEntity?> getPhoto(String fileName) async {
    Database db = await getDataBase();
    List<Map<String, dynamic>> maps = await db.query(name, where: 'fileName = ?', whereArgs: [fileName]);
    if (maps.length > 0) {
      PhotoEntity photo = PhotoEntity.fromJson(maps.first);
      return photo;
    }
    return null;
  }

  /// 查询一条Photo
  Future<PhotoEntity?> getPhotoByNoAndType(String? sheetNo, String? type, {String? status}) async {
    Database db = await getDataBase();
    String sql = 'sheetNo = ? and type = ?';
    List<String> whereArgs = [];
    if (sheetNo != null && type != null) {
      whereArgs = [sheetNo, type];
    }
    if (status != null) {
      sql += ' status = ?';
      whereArgs.add(status);
    }
    List<Map<String, dynamic>> maps = await db.query(name, where: sql, whereArgs: whereArgs);
    if (maps.length > 0) {
      PhotoEntity photo = PhotoEntity.fromJson(maps.first);
      return photo;
    }
    return null;
  }

  /// 查询一条Photo
  Future<PhotoEntity?> getPhotoByPath(String path, String type) async {
    Database db = await getDataBase();
    List<Map<String, dynamic>> maps = await db.query(name, where: 'path = ? and type = ?', whereArgs: [path, type]);
    if (maps.length > 0) {
      PhotoEntity photo = PhotoEntity.fromJson(maps.first);
      return photo;
    }
    return null;
  }

  Future<int?> getCount(String status, String? sheetNo) async {
    String sql = 'SELECT COUNT(*) FROM  $name where status = ?';
    if (sheetNo != null && sheetNo != '') {
      sql += " and sheetNo LIKE '%$sheetNo'";
    }
    Database db = await getDataBase();
    int? count = Sqflite.firstIntValue(await db.rawQuery(sql, [status]));
    return count;
  }

  Future<int?> getAllCount(String? sheetNo) async {
    String sql = 'SELECT COUNT(*) FROM  $name';
    if (sheetNo != null && sheetNo != '') {
      sql += " where sheetNo LIKE '%$sheetNo'";
    }
    Database db = await getDataBase();
    int? count = Sqflite.firstIntValue(await db.rawQuery(sql));
    return count;
  }

  Future clearPhoto() async {
    Database db = await getDataBase();
    db.execute('DELETE FROM $name');
  }

  Future<List<PhotoEntity>> getAllPhotoByStatus(String status) async {
    Database db = await getDataBase();
    var maps = await db.rawQuery('select * from $name where status = ?', [status]);
    if (maps.length > 0) {
      List<PhotoEntity> list = [];

      maps.asMap().forEach((k, v) {
        PhotoEntity s = PhotoEntity.fromJson(v);
        list.add(s);
      });

      return list;
    }
    return [];
  }
}
