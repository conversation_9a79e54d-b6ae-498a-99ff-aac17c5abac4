import 'dart:async';

import 'package:cabinet_flutter_app/common/ab/SqlProvider.dart';
import 'package:cabinet_flutter_app/common/entitys/customer_entity.dart';
import 'package:intl/intl.dart';
import 'package:sqflite/sqflite.dart';

///本地客户表
class CustomerDbProvider extends BaseDbProvider {
  final String name = 'Customer_table';

  CustomerDbProvider();

  @override
  tableSqlString() {
    return tableBaseString(name, 'mobile') +
        '''
        id text,
        mobileLastFour text,
        shopCode text,
        code text,
        name text,
        jianpin text,
        pinyin text,
        idNo text,
        email text,
        provinceCode text,
        provinceName text,
        cityCode text,
        cityName text,
        countyCode text,
        countyName text,
        address text,
        gender text,
        label text,
        createBy text,
        createDate text,
        isSensitiveConsumer text,
        remarks text,
        ymd text);
      ''';
  }

  @override
  tableName() {
    return name;
  }

  @override
  tableIndexArray() {
    return [
      {'name': 'IDX_customer_mobile', 'value': 'mobile', 'isUnique': false}
    ];
  }

  Future<Object?> getCustomerCount({String? customerName, String? mobile}) async {
    Database db = await getDataBase();

    String where = ' where 1=1';
    List<dynamic> data = [];

    if (customerName != null && customerName != '') {
      where += ' and name = ?';
      data.add(customerName);
    }

    if (mobile != null && mobile != '') {
      where += ' and mobile = ?';
      data.add(mobile);
    }

    var res = await db.rawQuery('select count(mobile) as count from ' + name + where, data);

    if (res.length > 0) {
      return res[0]["count"];
    }
    return 0;
  }

  Future<int?> getCount() async {
    Database db = await getDataBase();
    int? count = Sqflite.firstIntValue(await db.rawQuery("SELECT COUNT(*) FROM  $name"));
    return count;
  }

  Future<bool?> isNewCustomer(String mobile) async {
    Database db = await getDataBase();
    int? count = Sqflite.firstIntValue(await db.rawQuery("SELECT COUNT(*) FROM  $name where mobile = ?", [mobile]));
    return count == 0;
  }

  /// 查询一个Customer
  Future<CustomerEntity?> getCustomer(String? mobile) async {
    Database db = await getDataBase();
    List<Map<String, dynamic>> maps =
        await db.query(name, where: mobile?.length == 4 ? 'mobileLastFour = ?' : 'mobile = ?', whereArgs: [mobile]);
    if (maps.length > 0) {
      CustomerEntity customer = CustomerEntity.fromJson(maps.first);
      return customer;
    }
    return null;
  }

  ///插入(创建)
  Future saveCustomer(CustomerEntity? customer) async {
    Database db = await getDataBase();
    var batch = db.batch();
    CustomerEntity? oldCustomer = await getCustomer(customer?.mobile);
    if (oldCustomer != null) {
      batch.update(name, customer!.toJson(), where: "mobile = ?", whereArgs: [customer.mobile]);
//      customer.id = oldCustomer.id;
    } else {
      batch.insert(name, customer!.toJson());
    }
    await batch.commit();
    customer = await getCustomer(customer.mobile);
    return customer;
  }

  Future batchSaveCustomer(List list) async {
    Database db = await getDataBase();
    var batch = db.batch();
    for (var item in list) {
      CustomerEntity customer = CustomerEntity.fromJson(item);
      CustomerEntity? oldCustomer = await getCustomer(customer.mobile);
      if (oldCustomer == null) {
        batch.insert(name, customer.toJson());
      }
    }
    return await batch.commit();
  }

  static CustomerEntity buildCustomer(String mobile, {name = '', isSensitiveConsumer = 'N'}) {
    CustomerEntity customer = new CustomerEntity();
    customer.name = name;
    customer.mobile = mobile;
    customer.isSensitiveConsumer = isSensitiveConsumer;
    customer.mobileLastFour = mobile.substring(mobile.length - 4, mobile.length);
    customer.createDate = new DateFormat("yyyy-MM-dd HH:mm:ss").format(DateTime.now());
    customer.ymd = new DateFormat("yyyy-MM-dd").format(DateTime.now());
    return customer;
  }

  // 清空
  Future<int> clearCustomer(String mobile) async {
    Database db = await getDataBase();
    return await db.delete(name, where: 'mobile = ?', whereArgs: [mobile]);
  }

  query({String? mobile, int offset = 0}) async {
    Database db = await getDataBase();
    String where = " 1=1 ";
    var whereArgs = [];
    if (mobile != null && mobile != '') {
      where += mobile.length == 4 ? "and mobileLastFour = ?" : "and mobile = ?";
      whereArgs.add(mobile);
    }

    List<Map<String, dynamic>> maps = await db.query(name,
        where: where + 'order by createDate Desc', whereArgs: whereArgs, offset: offset * 10, limit: 30);
    if (maps.length > 0) {
      List<CustomerEntity> list = [];

      maps.asMap().forEach((k, v) {
        CustomerEntity s = CustomerEntity.fromJson(v);
        list.add(s);
      });

      return list;
    }
    return [];
  }
}
