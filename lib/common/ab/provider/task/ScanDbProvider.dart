import 'dart:async';

import 'package:cabinet_flutter_app/common/ab/SqlProvider.dart';
import 'package:cabinet_flutter_app/common/entitys/scan_item_entity.dart';
import 'package:intl/intl.dart';
import 'package:sqflite/sqflite.dart';

///本地任务表
class ScanDbDbProvider extends BaseDbProvider {
  final String name = 'Scan';

  ScanDbDbProvider();

  @override
  tableSqlString() {
    return tableBaseString(name, 'id') +
        '''
        orderNo text,
        orderType text,
        bizNo text,
        channelId text,
        channelName text,
        siteId text,
        siteName text,
        cabinetLocationId text,
        cabinetLocationName text,
        cabinetName text,
        courierId text,
        cabinetLocationCode text,
        cabinetId text,
        cabinetCode text,
        cabinetNo text,
        cabinetBoxPcbNo text,
        cabinetBoxId text,
        cabinetBoxLabel text,
        waybillNo text,
        brandCode text,
        brandName text,
        boxLabel text,
        receiverName text,
        receiverMobile text,
        receiverMobileLast4 text,
        normalWaybill text,
        sensitiveConsumer text,
        newCustomer text,
        boxType text,
        needCallCustomer text,
        shortNo text,
        uno text,
        inboundUserCode text,
        inboundUserName text,
        keepEffectTime text,
        inboundPhotoUrl text,
        inboundDate text,
        inboundYm text,
        inboundYmd text,
        inboundType text,
        inboundDeliveryReceiptDate text,
        inboundDeliveryState text,
        inboundDeliveryYmd text,
        checkCode text,
        messageType text,
        messageTplId text,
        messageStatus text,
        messageDeliveryTime text,
        messageReceiptTime text,
        messageReceiptContent text,
        inboundUserId text,
        inboundTime text,
        outboundTime text,
        outboundImageUrl text,
        serviceDuration text,
        isOutbound text,
        outboundBatchNo text,
        outboundUserCode text,
        outboundUserName text,
        outboundDate text,
        outboundPhotoUrl text,
        outboundYm text,
        outboundYmd text,
        isSign text,
        signDeliveryReceiptDate text,
        signDeliveryState text,
        signDeliveryYmd text,
        isBack text,
        backReason text,
        backUserCode text,
        backUserName text,
        backDate text,
        backYm text,
        backYmd text,
        backDeliveryReceiptDate text,
        backDeliveryState text,
        backDeliveryYmd text,
        pjDeliveryReceiptDate text,
        pjDeliveryState text,
        isKeep text,
        keepDayDesc text,
        keepEffectDate text,
        smsState text,
        isSendSms text,
        smsTplCode text,
        sendSmsDate text,
        createBy text,
        createDate text,
        updateBy text,
        updateDate text,
        remarks text,
        scanId text,
        type text,
        status text,
        engine text,
        rule text,
        day text,
        cod text,
        shelfNo text,
        no text,
        ymd text,
        isHb text,
        isIntercept text,
        msg text);
      ''';
  }

  @override
  tableName() {
    return name;
  }

  @override
  tableIndexArray() {
    return [
      // {'name': 'IDX_scan_waybillNo_type', 'value': 'waybillNo,type', 'isUnique': false},
      // {'name': 'IDX_scan_type', 'value': 'type', 'isUnique': false},
      // {'name': 'IDX_scan_createDate', 'value': 'createDate', 'isUnique': false},
      // {'name': 'IDX_scan_ymd', 'value': 'ymd', 'isUnique': false}
    ];
  }

  Future<List<ScanItemEntity>> getScanList(String type) async {
    Database db = await getDataBase();
    List<Map<String, dynamic>> maps =
        await db.query(name, where: "type = ? order by createDate DESC", whereArgs: [type]);
    if (maps.length > 0) {
      List<ScanItemEntity> list = [];

      maps.asMap().forEach((k, v) {
        ScanItemEntity s = ScanItemEntity.fromJson(v);
        list.add(s);
      });

      return list;
    }
    return [];
  }

  Future<Object?> getScanCount({String? type, String? brandCode, String? status, String? checkCode}) async {
    Database db = await getDataBase();

    String where = ' where 1=1';
    List<dynamic> data = [];

    if (type != null) {
      where += ' and type = ?';
      data.add(type);
    }

    if (brandCode != null) {
      where += ' and brandCode = ?';
      data.add(brandCode);
    }

    if (status != null) {
      where += ' and status = ?';
      data.add(status);
    }

    if (checkCode != null) {
      where += ' and checkCode = ?';
      data.add(checkCode);
    }

    var res = await db.rawQuery('select count(id) as count from ' + name + where, data);

    if (res.length > 0) {
      return res[0]["count"];
    }
    return 0;
  }

  Future<Object> getMaxIndex({String? shelfNo, String? day, String? rule}) async {
    String where = '1=1';
    List<dynamic> data = [];

    if (shelfNo != null) {
      where += ' and shelfNo = ?';
      data.add(shelfNo);
    }

    if (day != null) {
      where += ' and day = ?';
      data.add(day);
    }

    if (rule != null) {
      where += ' and rule = ?';
      data.add(rule);
    }

//    where += ' order by cast(no as integer) DESC';

    Database db = await getDataBase();
    var res = await db.rawQuery('select max(cast(no as integer)) from ' + name + ' where ' + where, data);
    if (res.length > 0) {
      return res[0]['max(cast(no as integer))'] ?? 0;
    }
    return 0;
  }

  Future<bool> hadScan(code, type) async {
    Database db = await getDataBase();
    List<Map> res =
        await db.rawQuery('select count(id) as count from ' + name + ' where waybillNo = ? and type= ?', [code, type]);
    if (res.length > 0) {
      int count = res[0]["count"];
      return count > 0;
    }
    return false;
  }

  Future<List<ScanItemEntity>> getScanUnUploadList(type) async {
    Database db = await getDataBase();
    List<Map<String, dynamic>> maps =
        await db.query(name, where: "status = ? and type = ? order by createDate Desc", whereArgs: ['N', type]);
    if (maps.length > 0) {
      List<ScanItemEntity> list = [];
      maps.forEach((v) {
        ScanItemEntity scanItemEntity = ScanItemEntity.fromJson(v);
        list.add(scanItemEntity);
      });
      return List.from(list);
    }
    return [];
  }

  Future<List<ScanItemEntity>> getScanUploadList() async {
    Database db = await getDataBase();
    List<Map<String, dynamic>> maps =
        await db.query(name, where: "status = ? order by createDate Desc", whereArgs: ['Y']);
    if (maps.length > 0) {
      List<ScanItemEntity> list = [];
      maps.forEach((v) {
        ScanItemEntity scanItemEntity = ScanItemEntity.fromJson(v);
        list.add(scanItemEntity);
      });
      return List.from(list);
    }
    return [];
  }

  Future<int> getUploadScanCount() async {
    Database db = await getDataBase();
    List<Map> res = await db.rawQuery('select count(id) as count from ' + name + ' where status = ?', ['Y']);
    if (res.length > 0) {
      return res[0]["count"];
    }
    return 0;
  }

  Future<int> getCount(String waybillNo, String type) async {
    Database db = await getDataBase();
    int? count = Sqflite.firstIntValue(
        await db.rawQuery("SELECT COUNT(*) FROM  $name where waybillNo = ? and type = ?", [waybillNo, type]));
    return count ?? 0;
  }

  /// 创建一个Scan
  static ScanItemEntity buildScan(String waybillNo, String type, String brandCode,
      {scanId = '',
      receiverName = '',
      cabinetId = '',
      receiverMobile = '',
      inboundPhotoUrl = '',
      outboundPhotoUrl = '',
      checkCode = '',
      engine = '',
      rule = '',
      shelfNo = '',
      no = '',
      day = '',
      cod = '0',
      normalWaybill = true,
      sensitiveConsumer = 0,
      boxType = 3,
      cabinetBoxId = '',
      isIntercept = '',
      cabinetLocationCode = '',
      msg = ''}) {
    ScanItemEntity scan = new ScanItemEntity();
    scan.waybillNo = waybillNo;
    scan.cabinetId = cabinetId;
    scan.type = type;
    scan.status = 'N';
    scan.brandCode = brandCode;
    scan.receiverName = receiverName;
    scan.receiverMobile = receiverMobile;
    scan.inboundPhotoUrl = inboundPhotoUrl;
    scan.scanId = scanId;
    scan.cabinetBoxId = cabinetBoxId;
    scan.boxType = boxType;
    scan.checkCode = checkCode;
    scan.engine = engine;
    scan.rule = rule;
    scan.shelfNo = shelfNo;
    scan.no = no;
    scan.cod = cod;
    scan.day = day;
    scan.normalWaybill = normalWaybill;
    scan.sensitiveConsumer = sensitiveConsumer;
    scan.isIntercept = isIntercept;
    scan.cabinetLocationCode = cabinetLocationCode;
    scan.outboundPhotoUrl = outboundPhotoUrl;
    scan.msg = msg;
    scan.createDate = new DateFormat("yyyy-MM-dd HH:mm:ss").format(DateTime.now());
    scan.ymd = new DateFormat("yyyy-MM-dd").format(DateTime.now());
    return scan;
  }

  /// 删除
  Future<int> deleteScan(String waybillNo) async {
    Database db = await getDataBase();
    return await db.delete(name, where: 'waybillNo = ?', whereArgs: [waybillNo]);
  }

  /// 批量删除
  Future deleteScanList(List ids, String type) async {
    Database db = await getDataBase();
    var batch = db.batch();
    ids.forEach((id) {
      batch.rawDelete("DELETE FROM $name WHERE id = ? and type = ?", [id, type]);
    });
    return batch.commit();
  }

  Future updateScanStatus(List ids) async {
    Database db = await getDataBase();
    String idss = ids.join(',');
    return await db.execute('UPDATE $name SET status = ? where id in ($idss)', ['Y']);
  }

  Future updateScanCheckCode(List<ScanItemEntity> list, String num) async {
    // 扫码合并
    list.forEach((scan) async {
      if (scan.isHb != 'N') {
        if (scan.checkCode != null) {
          List arr = scan.checkCode!.split('*');
          scan.checkCode = arr[0] + '*' + (num == '1' ? '' : '*' + num);
          await updateScan(scan);
        }
      }
    });
  }

  Future<int> clearScan(String type) async {
    Database db = await getDataBase();
    return await db.delete(name, where: 'type = ?', whereArgs: [type]);
  }

  Future<int> clearScanByNoYmd(String ymd) async {
    Database db = await getDataBase();
    return await db.delete(name, where: 'ymd != ? and status = ?', whereArgs: [ymd, 'Y']);
  }

  Future<int> clearScanByStatus(String status) async {
    Database db = await getDataBase();
    return await db.delete(name, where: 'status = ?', whereArgs: [status]);
  }

  Future<int> clearScanBeforeTodayY(String ymd) async {
    Database db = await getDataBase();
    return await db.delete(name, where: 'ymd != ? and status = ?', whereArgs: [ymd, 'Y']);
  }

  /// 查询一条Scan
  Future<ScanItemEntity?> getScan(String waybillNo, String type) async {
    Database db = await getDataBase();
    List<Map<String, dynamic>> maps =
        await db.query(name, where: 'waybillNo = ? and type = ?', whereArgs: [waybillNo, type]);
    if (maps.length > 0) {
      ScanItemEntity scan = ScanItemEntity.fromJson(maps.first);
      return scan;
    }
    return null;
  }

  Future batchSaveScan(List list) async {
    Database db = await getDataBase();
    var batch = db.batch();
    for (ScanItemEntity scan in list) {
      ScanItemEntity? oldScan = await getScan(scan.waybillNo, scan.type!);
      if (oldScan == null) {
        batch.insert(name, scan.toJson());
      }
    }
    return await batch.commit();
  }

  ///插入(创建)
  Future<ScanItemEntity?> saveScan(ScanItemEntity scan) async {
    Database db = await getDataBase();
    var batch = db.batch();
    ScanItemEntity? oldScan = await getScan(scan.waybillNo, scan.type!);
    if (oldScan != null) {
      batch.update(name, scan.toJson(), where: "waybillNo = ? and type = ?", whereArgs: [scan.waybillNo, scan.type]);
      scan.id = oldScan.id;
    }
    if (oldScan == null) {
      batch.insert(name, scan.toJson());
    }
    await batch.commit();
    ScanItemEntity? scanNew = await getScan(scan.waybillNo, scan.type!);
    return scanNew;
  }

  ///更新
  Future updateScan(ScanItemEntity scan) async {
    Database db = await getDataBase();
    var batch = db.batch();
    batch.update(name, scan.toJson(), where: "waybillNo = ? and type = ?", whereArgs: [scan.waybillNo, scan.type]);
    return await batch.commit();
  }

  ///更新单号
  Future updateScanSheetNo(ScanItemEntity scan, String oldWaybillNo) async {
    Database db = await getDataBase();
    var batch = db.batch();
    batch.update(name, scan.toJson(), where: "waybillNo = ? and type = ?", whereArgs: [oldWaybillNo, scan.type]);
    return await batch.commit();
  }
}
