import 'dart:async';

class DebounceUtils {
  // 防抖
  static Timer? _debounceTimer;
  
  static void debounce(Function func, [int milliseconds = 500]) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(Duration(milliseconds: milliseconds), () {
      func.call();
    });
  }

static DateTime? _throttleTime;

static dynamic throttle(Function func, [int milliseconds = 500]) {
  if (_throttleTime == null || 
      DateTime.now().difference(_throttleTime!) > Duration(milliseconds: milliseconds)) {
    _throttleTime = DateTime.now();
    return func.call();
  }
  return null;
}


  
  static final Map<String, DateTime> _throttleTimes = {};
  
  static Future<T> throttleWithCallback<T>({
    required String key,
    required Future<T> Function() func,
    int duration = 1000,
  }) async {
    if (_throttleTimes.containsKey(key)) {
      final lastTime = _throttleTimes[key]!;
      if (DateTime.now().difference(lastTime) < Duration(milliseconds: duration)) {
        throw '操作太频繁';
      }
    }
    
    _throttleTimes[key] = DateTime.now();
    
    try {
      return await func();
    } catch (e) {
      print("节流执行错误: $e");
      rethrow;
    }
  }
}