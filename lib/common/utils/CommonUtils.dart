// ignore_for_file: unnecessary_null_comparison, deprecated_member_use

import 'dart:async';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:cabinet_flutter_app/common/config/Config.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/dao/PhotoDao.dart';
import 'package:cabinet_flutter_app/common/entitys/box_item_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/customer_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/customer_mobile_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/package_view_sj_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/user_info_entity.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/localization/DefaultLocalizations.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/redux/LocaleRedux.dart';
import 'package:cabinet_flutter_app/common/redux/ThemeRedux.dart';
import 'package:cabinet_flutter_app/common/style/AppStringBase.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CabinetBoxUtil.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/utils/SoundUtils.dart';
import 'package:cabinet_flutter_app/page/finance//CustimJPasswordField.dart';
import 'package:cabinet_flutter_app/widget/AppFlexButton.dart';
import 'package:cabinet_flutter_app/widget/InputWidget.dart';
import 'package:cabinet_flutter_app/widget/selfWdiget/CommonRowWidget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:redux/redux.dart';
import 'package:scan/scan.dart';
import 'package:url_launcher/url_launcher.dart';

import '../entitys/cabinet_shop_entity.dart';
import 'FormValidateUtil.dart';
import 'RegExpUtil.dart';

late TabController tabController;

///通用逻辑
class CommonUtils {
  static const double MILLIS_LIMIT = 1000.0;

  static const double SECONDS_LIMIT = 60 * MILLIS_LIMIT;

  static const double MINUTES_LIMIT = 60 * SECONDS_LIMIT;

  static const double HOURS_LIMIT = 24 * MINUTES_LIMIT;

  static const double DAYS_LIMIT = 30 * HOURS_LIMIT;

  static double sStaticBarHeight = 0.0;

  static Scan scanPlugin = new Scan();

  static void initStatusBarHeight(context) async {
    sStaticBarHeight = 48 / MediaQuery.of(context).devicePixelRatio;
  }

  static String getDateStr(DateTime date) {
    if (date == null) {
      return "";
    } else if (date.toString().length < 10) {
      return date.toString();
    }
    return date.toString().substring(0, 10);
  }

  /// 根据单号识别快递公司(本地)
  static String checkCompany(String sheetNo) {
    String company = '未知';
    if (new RegExp(
            r"/^[Dd]{2}[8-9][0-9]{15}$|^([Yy])[0-9]{12}$|^([Gg])8[0-9]{16}$|^([Mm])[0-9]{14}$|^([Yy])([Tt])[0-9a-zA-Z]{13}$/")
        .hasMatch(sheetNo)) {
      company = 'YTO';
    }
    if (new RegExp(r"/^(STO)[0-9]{10}$/").hasMatch(sheetNo)) {
      company = 'STO';
    }
    if (new RegExp(r"/TT(66|88|99|(5([67])))\d{11}$/").hasMatch(sheetNo)) {
      company = 'TTKDEX';
    }
    if (new RegExp(r"/771206\d{8}$/").hasMatch(sheetNo)) {
      company = 'ZTO';
    }
    if (new RegExp(r"/^(JT)[0-9]{13}$/").hasMatch(sheetNo)) {
      company = 'JT';
    }
    return company;
  }

  ///日期格式转换
  static String getNewsTimeStr(DateTime date) {
    int subTime = DateTime.now().millisecondsSinceEpoch - date.millisecondsSinceEpoch;

    if (subTime < MILLIS_LIMIT) {
      return "刚刚";
    } else if (subTime < SECONDS_LIMIT) {
      return (subTime / MILLIS_LIMIT).round().toString() + " 秒前";
    } else if (subTime < MINUTES_LIMIT) {
      return (subTime / SECONDS_LIMIT).round().toString() + " 分钟前";
    } else if (subTime < HOURS_LIMIT) {
      return (subTime / MINUTES_LIMIT).round().toString() + " 小时前";
    } else if (subTime < DAYS_LIMIT) {
      return (subTime / HOURS_LIMIT).round().toString() + " 天前";
    } else {
      return getDateStr(date);
    }
  }

  static splitFileNameByPath(String path) {
    return path.substring(path.lastIndexOf("/"));
  }

  static getFullName(String repositoryUrl) {
    if (repositoryUrl.substring(repositoryUrl.length - 1) == "/") {
      repositoryUrl = repositoryUrl.substring(0, repositoryUrl.length - 1);
    }
    String fullName = '';
    List<String> splicurl = repositoryUrl.split("/");
    if (splicurl.length > 2) {
      fullName = splicurl[splicurl.length - 2] + "/" + splicurl[splicurl.length - 1];
    }
    return fullName;
  }

  static pushTheme(Store store, int index) {
    ThemeData themeData;
    List<MaterialColor>? colors = getThemeListColor();
    themeData = new ThemeData(primarySwatch: colors[index], platform: TargetPlatform.iOS);
    store.dispatch(new RefreshThemeDataAction(themeData));
  }

  // 切换语言
  static changeLocale(Store<AppState> store, int index) {
    Locale? locale = store.state.platformLocale;
    switch (index) {
      case 1:
        locale = Locale('zh', 'CH');
        break;
      case 2:
        locale = Locale('en', 'US');
        break;
    }
    if (locale != null) {
      store.dispatch(RefreshLocaleAction(locale));
    }
  }

  static AppStringBase getLocale(BuildContext context) {
    return AppLocalizations.of(context).currentLocalized;
  }

  static List<MaterialColor> getThemeListColor() {
    return [
      AppColors.primarySwatch,
      Colors.brown,
      Colors.blue,
      Colors.teal,
      Colors.amber,
      Colors.blueGrey,
      Colors.deepOrange,
    ];
  }

  static const IMAGE_END = [".png", ".jpg", ".jpeg", ".gif", ".svg"];

  static isImageEnd(path) {
    bool image = false;
    for (String item in IMAGE_END) {
      if (path.indexOf(item) + item.length == path.length) {
        image = true;
      }
    }
    return image;
  }

  static copy(String data, BuildContext context) {
    Clipboard.setData(new ClipboardData(text: data));
    Fluttertoast.showToast(msg: CommonUtils.getLocale(context).optionShareCopySuccess);
  }

  static Future<Null> showLoadingDialog(BuildContext context, [String message = '']) {
    return showDialog(
        context: context,
        builder: (BuildContext context) {
          return Material(
              color: Colors.transparent,
              child: WillPopScope(
                onWillPop: () async {
                  Navigator.of(context).pop();
                  return true;
                },
                child: Center(
                  child: Container(
                    width: 200.0,
                    height: 200.0,
                    padding: EdgeInsets.all(4.0),
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      //用一个BoxDecoration装饰器提供背景图片
                      borderRadius: BorderRadius.all(Radius.circular(4.0)),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        Container(child: SpinKitCubeGrid(color: Color(AppColors.white))),
                        Container(height: 10.0),
                        Container(
                            child: Text(message.isEmpty ? CommonUtils.getLocale(context).loadingText : message,
                                style: TextStyle(
                                  color: Color(AppColors.textColorWhite),
                                  fontSize: 13,
                                ))),
                      ],
                    ),
                  ),
                ),
              ));
        });
  }

  static Future<dynamic> confirm(BuildContext context, String dialogContent,
      {String title = '温馨提示', String confirmText = '确定', String cancelText = '取消'}) {
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text(dialogContent),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text(cancelText),
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
            TextButton(
              child: Text(confirmText),
              onPressed: () {
                Navigator.of(context).pop(true);
              },
            ),
          ],
        );
      },
    );
  }

  static Future<Null> customConfirmByReason(BuildContext context, String dialogContent, onPressed(backReason),
      {String title = '温馨提示',
      bool showClose = true,
      bool showInput = false,
      String confirmText = '是的',
      String warningText = '',
      String cancelText = '取消',
      TextInputType textInputType = TextInputType.name,
      List<Widget>? actions,
      List<Widget>? contentList,
      String backReason = '',
      String changeText = ''}) {
    TextEditingController backReasonController = TextEditingController();
    GlobalKey<FormState> _formKey = new GlobalKey<FormState>();
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
          return AlertDialog(
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(title),
                Offstage(
                    offstage: !showClose,
                    child: IconButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        icon: Icon(
                          Icons.close,
                          size: 20,
                        ),
                        iconSize: 12))
              ],
            ),
            content: SingleChildScrollView(
              child: ListBody(
                  children: CheckUtils.isNotNull(dialogContent)
                      ? <Widget>[
                          Text(dialogContent),
                          CheckUtils.isNotNull(warningText)
                              ? Text(
                                  warningText,
                                  style: TextStyle(color: Colors.red),
                                )
                              : Container(),
                          showInput
                              ? Container(
                                  margin: EdgeInsets.fromLTRB(0, 10, 0, 0),
                                  padding: EdgeInsets.fromLTRB(0, 10, 0, 10),
                                  child: Form(
                                    key: _formKey,
                                    child: TextFormField(
                                      autofocus: true,
                                      maxLength: 60,
                                      minLines: 3,
                                      maxLines: 3,
                                      inputFormatters: [LengthLimitingTextInputFormatter(100)],
                                      validator: (value) => FormValidateUtil.backReasonValidate(value!),
                                      keyboardType: textInputType,
                                      decoration: InputDecoration(
                                          hintText: changeText == 'changeText'
                                              ? '请输入取消原因，不超过60个字'
                                              : changeText == 'unusualOut'
                                                  ? '请输入出库原因，不超过60个字'
                                                  : changeText == 'logOff'
                                                      ? '请输入注销原因，不超过60个字'
                                                      : '请输入取出原因，不超过60个字',
                                          labelText: changeText == 'changeText'
                                              ? '取消原因'
                                              : changeText == 'unusualOut'
                                                  ? '出库原因'
                                                  : changeText == 'logOff'
                                                      ? '注销原因'
                                                      : '取出原因',
                                          focusedBorder: OutlineInputBorder(
                                              borderRadius: BorderRadius.circular(7),
                                              borderSide: BorderSide(color: Colors.red)),
                                          enabledBorder: InputBorder.none,
                                          errorBorder: InputBorder.none,
                                          disabledBorder: InputBorder.none,
                                          errorMaxLines: 1,
                                          border: OutlineInputBorder(
                                              borderRadius: BorderRadius.circular(7),
                                              borderSide: BorderSide(color: Colors.red)),
                                          contentPadding: EdgeInsets.fromLTRB(5, 2, 5, 2),
                                          suffixIcon: backReason.length > 0
                                              ? Container(
                                                  width: 20,
                                                  height: 20,
                                                  child: IconButton(
                                                    alignment: Alignment.center,
                                                    padding: EdgeInsets.zero,
                                                    iconSize: 18,
                                                    icon: Icon(
                                                      Icons.cancel,
                                                      color: DefaultConfig().configs.GREY_COLOR,
                                                    ),
                                                    onPressed: () {
                                                      backReasonController.value = TextEditingValue(text: '');
                                                      backReason = '';
                                                      setState(() {});
                                                    },
                                                  ),
                                                )
                                              : Text('')),
                                      onChanged: (String value) {
                                        setState(() {
                                          backReason = value;
                                        });
                                      },
                                      controller: backReasonController,
                                      textInputAction: TextInputAction.done,
                                    ),
                                  ),
                                )
                              : Container()
                        ]
                      : contentList != null
                          ? contentList
                          : []),
            ),
            actionsAlignment: MainAxisAlignment.center,
            actionsPadding: EdgeInsets.only(bottom: 10, left: 15, right: 15),
            actions: <Widget>[
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                TextButton(
                    onPressed: () async {
                      Navigator.of(context).pop();
                    },
                    child: Text('取消', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
                    style: ButtonStyle(
                        foregroundColor: MaterialStateProperty.resolveWith((states) {
                          return Colors.black;
                        }),
                        backgroundColor: MaterialStateProperty.resolveWith((states) {
                          return DefaultConfig().configs.BG_COLOR;
                        }),
                        minimumSize: MaterialStateProperty.all(Size(90, 40)))),
                TextButton(
                    onPressed: () async {
                      var validator = _formKey.currentState?.validate();
                      if (validator!) {
                        onPressed(backReason);
                      }
                    },
                    child: Text(confirmText, style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
                    style: ButtonStyle(
                        foregroundColor: MaterialStateProperty.resolveWith((states) {
                          return Colors.white;
                        }),
                        backgroundColor: MaterialStateProperty.resolveWith((states) {
                          return Theme.of(context).primaryColor;
                        }),
                        minimumSize: MaterialStateProperty.all(Size(90, 40)))),
              ])
            ],
          );
        });
      },
    );
  }

  static Future<Null> customConfirm(BuildContext context, String dialogContent,
      {String title = '温馨提示',
      bool showClose = true,
      VoidCallback? onPressed,
      String confirmText = '确定',
      String cancelText = '取消',
      List<Widget>? actions,
      List<Widget>? contentList}) {
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(title),
              Offstage(
                  offstage: !showClose,
                  child: IconButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      icon: Icon(
                        Icons.close,
                        size: 20,
                      ),
                      iconSize: 12))
            ],
          ),
          content: SingleChildScrollView(
            child: ListBody(
              children: CheckUtils.isNotNull(dialogContent)
                  ? <Widget>[
                      Text(dialogContent),
                    ]
                  : contentList != null
                      ? contentList
                      : [],
            ),
          ),
          actionsAlignment: MainAxisAlignment.center,
          actionsPadding: EdgeInsets.only(bottom: 10, left: 15, right: 15),
          actions: actions ??
              <Widget>[
                TextButton(
                  child: Text(cancelText),
                  style: ButtonStyle(
                    minimumSize: MaterialStateProperty.all(Size(90, 40)),
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
                TextButton(
                  child: Text(confirmText),
                  style: ButtonStyle(
                    minimumSize: MaterialStateProperty.all(Size(90, 40)),
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                    if (onPressed != null) {
                      onPressed();
                    }
                  },
                ),
              ],
        );
      },
    );
  }

  static Future<Null> accountTestLogin(BuildContext context, List<dynamic> accounts, onPressed(save)) {
    List<dynamic> list = [];
    accounts.forEach((item) {
      /// WAIT - 待验证，DONE - 验证成功， FAIL - 验证失败, PENDING - 验证中
      list.add({'channel': item['channel'], 'status': 'WAIT'});
    });

    int time = 0;
    int okNum = 0;

    testLogin(setState) async {
      for (int i = 0; i < accounts.length; i++) {
        UserInfoEntity item = UserInfoEntity.fromJson(accounts[i]);
        int index = list.indexWhere((v) => v['channel'] == item.channel);
        if (index > -1) {
          setState(() {
            list[index]['status'] = 'PENDING';
          });
          var res = await CabinetBoxUtil.loginCheck(context, item, '', false, item.channel);
          if (res != null && res.result) {
            if (res.data == 1) {
              setState(() {
                list[index]['status'] = 'FAIL';
              });
              Fluttertoast.showToast(msg: '校验失败,请输入验证码重新校验');
            } else {
              setState(() {
                okNum++;
                list[index]['status'] = 'DONE';
              });
              Fluttertoast.showToast(msg: '校验成功');
            }
          } else {
            setState(() {
              list[index]['status'] = 'FAIL';
            });
          }
        }
      }
    }

    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            time++;
            if (time == 1) {
              testLogin(setState);
            }
            return WillPopScope(
              onWillPop: () async => true,
              child: Material(
                type: MaterialType.transparency,
                child: Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Container(
                        width: 300,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.all(
                            Radius.circular(10.0),
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          mainAxisSize: MainAxisSize.min,
                          children: <Widget>[
                            Row(
                              children: <Widget>[
                                Expanded(
                                  child: Container(
                                    height: 60.0,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).primaryColor,
                                      borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(10.0),
                                        topRight: Radius.circular(10.0),
                                      ),
                                    ),
                                    child: Text(
                                      '账号验证登录',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 20,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                )
                              ],
                            ),
                            Container(
                              width: 300,
                              padding: EdgeInsets.only(bottom: 20, top: 10, right: 10),
                              child: Column(
                                children: [
                                  Wrap(
                                    alignment: WrapAlignment.start,
                                    children: list.map((item) {
                                      return Container(
                                        width: 130,
                                        padding: EdgeInsets.fromLTRB(5, 15, 5, 15),
                                        margin: EdgeInsets.only(top: 10, left: 10),
                                        decoration: BoxDecoration(
                                          color: Colors.grey.shade100,
                                          borderRadius: BorderRadius.all(Radius.circular(5)),
                                        ),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Text(
                                              '${Config().brandMap[item['channel']]}',
                                              style: TextStyle(
                                                color: item['status'] == 'FAIL' ? Colors.red : Colors.black87,
                                              ),
                                            ),
                                            Padding(padding: EdgeInsets.only(left: 5)),
                                            Offstage(
                                              offstage: item['status'] != 'PENDING',
                                              child: Row(
                                                children: [
                                                  SpinKitCircle(
                                                    color: Colors.orange,
                                                    size: 12,
                                                  ),
                                                  Padding(
                                                    padding: EdgeInsets.only(left: 5),
                                                    child: Text(
                                                      '验证中',
                                                      style: TextStyle(color: Colors.orange, fontSize: 12),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Offstage(
                                              offstage: item['status'] != 'WAIT',
                                              child: Text(
                                                '待验证',
                                                style: TextStyle(color: Colors.orangeAccent, fontSize: 12),
                                              ),
                                            ),
                                            Offstage(
                                              offstage: item['status'] != 'DONE',
                                              child: Icon(
                                                Icons.check_circle,
                                                color: Colors.green,
                                                size: 20,
                                              ),
                                            ),
                                            Offstage(
                                              offstage: item['status'] != 'FAIL',
                                              child: Icon(
                                                Icons.error_outlined,
                                                color: Colors.redAccent,
                                                size: 20,
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    }).toList(),
                                  ),
                                  Padding(
                                      padding: EdgeInsets.all(10),
                                      child: Text(
                                        okNum != list.length ? '存在异常账号，请前去处理~' : '恭喜你，账号全部验证成功~',
                                        style: TextStyle(
                                          color: okNum != list.length ? Colors.red : Colors.green,
                                          fontSize: 13,
                                        ),
                                      ))
                                ],
                              ),
                            ),
                            Row(
                              children: <Widget>[
                                Expanded(
                                  child: InkWell(
                                    onTap: () {
                                      onPressed(false);
                                      Navigator.of(context).pop();
                                    },
                                    child: Container(
                                      height: 50.0,
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                        border: Border(
                                          top: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                          right: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                        ),
                                      ),
                                      child: Text(
                                        okNum != list.length ? '取消' : '知道了',
                                        style: TextStyle(
                                          color: okNum != accounts.length ? Colors.grey.shade400 : Colors.green,
                                          fontSize: 15,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                okNum != list.length
                                    ? Expanded(
                                        child: InkWell(
                                          onTap: () {
                                            onPressed(true);
                                            Navigator.of(context).pop();
                                          },
                                          child: Container(
                                            height: 50.0,
                                            alignment: Alignment.center,
                                            decoration: BoxDecoration(
                                              border: Border(
                                                top: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                              ),
                                            ),
                                            child: Text(
                                              '去处理',
                                              style: TextStyle(
                                                color: Colors.red,
                                                fontSize: 15,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ),
                                        ),
                                      )
                                    : Container(),
                              ],
                            )
                          ],
                        ),
                      ),
                      Padding(padding: EdgeInsets.only(bottom: 60.0))
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  static Future<Null> showList(BuildContext context, String dialogContent,
      {String title = '温馨提示',
      bool showClose = true,
      VoidCallback? onPressed,
      List<Widget>? actions,
      Widget? header,
      List<Widget>? contentList}) {
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return AlertDialog(
          titlePadding: EdgeInsets.fromLTRB(15, 10, 15, 0),
          title: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(title),
                ],
              ),
              header ?? Container()
            ],
          ),
          contentPadding: EdgeInsets.fromLTRB(15, 5, 15, 0),
          content: SingleChildScrollView(
            child: ListBody(
              children: CheckUtils.isNotNull(dialogContent)
                  ? <Widget>[
                      Text(dialogContent),
                    ]
                  : contentList ?? [],
            ),
          ),
          actionsAlignment: MainAxisAlignment.center,
          actionsPadding: EdgeInsets.only(bottom: 5, left: 15, right: 15, top: 0),
          actions: actions ??
              <Widget>[
                Container(
                  height: 32,
                  child: OutlinedButton(
                    child: Text('确定'),
                    style: ButtonStyle(
                      minimumSize: MaterialStateProperty.all(Size(200, 40)),
                      shape: MaterialStateProperty.all(StadiumBorder()),
                      foregroundColor: MaterialStateProperty.resolveWith((states) {
                        return Colors.white;
                      }),
                      backgroundColor: MaterialStateProperty.resolveWith((states) {
                        if (states.contains(MaterialState.pressed)) {
                          return DefaultConfig().configs.PRIMARY_COLOR_LIGHT;
                        }
                        return Theme.of(context).primaryColor;
                      }),
                      side: MaterialStateProperty.all(BorderSide(color: Theme.of(context).primaryColor)),
                    ),
                    onPressed: () {
                      Navigator.of(context).pop();
                      if (onPressed != null) {
                        onPressed();
                      }
                    },
                  ),
                )
              ],
        );
      },
    );
  }

  static Future<Null> alert(BuildContext context,
      {String title = '温馨提示',
      List<Widget>? content,
      String? dialogContent,
      String buttonLabel = '确定',
      String buttonType = 'primary',
      bool buttonDisable = false,
      VoidCallback? onPressed}) {
    double width = MediaQuery.of(context).size.width;
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return UnconstrainedBox(
          constrainedAxis: Axis.vertical,
          child: SizedBox(
            width: width - 10,
            child: AlertDialog(
              title: Center(
                  child: Text(
                title,
                style: TextStyle(fontSize: 18, color: Colors.black, fontWeight: FontWeight.w500),
              )),
              content: SingleChildScrollView(
                child: ListBody(
                  children: CheckUtils.isNotNull(content)
                      ? content ?? []
                      : <Widget>[
                          Text(dialogContent ?? ''),
                        ],
                ),
              ),
              actionsAlignment: MainAxisAlignment.center,
              actionsPadding: EdgeInsets.only(bottom: 10),
              actions: <Widget>[
                MaterialButton(
                  minWidth: width * .6,
                  color: buttonDisable ? Color(0xFFFFE2D8) : Theme.of(context).primaryColor,
                  textColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(28.0),
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                    if (!buttonDisable) {
                      if (onPressed != null) {
                        onPressed();
                      }
                    }
                  },
                  child: Text(
                    buttonLabel,
                    style: TextStyle(fontSize: 16),
                  ),
                )
              ],
            ),
          ),
        );
        return AlertDialog(
          title: Center(
              child: Text(
            title,
            style: TextStyle(fontSize: 18, color: Colors.black, fontWeight: FontWeight.w500),
          )),
          content: SingleChildScrollView(
            child: ListBody(
              children: CheckUtils.isNotNull(content)
                  ? content ?? []
                  : <Widget>[
                      Text(dialogContent ?? ''),
                    ],
            ),
          ),
          actionsAlignment: MainAxisAlignment.center,
          actions: <Widget>[
            MaterialButton(
              color: buttonDisable ? Color(0xFFFFE2D8) : Theme.of(context).primaryColor,
              textColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(28.0),
              ),
              onPressed: () {
                Navigator.of(context).pop();
                if (!buttonDisable) {
                  if (onPressed != null) {
                    onPressed();
                  }
                }
              },
              child: Text(
                buttonLabel,
                style: TextStyle(fontSize: 16),
              ),
            )
          ],
        );
      },
    );
  }

  static Future<Null> passwordInput(BuildContext context,
      {String title = '请输入支付密码',
      List<Widget>? content,
      String? dialogContent,
      String buttonLabel = '确定',
      String buttonType = 'primary',
      bool buttonDisable = false,
      VoidCallback? onPressed}) {
    double width = MediaQuery.of(context).size.width;
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return UnconstrainedBox(
          constrainedAxis: Axis.vertical,
          child: SizedBox(
            width: width - 10,
            child: AlertDialog(
              title: Center(
                  child: Text(
                title,
                style: TextStyle(fontSize: 18, color: Colors.black, fontWeight: FontWeight.w500),
              )),
              content: SingleChildScrollView(
                  child: Container(
                height: 40,
                child: CustomJPasswordField(''),
              )),
              actionsAlignment: MainAxisAlignment.center,
              actionsPadding: EdgeInsets.only(bottom: 10),
              actions: <Widget>[
                TextButton(
                  style: ButtonStyle(
                    minimumSize: MaterialStateProperty.all(Size(200, 40)),
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                    if (onPressed != null) {
                      if (!buttonDisable) {
                        onPressed();
                      }
                    }
                  },
                  child: Text(
                    buttonLabel,
                    style: TextStyle(fontSize: 16),
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }

  static Future<Null> inputNoDialog(BuildContext context, String? hitText, ValueChanged<String> onTap) {
    var code;
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return Material(
          type: MaterialType.transparency,
          child: Padding(
            padding: const EdgeInsets.only(top: 120),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Container(
                  width: 300,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.all(
                      Radius.circular(10.0),
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Row(
                        children: <Widget>[
                          Expanded(
                            child: Container(
                              height: 50.0,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: Theme.of(context).primaryColor,
//                              border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0)), // borderRadius存在时，border四个边都要存在
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(10.0),
                                  topRight: Radius.circular(10.0),
                                ),
                              ),
                              child: Text('录入', style: TextStyle(color: Colors.white, fontSize: 18.0)),
                            ),
                          )
                        ],
                      ),
                      Padding(padding: EdgeInsets.only(top: 10.0)),
                      Row(
                        children: <Widget>[
                          Padding(padding: EdgeInsets.only(left: 10.0)),
                          InputWidget(
                            hintText: hitText != null ? hitText : '请输入',
                            autofocus: true,
                            onSaved: (String? value) {
                              code = value;
                            },
                          ),
                          Padding(padding: EdgeInsets.only(left: 10.0)),
                        ],
                      ),
                      Padding(padding: EdgeInsets.only(top: 10.0)),
                      Row(
                        children: <Widget>[
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                Navigator.of(context).pop();
                              },
                              child: Container(
                                height: 45.0,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  border: Border(
                                    top: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                    right: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                  ),
                                ),
                                child: Text(
                                  '取消',
                                  style: TextStyle(color: Colors.grey, fontSize: 16.0),
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                Navigator.of(context).pop();
                                onTap(code);
                              },
                              child: Container(
                                height: 45.0,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  border: Border(
                                    top: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                  ),
                                ),
                                child: Text(
                                  '添加',
                                  style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 16.0),
                                ),
                              ),
                            ),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
//              Padding(padding: EdgeInsets.only(bottom: 60.0))
              ],
            ),
          ),
        );
      },
    );
  }

  static getCompanyAlais(String company) {
    var c = "";
    switch (company) {
      case 'YTO':
        c = "圆";
        break;
      case 'ZTO':
        c = "中";
        break;
      case 'STO':
        c = "申";
        break;
      case 'YUNDA':
        c = "韵";
        break;
      case 'HTKY':
        c = "百世";
        break;
    }
    return c;
  }

  static Future<Null> showCommitOptionDialog(
    BuildContext context,
    List<String> commitMaps,
    ValueChanged<int> onTap, {
    width = 250.0,
    height = 400.0,
    List<Color>? colorList,
  }) {
    return showDialog(
        context: context,
        builder: (BuildContext context) {
          return Center(
            child: Container(
              width: width,
              height: height,
              padding: EdgeInsets.all(4.0),
              margin: EdgeInsets.all(20.0),
              decoration: BoxDecoration(
                color: Color(AppColors.white),
                //用一个BoxDecoration装饰器提供背景图片
                borderRadius: BorderRadius.all(Radius.circular(4.0)),
              ),
              child: ListView.builder(
                  itemCount: commitMaps.length,
                  itemBuilder: (context, index) {
                    return AppFlexButton(
                      maxLines: 2,
                      mainAxisAlignment: MainAxisAlignment.start,
                      fontSize: 14.0,
                      color: colorList != null ? colorList[index] : Theme.of(context).primaryColor,
                      text: commitMaps[index],
                      textColor: Color(AppColors.white),
                      onPress: () {
                        Navigator.pop(context);
                        onTap(index);
                      },
                    );
                  }),
            ),
          );
        });
  }

  static transMap2List() {
    List list = [];
    DefaultConfig().configs.EXPRESS.keys.toList().forEach((item) {
      list.add({"brandName": DefaultConfig().configs.EXPRESS[item], "brandCode": item});
    });
    return list;
  }

  static selectExpress(BuildContext context, onPressed(item), [isCanNull = false]) async {
    List<Widget> list = <Widget>[];
//    List brands = await LocalStorage.getJson(DefaultConfig().configs.BRAND_OPEN_LIST);
    List brands = transMap2List();
    if (brands.length == 0) {
      SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
      Fluttertoast.showToast(msg: '暂无开通品牌');
      return false;
    }

    if (isCanNull) {
      brands.insert(0, {"brandName": "全部", "brandCode": ""});
    }

    brands.forEach(
      (v) {
        String imgUrl = CommonUtils.getExpressLogo(v['brandCode']);
        list.add(
          Divider(
            height: 1.0,
          ),
        );

        list.add(
          ListTile(
            leading: CircleAvatar(
              radius: 15.0,
              backgroundColor: Colors.white,
              backgroundImage: AssetImage(imgUrl),
            ),
            title: Text(
              v['brandCode'] == '' ? v['brandName'] : (v['brandName'] + '-' + v['brandCode']),
            ),
            onTap: () {
              onPressed(v['brandCode']);
              Navigator.pop(context);
            },
          ),
        );
      },
    );

    return showModalBottomSheet(
      isDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return Container(
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: list,
            ),
          ),
        );
      },
    );
  }

  static selectScanType(BuildContext context, onPressed(item), [isCanNull = false]) {
    List<Widget> list = <Widget>[];

    Map express = {};

    if (isCanNull) {
      express = {'': '不选择'};
      express.addAll(DefaultConfig().configs.SCAN_TYPE);
    } else {
      express = DefaultConfig().configs.SCAN_TYPE;
    }

    express.forEach(
      (k, v) {
//        String imgUrl = CommonUtils.getExpressLogo(k);
        Color color = Colors.white;
        String text = '';
        switch (k) {
          case 'DP':
            text = '合';
            color = Theme.of(context).primaryColor;
            break;
          case 'PJ':
            text = '派';
            color = Colors.orangeAccent;
            break;
          case 'QS':
            text = '签';
            color = Colors.grey;
            break;
          case 'DS':
            text = '到';
            color = Colors.green;
            break;
          case 'WT':
            text = '问';
            color = Colors.redAccent;
            break;
          case 'FW':
            text = '发';
            color = Colors.black87;
            break;
        }
        list.add(
          Divider(
            height: 1.0,
          ),
        );
        list.add(
          Container(
            height: 65.0,
            child: ListTile(
              contentPadding: EdgeInsets.fromLTRB(20.0, 5.0, 10.0, 0.0),
              title: Row(
                children: <Widget>[
                  Container(
                      width: 30.0,
                      height: 30.0,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(color: color, borderRadius: BorderRadius.all(Radius.circular(30.0))),
                      child: Text(
                        text,
                        style: TextStyle(color: Colors.white, fontSize: 12.0),
                      )),
                  Padding(padding: EdgeInsets.only(left: 15.0)),
                  Text(
                    v + '(' + k + ')',
                  )
                ],
              ),
              onTap: () {
                onPressed(k);
                Navigator.pop(context);
              },
            ),
          ),
        );
      },
    );

    return showModalBottomSheet(
      isDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return Container(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: list,
          ),
        );
      },
    );
  }

  static choosePickCodeRule(BuildContext context, onPressed(item)) {
    List<Widget> list = <Widget>[];
    DefaultConfig().configs.PICKCODERULES.forEach((k, v) {
      list.add(
        InkWell(
          onTap: () {
            onPressed(k);
            Navigator.pop(context);
          },
          child: Container(
            height: 50.0,
            padding: EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
            decoration: BoxDecoration(
                color: Colors.white, border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0))),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                Text(
                  v,
                  style: TextStyle(fontSize: 16.0, color: Colors.black87),
                )
              ],
            ),
          ),
        ),
      );
    });

    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Container(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Column(
                mainAxisSize: MainAxisSize.min,
                children: list,
              ),
              InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Container(
                  margin: EdgeInsets.only(top: 10.0),
                  height: 50.0,
                  padding: EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
                  decoration: BoxDecoration(
                      color: Colors.white, border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0))),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      Text(
                        '取消',
                        style: TextStyle(fontSize: 18.0, color: Colors.grey.shade500),
                      )
                    ],
                  ),
                ),
              )
            ],
          ),
        );
      },
    );
  }

  static customizeBottomSheet(BuildContext context, List choice, String showKey, onPressed(item)) {
    List<Widget> list = <Widget>[];
    choice.forEach((v) {
      list.add(
        InkWell(
          onTap: () {
            onPressed(v);
            Navigator.pop(context);
          },
          child: Container(
            height: 50.0,
            padding: EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
            decoration: BoxDecoration(
                color: Colors.white, border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0))),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                Text(
                  '${v[showKey]}',
                  style: TextStyle(fontSize: 18.0, color: Colors.black87),
                )
              ],
            ),
          ),
        ),
      );
    });

    return showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Column(
                mainAxisSize: MainAxisSize.min,
                children: list,
              ),
              InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Container(
                  margin: EdgeInsets.only(top: 10.0),
                  height: 50.0,
                  padding: EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
                  decoration: BoxDecoration(
                      color: Colors.white, border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0))),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      Text(
                        '取消',
                        style: TextStyle(fontSize: 18.0, color: Colors.grey.shade500),
                      )
                    ],
                  ),
                ),
              )
            ],
          ),
        );
      },
    );
  }

  static Future<Null> privacyModal(BuildContext context, String dialogContent,
      {String title = '温馨提示',
      VoidCallback? onPressed,
      String confirmText = '确定',
      String cancelText = '取消',
      List<Widget>? actions,
      List<Widget>? contentList}) {
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return WillPopScope(
            onWillPop: () async {
              return false;
            },
            child: AlertDialog(
              title: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(title),
                ],
              ),
              content: SingleChildScrollView(
                child: ListBody(
                  children: CheckUtils.isNotNull(dialogContent)
                      ? <Widget>[
                          Text(dialogContent),
                        ]
                      : contentList != null
                          ? contentList
                          : [],
                ),
              ),
              actionsAlignment: MainAxisAlignment.center,
              actionsPadding: EdgeInsets.only(bottom: 10, left: 15, right: 15),
              actions: actions ??
                  <Widget>[
                    TextButton(
                      child: Text(cancelText),
                      style: ButtonStyle(
                        minimumSize: MaterialStateProperty.all(Size(90, 40)),
                      ),
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                    ),
                    TextButton(
                      child: Text(confirmText),
                      style: ButtonStyle(
                        minimumSize: MaterialStateProperty.all(Size(90, 40)),
                      ),
                      onPressed: () {
                        Navigator.of(context).pop();
                        if (onPressed != null) {
                          onPressed();
                        }
                      },
                    ),
                  ],
            ));
      },
    );
  }

  static Future<Null> comfirmOperateDialog(
    BuildContext context,
    String dialogContent,
  ) {
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('温馨提示'),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text(dialogContent),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text('取消'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text('确定'),
              onPressed: () {
                // Navigator.of(context).pop();
                NavigatorUtils.goLoginNew(context);
              },
            ),
          ],
        );
      },
    );
  }

  static customerDialog(BuildContext context, Widget widget,
      {actions = const <Widget>[], padding = const EdgeInsets.all(20.0)}) {
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return AlertDialog(
          contentPadding: padding,
          content: SingleChildScrollView(
            child: widget,
          ),
          actions: actions,
        );
      },
    );
  }

  static void showMessage(BuildContext context, {String msg = "提示", bool success = true, duration = 2}) {
    Color? color = success ? Theme.of(context).primaryColor : Colors.red;
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: color,
        content: Container(
          height: 100,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Text(
                msg,
                style: TextStyle(fontSize: 20),
              )
            ],
          ),
        ),
        duration: Duration(milliseconds: duration * 1000)));
  }

  static uniqueArrayByKey(List arr1, List arr2, String key) {
    var hash = {};
    var tmp = arr1.map((v) {
      hash[v[key]] = true;
      return v;
    }).toList();

    arr2.forEach((v) {
      if (hash[v[key]] != true) {
        tmp.add(v);
      }
    });
    return tmp;
  }

  // 获取快递公司logo
  static String getExpressLogo(String? company) {
    var imgUrl = '';
    switch (company) {
      case 'ZTO':
        imgUrl = LocalImageUtil.getImagePath('zt');
        break;
      case 'UC56':
        imgUrl = LocalImageUtil.getImagePath('ys');
        break;
      case 'ANE':
        imgUrl = LocalImageUtil.getImagePath('an');
        break;
      case 'STO':
        imgUrl = LocalImageUtil.getImagePath('st');
        break;
      case 'YTO':
        imgUrl = LocalImageUtil.getImagePath('yt');
        break;
      case 'YUNDA':
        imgUrl = LocalImageUtil.getImagePath('yd');
        break;
      case 'TTKDEX':
        imgUrl = LocalImageUtil.getImagePath('yy');
        break;
      case 'HTKY':
        imgUrl = LocalImageUtil.getImagePath('bs');
        break;
      case 'JT':
        imgUrl = LocalImageUtil.getImagePath('jt');
        break;
      case 'SF':
        imgUrl = LocalImageUtil.getImagePath('sf');
        break;
      case 'EMS':
        imgUrl = LocalImageUtil.getImagePath('ems');
        break;
      case 'POSTB':
        imgUrl = LocalImageUtil.getImagePath('yz');
        break;
      case 'JD':
        imgUrl = LocalImageUtil.getImagePath('jd');
        break;
      case 'SNWL':
        imgUrl = LocalImageUtil.getImagePath('sn');
        break;
      case 'DBKD':
        imgUrl = LocalImageUtil.getImagePath('db');
        break;
      case 'FW':
        imgUrl = LocalImageUtil.getImagePath('fw');
        break;
      case 'YZ_PDD':
        imgUrl = LocalImageUtil.getImagePath('pddYz');
        break;
      case 'YZ_TX':
        imgUrl = LocalImageUtil.getImagePath('ztoTx');
        break;
      case 'YZ_YDCS':
        imgUrl = LocalImageUtil.getImagePath('YD_CS');
        break;
      case 'YZ_MM':
        imgUrl = LocalImageUtil.getImagePath('ytoMm');
        break;
      case 'YZ_MZ':
        imgUrl = LocalImageUtil.getImagePath('mz');
        break;
      default:
        imgUrl = LocalImageUtil.getImagePath('logoAvatarCircle', isChannel: true);
        break;
    }
    return imgUrl;
  }

  static void scanQr(context, sheetNo) async {
//    String sheetNo = await scanPlugin.showScan();
//    CommonUtils.showLoadingDialog(context, '加载中');
//    var sheetInfoList = await CodeRegExpDao.getInfo(context, sheetNo, 'DS');
//    Navigator.pop(context);
//    if (sheetInfoList == null) {
//      SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
//      return;
//    }
//    ScanItemEntity sheet = sheetInfoList[0];
//
//    ScanNewItemEntity sc = ScanNewItemEntity();
//    sc.sheetNo = sheetNo;
//    sc.company = sheet.company;
//    NavigatorUtils.goSheetLogs(context, sc);
//     await NavigatorUtils.goFastScan(context, "TH");
//     await NavigatorUtils.goCabinetMangerPage(context, "TH");
  }

  static Future<Null> passwordDialog(BuildContext context, [onPressed(password)?]) {
    String? password = '';
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return Material(
          type: MaterialType.transparency,
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Container(
                  width: 300,
                  decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(10.0))),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Row(
                        children: <Widget>[
                          Expanded(
                            child: Container(
                              height: 50.0,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                  color: Theme.of(context).primaryColor,
//                              border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0)), // borderRadius存在时，border四个边都要存在
                                  borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(10.0), topRight: Radius.circular(10.0))),
                              child: Text('入口 密码', style: TextStyle(color: Colors.white, fontSize: 18.0)),
                            ),
                          )
                        ],
                      ),
                      Padding(padding: EdgeInsets.only(top: 20.0)),
                      Row(
                        children: <Widget>[
                          Padding(padding: EdgeInsets.only(left: 20.0)),
                          InputWidget(
                            label: '密码',
                            hintText: '请输入密码',
                            width: 40.0,
                            height: 40.0,
                            contentPadding: EdgeInsets.fromLTRB(5.0, 5.0, 5.0, 13.0),
                            color: Colors.grey.shade100,
                            obscureText: true,
                            autofocus: true,
                            onEditingComplete: () {
                              if (onPressed != null) {
                                onPressed(password);
                              }
                            },
                            onSaved: (String? value) {
                              password = value;
                            },
                          ),
                          Padding(padding: EdgeInsets.only(left: 20.0)),
                        ],
                      ),
                      Padding(padding: EdgeInsets.only(top: 20.0)),
                      Row(
                        children: <Widget>[
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                Navigator.of(context).pop();
                              },
                              child: Container(
                                height: 50.0,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  border: Border(
                                    top: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                    right: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                  ),
                                ),
                                child: Text(
                                  '取消',
                                  style: TextStyle(color: Colors.grey.shade400, fontSize: 18.0),
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                if (onPressed != null) {
                                  onPressed(password);
                                }
                              },
                              child: Container(
                                height: 50.0,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  border: Border(
                                    top: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                  ),
                                ),
                                child: Text(
                                  '确定',
                                  style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 18.0),
                                ),
                              ),
                            ),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
                Padding(padding: EdgeInsets.only(bottom: 60.0))
              ],
            ),
          ),
        );
      },
    );
  }

  static Future<Null> isSaveDialog(BuildContext context, onPressed(save),
      {String title = '温馨提示',
      Color? titleColor,
      String content = '即将退出该页面，将清除扫码记录?',
      String cancelText = '确认返回',
      String middleText = '',
      Color? bgColor,
      bool showCancel = true,
      bool showMiddle = false,
      String successText = '继续入柜',
      Widget? contentWidget,
      Color? successColor,
      Color? cancelColor,
      Color? middleColor,
      bool showSuccess = true}) {
    if (bgColor == null) {
      bgColor = Theme.of(context).primaryColor;
    }
    if (titleColor == null) {
      titleColor = Colors.white;
    }
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return Material(
          type: MaterialType.transparency,
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Container(
                  width: 300,
                  decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(10.0))),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Row(
                        children: <Widget>[
                          Expanded(
                            child: Container(
                              height: 50.0,
                              padding: EdgeInsets.only(top: 10, bottom: 10),
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                  color: bgColor,
                                  borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(10.0), topRight: Radius.circular(10.0))),
                              child: Text(title, style: TextStyle(color: titleColor, fontSize: 18.0)),
                            ),
                          )
                        ],
                      ),
                      contentWidget ??
                          Container(
                            padding: EdgeInsets.fromLTRB(15, 30, 15, 30),
                            alignment: Alignment.center,
                            child: Text(content),
                          ),
                      Row(
                        children: <Widget>[
                          showCancel
                              ? Expanded(
                                  child: InkWell(
                                    onTap: () {
                                      onPressed(false);
                                      Navigator.of(context).pop();
                                    },
                                    child: Container(
                                      height: 50.0,
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                        border: Border(
                                          top: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                          right: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                        ),
                                      ),
                                      child: Text(
                                        cancelText,
                                        style: TextStyle(color: cancelColor ?? Colors.grey.shade400, fontSize: 14.0),
                                      ),
                                    ),
                                  ),
                                )
                              : Container(),
                          showMiddle
                              ? Expanded(
                                  child: InkWell(
                                    onTap: () {
                                      onPressed('middle');
                                      Navigator.of(context).pop();
                                    },
                                    child: Container(
                                      height: 50.0,
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                        border: Border(
                                          top: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                          right: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                        ),
                                      ),
                                      child: Text(
                                        middleText,
                                        style: TextStyle(color: middleColor ?? Colors.grey.shade400, fontSize: 14.0),
                                      ),
                                    ),
                                  ),
                                )
                              : Container(),
                          showSuccess
                              ? Expanded(
                                  child: InkWell(
                                    onTap: () {
                                      onPressed(true);
                                      Navigator.of(context).pop();
                                    },
                                    child: Container(
                                      height: 50.0,
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                        border: Border(
                                          top: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                        ),
                                      ),
                                      child: Text(
                                        successText,
                                        style: TextStyle(
                                            color: successColor ?? Theme.of(context).primaryColor, fontSize: 14.0),
                                      ),
                                    ),
                                  ),
                                )
                              : Container(),
                        ],
                      )
                    ],
                  ),
                ),
                Padding(padding: EdgeInsets.only(bottom: 60.0))
              ],
            ),
          ),
        );
      },
    );
  }

  static Future<String?> radioSelect(BuildContext context,
      {String title = '温馨提示',
      Color? titleColor = Colors.black,
      List mapData = const [
        {'title': '投错包裹', 'subTitle': null},
        {'title': '格口已满', 'subTitle': null},
        {'title': '格口有包裹未取', 'subTitle': '异常格口,上报异常'},
        {'title': '柜门未开或故障', 'subTitle': '柜门未开,禁用格口并上报故障'}
      ],
      String cancelText = '取消',
      String content = '请选择',
      Color? bgColor = Colors.white,
      bool showCancel = true,
      String selectValue = '',
      String successText = '确认',
      Color? successColor,
      Color? cancelColor = Colors.black,
      bool showSuccess = true,
      bool showInput = true}) {
    if (bgColor == null) {
      bgColor = Theme.of(context).primaryColor;
    }
    if (titleColor == null) {
      titleColor = Colors.white;
    }
    String selectedVal = selectValue;
    GlobalKey<FormState> _formKey = new GlobalKey<FormState>();
    TextEditingController backReasonController = TextEditingController();
    TextInputType textInputType = TextInputType.name;
    String backReason = '';
    bool showInput_ = false;

    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (BuildContext context, StateSetter state) {
          changeCancelReason(value) {
            selectedVal = value;
            if (showInput) {
              if (value == '格口已满' || value == '投错包裹') {
                showInput_ = false;
              } else {
                showInput_ = true;
              }
            }
            state(() {});
          }

          List<Widget> list = <Widget>[];
          mapData.forEach((item) {
            list.add(Row(
              children: [
                Radio(value: item['title'], groupValue: selectedVal, onChanged: changeCancelReason),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item['title'],
                      style: TextStyle(fontSize: 16.0, color: Colors.black87),
                    ),
                    Offstage(
                      offstage: item['subTitle'] == null,
                      child: Text(item['subTitle'] ?? '',
                          style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 12)),
                    )
                  ],
                )
              ],
            ));
            // list.add(
            //   RadioListTile(
            //     value: item['title'],
            //     groupValue: selectedVal,
            //     dense: true,
            //     onChanged: changeCancelReason,
            //     title: Text(
            //       item['title'],
            //       style: TextStyle(fontSize: 16.0, color: Colors.black87),
            //     ),
            //     subtitle: item['subTitle'] != null
            //         ? Text(item['subTitle'], style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 12))
            //         : null,
            //   ),
            // );
          });
          return AnimatedContainer(
            duration: Duration(milliseconds: 300),
            padding: EdgeInsets.only(bottom: MediaQueryData.fromWindow(ui.window).viewInsets.bottom),
            child: Material(
              type: MaterialType.transparency,
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    Container(
                      width: MediaQuery.of(context).size.width - 80,
                      decoration:
                          BoxDecoration(color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(10.0))),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: <Widget>[
                          Row(
                            children: <Widget>[
                              Expanded(
                                child: Container(
                                  height: 36.0,
                                  padding: EdgeInsets.only(top: 10),
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                      color: bgColor,
                                      borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(10.0), topRight: Radius.circular(10.0))),
                                  child: Text(title, style: TextStyle(color: titleColor, fontSize: 18.0)),
                                ),
                              )
                            ],
                          ),
                          Offstage(
                            offstage: content == '' || content == null,
                            child: Text(content),
                          ),
                          ...list,
                          showInput_
                              ? Container(
                                  margin: EdgeInsets.fromLTRB(0, 10, 0, 0),
                                  padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
                                  child: Form(
                                    key: _formKey,
                                    child: TextFormField(
                                      autofocus: showInput_,
                                      maxLength: 60,
                                      minLines: 3,
                                      maxLines: 3,
                                      inputFormatters: [LengthLimitingTextInputFormatter(100)],
                                      validator: (value) => FormValidateUtil.backReasonValidate(value!),
                                      keyboardType: textInputType,
                                      decoration: InputDecoration(
                                          hintText: '请输入取消原因，不超过60个字',
                                          labelText: '取消原因',
                                          focusedBorder: OutlineInputBorder(
                                              borderRadius: BorderRadius.circular(7),
                                              borderSide: BorderSide(color: Colors.red)),
                                          errorMaxLines: 1,
                                          border: OutlineInputBorder(
                                              borderRadius: BorderRadius.circular(7),
                                              borderSide: BorderSide(color: Colors.red)),
                                          contentPadding: EdgeInsets.fromLTRB(5, 2, 5, 2),
                                          suffixIcon: backReason.length > 0
                                              ? Container(
                                                  width: 20,
                                                  height: 20,
                                                  child: IconButton(
                                                    alignment: Alignment.center,
                                                    padding: EdgeInsets.zero,
                                                    iconSize: 18,
                                                    icon: Icon(
                                                      Icons.cancel,
                                                      color: DefaultConfig().configs.GREY_COLOR,
                                                    ),
                                                    onPressed: () {
                                                      backReasonController.value = TextEditingValue(text: '');
                                                      backReason = '';
                                                      state(() {});
                                                    },
                                                  ),
                                                )
                                              : Text('')),
                                      onChanged: (String value) {
                                        state(() {
                                          backReason = value;
                                        });
                                      },
                                      controller: backReasonController,
                                      textInputAction: TextInputAction.done,
                                    ),
                                  ),
                                )
                              : Container(),
                          Offstage(
                            offstage: showInput_,
                            child: Padding(
                              padding: EdgeInsets.only(top: 10),
                            ),
                          ),
                          Row(
                            children: <Widget>[
                              showCancel
                                  ? Expanded(
                                      child: InkWell(
                                        onTap: () {
                                          Navigator.of(context).pop(null);
                                        },
                                        child: Container(
                                          height: 50.0,
                                          alignment: Alignment.center,
                                          decoration: BoxDecoration(
                                            border: Border(
                                              top: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                              right: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                            ),
                                          ),
                                          child: Text(
                                            cancelText,
                                            style:
                                                TextStyle(color: cancelColor ?? Colors.grey.shade400, fontSize: 14.0),
                                          ),
                                        ),
                                      ),
                                    )
                                  : Container(),
                              showSuccess
                                  ? Expanded(
                                      child: InkWell(
                                        onTap: () {
                                          if (showInput_) {
                                            if (selectedVal != '柜门未开或故障' && selectedVal != '格口有包裹未取') {
                                              Navigator.of(context).pop(selectedVal);
                                            } else {
                                              var validator = _formKey.currentState?.validate();
                                              if (validator!) {
                                                Navigator.of(context).pop(selectedVal + ',' + backReason);
                                              }
                                            }
                                          } else {
                                            Navigator.of(context).pop(selectedVal);
                                          }
                                        },
                                        child: Container(
                                          height: 50.0,
                                          alignment: Alignment.center,
                                          decoration: BoxDecoration(
                                            border: Border(
                                              top: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                            ),
                                          ),
                                          child: Text(
                                            successText,
                                            style: TextStyle(
                                                color: successColor ?? Theme.of(context).primaryColor, fontSize: 14.0),
                                          ),
                                        ),
                                      ),
                                    )
                                  : Container(),
                            ],
                          )
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ),
          );
        });
      },
    );
  }

  static Future<Null> inputWaybillNo(BuildContext context, String? waybillNo, onPressed(sheetNo),
      {String title = '单号录入',
      String label = '单号',
      String cancelText = '取消',
      String successText = '确定',
      double labelWidth = 50.0}) {
    String inputSheetNo = waybillNo ?? '';
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return Material(
          type: MaterialType.transparency,
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Container(
                  width: 300,
                  decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(10.0))),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Row(
                        children: <Widget>[
                          Expanded(
                            child: Container(
                              height: 50.0,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                  color: Theme.of(context).primaryColor,
//                              border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0)), // borderRadius存在时，border四个边都要存在
                                  borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(10.0), topRight: Radius.circular(10.0))),
                              child: Text(title, style: TextStyle(color: Colors.white, fontSize: 18.0)),
                            ),
                          )
                        ],
                      ),
                      Padding(padding: EdgeInsets.only(top: 20.0)),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: <Widget>[
                          Padding(padding: EdgeInsets.only(left: 10.0)),
                          Container(
                            width: labelWidth,
                            height: 40.0,
                            alignment: Alignment.centerLeft,
                            child: Text(
                              '$label:',
                              style: TextStyle(color: Colors.black87, fontSize: 16.0),
                            ),
                          ),
                          Expanded(
                            child: Container(
                              height: 40.0,
                              decoration: BoxDecoration(
                                  border: Border.all(width: 1.0, color: Color(0xFFEEEEEE)),
                                  borderRadius: BorderRadius.circular(2.0),
                                  color: Colors.grey.shade100),
                              child: TextField(
                                controller: TextEditingController.fromValue(
                                    TextEditingValue(text: '${inputSheetNo == '' ? '' : inputSheetNo}')),
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: '请输入$label',
                                  hintStyle: TextStyle(color: Colors.grey, fontSize: 16.0),
                                  contentPadding: EdgeInsets.fromLTRB(5.0, 5.0, 5.0, 13.0),
                                  counterText: "",
                                ),
                                keyboardType: TextInputType.emailAddress,
                                onChanged: (String value) {
                                  inputSheetNo = value;
                                },
                              ),
                            ),
                          ),
                          Padding(padding: EdgeInsets.only(left: 10.0)),
                        ],
                      ),
                      Padding(padding: EdgeInsets.only(top: 20.0)),
                      Row(
                        children: <Widget>[
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                Navigator.of(context).pop();
                                onPressed('');
                              },
                              child: Container(
                                height: 50.0,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  border: Border(
                                    top: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                    right: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                  ),
                                ),
                                child: Text(
                                  cancelText,
                                  style: TextStyle(color: Colors.grey.shade400, fontSize: 14.0),
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                Navigator.of(context).pop();
                                onPressed(inputSheetNo);
                              },
                              child: Container(
                                height: 50.0,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  border: Border(
                                    top: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                  ),
                                ),
                                child: Text(
                                  successText,
                                  style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 14.0),
                                ),
                              ),
                            ),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
                Padding(padding: EdgeInsets.only(bottom: 60.0))
              ],
            ),
          ),
        );
      },
    );
  }

  static Future<String?> inputNumber(BuildContext context, String? waybillNo,
      {String title = '单号录入',
      String label = '单号',
      String cancelText = '取消',
      String successText = '确定',
      double labelWidth = 50.0}) {
    String inputSheetNo = waybillNo ?? '';
    bool validator = false;
    GlobalKey<FormState> formKey = GlobalKey<FormState>();
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return Material(
          type: MaterialType.transparency,
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Container(
                  width: 300,
                  decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(10.0))),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Row(
                        children: <Widget>[
                          Expanded(
                            child: Container(
                              height: 50.0,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                  color: Theme.of(context).primaryColor,
//                              border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0)), // borderRadius存在时，border四个边都要存在
                                  borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(10.0), topRight: Radius.circular(10.0))),
                              child: Text(title, style: TextStyle(color: Colors.white, fontSize: 18.0)),
                            ),
                          )
                        ],
                      ),
                      Padding(padding: EdgeInsets.only(top: 20.0)),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: <Widget>[
                          Padding(padding: EdgeInsets.only(left: 10.0)),
                          Container(
                            width: labelWidth,
                            height: 70.0,
                            alignment: Alignment.centerLeft,
                            padding: EdgeInsets.only(bottom: 20),
                            child: Text(
                              '$label:',
                              style: TextStyle(color: Colors.black87, fontSize: 16.0),
                            ),
                          ),
                          Expanded(
                            child: Form(
                              key: formKey,
                              child: Container(
                                height: 70.0,
                                child: TextFormField(
                                  validator: (value) {
                                    return FormValidateUtil.isNotZeroNumber(value);
                                  },
                                  controller: TextEditingController.fromValue(
                                      TextEditingValue(text: '${inputSheetNo == '' ? '' : inputSheetNo}')),
                                  decoration: InputDecoration(
                                    errorStyle: TextStyle(fontSize: 12, height: 1),
                                    errorMaxLines: 2,
                                    border: InputBorder.none,
                                    filled: true,
                                    fillColor: Color(0xFFF6F6F8),
                                    hintText: '请输入$label',
                                    hintStyle: TextStyle(color: Colors.grey, fontSize: 16.0),
                                    contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 10),
                                    counterText: "",
                                  ),
                                  keyboardType: TextInputType.number,
                                  onChanged: (String value) {
                                    inputSheetNo = value;
                                  },
                                  onEditingComplete: () {
                                    validator = formKey.currentState?.validate() ?? false;
                                    if (validator) {
                                      FocusScope.of(context).requestFocus(FocusNode());
                                    }
                                  },
                                ),
                              ),
                            ),
                          ),
                          Padding(padding: EdgeInsets.only(left: 10.0)),
                        ],
                      ),
                      Padding(padding: EdgeInsets.only(top: 20.0)),
                      Row(
                        children: <Widget>[
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                Navigator.of(context).pop(null);
                              },
                              child: Container(
                                height: 50.0,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  border: Border(
                                    top: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                    right: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                  ),
                                ),
                                child: Text(
                                  cancelText,
                                  style: TextStyle(color: Colors.grey.shade400, fontSize: 14.0),
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                var validator = formKey.currentState?.validate();
                                if (validator!) {
                                  Navigator.of(context).pop(inputSheetNo);
                                }
                              },
                              child: Container(
                                height: 50.0,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  border: Border(
                                    top: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                  ),
                                ),
                                child: Text(
                                  successText,
                                  style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 14.0),
                                ),
                              ),
                            ),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
                Padding(padding: EdgeInsets.only(bottom: 60.0))
              ],
            ),
          ),
        );
      },
    );
  }

  static Future<Null> inputRemarks(BuildContext context, onPressed(remarks),
      {String title = '编辑备注', String cancelText = '取消', String successText = '确定', String remarks = ''}) {
    String inputRmarks = remarks;
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return Material(
          type: MaterialType.transparency,
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Container(
                  width: 250,
                  decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(10.0))),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Row(
                        children: <Widget>[
                          Expanded(
                            child: Container(
                              height: 50.0,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                  color: Theme.of(context).primaryColor,
//                              border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0)), // borderRadius存在时，border四个边都要存在
                                  borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(10.0), topRight: Radius.circular(10.0))),
                              child: Text(title, style: TextStyle(color: Colors.white, fontSize: 18.0)),
                            ),
                          )
                        ],
                      ),
                      Padding(padding: EdgeInsets.only(top: 15.0)),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: <Widget>[
                          Padding(padding: EdgeInsets.only(left: 15.0)),
                          Expanded(
                            child: Container(
                              decoration:
                                  BoxDecoration(borderRadius: BorderRadius.circular(5.0), color: Colors.grey.shade200),
                              child: TextField(
                                maxLength: 20,
                                maxLines: 3,
                                maxLengthEnforcement: MaxLengthEnforcement.enforced,
                                inputFormatters: [LengthLimitingTextInputFormatter(20)],
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: '备注信息 不超过20字',
                                  hintStyle: TextStyle(color: Colors.black54, fontSize: 14.0),
                                  contentPadding: EdgeInsets.fromLTRB(10.0, 5.0, 5.0, 13.0),
                                  counterText: "",
                                ),
                                onChanged: (String value) {
                                  inputRmarks = value;
                                },
                                controller: TextEditingController.fromValue(
                                    TextEditingValue(text: '${inputRmarks == '' ? '' : inputRmarks}')),
                              ),
                            ),
                          ),
                          Padding(padding: EdgeInsets.only(left: 15.0)),
                        ],
                      ),
                      Padding(padding: EdgeInsets.only(top: 15.0)),
                      Row(
                        children: <Widget>[
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                Navigator.of(context).pop();
                              },
                              child: Container(
                                height: 50.0,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  border: Border(
                                    top: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                    right: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                  ),
                                ),
                                child: Text(
                                  cancelText,
                                  style: TextStyle(color: Colors.grey.shade400, fontSize: 14.0),
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                onPressed(inputRmarks);
                                Navigator.of(context).pop();
                              },
                              child: Container(
                                height: 50.0,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  border: Border(
                                    top: BorderSide(color: Color(0xFFEEEEEE), width: 1.0),
                                  ),
                                ),
                                child: Text(
                                  successText,
                                  style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 14.0),
                                ),
                              ),
                            ),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
                Padding(padding: EdgeInsets.only(bottom: 60.0))
              ],
            ),
          ),
        );
      },
    );
  }

  static Future<Null> showMobileConfirmModal(
      BuildContext context, String? ocrImg, String mobile, String? name, onPressed(item)) {
    String mobileValue = '';
    return showModalBottomSheet(
        context: context,
        enableDrag: false,
        isDismissible: false,
        isScrollControlled: false,
        backgroundColor: Color(0x4c000000),
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
          topLeft: Radius.circular(10.0),
          topRight: Radius.circular(10.0),
        )),
        builder: (BuildContext _context) {
          return SingleChildScrollView(
            child: StatefulBuilder(
              builder: (_context, state) {
                return Column(
                  children: <Widget>[
                    Container(
                      padding: EdgeInsets.all(10),
                      width: MediaQuery.of(context).size.width,
                      height: 365,
                      child: Container(
                        color: Colors.transparent,
                        child: Column(children: [
                          Container(
                            width: MediaQuery.of(context).size.width,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Text('手机号识别结果',
                                    style: TextStyle(fontSize: 16, color: Colors.white, fontWeight: FontWeight.w500)),
                                Padding(padding: EdgeInsets.only(top: 5)),
                                Text('该客户为新用户,请核对运单和识别结果是否一致',
                                    style: TextStyle(fontSize: 12, color: Colors.white, fontWeight: FontWeight.w500)),
                              ],
                            ),
                          ),
                          Padding(padding: EdgeInsets.only(top: 10)),
                          Offstage(
                            offstage: ocrImg == '' || ocrImg == null,
                            child: Image.file(
                              File(ocrImg!),
                              filterQuality: FilterQuality.high,
                              fit: BoxFit.fitWidth,
                              cacheHeight: 60,
                              height: 60,
                            ),
                          ),
                          Padding(padding: EdgeInsets.only(top: 20)),
                          Expanded(
                            child: Column(
                              children: [
                                InkWell(
                                  onTap: () {
                                    state(() {
                                      mobileValue = mobile;
                                    });
                                    onPressed(mobileValue);
                                  },
                                  child: Container(
                                    width: MediaQuery.of(context).size.width - 40,
                                    color: Colors.white,
                                    padding: EdgeInsets.fromLTRB(20, 10, 20, 10),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          mobile,
                                          style:
                                              TextStyle(fontSize: 24, color: Colors.red, fontWeight: FontWeight.w600),
                                        ),
                                        Text(
                                          name!,
                                          style:
                                              TextStyle(fontSize: 14, color: Colors.red, fontWeight: FontWeight.w600),
                                        ),
                                        Text(
                                          '选择',
                                          style: TextStyle(
                                              fontSize: 14,
                                              color: Theme.of(context).primaryColor,
                                              fontWeight: FontWeight.normal),
                                        )
                                      ],
                                    ),
                                  ),
                                )
                              ],
                            ),
                          ),
                          Padding(padding: EdgeInsets.only(top: 20)),
                          OutlinedButton(
                            child: Text("手动输入", style: TextStyle(fontSize: 22, color: Theme.of(context).primaryColor)),
                            onPressed: () {
                              onPressed('');
                            },
                            style: ButtonStyle(
                                padding: MaterialStateProperty.all(EdgeInsets.fromLTRB(20, 10, 20, 10)),
                                backgroundColor: MaterialStateProperty.resolveWith((states) {
                                  return DefaultConfig().configs.WHITE_COLOR;
                                }),
                                minimumSize: MaterialStateProperty.all(Size(50, 32)),
                                shape: MaterialStateProperty.all(StadiumBorder()),
                                side: MaterialStateProperty.all(BorderSide(color: Theme.of(context).primaryColor))),
                          ),
                          Padding(padding: EdgeInsets.only(top: 20))
                        ]),
                      ),
                    ),
                  ],
                );
              },
            ),
          );
        });
  }

  /// 清除本地数据
  static clearCache(context, {bool isCheck = false}) async {
//    String todayYmd = new DateFormat("yyyy-MM-dd").format(DateTime.now());
    if (isCheck) {
      await CommonUtils.isSaveDialog(context, (isClear) async {
        if (isClear) {
          int dateSitamp = new DateTime.now().millisecondsSinceEpoch;
          await LocalStorage.save(DefaultConfig().configs.LAST_CLEAR_CACHE_TIME, dateSitamp);
//          ScanDao.clearScanBeforeTodayY(todayYmd);
//          ScanDao.clearScanByStatus('Y');
          await PhotoDao.deleteFileWithoutOpt();
          // scanPlugin.clearCache();
        }
      },
          content: '清除缓存后不可恢复, 确认清除?',
          cancelText: '取消',
          successText: '确认',
          bgColor: Colors.white,
          titleColor: Colors.black);
    } else {
      int? lastClearCacheTime = await LocalStorage.get(DefaultConfig().configs.LAST_CLEAR_CACHE_TIME);
      int nowSitamp = new DateTime.now().millisecondsSinceEpoch;
      if (lastClearCacheTime != null) {
        if (nowSitamp - lastClearCacheTime < 24 * 60 * 60 * 1000) {
          return false;
        }
      }
      await LocalStorage.save(DefaultConfig().configs.LAST_CLEAR_CACHE_TIME, nowSitamp);
//      ScanDao.clearScanBeforeTodayY(todayYmd);
//       scanPlugin.clearCache();
      PhotoDao.deleteFileWithoutOpt();
    }
  }

  static selectCustomer(BuildContext context, List<CustomerEntity> customer, onPressed(item)) async {
    List<Widget> list = <Widget>[];

    customer.forEach(
      (v) {
        list.add(
          Divider(
            height: 1.0,
          ),
        );
        list.add(
          Container(
            color: Colors.white,
            child: ListTile(
              title: Row(
                children: <Widget>[
                  Text(
                    '${(v.name == '') ? '老客户' : v.name}',
                    style: TextStyle(color: Colors.black87),
                  ),
                  Text(
                    '-',
                    style: TextStyle(color: Colors.black38),
                  ),
                  Text(
                    '${v.mobile}',
                    style: TextStyle(color: Colors.black87),
                  )
                ],
              ),
              onTap: () {
                onPressed(v);
                Navigator.of(context).pop();
              },
            ),
          ),
        );
      },
    );

    return showModalBottomSheet(
      isDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return Container(
          color: Colors.grey.shade200,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Column(
                  children: list,
                ),
                InkWell(
                  onTap: () {
                    Navigator.of(context).pop();
                  },
                  child: Container(
                    margin: EdgeInsets.only(top: 5.0),
                    height: 50.0,
                    padding: EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
                    decoration: BoxDecoration(
                        color: Colors.white, border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0))),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        Text(
                          '取消',
                          style: TextStyle(fontSize: 18.0, color: Colors.grey.shade500),
                        )
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }

  static selectHbRule(BuildContext context) async {
    return showModalBottomSheet(
      isDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return Container(
          color: Colors.grey.shade200,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Column(
                children: <Widget>[
                  Material(
                    color: Colors.white,
                    child: InkWell(
                      onTap: () async {
                        await LocalStorage.save('hbInfo', {"check": true, "isHb": true});
                        Navigator.of(context).pop();
                      },
                      child: Container(
                        height: 55.0,
                        alignment: Alignment.center,
                        decoration:
                            BoxDecoration(border: Border(bottom: BorderSide(width: 1.0, color: Colors.grey.shade100))),
                        child: Text(
                          '自动合并取件码',
                          style: TextStyle(color: Colors.black87, fontSize: 16.0),
                        ),
                      ),
                    ),
                  ),
                  Material(
                    color: Colors.white,
                    child: InkWell(
                      onTap: () async {
                        await LocalStorage.save('hbInfo', {"check": true, "isHb": false});
                        Navigator.of(context).pop();
                      },
                      child: Container(
                        height: 55.0,
                        alignment: Alignment.center,
                        decoration:
                            BoxDecoration(border: Border(bottom: BorderSide(width: 1.0, color: Colors.grey.shade100))),
                        child: Text(
                          '不合并取件码',
                          style: TextStyle(color: Colors.black87, fontSize: 16.0),
                        ),
                      ),
                    ),
                  ),
                  Material(
                    color: Colors.white,
                    child: InkWell(
                      onTap: () async {
                        await LocalStorage.save('hbInfo', {"check": false, "isHb": ''});
                        Navigator.of(context).pop();
                      },
                      child: Container(
                        height: 55.0,
                        alignment: Alignment.center,
                        decoration:
                            BoxDecoration(border: Border(bottom: BorderSide(width: 1.0, color: Colors.grey.shade100))),
                        child: Text(
                          '提示让我选择',
                          style: TextStyle(color: Colors.black87, fontSize: 16.0),
                        ),
                      ),
                    ),
                  )
                ],
              ),
              InkWell(
                onTap: () {
                  Navigator.of(context).pop();
                },
                child: Container(
                  margin: EdgeInsets.only(top: 5.0),
                  height: 50.0,
                  padding: EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
                  decoration: BoxDecoration(
                      color: Colors.white, border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0))),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      Text(
                        '取消',
                        style: TextStyle(fontSize: 18.0, color: Colors.grey.shade500),
                      )
                    ],
                  ),
                ),
              )
            ],
          ),
        );
      },
    );
  }

  static Future<String?> selectSavePhoto(BuildContext context, {showShare = true}) async {
    return showModalBottomSheet(
      isDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return Container(
          color: Colors.grey.shade200,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Column(
                children: <Widget>[
                  Material(
                    color: Colors.white,
                    child: InkWell(
                      onTap: () async {
                        Navigator.of(context).pop('save');
                      },
                      child: Container(
                        height: 55.0,
                        alignment: Alignment.center,
                        decoration:
                            BoxDecoration(border: Border(bottom: BorderSide(width: 1.0, color: Colors.grey.shade100))),
                        child: Text(
                          '保存图片',
                          style: TextStyle(color: Colors.black87, fontSize: 16.0),
                        ),
                      ),
                    ),
                  ),
                  Offstage(
                    offstage: !showShare,
                    child: Material(
                      color: Colors.white,
                      child: InkWell(
                        onTap: () async {
                          Navigator.of(context).pop('share');
                        },
                        child: Container(
                          height: 55.0,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                              border: Border(bottom: BorderSide(width: 1.0, color: Colors.grey.shade100))),
                          child: Text(
                            '分享链接',
                            style: TextStyle(color: Colors.black87, fontSize: 16.0),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              InkWell(
                onTap: () {
                  Navigator.of(context).pop();
                },
                child: Container(
                  margin: EdgeInsets.only(top: 5.0),
                  height: 50.0,
                  padding: EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
                  decoration: BoxDecoration(
                      color: Colors.white, border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0))),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      Text(
                        '取消',
                        style: TextStyle(fontSize: 18.0, color: Colors.grey.shade500),
                      )
                    ],
                  ),
                ),
              )
            ],
          ),
        );
      },
    );
  }

  static inputCommonDialog(BuildContext context, onPressed(value), String label,
      {String? hintText, TextInputType? keyboardType, String initValue = ''}) {
    TextEditingController valueController = TextEditingController();
    GlobalKey<FormState> _formKey = new GlobalKey<FormState>();
    String value = '';
    if (CheckUtils.isNotNull(initValue)) {
      value = initValue;
      valueController.value = TextEditingValue(text: initValue);
    }
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
          return Material(
            type: MaterialType.transparency,
            child: Scaffold(
              backgroundColor: Colors.transparent,
              body: Center(
                child: SingleChildScrollView(
                  physics: AlwaysScrollableScrollPhysics(),
                  child: Container(
                    width: MediaQuery.of(context).size.width - 80,
                    padding: EdgeInsets.fromLTRB(20, 20, 20, 10),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.all(Radius.circular(4.0)),
                    ),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: <Widget>[
                          Container(
                            width: MediaQuery.of(context).size.width - 120,
                            child: Text(
                              label,
                              style: TextStyle(fontSize: 18, color: Colors.black),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 10),
                          ),
                          Container(
                            margin: EdgeInsets.fromLTRB(0, 10, 0, 10),
                            padding: EdgeInsets.fromLTRB(0, 10, 0, 10),
                            child: TextFormField(
                              autofocus: true,
                              keyboardType: keyboardType ?? TextInputType.name,
                              decoration: InputDecoration(
                                  hintText: '$hintText',
                                  labelText: label,
                                  focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(7),
                                      borderSide: BorderSide(color: Colors.red)),
                                  enabledBorder: InputBorder.none,
                                  errorBorder: InputBorder.none,
                                  disabledBorder: InputBorder.none,
                                  errorMaxLines: 1,
                                  border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(7),
                                      borderSide: BorderSide(color: Colors.red)),
                                  contentPadding: EdgeInsets.fromLTRB(5, 2, 5, 2),
                                  suffixIcon: value.length > 0
                                      ? Container(
                                          width: 20,
                                          height: 20,
                                          child: IconButton(
                                            alignment: Alignment.center,
                                            padding: EdgeInsets.zero,
                                            iconSize: 18,
                                            icon: Icon(
                                              Icons.cancel,
                                              color: DefaultConfig().configs.GREY_COLOR,
                                            ),
                                            onPressed: () {
                                              valueController.value = TextEditingValue(text: '');
                                              value = '';
                                              setState(() {});
                                            },
                                          ),
                                        )
                                      : Text('')),
                              onChanged: (String _value) {
                                setState(() {
                                  value = _value;
                                });
                              },
                              controller: valueController,
                              textInputAction: TextInputAction.done,
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 10),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: <Widget>[
                              Material(
                                color: Colors.white,
                                child: InkWell(
                                  onTap: () {
                                    Navigator.pop(context);
                                  },
                                  child: Padding(
                                    padding: EdgeInsets.fromLTRB(15, 5, 15, 5),
                                    child: Text(
                                      '取消',
                                      style: TextStyle(
                                        fontSize: 16.0,
                                        color: Colors.grey.shade500,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Material(
                                color: Colors.white,
                                child: InkWell(
                                  onTap: () {
                                    onPressed(value);
                                    Navigator.pop(context);
                                  },
                                  child: Padding(
                                    padding: EdgeInsets.fromLTRB(15, 5, 0, 5),
                                    child: Text(
                                      '确定',
                                      style: TextStyle(fontSize: 16.0, color: Theme.of(context).primaryColor),
                                    ),
                                  ),
                                ),
                              )
                            ],
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        });
      },
    );
  }

  /// 修改手机号弹窗
  static inputMobileDialog(BuildContext context, bool isShowMobileSend, onPressed(mobile, bool isSendMessage),
      {String initMobile = ''}) {
    TextEditingController mobileController = TextEditingController();
    GlobalKey<FormState> _formKey = new GlobalKey<FormState>();
    String mobile = '';
    if (CheckUtils.isNotNull(initMobile)) {
      mobile = initMobile;
      mobileController.value = TextEditingValue(text: initMobile);
    }
    bool isSendMessage = true;
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
          return Material(
            type: MaterialType.transparency,
            child: Scaffold(
              backgroundColor: Colors.transparent,
              body: Center(
                child: SingleChildScrollView(
                  physics: AlwaysScrollableScrollPhysics(),
                  child: Container(
                    width: MediaQuery.of(context).size.width - 80,
                    padding: EdgeInsets.fromLTRB(20, 20, 20, 10),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.all(Radius.circular(4.0)),
                    ),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: <Widget>[
                          Container(
                            width: MediaQuery.of(context).size.width - 120,
                            child: Text(
                              '修改手机号',
                              style: TextStyle(fontSize: 18, color: Colors.black),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 10),
                          ),
                          Container(
                            margin: EdgeInsets.fromLTRB(0, 10, 0, 0),
                            padding: EdgeInsets.fromLTRB(0, 10, 0, 10),
                            child: TextFormField(
                              autofocus: true,
                              validator: (value) => FormValidateUtil.phoneValidate(value!, context),
                              keyboardType: TextInputType.phone,
                              decoration: InputDecoration(
                                  hintText: '请输入手机号',
                                  labelText: '手机号',
                                  focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(7),
                                      borderSide: BorderSide(color: Colors.red)),
                                  enabledBorder: InputBorder.none,
                                  errorBorder: InputBorder.none,
                                  disabledBorder: InputBorder.none,
                                  errorMaxLines: 1,
                                  border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(7),
                                      borderSide: BorderSide(color: Colors.red)),
                                  contentPadding: EdgeInsets.fromLTRB(5, 2, 5, 2),
                                  suffixIcon: mobile.length > 0
                                      ? Container(
                                          width: 20,
                                          height: 20,
                                          child: IconButton(
                                            alignment: Alignment.center,
                                            padding: EdgeInsets.zero,
                                            iconSize: 18,
                                            icon: Icon(
                                              Icons.cancel,
                                              color: DefaultConfig().configs.GREY_COLOR,
                                            ),
                                            onPressed: () {
                                              mobileController.value = TextEditingValue(text: '');
                                              mobile = '';
                                              setState(() {});
                                            },
                                          ),
                                        )
                                      : Text('')),
                              onChanged: (String value) {
                                if (value.length == 11) {
                                  _formKey.currentState?.validate();
                                }
                                setState(() {
                                  mobile = value;
                                });
                              },
                              controller: mobileController,
                              textInputAction: TextInputAction.done,
                            ),
                          ),
                          Offstage(
                            offstage: !isShowMobileSend,
                            child: InkWell(
                              onTap: () {
                                setState(() {
                                  isSendMessage = !isSendMessage;
                                });
                              },
                              child: Container(
                                alignment: Alignment.centerLeft,
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Container(
                                      height: 30.0,
                                      padding: EdgeInsets.only(right: 6),
                                      child: Icon(
                                        isSendMessage
                                            ? Icons.radio_button_checked_outlined
                                            : Icons.radio_button_unchecked_outlined,
                                        color: isSendMessage ? Theme.of(context).primaryColor : Colors.grey,
                                        size: 20,
                                      ),
                                    ),
                                    Expanded(
                                        child: Text(
                                      '修改成功后对新手机号重发取件信息',
                                      style: TextStyle(fontSize: 13),
                                    ))
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 10),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: <Widget>[
                              Material(
                                color: Colors.white,
                                child: InkWell(
                                  onTap: () {
                                    Navigator.pop(context);
                                  },
                                  child: Padding(
                                    padding: EdgeInsets.fromLTRB(15, 5, 15, 5),
                                    child: Text(
                                      '取消',
                                      style: TextStyle(
                                        fontSize: 16.0,
                                        color: Colors.grey.shade500,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Material(
                                color: Colors.white,
                                child: InkWell(
                                  onTap: () {
                                    var validator = _formKey.currentState?.validate();
                                    if (validator!) {
                                      onPressed(mobile, isSendMessage);
                                      Navigator.pop(context);
                                    }
                                  },
                                  child: Padding(
                                    padding: EdgeInsets.fromLTRB(15, 5, 0, 5),
                                    child: Text(
                                      '确定',
                                      style: TextStyle(fontSize: 16.0, color: Theme.of(context).primaryColor),
                                    ),
                                  ),
                                ),
                              )
                            ],
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        });
      },
    );
  }

  /// 修改快递公司弹窗
  static changeCompanyDialog(BuildContext context, brandMap, bool isShowSync, onPressed(brandCode, bool isSync),
      {String initBrandCode = ''}) {
    GlobalKey<FormState> _formKey = new GlobalKey<FormState>();
    String brandCode = '';
    if (CheckUtils.isNotNull(initBrandCode)) {
      brandCode = initBrandCode;
    }
    bool isSync = false;
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
          return Material(
            type: MaterialType.transparency,
            child: Scaffold(
              backgroundColor: Colors.transparent,
              body: Center(
                child: SingleChildScrollView(
                  physics: AlwaysScrollableScrollPhysics(),
                  child: Container(
                    width: MediaQuery.of(context).size.width - 80,
                    padding: EdgeInsets.fromLTRB(20, 20, 20, 10),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.all(Radius.circular(4.0)),
                    ),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: <Widget>[
                          Container(
                            width: MediaQuery.of(context).size.width - 120,
                            child: Text(
                              '修改快递公司',
                              style: TextStyle(fontSize: 18, color: Colors.black),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 10),
                          ),
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.all(
                                Radius.circular(7),
                              ),
                              border: Border.all(width: 1, color: DefaultConfig().configs.GREY_COLOR),
                            ),
                            margin: EdgeInsets.fromLTRB(0, 10, 0, 0),
                            padding: EdgeInsets.fromLTRB(0, 10, 0, 10),
                            child: Row(
                              children: [
                                InkWell(
                                  onTap: () {
                                    SoundUtils.audioPushFn(SoundUtils.CHOOSE_COMPANY);
                                    CommonUtils.showBottomBrandSelectModal(context, brandMap, (item) {
                                      SoundUtils.audioPushFn(SoundUtils.BRAND_SOUND[item]!);
                                      if (item != null) {
                                        setState(() {
                                          brandCode = item;
                                        });
                                      }
                                    }, bindCompany: brandCode);
                                  },
                                  child: Container(
                                      child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    children: [
                                      Container(
                                        width: 30,
                                        height: 30,
                                        child: CircleAvatar(
                                          radius: 10.0,
                                          backgroundColor: Colors.white,
                                          backgroundImage: AssetImage(CommonUtils.getExpressLogo(brandCode)),
                                        ),
                                        margin: EdgeInsets.only(left: 10, right: 5),
                                      ),
                                      Padding(
                                        padding: EdgeInsets.only(left: 10),
                                      ),
                                      Container(
                                          constraints: BoxConstraints(minWidth: 150),
                                          child: Text(
                                            DefaultConfig().configs.EXPRESS2[brandCode] ?? '',
                                            style: TextStyle(fontSize: 16, color: Colors.black),
                                          )),
                                      Container(
                                        width: 60,
                                        child: Icon(
                                          Icons.keyboard_arrow_down,
                                          size: 32,
                                          color: Colors.black,
                                        ),
                                      )
                                    ],
                                  )),
                                ),
                              ],
                            ),
                          ),
                          Offstage(
                            offstage: !isShowSync,
                            child: InkWell(
                              onTap: () {
                                setState(() {
                                  isSync = !isSync;
                                });
                              },
                              child: Container(
                                alignment: Alignment.centerLeft,
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Container(
                                      height: 30.0,
                                      padding: EdgeInsets.only(right: 6),
                                      child: Icon(
                                        isSync
                                            ? Icons.radio_button_checked_outlined
                                            : Icons.radio_button_unchecked_outlined,
                                        color: isSync ? Theme.of(context).primaryColor : Colors.grey,
                                        size: 20,
                                      ),
                                    ),
                                    Expanded(
                                        child: Text(
                                      '修改成功后重投物流',
                                      style: TextStyle(fontSize: 13),
                                    ))
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 10),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: <Widget>[
                              Material(
                                color: Colors.white,
                                child: InkWell(
                                  onTap: () {
                                    Navigator.pop(context);
                                  },
                                  child: Padding(
                                    padding: EdgeInsets.fromLTRB(15, 5, 15, 5),
                                    child: Text(
                                      '取消',
                                      style: TextStyle(
                                        fontSize: 16.0,
                                        color: Colors.grey.shade500,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Material(
                                color: Colors.white,
                                child: InkWell(
                                  onTap: () {
                                    var validator = _formKey.currentState?.validate();
                                    if (validator!) {
                                      onPressed(brandCode, isSync);
                                      Navigator.pop(context);
                                    }
                                  },
                                  child: Padding(
                                    padding: EdgeInsets.fromLTRB(15, 5, 0, 5),
                                    child: Text(
                                      '确定',
                                      style: TextStyle(fontSize: 16.0, color: Theme.of(context).primaryColor),
                                    ),
                                  ),
                                ),
                              )
                            ],
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        });
      },
    );
  }

  /// 修改快递单号弹窗
  static changeWayBillNoDialog(BuildContext context, onPressed(waybillNo, bool isSync), {String initWayBillNo = ''}) {
    GlobalKey<FormState> _formKey = new GlobalKey<FormState>();
    String waybillNo = '';
    if (CheckUtils.isNotNull(initWayBillNo)) {
      waybillNo = initWayBillNo;
    }
    bool isSync = false;
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
          return Material(
            type: MaterialType.transparency,
            child: Scaffold(
              backgroundColor: Colors.transparent,
              body: Center(
                child: SingleChildScrollView(
                  physics: AlwaysScrollableScrollPhysics(),
                  child: Container(
                    width: MediaQuery.of(context).size.width - 80,
                    padding: EdgeInsets.fromLTRB(20, 20, 20, 10),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.all(Radius.circular(4.0)),
                    ),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: <Widget>[
                          Container(
                            width: MediaQuery.of(context).size.width - 120,
                            child: Text(
                              '修改快递公司',
                              style: TextStyle(fontSize: 18, color: Colors.black),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 10),
                          ),
                          TextField(
                            controller: TextEditingController.fromValue(
                                TextEditingValue(text: '${waybillNo == '' ? '' : waybillNo}')),
                            decoration: InputDecoration(
                              border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(7),
                                  borderSide: BorderSide(color: DefaultConfig().configs.GREY_COLOR)),
                              hintText: '请输入单号',
                              hintStyle: TextStyle(color: Colors.grey, fontSize: 16.0),
                              contentPadding: EdgeInsets.fromLTRB(15.0, 5.0, 5.0, 5.0),
                              counterText: "",
                            ),
                            keyboardType: TextInputType.emailAddress,
                            onChanged: (String value) {
                              waybillNo = value;
                            },
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 10),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: <Widget>[
                              Material(
                                color: Colors.white,
                                child: InkWell(
                                  onTap: () {
                                    Navigator.pop(context);
                                  },
                                  child: Padding(
                                    padding: EdgeInsets.fromLTRB(15, 5, 15, 5),
                                    child: Text(
                                      '取消',
                                      style: TextStyle(
                                        fontSize: 16.0,
                                        color: Colors.grey.shade500,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Material(
                                color: Colors.white,
                                child: InkWell(
                                  onTap: () {
                                    var validator = _formKey.currentState?.validate();
                                    if (validator!) {
                                      onPressed(waybillNo, isSync);
                                      Navigator.pop(context);
                                    }
                                  },
                                  child: Padding(
                                    padding: EdgeInsets.fromLTRB(15, 5, 0, 5),
                                    child: Text(
                                      '确定',
                                      style: TextStyle(fontSize: 16.0, color: Theme.of(context).primaryColor),
                                    ),
                                  ),
                                ),
                              )
                            ],
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        });
      },
    );
  }

  /// 填写门店编码
  static shopCodeDialog(BuildContext context, bool isShowMobileSend, onPressed(shopCode, bool isSendMessage)) {
    TextEditingController shopCodeController = TextEditingController();
    GlobalKey<FormState> _formKey = new GlobalKey<FormState>();
    String shopCode = '';
    bool isSendMessage = true;
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
          return Material(
            type: MaterialType.transparency,
            child: Scaffold(
              backgroundColor: Colors.transparent,
              body: Center(
                child: SingleChildScrollView(
                  physics: AlwaysScrollableScrollPhysics(),
                  child: Container(
                    width: MediaQuery.of(context).size.width - 80,
                    padding: EdgeInsets.fromLTRB(20, 20, 20, 10),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.all(Radius.circular(4.0)),
                    ),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: <Widget>[
                          Container(
                            width: MediaQuery.of(context).size.width - 120,
                            child: Text(
                              '门店编码',
                              style: TextStyle(fontSize: 18, color: Colors.black),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 10),
                          ),
                          Container(
                            margin: EdgeInsets.fromLTRB(0, 10, 0, 0),
                            padding: EdgeInsets.fromLTRB(0, 10, 0, 10),
                            child: TextFormField(
                              autofocus: true,
                              validator: (value) => null,
                              keyboardType: TextInputType.text,
                              decoration: InputDecoration(
                                  hintText: '请输入门店编码',
                                  labelText: '门店编码',
                                  focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(7),
                                      borderSide: BorderSide(color: Colors.red)),
                                  enabledBorder: InputBorder.none,
                                  errorBorder: InputBorder.none,
                                  disabledBorder: InputBorder.none,
                                  errorMaxLines: 1,
                                  border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(7),
                                      borderSide: BorderSide(color: Colors.red)),
                                  contentPadding: EdgeInsets.fromLTRB(5, 2, 5, 2),
                                  suffixIcon: shopCode.length > 0
                                      ? Container(
                                          width: 20,
                                          height: 20,
                                          child: IconButton(
                                            alignment: Alignment.center,
                                            padding: EdgeInsets.zero,
                                            iconSize: 18,
                                            icon: Icon(
                                              Icons.cancel,
                                              color: DefaultConfig().configs.GREY_COLOR,
                                            ),
                                            onPressed: () {
                                              shopCodeController.value = TextEditingValue(text: '');
                                              shopCode = '';
                                              setState(() {});
                                            },
                                          ),
                                        )
                                      : Text('')),
                              onChanged: (String value) {
                                if (value.length == 11) {
                                  _formKey.currentState?.validate();
                                }
                                setState(() {
                                  shopCode = value;
                                });
                              },
                              controller: shopCodeController,
                              textInputAction: TextInputAction.done,
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 10),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: <Widget>[
                              Material(
                                color: Colors.white,
                                child: InkWell(
                                  onTap: () {
                                    Navigator.pop(context);
                                  },
                                  child: Padding(
                                    padding: EdgeInsets.fromLTRB(15, 5, 15, 5),
                                    child: Text(
                                      '取消',
                                      style: TextStyle(
                                        fontSize: 16.0,
                                        color: Colors.grey.shade500,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Material(
                                color: Colors.white,
                                child: InkWell(
                                  onTap: () {
                                    var validator = _formKey.currentState?.validate();
                                    if (validator!) {
                                      onPressed(shopCode, isSendMessage);
                                      Navigator.pop(context);
                                    }
                                  },
                                  child: Padding(
                                    padding: EdgeInsets.fromLTRB(15, 5, 0, 5),
                                    child: Text(
                                      '确定',
                                      style: TextStyle(fontSize: 16.0, color: Theme.of(context).primaryColor),
                                    ),
                                  ),
                                ),
                              )
                            ],
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        });
      },
    );
  }

  static showBottomModal(BuildContext context, Map mapData, onPressed(item)) {
    List<Widget> list = <Widget>[];
    mapData.forEach((k, v) {
      list.add(
        InkWell(
          onTap: () {
            onPressed(k);
            Navigator.pop(context);
          },
          child: Container(
            height: 50.0,
            padding: EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
            decoration: BoxDecoration(
                color: Colors.white, border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0))),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                Text(
                  v,
                  style: TextStyle(fontSize: 18.0, color: Colors.black87),
                )
              ],
            ),
          ),
        ),
      );
    });

    return showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
            child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Column(
                mainAxisSize: MainAxisSize.min,
                children: list,
              ),
              InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Container(
                  margin: EdgeInsets.only(top: 10.0),
                  height: 50.0,
                  padding: EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
                  decoration: BoxDecoration(
                      color: Colors.white, border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0))),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      Text(
                        '取消',
                        style: TextStyle(fontSize: 18.0, color: Colors.grey.shade500),
                      )
                    ],
                  ),
                ),
              )
            ],
          ),
        ));
      },
    );
  }

  /// 底部追加入柜格口选择
  static Future<CustomerMobileOrderList?> showBottomBoxSelectModal(
      BuildContext context, List<CustomerMobileOrderList> mapData) {
    List<Widget> list = <Widget>[];
    mapData.forEach((CustomerMobileOrderList item) {
      list.add(
        InkWell(
          onTap: () {
            Navigator.pop(context, item);
          },
          child: Container(
            height: 64.0,
            padding: EdgeInsets.all(10.0),
            decoration: BoxDecoration(
                color: Colors.white, border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0))),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                Text(
                    '${item.cabinetBoxLabel}(${DefaultConfig().configs.CABINET_BOX_TYPE2[item.cabinetBoxType]})',
                    style: TextStyle(fontSize: 24, color: Colors.red, fontWeight: FontWeight.w600))
              ],
            ),
          ),
        ),
      );
    });

    return showModalBottomSheet(
      context: context,
      enableDrag: false,
      isDismissible: false,
      isScrollControlled: false,
      builder: (BuildContext context) {
        return Container(
            child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Container(
                padding: EdgeInsets.all(5.0),
                decoration: BoxDecoration(
                    color: Colors.white, border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0))),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    Text(
                      '该号码已有包裹在柜未取，是否复用格口？',
                      style: TextStyle(fontSize: 14.0, color: Colors.black87),
                    )
                  ],
                ),
              ),
              Column(
                mainAxisSize: MainAxisSize.min,
                children: list,
              ),
              InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Container(
                  margin: EdgeInsets.only(top: 10.0),
                  height: 50.0,
                  padding: EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
                  decoration: BoxDecoration(
                      color: Colors.white, border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0))),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      Text(
                        '取消',
                        style: TextStyle(fontSize: 18.0, color: Colors.grey.shade500),
                      )
                    ],
                  ),
                ),
              )
            ],
          ),
        ));
      },
    );
  }

  /// 底部手机号选择
  static Future<CustomerEntity?> showBottomGussMobileModal(
      BuildContext context, List<CustomerEntity> mapData, onPressed(CustomerEntity? item),
      {String? cabinetLocationCode}) {
    return showModalBottomSheet(
      context: context,
      enableDrag: false,
      isDismissible: false,
      isScrollControlled: false,
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (context, state) {
          List<Widget> list = <Widget>[];
          mapData.forEach((CustomerEntity item) {
            String mobile = item.mobile.substring(item.mobile.length - 11, item.mobile.length);
            list.add(
              InkWell(
                onTap: () {
                  if (item.isBlacklist == 1) {
                    Fluttertoast.showToast(msg: '黑名单客户无法入柜');
                  } else {
                    onPressed(item);
                  }
                },
                child: Container(
                  height: 76.0,
                  width: MediaQuery.of(context).size.width,
                  padding: EdgeInsets.fromLTRB(15, 0, 0, 0),
                  decoration: BoxDecoration(
                      color: item.isBlacklist == 1 ? Color(0xFFEEEEEE) : Colors.white,
                      border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0))),
                  child: Stack(
                    children: [
                      Container(
                          margin: EdgeInsets.only(top: 5),
                          height: 66,
                          width: MediaQuery.of(context).size.width,
                          alignment: Alignment.centerLeft,
                          child: Container(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Row(
                                  children: [
                                    Offstage(
                                        offstage: !item.mobile.contains("*"),
                                        child: Container(
                                          padding: EdgeInsets.fromLTRB(3, 1, 3, 1),
                                          decoration: BoxDecoration(
                                              color: Theme.of(context).primaryColor,
                                              borderRadius: BorderRadius.circular(4)),
                                          child: Text(
                                            '代发',
                                            style: TextStyle(
                                                fontSize: 10, color: Colors.white, fontWeight: FontWeight.w400),
                                          ),
                                        )),
                                  ],
                                ),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    RichText(
                                        text: TextSpan(children: <TextSpan>[
                                      TextSpan(
                                        text: '${mobile.substring(0, 3)}',
                                        style:
                                            TextStyle(color: Colors.black87, fontSize: 22, fontWeight: FontWeight.w500),
                                      ),
                                      TextSpan(
                                        text: '${mobile.substring(3, 7)}',
                                        style: TextStyle(
                                            color: item.isBlacklist == 1 ? Colors.black87 : Colors.red,
                                            fontSize: 22,
                                            fontWeight: FontWeight.w500),
                                      ),
                                      TextSpan(
                                        text: '${mobile.substring(7, 11)}',
                                        style: TextStyle(
                                            color: item.isBlacklist == 1 ? Colors.red : Colors.black87,
                                            fontSize: 22,
                                            fontWeight: FontWeight.w500),
                                      ),
                                    ])),
                                    Offstage(
                                      offstage: item.name == '' || item.name == null,
                                      child: Container(
                                          padding: EdgeInsets.only(left: 10),
                                          child: Text(
                                              '${item.name.length > 10 ? item.name.replaceRange(10, item.name.length, '***') : item.name}',
                                              style: TextStyle(
                                                  fontSize: 18, color: Colors.black87, fontWeight: FontWeight.w600))),
                                    ),
                                    Offstage(
                                      offstage: item.mobileDesc == '' || item.mobileDesc == null,
                                      child: Container(
                                          padding: EdgeInsets.only(left: 10),
                                          child: Text('${item.mobileDesc}',
                                              style: TextStyle(
                                                  fontSize: 14, color: Colors.black87, fontWeight: FontWeight.w600))),
                                    ),
                                  ],
                                ),
                                Offstage(
                                  offstage: item.lastInboundTime == '' || item.lastInboundTime == null,
                                  child: Text('最近入库: ${item.lastInboundTime}',
                                      style: TextStyle(
                                          fontSize: 13,
                                          color:
                                              item.isBlacklist == 1 ? Colors.black87 : Theme.of(context).primaryColor,
                                          fontWeight: FontWeight.w400)),
                                ),
                                Offstage(
                                  offstage: item.address == '' || item.address == null,
                                  child: Text('${item.address}',
                                      style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.black87 ,
                                          fontWeight: FontWeight.w300)),
                                ),
                              ],
                            ),
                          )),
                      Positioned(
                        right: 10,
                        top: 0,
                        child: Offstage(
                            offstage: item.mobile.contains("*") || item.isBlacklist == 1,
                            child: InkWell(
                              onTap: () {
                                CommonUtils.inputWaybillNo(context, item.name, (name) async {
                                  if (name != '') {
                                    item.name = name;
                                    Map<String, dynamic> params = item.toJson();
                                    if (cabinetLocationCode != '') {
                                      params.putIfAbsent('cabinetLocationCode', () => cabinetLocationCode);
                                    }
                                    DataResult res = await CourierDao.updateCustomer(params);
                                    if (res.result) {}
                                  }
                                }, title: '客户名称', label: '姓名');
                              },
                              child: Container(
                                width: 32,
                                height: 32,
                                decoration: BoxDecoration(
                                  color: Theme.of(context).primaryColorLight,
                                  borderRadius: BorderRadius.all(Radius.circular(20.0)),
                                ),
                                margin: EdgeInsets.only(top: 18),
                                child: Icon(
                                  Icons.edit,
                                  size: 20,
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                            )),
                      ),
                      Positioned(
                        right: 0,
                        top: 0,
                        child: Offstage(
                            offstage: item.isBlacklist != 1,
                            child: Image.asset(
                              LocalImageUtil.getImagePath('blackList'),
                              width: 60.0,
                            )),
                      )
                    ],
                  ),
                ),
              ),
            );
          });
          return Container(
              child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: list,
                ),
                InkWell(
                  onTap: () {
                    onPressed(null);
                    // Navigator.pop(context);
                  },
                  child: Container(
                    margin: EdgeInsets.only(top: 10.0),
                    height: 50.0,
                    padding: EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
                    decoration: BoxDecoration(
                        color: Colors.white, border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0))),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        Text(
                          '取消',
                          style: TextStyle(fontSize: 18.0, color: Colors.grey.shade500),
                        )
                      ],
                    ),
                  ),
                )
              ],
            ),
          ));
        });
      },
    );
  }

  static showBottomBrandSelectModal(BuildContext context, Map mapData, onPressed(item),
      {String? bindCompany, String? showText, bool isForce = false}) {
    String selected = '';
    return showModalBottomSheet(
      enableDrag: false,
      isDismissible: false,
      isScrollControlled: false,
      context: context,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
        topLeft: Radius.circular(10.0),
        topRight: Radius.circular(10.0),
      )),
      builder: (BuildContext _context) {
        return StatefulBuilder(
          builder: (_context, state) {
            buildList() {
              List<Widget> list = <Widget>[];
              mapData.forEach((k, v) {
                Widget currentBrandWidget = Text('');
                if (bindCompany != null && bindCompany != '') {
                  currentBrandWidget = Container(
                      width: 70,
                      child: Text('当前品牌',
                          style:
                              TextStyle(color: Theme.of(context).primaryColor, fontSize: AppConstant.smallTextSize)));
                }
                list.add(
                  InkWell(
                    onTap: () {
                      state(() {
                        selected = k;
                      });
                    },
                    child: Container(
                        height: 50.0,
                        padding: EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
                        margin: EdgeInsets.only(left: 10, right: 10),
                        decoration: BoxDecoration(
                            color: selected == k ? DefaultConfig().configs.BG_COLOR : Colors.white,
                            borderRadius: BorderRadius.all(Radius.circular(10))),
                        child: Container(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: <Widget>[
                              Offstage(
                                offstage: bindCompany != k,
                                child: currentBrandWidget,
                              ),
                              Text(
                                v,
                                style: TextStyle(fontSize: 18.0, color: Colors.black87),
                              ),
                              Offstage(
                                offstage: bindCompany != k,
                                child: Padding(
                                  padding: EdgeInsets.only(right: 70),
                                ),
                              ),
                            ],
                          ),
                        )),
                  ),
                );
              });
              return list;
            }

            return Stack(
              children: [
                Container(
                  height: 30.0,
                  width: double.infinity,
                  color: Colors.black54,
                ),
                Container(
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      )),
                ),
                Container(
                    padding: EdgeInsets.only(top: 10),
                    height: MediaQuery.of(context).size.height * 0.6,
                    child: Column(
                      children: [
                        Container(
                          padding: EdgeInsets.fromLTRB(15, 5, 15, 10),
                          decoration:
                              BoxDecoration(border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0))),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              InkWell(
                                onTap: () {
                                  if (!isForce) {
                                    Navigator.pop(context);
                                  }
                                },
                                child: Text(isForce ? '' : '取消',
                                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
                              ),
                              Text(showText == 'show' ? '选择所属快递公司' : '选择快递品牌',
                                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
                              InkWell(
                                onTap: () {
                                  if (isForce) {
                                    if (selected != '') {
                                      onPressed(selected);
                                      Navigator.pop(context);
                                    }
                                  } else {
                                    onPressed(selected);
                                    Navigator.pop(context);
                                  }
                                },
                                child: Text('确定',
                                    style: TextStyle(
                                        fontSize: 16,
                                        color: Theme.of(context).primaryColor,
                                        fontWeight: FontWeight.w500)),
                              )
                            ],
                          ),
                        ),
                        Expanded(
                          child: SingleChildScrollView(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: buildList(),
                            ),
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.fromLTRB(20, 5, 20, 10),
                          decoration:
                              BoxDecoration(border: Border(top: BorderSide(color: Color(0xFFEEEEEE), width: 1.0))),
                          child: Center(
                            child: Text(showText == 'show' ? '如需修改所属快递品牌，请在我的界面选择修改' : '如需入柜其他品牌，请在我的-快递品牌管理开启对应品牌',
                                style: TextStyle(fontSize: 13, color: Color(0xFF999999))),
                          ),
                        )
                      ],
                    )),
              ],
            );
          },
        );
      },
    );
  }

  static showBottomCardModal(BuildContext context, PackageViewSjEntity package, onCancelPressed(), onSurePressed(),
      {String? bindCompany, String? showText}) {
    return showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
        topLeft: Radius.circular(10.0),
        topRight: Radius.circular(10.0),
      )),
      builder: (BuildContext _context) {
        return Container(
          height: 420,
          padding: EdgeInsets.all(15),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CommonRowWidget(
                label: '点位信息',
                value: package.cabinetLocationName,
              ),
              CommonRowWidget(
                label: '柜门号',
                value: '${package.cabinetName}-${package.cabinetBoxLabel}',
              ),
              Container(
                margin: EdgeInsets.only(top: 5),
                height: 1,
                color: Colors.grey.shade200,
                width: double.infinity,
              ),
              CommonRowWidget(
                leftWidget: Row(
                  children: [
                    Text('寄件人信息',
                        style: TextStyle(
                            fontSize: AppConstant.smallTextSize,
                            color: Theme.of(context).primaryColor,
                            fontWeight: FontWeight.bold)),
                  ],
                ),
              ),
              CommonRowWidget(
                label: '${package.senderName}',
                rightWidget: InkWell(
                    onTap: () async {
                      await CommonUtils.makePhoneCall(context, package.receiverMobile);
                    },
                    child: Row(
                      children: [
                        Icon(Icons.phone_in_talk, size: 16, color: Colors.blue),
                        Text(
                          '${package.senderMobile}',
                          style: TextStyle(fontSize: AppConstant.smallTextSize, color: Colors.blue),
                        )
                      ],
                    )),
              ),
              CommonRowWidget(
                label:
                    '${package.senderProvinceName}${package.senderCityName}${package.senderAreaName}${package.senderStreetName}',
                value: '',
              ),
              CommonRowWidget(
                leftWidget: Row(
                  children: [
                    Text('收件人信息',
                        style: TextStyle(
                            fontSize: AppConstant.smallTextSize,
                            color: Theme.of(context).primaryColor,
                            fontWeight: FontWeight.bold)),
                  ],
                ),
              ),
              CommonRowWidget(
                label: '${package.receiverName}',
                rightWidget: InkWell(
                    child: Row(
                  children: [
                    Text('${package.receiverMobile}',
                        style: TextStyle(fontSize: AppConstant.smallTextSize, color: Colors.black)),
                  ],
                )),
              ),
              CommonRowWidget(
                label:
                    '${package.receiverProvinceName}${package.receiverCityName}${package.receiverAreaName}${package.receiverStreetName}',
                value: '',
              ),
              Container(
                margin: EdgeInsets.only(top: 5),
                height: 1,
                color: Colors.grey.shade200,
                width: double.infinity,
              ),
              Container(
                alignment: Alignment.centerLeft,
                child: Text(
                  '1、物品确认无误后，请点击【确认取出】按钮\n2、如无法寄送，请【取消订单】并通知客户',
                  style: TextStyle(fontSize: 14, color: Colors.red, fontWeight: FontWeight.w500),
                ),
                margin: EdgeInsets.only(top: 10),
              ),
              Expanded(
                  child: Container(
                alignment: Alignment.bottomLeft,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Offstage(
                      offstage: false,
                      child: ElevatedButton(
                        child: Text("取消订单",
                            style: TextStyle(
                                fontSize: AppConstant.smallTextSize, color: DefaultConfig().configs.WHITE_COLOR)),
                        onPressed: () {
                          onCancelPressed();
                        },
                        style: ButtonStyle(
                            minimumSize: MaterialStateProperty.all(Size(80, 32)),
                            shape: MaterialStateProperty.all(StadiumBorder()),
                            side: MaterialStateProperty.all(BorderSide(color: Theme.of(context).primaryColor)),
                            backgroundColor: MaterialStateProperty.all(Theme.of(context).primaryColor)),
                      ),
                    ),
                    Offstage(
                      offstage: false,
                      child: ElevatedButton(
                        child: Text("确认取出",
                            style: TextStyle(
                                fontSize: AppConstant.smallTextSize, color: DefaultConfig().configs.WHITE_COLOR)),
                        onPressed: () {
                          onSurePressed();
                        },
                        style: ButtonStyle(
                            minimumSize: MaterialStateProperty.all(Size(80, 32)),
                            shape: MaterialStateProperty.all(StadiumBorder()),
                            side: MaterialStateProperty.all(BorderSide(color: Theme.of(context).primaryColor)),
                            backgroundColor: MaterialStateProperty.all(Theme.of(context).primaryColor)),
                      ),
                    ),
                  ],
                ),
              ))
            ],
          ),
        );
      },
    );
  }

  static Future<CabinetShopEntity?> showBottomShopSelectModal(BuildContext context, List<CabinetShopEntity?> shopList) {
    String selected = '';
    return showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
        topLeft: Radius.circular(10.0),
        topRight: Radius.circular(10.0),
      )),
      builder: (BuildContext _context) {
        return StatefulBuilder(
          builder: (_context, state) {
            buildList() {
              List<Widget> list = <Widget>[];
              shopList.forEach((item) {
                list.add(
                  InkWell(
                    onTap: () {
                      Navigator.pop(context, item);
                    },
                    child: Container(
                        height: 64.0,
                        padding: EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
                        decoration: BoxDecoration(color: Colors.white),
                        child: Container(
                            decoration: BoxDecoration(
                                color: Colors.white,
                                border: Border(
                                  bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1),
                                )),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: <Widget>[
                                    Text(
                                      item!.name ?? '',
                                      style: TextStyle(fontSize: 18.0, color: Colors.black87),
                                    ),
                                  ],
                                ),
                                Wrap(
                                  children: [
                                    Text(
                                      '极大：${item.superCount ?? 0}',
                                      style: TextStyle(fontSize: 14.0, color: Colors.black54),
                                    ),
                                    Text(
                                      '超大：${item.hugeCount ?? 0}',
                                      style: TextStyle(fontSize: 14.0, color: Colors.black54),
                                    ),
                                    Text(
                                      '大：${item.largeCount ?? 0}',
                                      style: TextStyle(fontSize: 14.0, color: Colors.black54),
                                    ),
                                    Text(
                                      '中：${item.mediumCount ?? 0}',
                                      style: TextStyle(fontSize: 14.0, color: Colors.black54),
                                    ),
                                    Text(
                                      '小：${item.smallCount ?? 0}',
                                      style: TextStyle(fontSize: 14.0, color: Colors.black54),
                                    ),
                                    Text(
                                      '超小：${item.miniCount ?? 0}',
                                      style: TextStyle(fontSize: 14.0, color: Colors.black54),
                                    ),
                                    Text(
                                      '极小：${item.microCount ?? 0}',
                                      style: TextStyle(fontSize: 14.0, color: Colors.black54),
                                    ),
                                  ],
                                )
                              ],
                            ))),
                  ),
                );
              });
              return list;
            }

            return Stack(
              children: [
                Container(
                  height: 30.0,
                  width: double.infinity,
                  color: Colors.black54,
                ),
                Container(
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      )),
                ),
                Container(
                    padding: EdgeInsets.only(top: 10),
                    height: MediaQuery.of(context).size.height * 0.6,
                    decoration: BoxDecoration(),
                    child: Column(
                      children: [
                        Container(
                          padding: EdgeInsets.fromLTRB(15, 5, 15, 10),
                          decoration:
                              BoxDecoration(border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0))),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              InkWell(
                                onTap: () {
                                  Navigator.pop(context);
                                },
                                child: Text('', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
                              ),
                              Text('选择门店点位', style: TextStyle(fontSize: 20, fontWeight: FontWeight.w300)),
                              InkWell(
                                onTap: () {
                                  Navigator.pop(context, selected);
                                },
                                child: Text('',
                                    style: TextStyle(
                                        fontSize: 16,
                                        color: Theme.of(context).primaryColor,
                                        fontWeight: FontWeight.w500)),
                              )
                            ],
                          ),
                        ),
                        Expanded(
                            child: Container(
                          color: Color(0xFFF5F5F5),
                          child: SingleChildScrollView(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: buildList(),
                            ),
                          ),
                        )),
                      ],
                    )),
              ],
            );
          },
        );
      },
    );
  }

  static Future<BoxItemEntity?> showBottomBoxItemSelectModal(
      BuildContext context, List<BoxItemEntity?> boxList, String? boxId) {
    return showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
        topLeft: Radius.circular(10.0),
        topRight: Radius.circular(10.0),
      )),
      builder: (BuildContext _context) {
        return StatefulBuilder(
          builder: (_context, state) {
            buildList() {
              List<Widget> list = <Widget>[];
              boxList.forEach((item) {
                list.add(
                  InkWell(
                    onTap: () {
                      Navigator.pop(context, item);
                    },
                    child: Container(
                        height: 64.0,
                        padding: EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
                        margin: EdgeInsets.only(left: 10, right: 10),
                        decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border(
                              bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1),
                            )),
                        child: Container(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: <Widget>[
                              RichText(
                                text: TextSpan(children: <TextSpan>[
                                  TextSpan(
                                      text: item?.boxLabel ?? '',
                                      style: TextStyle(
                                          fontSize: 28.0,
                                          color: boxId == item?.boxId ? Theme.of(context).primaryColor : Colors.black87,
                                          fontWeight: FontWeight.w600)),
                                  TextSpan(
                                      text: '号',
                                      style: TextStyle(
                                          fontSize: 20.0, color: Colors.black87, fontWeight: FontWeight.w400)),
                                ]),
                              ),
                            ],
                          ),
                        )),
                  ),
                );
              });
              return list;
            }

            return Stack(
              children: [
                Container(
                  height: 30.0,
                  width: double.infinity,
                  color: Colors.black54,
                ),
                Container(
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      )),
                ),
                Container(
                    padding: EdgeInsets.only(top: 10),
                    height: MediaQuery.of(context).size.height * 0.5,
                    child: Column(
                      children: [
                        Container(
                          padding: EdgeInsets.fromLTRB(15, 5, 15, 10),
                          decoration:
                              BoxDecoration(border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0))),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text('选择格口', style: TextStyle(fontSize: 20, fontWeight: FontWeight.w300)),
                            ],
                          ),
                        ),
                        Expanded(
                            child: Container(
                          color: Color(0xFFF5F5F5),
                          child: SingleChildScrollView(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: buildList(),
                            ),
                          ),
                        )),
                      ],
                    )),
              ],
            );
          },
        );
      },
    );
  }

  /// 选择预设货架号层数
  static choosePresetFloor(BuildContext context, int floors, onPressed(item)) {
    if (floors > 0) {
      List<Widget> list = <Widget>[];
      for (int i = 0; i < floors; i++) {
        list.add(
          InkWell(
            onTap: () {
              onPressed(i + 1);
              Navigator.pop(context);
            },
            child: Container(
              height: 50.0,
              padding: EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
              decoration: BoxDecoration(
                  color: Colors.white, border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0))),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  Text(
                    '${i + 1}层',
                    style: TextStyle(fontSize: 14.0, color: Colors.black87),
                  )
                ],
              ),
            ),
          ),
        );
      }
      return showModalBottomSheet(
        context: context,
        builder: (BuildContext context) {
          return Container(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: list,
                ),
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    margin: EdgeInsets.only(top: 10.0),
                    height: 50.0,
                    padding: EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
                    decoration: BoxDecoration(
                        color: Colors.white, border: Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1.0))),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        Text(
                          '取消',
                          style: TextStyle(fontSize: 18.0, color: Colors.grey.shade500),
                        )
                      ],
                    ),
                  ),
                )
              ],
            ),
          );
        },
      );
    } else {
      Fluttertoast.showToast(msg: '暂无层数');
    }
  }

  static makePhoneCall(BuildContext context, String? phone) async {
    bool isPhone = RegExpUtil.isPhone(phone!);
    if (phone == '' || !isPhone) {
      return false;
    }
    String phoneNumber = 'tel:' + phone;
    if (await canLaunch(phoneNumber)) {
      await launch(phoneNumber);
    } else {
      CommonUtils.alert(context, dialogContent: '拨打电话出错');
    }
  }

  static clipboradData(String value) async {
    Clipboard.setData(ClipboardData(text: value));
    ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
    if (CheckUtils.isNotNull(data?.text)) {
      Fluttertoast.showToast(msg: "复制成功", gravity: ToastGravity.CENTER);
    }
  }
}
