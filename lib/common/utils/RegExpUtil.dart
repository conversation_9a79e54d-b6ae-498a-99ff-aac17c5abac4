class RegExpUtil {
  static RegExp phoneEpx = RegExp(r'^1[3-9][0-9]\d{8}$');
  static RegExp virtualPhoneEpx = RegExp(r'^1[3-9]\d[0-9*]{4}\d{4}$');
  static RegExp virtualPhoneEpx2 = RegExp(r'^[1*][3-9*][0-9*]{5}\d{4}$');
  static RegExp notZeroNNumberEpx = RegExp(r'^([1-9]\d*|0)(\.\d{1,2})?$|^0\.0?[1-9]$');
  static RegExp idNumberEpx =
  RegExp(r'^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$');
  static RegExp realNameEpx = RegExp(r'^[a-zA-Z\u4E00-\u9FA5\uf900-\ufa2d·s]{2,20}$');

  static isPhone(String phone) {
    return phoneEpx.hasMatch(phone);
  }

  static checkPhone(String? phone) {
    if (phone != null) {
      if (phone.length == 11) {
        return virtualPhoneEpx2.hasMatch(phone);
      } else if (phone.length > 11) {
        return true;
      }
    }
    return false;
  }

  static checkPhoneInRule(String? phone, {bool hasSecret = false}) {
    if (phone != null) {
      return virtualPhoneEpx.hasMatch(phone);
    }
    return false;
  }

  static checkPhoneContainsAsterisk(String? phone, {bool hasSecret = false}) {
    if (phone != null) {
      return virtualPhoneEpx2.hasMatch(phone);
    }
    return false;
  }

  static isIdNumber(String phone) {
    return idNumberEpx.hasMatch(phone);
  }

  static isNotZeroNumber(String value) {
    return notZeroNNumberEpx.hasMatch(value);
  }

  static commonExp(String str, RegExp regExp) {
    return regExp.hasMatch(str);
  }

  static isRealName(String str) {
    return realNameEpx.hasMatch(str);
  }
}
