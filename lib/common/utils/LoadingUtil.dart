import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class LoadingUtil extends StatefulWidget {
  final String? status;

  LoadingUtil({Key? key, this.status}) : super(key: key);

  void show(BuildContext context) {
    EasyLoading.show(status: status, maskType: EasyLoadingMaskType.black);
    // showDialog(
    //   barrierDismissible: true,
    //   context: context,
    //   builder: (ctx) => Theme(
    //     data: Theme.of(ctx).copyWith(dialogBackgroundColor: Colors.transparent),
    //     child: LoadingUtil(
    //       status: status,
    //     ),
    //   ),
    // );
  }

  static void dismiss(context) {
    EasyLoading.dismiss();
    // Navigator.pop(context);
  }

  @override
  _LoadingUtilState createState() => _LoadingUtilState();
}

class _LoadingUtilState extends State<LoadingUtil> {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.transparent,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(5),
              ),
              width: 60,
              height: 60,
              alignment: Alignment.center,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  SpinKitFadingCircle(
                    color: Colors.black,
                    size: 46.0,
                  )
                ],
              ),
            ),
            Container(
                margin: EdgeInsets.only(top: 12),
                child: Text(
                  widget.status ?? '',
                  style: TextStyle(color: Colors.white, fontSize: 15),
                ))
          ],
        ),
      ),
    );
  }
}
