import 'dart:convert';
import 'dart:math' as math;

import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/entitys/box_price_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/user_info_entity.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:flutter/cupertino.dart';
import 'package:fluttertoast/fluttertoast.dart';

class CabinetBoxUtil {
  /// 组装点位可用格口数量 类型 价格
  /// * @param [rentJson] 租用价格json字符串
  /// * @param [countMap]
  static getCabinetBoxAvailableCount(String? rentJson, Map<String, dynamic> countMap, {bool isDefault = true}) {
    Map<String, dynamic> priceInfo = {};
    if (rentJson != null) {
      priceInfo = jsonDecode(rentJson);
      if (priceInfo['supers'] == null) {
        priceInfo['supers'] = 0;
      }
      if (priceInfo['micro'] == null) {
        priceInfo['micro'] = 0;
      }
    } else {
      priceInfo = {'supers': 0, "huge": 0, "large": 0, "medium": 0, "small": 0, "mini": 0, 'micro': 0,};
    }
    BoxPriceEntity priceEntity = BoxPriceEntity.fromJson(priceInfo);
    Map<String, List<dynamic>> brandPriceMap = {};
    brandPriceMap.putIfAbsent('DEFAULT', () => buildPriceList(priceEntity, countMap));
    if (isDefault) {
      return brandPriceMap['DEFAULT'];
    }
    priceInfo.keys.forEach((item) {
      if (priceInfo[item] is Map) {
        priceEntity = BoxPriceEntity.fromJson(priceInfo[item]);
        brandPriceMap.putIfAbsent(item, () => buildPriceList(priceEntity, countMap));
      }
      if (item == 'list') {
        List<dynamic> brandList = priceInfo[item];
        brandList.forEach((ele) {
          priceEntity = BoxPriceEntity.fromJson(ele);
          brandPriceMap.putIfAbsent(ele['brandCode'], () => buildPriceList(priceEntity, countMap));
        });
      }
    });
    return brandPriceMap;
  }

  static buildPriceList(BoxPriceEntity priceEntity, Map<String, dynamic> countMap) {
    return [
      {'type': 6, 'availableNum': countMap['microCount'], 'price': priceEntity.micro},
      {'type': 5, 'availableNum': countMap['miniCount'], 'price': priceEntity.mini},
      {'type': 4, 'availableNum': countMap['smallCount'], 'price': priceEntity.small},
      {'type': 3, 'availableNum': countMap['mediumCount'], 'price': priceEntity.medium},
      {'type': 2, 'availableNum': countMap['largeCount'], 'price': priceEntity.large},
      {'type': 1, 'availableNum': countMap['hugeCount'], 'price': priceEntity.huge},
      {'type': 0, 'availableNum': countMap['superCount'], 'price': priceEntity.supers},
    ];
  }

  static loginCheck(BuildContext context, UserInfoEntity? userData, String? captchaEditingController,
      bool captchaShow, String? channel) async {
    if (captchaEditingController == '' && captchaShow) {
      Fluttertoast.showToast(msg: '验证码不能为空');
      return;
    }
    LoadingUtil(
      status: '校验登录中...',
    ).show(context);
    Map<String, dynamic> info = {'shopYzAccountId': userData?.id, 'checkCode': captchaEditingController};
    var res = await UserDao.loginCheck(info);
    LoadingUtil.dismiss(context);
    return res;
  }

  static getDistance(double? distance) {
    if (distance != null) {
      if (distance > 1000) {
        return (distance / 1000).toStringAsFixed(2) + 'km';
      } else {
        return distance.toStringAsFixed(2) + 'm';
      }
    }
    return '未知';
  }

  ///经纬度转换成三角函数中度分表形式。
  static rad(double d) {
    return d * math.pi / 180.0;
  }

  static getCabinetDistanceByLocation(double lat1, double lng1, double? lat2, double? lng2) {
    double radLat1 = rad(lat1);
    double radLat2 = 0;
    if (lat2 != null) {
      radLat2 = rad(lat2);
    }
    double a = radLat1 - radLat2;
    double radLng2 = 0;
    if (lng2 != null) {
      radLng2 = rad(lng2);
    }
    double b = rad(lng1) - radLng2;
    var s = 2 *
        math.asin(math
            .sqrt(math.pow(math.sin(a / 2), 2) + math.cos(radLat1) * math.cos(radLat2) * math.pow(math.sin(b / 2), 2)));
    s = s * 6378.137; // EARTH_RADIUS;
    s = (s * 10000).round() / 10; //输出为米
    return getDistance(s);
  }
}
