
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:flutter/cupertino.dart';

class NetworkImageUtil {
  /// 获取网络图片
  /// * @param [key] 图片key
  /// * @param [width] 图片宽度
  /// * @param [height] 图片高度
  /// * @return String
  static getImageNetwork(String? key, {double? width, double? height}) {
    return Image(image: NetworkImage(getImageUrl(key)), width: width, height: height);
  }

  /// 获取网络图片路径
  /// * @param [key] 图片key
  /// * @return String
  static getImageUrl(String? key) {
    if (CheckUtils.isNotNull(key)) {
      return DefaultConfig().configs.BASE_URL + imageMapper[key];
    }
    return 'noData';
  }

  static Map<String, dynamic> imageMapper = {
    'logo-bg': '/app/images/logo-bg.png',
    'splash': '/app/images/splash.png',
    'logo': '/app/images/logo/logo.png',
    'logoAvatar': '/app/images/logo/logo-avatar.png',
    'logoAvatarCircle': '/app/images/logo/logo-avatar-circle.png',
    'logoAvatarCircleSmall': '/app/images/logo/logo-avatar-circle-small.png',
    '1': '/app/images/menu/1.png',
    '2': '/app/images/menu/2.png',
    '3': '/app/images/menu/3.png',
    '4': '/app/images/menu/4.png',
    '5': '/app/images/menu/5.png',
    '6': '/app/images/menu/6.png',
    '7': '/app/images/menu/7.png',
    '8': '/app/images/menu/8.png',
    '9': '/app/images/menu/9.png',
    'data': '/app/images/tab/data.png',
    'my': '/app/images/tab/my.png',
    'home': '/app/images/tab/home.png',
    'notify': '/app/images/tab/notify.png',
    'scanIndex': '/app/images/tab/scan-index.png',
    'dataActive': '/app/images/tab/data-active.png',
    'myActive': '/app/images/tab/my-active.png',
    'homeActive': '/app/images/tab/home-active.png',
    'notifyActive': '/app/images/tab/notify-active.png',
    'hd': '/app/images/notify/hd.png',
    'yy': '/app/images/notify/yy.png',
    'zl': '/app/images/notify/zl.png',
    'xx': '/app/images/notify/xx.png',
    'an': '/app/images/brand/an.png',
    'bs': '/app/images/brand/bs.png',
    'db': '/app/images/brand/db.png',
    'dd': '/app/images/brand/dd.png',
    'dn': '/app/images/brand/dn.png',
    'ems': '/app/images/brand/ems.png',
    'gt': '/app/images/brand/gt.png',
    'ht': '/app/images/brand/ht.png',
    'jd': '/app/images/brand/jd.png',
    'jh': '/app/images/brand/jh.png',
    'jt': '/app/images/brand/jt.png',
    'kj': '/app/images/brand/kj.png',
    'lbws': '/app/images/brand/lbws.png',
    'pj': '/app/images/brand/pj.png',
    'qf': '/app/images/brand/qf.png',
    'qy': '/app/images/brand/qy.png',
    'rfd': '/app/images/brand/rfd.png',
    'sad': '/app/images/brand/sad.png',
    'sb': '/app/images/brand/sb.png',
    'se': '/app/images/brand/se.png',
    'sf': '/app/images/brand/sf.png',
    'sn': '/app/images/brand/sn.png',
    'st': '/app/images/brand/st.png',
    'tt': '/app/images/brand/tt.png',
    'yd': '/app/images/brand/yd.png',
    'ys': '/app/images/brand/ys.png',
    'yz': '/app/images/brand/yz.png',
    'zt': '/app/images/brand/zt.png',
    'yt': '/app/images/brand/yt.png',
    'zy': '/app/images/brand/zy.png',
    'search': '/app/images/common/search.png',
    'searchNull': '/app/images/common/searchNull.png',
    'box3': '/app/images/common/box-middle.png',
    'boxOpen': '/app/images/common/box-open.png',
    'mobile': '/app/images/common/mobile.png',
    'banner': '/app/images/common/banner.png',
    'noData': '/app/images/common/no-data.png',
    'edit': '/app/images/common/edit.png',
    'tz': '/app/images/common/tz.png',
    'refresh': '/app/images/common/refresh.png',
    'refreshGrey': '/app/images/common/refresh-grey.png',
    'flashOpen': '/app/images/common/flash-open.png',
    'flashClose': '/app/images/common/flash-close.png',
    'cabinetOpen': '/app/images/common/cabinet-open.png',
    'payFail': '/app/images/pay/fail.png',
    'paySuccess': '/app/images/pay/success.png',
    'wx': '/app/images/pay/wx.png',
    'alipay': '/app/images/pay/alipay.png',
    'cash': '/app/images/pay/cash.png',
    'bg': '/app/images/my/bg.png',
    'brands': '/app/images/my/brands.png',
    'editBlack': '/app/images/my/edit-black.png',
    'help': '/app/images/my/help.png',
    'server': '/app/images/my/server.png',
    'setting': '/app/images/my/setting.png',
    'share': '/app/images/my/share.png',
    'wallet': '/app/images/my/wallet.png',
    'editCk': '/app/images/editCk.png',
    'loginBg': '/app/images/loginBg.png',
    'loginLogo': '/app/images/loginLogo.png',
    'editGrey': '/app/images/edit-grey.png',
    'isNew': '/app/images/isNew.png',
    'location': '/app/images/common/location.png',
    'noticeWx': '/app/images/noticeWx.png',
    'markRed': '/app/images/customer/markRed.png',
    'markYellow': '/app/images/customer/markYellow.png',
    'markMg': '/app/images/customer/markMg.png',
    'markLh': '/app/images/customer/markLh.png',
    'picLoss': '/app/images/pic-loss.png',
    'contact': '/app/images/my/contact.png',
    'zngzs': '/app/images/zngzs.png',
  };
}
