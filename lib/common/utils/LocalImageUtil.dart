import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:flutter/cupertino.dart';

class LocalImageUtil {
  static const channel = String.fromEnvironment('CHANNEL');

  /// 获取本地图片
  /// * @param [key] 图片key
  /// * @param [width] 图片宽度
  /// * @param [height] 图片高度
  /// * @param [isChannel] 是否分渠道 仅针对logo 启动页类型的图片
  /// * @return String
  static getImageAsset(String? key, {double? width, double? height, bool isChannel = false, BoxFit? fit}) {
    return Image(
      image: AssetImage(getImagePath(key, isChannel: isChannel)),
      width: width,
      height: height,
      fit: fit,
    );
  }

  /// 获取本地图片路径
  /// * @param [key] 图片key
  /// * @param [isChannel] 是否分渠道 仅针对logo 启动页类型的图片
  /// * @return String
  static getImagePath(String? key, {bool isChannel = false}) {
    if (CheckUtils.isNotNull(key)) {
      String path = isChannel ? getChanelPath() : 'static/images';
      return path + imageMapperLocal[key];
    }
    return 'noData';
  }

  static getChanelPath() {
    String path = 'static/images';
    switch (channel) {
      case 'bl':
        path += '-bl';
        break;
      default:
        path += '';
        break;
    }
    return path;
  }

  static Map<String, dynamic> imageMapperLocal = {
    'logo-bg': '/logo-bg.png',
    'splash': '/splash.png',
    'logo': '/logo/logo.png',
    'uploadFail': '/upload-fail.png',
    'billed': '/billed.png',
    'scan': '/scan.png',
    'checked': '/checked.png',
    'logoAvatar': '/logo/logo-avatar.png',
    'logoAvatarCircle': '/logo/logo-avatar-circle.png',
    'logoAvatarCircleSmall': '/logo/logo-avatar-circle-small.png',
    '1': '/menu/1.png',
    '2': '/menu/2.png',
    '3': '/menu/3.png',
    '4': '/menu/4.png',
    '5': '/menu/5.png',
    '6': '/menu/6.png',
    '7': '/menu/7.png',
    '8': '/menu/8.png',
    '9': '/menu/9.png',
    '10': '/menu/10.png',
    '11': '/menu/11.png',
    '12': '/menu/12.png',
    '13': '/menu/13.png',
    '14': '/menu/14.png',
    '15': '/menu/15.png',
    '16': '/menu/16.png',
    '17': '/menu/17.png',
    '18': '/menu/18.png',
    '19': '/menu/19.png',
    'data': '/tab/data.png',
    'my': '/tab/my.png',
    'home': '/tab/home.png',
    'notify': '/tab/notify.png',
    'scanIndex': '/tab/scan-index.png',
    'dataActive': '/tab/data-active.png',
    'myActive': '/tab/my-active.png',
    'homeActive': '/tab/home-active.png',
    'notifyActive': '/tab/notify-active.png',
    'hd': '/notify/hd.png',
    'yy': '/notify/yy.png',
    'zl': '/notify/zl.png',
    'xx': '/notify/xx.png',
    'an': '/brand/an.png',
    'bs': '/brand/bs.png',
    'db': '/brand/db.png',
    'dd': '/brand/dd.png',
    'dn': '/brand/dn.png',
    'ems': '/brand/ems.png',
    'gt': '/brand/gt.png',
    'ht': '/brand/ht.png',
    'jd': '/brand/jd.png',
    'jh': '/brand/jh.png',
    'jt': '/brand/jt.png',
    'kj': '/brand/kj.png',
    'lbws': '/brand/lbws.png',
    'pj': '/brand/pj.png',
    'qf': '/brand/qf.png',
    'qy': '/brand/qy.png',
    'rfd': '/brand/rfd.png',
    'sad': '/brand/sad.png',
    'sb': '/brand/sb.png',
    'se': '/brand/se.png',
    'sf': '/brand/sf.png',
    'sn': '/brand/sn.png',
    'st': '/brand/st.png',
    'tt': '/brand/tt.png',
    'fw': '/brand/fw.png',
    'yd': '/brand/yd.png',
    'ys': '/brand/ys.png',
    'yz': '/brand/yz.png',
    'zt': '/brand/zt.png',
    'yt': '/brand/yt.png',
    'zy': '/brand/zy.png',
    'tm': '/brand/tm.png',
    'pddYz': '/brand/PDD_YZ.png',
    'ztoTx': '/brand/ZTO_TX.png',
    'YD_CS': '/brand/YD_CS.png',
    'ytoMm': '/brand/YTO_MM.png',
    'mz': '/brand/mz.png',
    'search': '/common/search.png',
    'searchGrey': '/search.png',
    'searchNull': '/common/searchNull.png',
    'box3': '/common/box-middle.png',
    'reBack': '/common/reback.png',
    'box': '/common/box.png',
    'boxOpen': '/common/box-open.png',
    'mobile': '/common/mobile.png',
    'blackList': '/common/blackList.png',
    'banner': '/common/banner.jpg',
    'noData': '/common/no-data.png',
    'edit': '/common/edit.png',
    'tz': '/common/tz.png',
    'refresh': '/common/refresh.png',
    'refreshGrey': '/common/refresh-grey.png',
    'flashOpen': '/common/flash-open.png',
    'flashClose': '/common/flash-close.png',
    'cabinetOpen': '/common/cabinet-open.png',
    'payFail': '/pay/fail.png',
    'paySuccess': '/pay/success.png',
    'wx': '/pay/wx.png',
    'alipay': '/pay/alipay.png',
    'cash': '/pay/cash.png',
    'bg': '/my/bg.png',
    'brands': '/my/brands.png',
    'autoSign': '/my/autoSign.png',
    'coMatching': '/my/coMatching.png',
    'userList': '/my/userList.png',
    'userListBind': '/my/userListBind.png',
    'shopCourier': '/my/shopCourier.png',
    'editBlack': '/my/edit-black.png',
    'help': '/my/help.png',
    'server': '/my/server.png',
    'setting': '/my/setting.png',
    'feedback': '/my/feedback.png',
    'share': '/my/share.png',
    'wallet': '/my/wallet.png',
    'editCk': '/editCk.png',
    'loginBg': '/loginBg.png',
    'loginLogo': '/loginLogo.png',
    'editGrey': '/edit-grey.png',
    'isNew': '/isNew.png',
    'location': '/common/location.png',
    'noticeWx': '/noticeWx.png',
    'add': '/common/add.png',
    'addDisable': '/common/addDisable.png',
    'logistics': '/common/logistics.png',
    'logisticsDisable': '/common/logisticsDisable.png',
    'bu': '/common/bu.png',
    'buDisable': '/common/buDisable.png',
    'ocr': '/common/ocr-icon.png',
    'ocrDisable': '/common/ocr-iconDisable.png',
    'jrk': '/common/jrk.png',
    'jrkDisable': '/common/jrkDisable.png',
    'jsrg': '/common/jsrg.png',
    'jsrgDisable': '/common/jsrgDisable.png',
    'khtx': '/common/khtx.png',
    'khtxDisable': '/common/khtxDisable.png',
    'pdaAdd': '/pda/add.png',
    'pdaLogistics': '/pda/logistics.png',
    'pdaBu': '/pda/bu.png',
    'pdaOcr': '/pda/ocr-icon.png',
    'pdaJrk': '/pda/jrk.png',
    'pdaJsrg': '/pda/jsrg.png',
    'pdaKhtx': '/pda/khtx.png',
    'pdaAddDisable': '/pda/addDisable.png',
    'pdaBuDisable': '/pda/buDisable.png',
    'pdaOcrDisable': '/pda/ocr-iconDisable.png',
    'pdaJrkDisable': '/pda/jrkDisable.png',
    'pdaJsrgDisable': '/pda/jsrgDisable.png',
    'pdaKhtxDisable': '/pda/khtxDisable.png',
    'pdaLogisticsDisable': '/pda/logisticsDisable.png',

    'markRed': '/customer/markRed.png',
    'markYellow': '/customer/markYellow.png',
    'markMg': '/customer/markMg.png',
    'markLh': '/customer/markLh.png',
    'picLoss': '/pic-loss.png',
    'contact': '/my/contact.png',
    'zngzs': '/zngzs.png',
    'gp-login': '/gp-login.png',
    'c_courier': '/c_courier.png',
    'c_station': '/c_station.png',
    'code': '/code.png',
    'delivery-logo': '/delivery-logo.png',
    'again': '/again.png',
    'team': '/my/team.png',
    'cabinet_close': '/cabinet_close.png',
    'cabinet_monitor': '/cabinet_monitor.png',
  };
}
