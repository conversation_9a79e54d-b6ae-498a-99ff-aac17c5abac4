import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/RegExpUtil.dart';
import 'package:flutter/cupertino.dart';

class FormValidateUtil {
  /// 手机号验证
  static String? phoneValidate(String phone, BuildContext context) {
    bool isPhone = RegExpUtil.isPhone(phone);
    if (!isPhone) {
      return CommonUtils.getLocale(context).phoneCheck;
    }
    return null;
  }

  static String? phoneValidate2(String phone, BuildContext context) {
    if (phone.length >= 11) {
      bool isPhone = RegExpUtil.isPhone(phone);
      if (!isPhone) {
        return CommonUtils.getLocale(context).phoneCheck;
      }
    }
    return null;
  }

  /// 密码验证
  static String? passwordValidate(String value) {
    if (!CheckUtils.isNotNull(value)) {
      return "密码不能为空";
    }
    if (value.trim().length < 6) {
      return "密码需要大于6位";
    }
    return null;
  }

  /// 取出原因
  static String? backReasonValidate(String value) {
    if (!CheckUtils.isNotNull(value)) {
      return "退回(取消)原因不能为空";
    }
    if (value.trim().length > 60) {
      return "退回(取消)原因不能60个字";
    }
    return null;
  }

  /// 密码验证
  static String? payPasswordValidate(String value) {
    if (!CheckUtils.isNotNull(value)) {
      return "密码不能为空";
    }
    if (value.trim().length != 6) {
      return "请设置6位数字密码";
    }
    return null;
  }

  /// 身份证校验
  static String? idNumberValidate(String? value) {
    if (value != null) {
      if (!RegExpUtil.isIdNumber(value)) {
        return "身份证号码格式错误";
      }
    }
    return null;
  }

  /// 门店编码
  static String? shopCodeValidate(String? value) {
    if (value != null) {
      if (value == '') {
        return "驿站编码不能为空";
      }
    }
    return null;
  }
  /// 门店编码
  static String? brandCodeValidate(String? value) {
    if (value != null) {
      if (value == '') {
        return "编码不能为空";
      }
    }
    return null;
  }

  /// 非零数字校验
  static String? isNotZeroNumber(String? value) {
    if (value != null) {
      if (value == '') {
        return "金额不能为空";
      } else {
        if (!RegExpUtil.isNotZeroNumber(value)) {
          return "输入金额有误,最多两位小数";
        }
      }
    }
    return null;
  }

  /// 姓名校验
  static String? realNameValidate(String? value) {
    if (value != null) {
      if (value.length < 1) {
        return "姓名不能为空";
      }
      if (!RegExpUtil.isRealName(value)) {
        return "请输入2-10位字母或者汉字";
      }
    }
    return null;
  }

  /// 姓名校验
  static String? captchaValid(String? value) {
    if (value != null) {
      if (value.length < 1) {
        return "验证码不能为空";
      }
    }
    return null;
  }
}
