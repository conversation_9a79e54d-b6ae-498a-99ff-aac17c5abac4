import 'dart:async';

import 'package:cabinet_flutter_app/widget/BarMusicLoading.dart';
import 'package:flutter/material.dart';

class VoiceUtil extends StatefulWidget {

  VoiceUtil({Key? key}) : super(key: key);

  void show(BuildContext context) {
    showDialog(
      barrierDismissible: true,
      context: context,
      builder: (ctx) => Theme(
        data: Theme.of(ctx).copyWith(dialogBackgroundColor: Colors.transparent),
        child: VoiceUtil(),
      ),
    );
  }

  static void dismiss(context) {
    Navigator.pop(context);
  }

  @override
  _VoiceUtilState createState() => _VoiceUtilState();
}

class _VoiceUtilState extends State<VoiceUtil> {
  int countTime = 10;
  Timer? timer;

  @override
  initState() {
    super.initState();
    init();
  }

  init() {
    timer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (countTime < 1) {
          Navigator.pop(context, true);
        Navigator.pop(context);
        timer.cancel();
      } else {
        setState(() {
          countTime--;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        return false;
      },
      child: Container(
        color: Colors.transparent,
        child: Stack(
          children: [
            Positioned(
                top: 0,
                bottom: 0,
                left: 0,
                right: 0,
                child: Center(
                  child: SizedBox(
                    width: 60,
                    child: BarMusicLoading(),
                  ),
                )),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              height: MediaQuery.of(context).size.height * .5,
              child: Center(
                child: OutlinedButton(
                    style: ButtonStyle(
                      shape: MaterialStateProperty.all(StadiumBorder()),
                      minimumSize: MaterialStateProperty.all(Size(120, 40)),
                      side: MaterialStateProperty.all(BorderSide(color: Theme.of(context).primaryColor)),
                    ),
                    onPressed: () {
                      Navigator.pop(context, true);
                    },
                    child: Text(
                      '取消$countTime s',
                      style: TextStyle(fontSize: 20),
                    )),
              ),
            )
          ],
        ),
      ),
    );
  }
}
