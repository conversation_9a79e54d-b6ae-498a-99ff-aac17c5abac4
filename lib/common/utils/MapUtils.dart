// ignore_for_file: deprecated_member_use

import 'dart:io';

import 'package:fluttertoast/fluttertoast.dart';
import 'package:url_launcher/url_launcher.dart';

class MapUtils {
  /// 高德地图
  static Future<bool> gotoAMap(lon, lat) async {
    var url = '${Platform.isIOS ? 'ios' : 'android'}amap://navi?sourceApplication=amap&lat=$lat&lon=$lon&dev=0&style=2';
    bool canLaunchUrl = await canLaunch(url);

    if (!canLaunchUrl) {
      Fluttertoast.showToast(msg: '未检测到高德地图~');
      return false;
    }

    await launch(url);

    return true;
  }

  /// 腾讯地图
  static Future<bool> gotoTencentMap(lon, lat) async {
    var url =
        'qqmap://map/routeplan?type=drive&fromcoord=CurrentLocation&tocoord=$lat,$lon&referer=IXHBZ-QIZE4-ZQ6UP-DJYEO-HC2K2-EZBXJ';
    bool canLaunchUrl = await canLaunch(url);

    if (!canLaunchUrl) {
      Fluttertoast.showToast(msg: '未检测到腾讯地图~');
      return false;
    }

    await launch(url);

    return canLaunchUrl;
  }

  /// 百度地图
  static Future<bool> gotoBaiduMap(lon, lat) async {
    var url = 'baidumap://map/direction?destination=$lat,$lon&coord_type=bd09ll&mode=driving';

    bool canLaunchUrl = await canLaunch(url);

    if (!canLaunchUrl) {
      Fluttertoast.showToast(msg: '未检测到百度地图~');
      return false;
    }

    await launch(url);

    return canLaunchUrl;
  }

  /// 苹果地图
  static Future<bool?> gotoAppleMap(lon, lat) async {
    var url = 'http://maps.apple.com/?&daddr=$lat,$lon';

    bool canLaunchUrl = await canLaunch(url);

    if (!canLaunchUrl) {
      Fluttertoast.showToast(msg: '打开失败~');
      return false;
    }

    await launch(url);
    return null;
  }
}
