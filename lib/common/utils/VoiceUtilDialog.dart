import 'package:cabinet_flutter_app/widget/BarMusicLoading.dart';
import 'package:flutter/material.dart';

class VoiceUtilDialog {
  static show(BuildContext context, onPressed()) {
    return showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
            return WillPopScope(
              onWillPop: () async {
                return false;
              },
              child: Container(
                color: Colors.transparent,
                child: Stack(
                  children: [
                    Positioned(
                        top: 0,
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: Center(
                          child: SizedBox(
                            width: 60,
                            child: BarMusicLoading(),
                          ),
                        )),
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      height: MediaQuery.of(context).size.height * .5,
                      child: Center(
                        child: OutlinedButton(
                            style: ButtonStyle(
                              shape: MaterialStateProperty.all(StadiumBorder()),
                              minimumSize: MaterialStateProperty.all(<PERSON><PERSON>(120, 40)),
                              side: MaterialStateProperty.all(BorderSide(color: Theme.of(context).primaryColor)),
                            ),
                            onPressed: onPressed,
                            child: Text(
                              '取消',
                              style: TextStyle(fontSize: 20),
                            )),
                      ),
                    )
                  ],
                ),
              ),
            );
          });
        });
  }
}
