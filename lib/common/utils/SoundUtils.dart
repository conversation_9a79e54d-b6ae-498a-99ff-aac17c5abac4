import 'dart:async';

import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/services.dart';

///扫码工具类
class SoundUtils {
  static AudioPlayer? audioPlayer;
  static List<String> soundsList = [];
  static const String SUCCESS_OK = 'new_success.mp3'; //成功
  static const String ERROR_NOT_READ = 'warning.mp3'; //不识别条码
  static const String ERROR_FAILURE = 'operatefailure.wav';
  static const String ERROR_REPEAT = 'cfsm.mp3'; // 重复扫码
  static const String ERROR_NOT_NO = 'warning.mp3';
  static const String CHOOSE_BOX_TYPE = 'choose_box_type.mp3'; // 请选择格口类型
  static const String CHOOSE_CABINET = 'cabinetselect.mp3';
  static const String VIRTUAL_NUMBER = 'xnh.mp3';
  static const String INPUT_MOBILE = 'inputmobile.mp3'; //请输入手机号
  static const String INPUT_ALL_MOBILE = 'inputallmobile.mp3'; //请输入手机号
  static const String CHOOSE_PHONE = 'choosephone.mp3';
  static const String INBOUND_SUCCESS = 'inboundsuccess.mp3';
  static const String BATCH_OPEN = 'batchopne.mp3';
  static const String VIP = 'vip.wav';
  static const String ADMINISTRATOR_REVIEW = 'administrator_review.wav';
  static const String REAL_NAME_AUTHENTICATION = 'real_name_authentication.wav';
  static const String CHOOSE_POINT = 'choose_point.wav';
  static const String SKIP_SUCCESS = 'skip_success.wav';
  static const String BATCH_FINISH = 'batch_finish.wav';
  static const String PUT_IN_BOX = 'put_in_box.wav';

  static const Map<int, String> WAYBILL_TYPE = {
    1: 'dfj.mp3', // 到付件`
    2: 'cod.wav', // 货款件`
    3: 'ljj.wav', // 拦截件`
    4: 'intercept.mp3', // 精准件`
    5: 'isprivate.mp3', // 隐私件`
    6: 'vip.mp3', // 精准件`
    7: 'fresh.mp3', // 生鲜件`
    8: 'dlj.mp3', // 电联件`
    9: 'psj.mp3', // 派送件``
    10: 'psj.mp3', // 圆准达派送件``
    11: 'warning.mp3', // 异常件
  };
  static const Map<String, String> BRAND_SOUND = {
    'ZTO': 'zto.wav',
    'ANE': 'ane.wav',
    'YTO': 'yto.wav',
    'STO': 'sto.wav',
    'EMS': 'ems.wav',
    'YUNDA': 'yunda.wav',
    'TTKDEX': 'ttkdex',
    'HTKY': 'htky.wav',
    'JT': 'jt.wav',
    'UC56': 'uc56.wav',
    'FW': 'fw.mp3',
    'SF': 'sf.wav',
    'POSTB': 'postb.wav',
    'TM': 'tm.wav',
    'JD': 'jd.wav',
    'SNWL': 'snwl.wav',
    'DBKD': 'dbkd.wav',
    'CAINIAO': '',
    'UNKNOW': 'new_success.mp3',
  };
  static const Map<String, String> YZ_CHANNEL = {
    'YZ_MM': 'mmyz.wav',
    'YZ_TX': 'tx.wav',
    'YZ_PDD': 'ddyz.wav',
    'YZ_YDCS': 'ydcs.wav',
    'YZ_MZ': 'mz.mp3',
    'YZ_CN': 'cn.mp3',
  };
  static const String XKH = 'xkh.mp3'; // 新客户
  static const String ADD_CABINET = 'zjrg.mp3'; // 追加入柜
  static const String SCAN_PHONE = 'phone.mp3'; // 请扫描手机号
  static const String CHOOSE_COMPANY = 'choosecompany.mp3'; //选择快递公司

  static const String CHOOSE_OCR_TYPE = 'ocrtype.mp3'; //选择OCR识别引擎
  static const String CHOOSE_OCR_OK = 'qhcg.mp3'; //OCR识别引擎切换成功
  static const String PHONE_SUCCESS = 'scanphonesuccess.mp3'; // 手机号扫描成功

  static Future<ByteData> loadAsset(String name) async {
    return await rootBundle.load('static/sounds/$name');
  }

  /// 语音队列
  static audioPushFn(String name) async {
    if (name != '') {
      SoundUtils.soundsList.add(name);
      if (SoundUtils.soundsList.length == 1) {
        SoundUtils.audioPlayItem();
      }
    }
  }

  /// 语音播报
  static audioPlayFn(String name) async {
    if (audioPlayer == null) {
      SoundUtils.initSounds();
    }
    try {
      if (name != '') {
        /// 所有大写转成小写
        name = name.toLowerCase();
        String soundPath = '$name';
        AssetSource url = AssetSource(soundPath);
        audioPlayer?.play(url, volume: 1);
      } else {
        removeItem();
        SoundUtils.audioPlayItem();
      }
    } catch (e) {
      print(e);

      /// 异常后也将当前删除
      removeItem();
      SoundUtils.audioPlayItem();
    }
  }

  static AudioContext _getAudioContext() {
    return AudioContext(
      android: AudioContextAndroid(
        isSpeakerphoneOn: false,
        stayAwake: true,
        contentType: AndroidContentType.music,
        usageType: AndroidUsageType.media,
        audioFocus: AndroidAudioFocus.gain,
      ),
      iOS: AudioContextIOS(
        // defaultToSpeaker: false,
        category: AVAudioSessionCategory.playback,
        // options: [AVAudioSessionOptions.mixWithOthers] +
        //     [AVAudioSessionOptions.allowAirPlay] +
        //     [AVAudioSessionOptions.allowBluetooth] +
        //     [AVAudioSessionOptions.allowBluetoothA2DP]
      ),
    );
  }

  /// 监听语音播报
  static initSounds({bool isInit = false}) {
    if (audioPlayer == null || isInit) {
      // final AudioContext audioContext = AudioContext(
      //   iOS: AudioContextIOS(
      //     defaultToSpeaker: true,
      //     category: AVAudioSessionCategory.ambient,
      //     options: [
      //       AVAudioSessionOptions.defaultToSpeaker,
      //       AVAudioSessionOptions.mixWithOthers,
      //     ],
      //   ),
      //   android: AudioContextAndroid(
      //     isSpeakerphoneOn: true,
      //     stayAwake: true,
      //     contentType: AndroidContentType.sonification,
      //     usageType: AndroidUsageType.assistanceSonification,
      //     audioFocus: AndroidAudioFocus.none,
      //   ),
      // );
      // AudioPlayer.global.setGlobalAudioContext(_getAudioContext());
      audioPlayer = AudioPlayer();
      audioPlayer?.setAudioContext(_getAudioContext());
    }
    // audioPlayer?.onPlayerStateChanged.listen((status) {
    //   if (status == PlayerState.completed || status == PlayerState.stopped) {
    //     SoundUtils.audioPlayItem();
    //   }
    // });
    audioPlayer?.onPlayerComplete.listen((event) {
      removeItem();
      SoundUtils.audioPlayItem();
    });
  }

  static audioPlayItem() {
    if (SoundUtils.soundsList.length > 0) {
      SoundUtils.audioPlayFn(SoundUtils.soundsList[0]);
    }
  }

  static removeItem() {
    if (SoundUtils.soundsList.length > 0) {
      SoundUtils.soundsList.removeAt(0);
    }
  }
}
