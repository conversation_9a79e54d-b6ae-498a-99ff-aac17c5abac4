import 'package:cabinet_flutter_app/common/entitys/mqtt_config_entity.dart';
import 'package:mqtt5_client/mqtt5_client.dart';
import 'package:mqtt5_client/mqtt5_server_client.dart';
import 'package:uuid/uuid.dart';

class MqttUtils {
  static Future<MqttServerClient> init(MqttConfigEntity mqttConfig) async {
    MqttServerClient client = MqttServerClient.withPort(
        mqttConfig.broker, "C_${mqttConfig.username}_${const Uuid().v4().substring(0, 8)}", int.parse(mqttConfig.port));
    client.logging(on: true);
    // 开启重连
    client.autoReconnect = true;
    // 重连后自动重新订阅
    client.resubscribeOnAutoReconnect = true;
    // // 常用回调监听
    // client.onConnected = () {
    //   print('mqtt Connected');
    // };
    // client.onDisconnected = () {
    //   print('mqtt Disconnected');
    // };
    // client.onSubscribed = (SubscribeCallback? cb) {
    //   print('mqtt Subscribed');
    // } as SubscribeCallback?;
    // client.onSubscribeFail = (SubscribeCallback? cb) {
    //   print('mqtt Subscribed');
    // } as SubscribeCallback?;
    // client.pongCallback = () {
    //   print('mqtt Ping response client callback invoked');
    // };

    final connMessage = MqttConnectMessage()
        .authenticateAs(mqttConfig.username, mqttConfig.password)
        .keepAliveFor(30)
        .withWillTopic('willtopic')
        .startClean()
        .withWillQos(MqttQos.atMostOnce);
    client.connectionMessage = connMessage;
    try {
      await client.connect();
    } catch (e) {
      print('Exception: $e');
      client.disconnect();
    }
    return client;
  }
}
