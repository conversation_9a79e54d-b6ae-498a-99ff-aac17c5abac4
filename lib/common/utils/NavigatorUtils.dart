import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/customer_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/gp_user_list_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/notify_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/package_view_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/shop_cabinet_location_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/shop_courier_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/trade_record_entity.dart';
import 'package:cabinet_flutter_app/page/BrandBindPage.dart';
import 'package:cabinet_flutter_app/page/HomePage.dart';
import 'package:cabinet_flutter_app/page/NetworkCheck.dart';
import 'package:cabinet_flutter_app/page/RequestSetting.dart';
import 'package:cabinet_flutter_app/page/data/EmptyContainerPage.dart';
import 'package:cabinet_flutter_app/page/data/EmptyLookupPage.dart';
import 'package:cabinet_flutter_app/page/data/OrderAbnormalPage.dart';
import 'package:cabinet_flutter_app/page/data/PackageDetailPage.dart';
import 'package:cabinet_flutter_app/page/data/PackageListPage.dart';
import 'package:cabinet_flutter_app/page/data/PackageSearchPage.dart';
import 'package:cabinet_flutter_app/page/finance/AccountBindPage.dart';
import 'package:cabinet_flutter_app/page/finance/CapitalFlowDetailListPage.dart';
import 'package:cabinet_flutter_app/page/finance/CapitalFlowDetailPage.dart';
import 'package:cabinet_flutter_app/page/finance/CapitalFlowPage.dart';
import 'package:cabinet_flutter_app/page/finance/CashOutPage.dart';
import 'package:cabinet_flutter_app/page/finance/CashOutRecordPage.dart';
import 'package:cabinet_flutter_app/page/finance/PayForGetPwdPage.dart';
import 'package:cabinet_flutter_app/page/finance/PayModifyPwdPage.dart';
import 'package:cabinet_flutter_app/page/finance/PayResultPage.dart';
import 'package:cabinet_flutter_app/page/finance/PaySetPwdPage.dart';
import 'package:cabinet_flutter_app/page/finance/PaymentSettingPage.dart';
import 'package:cabinet_flutter_app/page/finance/RechargePage.dart';
import 'package:cabinet_flutter_app/page/finance/WalletPage.dart';
import 'package:cabinet_flutter_app/page/index/station/CabinetLocationPage.dart';
import 'package:cabinet_flutter_app/page/index/station/CabinetManagerPage.dart';
import 'package:cabinet_flutter_app/page/index/CabinetPackagePage.dart';
import 'package:cabinet_flutter_app/page/index/station/CabinetTakeOutPage.dart';
import 'package:cabinet_flutter_app/page/index/CustomerDetailPage.dart';
import 'package:cabinet_flutter_app/page/index/CustomerListPage.dart';
import 'package:cabinet_flutter_app/page/index/DataSummeryPage.dart';
import 'package:cabinet_flutter_app/page/index/InterceptListPage.dart';
import 'package:cabinet_flutter_app/page/index/InterceptDetailPage.dart';
import 'package:cabinet_flutter_app/page/index/AddInterceptPage.dart';
import 'package:cabinet_flutter_app/page/index/shop/CheckCodePage.dart';
import 'package:cabinet_flutter_app/page/my/AutoSignPage.dart';
import 'package:cabinet_flutter_app/page/my/ChangeRealNamePage.dart';
import 'package:cabinet_flutter_app/page/my/CoMatchingBindPage.dart';
import 'package:cabinet_flutter_app/page/my/CoMatchingDetailPage.dart';
import 'package:cabinet_flutter_app/page/my/CoMatchingPage.dart';
import 'package:cabinet_flutter_app/page/my/CourierShopDetailPage.dart';
import 'package:cabinet_flutter_app/page/my/CourierShopPage.dart';
import 'package:cabinet_flutter_app/page/my/SwitchUsersPage.dart';
import 'package:cabinet_flutter_app/page/my/UserListBindPage.dart';
import 'package:cabinet_flutter_app/page/my/UserListPage.dart';
import 'package:cabinet_flutter_app/page/reservation/CabinetBoxRulesPage.dart';
import 'package:cabinet_flutter_app/page/reservation/CabinetBoxUseDetail.dart';
import 'package:cabinet_flutter_app/page/scan/shop/InboundPda.dart';
import 'package:cabinet_flutter_app/page/scan/shop/OutboundPda.dart';
import 'package:cabinet_flutter_app/page/scan/station/EntryScan.dart';
import 'package:cabinet_flutter_app/page/index/PhotoUpload.dart';
import 'package:cabinet_flutter_app/page/scan/BatchEntryScanCabinet.dart';
import 'package:cabinet_flutter_app/page/scan/BatchEntryScanCabinetPda.dart';
import 'package:cabinet_flutter_app/page/index/shop/DeliveryPage.dart';
import 'package:cabinet_flutter_app/page/scan/shop/EntryScanCabinet.dart';
import 'package:cabinet_flutter_app/page/scan/shop/EntryScanCabinetPda.dart';
import 'package:cabinet_flutter_app/page/scan/shop/Inbound.dart';
import 'package:cabinet_flutter_app/page/scan/shop/Outbound.dart';
import 'package:cabinet_flutter_app/page/index/shop/ShopCabinetListPage.dart';
import 'package:cabinet_flutter_app/page/index/shop/UnbindCabinetPage.dart';
import 'package:cabinet_flutter_app/page/login/ChoiceRolePage.dart';
import 'package:cabinet_flutter_app/page/login/ForgotPwPage.dart';
import 'package:cabinet_flutter_app/page/login/LoginPageNew.dart';
import 'package:cabinet_flutter_app/page/login/ModifyPwdPage.dart';
import 'package:cabinet_flutter_app/page/login/RealNamePage.dart';
import 'package:cabinet_flutter_app/page/login/RegisterPage.dart';
import 'package:cabinet_flutter_app/page/my/BrandsManagePage.dart';
import 'package:cabinet_flutter_app/page/my/ContactPage.dart';
import 'package:cabinet_flutter_app/page/my/DownloadAppPage.dart';
import 'package:cabinet_flutter_app/page/my/MyRealNamePage.dart';
import 'package:cabinet_flutter_app/page/my/SettingPage.dart';
import 'package:cabinet_flutter_app/page/my/TeamAddPage.dart';
import 'package:cabinet_flutter_app/page/my/TeamMangerPage.dart';
import 'package:cabinet_flutter_app/page/notify/NoticeDetailPage.dart';
import 'package:cabinet_flutter_app/page/notify/NoticeListPage.dart';
import 'package:cabinet_flutter_app/page/scan/CabinetBindPage.dart';
import 'package:cabinet_flutter_app/page/scan/station/EntryScanPda.dart';
import 'package:cabinet_flutter_app/widget/PhotoGalleryWidget.dart';
import 'package:flutter/cupertino.dart';
import 'package:cabinet_flutter_app/page/reservation/CabinetBoxUseCreate.dart';
import 'package:cabinet_flutter_app/page/reservation/CabinetBoxUsePage.dart';
import 'package:cabinet_flutter_app/page/my/FeedbackPage.dart';
import 'package:cabinet_flutter_app/page/my/MyFeedbackPage.dart';
import 'package:cabinet_flutter_app/page/index/BusinessStatisticsPage.dart';
import 'package:flutter/material.dart';

class NavigatorUtils {
  ///替换
  static pushReplacementNamed(BuildContext context, String routeName) {
    Navigator.pushReplacementNamed(context, routeName);
  }

  ///切换无参数页面
  static pushNamed(BuildContext context, String routeName) {
    Navigator.pushNamed(context, routeName);
  }

  ///主页
  static goHome(BuildContext context) {
    Navigator.pushReplacementNamed(context, HomePage.sName);
  }

  ///主页
  static replaceHome(BuildContext context) {
    // Navigator.pushNamedAndRemoveUntil(context, HomePage.sName, (route) => route == null);
    // Navigator.popUntil(context, (route) => false);
    Navigator.popUntil(context, ModalRoute.withName(HomePage.sName));
  }

  ///登录页
  static goLoginNew(BuildContext context) {
    Navigator.pushNamedAndRemoveUntil(context, LoginPageNew.sName, (route) => route == null);
  }

  // ///登录页
  // static goLogin(BuildContext context) {
  //   print('$ModalRoute');
  //   Navigator.of(context).pushNamedAndRemoveUntil(CheckPhonePage.sName, ModalRoute.withName(CheckPhonePage.sName));
  // }

  /// 选择角色类型
  static goChoicePolePage(BuildContext context, String phone) {
    Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new ChoiceRolePage(phone), settings: RouteSettings(name: 'choicePolePage')));
  }

  /// 快递公司绑定界面
  static goBrandBind(BuildContext context) {
    Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new BrandBindPage(), settings: RouteSettings(name: 'brandBindPage')));
  }

  /// 快递公司绑定界面
  static goBrandsManage(BuildContext context) {
    Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new BrandsManagePage(), settings: RouteSettings(name: 'brandsManagePage')));
  }

  /// 驿站签收设置界面
  static goAutoSign(BuildContext context) {
    Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new AutoSignPage(), settings: RouteSettings(name: 'autoSignPage')));
  }

  /// 共配账号页面
  static goCoMatching(BuildContext context, String gpUserId) {
    Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new CoMatchingPage(gpUserId), settings: RouteSettings(name: 'coMatchingPage')));
  }

  /// 共配账号详情页面
  static goCoMatchingDetail(BuildContext context, GpUserListEntity gpUserListEntity) {
    Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new CoMatchingDetailPage(gpUserListEntity),
            settings: RouteSettings(name: 'coMatchingDetailPage')));
  }

  /// 绑定共配页面
  static goCoMatchingBind(BuildContext context) {
    Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new CoMatchingBindPage(), settings: RouteSettings(name: 'coMatchingBindPage')));
  }

  /// 驿站合作界面
  static goCourierShop(BuildContext context) {
    Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new CourierShopPage(), settings: RouteSettings(name: 'courierShopPage')));
  }

  /// 驿站合作详情界面
  static goCourierShopDetailPage(BuildContext context, ShopCourierEntity? shopCourierEntity) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new CourierShopDetailPage(shopCourierEntity),
            settings: RouteSettings(name: 'customerDetailPage')));
  }

  /// 快递公司绑定界面
  static goCabinetBindPage(BuildContext context, {ScanAction scanAction = ScanAction.BINDCABINET}) async {
    return await Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new CabinetBindPage(scanAction: scanAction),
            settings: RouteSettings(name: 'cabinetBindPage')));
  }

  /// 取件码管理界面
  static goCheckCodePage(BuildContext context) async {
    return await Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new CheckCodePage(), settings: RouteSettings(name: 'CheckCodePage')));
  }

  /// 柜机选择
  // static goCabinetSelectPage(BuildContext context, String? cabinetLocationCode) async {
  //   return await Navigator.push(
  //       context,
  //       new CupertinoPageRoute(
  //           builder: (context) => new CabinetSelectPage(cabinetLocationCode),
  //           settings: RouteSettings(name: 'cabinetSelectPage')));
  // }

  /// 门店未绑定点位
  static goUnbindCabinetPage(BuildContext context) {
    Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new UnbindCabinetPage(), settings: RouteSettings(name: 'unbindCabinetPage')));
  }

  ///获取门店点位列表
  static goShopCabinetListPage(BuildContext context) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new ShopCabinetListPage(), settings: RouteSettings(name: 'shopCabinetListPage')));
  }

  ///门店点位管理
  static goDeliveryPage(BuildContext context, code, {bool isDelivery = false}) {
    print('goDeliveryPage: $code, $isDelivery');
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new DeliveryPage(code, isDelivery), settings: RouteSettings(name: 'deliveryPage')));
  }

  ///门店入柜
  static goEntryScanCabinet(BuildContext context, CabinetItemEntity cabinetItem, String cabinetLocationId,
      CabinetBoxItem boxList, bool isDelivery,
      {String hostIndex = ''}) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new EntryScanCabinet(cabinetItem, cabinetLocationId, boxList, isDelivery, hostIndex),
            settings: RouteSettings(name: 'scanInCabinet')));
  }

  /// 门店批量入柜
  static goBatchEntryScanCabinet(BuildContext context, String cabinetLocationCode, String cabinetName,
      {String hostIndex = '', bool isBlind = false}) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new BatchEntryScanCabinet(cabinetLocationCode, cabinetName, hostIndex, isBlind),
            settings: RouteSettings(name: 'BatchEntryScanCabinet')));
  }

  ///门店入柜-pda
  static goEntryScanCabinetPda(BuildContext context, CabinetItemEntity cabinetItem, String cabinetLocationId,
      CabinetBoxItem boxItem, bool isDelivery,
      {String hostIndex = ''}) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) =>
                new EntryScanCabinetPda(cabinetItem, cabinetLocationId, boxItem, isDelivery, hostIndex),
            settings: RouteSettings(name: 'scanInCabinetPda')));
  }

  ///门店批量入柜-pda
  static goBatchEntryScanCabinetPda(BuildContext context, String cabinetLocationCode, String cabinetName,
      {String hostIndex = '', bool isBlind = false}) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new BatchEntryScanCabinetPda(cabinetLocationCode, cabinetName, hostIndex, isBlind),
            settings: RouteSettings(name: 'BatchEntryScanCabinetPda')));
  }

  ///请求地址设置
  static goRequestSetting(BuildContext context) {
    Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new RequestSetting(), settings: RouteSettings(name: 'requestSetting')));
  }

  ///网络检测
  static goNetworkCheck(BuildContext context) {
    Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new NetworkCheck(), settings: RouteSettings(name: 'networkCheck')));
  }

  /// 跳转客户信息界面
  static goCustomerDetailPage(BuildContext context, CustomerEntity? customerEntity) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new CustomerDetailPage(customerEntity),
            settings: RouteSettings(name: 'customerDetailPage')));
  }

  /// 数据统计
  static goSettingPage(BuildContext context) {
    return Navigator.push(context,
        new CupertinoPageRoute(builder: (context) => new SettingPage(), settings: RouteSettings(name: 'settingPage')));
  }

  /// 跳转客户管理列表界面
  static goCustomerListPage(BuildContext context) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new CustomerListPage(), settings: RouteSettings(name: 'customerListPage')));
  }

  ///拦截单号列表
  static goInterceptListPage(BuildContext context) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new InterceptListPage(), settings: RouteSettings(name: 'interceptListPage')));
  }

  // ///拦截单号详情
  // static goInterceptDetailPage(BuildContext context, dynamic interceptData) {
  //   return Navigator.push(
  //       context,
  //       new CupertinoPageRoute(
  //           builder: (context) => new InterceptDetailPage(interceptData: interceptData), 
  //           settings: RouteSettings(name: 'interceptDetailPage')));
  // }

  ///添加拦截件
  static goAddInterceptPage(BuildContext context, [dynamic interceptData]) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new AddInterceptPage(interceptData: interceptData), 
            settings: RouteSettings(name: 'addInterceptPage')));
  }

  ///软件分享
  static goDownloadAppPage(BuildContext context) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new DownloadAppPage(), settings: RouteSettings(name: 'downloadAppPage')));
  }

  ///团队管理
  static goTeamMangerPage(BuildContext context) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new TeamMangerPage(), settings: RouteSettings(name: 'teamMangerPage')));
  }

  ///团队管理加入
  static goTeamAddPage(BuildContext context) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new TeamAddPage(), settings: RouteSettings(name: 'teamMangerPage')));
  }

  /// 忘记密码
  static goForGetPw(BuildContext context, String mobile) {
    Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new ForgotPwPage(mobile), settings: RouteSettings(name: 'forgetPw')));
  }

  ///实名界面
  static goRealName(BuildContext context, int userType) {
    Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new RealNamePage(userType), settings: RouteSettings(name: 'realName')));
  }

  ///注册
  static goRegister(BuildContext context, String mobile, int userType) async {
    return await Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => RegisterPage(mobile, userType), settings: RouteSettings(name: 'register')));
  }

  ///扫描入柜
  static goEntryScan(BuildContext context, String cabinetLocationCode, {String hostIndex = ''}) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new EntryScan(cabinetLocationCode, hostIndex),
            settings: RouteSettings(name: 'entryScan')));
  }

  ///扫描入柜
  static goEntryScanPda(BuildContext context, String cabinetLocationCode, {String hostIndex = ''}) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new EntryScanPda(cabinetLocationCode, hostIndex),
            settings: RouteSettings(name: 'entryScanPda')));
  }

  ///入仓扫描
  static goInboundPage(BuildContext context, String type, bool isDelivery) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new InboundPage(type, isDelivery), settings: RouteSettings(name: 'inboundPage')));
  }

  ///入仓扫描-pda
  static goInboundPdaPage(BuildContext context, String type, bool isDelivery) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new InboundPdaPage(type, isDelivery),
            settings: RouteSettings(name: 'inboundPdaPage')));
  }

  ///出仓扫描
  static goOutboundPage(BuildContext context) {
    return Navigator.push(context,
        new CupertinoPageRoute(builder: (context) => new Outbound(), settings: RouteSettings(name: 'entryScanOut')));
  }

  ///出仓扫描-pda
  static goOutboundPda(BuildContext context) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new OutboundPda(), settings: RouteSettings(name: 'entryScanOutPda')));
  }

  ///消息列表详情
  static goNoticeListPage(BuildContext context, int? type) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new NoticeListPage(type), settings: RouteSettings(name: 'noticeListPage')));
  }

  ///消息详情
  static goNoticeDetailPage(BuildContext context, NotifyEntity notice) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new NoticeDetailPage(notice), settings: RouteSettings(name: 'noticeDetailPage')));
  }

  ///点位管理
  static goCabinetMangerPage(BuildContext context, {required String code, String hostIndex = ''}) {
    // return Navigator.pushReplacement(context, );
    return Navigator.pushReplacement(
        context,
        new CupertinoPageRoute(
            builder: (context) => new CabinetManagerPage(code, hostIndex),
            settings: RouteSettings(name: 'cabinetManagerPage')));
  }

  ///点位管理
  static goCabinetMangerPage2(BuildContext context, {required String code, String hostIndex = ''}) {
    // return Navigator.pushReplacement(context, );
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new CabinetManagerPage(code, hostIndex),
            settings: RouteSettings(name: 'cabinetManagerPage')));
  }

  /// 滞留件待取件列表
  static goCabinetPackagePage(BuildContext context, CabinetEntity cabinet, PackageType type,
      {int overDays = 7, String hostIndex = ''}) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new CabinetPackagePage(cabinet, type, overDays, hostIndex),
            settings: RouteSettings(name: 'cabinetPackagePage')));
  }

  /// 寄件待取出
  static goCabinetTakeOutPage(BuildContext context, CabinetEntity cabinet, PackageType type, {String hostIndex = ''}) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new CabinetTakeOutPage(cabinet, type, hostIndex),
            settings: RouteSettings(name: 'cabinetTakeOutPage')));
  }

  /// 滞留件待取件列表
  static goCabinetLocationPage(BuildContext context, String cabinetLocationCode) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new CabinetLocationPage(cabinetLocationCode),
            settings: RouteSettings(name: 'cabinetLocationPage')));
  }

  /// 包裹详情
  static goPackageDetailPage(BuildContext context, PackageViewEntity package,
      {bool showSync = true, bool showReNotice = true, bool showBack = true, bool showEditPhone = true}) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new PackageDetailPage(package, showSync, showReNotice, showBack, showEditPhone),
            settings: RouteSettings(name: 'PackageDetailPage')));
  }

  /// 实名认证
  static goMyRealName(BuildContext context) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new MyRealNamePage(), settings: RouteSettings(name: 'myRealNamePage')));
  }

  /// 修改实名认证
  static goChangeRealName(BuildContext context) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new ChangeRealName(), settings: RouteSettings(name: 'changeRealName')));
  }

  /// 滞留件列表 待取列表
  static goPackageListPage(BuildContext context, PackageType type, String? cabinetCode,
      {int? keepEffectDay, String? query}) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new PackageListPage(type, cabinetCode, keepEffectDay, query),
            settings: RouteSettings(name: 'packageListPage')));
  }

  static goOrderAbnormalPage(BuildContext context) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new OrderAbnormalPage(), settings: RouteSettings(name: 'orderAbnormalPage')));
  }

  /// 包裹查询
  static goPackageSearchPage(BuildContext context, {String? value, bool autoFocus = false}) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new PackageSearchPage(value, autoFocus),
            settings: RouteSettings(name: 'packageSearchPage')));
  }

  /// 包裹查询
  static goDataSummeryPage(BuildContext context, bool isCourier) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new DataSummeryPage(isCourier), settings: RouteSettings(name: 'dataSummeryPage')));
  }

  /// 空箱查询
  static goEmptyContainerPage(BuildContext context, CabinetBoxListPageType pageType) async {
    return await Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new EmptyContainerPage(pageType),
            settings: RouteSettings(name: 'emptyContainerPage')));
  }

  /// 空箱查询详情地图
  static goEmptyLookupPage(BuildContext context) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new EmptyLookupPage(), settings: RouteSettings(name: 'emptyLookupPage')));
  }

  /// 钱包
  static goWalletPage(BuildContext context) {
    return Navigator.push(context,
        new CupertinoPageRoute(builder: (context) => new WalletPage(), settings: RouteSettings(name: 'walletPage')));
  }

  /// 充值界面
  static goRechargePage(BuildContext context, String balanceNum) async {
    return await Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new RechargePage(balanceNum), settings: RouteSettings(name: 'rechargePage')));
  }

  /// 支付结果
  static goPayResultPage(BuildContext context, PayResult result, double money) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new PayResultPage(result, money), settings: RouteSettings(name: 'payResultPage')));
  }

  /// 提现
  static goCashOutPage(BuildContext context) {
    return Navigator.push(
            context,
            new CupertinoPageRoute(
                builder: (context) => new CashOutPage(), settings: RouteSettings(name: 'cashOutPage')))
        .then((value) => null);
  }

  /// 支付账号绑定界面
  static goAccountBindPage(BuildContext context) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new AccountBindPage(), settings: RouteSettings(name: 'accountBindPage')));
  }

  /// 资金明细
  static goCapitalFlowPage(BuildContext context) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new CapitalFlowPage(), settings: RouteSettings(name: 'capitalFlowPage')));
  }

  /// 提现明细
  static goCashOutRecordPage(BuildContext context) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new CashOutRecordPage(), settings: RouteSettings(name: 'cashOutRecordPage')));
  }

  /// 交易明细
  static goCapitalFlowDetailPage(BuildContext context, TradeRecordEntity trade) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new CapitalFlowDetailPage(trade),
            settings: RouteSettings(name: 'capitalFlowDetailPage')));
  }

  /// 交易明细详情
  static goCapitalFlowDetailListPage(BuildContext context, TradeRecordEntity trade) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new CapitalFlowDetailListPage(trade),
            settings: RouteSettings(name: 'capitalFlowDetailListPage')));
  }

  /// 支付设置
  static goPaymentSettingPage(BuildContext context) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new PaymentSettingPage(), settings: RouteSettings(name: 'paymentSettingPage')));
  }

  /// 支付设置
  static goSetPayPassword(BuildContext context) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new SetPayPasswordPage(), settings: RouteSettings(name: 'setPayPasswordPage')));
  }

  /// 修改支付密码
  static goModifyPayPwdPage(BuildContext context) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new ModifyPayPwdPage(), settings: RouteSettings(name: 'modifyPayPwdPage')));
  }

  /// 修改账号密码
  static goModifyPwdPage(BuildContext context) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new ModifyPwdPage(), settings: RouteSettings(name: 'modifyPwdPage')));
  }

  /// 忘记支付密码
  static goPayForGetPwdPage(BuildContext context) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new PayForGetPwdPage(), settings: RouteSettings(name: 'payForGetPwdPage')));
  }

  static goContactPage(BuildContext context) async {
    return await Navigator.push(context,
        new CupertinoPageRoute(builder: (context) => ContactPage(), settings: RouteSettings(name: 'contactPage')));
  }

  static goPhotoViewGalleryScreen(BuildContext context, List imgs, int index) async {
    return await Navigator.push(
      context,
      new CupertinoPageRoute(
        builder: (context) => PhotoViewGalleryScreen(imgs, index),
      ),
    );
  }

  ///图片上传
  static goPhotoUpload(BuildContext context) {
    Navigator.push(context, new CupertinoPageRoute(builder: (context) => new PhotoUpload()));
  }

  /// 点位格口使用
  static goCabinetBoxUsePage(BuildContext context, CabinetBoxUseType pageType) async {
    return await Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new CabinetBoxUsePage(pageType), settings: RouteSettings(name: 'cabinetBoxUsePage')));
  }

  /// 点位格口使用创建
  static goCabinetBoxCreatePage(BuildContext context, CabinetEntity cabinet, CabinetBoxUseType boxUseType) async {
    return await Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new CabinetBoxUseCreate(cabinet, boxUseType),
            settings: RouteSettings(name: 'cabinetBoxCreatePage')));
  }

  /// 点位格口使用规则
  static goCabinetBoxRulesPage(BuildContext context) async {
    return await Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new CabinetBoxRulesPage(), settings: RouteSettings(name: 'cabinetBoxRulesPage')));
  }

  /// 点位格口使用创建
  static goCabinetBoxUseDetail(BuildContext context, String id, CabinetBoxUseType boxType) async {
    return await Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new CabinetBoxUseDetail(id, boxType),
            settings: RouteSettings(name: 'cabinetBoxCreatePage')));
  }

  static goUserListPage(BuildContext context) {
    Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new UserListPage(), settings: RouteSettings(name: 'userListPage')));
  }

  static goUserListBindPage(BuildContext context) {
    Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new UserListBindPage(), settings: RouteSettings(name: 'userListBindPage')));
  }

  ///切换账户
  static goTSwitchUsersPage(BuildContext context) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new SwitchUsersPage(), settings: RouteSettings(name: 'switchUsersPage')));
  }

  ///意见反馈
  static goToFeedbackPage(BuildContext context) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new FeedbackPage(), settings: RouteSettings(name: 'feedbackPage')));
  }

  ///我的反馈
  static goToMyFeedbackPage(BuildContext context) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new MyFeedbackPage(), settings: RouteSettings(name: 'myFeedbackPage')));
  }

  static goBusinessStatisticsPage(BuildContext context) {
    return Navigator.push(
        context,
        new CupertinoPageRoute(
            builder: (context) => new BusinessStatisticsPage(), settings: RouteSettings(name: 'businessStatisticsPage')));
    // Navigator.push(context, MaterialPageRoute(builder: (context) => BusinessStatisticsPage()));
  }
}
