
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';

class BrandUtils {
  /// 首页是否刷新数据
  static Future<bool> needRefresh() async {
    bool isFresh = await LocalStorage.get(DefaultConfig().configs.HOME_PAGE_REFRESH) ?? false;
    return isFresh;
  }

  /// 保存首页是否刷新数据
  static saveHomePageRefresh({bool isRefresh = true}) async {
    await LocalStorage.save(DefaultConfig().configs.HOME_PAGE_REFRESH, isRefresh);
  }

  ///获取开通品牌信息
  static getBrandBindList({bool needRefresh = false}) async {
    var _brandMap = await LocalStorage.get<Map>(DefaultConfig().configs.CAN_IN_BRANDS, isPrivate: true);
    if (_brandMap == null || needRefresh) {
      _brandMap = {};
      DataResult res = await UserDao.getBrandBindList();
      if (res.result) {
        _brandMap = {};
        res.data.forEach((item) {
          if (item['allowIn'] == 1) {
            _brandMap[item['brandCode']] = DefaultConfig().configs.EXPRESS2[item['brandCode']]!;
          }
        });
        await LocalStorage.save(DefaultConfig().configs.CAN_IN_BRANDS, _brandMap, isPrivate: true);
      }
    }
    return _brandMap;
  }
}
