import 'dart:async';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/entitys/company_rules_entity.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

///扫码工具类
class ScanUtils {
  static List<CompanyRulesEntity?>? companyRules;

  /// 清空猜单规则
  static clearCompanyRules() {
    ScanUtils.companyRules = [];
  }

  /// 猜品牌
  static Future<String> getCompany(BuildContext context, String sheetNo) async {
    List<CompanyRulesEntity?>? rules = await ScanUtils.getCompanyRule();
    String company = "";
    if (rules != null && rules.length > 0) {
      /// 本地猜单
      company = await ScanUtils.getCompanyOnLine(sheetNo, rules);
      print('接口返回的猜单规则(本地): $company');
    }

    return company;
  }

  /// 接口获取的猜单规则猜单
  static Future<String> getCompanyOnLine(String sheetNo, List<CompanyRulesEntity?>? rules) async {
    sheetNo = sheetNo.toUpperCase();

    String company = "";
    if (rules == null) {
      return '';
    }
    for (int i = 0; i < rules.length; i++) {
      CompanyRulesEntity? item = rules[i];
      if (item == null) {
        return '';
      }
      RegExp exp = RegExp(item.regx!);
      if (exp.hasMatch(sheetNo)) {
        company = item.code!;
        break;
      }
    }

    return company;
  }

  /// 获取猜单规则(本地缓存)
  static Future<List<CompanyRulesEntity?>?> getCompanyRule() async {
    // if (ScanUtils.companyRules == null) {
    companyRules = await LocalStorage.get(DefaultConfig().configs.COMPANY_RULE);
    // }
    return ScanUtils.companyRules;
  }
}
