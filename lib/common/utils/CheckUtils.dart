import 'dart:async';

import 'package:cabinet_flutter_app/common/ab/provider/task/PhotoDbProvider.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CodeRegExpDao.dart';
import 'package:cabinet_flutter_app/common/dao/PhotoDao.dart';
import 'package:cabinet_flutter_app/common/entitys/photo_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/net/HttpManager.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/utils/OssUrlUtil.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:redux/redux.dart';
import 'package:scan/scan.dart';
import 'package:package_info_plus/package_info_plus.dart';

///通用逻辑
class CheckUtils {
  /// 获取token
  static getToken() async {
    Map<String, dynamic>? authorization = await LocalStorage.getJson(DefaultConfig().configs.TOKEN_KEY);
    return authorization;
  }

  /// 设置token
  static setToken(dynamic token) async {
    await LocalStorage.save(DefaultConfig().configs.TOKEN_KEY, token);
    return token;
  }

  /// 判断是否打印取件码
  static checkPrintIsOpen() async {
    bool openPrint = false;
    var _openPrint = await LocalStorage.get(DefaultConfig().configs.OPEN_PRINT);
    if (_openPrint != null) {
      openPrint = _openPrint == 'Y';
    }
    return openPrint;
  }

  /// 判断是否是智能识别模式，读码引擎
  static checkIsAi() async {
    bool isAi = false;
    var _isAi = await LocalStorage.get(DefaultConfig().configs.IS_AI);
    if (_isAi != null) {
      isAi = _isAi;
    }
    return isAi;
  }

  /// 判断OCR引擎，白龙(百度)或者翼龙(yd)
  static checkOcrType() async {
    int ocrType = Scan.OCR_YI_LONG;
    var _ocrType = await LocalStorage.get(DefaultConfig().configs.OCR_TYPE);
    if (_ocrType != null) {
      ocrType = _ocrType;
    }
    return ocrType;
  }

  /// 判断急速OCR引擎，翼龙(yd) 麒麟
  static checkOcrJSType() async {
    int ocrType = Scan.OCR_KYLIN;
    var _ocrType = await LocalStorage.get(DefaultConfig().configs.OCR_JS_TYPE);
    if (_ocrType != null) {
      ocrType = _ocrType;
    }
    return ocrType;
  }

  /// 判断是否支持外送
  static checkIsSupportWs() async {
    // 支持外送的开关
    bool isSupportWs = false;
    var _isSupportWs = await LocalStorage.get(DefaultConfig().configs.IS_SUPPORT_WS);
    if (_isSupportWs != null) {
      isSupportWs = _isSupportWs == 'Y';
    }

    // 本地缓存的入库类型：外送、入库
    String inboundType = 'ZT'; // 外送、入库
    var _inboundType = await LocalStorage.get(DefaultConfig().configs.INBOUND_TYPE);
    if (_inboundType != null) {
      inboundType = isSupportWs ? _inboundType : 'ZT';
    }
    return {"isSupportWs": isSupportWs, "inboundType": inboundType};
  }

  /// 判断设备是否支持OCR
  static checkIsSupportOcr(Scan scanPlugin) async {
    bool isSupportOcr = true;
    bool _isSupportOcr = await scanPlugin.isSupportOcr();
    isSupportOcr = _isSupportOcr;
    return isSupportOcr;
  }

  /// 判断拦截按钮状态
  static checkLjIsOpen() async {
    bool checkIntercept = false;
    var isOpenLj = await LocalStorage.get(DefaultConfig().configs.IS_LJ);
    if (isOpenLj != null) {
      checkIntercept = isOpenLj == 'Y';
    }
    return checkIntercept;
  }

  /// 判断派件按钮状态
  static checkPjIsOpen() async {
    bool needPj = false;
    var _needPj = await LocalStorage.get(DefaultConfig().configs.IS_PJ);
    if (_needPj != null) {
      needPj = _needPj == 'Y';
    }
    return needPj;
  }

  /// 判断自动拍照按钮状态（入库）
  static checkPhotoIsOpen() async {
    bool isPhoto = true;
    var isOpenPhoto = await LocalStorage.get(DefaultConfig().configs.IS_PHOTO);
    if (isOpenPhoto != null) {
      isPhoto = isOpenPhoto;
    }
    return isPhoto;
  }

  /// 判断自动拍照按钮状态(出库)
  static checkCkPhotoIsOpen() async {
    bool isCkPhoto = true;
    var isOpenCkPhoto = await LocalStorage.get(DefaultConfig().configs.IS_CK_PHOTO);
    if (isOpenCkPhoto != null) {
      isCkPhoto = isOpenCkPhoto;
    }
    return isCkPhoto;
  }

  /// 判断打印机连接状态
  static checkPrintIsConnect(BuildContext context) async {
    // bool printConnect = false;
    // if (connection == null) {
    //   var p = await LocalStorage.getJson(DefaultConfig().configs.PRINT_DEVICE);
    //   if (p != null) {
    //     BluetoothDevice printDevice = BluetoothDevice.fromMap(p);
    //     await PrintUtils.connect(context, printDevice.address, longTips: false);
    //     printConnect = connection != null;
    //   }
    // } else {
    //   printConnect = true;
    // }
    // return printConnect;
    return false;
  }

  /// 判断送货上门是否开启
  // static checkIsDelivery() async {
  //   bool isDelivery = true;
  //   var _isDelivery = await LocalStorage.get(DefaultConfig().configs.DELIVERY_ORDER, isPrivate: true);
  //   if (_isDelivery != null) {
  //     isDelivery = _isDelivery;
  //   }
  //   return isDelivery;
  // }

  /// 判断追加入柜是否开启
  static checkAddCabinetIsOpen() async {
    bool isAddCabinet = true;
    var _isAddCabinet = await LocalStorage.get(DefaultConfig().configs.ADD_CABINET, isPrivate: true);
    if (_isAddCabinet != null) {
      isAddCabinet = _isAddCabinet;
    }
    return isAddCabinet;
  }

  static checkSynchronizedLogistics() async {
    bool isSynchronizedLogistics = true;
    var _isSynchronizedLogistics =
        await LocalStorage.get(DefaultConfig().configs.SYNCHRONIZED_LOGISTICS, isPrivate: true);
    if (_isSynchronizedLogistics != null) {
      isSynchronizedLogistics = _isSynchronizedLogistics;
    }
    return isSynchronizedLogistics;
  }

  /// 判断是否网点开启追加入柜
  static checkSiteOpenAddCabinetIsOpen() async {
    bool hasOpenAddCabinet = false;
    var _hasOpenAddCabinet = await LocalStorage.get(DefaultConfig().configs.HAS_SITE_OPEN_ADD_CABINET, isPrivate: true);
    print('hasOpenAddCabinet: $_hasOpenAddCabinet');
    if (_hasOpenAddCabinet != null) {
      hasOpenAddCabinet = _hasOpenAddCabinet == 1 ? true : false;
    }
    return hasOpenAddCabinet;
  }

  static checkChangeBrand() async {
    bool hasChangeBrand = false;
    var _hasChangeBrand = await LocalStorage.get(DefaultConfig().configs.HAS_CHANGE_BRAND, isPrivate: true);
    if (_hasChangeBrand != null) {
      hasChangeBrand = _hasChangeBrand == 1 ? true : false;
    }
    return hasChangeBrand;
  }

  /// 判断是否开启单客户提醒
  static checkSingleCustomerListNotice() async {
    bool hasOpenSingleCustomerNotice = false;
    var _hasOpenSingleCustomerNotice =
        await LocalStorage.get(DefaultConfig().configs.HAS_OPEN_SINGEL_CUSTOMER_NOTICE, isPrivate: true);
    if (_hasOpenSingleCustomerNotice != null) {
      hasOpenSingleCustomerNotice = _hasOpenSingleCustomerNotice;
    }
    return hasOpenSingleCustomerNotice;
  }

  /// 判断是否开启到派
  static checkDPIsOpen() async {
    bool checkDP = false;
    var _checkDP = await LocalStorage.get(DefaultConfig().configs.IS_DP, isPrivate: true);
    if (_checkDP != null) {
      checkDP = _checkDP;
    }
    return checkDP;
  }

  /// 判断是否开启Ocr
  static checkOCRIsOpen() async {
    bool checkOcr = true;
    var _checkOcr = await LocalStorage.get(DefaultConfig().configs.IS_OCR, isPrivate: true);
    if (_checkOcr != null) {
      checkOcr = _checkOcr;
    }
    return checkOcr;
  }

  static checkFastIntoIsOpen() async {
    bool hasOpen = false;
    var _hasOpen = await LocalStorage.get(DefaultConfig().configs.HAS_OPEN_FAST_INTO, isPrivate: true);
    if (_hasOpen != null) {
      hasOpen = _hasOpen;
    }
    return hasOpen;
  }

  /// 获取当前选择的快递公司
  static getBindCompany() async {
    var brand = await LocalStorage.get(DefaultConfig().configs.CHOOSE_BRAND, isPrivate: true);
    if (brand == null) {
      brand = '';
    }
    return brand;
  }

  /// 根据类型获取bucket
  static checkBucketName(String type) {
    return OssUrlUtil.textMap_bucket[type];
  }

  static checkPhotoUrl(String photoUrl, String type) {
    return OssUrlUtil.textMap[type] + photoUrl;
  }

  /// 图片上传（一步）
  static checkPhotoUpload(String path, String sheetNo, String brandCode, String typeId, String issueStatus,
    {String type = 'rk'}) async {
    String photoUrl = await OssUrlUtil.contactName(sheetNo, typeId, issueStatus, type: type);
    String bucket = CheckUtils.checkBucketName(type);
    String url = '';
    if (path != '' && photoUrl != '' && bucket != '') {
      const timeout = const Duration(milliseconds: 800);
      Timer(timeout, () {
        scanPlugin.upload(path, photoUrl, brandCode, bucket, type: type);
      });
      url = CheckUtils.checkPhotoUrl(photoUrl, type);
    }
    return url;
  }

  /// 图片上传
  /// path: 图片本地路径
  /// name: 图片名称
  /// bucket：存储桶
  static checkPhotoUploadTwo(String path, String name, String company, String bucket, String type) {
    if (path != '' && name != '' && bucket != '') {
      const timeout = const Duration(milliseconds: 800);
      Timer(timeout, () {
        scanPlugin.upload(path, name, company, bucket, type: type);
      });
    }
  }

  static savePhotoLocal(String path, String fileName, String company, String status, String msg, String type) async {
    if (path != '' && fileName != '' && status != '') {
      PhotoEntity photoEntity = PhotoDbDbProvider.buildPhoto(path, fileName, company, status, msg, type);
      await PhotoDao.savePhoto(photoEntity);
    }
  }

  /// 获取字符串后四位
  /// * param String str
  /// * @return String
  static getEnd4Str(String str) {
    if (str.length > 4) {
      return str.substring(str.length - 4);
    }
    return '';
  }

  /// 判断是否为null
  static isNotNull(dynamic value) {
    if (value == null) {
      return false;
    }
    if (value is num) {
      value = value.toString();
    }
    return value.isNotEmpty;
  }

  /// 判断是否为null
  static isNum(dynamic value) {
    if (value is num) {
      value = value.toString();
    }
    print('$value');
    return value.isNotEmpty;
  }

  static Future<bool> checkUserHasReal() async {
    /// todo
    var res = await LocalStorage.getJson(DefaultConfig().configs.USER_INFO);
    UserEntity? userInfo = UserEntity.fromJson(res);
    if (CheckUtils.isNotNull(userInfo.hasReal)) {
      return userInfo.hasReal == 1;
    }
    return false;
  }

  /// 入柜权限审核
  static Future<String> checkUserHasInStorePermission() async {
    /// todo
    return 'PASS';
  }

  /// 预约权限审核
  static Future<String> checkUserHasOrderPermission() async {
    /// todo
    return 'REJECT';
  }

  /// 租用权限审核
  static Future<String> checkUserHasRentPermission() async {
    /// todo
    return 'NEW';
  }

  static countDay(String dateTime) {
    if (CheckUtils.isNotNull(dateTime)) {
      int hours = DateTime.now().difference(DateTime.parse(dateTime)).inHours;
      return (hours / 24).toStringAsFixed(1);
    }
    return null;
  }

  /// 检查滞留件是否超过设置滞留超时天数
  static checkEffectTimeIsOverTime(String dateTime, {int overDueDay = 7}) {
    DateTime now = DateTime.now();
    DateTime keepEffectOverDueDay = DateTime.parse(dateTime).add(Duration(days: overDueDay));
    return keepEffectOverDueDay.isBefore(now);
  }

  /// 获取角色  4室外柜  6室内柜
  static getUserType(BuildContext context) {
    Store<AppState> store = StoreProvider.of(context);
    int userType = store.state.userType;
    return userType;
  }

  /// 获取角色
  static bool isCourier(BuildContext context) {
    int userType = getUserType(context);
    return userType == 4;
  }

  /// 手机号检测
  static isMobile(String mobile) {
    RegExp exp = RegExp(r'^1[3-9]\d{9}$');
    return exp.hasMatch(mobile);
  }

  static getVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String version = packageInfo.version;
    return version == '' ? '获取版本失败' : version;
  }

  static getPackageName() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String packageName = packageInfo.packageName;
    return packageName == '' ? '获取版本失败' : packageName;
  }

  /// 判断是否是隐私号码
  static isSecretWayBill(String mobile, {int isSecretWayBill = 0}) {
    return mobile.contains('*');
  }

  /// 判断是否是pda
  static isPda() async {
    var res = await LocalStorage.get(DefaultConfig().configs.IS_PDA);
    if (res != null) {
      return res == 'true';
    }
    return true;
  }

  static isOptimizeNetwork() async {
    var res = await LocalStorage.get(DefaultConfig().configs.IS_OPTIMIZE_NETWORK);
    if (res != null) {
      return res == 'true';
    }
    return true;
  }

  /// 判断是否是自定义键盘
  static isCustomerKeyboard() async {
    var res = await LocalStorage.get(DefaultConfig().configs.IS_CUSTOMER_KEYBOARD);
    if (res != null) {
      return res == 'true';
    }
    return false;
  }

  /// 保存是否pda
  static saveIsPda(bool isPda, {bool isForce = false}) async {
    if (isForce) {
      LocalStorage.save(DefaultConfig().configs.IS_PDA, isPda.toString());
      return;
    }
    var res = await LocalStorage.get(DefaultConfig().configs.IS_PDA);
    if (res == null) {
      LocalStorage.save(DefaultConfig().configs.IS_PDA, isPda.toString());
    }
  }

  static saveIsOptimizeNetwork(bool isOptimizeNetwork, {bool isForce = false}) async {
    await HttpManager.clearClient();
    if (isForce) {
      LocalStorage.save(DefaultConfig().configs.IS_OPTIMIZE_NETWORK, isOptimizeNetwork.toString());
      return;
    }
    var res = await LocalStorage.get(DefaultConfig().configs.IS_OPTIMIZE_NETWORK);
    if (res == null) {
      LocalStorage.save(DefaultConfig().configs.IS_OPTIMIZE_NETWORK, isOptimizeNetwork.toString());
    }
  }

  static addUserList(_userName, _password) async {
    var data = await LocalStorage.get<Map>(DefaultConfig().configs.USER_LIST);
    List<dynamic> userList = [];
    if (data != null) {
      userList = data;
    }
    var isHave = false;
    for (var item in userList) {
      if (item['loginName'] == _userName) {
        isHave = true;
        item['password'] = _password;
      }
    }
    if (!isHave) {
      userList.add({'loginName': _userName, 'password': _password});
    }
    await LocalStorage.save(DefaultConfig().configs.USER_LIST, userList);
  }

  /// 保存是否自定义键盘
  static saveIsCustomerKeyborad(bool isPda) async {
    LocalStorage.save(DefaultConfig().configs.IS_CUSTOMER_KEYBOARD, isPda.toString());
  }

  static Future<void> checkPermission(Permission permission, BuildContext context, String dialogContent) async {
    final status = await permission.status;
    if (status.isDenied) {
      showDialog(
        context: context,
        barrierDismissible: false, // user must tap button!
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text('温馨提示'),
            content: SingleChildScrollView(
              child: ListBody(
                children: <Widget>[
                  Text(dialogContent),
                ],
              ),
            ),
            actions: <Widget>[
              TextButton(
                child: Text('取消'),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
              TextButton(
                child: Text('确定'),
                onPressed: () async {
                  Navigator.of(context).pop();
                  if (Permission.manageExternalStorage == permission) {
                    permission.request();
                  } else {
                    await openAppSettings();
                    Navigator.of(context).pop();
                  }
                },
              ),
            ],
          );
        },
      );
    }
  }

  static Future<void> showPermission(List<Permission> permissions, BuildContext context, String dialogContent) async {
    showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('温馨提示'),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text(dialogContent),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text('取消'),
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text('确定'),
              onPressed: () async {
                Navigator.of(context).pop();
                Navigator.of(context).pop();
                permissions.request();
              },
            ),
          ],
        );
      },
    );
  }
}
