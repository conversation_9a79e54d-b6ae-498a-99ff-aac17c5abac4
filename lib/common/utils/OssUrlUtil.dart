// ignore_for_file: non_constant_identifier_names

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:intl/intl.dart';

class OssUrlUtil {
  static Map<String, dynamic> textMap = {
    'ck': DefaultConfig().configs.BUCKET_CK_URL,
    'rk': DefaultConfig().configs.BUCKET_RK_URL,
    'th': DefaultConfig().configs.BUCKET_TH_URL
  };
  static Map<String, dynamic> textMap_bucket = {
    'ck': DefaultConfig().configs.BUCKET_CK,
    'rk': DefaultConfig().configs.BUCKET_RK,
    'th': DefaultConfig().configs.BUCKET_TH,
  };

  /// 获取文件名
  // static getUploadName(String url) async {
  //   String fileName = url.replaceAll(RegExp(r'.*/yz/\d{6,8}/'), '');
  //   UserEntity userEntity = await LocalStorage.get<UserEntity>(DefaultConfig().configs.USER_INFO);
  //   String path = DateFormat("yyyy/MM/dd").format(DateTime.now()) + '/' + userEntity.shopCode;
  //   return path + '/' + fileName;
  // }

  /// 获取上传后url
  // static getUploadUrl(String url, {String type = 'rk'}) async {
  //   String name = await getUploadName(url);
  //   return IMG_URL.replaceAll(BUCKET_DEFAULT, textMap[type]) + name;
  // }

  /// 获取上传图片文件名
  /// * 年/月/日/门店号/品牌/单号-时间戳,
  /// * yyyy/MM/dd/shopCode/brandCode/waybillNo-4位.jpg
  /// * @param [waybillNo] 运单号
  /// * @param [brandCode] 品牌编号
  /// * @param [type]  default rk 类型
  /// * @return String
  static contactName(String waybillNo, String typeId, String issueStatus, {String type = 'rk'}) async {
    String path = DateFormat("yyyyMMdd").format(DateTime.now()) + '/' + typeId + '/' + issueStatus;
    var photoUrl = path + '/' + waybillNo + '.jpg';
    return photoUrl;
  }

  /// 获取上传图片路径
  /// * @param [waybillNo] 运单号
  /// * @param [brandCode] 品牌编号
  /// * @param [type]  default rk 类型
  /// * @return String
  static contactPath(String waybillNo, String typeId, String issueStatus, {String type = 'rk'}) async {
    String filePath = await contactName(waybillNo, typeId, issueStatus);
    var photoUrl = textMap[type] + filePath;
    return photoUrl;
  }
}
