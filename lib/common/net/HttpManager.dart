import 'dart:convert';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/net/Code.dart';
import 'package:cabinet_flutter_app/common/net/ResultData.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:convert/convert.dart';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart' as Encrypt;
import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:intl/intl.dart';
import 'package:rhttp/rhttp.dart';

// Dio dio;
enum ApiVersion { V1, V2, V3 }

Map<ApiVersion, String> versionMap = {ApiVersion.V1: '1.0', ApiVersion.V2: '2.0', ApiVersion.V3: '3.0'};

var client;

///http请求
class HttpManager {
  static const CONTENT_TYPE_JSON = "application/json";
  static const channel = String.fromEnvironment("CHANNEL");
  static const aesSecret = "qweasdzxc6543210";
  static const requestSecret = "0123456qweasdzxc";

  static clearClient() async {
    return client = null;
  }

  static Map optionParams = {
    "timeoutMs": 15000,
    "token": null,
    "Authorization": null,
  };

  static getLocalApiUrl() async {
    var url = '';
    var apiUrl = await LocalStorage.getJson('apiUrl');
    if (apiUrl != null && apiUrl['isOpen']) {
      url = apiUrl['url'];
    } else {
      url = DefaultConfig().configs.PROD_URL;
    }
    return url;
  }

  static String generateMd5(String data) {
    var content = new Utf8Encoder().convert(data);
    var digest = md5.convert(content);
    // 这里其实就是 digest.toString()
    return hex.encode(digest.bytes);
  }

  /// 加密
  static encryptAes(String params) {
    var key = Encrypt.Key.fromUtf8(requestSecret);
    var encrypter = Encrypt.Encrypter(Encrypt.AES(key, mode: Encrypt.AESMode.ecb));
    var encriptData = encrypter.encrypt(jsonEncode(params));
    return encriptData;
  }

  /// 解密
  static String decryptAes(String params) {
    // AES
    var res = base64.decode(params);
    final key = Encrypt.Key.fromUtf8(aesSecret);
    final encrypter = Encrypt.Encrypter(Encrypt.AES(key, mode: Encrypt.AESMode.ecb));
    final decrypted = encrypter.decrypt(Encrypt.Encrypted(res), iv: Encrypt.IV.fromUtf8(''));

    return decrypted;
  }

  static post(String url, params, {noTip = false, isJson = false, ApiVersion version = ApiVersion.V1}) async {
    return await netFetch(url, params, null, 'post', noTip: noTip, version: version);
  }

  static get(String url, Map<String, String> params, {noTip = false, isJson = false}) async {
    return await netFetch(url, params, null, 'get', noTip: noTip);
  }

  static getAppKey() async {
    var apiUrl = await LocalStorage.getJson('apiUrl');
    if (apiUrl != null) {
      if (apiUrl['isProdSecret']) {
        return DefaultConfig().configs.APP_KEY;
      } else {
        return 'test';
      }
    }
    return DefaultConfig().configs.APP_KEY;
  }

  static getAppSecret() async {
    var apiUrl = await LocalStorage.getJson('apiUrl');
    if (apiUrl != null) {
      if (apiUrl['isProdSecret']) {
        return DefaultConfig().configs.APP_SECRET;
      } else {
        return '123456';
      }
    }
    return DefaultConfig().configs.APP_SECRET;
  }

  static buildSign(Map<String, dynamic> params, String secret) {
    List<String> keys = params.keys.toList()..sort();
    String data = '';
    keys.forEach((item) {
      data += item + params[item];
    });
    String source = secret + data + secret;
    return generateMd5(source).toUpperCase();
  }

  ///发起网络请求
  ///[ url] 请求url
  ///[ params] 请求参数
  ///[ header] 外加头
  ///[ option] 配置
  static netFetch(String url, params, Map<String, String>? header, String method,
      {noTip = false, version = ApiVersion.V1}) async {
    String apiUrl = await getLocalApiUrl();
    String appSecret = await getAppSecret();
    String appKey = await getAppKey();
    Map<String, String> headers = {'content-type': CONTENT_TYPE_JSON};
  
    if (kDebugMode) {
      print('请求地址: $apiUrl/$url');
      print('请求参数: $params');
    }
    if (header != null) {
      headers.addAll(header);
    }

    Map<dynamic, dynamic>? authorization = await CheckUtils.getToken();
    String? key = authorization?.keys.join('');
    if (authorization != null && authorization.length > 0) {
      if (key != null) {
        headers[key] = authorization[key];
      }
    }

    var data = jsonEncode(params ?? {});
    var dataString = Uri.encodeComponent(data);
    HttpTextResponse response;
    try {
      if (method == 'post') {
        params = {
          'app_key': appKey,
          'data': dataString,
          'name': url,
          'format': 'json',
          'timestamp': new DateFormat("yyyy-MM-dd HH:mm:ss").format(DateTime.now()),
          'version': versionMap[version]
        };

        params['sign'] = buildSign(params, appSecret);
        var key = Encrypt.Key.fromUtf8(requestSecret);
        var encrypt = Encrypt.Encrypter(Encrypt.AES(key, mode: Encrypt.AESMode.ecb));
        params = encrypt.encrypt(json.encode(params).toString(), iv: Encrypt.IV.fromUtf8('')).base64;
      }
    } catch (e) {
      return new ResultData(Code.errorHandleFunction(400, "加密错误", true), false, 400);
    }
    HttpVersionPref httpVersionPref = HttpVersionPref.http2;
    if (await CheckUtils.isOptimizeNetwork()) {
      httpVersionPref = HttpVersionPref.http3;
    }
    client ??= await RhttpClient.create(
      settings: ClientSettings(
        baseUrl: apiUrl,
        httpVersionPref: httpVersionPref,
        timeoutSettings: const TimeoutSettings(
          keepAliveTimeout: Duration(seconds: 60),
          keepAlivePing: Duration(seconds: 15),
          timeout: Duration(seconds: 15),
          connectTimeout: Duration(seconds: 3),
        ),
        dnsSettings: const DnsSettings.static(
          overrides: {
            'api.711bear.com': ['47.101.194.107'],
            'blzng-api.711bear.com': ['47.100.93.140'],
          },
        ),
        tlsSettings: const TlsSettings(
          verifyCertificates: false,
        ),
        // proxySettings: const ProxySettings.proxy("http://192.168.3.33:9010"),
      ),
    );
    try {
      // response = await dio.request('/api/v1', data: params, queryParameters: null);
      var httpsHeaders = HttpHeaders.rawMap(headers);
      if (method == 'get') {
        response = await client.get('/api/v1', headers: httpsHeaders, query: HttpBody.text(params));
      } else {
        response = await client.post('/api/v1', headers: httpsHeaders, body: HttpBody.text(params));
      }
    } on RhttpException catch (e) {
      return new ResultData(e.toString(), false, 400);
    }

    try {
      var body = jsonDecode(response.body);
      if (body['code'] == 0 || body['code'] == '0') {
        var res = base64.decode(body['data']);
        final key = Encrypt.Key.fromUtf8(aesSecret);
        final encrypter = Encrypt.Encrypter(Encrypt.AES(key, mode: Encrypt.AESMode.ecb));
        final decrypted = encrypter.decrypt(Encrypt.Encrypted(res), iv: Encrypt.IV.fromUtf8(''));
        var data;
        try {
          data = jsonDecode(decrypted);
        } catch (e) {
          print('jsonDecode err');
          data = decrypted;
        }
        if (kDebugMode) {
          print('解密数据: $data');
        }
        return new ResultData(data, true, Code.SUCCESS, headers: response.headers);
      } else if (['14', '15', '16', '17', '18', '19', '20', '21'].indexOf(body['code']) > -1) {
        return new ResultData(Code.errorHandleFunction(401, body['msg'], noTip), false, 200);
      } else {
        return new ResultData(Code.errorHandleFunction(response.statusCode, body['msg'], noTip), false, 200);
      }
    } catch (e) {
      EasyLoading.dismiss();
      return new ResultData(e.toString(), false, response.statusCode, headers: response.headers);
    }
  }

  ///清除授权
  static clearAuthorization() {
    LocalStorage.remove(DefaultConfig().configs.TOKEN_KEY);
  }
}
