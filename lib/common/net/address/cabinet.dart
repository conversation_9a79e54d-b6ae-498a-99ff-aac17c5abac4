part of address;

class Cabinet {
  /// 获取点位二维码
  static String qr = 'cabinet.qr';

  /// 获取点位信息
  static String cabinetInfo = "cabinet.info";
  static String getMqttConfig = "get.mqtt.config";

  /// 根据门店投件码，获取门店编码
  static String decryptShopCode = "decrypt.shop.code";

  /// 获取点位所有柜机及盒子
  static String cabinetFullInfo = "cabinet.full.info";

  /// 根据格口id获取订单信息
  static String courierOderDetail = "order.waybill.by.box.id";

  /// 获取点位所有柜子信息及其格口相关数据
  static String shopCabinetDetail = "shop.cabinet.detail";

  /// 点位可用格口列表1.0
  static String cabinetBoxUsableList = "cabinet.box.usable.list";

  /// 批量打开柜子可用格口1.0
  static String batchOpenCabinet = "batch.open.cabinet";

  /// 根据格口标签获取格口信息1.0
  static String cabinetBoxByLabel = "cabinet.box.by.label";

  /// 获取点位可用盒子汇总
  static String cabinetUsable = "cabinet.usable.summary";

  /// 获取格口开关门状态
  static String cabinetBoxOpenStatus = "cabinet.box.open.status";

  /// 格口关门
  static String cabinetBoxCloseDoor = "cabinet.box.close";

  /// 我的收藏点位列表
  static String cabinetLocationList = "courier.cabinet.location.list";

  /// 检测更新
  static String cabinetAppInfo = "cabinet.app.info";

  static String nextBoxCabinetId = "next.box.cabinet.id";

}
