part of address;

class Courier {
  /// 检测手机号
  static String checkMobile = "login.check";

  /// 获取App二维码
  static String appQr = "app.qr";

  /// 发送验证码
  static String sendSms = "send.sms";

  /// 注册
  static String register = "register";

  /// 注册
  static String shopRegister = "shop.register";

  /// 注册
  static String courierRegister = "courier.register";

  /// 登录
  static String login = "auth.login";

  /// 重置密码
  static String resetPwd = "reset.password";

  /// 批量设置开通的快递品牌
  static String bindBrand = "batch.update.brand";

  /// 设置快递公司
  static String setBindCompanyBrand = "courier.update.brand.code";

  /// 快递公司管理
  static String setBrand = "batch.update.brand";

  /// 单独设置快递品牌
  static String updateBrand = "update.brand";

  /// 获取信息
  static String getUserInfo = "user.info";

  /// 获取支持的快递公司列表
  static String getBrandList = "brand.code.list";

  /// 获取开通的品牌列表
  static String getBindBrandList = "brand.list";

  /// 实名认证
  static String realNameAuth = "real.name.auth";

  /// 微信充值
  static String rechargeWx = "wx.recharge";

  /// 获取微信小程序跳转链接
  static String wxmpJumpSchema = "wxmp.jump.schema";

  /// 支付宝充值
  static String rechargeAli = "alipay.recharge";

  /// 账号注销
  static String delUser = "del.user";

  /// 余额可提现转入
  static String rechargeBal = "balance.recharge";

  /// 获取账号余额等信息
  static String getWealthInfo = "account.info";

  /// 设置支付密码
  static String setPayPassword = "set.pay.password";

  /// 修改支付密码
  static String modifyPayPassword = "change.pay.password";

  /// 忘记支付密码
  static String forgetPayPassword = "forget.pay.password";

  /// 绑定支付宝账号
  static String bindAccount = "bind.alipay";

  ///资金明细
  static String fundDetails = "trade.record.page";

  ///提现明细
  static String withdrawalDetails = "withdrawal.page";

  ///提现
  static String withdrawalCreate = "withdrawal.create";

  ///资金明细汇总
  static String totalData = "trade.record.summary";

  ///我的消息
  static String myNotifyList = "media.notice.summary";

  ///我的消息详情列表
  static String myNotifyDetailList = "media.notice.page";

  ///读取单个消息
  static String readOneNotice = "read.media.notice";

  ///读取全部消息
  static String readAllNotice = "read.all.media.notice";

  ///团队管理列表
  static String myTeamList = "shop.employee.list";

  ///团队管理列表解绑
  static String myTeamDelete = "shop.employee.remove";

  ///团队管理列表加入
  static String myTeamAdd = "shop.employee.add";

  ///开启关闭寄件
  static String switchSend = "courier.save.switch.send";

  /// 绑定点位
  static String bindQr = "courier.cabinet.bind.qr";

  /// 获取快递员与点位关系
  static String courierCabinetStatus = "courier.cabinet.status";

  /// 获取快递员与点位信息
  static String fullInfo = "courier.cabinet.full.info";

  /// 获取快递员在柜的订单数量汇总
  static String courierCabinetWaybillSummary = "courier.cabinet.waybill.summary";

  /// 获取快递员在柜的寄件订单数量汇总
  static String getCabinetWaitTakenSummary = "courier.cabinet.wait.taken.count";

  /// 收藏点位
  static String courierCollectCabinet = "courier.collect.cabinet";

  /// 通过手机号获取用户标签
  static String courierMobileCheck = "customer.mobile.check";

  /// 快递员存件并开门
  static String courierOrderWaybillCreate = "courier.order.waybill.create";

  /// 快递员存件开门
  // static String courierOpenDoor = "courier.in.open.door";

  /// 门店存件开门
  static String courierShopOpenDoor = "shop.order.create";

  /// 门店存件获取下个格口
  static String courierShopOpenNextDoor = "shop.next.cabinet.box";

  /// 门店入仓
  static String courierShopIntoDoor = "shop.inbound";

  /// 门店出仓
  static String courierShopOutDoor = "shop.outbound";

  /// 拒收
  static String resendOrderBack = "resend.order.back";

  /// 所有点位列表
  static String shopCabinetLocation = "shop.cabinet.location.list";

  /// 我的关联点位1.0
  static String myCabinetLocationList = "my.cabinetLocation.list";

  /// 分页驿站合作列表1.0
  static String courierShopPage = "courier.shop.page";

  /// 创建驿站合作1.0
  static String courierShopCreate = "courier.shop.create";

  /// 修改驿站合作价格1.0
  static String courierShopChangePrice = "courier.shop.change.price";

  /// 处理快递员驿站合作1.0
  static String courierShopReview = "shop.courier.review";

   /// 驿站移除快递员驿站合作
  static String shopCourierDelShop = "shop.courier.del.shop";

  /// 快递员移除快递员驿站合作
  static String shopCourierDelCourier = "shop.courier.del.courier";

  /// 货架列表
  static String courierShopShelfList = "shop.shelf.list";

  /// 货架更新
  static String courierShopShelfSave = "shop.shelf.save";

  /// 根据货架名称获取取件码
  static String courierUserCode = "shop.shelf.check.code.get";

  /// 货架删除
  static String courierShopShelfDelete = "shop.shelf.delete";

  /// 快递员创建入柜订单
  static String courierOrderCreate = "courier.order.create";

  /// 快递员取消入柜订单
  static String courierOrderCancel = "order.cancel";

  /// 获取点位订单
  static String courierCabinetOrderList = "inbound.order.waybill.list";

  /// 获取点位订单
  static String courierCabinetOrderPagList = "inbound.order.waybill.page";

  /// 获取点位订单
  static String cabinetOrderPage = "order.waybill.page";

  /// 我的收件-待收列表
  static String courierSjCabinetOrderPagList = "courier.order.send.wait.taken.page";

  /// 我的收件-待收列表
  static String courierSjSendTakenPagList = "courier.order.send.taken.page";

  /// 获取我的派件所有滞留件、待取件
  static String courierCabinetSummaryList = "inbound.order.waybill.summary";

  /// 获取我的派件所有已取件
  static String courierCabinetOutboundList = "outbound.order.waybill.summary";

  /// 获取我的派件所有已取件分页列表
  static String courierOutboundOrderList = "outbound.order.waybill.page";

  /// 获取单号详情
  static String courierPackageDetail = "order.detail";

  ///根据订单id修改收件人手机号
  static String courierEditOrderMobile = "change.order.mobile";

  /// 重发短信订单短信
  static String courierOrderResendSms = "resend.order.sms";

  /// 取出,开门
  static String courierOutboundWaybill = "shop.outbound.order.waybill";

  /// 异常出库
  static String courierUnusualOutWaybill = "outbound.order.waybill";

  /// 寄件取消订单
  static String courierOutboundWaybillSj = "courier.cancel.order.send";

  /// 回填单号
  static String courierBackOrderNo = "courier.update.order.send.waybill.no";

  /// 快递员开门检查
  static String courierBoxOpenCheck = "cabinet.box.check";

  static String  remoteOpenBoxCheck = "remote.open.box.check";
  
 
  /// 确认取出寄件订单
  static String courierTakeOrderSend = "courier.take.order.send";

  /// 首页滞留件、待取件
  static String courierHomeInCabinet = "in.cabinet.summary";

  /// 物流信息轨迹同步
  static String courierGatewayResend = "resend.order.waybill.inbound";

  static String resendBdp = "resend.order.bdp";

  /// 快递订单物流轨迹投递日志
  static String courierSyncLog = "order.waybill.gateway.log.list";

  /// 快递订单物流轨迹投递日志
  static String courierOrderLog = "order.log.list";

  /// 用户消息日志
  static String courierNoticeLog = "order.message.list";

  ///分页获取客户信息
  static String customerList = "customer.page";

  /// 获取已有客户标签列表
  static String customerLabelList = "customer.label.list";

  /// 删除客户标签
  static String customerLabelRemove = "customer.label.remove";

  /// 删除客户
  static String customerRemove = "customer.remove";

  /// 更新或添加客户
  static String customerEdit = "customer.save.or.update";

  /// 更新客户名称
  static String customerUpdate = "customer.update.name";

  /// 数据中心-今日出入库收入汇总
  static String todaySummary = "today.summary";

  static String brandSummary = "brand.inbound.outbound.summary";

  /// 首页汇总1.0
  static String homeSummary = "home.summary";

  /// 数据中心-获取用户滞留件汇总
  static String keepEffectSummary = "keep.effect.summary";

  /// 数据中心-获取15日入柜数据汇总
  static String halfMonthInboundSummary = "half.month.inbound.summary";

  /// 数据中心-按月获取半年入柜数据汇总
  static String halfYearInboundSummary = "half.year.inbound.summary";

  /// 数据中心-获取15日出库数据汇总
  static String halfMonthOutboundSummary = "half.month.outbound.summary";

  /// 数据中心-按月获取半年出库柜数据汇总
  static String halfYearOutboundSummary = "half.year.outbound.summary";

  /// 业务统计-获取统计数据汇总
  static String businessStatisticsSummary = "business.statistics.summary";

  /// 业务统计-获取每日明细数据
  static String businessStatisticsDaily = "business.statistics.daily";

  /// 业务统计-获取每日详细数据（按快递公司/业务员/员工分组）
  static String businessStatisticsDetail = "business.statistics.detail";

  /// 包裹查询
  static String getCourierOrderSearch = "order.waybill.search";

  /// 附近点位
  static String getCourierCabinetLocation = "courier.nearby.cabinet.location.page";

  ///空箱查询
  static String getCourierEmptyBox = "courier.empty.box.page";

  /// 获取管家广告列表
  static String getBannerList = "banner.list";

  /// 分页获取资金订单明细列表
  static String getTradePage = "order.trade.record.page";

  /// 更新快递公司
  static String changeCompany = "order.waybill.company.change";

  /// 更新快递公司
  static String updateWayBillNo = "update.waybillno";

  /// 创建预约订单
  static String courierOrderBoolCreate = "courier.order.book.create";

  /// 获取预约列表
  static String courierOrderBookPage = "courier.order.book.page";

  /// 获取预约单详情
  static String courierOrderBookDetail = "courier.order.book.detail";

  /// 单独设置快递品牌自动签收
  static String updateAutoSign = "shop.set.auto.sign";

  /// 获取自动签收列表
  static String getAutoSignList = "shop.brand.list";

  static String gpAddGpconfig = "gp.add.gpconfig";

  static String gpHasGpconfig = "gp.has.gpconfig";

  static String gpUserAccounts = "gp.userAccounts";

  static String gpDelete = "gp.delete";

  static String accountList = "shop.yz.account.list";

  static String accountDel = "shop.yz.account.del";

  static String getCaptcha = "shop.yz.account.getCaptchaV2";

  static String verifyCaptcha = "shop.yz.account.verifyCaptcha";

  static String updateAccount = "shop.yz.save.update";

  static String loginCheck = "shop.yz.login.check";

  static String sendLoginCode = "shop.yz.login.sms";

  static String ddGet = "shop.yz.account.dd.get";

  static String ruleDetail = "shop.yz.rule.detail";

  static String ruleSaveOrUpdate = "shop.yz.rule.saveOrUpdate";

  static String orderExceptionLog = "order.exception.log.page";

  static String orderExceptionLogDel = "order.exception.log.del";

  static String gatewayResend = "resend.order.waybill";

  static String reInboundChannel = "order.reInbound.channel.default";

  static String sysConfig = "sys.config.query";

  /// 拦截单号列表查询
  static String interceptList = "shop.waybill.intercept.list";

  /// 添加拦截单号
  static String interceptAdd = "shop.waybill.intercept.add";

  /// 删除拦截单号
  static String interceptDel = "shop.waybill.intercept.del";

  /// 创建反馈
  static String feedbackCreate = "feedback.create";

  /// 查询反馈列表
  static String feedbackQuery = "feedback.query";

}
