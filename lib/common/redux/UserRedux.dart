import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:redux/redux.dart';

/**
 * 用户相关Redux
 * Created by denonzhu
 * Date: 2018-07-16
 */

/// redux 的 combineReducers, 通过 TypedReducer 将 UpdateUserAction 与 reducers 关联起来
final userReducer = combineReducers<UserEntity?>([
  TypedReducer<UserEntity?, UpdateUserAction>(_updateLoaded),
]);

/// 如果有 UpdateUserAction 发起一个请求时
/// 就会调用到 _updateLoaded
/// _updateLoaded 这里接受一个新的userInfo，并返回
UserEntity _updateLoaded(UserEntity? user, action) {
  user = action.userInfo;
  return user!;
}

///定一个 UpdateUserAction ，用于发起 userInfo 的的改变
///类名随你喜欢定义，只要通过上面TypedReducer绑定就好
class UpdateUserAction {
  final UserEntity userInfo;

  UpdateUserAction(this.userInfo);
}
