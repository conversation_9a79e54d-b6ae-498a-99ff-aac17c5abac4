import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:cabinet_flutter_app/common/redux/CurrentTabIndexRedux.dart';
import 'package:cabinet_flutter_app/common/redux/LocaleRedux.dart';
import 'package:cabinet_flutter_app/common/redux/ScanRedux.dart';
import 'package:cabinet_flutter_app/common/redux/ThemeRedux.dart';
import 'package:cabinet_flutter_app/common/redux/UserRedux.dart';
import 'package:cabinet_flutter_app/common/redux/UserTypeRedux.dart';
import 'package:flutter/material.dart';

/**
 * Redux全局State
 * Created by denonzhu
 * Date: 2018-07-16
 */

///全局Redux store 的对象，保存State数据
class AppState {
  ///用户信息
  UserEntity? userInfo;

  /// 用户类型 -1:未注册，4-快递员，6-店员
  int userType = 4;

  ///主题数据
  ThemeData? themeData;

  ///语言
  Locale? locale;

  ///当前手机平台默认语言
  Locale? platformLocale;

  ///扫码信息
  String? scan;

  int? currentTabIndex = 0;

  ///构造方法
  AppState({this.userInfo, this.userType = 4, this.themeData, this.locale, this.scan, this.currentTabIndex = 0});
}

///创建 Reducer
///源码中 Reducer 是一个方法 typedef State Reducer<State>(State state, dynamic action);
///我们自定义了 appReducer 用于创建 store
AppState appReducer(AppState state, action) {
  return AppState(

    ///通过 UserReducer 将 AppState 内的 userInfo 和 action 关联在一起
      userInfo: userReducer(state.userInfo, action),
      userType: userTypeReducer(state.userType, action),

      ///通过 ThemeDataReducer 将 AppState 内的 themeData 和 action 关联在一起
      themeData: themeDataReducer(state.themeData!, action),

      ///通过 LocaleReducer 将 AppState 内的 locale 和 action 关联在一起
      locale: localeReducer(state.locale!, action),
      scan: scanRedux(state.scan!, action),
      currentTabIndex: currentTabIndexRedux(state.currentTabIndex!, action));
}
