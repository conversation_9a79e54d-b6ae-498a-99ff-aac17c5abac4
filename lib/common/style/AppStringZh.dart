import 'package:cabinet_flutter_app/common/style/AppStringBase.dart';

/// Created by denonzhu
/// Date: 2018-08-15
class AppStringZh extends AppStringBase {
  @override
  String welcomeMessage = "Welcome To Flutter";

  @override
  String appName = "熊猫管家";

  @override
  String appOk = "确定";
  @override
  String appCancel = "取消";
  @override
  String appEmpty = "当前没有数据哦~";
  @override
  String appLicenses = "协议";
  @override
  String appClose = "关闭";
  @override
  String appVersion = "版本";
  @override
  String appBackTip = "确定要退出应用？";

  @override
  String appNotNewVersion = "当前没有新版本";
  @override
  String appVersionTitle = "版本更新";

  @override
  String nothingNow = "当前没有数据哦~";
  @override
  String loadingText = "努力加载中···";

  @override
  String optionWeb = "浏览器打开";
  @override
  String optionCopy = "复制链接";
  @override
  String optionShare = "分享";
  @override
  String optionWebLauncherError = "url异常";
  @override
  String optionShareTitle = "分享自AppGitHubFlutter： ";
  @override
  String optionShareCopySuccess = "已经复制到粘贴板";

  @override
  String loginText = "登录";

  @override
  String loginOrRegister = "登录/注册";

  @override
  String loginAndRegister = "登录并注册";

  @override
  String phoneCheck = "请输入正确的手机号";

  @override
  String signOut = "退出登录";

  @override
  String homeReply = "问题反馈";
  @override
  String homeChangeLanguage = "语言切换";
  @override
  String homeAbout = "关于";
  @override
  String homeCheckUpdate = "检测更新";
  @override
  String homeHistory = "阅读历史";
  @override
  String homeUserInfo = "个人信息";
  @override
  String homeChangeTheme = "切换主题";
  @override
  String homeLanguageDefault = "默认";
  @override
  String homeLanguageZH = "中文";
  @override
  String homeLanguageEN = "英文";

  @override
  String homeThemeDefault = "默认主题";
  @override
  String homeTheme1 = "主题1";
  @override
  String homeTheme2 = "主题2";
  @override
  String homeTheme3 = "主题3";
  @override
  String homeTheme4 = "主题4";
  @override
  String homeTheme5 = "主题5";
  @override
  String homeTheme6 = "主题6";

  @override
  String loginUsernameHintText = "请输入用户名";
  @override
  String loginPasswordHintText = "请输入密码";
  @override
  String loginSuccess = "登录成功";

  @override
  String networkError_401 = "登录失效,请重新登录";
  @override
  String networkError_403 = "403权限错误";
  @override
  String networkError_404 = "404错误";
  @override
  String networkErrorTimeout = "请求超时";
  @override
  String networkErrorUnknown = "其他异常";
  @override
  String networkError = "网络错误";

  @override
  String loadMoreNot = "没有更多数据";
  @override
  String loadMoreText = "正在加载更多";

  @override
  String homeDynamic = "动态";

  @override
  String homeIndex = "首页";
  @override
  String homeAssessment = "报表";
  @override
  String homeTrend = "趋势";
  @override
  String homeMy = "我的";

  @override
  String trendDay = '今日';
  @override
  String trendWeek = '本周';
  @override
  String trendMonth = '本月';
  @override
  String trendAll = '全部';

  @override
  String userTabRepos = "仓库";
  @override
  String userTabFans = "粉丝";
  @override
  String userTabFocus = "关注";
  @override
  String userTabStar = "星标";
  @override
  String userTabHonor = "荣耀";
  @override
  String userDynamicGroup = "组织成员";
  @override
  String userDynamicTitle = "个人动态";
  @override
  String userFocus = "已关注";
  @override
  String userUnFocus = "关注";
  @override
  String userFocusNoSupport = "不支持关注组织。";
  @override
  String userCreateAt = "创建于：";
  @override
  String userOrgsTitle = "所在组织";

  @override
  String reposTabReadme = "详情";
  @override
  String reposTabInfo = "动态";
  @override
  String reposTabFile = "文件";
  @override
  String reposTabIssue = "ISSUE";
  @override
  String reposTabActivity = "动态";
  @override
  String reposTabCommits = "提交";
  @override
  String reposTabIssueAll = "所有";
  @override
  String reposTabIssueOpen = "打开";
  @override
  String reposTabIssueClosed = "关闭";
  @override
  String reposOptionRelease = "版本";
  @override
  String reposOptionBranch = "分支";
  @override
  String reposForkAt = "Fork于 ";
  @override
  String reposCreateAt = "创建于 ";
  @override
  String reposLastCommit = "最后提交于 ";
  @override
  String reposAllIssueCount = "所有Issue数：";
  @override
  String reposOpenIssueCount = "开启Issue数：";
  @override
  String reposCloseIssueCount = "关闭Issue数：";

  @override
  String reposIssueSearch = "搜索";

  @override
  String issueReply = "回复";
  @override
  String issueEdit = "编辑";
  @override
  String issueOpen = "打开";
  @override
  String issueClose = "关闭";
  @override
  String issueLock = "锁定";
  @override
  String issueUnlock = "解锁";
  @override
  String issueReplyIssue = "回复Issue";
  @override
  String issueCommitIssue = "提交Issue";
  @override
  String issueEditIssue = "编译Issue";
  @override
  String issueEditIssueCommit = "编译回复";
  @override
  String issueEditIssueEditCommit = "编辑";
  @override
  String issueEditIssueDeleteCommit = "删除";
  @override
  String issueEditIssueCopyCommit = "复制";
  @override
  String issueEditIssueContentNotBeNull = "内容不能为空";
  @override
  String issueEditIssueTitleNotBeNull = "标题不能为空";
  @override
  String issueEditIssueTitleTip = "请输入标题";
  @override
  String issueEditIssueContentTip = "请输入内容";

  @override
  String notifyTitle = "通知";
  @override
  String notifyTabAll = "所有";
  @override
  String notifyTabPart = "参与";
  @override
  String notifyTabUnread = "未读";
  @override
  String notifyUnread = "未读";
  @override
  String notifyReaded = "已读";
  @override
  String notifyStatus = "状态";
  @override
  String notifyType = "类型";

  @override
  String searchTitle = "搜索";
  @override
  String searchTabRepos = "仓库";
  @override
  String searchTabUser = "用户";

  @override
  String releaseTabRelease = "版本";
  @override
  String releaseTabTag = "标记";

  @override
  String userProfileName = "名字";
  @override
  String userProfileEmail = "邮箱";
  @override
  String userProfileLink = "链接";
  @override
  String userProfileOrg = "公司";
  @override
  String userProfileLocation = "位置";
  @override
  String userProfileInfo = "简介";

  @override
  String searchType = "类型";
  @override
  String searchSort = "排序";
  @override
  String searchLanguage = "语言";
}
