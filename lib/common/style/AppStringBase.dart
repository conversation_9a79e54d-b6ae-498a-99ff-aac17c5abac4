abstract class AppStringBase {
  late String welcomeMessage;

  late String appName;

  late String appOk;

  late String appCancel;

  late String appEmpty;
  late String appLicenses;
  late String appClose;

  late String appVersion;

  late String appBackTip;

  late String appNotNewVersion;
  late String appVersionTitle;

  late String nothingNow;

  late String loadingText;

  late String optionWeb;

  late String optionCopy;

  late String optionShare;

  late String optionWebLauncherError;

  late String optionShareTitle;
  late String optionShareCopySuccess;

  late String loginText;
  late String loginOrRegister;

  late String loginAndRegister;

  late String phoneCheck;

  late String signOut;

  late String homeReply;

  late String homeChangeLanguage;

  late String homeAbout;

  late String homeCheckUpdate;

  late String homeHistory;

  late String homeUserInfo;

  late String homeChangeTheme;

  late String homeLanguageDefault;
  late String homeLanguageZH;
  late String homeLanguageEN;

  late String homeThemeDefault;

  late String homeTheme1;

  late String homeTheme2;

  late String homeTheme3;

  late String homeTheme4;

  late String homeTheme5;

  late String homeTheme6;

  late String loginUsernameHintText;

  late String loginPasswordHintText;

  late String loginSuccess;

  late String networkError_401;
  late String networkError_403;

  late String networkError_404;

  late String networkErrorTimeout;

  late String networkErrorUnknown;

  late String networkError;

  late String loadMoreNot;

  late String loadMoreText;

  late String homeDynamic;
  late String homeIndex;

  late String homeTrend;
  late String homeMy;

  late String trendDay;

  late String trendWeek;

  late String trendMonth;

  late String trendAll;

  late String userTabRepos;

  late String userTabFans;

  late String userTabFocus;

  late String userTabStar;

  late String userTabHonor;
  late String userDynamicGroup;

  late String userDynamicTitle;

  late String userFocus;
  late String userUnFocus;

  late String userFocusNoSupport;
  late String userCreateAt;
  late String userOrgsTitle;

  late String reposTabReadme;

  late String reposTabInfo;

  late String reposTabFile;

  late String reposTabIssue;

  late String reposTabActivity;
  late String reposTabCommits;
  late String reposTabIssueAll;
  late String reposTabIssueOpen;
  late String reposTabIssueClosed;
  late String reposOptionRelease;
  late String reposOptionBranch;

  late String reposForkAt;
  late String reposCreateAt;

  late String reposLastCommit;

  late String reposAllIssueCount;

  late String reposOpenIssueCount;
  late String reposCloseIssueCount;

  late String reposIssueSearch;

  late String issueReply;

  late String issueEdit;

  late String issueOpen;

  late String issueClose;

  late String issueLock;
  late String issueUnlock;

  late String issueReplyIssue;

  late String issueCommitIssue;

  late String issueEditIssue;

  late String issueEditIssueCommit;
  late String issueEditIssueEditCommit;

  late String issueEditIssueDeleteCommit;

  late String issueEditIssueCopyCommit;
  late String issueEditIssueContentNotBeNull;

  late String issueEditIssueTitleNotBeNull;

  late String issueEditIssueTitleTip;

  late String issueEditIssueContentTip;

  late String notifyTitle;
  late String notifyTabAll;

  late String notifyTabPart;

  late String notifyTabUnread;
  late String notifyUnread;

  late String notifyReaded;

  late String notifyStatus;

  late String notifyType;

  late String searchTitle;
  late String searchTabRepos;

  late String searchTabUser;

  late String releaseTabRelease;

  late String releaseTabTag;

  late String userProfileName;

  late String userProfileEmail;

  late String userProfileLink;

  late String userProfileOrg;
  late String userProfileLocation;
  late String userProfileInfo;

  late String searchType;
  late String searchSort;
  late String searchLanguage;

  late String homeAssessment;
}
