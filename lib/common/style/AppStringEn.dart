import 'package:cabinet_flutter_app/common/style/AppStringBase.dart';

/// Created by denonzhu
/// Date: 2018-08-15
class AppStringEn extends AppStringBase {
  @override
  String welcomeMessage = "Welcome To Flutter";

  @override
  String appName = "Xiongmao Smart Cabinet";

  @override
  String appOk = "ok";
  @override
  String appCancel = "cancel";
  @override
  String appEmpty = "Empty(oﾟ▽ﾟ)o";
  @override
  String appLicenses = "licenses";
  @override
  String appClose = "close";
  @override
  String appVersion = "version";
  @override
  String appBackTip = "Exit？";

  @override
  String appNotNewVersion = "No new version.";
  @override
  String appVersionTitle = "Update Version";

  @override
  String nothingNow = "Nothing";
  @override
  String loadingText = "Loading···";

  @override
  String optionWeb = "browser";
  @override
  String optionCopy = "copy";
  @override
  String optionShare = "share";
  @override
  String optionWebLauncherError = "url error";
  @override
  String optionShareTitle = "share form AppGitHubFlutter： ";
  @override
  String optionShareCopySuccess = "Copy Success";

  @override
  String loginText = "Sign in";

  @override
  String loginOrRegister = "Sign in/ Sign up";
  @override
  String loginAndRegister = "Sign in and Sign up";
  @override
  String phoneCheck = "Please Check Phone Number!";

  @override
  String signOut = "Logout";

  @override
  String homeReply = "Feedback";
  @override
  String homeChangeLanguage = "Language";
  @override
  String homeAbout = "About";
  @override
  String homeCheckUpdate = "CheckUpdate";
  @override
  String homeHistory = "History";
  @override
  String homeUserInfo = "Profile";
  @override
  String homeChangeTheme = "Theme";
  @override
  String homeLanguageDefault = "Default";
  @override
  String homeLanguageZH = "Chinese";
  @override
  String homeLanguageEN = "English";

  @override
  String homeThemeDefault = "Default";
  @override
  String homeTheme1 = "Theme1";
  @override
  String homeTheme2 = "Theme2";
  @override
  String homeTheme3 = "Theme3";
  @override
  String homeTheme4 = "Theme4";
  @override
  String homeTheme5 = "Theme5";
  @override
  String homeTheme6 = "Theme6";

  @override
  String loginUsernameHintText = "username";
  @override
  String loginPasswordHintText = "password";
  @override
  String loginSuccess = "Login Success";

  @override
  String networkError_401 = "Http 401";
  @override
  String networkError_403 = "Http 403";
  @override
  String networkError_404 = "Http 404";
  @override
  String networkErrorTimeout = "Http timeout";
  @override
  String networkErrorUnknown = "Http unknown error";
  @override
  String networkError = "network error";

  @override
  String loadMoreNot = "nothing";
  @override
  String loadMoreText = "loading";

  @override
  String homeDynamic = "Dynamic";
  @override
  String homeTrend = "Trend";
  @override
  String homeMy = "My";

  @override
  String trendDay = 'today';
  @override
  String trendWeek = 'week';
  @override
  String trendMonth = 'month';
  @override
  String trendAll = 'all';

  @override
  String userTabRepos = "repos";
  @override
  String userTabFans = "fan";
  @override
  String userTabFocus = "focus";
  @override
  String userTabStar = "star";
  @override
  String userTabHonor = "honor";
  @override
  String userDynamicGroup = "Members;";
  @override
  String userDynamicTitle = "Dynamic";
  @override
  String userFocus = "Focused";
  @override
  String userUnFocus = "Focus";
  @override
  String userFocusNoSupport = "Not Support。";
  @override
  String userCreateAt = "Create at：";
  @override
  String userOrgsTitle = "organization";

  @override
  String reposTabReadme = "readme";
  @override
  String reposTabInfo = "info";
  @override
  String reposTabFile = "file";
  @override
  String reposTabIssue = "issue";
  @override
  String reposTabActivity = "activity";
  @override
  String reposTabCommits = "commits";
  @override
  String reposTabIssueAll = "all";
  @override
  String reposTabIssueOpen = "open";
  @override
  String reposTabIssueClosed = "close";
  @override
  String reposOptionRelease = "release";
  @override
  String reposOptionBranch = "branch";
  @override
  String reposForkAt = "Fork at ";
  @override
  String reposCreateAt = "create at ";
  @override
  String reposLastCommit = "last commit at ";
  @override
  String reposAllIssueCount = "all Issue：";
  @override
  String reposOpenIssueCount = "open Issue：";
  @override
  String reposCloseIssueCount = "close Issue：";

  @override
  String reposIssueSearch = "Search";

  @override
  String issueReply = "reply";
  @override
  String issueEdit = "edit";
  @override
  String issueOpen = "open";
  @override
  String issueClose = "close";
  @override
  String issueLock = "lock";
  @override
  String issueUnlock = "unlock";
  @override
  String issueReplyIssue = "reply Issue";
  @override
  String issueCommitIssue = "commit Issue";
  @override
  String issueEditIssue = "edit issue";
  @override
  String issueEditIssueCommit = "edit reply";
  @override
  String issueEditIssueEditCommit = "Edit";
  @override
  String issueEditIssueDeleteCommit = "Delete";
  @override
  String issueEditIssueCopyCommit = "Copy";
  @override
  String issueEditIssueContentNotBeNull = "Could't not be empty";
  @override
  String issueEditIssueTitleNotBeNull = "Could't not be empty";
  @override
  String issueEditIssueTitleTip = "please input title";
  @override
  String issueEditIssueContentTip = "please input content";

  @override
  String notifyTitle = "Notify";
  @override
  String notifyTabAll = "all";
  @override
  String notifyTabPart = "part";
  @override
  String notifyTabUnread = "unread";
  @override
  String notifyUnread = "unread";
  @override
  String notifyReaded = "read";
  @override
  String notifyStatus = "status";
  @override
  String notifyType = "type";

  @override
  String searchTitle = "Search";
  @override
  String searchTabRepos = "Repos";
  @override
  String searchTabUser = "User";

  @override
  String releaseTabRelease = "Release";
  @override
  String releaseTabTag = "Tag";

  @override
  String userProfileName = "name";
  @override
  String userProfileEmail = "email";
  @override
  String userProfileLink = "link";
  @override
  String userProfileOrg = "company";
  @override
  String userProfileLocation = "location";
  @override
  String userProfileInfo = "info";

  @override
  String searchType = "type";
  @override
  String searchSort = "sort";
  @override
  String searchLanguage = "language";
}
