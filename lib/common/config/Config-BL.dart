// ignore_for_file: non_constant_identifier_names

import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:flutter/material.dart';

class ConfigBL extends ConfigBase {
  @override
  bool DEBUG = true;
  @override
  String APP_KEY = 'blzng';

  @override
  String APP_SECRET = 'Blzngqwe123';
  @override
  String APP_NAME = '便利管家';

  @override
  String DEV_URL = 'https://api-test.711bear.com:10043';

  /// 正式服务URL
  @override
  String PROD_URL = 'https://blzng-api.711bear.com';

  @override
  String WEB_URL = 'https://blzng-s.711bear.com/#';

  @override
  String KUAIDI100 = 'https://m.kuaidi100.com/app/query/?coname=cabinet&';

  /// OSS 配置相关
  // @override
  String IMG_URL = 'https://zqgp-img.oss-cn-hangzhou.aliyuncs.com/';
  @override
  String BASE_URL = 'https://blzng-img.oss-cn-hangzhou.aliyuncs.com';
  @override
  String BUCKET_RK = 'blzng-photo';
  @override
  String BUCKET_CK = 'blzng-photo';
  @override
  String BUCKET_TH = 'kdyz-back';
  @override
  String BUCKET_RK_URL = 'http://blzng-photo.oss-cn-shanghai.aliyuncs.com/';
  @override
  String BUCKET_CK_URL = 'http://blzng-photo.oss-cn-shanghai.aliyuncs.com/';
  @override
  String BUCKET_TH_URL = 'http://blzng-photo.oss-cn-shanghai.aliyuncs.com/';

  //微信支付
  @override
  String WX_APP_ID = 'wxadbd7e4094042142';

  String UNIVERSAL_LINK = 'https://blzng-s.711bear.com';

  String WXMP_SOURCE_ID = 'gh_f7e1b7c4d52b';

  //支付宝支付
  @override
  String ALI_APP_ID = '2021004136643249';

  //高德key
  @override
  String GAO_MAP_KEY = 'a475e6cd66cbaba987c07788d5d80cf5';
  @override
  Color PRIMARY_COLOR = Color(0xFF1890ee);
  @override
  Color PRIMARY_COLOR_LIGHT = Color(0xffd6eafa);
  @override
  String PRIMARY_COLOR_TEXT = '#1890ee';
  @override
  Color PRIMARY_COLOR_GHOST = Color(0x211675F7);
  @override
  Color PRIMARY_COLOR_SWATCH = Colors.blue;
}
