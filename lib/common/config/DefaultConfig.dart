import 'package:cabinet_flutter_app/common/config/Config-BL.dart';
import 'package:cabinet_flutter_app/common/config/Config.dart';
import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';

class DefaultConfig {
  static const channel = String.fromEnvironment("CHANNEL");

  static late bool isStation = false;

  const DefaultConfig();

  ConfigBase get configs {
    late ConfigBase config;
    switch (channel) {
      case 'xm':
        config = Config();
        break;
      case 'bl':
        config = ConfigBL();
        break;
      default:
        config = Config();
        break;
    }
    return config;
  }

  setStation(_isStation){
    isStation = _isStation;
  }
}
