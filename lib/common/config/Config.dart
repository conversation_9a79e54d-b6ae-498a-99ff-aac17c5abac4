// ignore_for_file: non_constant_identifier_names, override_on_non_overriding_member

import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:flutter/material.dart';

class Config extends ConfigBase {
  @override
  bool DEBUG = true;

  @override
  String APP_KEY = 'xmzng';

  @override
  String APP_SECRET = 'Xmzngqwe123';
  @override
  String APP_NAME = '熊猫管家';

  /// 开发环境URL
  @override
  String DEV_URL = 'https://api-test.711bear.com:10043';

  /// 正式服务URL
  @override
  String PROD_URL = 'https://api.711bear.com';

  @override
  String WEB_URL = 'https://s.711bear.com/#';

  @override
  String KUAIDI100 = 'https://m.kuaidi100.com/app/query/?coname=cabinet&';

  /// OSS 配置相关
  @override
  String IMG_URL = 'https://zqgp-img.oss-cn-hangzhou.aliyuncs.com/';
  @override
  String BASE_URL = 'https://cabinet-img.oss-cn-hangzhou.aliyuncs.com';
  @override
  String BUCKET_RK = 'cabinet-photo';
  @override
  String BUCKET_CK = 'cabinet-photo';
  @override
  String BUCKET_TH = 'kdyz-back';
  @override
  String BUCKET_RK_URL = 'http://img.711bear.com/';
  @override
  String BUCKET_CK_URL = 'http://img.711bear.com/';
  @override
  String BUCKET_TH_URL = 'https://kdyz-back.oss-cn-hangzhou.aliyuncs.com/';

  //微信支付
  @override
  String WX_APP_ID = 'wxb3d58d94e42c0244';

  String UNIVERSAL_LINK = 'https://s.711bear.com';

  String WXMP_SOURCE_ID = 'gh_b5d383d15a5a';

  //支付宝支付
  @override
  String ALI_APP_ID = '2021003126670036';

  //高德key
  @override
  String GAO_MAP_KEY = 'c8e457cebbe5762e42f2c54140fb0f39';

  @override
  Color PRIMARY_COLOR = Color(0xFFFF6600);

  Color PRIMARY_COLOR_LIGHT = Color(0xFFFFEDE7);
  @override
  String PRIMARY_COLOR_TEXT = '#FF6600';
  @override
  Color PRIMARY_COLOR_GHOST = Color(0x211675F7);
  @override
  Color PRIMARY_COLOR_SWATCH = Colors.red;
}
