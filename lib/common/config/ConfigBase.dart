// ignore_for_file: non_constant_identifier_names

import 'package:flutter/material.dart';

abstract class ConfigBase {
  late bool DEBUG;

  late String APP_KEY;

  late String APP_SECRET;

  late String APP_NAME;

  ///测试服
  late String DEV_URL;

  late String PROD_URL;

  late String WEB_URL;

  late String KUAIDI100;

  int PAGE_SIZE = 20;

  late String BASE_URL;
  late String BUCKET_RK;
  late String BUCKET_CK;
  late String BUCKET_TH;
  late String BUCKET_RK_URL;
  late String BUCKET_CK_URL;
  late String BUCKET_TH_URL;

  //微信支付
  late String WX_APP_ID;
  late String WXMP_SOURCE_ID;
  late String UNIVERSAL_LINK;

  //支付宝支付
  late String ALI_APP_ID;

  //高德key
  late String GAO_MAP_KEY;

  //服务协议
  String USER_AGREE = '/userProtocol';
  String USER_AGREE1 = '/privacyProtocol';
  String USER_AGREE2 = '/rechargeProtocol';
  String HELP_CENTER = '/helpCenter';

  //人工客服
  String CUSTOMER_SERVICE =
      'https://chatlink-new.meiqia.cn/widget/standalone.html?eid=f8cdda38bc77a57675917b25a5f7db70&subSource=sub_source_2';

  ///常量
  String FAST_OUT = "fast-out";
  String AIDC_BRAND = "AIDC-brand";
  String ADD_CABINET = "ADD_CABINET";
  String SYNCHRONIZED_LOGISTICS = "SYNCHRONIZED_LOGISTICS";
  String HAS_SITE_OPEN_ADD_CABINET = "HAS_SITE_OPEN_ADD_CABINET";
  String HAS_OPEN_SINGEL_CUSTOMER_NOTICE = "HAS_OPEN_SINGEL_CUSTOMER_NOTICE";
  String HAS_OPEN_FAST_INTO = "HAS_OPEN_FAST_INTO";
  String CAN_IN_BRANDS = "can-in-brands";
  String CHOOSE_BRAND = "choose-brand";
  String CABINET_BIND = "bind-cabinet";
  String COMPANY_RULE = "companyRule";
  String ONE_OUT = "one-out";
  String TOKEN_KEY = "token";
  String TOKEN_NAME = "token-name";
  String TOKEN_VALUE = "token-value";
  String OFFLINE = "offline-mod";
  String IS_PDA = "is-pda";
  String IS_OPTIMIZE_NETWORK = "is-optimize-network";
  String IS_NEW = "is-new";
  String IS_DP = "is-dp";
  String IS_OCR = "is-ocr";
  String IS_CUSTOMER_KEYBOARD = "is-customer-keyboard";
  String IS_SUPPORT_WS = "is-support-ws";
  String INBOUND_TYPE = "inbound-type";
  String OPEN_PRINT = "open-print";
  String SITE_INFO = "site-info";
  String IS_LJ = "isLj";
  String IS_PJ = "isPj";
  String IS_AI = "is-open-ai-new2";
  String IS_BATCH = "is-batch";
  String IS_PHOTO = "is-photo";
  String IS_CK_PHOTO = "is-ck-photo";
  String USER_NAME_KEY = "user-name";
  String USER_PERMIT = "user-permit";
  String LOGIN_TIME = "login-time";
  String DEPLOY_CODE = "deploy-code";
  String PW_KEY = "user-pw";
  String USER_TYPE = "user-type";
  String HAS_WALLET = "has-wallet";
  String HAS_SWITCH_SEND = "has-switch_send";
  String SHOP_ID = "shop-id";
  String HAS_ADMIN = "has-admin";
  String CHECK_REFRESH_TIME = "check-refresh-time";
  String USER_INFO = "user-info";
  String USER_ID = "user-id";
  String IS_LOGOUT = "is-logout";
  String SIGN_TYPES = "sign-types";
  String USER_RESOURCES = "user_resources";
  String PRINT_DEVICE = "print-device";
  String PRINT_DEVICE_INSTRUCTION = "print-device-instruction";
  String PRINT_TEMPLATE_LIST = "print-template-list";
  String BRAND_OPEN_LIST = "brand-open-list";
  String SELECT_TEMPLATE = "select-print-template";
  String THEME_COLOR = "theme-color";
  String LOCALE = "locale";
  String INDEX_QUERY = "index-query"; // 首页跳转数据页参数
  String UPLOAD_TIME = "upload-time";
  String LAST_CLEAR_CACHE_TIME = "last-clear-cache-time";
  String PICK_CODE_RULE = "pick-code-rule-new";
  String PICK_SHELF_NO = "pick-shelf-no-new";
  String PICK_PRE_SHELF_NO = "pick-pre-shelf-no";
  String PRE_SHELF_List = "pre-shelf-list";
  String SHELFNO_LIST = "shelfNo-list";
  String CHECK_TEXT_TEMP = "check-text-temp";
  String CHECK_TEXT_TEMP2 = "check-text-temp2";
  String NOTICE_TYPE = "notice-type";
  String OCR_TYPE = "ocr-type";
  String OCR_JS_TYPE = "ocr-js-type";
  String APK_PATH = "apk-path-yz";
  String TAKE_CODE = "take-code";
  String HOME_PAGE_REFRESH = "home-page-refresh";
  String DEFALUT_LOC = "default-location";
  String NOW_DAY = "now-day";
  String USER_LIST = "user-list";
  String LOGIN_USER = "login-user";
  String HAS_CHANGE_BRAND = "HAS_CHANGE_BRAND";
  Map EXPRESS = {
    'ZTO': '中通速递',
    'ANE': '安能物流',
    'YTO': '圆通速递',
    'STO': '申通快递',
    'YUNDA': '韵达速递',
    'TTKDEX': '天天快递',
    'HTKY': '百世快递',
    'JT': '极兔速递',
    'UC56': '优速快递',
    'FW': '丰网',
    'SF': '顺丰',
    'EMS': 'EMS',
    'POSTB': '邮政',
    'TM': '天猫',
    'JD': '京东',
    'SNWL': '苏宁',
    'DBKD': '德邦',
    'CAINIAO': '菜鸟',
    'UNKNOW': '其他',
  };
  Map EXPRESS2 = {
    '': '品牌',
    'ZTO': '中通',
    'ANE': '安能',
    'CAINIAO': '菜鸟',
    'UC56': '优速',
    'YTO': '圆通',
    'STO': '申通',
    'YUNDA': '韵达',
    'TTKDEX': '天天',
    'HTKY': '百世',
    'FW': '丰网',
    'JT': '极兔',
    'SF': '顺丰',
    'EMS': 'EMS',
    'POSTB': '邮政',
    'TM': '天猫',
    'JD': '京东',
    'SNWL': '苏宁',
    'DBKD': '德邦',
    'UNKNOW': '其他',
  };
  Map expressChannel = {
    'ZTO': '中通',
    'ANE': '安能',
    'CAINIAO': '菜鸟',
    'UC56': '优速',
    'YTO': '圆通',
    'STO': '申通',
    'YUNDA': '韵达',
    'TTKDEX': '天天',
    'HTKY': '百世',
    'FW': '丰网',
    'JT': '极兔',
    'SF': '顺丰',
    'EMS': 'EMS',
    'POSTB': '邮政',
    'TM': '天猫',
    'JD': '京东',
    'SNWL': '苏宁',
    'DBKD': '德邦',
  };
  Map<dynamic, dynamic> brandMap = {
    'YZ_MM': '妈妈驿站',
    'YZ_TX': '兔喜',
    'YZ_PDD': '多多驿站',
    'YZ_YDCS': '韵达超市',
    'YZ_MZ': '喵站',
  };
  Map<dynamic, dynamic> brandType = {
    '1': '驿站',
    '2': '点位',
  };
  Map smsStateMap = {"": "全部", "WAIT": "待发送", "RUN": "发送中", "OK": "发送成功", "FAIL": "发送失败"};
  Map keepMap = {"": "全部", "Y": "滞留件", "N": "非滞留件"};
  Map ymdMap = {"": "全部", "today": "今日", "yesterDay": "昨日"};
  Map isNew = {"": "全部", "Y": "新客户", "N": "老客户"};
  Map deliveryMap = {"": "全部", "WAIT": "待上传", "OK": "已上传", "FAIL": "上传失败", "RUN": "正在上传"};
  Map deliveryMap_color = {"": "全部", "WAIT": Colors.grey, "OK": Colors.green, "FAIL": Colors.redAccent};
  Map EXPRESS_CHART_COLOR = {
    'ZTO': Color(0xFF015eac),
    'YTO': Color(0xFF490870),
    'STO': Color(0xFF666465),
    'YUNDA': Color(0xFFFFCF01),
    'TTKDEX': Color(0xFF00b0ec),
    'HTKY': Color(0xFFFFE1E3),
    'JT': Color(0xFFed1b24),
    'SF': Color(0xF000000),
    'EMS': Color(0xFF2984ed),
    'POSTB': Color(0xFF008738),
    'TM': Color(0xFFdc0805),
    'JD': Color(0xFFbe0002),
    'SNWL': Color(0xFFf9b200),
    'DBKD': Color(0xFF003c8d),
    'UNKNOW': Color(0xFFF5F5F5),
  };

  Map SCAN_TYPE = {'DP': '到派合一', 'PJ': '派件', 'QS': '签收', 'DS': '到件', 'WT': '问题件', 'FW': '发往'};
  Map PICKCODERULES = {
    'end_four_sheetNo': '单号尾号(4位)',
    'end_four_phone': '手机尾号(4位)',
    'date_no': '日期(2位) + 编号(4位)',
    'shelf_no': '自由货架 + 日期(2位) + 编号(2位)',
    'shelf_no_end_four_sheetNo': '自由货架 + 单号尾号(4位)',
    'shelf_no_end_four_phone': '自由货架 + 手机尾号(4位)',
    'preset_shelf_no_fetch': '预设货架 + 日期(2位) + 编号(2位)',
    'preset_shelf_no_end_four_sheetNo': '预设货架 + 单号尾号(4位)',
    'preset_shelf_no_end_four_phone': '预设货架 + 手机尾号(4位)',
    'preset_shelf_no': '预设货架 + 日期(2位) + 自由编号(2位)',
  };
  Map PICKCODERULES_SHORT = {
    'end_four_sheetNo': '单号尾号',
    'end_four_phone': '手机尾号',
    'date_no': '日期+编号',
    'shelf_no': '自由货架+日期+编号',
    'shelf_no_end_four_sheetNo': '自由货架+单号尾号',
    'shelf_no_end_four_phone': '自由货架+手机尾号',
    'preset_shelf_no_fetch': '预设货架+日期+编号',
    'preset_shelf_no_end_four_sheetNo': '预设货架+单号尾号',
    'preset_shelf_no_end_four_phone': '预设货架+手机尾号',
    'preset_shelf_no': '预设货架+日期+自由编号',
  };
  Map STORAGEMODE = {
    'DS': '驿站代收',
  };
  Map PACKAGE_STATE = {'WQJ': '未取件', 'ZLJ': '滞留件', 'YQJ': '已取件'};
  Map PACKAGE_STATE_COLOR = {'WQJ': Colors.grey, 'ZLJ': Colors.orange, 'YQJ': Colors.green};
  Map WAYBILL_TYPE = {
    0: '正常件',
    1: '到付件',
    2: '代收货款',
    3: '拦截件',
    4: '精准件',
    5: '隐私件',
    6: 'VIP',
    7: '生鲜件',
    8: '电联件',
    9: '派送件',
    10: '圆准达派送件'
  };

  Map ORDER_TYPE = {
    1:'派件单',
    2:'暂存单',
    3:'寄件单'
  };

  Map COURIER_SHOP_STATUS = {
    0:'待确认',
    1:'已生效',
    2:'已拒绝',
    3:'已失效'
  };

  Map KEEP_DAY = {
    '0': '无',
    '1': '1天',
    '2': '2天',
    '3': '3天',
    '4': '4天',
    '5': '5天',
    '6': '6天',
    '7': '7天',
  };
  Map CABINET_BOX_TYPE = {
    0: '极大',
    1: '超大',
    2: '大格',
    3: '中格',
    4: '小格',
    5: '超小',
    6: '极小',
  };
  Map CABINET_BOX_TYPE2 = {
    0: '极大',
    1: '超大',
    2: '大',
    3: '中',
    4: '小',
    5: '超小',
    6: '极小',
  };
  Map CABINET_BOX_TYPE_VALUE = {
    '极大格口': 0,
    '超大格口': 1,
    '大格口': 2,
    '中格口': 3,
    '小格口': 4,
    '超小格口': 5,
    '极小格口': 6,
  };

  Map CABINET_BOX_WORK_STATUS_COLOR = {
    1: Colors.white, //空
    2: Color(0xFFCCFF90), //派件
    3: Colors.orange, //寄存
    4: Colors.greenAccent, //暂存
    5: Colors.blue, //其它
  };
  Map CABINET_BOX_SPECIAL_STATUS_ICON = {
    4: Icons.https, //锁定
    5: Icons.warning_rounded, // 故障
  };
  Color KEEP_EFFECT_COLOR = Color(0xFFFF8A80);
  Color BOX_STATUS0_COLOR = Colors.red;
  Map WAYBILL_STATUS_COLOR = {
    'DCK': Colors.orangeAccent,
    'YCK': Colors.green,
    'THJ': Colors.redAccent,
    'ZLJ': Colors.orange,
    'QSDCK': Colors.deepOrangeAccent
  };
  Map BUSINESS_TYPE = {
    'PJ_YZQS': '派件+驿站签收',
    'YZQS': '驿站签收',
    'PJ_KDYQS': '派件+快递员签收',
    'KDYQS': '快递员签收',
    'NONE': '仅入库',
    'YUNDA': '代收',
  };

  Map TRADE_TYPE_MAP = {'0': '全部', '1': '收入', '2': '支出', '3': '充值', '4': '消费'};
  Map WITHDRAWAL_TYPE_MAP = {null: '提现类型', '1': '待审核', '2': '待打款', '3': '已打款', '4': '已驳回'};
  Map NOTICE_TYPE_MAP = {1: '消息通知', 2: '紧急通知', 3: '活动通知'};
  Map PAY_TYPE_MAP = {1: '微信支付', 2: '支付宝支付', 3: '余额支付'};
  Map TRADE_SUBJECT_MAP = {
    1: '短信费',
    2: '暂存费',
    3: '寄件费',
    4: '余额转入',
    5: '账户提现',
    6: '账户充值',
    7: '投柜费',
    8: '佣金收入',
    9: '用户超期费',
    10: '暂存超期费',
    11: '微信通知费',
    12: '调整余额',
    13: '代发短信费	',
    14: '许可证',
    15: '服务费',
    16: '预约费',
    17: '租用费',
    18: '补到派费',
    19: '日服务费',
    20: '代入库费',
    21: '金额预警短信费',
    22: '流量卡流量充值费',
    23: '流量卡续期费',
  };
  Map CUSTOMER_LABEL = {
    'VIP': 'VIP',
    'RED': '标红',
    'YELLOW': '标黄',
    'SENSITIVE': '敏感',
    '敏感': '拉黑',
  };
  Map OUTBOUND_TYPE = {
    1: '扫码取件',
    2: '取件码取件',
    3: '快递员取出退回',
    4: '门店出库',
    5: '门店取出退回',
    6: '管理员远程出库',
    7: '管理员远程取出退回',
  };
  Map BDP_TYPE = {
    0: '无',
    1: '待投递',
    2: '处理中',
    3: '成功',
    4: '失败',
  };
  Map<String, dynamic> DEVICE_BLUETOOTH = {
    'QR': {'img': 'static/images/qrPrint.png', 'zlType': 'TSC'},
    'HM': {'img': 'static/images/hyPrint.png', 'zlType': 'CPCL'},
    'M8': {'img': 'static/images/hyPrint.png', 'zlType': 'CPCL'},
    'PDD': {'img': 'static/images/dyyPrint.png', 'zlType': 'CPCL'},
    'CS3': {'img': 'static/images/cs3Print.png', 'zlType': 'TSC'},
    'B3S': {'img': 'static/images/jcPrint.png', 'zlType': 'TSC'},
    'PP802': {'img': 'static/images/xcPrint.png', 'zlType': 'CPCL'}
  };
  Map<int, String> messageStateMap = {
    0: '无',
    1: '待投递',
    2: '处理中',
    3: '成功',
    4: '失败',
  };

  Map<int, String> orderStateMap = {
    1: '使用中',
    11: '已取消',
    12: '已完成',
  };

  Map<int, String> orderListStatus = {
    0: '待使用',
    1: '已使用',
    2: '已过期',
  };

  Map<String, String> shopAutoSignDay = {
    '1': '1天',
    '2': '2天',
    '3': '3天',
    '4': '4天',
    '5': '5天',
    '6': '6天',
    '7': '7天',
  };

  Map<dynamic, dynamic> listTypeState = {PackageType.ZL: 2, PackageType.DQ: 3, PackageType.YQC: 1};
  String DEFAULT_PRINT_IMG = 'static/images/print.png';
  List INSTRUCTION_LIST = ['TSC', 'CPCL'];

  late Color PRIMARY_COLOR;
  late Color PRIMARY_COLOR_LIGHT;
  late String PRIMARY_COLOR_TEXT;
  late Color PRIMARY_COLOR_GHOST;
  late Color PRIMARY_COLOR_SWATCH;

  Color SUCCESS_COLOR = Color(0xFF67B939);
  Color SUCCESS_COLOR_GHOST = Color(0x4867B939);

  Color ERROR_COLOR = Color(0xFFFDA1A1);
  Color ERROR_COLOR_GHOST = Color(0x31ff4d4d);

  Color WARNING_COLOR = Color(0xFFE6A23E);
  Color WARNING_COLOR_GHOST = Color(0x21E6A23E);

  Color INFO_COLOR = Color(0xFFB5B5B5);
  Color INFO_COLOR_GHOST = Color(0x21B5B5B5);

  Color TIFFANY_COLOR = Color(0xFF81d8d0);
  Color TIFFANY_COLOR_GHOST = Color(0x2181d8d0);

  Color DISABLED_COLOR = Color(0x8A000000);
  Color BLACK_COLOR = Color(0x80000000);
  Color DISABLED_COLOR_GHOST = Color(0x21000000);

  Color Border_COLOR = Color(0xFFEEEEEE);
  Color BG_COLOR = Color(0xFFF5F5F5);
  Color WHITE_COLOR = Color(0xFFFFFFFF);

  Color ORANGE_COLOR = Color(0xFFFF5E24);
  Color GREY_COLOR = Color(0xFF585858);
  Color GREY_LIGHT_COLOR = Color(0xFF858585);
  Color GREY_LIGHT1_COLOR = Color(0xFF999999);
  Color BLUE_COLOR = Color(0xFF0066FF);
  Color ORANGE_COLOR_GHOST = Color(0xFFFFE2D8);
}

/// 包裹列表类型
enum ListType {
  WAIT, // 待取
  REMOVED, //已取出
  RETENTION, // 滞留
  SEARCH, // 查询
  ALL, // 全部
  STAY, // 寄件待收
  TAKEN, //寄件已取
  COMPLETE // 已完成
}

/// 扫码动作
enum ScanAction {
  BINDCABINET, // 绑定柜机
  SCANSEARCH, // 扫码查询
  SCANQR, // 扫秒二维码
  SCANTRACKING, // 扫描识别订单号
}

/// 包裹类型
enum PackageType {
  ZL, //滞留
  DQ, //待取
  YQC, //已取出
  DS, //待收
  YS, //已收
  YWC, //已完成
  THJ, //退回件
  WTJ, //问题件
  SEARCH,
  ALL
}

/// 包裹类型
enum CabinetBoxListPageType {
  FJGZ, //附近点位
  KXCX, //空箱查询
  SCGJ, //收藏柜机
}

/// 点位格口使用类型
enum CabinetBoxUseType {
  XGZY, //箱柜租用
  YYGK, //预约格口
  KXCX //空箱查询
}

/// 支付结果类型
enum PayResult { SUCCESS, FAIL }

/// 日期类型
enum DateType { near15Day, monthly }

/// 输入框类型 EntryScan
enum InputType { input, scanView, autoInput }

enum ScanType { CABINET, SHELF }
