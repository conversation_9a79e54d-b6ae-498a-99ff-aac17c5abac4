import 'dart:convert';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/entitys/company_rules_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:shared_preferences/shared_preferences.dart';

///SharedPreferences 本地存储
class LocalStorage {
  static save(String key, value, {bool isPrivate = false}) async {
    if (isPrivate) {
      key = await getKey(key);
    }
    SharedPreferences prefs = await SharedPreferences.getInstance();
    if (value is String) {
      prefs.setString(key, value);
    } else if (value is int) {
      prefs.setInt(key, value);
    } else if (value is bool) {
      prefs.setBool(key, value);
    } else {
      try {
        value = jsonEncode(value);
        prefs.setString(key, value);
      } catch (e) {
        print(e.toString());
      }
    }
  }

  static Future get<T>(String key, {bool isPrivate = false}) async {
    try {
      if (isPrivate) {
        key = await getKey(key);
      }
      SharedPreferences prefs = await SharedPreferences.getInstance();
      var data = prefs.get(key);
      if (data != null) {
        if (key == DefaultConfig().configs.COMPANY_RULE) {
          //先将json字符串转json
          List<CompanyRulesEntity?>? companyRules =
              jsonConvert.convertList<CompanyRulesEntity>(jsonDecode(data.toString()));
          return companyRules;
        } else if (T.toString() != "dynamic" &&
            T.toString() != "String" &&
            T.toString() != "int" &&
            T.toString() != "bool") {
          return JsonConvert.fromJsonAsT(jsonDecode(data.toString()));
        }
        return data;
      } else {
        var defaultValue;
        switch (T.toString()) {
          case "String":
            defaultValue = "";
            break;
          case "int":
            defaultValue = 0;
            break;
          case "bool":
            defaultValue = false;
            break;
        }
        return defaultValue;
      }
    } catch (e) {
      return null;
    }
  }

  static getJson(String key, {bool isPrivate = false}) async {
    try {
      if (isPrivate) {
        key = await getKey(key);
      }
      SharedPreferences prefs = await SharedPreferences.getInstance();
      return jsonDecode(prefs.get(key).toString());
    } catch (e) {
      return null;
    }
  }

  static getKey(String key) async {
    try {
      String? info = await LocalStorage.get(DefaultConfig().configs.USER_INFO);
      UserEntity? userInfo;
      if(info!= null){
        userInfo = JsonConvert.fromJsonAsT(jsonDecode(info.toString()));
      }
      if (userInfo != null) {
        return userInfo.id! + '-' + key;
      } else {
        return key;
      }
    } catch (e) {
      return key;
    }
  }

  static remove(String key, {bool isPrivate = false}) async {
    if (isPrivate) {
      key = await getKey(key);
    }
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.remove(key);
  }

  static clear() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.clear();
  }
}
