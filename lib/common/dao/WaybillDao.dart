import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/net/Address.dart';
import 'package:cabinet_flutter_app/common/net/HttpManager.dart';
import 'package:cabinet_flutter_app/common/style/AppStyle.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:fluttertoast/fluttertoast.dart';

import 'CourierDao.dart';
import 'DaoResult.dart';

class WaybillDao {
  /// 重新通知方法
  /// * @param [context]  BuildContext
  /// * @param [mobile] 手机号
  /// * @param [orderId]  订单id
  /// * @param [isForce]  是否显示弹框
  static reNotice(BuildContext context, String mobile, String orderId, {bool isForce = false}) {
    if (isForce) {
      notice(mobile, orderId);
      return;
    }
    CommonUtils.customConfirm(context, '是否给客户再次发送取件通知？', title: '重新通知', showClose: false, actions: <Widget>[
      Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
            },
            child: Text('取消', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
            style: ButtonStyle(
                foregroundColor: MaterialStateProperty.resolveWith((states) {
                  return Colors.black;
                }),
                backgroundColor: MaterialStateProperty.resolveWith((states) {
                  return DefaultConfig().configs.WHITE_COLOR;
                }),
                minimumSize: MaterialStateProperty.all(Size(90, 40)))),
        TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              notice(mobile, orderId);

              /// todo
            },
            child: Text('是的', style: TextStyle(fontSize: AppConstant.middleTextWhiteSize)),
            style: ButtonStyle(
                foregroundColor: MaterialStateProperty.resolveWith((states) {
                  return Theme.of(context).primaryColor;
                }),
                backgroundColor: MaterialStateProperty.resolveWith((states) {
                  return Colors.white;
                }),
                minimumSize: MaterialStateProperty.all(Size(90, 40)))),
      ])
    ]);
  }

  static notice(phone, order) async {
    // bool matched = RegExpUtil.isPhone(phone);
    // if (!matched) {
    //   Fluttertoast.showToast(msg: '手机号格式错误');
    //   return;
    // }
    EasyLoading.show(status: '正在发送...', maskType: EasyLoadingMaskType.black);
    var res = await CourierDao.courierOrderResendSms(order);
    EasyLoading.dismiss();
    if (res.result) {
      Fluttertoast.showToast(msg: '重发成功');
      await Future.delayed(Duration(milliseconds: 100));
    }
  }

  /// 校验快递单号 3.0
  static waybillCheck(String cabinetLocationCode, String waybillNo, String brandCode,
      {String ocrTexts = '',
      String extraParam = '',
      String ocrName = '',
      String ocrPhone = '',
      String ocrAddress = ''}) async {
    Map<String, dynamic> info = {
      'cabinetLocationCode': cabinetLocationCode,
      'waybillNo': waybillNo,
      'brandCode': brandCode,
      'ocrTexts': ocrTexts,
      'extraParam': extraParam,
    };
    var res = await HttpManager.post(Waybill.waybillCheck, info, version: ApiVersion.V3);
    return DataResult(res.data, res.result);
  }

  /// 通过单号猜客户手机号1.0
  static mobileGuss(
    String cabinetLocationCode,
    String waybillNo,
    String brandCode,
  ) async {
    Map<String, dynamic> info = {
      'cabinetLocationCode': cabinetLocationCode,
      'waybillNo': waybillNo,
      'brandCode': brandCode
    };
    var res = await HttpManager.post(Waybill.mobileGuss, info);
    return DataResult(res.data, res.result);
  }

  /// 重新计费1.0
  static reCalculateFee(
    String cabinetLocationCode,
    String orderId,
  ) async {
    Map<String, dynamic> info = {'cabinetLocationCode': cabinetLocationCode, 'orderId': orderId};
    var res = await HttpManager.post(Waybill.reCalculateFee, info);
    return DataResult(res.data, res.result);
  }

  /// 通过带星手机号获取客户列表2.0
  static mobileGuss2(String cabinetLocationCode, String mobile) async {
    Map<String, dynamic> info = {'cabinetLocationCode': cabinetLocationCode, 'mobile': mobile};
    var res = await HttpManager.post(Waybill.mobileGuss, info, version: ApiVersion.V2);
    return DataResult(res.data, res.result);
  }

  /// 通过手机号后四位获取客户列表1.0
  static mobileGussbyLast4(String cabinetLocationCode, String mobile) async {
    Map<String, dynamic> info = {'cabinetLocationCode': cabinetLocationCode, 'lastFour': mobile};
    var res = await HttpManager.post(Waybill.mobileGussbyLast4, info);
    return DataResult(res.data, res.result);
  }
}
