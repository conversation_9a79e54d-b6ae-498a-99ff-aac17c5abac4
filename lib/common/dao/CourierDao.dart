// ignore_for_file: unnecessary_null_comparison

import 'dart:io';

import 'package:cabinet_flutter_app/common/config/ConfigBase.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/entitys/package_view_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/package_view_sj_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/scan_item_entity.dart';
import 'package:cabinet_flutter_app/common/net/Address.dart';
import 'package:cabinet_flutter_app/common/net/HttpManager.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/DebounceUtils.dart';
import 'package:cabinet_flutter_app/common/utils/RegExpUtil.dart';
import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';

import 'DaoResult.dart';

class CourierDao {
  /// 绑定点位
  static bindQr(Map info) async {
    var res = await HttpManager.post(Courier.bindQr, info);
    return DataResult(res.data, res.result);
  }

  /// 获取快递员与点位关系
  static getCourierCabinetStatus(String code) async {
    var res = await HttpManager.post(
        Courier.courierCabinetStatus, {'cabinetLocationCode': code});
    return DataResult(res.data, res.result);
  }

  /// 获取快递员与点位关系
  static getCourierCabinetFullInfo(String code) async {
    var res =
        await HttpManager.post(Courier.fullInfo, {'cabinetLocationCode': code});
    return DataResult(res.data, res.result);
  }

  /// 获取快递员在柜的订单数量汇总
  static getCabinetWaybillSummary(String code) async {
    var res = await HttpManager.post(
        Courier.courierCabinetWaybillSummary, {'cabinetLocationCode': code});
    return DataResult(res.data, res.result);
  }

  /// 驿站移除快递员驿站合作
  static shopCourierDelShop(String id) async {
    Map<String, dynamic> info = {'id': id};
    var res = await HttpManager.post(Courier.shopCourierDelShop, info);
    return DataResult(res.data, res.result);
  }

  /// 快递员移除快递员驿站合作
  static shopCourierDelCourier(String id) async {
    Map<String, dynamic> info = {'id': id};
    var res = await HttpManager.post(Courier.shopCourierDelCourier, info);
    return DataResult(res.data, res.result);
  }

  /// 获取快递员在柜的寄件订单数量汇总
  static getCabinetWaitTakenSummary({String? cabinetLocationCode}) async {
    var res = await HttpManager.post(Courier.getCabinetWaitTakenSummary,
        {'cabinetLocationCode': cabinetLocationCode});
    return DataResult(res.data, res.result);
  }

  /// 收藏点位
  static setCabinetCollected(String code, bool isCollect) async {
    var res = await HttpManager.post(Courier.courierCollectCabinet,
        {'cabinetLocationCode': code, 'isCollect': isCollect});
    return DataResult(res.data, res.result);
  }

  /// 通过手机号获取用户标签
  static courierMobileCheck(String phone,
      {String? cabinetLocationCode, bool? noCheckOrder}) async {
    Map<String, dynamic> info = {'mobile': phone, "noCheckOrder": noCheckOrder};
    if (cabinetLocationCode != null && cabinetLocationCode != '') {
      info.addAll({'cabinetLocationCode': cabinetLocationCode});
    }
    var res = await HttpManager.post(Courier.courierMobileCheck, info);
    return DataResult(res.data, res.result);
  }

  /// 通过手机号获取用户标签并等待结果
  static Future<bool> checkMobilePhone(String phone,
      {String? cabinetLocationCode}) async {
    try {
      var result = await courierMobileCheck(phone,
          cabinetLocationCode: cabinetLocationCode);
      return result.result;
    } catch (e) {
      print("手机号检查错误: $e");
      return false;
    }
  }

  /// 快递员存件并开门
  static courierOrderWaybillCreate(ScanItemEntity scan, String lastCabinetBoxId,
      int? hasDp, String? yzSopAccountId, String? yzChannel,
      {bool isBatch = false, hostIndex = ''}) async {
    if (scan.waybillNo == '') {
      Fluttertoast.showToast(msg: '未识别快递单号号');
      return;
    }
    if (scan.brandCode == '' || scan.brandCode == null) {
      Fluttertoast.showToast(msg: '未识别快递品牌');
      return;
    }
    bool isPhone = RegExpUtil.checkPhone(scan.receiverMobile);
    if (!isPhone) {
      Fluttertoast.showToast(msg: '请输入正确手机号');
      return;
    }
//  // 等待手机号验证
//   bool mobileValid = await checkMobilePhone(
//     scan.receiverMobile,
//     cabinetLocationCode: scan.cabinetLocationCode
//   );
//   if (!mobileValid) {
//     Fluttertoast.showToast(msg: '手机号验证失败');
//     return ;
//   }
    Map<String, dynamic> info = scan.toJson();
    // 添加必要的字段把虚拟号码和分机号传给后端
    if (scan.virtualPhone != '') {
      //虚拟手机号
      info.addAll({'virtualPhone': scan.virtualPhone});
    }
    print("虚拟手机号: ${scan.virtualPhone}");
    if (lastCabinetBoxId != '') {
      info.addAll({'latestCabinetBoxId': lastCabinetBoxId});
    }
    info.addAll({'hasBatch': isBatch});
    info.addAll({'hostIndex': hostIndex});
    info.addAll({'hasDp': hasDp});
    info.addAll({'yzChannel': yzChannel});
    info.addAll({'yzSopAccountId': yzSopAccountId});
    String _version = await CheckUtils.getVersion();
    bool isIos = Platform.isIOS;
    if (isIos) {
      info.addAll({'deviceVersion': 'IOS-' + _version});
    } else {
      info.addAll({'deviceVersion': 'Android-' + _version});
    }
    try {
      // 使用新的防抖方式
      return await DebounceUtils.throttleWithCallback(
        key: 'courierOrderWaybillCreate_${scan.waybillNo}',
        func: () async {
          print("快递员存件开门:次数${info['waybillNo']}");
          var res = await HttpManager.post(Courier.courierOrderWaybillCreate, info);
          // 成功后立即显示提示
          if (res.result) {
            Fluttertoast.showToast(msg: '存件成功');
          }
          return DataResult(res.data, res.result);
        },
        duration: 2000,
      );
    } catch (e) {
      print("存件错误: $e");
      return DataResult(e.toString(), false);
    }
  }

  /// 快递员存件开门
  // static courierOpenDoor(String cabinetCode, int boxType, waybillNo, brandCode, receiverMobile,
  //     {String? lastCabinetBoxId}) async {
  //   if (waybillNo == '' || waybillNo == null) {
  //     Fluttertoast.showToast(msg: '未识别快递单号号');
  //     return;
  //   }
  //   if (brandCode == '' || brandCode == null) {
  //     Fluttertoast.showToast(msg: '未识别快递品牌');
  //     return;
  //   }
  //   if (receiverMobile == '' || receiverMobile == null) {
  //     Fluttertoast.showToast(msg: '未识别手机号');
  //     return;
  //   }
  //   Map<String, dynamic> info = {
  //     'cabinetLocationCode': cabinetCode,
  //     'boxType': boxType,
  //     'waybillNo': waybillNo,
  //     'brandCode': brandCode,
  //     'receiverMobile': receiverMobile
  //   };
  //   if (CheckUtils.isNotNull(lastCabinetBoxId)) {
  //     info.addAll({'lastCabinetBoxId': lastCabinetBoxId});
  //   }
  //   var res = await HttpManager.post(Courier.courierOpenDoor, info);
  //   return DataResult(res.data, res.result);
  // }

  /// 门店入柜订单创建并开门
  static courierShopOpenDoor(
    cabinetLocationCode,
    cabinetId,
    cabinetBoxId,
    waybillNo,
    brandCode,
    receiverName,
    receiverMobile,
    virtualPhone,
    secretWaybill,
    substituteSmsWaybill,
    sensitiveConsumer,
    newCustomer,
    needCallCustomer,
    cod,
    platform,
    yzSopAccountId,
    yzChannel,
    virtualNumber,
    inboundImageUrl, {
    bool? addInCabinet,
    int? deliveryWaybill,
    int? hasDp,
    bool isBatch = false,
  }) async {
    print("virtualPhone: $virtualPhone");
    if (waybillNo == '' || waybillNo == null) {
      Fluttertoast.showToast(msg: '未识别快递单号号');
      return;
    }
    if (brandCode == '' || brandCode == null) {
      Fluttertoast.showToast(msg: '未识别快递品牌');
      return;
    }
    bool isPhone = RegExpUtil.checkPhone(receiverMobile);
    if (!isPhone) {
      Fluttertoast.showToast(msg: '请输入正确手机号');
      return;
    }
    String _version = await CheckUtils.getVersion();
    bool isIos = Platform.isIOS;
    String deviceVersion = '';
    if (isIos) {
      deviceVersion = 'IOS-' + _version;
    } else {
      deviceVersion = 'Android-' + _version;
    }
    Map<String, dynamic> info = {
      'cabinetLocationCode': cabinetLocationCode,
      'cabinetId': cabinetId,
      'cabinetBoxId': cabinetBoxId,
      'addInCabinet': addInCabinet,
      'deliveryWaybill': deliveryWaybill,
      'waybillNo': waybillNo,
      'brandName': DefaultConfig().configs.EXPRESS2[brandCode],
      'brandCode': brandCode,
      'receiverMobile': receiverMobile,
      'virtualPhone': virtualPhone, //虚拟手机号
      'receiverName': receiverName,
      'secretWaybill': secretWaybill,
      'hasSubstituteSms': substituteSmsWaybill,
      'sensitiveConsumer': sensitiveConsumer,
      'newCustomer': newCustomer,
      'needCallCustomer': needCallCustomer,
      'cod': cod,
      'platform': platform,
      'yzSopAccountId': yzSopAccountId,
      'yzChannel': yzChannel,
      'inboundImageUrl': inboundImageUrl,
      'virtualNumber': virtualNumber,
      'hasBatch': isBatch,
      'hasDp': hasDp,
      'deviceVersion': deviceVersion,
    };
      try {
      // 使用新的防抖方式
      return await DebounceUtils.throttleWithCallback(
        key: 'courierOrderWaybillCreate_${waybillNo}',
        func: () async {
          print("驿站存件开门:次数${info['waybillNo']}");
           var res = await HttpManager.post(Courier.courierShopOpenDoor, info);
          // 成功后立即显示提示
          return DataResult(res.data, res.result);
        },
        duration: 1000,
      );
    } catch (e) {
      print("存件错误: $e");
      return DataResult(e.toString(), false);
    }
  }

  /// 门店存件获取下个格口
  static courierShopOpenNextDoor(cabinetId, {cabinetBoxType}) async {
    Map<String, dynamic> info = {
      'cabinetBoxId': cabinetId,
    };
    if (cabinetBoxType != null) {
      info.putIfAbsent('cabinetBoxType', () => cabinetBoxType);
    }
    var res = await HttpManager.post(Courier.courierShopOpenNextDoor, info);
    return DataResult(res.data, res.result);
  }

  /// 门店入仓扫描入库
  static courierShopIntoDoor(
      shelfId,
      shelfName,
      checkCode,
      waybillNo,
      receiverName,
      brandCode,
      receiverMobile,
      secretWaybill,
      substituteSmsWaybill,
      sensitiveConsumer,
      newCustomer,
      needCallCustomer,
      cod,
      platform,
      yzSopAccountId,
      yzChannel,
      virtualNumber,
      inboundImageUrl,
      deliveryWaybill,
      deliverDoor) async {
    if (waybillNo == '' || waybillNo == null) {
      Fluttertoast.showToast(msg: '未识别快递单号号');
      return;
    }
    if (brandCode == '' || brandCode == null) {
      Fluttertoast.showToast(msg: '未识别快递品牌');
      return;
    }
    if (receiverMobile == '' || receiverMobile == null) {
      Fluttertoast.showToast(msg: '未识别手机号');
      return;
    }

    bool isPhone = RegExpUtil.checkPhone(receiverMobile);
    if (!isPhone) {
      Fluttertoast.showToast(msg: '请输入正确手机号');
      return;
    }
    Map<String, dynamic> info = {
      'shelfId': shelfId,
      'shelfName': shelfName,
      'checkCode': checkCode,
      'waybillNo': waybillNo,
      'receiverName': receiverName,
      'brandCode': brandCode,
      'receiverMobile': receiverMobile,
      'secretWaybill': secretWaybill,
      'hasSubstituteSms': substituteSmsWaybill,
      'sensitiveConsumer': sensitiveConsumer,
      'newCustomer': newCustomer,
      'needCallCustomer': needCallCustomer,
      'cod': cod,
      'platform': platform,
      'yzSopAccountId': yzSopAccountId,
      'yzChannel': yzChannel,
      'virtualNumber': virtualNumber,
      'inboundImageUrl': inboundImageUrl,
      'deliveryWaybill': deliveryWaybill,
      'deliverDoor': deliverDoor // 是否送货上门
    };
    var res = await HttpManager.post(Courier.courierShopIntoDoor, info);
    return DataResult(res.data, res.result);
  }

  /// 门店出仓扫描出库
  static courierShopOutDoor(waybillNo, {outboundImageUrl}) async {
    if (waybillNo == '' || waybillNo == null) {
      Fluttertoast.showToast(msg: '未识别快递单号号');
      return;
    }
    Map<String, dynamic> info = {
      'waybillNo': waybillNo,
      'outboundImageUrl': outboundImageUrl
    };
    var res = await HttpManager.post(Courier.courierShopOutDoor, info);
    return DataResult(res.data, res.result);
  }

  /// 拒收
  static resendOrderBack(orderId) async {
    Map<String, dynamic> info = {'orderId': orderId, 'action': 'back'};
    var res = await HttpManager.post(Courier.resendOrderBack, info);
    return DataResult(res.data, res.result);
  }

  /// 获取点位列表
  static shopCabinetLocation() async {
    var res = await HttpManager.post(Courier.shopCabinetLocation, null);
    return DataResult(res.data, res.result);
  }

  /// 我的关联点位1
  static getMyCabinetList() async {
    var res = await HttpManager.post(Courier.myCabinetLocationList, null);
    return DataResult(res.data, res.result);
  }

  /// 分页驿站合作列表
  static getCourierShopList(Map info) async {
    var res = await HttpManager.post(Courier.courierShopPage, info);
    return DataResult(res.data['records'], res.result,
        total: res.result ? int.parse(res.data['total']) : 0);
  }

  /// 创建驿站合作1.0
  static createShopCourier(Map info) async {
    var res = await HttpManager.post(Courier.courierShopCreate, info);
    return DataResult(res.data, res.result);
  }

  /// 修改驿站合作价格1.0
  static courierShopChangePrice(Map info) async {
    var res = await HttpManager.post(Courier.courierShopChangePrice, info);
    return DataResult(res.data, res.result);
  }

  /// 处理快递员驿站合作1.0
  static reviewShopCourier(Map info) async {
    var res = await HttpManager.post(Courier.courierShopReview, info);
    return DataResult(res.data, res.result);
  }

  /// 快递员创建入柜订单
  static courierOrderCreate(ScanItemEntity item) async {
    Map<String, dynamic> info = item.toJson();
    var res = await HttpManager.post(Courier.courierOrderCreate, info);
    return DataResult(res.data, res.result);
  }

  /// 货架列表
  static courierShopShelfList() async {
    var res = await HttpManager.post(Courier.courierShopShelfList, null);
    return DataResult(res.data, res.result);
  }

  /// 货架保存/更新
  static courierShopShelfSave(name) async {
    Map<String, dynamic> info = {
      'name': name,
    };
    var res = await HttpManager.post(Courier.courierShopShelfSave, info);
    return DataResult(res.data, res.result);
  }

  /// 根据货架名称获取取件码
  static courierUserCode(id, bool isDelivery) async {
    Map<String, dynamic> info = {
      'shelfId': id,
      'isDeliveryWaybill': isDelivery,
    };
    var res = await HttpManager.post(Courier.courierUserCode, info);
    return DataResult(res.data, res.result);
  }

  /// 货架删除
  static courierShopShelfDelete(id) async {
    Map<String, dynamic> info = {
      'id': id,
    };
    var res = await HttpManager.post(Courier.courierShopShelfDelete, info);
    return DataResult(res.data, res.result);
  }

  /// 提交反馈
  static submitFeedback(Map<String, dynamic> params) async {
    var res = await HttpManager.post(Courier.feedbackCreate, params);
    return DataResult(res.data, res.result);
  }

  /// 查询反馈列表
  /// 可以传入userId参数，如果不传则使用null
  static queryFeedbackList() async {
    var res = await HttpManager.post(Courier.feedbackQuery,null);
    return DataResult(res.data, res.result);
  }

  /// 获取点位订单
  /// * @param [code] 点位编码
  /// * @param [brandCode] 快递品牌
  /// * @param [status] 滞留件状态 1：全部, 2：滞留件, 3：非滞留件
  static courierCabinetOrderList(String code, String? brandCode,
      {ListType status = ListType.ALL, String? time}) async {
    Map<ListType, int> statusMap = {
      ListType.ALL: 1,
      ListType.RETENTION: 2,
      ListType.WAIT: 3
    };
    Map<String, dynamic> info = {
      'cabinetLocationCode': code,
      'brandCode': brandCode == 'ALL' ? null : brandCode,
      'keepEffectStatus': statusMap[status]
    };
    if (time != null) {
      int keepEffectDay = 1;
      switch (time) {
        case 'oneDay':
          keepEffectDay = 1;
          break;
        case 'twoDays':
          keepEffectDay = 2;
          break;
        case 'before3Days':
          keepEffectDay = 3;
          break;
        case 'before7Days':
          keepEffectDay = 7;
          break;
        case 'all':
          keepEffectDay = 0;
          break;
      }
      info.putIfAbsent('keepEffectDay', () => keepEffectDay);
    }
    var res = await HttpManager.post(Courier.courierCabinetOrderList, info);
    return DataResult(res.data, res.result, total: res.data.length);
  }

  /// 分页获取点位订单
  // * @param [code] 点位编码
  // * @param [brandCode] 快递品牌
  // * @param [status] 滞留件状态 1：全部, 2：滞留件, 3：非滞留件
  static courierCabinetOrderPagList(query) async {
    print('query: $query');
    if (query['timeRanger'] != null) {
      switch (query['timeRanger']) {
        case 'near3Days':
          query['beginYmd'] = DateFormat("yyyy-MM-dd")
              .format(DateTime.now().subtract(Duration(days: 3)));
          break;
        case 'near5Days':
          query['beginYmd'] = DateFormat("yyyy-MM-dd")
              .format(DateTime.now().subtract(Duration(days: 5)));
          break;
        case 'near7Days':
          query['beginYmd'] = DateFormat("yyyy-MM-dd")
              .format(DateTime.now().subtract(Duration(days: 7)));
          break;
        case 'near14Days':
          query['beginYmd'] = DateFormat("yyyy-MM-dd")
              .format(DateTime.now().subtract(Duration(days: 14)));
          break;
        case 'near30Days':
          query['beginYmd'] = DateFormat("yyyy-MM-dd")
              .format(DateTime.now().subtract(Duration(days: 30)));
          break;
        case 'near90Days':
          query['beginYmd'] = DateFormat("yyyy-MM-dd")
              .format(DateTime.now().subtract(Duration(days: 90)));
          break;
      }
    }
    var res = await HttpManager.post(Courier.cabinetOrderPage, query);
    return DataResult(res.result ? res.data['records'] : [], res.result,
        total: res.result ? int.parse(res.data['total']) : 0);
  }

  /// //我的派件已出库列表
  static courierOutboundOrderList(String ymd, String current, String size,
      {String? cabinetLocationCode, String? brandCode}) async {
    Map<String, dynamic> info = {'ymd': ymd, 'current': current, 'size': size};
    if (brandCode != null) {
      info.addAll({'brandCode': brandCode == 'ALL' ? null : brandCode});
    }
    if (cabinetLocationCode != null && cabinetLocationCode != '') {
      info.addAll({'cabinetLocationCode': cabinetLocationCode});
    }
    var res = await HttpManager.post(Courier.courierOutboundOrderList, info);
    return DataResult(
        jsonConvert.convertList<PackageViewEntity>(res.data['records']),
        res.result,
        total: int.parse(res.data['total']));
  }

  /// //我的收件-待收列表
  static courierSjCabinetOrderPagList(String current, String size,
      {String? cabinetLocationCode}) async {
    Map<String, dynamic> info = {
      'cabinetLocationCode': cabinetLocationCode,
      'current': current,
      'size': size
    };
    var res =
        await HttpManager.post(Courier.courierSjCabinetOrderPagList, info);
    return DataResult(
        jsonConvert.convertList<PackageViewSjEntity>(res.data['records']),
        res.result,
        total: int.parse(res.data['total']));
  }

  /// //我的收件-已收件
  static courierSjSendTakenPagList(List statusList, String current, String size,
      {String? ymd}) async {
    Map<String, dynamic> info = {
      'ymd': ymd,
      'statusList': statusList,
      'current': current,
      'size': size
    };
    var res = await HttpManager.post(Courier.courierSjSendTakenPagList, info);
    return DataResult(
        jsonConvert.convertList<PackageViewSjEntity>(res.data['records']),
        res.result,
        total: int.parse(res.data['total']));
  }

  /// 快递员取消入柜订单
  /// * @param [cabinetCode] 点位编码
  /// * @param [orderId] 订单ID
  static courierOrderCancel(String cabinetCode, String orderId,
      {String cancelReason = '', String hostIndex = ''}) async {
    Map<String, dynamic> info = {
      'cabinetLocationCode': cabinetCode,
      'orderId': orderId,
      'cancelReason': cancelReason,
      'hostIndex': hostIndex
    };
    var res = await HttpManager.post(Courier.courierOrderCancel, info);
    return DataResult(res.data, res.result);
  }

  /// 取出，开门
  /// * @param [cabinetCode] 点位编码
  /// * @param [orderId] 订单ID
  static courierOutboundWaybill(String cabinetCode, String orderId) async {
    Map<String, dynamic> info = {
      'cabinetLocationCode': cabinetCode,
      'orderId': orderId
    };
    var res = await HttpManager.post(Courier.courierOutboundWaybill, info);
    return DataResult(res.data, res.result);
  }

  /// 异常出库
  /// * @param [cabinetCode] 点位编码
  /// * @param [orderId] 订单ID
  static courierUnusualOutWaybill(
      String cabinetCode, String orderId, String backReason,
      {String hostIndex = ''}) async {
    Map<String, dynamic> info = {
      'cabinetLocationCode': cabinetCode,
      'orderId': orderId,
      'returnReason': backReason,
      'hostIndex': hostIndex
    };
    var res = await HttpManager.post(Courier.courierUnusualOutWaybill, info);
    return DataResult(res.data, res.result);
  }

  /// 寄件取消订单
  /// * @param [cabinetCode] 点位编码
  /// * @param [orderId] 订单ID
  static courierOutboundWaybillSj(String cabinetCode, String orderId,
      String backReason, bool isRefuse) async {
    Map<String, dynamic> info = {
      'cabinetLocationCode': cabinetCode,
      'orderId': orderId,
      'cancelReason': backReason,
      'isRefuse': isRefuse
    };
    var res = await HttpManager.post(Courier.courierOutboundWaybillSj, info);
    return DataResult(res.data, res.result);
  }

  ///  单号回传
  /// * @param [cabinetCode] 点位编码
  /// * @param [orderId] 订单ID
  static courierBackOrderNo(String orderId, String waybillNo) async {
    Map<String, dynamic> info = {
      'orderId': orderId,
      'waybillNo': waybillNo,
    };
    var res = await HttpManager.post(Courier.courierBackOrderNo, info);
    return DataResult(res.data, res.result);
  }

  /// 开门检查
  /// * @param [cabinetBoxId] 柜门id
  static courierBoxOpenCheck(String? cabinetBoxId,
      {String hostIndex = ''}) async {
    Map<String, dynamic> info = {
      'cabinetBoxId': cabinetBoxId,
      'hostIndex': hostIndex
    };
    var res = await HttpManager.post(Courier.courierBoxOpenCheck, info);
    return DataResult(res.data, res.result);
  }

  static boxOpenDoor(String cabinetLocationCode, int serialNo) async {
    Map<String, dynamic> info = {
      'cabinetLocationCode': cabinetLocationCode,
      'serialNo': serialNo,
      'hostIndex': '1',
      'pcbNo': 0
    };
    var res = await HttpManager.post(Courier.remoteOpenBoxCheck, info);
    return DataResult(res.data, res.result);
  }

  // 一键异常出库
  static courierBoxOpenDoor(String cabinetLocationCode, int serialNo) async {
    Map<String, dynamic> info = {
      'cabinetLocationCode': cabinetLocationCode,
      'serialNo': serialNo,
      'hostIndex': '1',
      'pcbNo': 0
    };
    // var res =
    //     await HttpManager.post(Courier.courierUnusualOutWaybillBatch, info);
    // return DataResult(res.data, res.result);
  }

  /// 确认取出
  /// * @param [cabinetBoxId] 柜门id
  static courierTakeOrderSend(String? cabinetBoxId, String orderId) async {
    Map<String, dynamic> info = {
      'cabinetLocationCode': cabinetBoxId,
      'orderId': orderId
    };
    var res = await HttpManager.post(Courier.courierTakeOrderSend, info);
    return DataResult(res.data, res.result);
  }

  /// 重新上传入库物流轨迹
  /// * @param [cabinetBoxId] 柜门id
  static courierGatewayResend(String? orderId) async {
    Map<String, dynamic> info = {'orderId': orderId};
    var res = await HttpManager.post(Courier.courierGatewayResend, info);
    return DataResult(res.data, res.result);
  }

  static resendBdp(String? orderId) async {
    Map<String, dynamic> info = {'orderId': orderId};
    var res = await HttpManager.post(Courier.resendBdp, info);
    return DataResult(res.data, res.result);
  }

  /// 重新上传物流轨迹
  static gatewayResend(String? waybillId, String action) async {
    Map<String, dynamic> info = {'orderId': waybillId, 'action': action};
    var res = await HttpManager.post(Courier.gatewayResend, info);
    return DataResult(res.data, res.result);
  }

  static reInboundChannel(String? waybillId) async {
    Map<String, dynamic> info = {'orderId': waybillId};
    var res = await HttpManager.post(Courier.reInboundChannel, info);
    return DataResult(res.data, res.result);
  }

  ///
  /// 首页滞留件、待取件
  static courierHomeInCabinet() async {
    var res = await HttpManager.post(Courier.courierHomeInCabinet, null);
    return DataResult(res.data, res.result);
  }

  /// 我的派件单汇总
  ///
  /// * @param [status] 滞留件状态 1：全部, 2：滞留件, 3：非滞留件
  static courierCabinetSummaryList({ListType status = ListType.ALL}) async {
    Map<ListType, int> statusMap = {
      ListType.ALL: 1,
      ListType.RETENTION: 2,
      ListType.WAIT: 3
    };
    Map<String, dynamic> info = {'keepEffectStatus': statusMap[status]};
    var res = await HttpManager.post(Courier.courierCabinetSummaryList, info);
    return DataResult(res.data, res.result, total: res.data.length);
  }

  /// 我的派件单汇总-已取出
  static courierCabinetOutboundList(String outboundYmd,
      {String? cabinetLocationCode, String? brandCode}) async {
    Map<String, dynamic> info = {'outboundYmd': outboundYmd};
    var res = await HttpManager.post(Courier.courierCabinetOutboundList, info);
    return DataResult(res.data, res.result, total: res.data.length);
  }

  /// 我的派件单详情
  static courierPackageDetail(cabinetLocationCode, orderId) async {
    Map<String, dynamic> info = {
      'cabinetLocationCode': cabinetLocationCode,
      'orderId': orderId
    };
    var res = await HttpManager.post(Courier.courierPackageDetail, info);
    return DataResult(res.data, res.result);
  }

  /// 用户消息日志
  static getOrderMessageList(orderId) async {
    Map<String, dynamic> info = {'orderId': orderId};
    var res = await HttpManager.post(Courier.courierNoticeLog, info);
    return DataResult(res.data, res.result);
  }

  /// 同步日志
  static getSyncLogList(orderId) async {
    Map<String, dynamic> info = {'orderId': orderId};
    var res = await HttpManager.post(Courier.courierSyncLog, info);
    return DataResult(res.data, res.result);
  }

  /// 订单日志
  static getOrderLogList(orderId) async {
    Map<String, dynamic> info = {'orderId': orderId, 'orderActionType': 0};
    var res = await HttpManager.post(Courier.courierOrderLog, info);
    return DataResult(res.data, res.result);
  }

  /// 修改订单手机号
  static courierEditOrderMobile(cabinetLocationCode, orderId) async {
    Map<String, dynamic> info = {
      'mobile': cabinetLocationCode,
      'orderId': orderId
    };
    var res = await HttpManager.post(Courier.courierEditOrderMobile, info);
    return DataResult(res.data, res.result);
  }

  /// 重发短信
  ///
  ///
  static courierOrderResendSms(orderId) async {
    Map<String, dynamic> info = {'orderId': orderId};
    var res = await HttpManager.post(Courier.courierOrderResendSms, info);
    return DataResult(res.data, res.result);
  }

  /// 获取拦截单列表
  static getInterceptList(Map info) async {
    var res = await HttpManager.post(Courier.interceptList, info);
    return DataResult(res.data['records'], res.result,
        total: int.parse(res.data['total']));
  }

  //添加拦截单号
  static addInterceptNo(Map info) async {
    var res = await HttpManager.post(Courier.interceptAdd, info);
    return DataResult(res.data, res.result);
  }

  // 删除拦截单号
  static removeInterceptNo(String waybillNo) async {
    Map<String, dynamic> info = {'waybillNo': waybillNo};
    var res = await HttpManager.post(Courier.interceptDel, info);
    return DataResult(res.data, res.result);
  }

  /// 分页获取客户信息
  static getCustomerList(Map info) async {
    var res = await HttpManager.post(Courier.customerList, info);
    return DataResult(res.data['records'], res.result,
        total: int.parse(res.data['total']));
  }

  /// 获取已有客户标签列表
  static getCustomerLabelList() async {
    var res = await HttpManager.post(Courier.customerLabelList, null);
    return DataResult(res.data, res.result);
  }

  /// 删除客户标签
  static customerLabelRemove(String label) async {
    Map<String, dynamic> info = {'label': label};
    var res = await HttpManager.post(Courier.customerLabelRemove, info);
    return DataResult(res.data, res.result);
  }

  /// 删除客户
  static customerRemove(String id) async {
    Map<String, dynamic> info = {'id': id};
    var res = await HttpManager.post(Courier.customerRemove, info);
    return DataResult(res.data, res.result);
  }

  /// 更新或添加客户
  static saveCustomer(Map info) async {
    var res = await HttpManager.post(Courier.customerEdit, info);
    return DataResult(res.data, res.result);
  }

  /// 更新客户名称
  static updateCustomer(Map info) async {
    var res = await HttpManager.post(Courier.customerUpdate, info);
    return DataResult(res.data, res.result);
  }

  /// 数据中心-今日出入库收入汇总
  static getTodaySummary([Map? params]) async {
    var res = await HttpManager.post(Courier.todaySummary, params);
    return DataResult(res.data, res.result);
  }

  static getBrandSummary(Map info) async {
    var res = await HttpManager.post(Courier.brandSummary, info);
    return DataResult(res.data, res.result);
  }

  /// 首页汇总1.0
  static getHomeSummary() async {
    var res = await HttpManager.post(Courier.homeSummary, null);
    return DataResult(res.data, res.result);
  }

  /// 数据中心-获取用户滞留件汇总
  static getKeepEffectSummary([Map<String, dynamic>? params]) async {
    var res = await HttpManager.post(Courier.keepEffectSummary, params);
    return DataResult(res.data, res.result);
  }

  /// 数据中心-获取15日入柜数据汇总
  static getHalfMonthInboundSummary([Map<String, dynamic>? params]) async {
    var res = await HttpManager.post(Courier.halfMonthInboundSummary, params);
    return DataResult(res.data, res.result);
  }

  /// 数据中心-获取15日出库数据汇总
  static getHalfMonthOutboundSummary([Map<String, dynamic>? params]) async {
    var res = await HttpManager.post(Courier.halfMonthOutboundSummary, params);
    return DataResult(res.data, res.result);
  }

  /// 数据中心-按月获取半年入柜数据汇总
  static getHalfYearInboundSummary([Map<String, dynamic>? params]) async {
    var res = await HttpManager.post(Courier.halfYearInboundSummary, params);
    return DataResult(res.data, res.result);
  }

  /// 数据中心-按月获取半年出库柜数据汇总
  static getHalfYearOutboundSummary([Map<String, dynamic>? params]) async {
    var res = await HttpManager.post(Courier.halfYearOutboundSummary, params);
    return DataResult(res.data, res.result);
  }

  /// 分页获取资金订单明细列表
  static getTradeRecordPage(Map<String, dynamic> info) async {
    var res = await HttpManager.post(Courier.getTradePage, info);
    return DataResult(res.data['records'], res.result,
        total: int.parse(res.data['total']));
  }

  /// 更新快递公司
  static waybillChangeCompany(Map<String, dynamic> info) async {
    var res = await HttpManager.post(Courier.changeCompany, info);
    return DataResult(res.data, res.result);
  }

  /// 更新快递单号
  static changeWayBillNo(Map<String, dynamic> info) async {
    var res = await HttpManager.post(Courier.updateWayBillNo, info);
    return DataResult(res.data, res.result);
  }

  /// 获取异常管理
  static orderExceptionLog(Map<String, dynamic> info) async {
    var res = await HttpManager.post(Courier.orderExceptionLog, info);
    return DataResult(res.data['records'], res.result,
        total: int.parse(res.data['total']));
  }

  /// 忽略异常
  static orderExceptionLogDel(String? waybillId, String action) async {
    Map<String, dynamic> info = {'waybillId': waybillId, 'action': action};
    var res = await HttpManager.post(Courier.orderExceptionLogDel, info);
    return DataResult(res.data, res.result);
  }

  /// 业务统计-获取统计数据汇总
  static getBusinessStatisticsSummary(Map<String, dynamic> params) async {
    var res = await HttpManager.post(Courier.businessStatisticsSummary, params);
    return DataResult(res.data, res.result);
  }

  /// 业务统计-获取每日明细数据
  static getBusinessStatisticsDaily(Map<String, dynamic> params) async {
    var res = await HttpManager.post(Courier.businessStatisticsDaily, params);
    return DataResult(res.data, res.result);
  }

  /// 业务统计-获取每日详细数据（按快递公司/业务员/员工分组）
  static getBusinessStatisticsDetail(Map<String, dynamic> params) async {
    var res = await HttpManager.post(Courier.businessStatisticsDetail, params);
    return DataResult(res.data, res.result);
  }
}
