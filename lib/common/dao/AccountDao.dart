import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/net/HttpManager.dart';

class AccountDao {
  static saveDeviceId(String company, String deviceId) async {
    Map requestParams = {'company': company, 'deviceId': deviceId};
    var res = await HttpManager.post('/account/saveDeviceId', requestParams);
    return new DataResult(res.data, true);
  }

  static sendStoSms() async {
    var res = await HttpManager.post('/account/sendStoSms', {});
    return new DataResult(res.data, true);
  }

  static bindStoDevice(info) async {
    var res = await HttpManager.post('/account/bindStoDevice', info);
    if (res.code == 200) {
      return new DataResult(res.data, true);
    } else {
      return null;
    }
//    return res;
  }
}
