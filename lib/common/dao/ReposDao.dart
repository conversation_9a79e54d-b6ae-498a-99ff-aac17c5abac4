// ignore_for_file: unnecessary_null_comparison

import 'dart:io';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/entitys/update_app_entity.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/net/Address.dart';
import 'package:cabinet_flutter_app/common/net/HttpManager.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app_upgrade/flutter_app_upgrade.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:scan/scan.dart';

class ReposDao {
  static getRepository() async {
    var res = await HttpManager.get('/sys/a/appVersion/getByAppCode', {'appCode': 't7'});
    if (res != null && res.result && res.data.length > 0) {
      var dataList = res.data;
      if (dataList == null || dataList.length == 0) {
//        return new DataResult(null, false);
        return null;
      }
      return dataList;
    } else {
//      return new DataResult(null, false);
      return null;
    }
  }

//   ///版本更新
//   static getNewsVersion(context, showTip, [showLoading = true]) async {
//     if (showLoading) {
//       EasyLoading.show(status: '加载中...');
//     }
// //    var res = await getRepository();
//     var res = await new Scan().checkUpdate();
//     if (showLoading) {
//       EasyLoading.dismiss();
//     }
//     Fluttertoast.showToast(msg: res);
//   }

  ///版本更新检测
  static getNewsVersion(context, {bool isForce = false}) async {
    if(Platform.isIOS) {
      return false;
    }
    int now = new DateTime.now().millisecondsSinceEpoch;
    int? checkTime = await LocalStorage.get(DefaultConfig().configs.CHECK_REFRESH_TIME);
    if (!isForce) {
      if (checkTime != null) {
        if (now - checkTime < 30 * 60 * 1000) {
          return;
        }
      }
    }
    if (isForce) {
      LoadingUtil(
        status: '版本号检测中...',
      ).show(context);
    }
    UpdateAppEntity? appEntity = await ReposDao.checkUpdate();
    await LocalStorage.save(DefaultConfig().configs.CHECK_REFRESH_TIME, now);
    if (isForce) {
      LoadingUtil.dismiss(context);
    }
    if (appEntity != null) {
      String _version = await CheckUtils.getVersion();
      String _newVersion = appEntity.appVer!;
      int version = int.parse(_version.replaceAll('.', ''));
      int newVersion = int.parse(_newVersion.replaceAll('.', ''));
      if (newVersion > version) {
        /// 版本号判断是否更新
        var apkPath = await LocalStorage.get(DefaultConfig().configs.APK_PATH);
        var res = await new Scan().checkUpdate();
        if (res != null) {
          res['note'] = appEntity.releaseNotes;
          if (res['message'] != null) {
            Fluttertoast.showToast(msg: res['message']);
            return;
          }
          if (apkPath != null) {
            if (apkPath.indexOf(res['name']) > -1) {
              File apkFile = File(apkPath);
              bool isHave = await apkFile.exists();
              if (isHave) {
                FlutterUpgrade.installAppForAndroid(apkPath);
                return;
              }
            }
          }

          updateApp(context, res, force: appEntity.hasForce == 1);
        }
      } else {
        if (isForce) {
          Fluttertoast.showToast(msg: '已是最新版本');
        }
      }
    } else {
      if (isForce) {
        Fluttertoast.showToast(msg: '已是最新版本');
      }
    }
  }

  ///版本更新
  static updateApp(context, res, {bool force = false}) {
    AppUpgrade.appUpgrade(
      context,
      ReposDao.checkAppInfo(res),
      contentStyle: TextStyle(color: Colors.black54, fontSize: 16),
      borderRadius: 10,
      force: force,
      okTextStyle: TextStyle(color: Colors.white, fontSize: 16),
      progressBarColor: Colors.blueAccent.withOpacity(.4),
      // okBackgroundColors: [Colors.white, Colors.white],
      onCancel: () async {
        await LocalStorage.save(DefaultConfig().configs.CHECK_REFRESH_TIME, new DateTime.now().millisecondsSinceEpoch);
      },
      downloadStatusChange: (status, path, {error}) async {
        if (status == DownloadStatus.done) {
          await LocalStorage.save(DefaultConfig().configs.CHECK_REFRESH_TIME, new DateTime.now().millisecondsSinceEpoch);
          await LocalStorage.save(DefaultConfig().configs.APK_PATH, path);
        }
      },
    );
  }

  static Future<AppUpgradeInfo> checkAppInfo(Map info) async {
    //这里一般访问网络接口，将返回的数据解析成如下格式
    return Future.delayed(Duration(seconds: 1), () {
      List<String> note = [];
      if (info['note'] == '' || info['note'] == null) {
        note = ['暂无更新说明'];
      } else {
        note = info['note'].split('\n');
      }
      return AppUpgradeInfo(
        title: info['name'],
        contents: note,
        apkDownloadUrl: info['url'],
        force: true,
      );
    });
  }

  static Future<UpdateAppEntity?> checkUpdate() async {
    String packageName = await CheckUtils.getPackageName();
    Map info = {
      "appId": packageName,
    };
    var res = await HttpManager.post(Cabinet.cabinetAppInfo, info);
    if (res != null && res.result) {
      UpdateAppEntity updateInfo = UpdateAppEntity.fromJson(res.data);
      return updateInfo;
    }
    return null;
  }
}
