import 'dart:async';

import 'package:cabinet_flutter_app/common/ab/provider/task/ScanDbProvider.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/entitys/scan_item_entity.dart';
import 'package:cabinet_flutter_app/common/net/HttpManager.dart';
import 'package:cabinet_flutter_app/common/utils/timer_util.dart';

class ScanDao {
  static late TimerUtil uploadTimerUtil;

  /// 获取scan列表
  static getScanList(type) async {
    ScanDbDbProvider provider = new ScanDbDbProvider();
    var result = await provider.getScanList(type);
    return result;
  }

  static search(info, {reload = false}) async {
    var res = await HttpManager.get("/scan/page", info);
    if (res != null && res.result) {
      return new DataResult(res.data['records'], true, total: res.data['total']);
    } else {
      return new DataResult(null, false);
    }
  }

  static getScanCount({String? type, String? brandCode, required String status, String? checkCode}) async {
    ScanDbDbProvider provider = new ScanDbDbProvider();
    var result = await provider.getScanCount(type: type, brandCode: brandCode, status: status, checkCode: checkCode);
    return result;
  }

  static getMaxIndex({String? shelfNo, String? day, String? rule}) async {
    ScanDbDbProvider provider = new ScanDbDbProvider();
    var result = await provider.getMaxIndex(shelfNo: shelfNo, day: day, rule: rule);
    return result;
  }

  static getUploadScanCount() async {
    ScanDbDbProvider provider = new ScanDbDbProvider();
    var result = await provider.getUploadScanCount();
    return result;
  }

  static Future<bool> hadScan(String code, String type) async {
    ScanDbDbProvider provider = new ScanDbDbProvider();
    bool result = await provider.hadScan(code, type);
    return result;
  }

  /// 删除
  static deleteScan(String waybillNo) async {
    ScanDbDbProvider provider = new ScanDbDbProvider();
    var result = await provider.deleteScan(waybillNo);
    return result;
  }

  /// 批量删除
  static deleteScanList(List ids, String type) async {
    ScanDbDbProvider provider = new ScanDbDbProvider();
    var result = await provider.deleteScanList(ids, type);
    return result;
  }

  static clearScan(type) async {
    ScanDbDbProvider provider = new ScanDbDbProvider();
    var result = await provider.clearScan(type);
    return result;
  }

  static clearScanByStatus(status) async {
    ScanDbDbProvider provider = new ScanDbDbProvider();
    var result = await provider.clearScanByStatus(status);
    return result;
  }

  // 清楚今天以前已上传的入库数据
  static clearScanBeforeTodayY(ymd) async {
    ScanDbDbProvider provider = new ScanDbDbProvider();
    var result = await provider.clearScanBeforeTodayY(ymd);
    return result;
  }

  static batchSaveScan(List list) async {
    ScanDbDbProvider provider = new ScanDbDbProvider();
    var result = await provider.batchSaveScan(list);
    return result;
  }

  /// 保存
  static saveScan(ScanItemEntity scan) async {
    ScanDbDbProvider provider = new ScanDbDbProvider();
    var result = await provider.saveScan(scan);
    return result;
  }

  /// 更新
  static updateScan(ScanItemEntity scan) async {
    ScanDbDbProvider provider = new ScanDbDbProvider();
    var result = await provider.updateScan(scan);
    return result;
  }
}
