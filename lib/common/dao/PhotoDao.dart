import 'dart:io';

import 'package:cabinet_flutter_app/common/ab/provider/task/PhotoDbProvider.dart';
import 'package:cabinet_flutter_app/common/dao/CodeRegExpDao.dart';
import 'package:cabinet_flutter_app/common/entitys/photo_entity.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/SoundUtils.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:scan/scan.dart';

class PhotoDao {
  /// 保存
  static savePhoto(PhotoEntity photo) async {
    PhotoDbDbProvider provider = new PhotoDbDbProvider();
    var result = await provider.savePhoto(photo);
    return result;
  }

  /// 删除
  static deletePhoto(PhotoEntity photo) async {
    PhotoDbDbProvider? provider = new PhotoDbDbProvider();
    var result = await provider.deletePhoto(photo.sheetNo!, photo.type!);
    return result;
  }

  /// 根据状态批量删除
  static deletePhotoByStatus({String status = 'OK'}) async {
    PhotoDbDbProvider provider = new PhotoDbDbProvider();
    var result = await provider.deletePhotoByStatus(status);
    return result;
  }

  /// 根据状态批量删除
  static deleteDuplicateData() async {
    PhotoDbDbProvider provider = new PhotoDbDbProvider();
    var result = await provider.deleteDuplicateData();
    return result;
  }

  /// 根据状态获取photo列表--分页
  static getPhotoList({String status = 'FAIL', int page = 0, String? sheetNo}) async {
    PhotoDbDbProvider provider = new PhotoDbDbProvider();
    var result = await provider.getPhotoList(status: status, page: page, sheetNo: sheetNo);
    return result;
  }

  /// 根据状态获取photo列表
  static getPhotoCount({String status = 'FAIL', String? sheetNo}) async {
    PhotoDbDbProvider provider = new PhotoDbDbProvider();
    var result = await provider.getCount(status, sheetNo);
    return result;
  }

  /// 获取全部photo列表
  static getPhotoAllCount(String sheetNo) async {
    PhotoDbDbProvider provider = new PhotoDbDbProvider();
    var result = await provider.getAllCount(sheetNo);
    return result;
  }

  /// 清空本地照片表
  static clearPhoto() async {
    PhotoDbDbProvider provider = new PhotoDbDbProvider();
    await provider.clearPhoto();
  }

  /// 根据状态获取全部拖
  static getAllPhotoByStatus(String status) async {
    PhotoDbDbProvider provider = new PhotoDbDbProvider();
    List<PhotoEntity> list = await provider.getAllPhotoByStatus(status);
    return list;
  }

  /// 根据单号和类型查询图片
  static getPhotoByNoAndType(String sheetNo, String type) async {
    PhotoDbDbProvider provider = new PhotoDbDbProvider();
    var item = await provider.getPhotoByNoAndType(sheetNo, type);
    return item;
  }

  /// 根据状态删除所有图片
  static deleteAllPhotoByStatus(String status) async {
    List<PhotoEntity> list = await getAllPhotoByStatus(status);
    if (list.length > 0) {
      await deletePhotoByStatus(status: status);
      SoundUtils.audioPushFn(SoundUtils.SUCCESS_OK);
      list.forEach((item) {
        File imageFile = new File(item.path!);
        try {
          imageFile.deleteSync();
          print("删除文件成功");
        } catch (e) {
          print("删除文件出错了：$e");
        }
      });
    } else {
      SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
      Fluttertoast.showToast(msg: '暂无可上传图片');
    }
  }

  /// 根据状态上传所有图片
  static uploadAllPhotoByStatus(String status) async {
    List<PhotoEntity> list = await getAllPhotoByStatus(status);
    if (list.length > 0) {
      list.forEach((item) {
        String bucket = CheckUtils.checkBucketName(item.type!);
        // 上传图片到OSS
        // item.path: 图片本地路径
        // item.fileName: 图片文件名
        // item.company: 公司代码
        // bucket: OSS存储桶名称
        // item.type: 图片类型(入库/出库)
        CheckUtils.checkPhotoUploadTwo(item.path!, item.fileName!, item.company!, bucket, item.type!);
       
      });
      SoundUtils.audioPushFn(SoundUtils.SUCCESS_OK);
    } else {
      SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
      Fluttertoast.showToast(msg: '暂无可上传图片');
    }
  }

  static deletePhoto7DaysBefore() async {
    PhotoDbDbProvider provider = new PhotoDbDbProvider();
    List<PhotoEntity> list = await provider.deletePhoto7DaysBefore();
    if (list.length > 0) {
      list.forEach((item) {
        File imageFile = new File(item.path!);
        try {
          imageFile.deleteSync();
          print("删除文件成功");
        } catch (e) {
          print("删除文件出错了：$e");
        }
      });
    }
  }

  /// 删除没有操作的本地图片（脏数据）
  static deleteFileWithoutOpt() async {
    int count = await getPhotoAllCount('');
    if (count > 0) {
      await deleteFile('rk');
      await deleteFile('ck');
    } else {
      scanPlugin.clearCache();
    }
    return true;
  }

  /// 删除数据库内不存在的本地图片文件
  /// * [type] 图片类型 { 'rk', 'ck' }
  /// * [cardPath] 默认内部存储路径
  /// * [isRecur] 是否递归 默认内部存储时不递归
  static deleteFile(String type, {String cardPath = '/storage/emulated/0', bool isRecur = true}) async {
    String _path = cardPath + '/365zmyz/' + type;
    if (_path != '') {
      var directory = new Directory(_path);
      bool flag = await directory.exists();
      if (flag) {
        Stream<FileSystemEntity> entityList = directory.list();
        try {
          await for (FileSystemEntity entity in entityList) {
            PhotoDbDbProvider provider = new PhotoDbDbProvider();
            var res = await provider.getPhotoByPath(entity.path, type);
            if (res == null) {
              File imageFile = new File(entity.path);
              try {
                imageFile.deleteSync();
                print("删除文件成功");
              } catch (e) {
                print("删除文件出错了：$e");
              }
            }
          }
        } catch (e) {
          print(e);
        }
      } else {
        if (isRecur) {
          var directory = new Directory('/storage');
          bool flag = await directory.exists();
          if (flag) {
            Stream<FileSystemEntity> entityList = directory.list();
            await for (FileSystemEntity entity in entityList) {
              if (entity.path != '/storage/emulated') {
                deleteFile(type, cardPath: entity.path, isRecur: false);
              }
            }
          }
        }
      }
    }
  }
}
