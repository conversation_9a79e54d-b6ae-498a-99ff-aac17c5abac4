import 'package:cabinet_flutter_app/common/ab/provider/task/ScanDbProvider.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/entitys/scan_item_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/user_account_entity.dart';
import 'package:scan/scan.dart';

Scan scanPlugin = Scan();

class CodeRegExpDao {
  /// 获取该账号支持的快递公司列表
  static getSupportCompany(List<UserAccountEntity> accounts) {
    Map supportCompanyNameList = {};

    accounts.forEach((account) {
      switch (account.company) {
        case "STO":
          supportCompanyNameList['STO'] = "申通快递";
          break;
        case "YTO":
          supportCompanyNameList['YTO'] = "圆通快递";
          break;
        case "ZTO":
          supportCompanyNameList['ZTO'] = "中通快递";
          break;
        case "YUNDA":
          supportCompanyNameList['YUNDA'] = "韵达快递";
          break;
        case "HTKY":
          supportCompanyNameList['HTKY'] = "百世快递";
          break;
        case "TTKDEX":
          supportCompanyNameList['TTKDEX'] = "天天快递";
          break;
      }
    });
    return supportCompanyNameList;
  }

  static bool check(String code) {
    if (code.length > 20) {
      return false;
    }

    if (code.startsWith('LP')) {
      // 百世LP的件拦截
      return false;
    }

    if (!new RegExp("^[A-Za-z]{0,3}[0-9]{8,20}\$").hasMatch(code)) {
      return false;
    }

//    if (new RegExp("^VIP[0-9]{9}|^V[0-9]{11}\$|^LBX[0-9]{15}-[2-9AZ]{1}-[1-9A-Z]{1}\$|^(9001)[0-9]{8}\$")
//        .hasMatch(code)) {
//      // 拦截优速快递
//      return false;
//    }

    return true;
  }

  // /// 更新本地扫码快递公司
  // static updateCompany(ScanItemEntity scanDb) async {
  //   var info = {'no': scanDb.waybillNo, 'type': scanDb.type};
  //   var res = await HttpManager.get("/check", info);
  //   if (res != null) {
  //     if (res.code == 200) {
  //       if (res.data != '') {
  //         scanDb.brandCode = res.data;
  //         await ScanDao.saveScan(scanDb);
  //       }
  //     }
  //   }
  // }

  static buildScanList(code, type) {
    List<ScanItemEntity> list = [];
    DefaultConfig().configs.EXPRESS.keys.forEach((brandCode) {
      ScanItemEntity scan = ScanDbDbProvider.buildScan(code, type, brandCode);
      list.add(scan);
    });
    return list;
  }
}
