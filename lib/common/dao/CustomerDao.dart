import 'package:cabinet_flutter_app/common/entitys/customer_entity.dart';
import 'package:cabinet_flutter_app/common/net/HttpManager.dart';

class CustomerDao {
  // // 检测是不是新用户
  // static checkCustomer(String phone) async {
  //   CustomerDbProvider provider = new CustomerDbProvider();
  //   bool? result = await provider.isNewCustomer(phone);
  //   return result;
  // }
  //
  // // 保存新用户
  // static saveCustomer(CustomerEntity customer) async {
  //   CustomerDbProvider provider = new CustomerDbProvider();
  //   var result = await provider.saveCustomer(customer);
  //   return result;
  // }
  //
  // // 批量保存新用户
  // static batchSaveCustomer(List<dynamic> list) async {
  //   CustomerDbProvider provider = new CustomerDbProvider();
  //   var result = await provider.batchSaveCustomer(list);
  //   return result;
  // }
  //
  // static searchCustomer(String mobile) async {
  //   CustomerDbProvider provider = new CustomerDbProvider();
  //   var reslut = await provider.getCustomer(mobile);
  //   return reslut;
  // }
  //
  // static getCustomerCount() async {
  //   CustomerDbProvider provider = new CustomerDbProvider();
  //   int? count = await provider.getCount();
  //   return count;
  // }
}
