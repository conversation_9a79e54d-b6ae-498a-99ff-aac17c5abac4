import 'package:cabinet_flutter_app/common/ab/SqlManager.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/entitys/login_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/net/Address.dart';
import 'package:cabinet_flutter_app/common/net/HttpManager.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/redux/CurrentTabIndexRedux.dart';
import 'package:cabinet_flutter_app/common/redux/UserRedux.dart';
import 'package:cabinet_flutter_app/common/redux/UserTypeRedux.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/utils/timer_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';

late TimerUtil uploadTimerUtil;

class UserDao {
  static checkMobile(Map info) async {
    var res = await HttpManager.post(Courier.checkMobile, info);
    return DataResult(res.data, res.result);
  }

  static appQr(Map info) async {
    var res = await HttpManager.post(Courier.appQr, info);
    return DataResult(res.data, res.result);
  }

  static sendSms(Map info) async {
    var res = await HttpManager.post(Courier.sendSms, info);
    return DataResult(res.data, res.result);
  }

  static login(Map info, store) async {
    Map requestParams = info;
    var res = await HttpManager.post(Courier.login, requestParams);
    EasyLoading.dismiss();
    if (res != null && res.result) {
      await LocalStorage.save(DefaultConfig().configs.IS_LOGOUT, false);
      LoginEntity loginEntity = LoginEntity.fromJson(res.data);

      /// 默认开启仅入库

      await LocalStorage.save(DefaultConfig().configs.USER_NAME_KEY, info['loginName']);
      await LocalStorage.save(DefaultConfig().configs.USER_PERMIT, 'Y');
      await LocalStorage.save(DefaultConfig().configs.LOGIN_TIME, new DateTime.now().millisecondsSinceEpoch);
      await LocalStorage.save(DefaultConfig().configs.PW_KEY, info['password'], isPrivate: true);
      CheckUtils.setToken({loginEntity.tokenName: loginEntity.tokenValue});
    }
    return new DataResult(res.data, res.result);
  }

  static realNameAuth(Map info) async {
    var res = await HttpManager.post(Courier.realNameAuth, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  static logout(BuildContext context) async {
    await LocalStorage.save(DefaultConfig().configs.IS_LOGOUT, true);
    HttpManager.clearAuthorization();
    Store<AppState> store = StoreProvider.of(context);
    NavigatorUtils.goLoginNew(context);
    store.dispatch(new UpdateCurrentTabIndexAction(0));
    UserDao.clearAll(store);
    SqlManager.close();
  }

  /// 欢迎界面自动登录
  static autoLogin(String? loginName, String? password, int? userType, store, context, {bool checkTime = true}) async {
    Map<String, dynamic> info = {
      'loginName': loginName?.trim(),
      'password': password?.trim(),
    };
    int? loginLastTime = await LocalStorage.get(DefaultConfig().configs.LOGIN_TIME);
    int nowTime = new DateTime.now().millisecondsSinceEpoch;
    bool flag = true;
    if (checkTime) {
      if (loginLastTime != null && loginLastTime > 0 && nowTime - loginLastTime > 2 * 60 * 60 * 1000) {
      } else {
        flag = false;
      }
    }
    if (flag) {
      var res = await UserDao.login(info, store);
      if (res != null && res.result) {
        var _res = await UserDao.getUserInfo(store);
        if (_res != null && _res.result) {
          if (_res.data is UserEntity) {
            UserEntity user = _res.data;
            if (user.hasReal == 0) {
              NavigatorUtils.goRealName(context, userType!);
            } else if (user.hasReal == 1) {
              if (user.brands?.length == 0) {
                NavigatorUtils.goBrandBind(context);
              } else {
                NavigatorUtils.goHome(context);
              }
            }
          }
        }
      } else {
        NavigatorUtils.goLoginNew(context);
      }
    } else {
      NavigatorUtils.goLoginNew(context);
    }
  }

  ///获取用户详细信息
  static getUserInfo(store, {needDb = false}) async {
    var res;
    res = await HttpManager.post(Courier.getUserInfo, null);
    if (res != null && res.result) {
      UserEntity user = UserEntity.fromJson(res.data);
      LocalStorage.save(DefaultConfig().configs.USER_INFO, user);
      await LocalStorage.save(DefaultConfig().configs.USER_TYPE, user.userType, isPrivate: true);
      await LocalStorage.save(DefaultConfig().configs.HAS_ADMIN, user.hasAdmin, isPrivate: true);
      await LocalStorage.save(DefaultConfig().configs.HAS_WALLET, user.hasWallet, isPrivate: true);
      await LocalStorage.save(DefaultConfig().configs.HAS_SWITCH_SEND, user.switchSend, isPrivate: true);
      await LocalStorage.save(DefaultConfig().configs.SHOP_ID, user.shopId, isPrivate: true);
      store.dispatch(new UpdateUserAction(user));
      store.dispatch(new UpdateUserTypeAction(user.userType!));
      return new DataResult(user, true);
    } else {
      return new DataResult(res.data, false);
    }
  }

  ///更新用户详细信息
  static updateUserInfo(store) async {
    var res;
    res = await HttpManager.post(Courier.getUserInfo, null);
    if (res != null && res.result) {
      UserEntity user = UserEntity.fromJson(res.data);
      LocalStorage.save(DefaultConfig().configs.USER_INFO, user);
      store.dispatch(new UpdateUserAction(user));
    }
  }

  ///获取快递公司列表
  static getBrandList() async {
    var res = await HttpManager.post(Courier.getBrandList, null);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  static getWealthInfo() async {
    var res = await HttpManager.post(Courier.getWealthInfo, null);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  ///wechat账户充值
  static rechargeWx(Map info) async {
    var res = await HttpManager.post(Courier.rechargeWx, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  /// 获取配置信息
  static sysConfig(Map info) async {
    var res = await HttpManager.post(Courier.sysConfig, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  static wxmpJumpSchema(Map info) async {
    var res = await HttpManager.post(Courier.wxmpJumpSchema, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  ///alibaba账户充值
  static rechargeAli(Map info) async {
    var res = await HttpManager.post(Courier.rechargeAli, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  ///账号注销
  static delUser(Map info) async {
    var res = await HttpManager.post(Courier.delUser, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  ///账户余额充值
  static rechargeBal(Map info) async {
    var res = await HttpManager.post(Courier.rechargeBal, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  ///设置支付密码
  static setPayPassword(Map info) async {
    var res = await HttpManager.post(Courier.setPayPassword, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  ///修改支付密码
  static modifyPayPassword(Map info) async {
    var res = await HttpManager.post(Courier.modifyPayPassword, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  ///忘记支付密码
  static forgetPayPassword(Map info) async {
    var res = await HttpManager.post(Courier.forgetPayPassword, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  ///绑定支付宝
  static bindAccount(Map info) async {
    var res = await HttpManager.post(Courier.bindAccount, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  ///资金明细
  static fundDetails(Map info) async {
    var res = await HttpManager.post(Courier.fundDetails, info);
    if (res != null && res.result) {
      return new DataResult(
        res.data,
        res.result,
      );
    } else {
      return new DataResult(res.data, false);
    }
  }

  ///提现明细
  static goWithdrawalDetails(Map info) async {
    var res = await HttpManager.post(Courier.withdrawalDetails, info);
    if (res != null && res.result) {
      return new DataResult(
        res.data,
        res.result,
      );
    } else {
      return new DataResult(res.data, false);
    }
  }

  ///提现
  static goWithdrawalCreate(Map info) async {
    var res = await HttpManager.post(Courier.withdrawalCreate, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  ///资金明细汇总
  static totalData(Map info) async {
    var res = await HttpManager.post(Courier.totalData, info);
    if (res != null && res.result) {
      return new DataResult(
        res.data,
        res.result,
      );
    } else {
      return new DataResult(res.data, false);
    }
  }

  ///我的消息
  static myNotifyList(Map info) async {
    var res = await HttpManager.post(Courier.myNotifyList, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  ///我的消息详情
  static myNotifyDetailList(Map info) async {
    var res = await HttpManager.post(Courier.myNotifyDetailList, info);
    if (res != null && res.result) {
      return DataResult(res.data['records'], res.result, total: int.parse(res.data['total']));
    } else {
      return new DataResult(res.data, false);
    }
  }

  ///读取我的单个消息
  static readOneNotice(Map info) async {
    var res = await HttpManager.post(Courier.readOneNotice, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  ///读取我的全部消息
  static readAllNotice(Map info) async {
    var res = await HttpManager.post(Courier.readAllNotice, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  ///获取开通品牌信息
  static getBrandBindList() async {
    var res = await HttpManager.post(Courier.getBindBrandList, null);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  ///团队管理列表
  static myTeamList(Map info) async {
    var res = await HttpManager.post(Courier.myTeamList, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  ///团队管理解除
  static myTeamDelete(Map info) async {
    var res = await HttpManager.post(Courier.myTeamDelete, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  ///团队管理加入
  static myTeamAdd(Map info) async {
    var res = await HttpManager.post(Courier.myTeamAdd, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  ///开启收件功能
  static switchSend(Map info) async {
    var res = await HttpManager.post(Courier.switchSend, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  static getBannerList(Map info) async {
    var res = await HttpManager.post(Courier.getBannerList, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  ///初始化用户信息
  static initUserInfo(Store<AppState> store) async {
    var token = await LocalStorage.get(DefaultConfig().configs.TOKEN_KEY);
    var res = await getUserInfoLocal();
    if (res != null && res.result && token != null) {
      store.dispatch(UpdateUserAction(res.data));
      store.dispatch(UpdateUserTypeAction(res.data.userType ?? 4));
    }

    ///读取主题
    String? themeIndex = await LocalStorage.get(DefaultConfig().configs.THEME_COLOR);
    if (themeIndex != null) {
      if (themeIndex.length >= 0) {
        CommonUtils.pushTheme(store, int.parse(themeIndex));
      }
    }

    ///切换语言
    String? localeIndex = await LocalStorage.get(DefaultConfig().configs.LOCALE);
    if (localeIndex != null) {
      if (localeIndex.length != 0) {
        CommonUtils.changeLocale(store, int.parse(localeIndex));
      }
    }

    return new DataResult(res.data, (res.result && (token != null)));
  }

  ///获取本地登录用户信息
  static getUserInfoLocal() async {
    Map<String, dynamic>? userEntityMap = await LocalStorage.getJson(DefaultConfig().configs.USER_INFO);
    if (userEntityMap != null) {
      return new DataResult(UserEntity.fromJson(userEntityMap), true);
    } else {
      return new DataResult(null, false);
    }
  }

  // static getUserBalance() async {
  //   var res;
  //   res = await HttpManager.get(Address.getUserBalance(), null);
  //   if (res != null && res.result) {
  //     return new DataResult(res.data, true);
  //   } else {
  //     return new DataResult(res.data, false);
  //   }
  // }

  static clearAll(Store store) async {
    HttpManager.clearAuthorization();
    LocalStorage.remove(DefaultConfig().configs.USER_INFO);
    store.dispatch(new UpdateUserAction(new UserEntity()));
  }

  static register(Map<String, dynamic> info) async {
    var res = await HttpManager.post(Courier.register, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  static shopRegister(Map<String, dynamic> info) async {
    var res = await HttpManager.post(Courier.shopRegister, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  static courierRegister(Map<String, dynamic> info) async {
    var res = await HttpManager.post(Courier.courierRegister, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  static resetPwd(Map<String, dynamic> info) async {
    var res = await HttpManager.post(Courier.resetPwd, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  static bindBrand(Map<String, dynamic> info) async {
    var res = await HttpManager.post(Courier.bindBrand, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  static setBindCompanyBrand(Map<String, dynamic> info) async {
    var res = await HttpManager.post(Courier.setBindCompanyBrand, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  static setBrand(Map<String, dynamic> info) async {
    var res = await HttpManager.post(Courier.setBrand, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  static updateBrand(Map<String, dynamic> info) async {
    var res = await HttpManager.post(Courier.updateBrand, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  static getCabinetCode() async {
    String? code = await LocalStorage.get(DefaultConfig().configs.CABINET_BIND, isPrivate: true);
    return code;
  }

  ///设置自动签收
  static updateAutoSign(Map<String, dynamic> info) async {
    var res = await HttpManager.post(Courier.updateAutoSign, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  ///获取自动签收列表
  static getAutoSignList() async {
    var res = await HttpManager.post(Courier.getAutoSignList, null);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  // 绑定共配
  static bindGP(Map info) async {
    var res = await HttpManager.post(Courier.gpAddGpconfig, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  // 是否绑定共配
  static hasGpconfig() async {
    var res = await HttpManager.post(Courier.gpHasGpconfig, null);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  // 是否绑定共配
  static userAccounts(Map info) async {
    var res = await HttpManager.post(Courier.gpUserAccounts, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  // 是否绑定共配
  static gpDelete() async {
    var res = await HttpManager.post(Courier.gpDelete, null);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  // 获取驿站通道账号列表
  static accountList(Map info) async {
    var res = await HttpManager.post(Courier.accountList, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  static accountDel(Map info) async {
    var res = await HttpManager.post(Courier.accountDel, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  static getCaptcha(Map info) async {
    var res = await HttpManager.post(Courier.getCaptcha, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  static verifyCaptcha(Map info) async {
    var res = await HttpManager.post(Courier.verifyCaptcha, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  static updateAccount(Map info) async {
    var res = await HttpManager.post(Courier.updateAccount, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  static ddGet(Map info) async {
    var res = await HttpManager.post(Courier.ddGet, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  static ruleDetail(Map info) async {
    var res = await HttpManager.post(Courier.ruleDetail, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  static ruleSaveOrUpdate(Map info) async {
    var res = await HttpManager.post(Courier.ruleSaveOrUpdate, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  static loginCheck(Map info) async {
    var res = await HttpManager.post(Courier.loginCheck, info);
    if (res != null && res.result) {
      return new DataResult(res.data, res.result);
    } else {
      return new DataResult(res.data, false);
    }
  }

  static sendUserSms(Map info) async {
    var res = await HttpManager.post(Courier.sendLoginCode, info);
    return DataResult(res.data, res.result);
  }
}
