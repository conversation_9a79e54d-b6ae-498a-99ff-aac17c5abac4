import 'package:cabinet_flutter_app/common/net/Address.dart';
import 'package:cabinet_flutter_app/common/net/HttpManager.dart';

import 'DaoResult.dart';

class CabinetDao {
  /// 获取二维码
  static getQr(Map info) async {
    var res = await HttpManager.post(Cabinet.qr, info);
    return DataResult(res.data, res.result);
  }

  ///获取点位信息
  static getCabinetInfo(String code) async {
    var res = await HttpManager.post(Cabinet.cabinetInfo, {'code': code});
    return DataResult(res.data, res.result);
  }

  static getMqttConfig(String cabinetLocationCode) async {
    var res = await HttpManager.post(Cabinet.getMqttConfig, {'code': cabinetLocationCode});
    return DataResult(res.data, res.result);
  }

  ///根据门店投件码，获取门店编码1.0
  static decryptShopCode(String? code) async {
    var res = await HttpManager.post(Cabinet.decryptShopCode, {'qrCode': code});
    return DataResult(res.data, res.result);
  }

  ///获取点位所有柜机及盒子
  static getCabinetFullInfo(String code) async {
    var res = await HttpManager.post(Cabinet.cabinetFullInfo, {'cabinetLocationCode': code});
    return DataResult(res.data, res.result);
  }

  ///获取点位所有柜机及盒子
  static getCabinetDetail(String code) async {
    var res = await HttpManager.post(Cabinet.shopCabinetDetail, {'cabinetLocationCode': code});
    return DataResult(res.data, res.result);
  }

  ///点位可用格口列表1.0q
  static getCabinetUseableList(String code) async {
    var res = await HttpManager.post(Cabinet.cabinetBoxUsableList, {'cabinetLocationCode': code});
    return DataResult(res.data, res.result);
  }

  ///点位可用格口列表1.0
  static batchOpenBox(String code, List<String> ids, int boxType, {String hostIndex = ''}) async {
    Map info = {'cabinetLocationCode': code, 'cabinetIds': ids, 'hostIndex': hostIndex};
    if (boxType > -1) {
      info.putIfAbsent('cabinetBoxType', () => boxType);
    }
    var res = await HttpManager.post(Cabinet.batchOpenCabinet, info, version: ApiVersion.V2);
    return DataResult(res.data, res.result);
  }

  /// 柜机可用格口
  static cabinetOpenBox(String cabinetId, int boxType) async {
    Map info = {'cabinetId': cabinetId};
    if (boxType > -1) {
      info.putIfAbsent('cabinetBoxType', () => boxType);
    }
    var res = await HttpManager.post(Cabinet.nextBoxCabinetId, info, version: ApiVersion.V1);
    return DataResult(res.data, res.result);
  }

  ///根据格口标签获取格口信息1.0
  static getBoxItem(String code, String label) async {
    var res = await HttpManager.post(Cabinet.cabinetBoxByLabel, {'cabinetLocationCode': code, 'boxLabel': label});
    return DataResult(res.data, res.result);
  }

  ///获取点位所有柜机及盒子
  static getCourierOderDetail(String code, String boxId) async {
    var res = await HttpManager.post(Cabinet.courierOderDetail, {'cabinetLocationCode': code, 'cabinetBoxId': boxId});
    return DataResult(res.data, res.result);
  }

  ///获取点位可用盒子汇总
  static getCabinetUsableBox(String code) async {
    var res = await HttpManager.post(Cabinet.cabinetUsable, {'cabinetLocationCode': code});
    return DataResult(res.data, res.result);
  }

  ///获取格口开关门状态
  static cabinetBoxOpenStatus(String boxId) async {
    var res = await HttpManager.post(Cabinet.cabinetBoxOpenStatus, {'cabinetBoxId': boxId});
    return DataResult(res.data, res.result);
  }

  ///格口关门
  static cabinetBoxCloseDoor(String boxId) async {
    var res = await HttpManager.post(Cabinet.cabinetBoxCloseDoor, {'cabinetBoxId': boxId});
    return DataResult(res.data, res.result);
  }

  ///我的收藏点位列表
  static cabinetLocationList() async {
    var res = await HttpManager.post(Cabinet.cabinetLocationList, null);
    return DataResult(res.data, res.result);
  }
}
