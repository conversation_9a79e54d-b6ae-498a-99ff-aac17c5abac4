
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/entitys/company_rules_entity.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/net/Address.dart';
import 'package:cabinet_flutter_app/common/net/HttpManager.dart';
import 'package:cabinet_flutter_app/common/utils/ScanUtils.dart';

class SyncDao {
  /// 获取猜单规则(走接口)
  static fetchCompanyRule() async {
    var res = await HttpManager.post(Express.getCompanyRule, null);
    if (res != null && res.result) {
      List<CompanyRulesEntity> rules = [];
      res.data.forEach((v) {
        CompanyRulesEntity item = CompanyRulesEntity.fromJson(v);
        rules.add(item);
      });
      await LocalStorage.save(DefaultConfig().configs.COMPANY_RULE, rules);
      ScanUtils.clearCompanyRules();
    }
  }
}
