import 'dart:convert';

import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/dd_data_entity.g.dart';

export 'package:cabinet_flutter_app/generated/json/dd_data_entity.g.dart';

@JsonSerializable()
class DdDataEntity {
  late String key;
  late String data;

  DdDataEntity();

  factory DdDataEntity.fromJson(Map<String, dynamic> json) => $DdDataEntityFromJson(json);

  Map<String, dynamic> toJson() => $DdDataEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
