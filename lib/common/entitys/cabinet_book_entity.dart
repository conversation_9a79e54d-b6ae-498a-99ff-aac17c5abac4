import 'dart:convert';

import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/cabinet_book_entity.g.dart';

@JsonSerializable()
class CabinetBookEntity {
  CabinetBookEntity();

  factory CabinetBookEntity.fromJson(Map<String, dynamic> json) => $CabinetBookEntityFromJson(json);

  Map<String, dynamic> toJson() => $CabinetBookEntityToJson(this);

  String? id;
  int? orderStatus;
  String? orderNo;
  String? channelId;
  String? channelName;
  String? siteId;
  String? siteName;
  String? shopId;
  String? shopName;
  String? cabinetLocationId;
  String? cabinetLocationCode;
  String? cabinetLocationName;
  int? cabinetLocationOwner;
  String? bookUserId;
  String? bookUserName;
  String? bookUserMobile;
  int? totalFee;
  int? minute;
  int? superBoxCount;
  int? hugeBoxCount;
  int? largeBoxCount;
  int? mediumBoxCount;
  int? smallBoxCount;
  int? miniBoxCount;
  int? microBoxCount;
  String? bookStartTime;
  String? bookEndTime;
  String? createTime;
  String? bookJson;
  List<CabinetBookList>? list;
}


@JsonSerializable()
class CabinetBookList {
  late String id;
  late String orderId;
  late int status;
  late String cabinetId;
  late String cabinetCode;
  late String cabinetName;
  late int cabinetSerialNo;
  late String cabinetBoxId;
  late int cabinetBoxType;
  late String cabinetBoxLabel;
  late int cabinetBoxPcbNo;
  late int fee;

  CabinetBookList();

  factory CabinetBookList.fromJson(Map<String, dynamic> json) => $CabinetBookListFromJson(json);

  Map<String, dynamic> toJson() => $CabinetBookListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}