import 'dart:convert';

import 'package:cabinet_flutter_app/common/entitys/customer_entity.dart';
import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/mobile_guss_entity.g.dart';

@JsonSerializable()
class MobileGussEntity {
  String phone = '';
  String name = '';
  String address = '';
  bool hasSecret = false;
  String platform = '';
  int virtualNumber = 2; // 2非虚拟  1虚拟
  bool hasSubstituteSms = false;
  String tagRemark = '';
  int tagType = 1;
  String yzChannel = '';
  String yzSopAccountId = '';
  int guessTime = 0;
  late List<CustomerEntity> gussMobiles;

  MobileGussEntity();

  factory MobileGussEntity.fromJson(Map<String, dynamic> json) => $MobileGussEntityFromJson(json);

  Map<String, dynamic> toJson() => $MobileGussEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
