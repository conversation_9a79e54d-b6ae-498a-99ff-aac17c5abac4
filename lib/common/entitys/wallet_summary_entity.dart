import 'dart:convert';

import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/wallet_summary_entity.g.dart';

@JsonSerializable()
class WalletSummaryEntity {
  int? incomeAmount;
  int? outcomeAmount;
  int? rechargeAmount;
  int? withdrawAmount;

  WalletSummaryEntity();

  factory WalletSummaryEntity.fromJson(Map<String, dynamic> json) => $WalletSummaryEntityFromJson(json);

  Map<String, dynamic> toJson() => $WalletSummaryEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
