import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/box_item_entity.g.dart';
import 'dart:convert';

@JsonSerializable()
class BoxItemEntity {
	String? cabinetLocationCode;
	String? cabinetId;
	String? cabinetCode;
	int? serialNo;
	String? cabinetName;
	String? cabinetLocationNo;
	String? boxId;
	int? boxType;
	int? pcbNo;
	String? boxLabel;
	bool isUsed = false;

	BoxItemEntity();

	factory BoxItemEntity.fromJson(Map<String, dynamic> json) => $BoxItemEntityFromJson(json);

	Map<String, dynamic> toJson() => $BoxItemEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}