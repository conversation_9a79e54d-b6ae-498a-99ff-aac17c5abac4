import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/recharge_entity.g.dart';

@JsonSerializable()
class RechargeEntity {
  RechargeEntity();

  factory RechargeEntity.fromJson(Map<String, dynamic> json) => $RechargeEntityFromJson(json);

  Map<String, dynamic> toJson() => $RechargeEntityToJson(this);

  String? packageValue;
  String? appId;
  String? sign;
  String? partnerid;
  String? prepayid;
  String? noncestr;
  String? timestamp;
}
