import 'dart:convert';

import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/wallet_entity.g.dart';

@JsonSerializable()
class WalletEntity {
  String? id;
  String? createTime;
  String? updateTime;
  String? code;
  int? balance;
  int? commission;
  int? waitWithdrawalMoney;
  int? totalWithdrawalMoney;
  int? frozenMoney;
  String? bindTime;
  int? bindTargetType;
  String? bindTargetId;
  String? bindTargetName;
  int? status;
  int? version;

  WalletEntity();

  factory WalletEntity.fromJson(Map<String, dynamic> json) => $WalletEntityFromJson(json);

  Map<String, dynamic> toJson() => $WalletEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
