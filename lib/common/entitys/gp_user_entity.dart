import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/gp_user_entity.g.dart';
import 'dart:convert';
export 'package:cabinet_flutter_app/generated/json/gp_user_entity.g.dart';

@JsonSerializable()
class GpUserEntity {
	late String id;
	late String createTime;
	late String updateTime;
	late int type;
	late String bizId;
	late String gpUserId;
	late String gpUserName;

	GpUserEntity();

	factory GpUserEntity.fromJson(Map<String, dynamic> json) => $GpUserEntityFromJson(json);

	Map<String, dynamic> toJson() => $GpUserEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}