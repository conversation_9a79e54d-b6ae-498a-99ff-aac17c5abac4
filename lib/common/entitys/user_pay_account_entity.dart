import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/user_pay_account_entity.g.dart';

@JsonSerializable()
class UserPayAccountEntity {
  UserPayAccountEntity({this.waitWithdrawalMoney = 0});

  factory UserPayAccountEntity.fromJson(Map<String, dynamic> json) => $UserPayAccountEntityFromJson(json);

  Map<String, dynamic> toJson() => $UserPayAccountEntityToJson(this);

  String? id;
  String? code;
  int? commission;
  int? balance;
  int waitWithdrawalMoney;
  int? totalWithdrawalMoney;
  int? frozenMoney;
  String? realName;
  String? wechatId;
  String? wechatAccount;
  String? alipayId;
  String? alipayAccount;
  String? alipayIdNumber;
  String? bindTime;
  String? bindTargetName;
  String? bindTargetId;
  int? minWithdrawMoney;
  int? maxWithdrawMoney;
  int? bindTargetType;
}
