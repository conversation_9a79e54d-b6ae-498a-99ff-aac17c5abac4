import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/customer_entity.g.dart';

@JsonSerializable()
class CustomerEntity {
  CustomerEntity({
    this.name = '',
    this.mobile = '',
    this.hasSubstituteSms = false,
    this.mobileDesc = '',
    this.platform = '',
    this.isNew = 0,
    this.isBlacklist = 0,
  });

  factory CustomerEntity.fromJson(Map<String, dynamic> json) => $CustomerEntityFromJson(json);

  Map<String, dynamic> toJson() => $CustomerEntityToJson(this);

  String? id;
  String? shopCode;
  String? code;
  String name;
  String? jianpin;
  String? pinyin;
  String mobile;
  int? labelNotice;
  int? isNew;
  int? isBlacklist;
  String? mobileLastFour;
  String? idNo;
  String? email;
  String? provinceCode;
  String? provinceName;
  String? cityCode;
  String? cityName;
  String? countyName;
  String? countyCode;
  String? address;
  String? gender;
  String? label;
  String? createBy;
  String? remarks;
  String? ymd;
  String? createDate;
  String? isBindWx;
  String? isSensitiveConsumer;
  String? lastInboundTime;
  bool hasSubstituteSms;
  String? mobileDesc;
  String? platform;
}
