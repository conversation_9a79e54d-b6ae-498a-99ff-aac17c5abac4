import 'dart:convert';

import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/mqtt_config_entity.g.dart';

export 'package:cabinet_flutter_app/generated/json/mqtt_config_entity.g.dart';

@JsonSerializable()
class MqttConfigEntity {
  late String broker;
  late String port;
  late String productKey;
  late String username;
  late String password;
  late String topic;

  MqttConfigEntity();

  factory MqttConfigEntity.fromJson(Map<String, dynamic> json) => $MqttConfigEntityFromJson(json);

  Map<String, dynamic> toJson() => $MqttConfigEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
