import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/cabinet_waybill_entity.g.dart';

@JsonSerializable()
class CabinetWaybillEntity {
  CabinetWaybillEntity({this.waybillNo = '', this.receiverMobile = ''});

  factory CabinetWaybillEntity.fromJson(Map<String, dynamic> json) => $CabinetWaybillEntityFromJson(json);

  Map<String, dynamic> toJson() => $CabinetWaybillEntityToJson(this);

  String waybillNo;
  String? brandCode;
  String? brandName;
  int? waybillStatus;
  int? waybillType;
  String receiverMobile;
  String? checkCode;
  int? messageState;
  int? messageType;
  String? inboundTime;
  String? outboundTime;
  double? effectTimeFee;
  String? cabinetName;
  String? cabinetBoxNo;
}
