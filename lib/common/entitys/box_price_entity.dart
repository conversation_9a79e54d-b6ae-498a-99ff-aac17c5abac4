import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/box_price_entity.g.dart';

@JsonSerializable()
class BoxPriceEntity {
  BoxPriceEntity();

  factory BoxPriceEntity.fromJson(Map<String, dynamic> json) => $BoxPriceEntityFromJson(json);

  Map<String, dynamic> toJson() => $BoxPriceEntityToJson(this);

  int? supers;
  int? huge;
  int? large;
  int? medium;
  int? small;
  int? mini;
  int? micro;
}
