import 'dart:convert';

import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/data_pj_entity.g.dart';

@JsonSerializable()
class DataPjEntity {
  String? cabinetLocationId;
  String? cabinetLocationName;
  String? cabinetLocationCode;
  int? num;

  DataPjEntity();

  factory DataPjEntity.fromJson(Map<String, dynamic> json) => $DataPjEntityFromJson(json);

  Map<String, dynamic> toJson() => $DataPjEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
