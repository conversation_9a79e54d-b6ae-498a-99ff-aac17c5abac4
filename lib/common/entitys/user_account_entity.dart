import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/user_account_entity.g.dart';

@JsonSerializable()
class UserAccountEntity {
  UserAccountEntity();

  factory UserAccountEntity.fromJson(Map<String, dynamic> json) => $UserAccountEntityFromJson(json);

  Map<String, dynamic> toJson() => $UserAccountEntityToJson(this);

  String? createBy;
  String? createDate;
  String? deviceId;
  String? deviceName;
  String? expireDate;
  String? company;
  UserAccountExt? ext;
  String? id;
  bool? isIntercept;
  String? loginName;
  String? mobile;
  String? name;
  String? openId;
  String? password;
  String? password2;
  String? pjCode;
  String? preSite;
  String? remarks;
  String? siteCode;
  String? siteName;
  String? sms;
  String? threeCode;
  String? token;
  String? type;
  String? updateBy;
  String? updateDate;
  String? userCode;
}

@JsonSerializable()
class UserAccountExt {
  UserAccountExt();

  factory UserAccountExt.fromJson(Map<String, dynamic> json) => $UserAccountExtFromJson(json);

  Map<String, dynamic> toJson() => $UserAccountExtToJson(this);
}
