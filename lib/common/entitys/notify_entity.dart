import 'dart:convert';

import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/notify_entity.g.dart';

@JsonSerializable()
class NotifyEntity {
  String? id;
  int? noticeType;
  int unReadCount;
  String? noticeTitle;
  String? content;
  String? noticeSubtitle;
  String? onlineTime;
  int? readStatus;

  NotifyEntity({this.unReadCount = 0});

  factory NotifyEntity.fromJson(Map<String, dynamic> json) => $NotifyEntityFromJson(json);

  Map<String, dynamic> toJson() => $NotifyEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
