import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/package_view_entity.g.dart';

@JsonSerializable()
class PackageViewEntity {
  PackageViewEntity(
      {this.id = '',
      this.cabinetLocationCode = '',
      this.cabinetLocationName = '',
      this.waybillOrderId = '',
      this.receiverMobile = '',
      this.keepEffectTime = '',
      this.outboundTime = '',
      this.createTime = '',
      this.orderId = '',
      this.keepEffect = false,
      this.inboundTime = '',
      this.yzAccountId = '',
      });

  factory PackageViewEntity.fromJson(Map<String, dynamic> json) => $PackageViewEntityFromJson(json);

  Map<String, dynamic> toJson() => $PackageViewEntityToJson(this);
  
  String id;
  String? waybillNo;
  String cabinetLocationCode;
  String cabinetLocationName;
  String waybillOrderId;
  String? brandCode;
  String? brandName;
  String orderId;
  String? receiverName;
  String receiverMobile;
  String? virtualPhone;  //虚拟手机号;
  String keepEffectTime;
  String? cabinetBoxLabel;
  String inboundTime;
  String? yzAccountId;
  String createTime;
  int? orderType;
  int? outboundType;
  String outboundTime;
  String? checkCode;
  int? status;
  int? messageSmsStatus;
  int? messageWxStatus;
  int? price;
  int? messageType;
  int? cabinetType;
  String? cabinetName;
  String? cabinetBoxId;
  bool keepEffect;
  int? hasOutbound;
  int? inboundDeliveryStatus;
  int? storeType;
  String? shelfName;
  String? overdueTime;
  String? overdueEffectTime;
  String? inboundImageUrl;
  String? outboundImageUrl;
  String? inboundUserName;
  String? inboundUserMobile;
  int? inboundUserFee;
  int? hasSign;
  int? signDeliveryStatus;
  int? hasReturn;
  int? returnDeliveryStatus;
  int? hasDp;
  int? dpStatus;
  String? dpMsg;
}
