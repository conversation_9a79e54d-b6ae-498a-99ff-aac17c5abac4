import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/photo_entity.g.dart';

@JsonSerializable()
class PhotoEntity {
  PhotoEntity();

  factory PhotoEntity.fromJson(Map<String, dynamic> json) => $PhotoEntityFromJson(json);

  Map<String, dynamic> toJson() => $PhotoEntityToJson(this);

  String? id;
  String? path;
  String? fileName;
  String? status;
  String? type;
  String? createDate;
  String? ymd;
  String? msg;
  String? sheetNo;
  String? company;
}
