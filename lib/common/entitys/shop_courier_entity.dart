import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/shop_courier_entity.g.dart';
import 'dart:convert';

@JsonSerializable()
class ShopCourierEntity {
	String? id;
	String? courierId;
	String? courierName;
	String? courierMobile;
	String? shopAddress;
	String? shopLinkman;
	String? shopMobile;
	String? shopId;
	String? shopName;
	String? brandCode;
	double? deliveryFee;
	int? status;

	ShopCourierEntity();

	factory ShopCourierEntity.fromJson(Map<String, dynamic> json) => $ShopCourierEntityFromJson(json);

	Map<String, dynamic> toJson() => $ShopCourierEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}