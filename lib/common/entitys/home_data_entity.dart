import 'dart:convert';

import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/home_data_entity.g.dart';

@JsonSerializable()
class HomeDataEntity {
  int? waitPickUpNum;
  int? keepEffectNum;
  int? waitTakenNum;
  int? shelfWaitPickUpNum;
  int? shelfKeepEffectNum;

  int? takenNum;

  HomeDataEntity();

  factory HomeDataEntity.fromJson(Map<String, dynamic> json) => $HomeDataEntityFromJson(json);

  Map<String, dynamic> toJson() => $HomeDataEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
