import 'dart:convert';

import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/today_summary_entity.g.dart';

@JsonSerializable()
class TodaySummaryEntity {
  int? inboundCount;
  int? outboundCount;
  int? income;
  int? outcome;
  int? storeIncome = 0;        // 暂存费收入
  int? sendIncome = 0;         // 寄件费收入
  int? dispatchIncome = 0;     // 投柜费收入
  int? dispatchOverTimeIncome = 0; // 用户超期费收入
  int? keepOverdueIncome = 0;  // 暂存超期费收入
  int? bookIncome = 0;         // 预约格口费用收入
  int? rentIncome = 0;         // 租用格口收入
  
  int? smsOutgo = 0;          // 短信费支出
  int? wxOutgo = 0;           // 微信通知费支出
  int? virtualSmsOutgo = 0;   // 代发短信支出
  int? serviceFeeOutgo = 0;   // 服务费支出
  int? bdpoutgo = 0;          // 补到派支出
  int? dayServiceFeeOutgo = 0; // 日服务费支出
  int? replInboundoutgo = 0;   // 代入库费支出
  int? balanceWarningOutgo = 0; // 金额预警支出
  int? simCardRechargeOutgo = 0; // 流量卡流量充值支出
  int? simcardRenewOutgo = 0;   // 流量卡续费支出

  TodaySummaryEntity();

  factory TodaySummaryEntity.fromJson(Map<String, dynamic> json) => $TodaySummaryEntityFromJson(json);

  Map<String, dynamic> toJson() => $TodaySummaryEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

 
}
