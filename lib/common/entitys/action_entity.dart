import 'package:cabinet_flutter_app/generated/json/action_entity.g.dart';
import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';

@JsonSerializable()
class ActionEntity {
  ActionEntity();

  factory ActionEntity.fromJson(Map<String, dynamic> json) => $ActionEntityFromJson(json);

  Map<String, dynamic> toJson() => $ActionEntityToJson(this);

  String? actionCode;
  String? actionName;
  String? actionTime;
  String? desc;
  String? descPhotoUrl;
}
