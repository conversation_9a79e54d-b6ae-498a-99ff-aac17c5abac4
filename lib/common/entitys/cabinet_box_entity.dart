import 'dart:convert';

import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/cabinet_box_entity.g.dart';

@JsonSerializable()
class CabinetBoxEntity {
  late String cabinetBoxId;
  late int boxType;
  late String boxLabel;
  late String cabinetName;
  late String cabinetId;
  late int availableCount;

  CabinetBoxEntity();

  factory CabinetBoxEntity.fromJson(Map<String, dynamic> json) => $CabinetBoxEntityFromJson(json);

  Map<String, dynamic> toJson() => $CabinetBoxEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
