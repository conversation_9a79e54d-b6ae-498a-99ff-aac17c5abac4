import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/cabinet_location_entity.g.dart';

@JsonSerializable()
class CabinetLocationEntity {
  CabinetLocationEntity();

  factory CabinetLocationEntity.fromJson(Map<String, dynamic> json) => $CabinetLocationEntityFromJson(json);

  Map<String, dynamic> toJson() => $CabinetLocationEntityToJson(this);

  String? id;
  String? siteId;
  String? code;
  String? name;
  int? type;
  String? linkman;
  String? mobile;
  String? address;
  String? smsPrice;
  String? wxPrice;
  int? noticeType;
  String? cabinetType;
  String? servicePhone;
  String? provinceCode;
  String? provinceName;
  String? areaCode;
  String? areaName;
  String? cityName;
  String? cityCode;
  String? longitude;
  String? latitude;
  int? status;
  String? applyTime;
  int? switchRent;
  int? boxAvailableNum;
  int? boxEmptyNum;
  String? cabinetLocationId;
  bool? inCommonUse;
  bool isCollect = false;
}
