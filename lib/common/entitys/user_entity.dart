import 'package:cabinet_flutter_app/common/entitys/brand_bind_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/user_account_entity.dart';
import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/user_entity.g.dart';

@JsonSerializable()
class UserEntity {
  UserEntity({this.name = '', this.realName = ''});

  factory UserEntity.fromJson(Map<String, dynamic> json) => $UserEntityFromJson(json);

  Map<String, dynamic> toJson() => $UserEntityToJson(this);

  String? id;
  String? siteId;
  String? userId;
  String? accountId;
  String? brandCode;
  String? type;
  String? siteName;
  String? loginName;
  double? discountFactor;
  int? cutType;
  int? cutAmount;
  String name;
  String realName;
  String? idNumber;
  String? yundaAccount;
  String? yzPhone;
  String? deployCode;
  bool? isAdmin;
  int? hasReal;
  String? shopCode;
  String? shopName;
  String? shopMobile;
  int? reviewStatus;
  String? shopAddress;
  String? siteCode;
  String? userCode;
  String? userName;
  String? kdyId;
  int? hasSetPayPwd;
  int? hasBindAlipay;
  int? userType;
  int? hasAdmin;
  int? hasWallet;
  int? switchSend;
  String? shopId;

  List<UserAccountEntity?>? accounts;
  List<String?>? companys;
  List<BrandBindEntity?>? brands;
}
