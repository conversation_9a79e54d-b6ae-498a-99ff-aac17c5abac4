import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/brand_bind_entity.g.dart';

@JsonSerializable()
class BrandBindEntity {
  BrandBindEntity();

  factory BrandBindEntity.fromJson(Map<String, dynamic> json) => $BrandBindEntityFromJson(json);

  Map<String, dynamic> toJson() => $BrandBindEntityToJson(this);
  String? id;
  String? brandCode;
  int? allowIn;
  int? syncTrace;
  int? switchAutoSign;
  int? autoSignDay;
}
