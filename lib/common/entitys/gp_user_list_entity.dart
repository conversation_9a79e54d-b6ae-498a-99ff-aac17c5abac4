import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/gp_user_list_entity.g.dart';
import 'dart:convert';
export 'package:cabinet_flutter_app/generated/json/gp_user_list_entity.g.dart';

@JsonSerializable()
class GpUserListEntity {
	late String id;
	late String name;
	late String company;
	late String mobile;
	late String siteCode;
	late String loginName;
	late String password;
	late String deviceId;
	late String deviceName;
	late String threeCode;
	late bool isPassed;
	late bool isYbx;
	late bool isXingzhe;
	late bool isZtoPda;

	GpUserListEntity();

	factory GpUserListEntity.fromJson(Map<String, dynamic> json) => $GpUserListEntityFromJson(json);

	Map<String, dynamic> toJson() => $GpUserListEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}