import 'dart:convert';

import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/cabinet_shop_entity.g.dart';

@JsonSerializable()
class CabinetShopEntity {
  String? id;
  String? createBy;
  String? createTime;
  String? updateBy;
  String? updateTime;
  String? siteId;
  String? shopId;
  String? code;
  String? name;
  int? type;
  String? linkman;
  String? mobile;
  int? smsPrice;
  int? wxPrice;
  int? noticeType;
  int? cabinetType;
  String? servicePhone;
  String? hostActivationCode1;
  int? hostStatus1;
  String? hostVersion1;
  String? hostSerialPort1;
  String? hostBaudRate1;
  String? hostFactory1;
  String? provinceCode;
  String? provinceName;
  String? cityCode;
  String? cityName;
  String? areaCode;
  String? areaName;
  String? streetCode;
  String? streetName;
  String? address;
  double? longitude;
  double? latitude;
  int? status;
  int? applyTime;
  int? hasFeederLine;
  String? deleted;
  int? hugeCount;
  int? largeCount;
  int? mediumCount;
  int? smallCount;
  int? miniCount;
  int? superCount;
  int? microCount;


  CabinetShopEntity();

  factory CabinetShopEntity.fromJson(Map<String, dynamic> json) => $CabinetShopEntityFromJson(json);

  Map<String, dynamic> toJson() => $CabinetShopEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
