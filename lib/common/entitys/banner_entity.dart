import 'dart:convert';

import 'package:cabinet_flutter_app/generated/json/banner_entity.g.dart';
import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';

@JsonSerializable()
class BannerEntity {
  String? id;
  String? fileUrl;

  BannerEntity();

  factory BannerEntity.fromJson(Map<String, dynamic> json) => $BannerEntityFromJson(json);

  Map<String, dynamic> toJson() => $BannerEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
