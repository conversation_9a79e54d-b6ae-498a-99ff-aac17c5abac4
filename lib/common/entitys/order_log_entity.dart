import 'dart:convert';

import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/order_log_entity.g.dart';

@JsonSerializable()
class OrderLogEntity {
  String? id;
  String? createTime;
  String? updateTime;
  String? orderId;
  int? type;
  int? actionType;
  String? scanType;
  String? mobile;
  String? smsTplId;
  String? smsContent;
  int? wordNum;
  int? billingNum;
  int? billingPrice;
  int? billingMoney;
  int? secretWaybill;
  String? ym;
  String? content;
  String? ymd;
  int? status;
  String? action;
  String? beginTime;
  String? exception;
  String? errorMessage;

  OrderLogEntity();

  factory OrderLogEntity.fromJson(Map<String, dynamic> json) => $OrderLogEntityFromJson(json);

  Map<String, dynamic> toJson() => $OrderLogEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
