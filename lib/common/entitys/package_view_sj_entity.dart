import 'dart:convert';

import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/package_view_sj_entity.g.dart';

@JsonSerializable()
class PackageViewSjEntity {
  String? id;
  String? createBy;
  String? createTime;
  String? updateTime;
  int? orderType;
  int? orderStatus;
  String? orderNo;
  String? bizNo;
  String? channelId;
  String? channelName;
  String? siteId;
  String? siteName;
  String? shopId;
  String? shopName;
  String? cabinetLocationId;
  String? cabinetLocationCode;
  String? cabinetLocationName;
  String? cabinetLocationAddress;
  String? cabinetId;
  String? cabinetCode;
  String? cabinetName;
  int? cabinetSerialNo;
  String? cabinetBoxId;
  int? cabinetBoxType;
  String? cabinetBoxLabel;
  int? cabinetBoxPcbNo;
  int? storeType;
  String? receiverName;
  String? receiverMobile;
  String? receiverMobileLast4;
  String? checkCode;
  int? messageType;
  int? messageSmsStatus;
  int? messageWxStatus;
  String? inboundUserId;
  String? inboundUserName;
  int? inboundUserFee;
  String? inboundUserMobile;
  String? inboundTime;
  String? inboundYm;
  String? inboundYmd;
  int? hasOutbound;
  int? outboundUserFee;
  String? remarks;
  int? serviceDuration;
  int? version;
  int? sendStatus;
  String? brandCode;
  String? brandName;
  String? senderName;
  String? senderIdNumber;
  String? senderMobile;
  String? senderProvinceCode;
  String? senderProvinceName;
  String? senderCityCode;
  String? senderCityName;
  String? senderAreaCode;
  String? senderAreaName;
  String? senderStreetCode;
  String? senderStreetName;
  String? senderAddress;
  String? receiverProvinceCode;
  String? receiverProvinceName;
  String? receiverCityCode;
  String? receiverCityName;
  String? receiverAreaCode;
  String? receiverAreaName;
  String? receiverStreetCode;
  String? receiverStreetName;
  String? receiverAddress;
  String? waybillNo;
  int? show;

  PackageViewSjEntity();

  factory PackageViewSjEntity.fromJson(Map<String, dynamic> json) => $PackageViewSjEntityFromJson(json);

  Map<String, dynamic> toJson() => $PackageViewSjEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
