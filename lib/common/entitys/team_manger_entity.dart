import 'dart:convert';

import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/team_manger_entity.g.dart';

@JsonSerializable()
class TeamMangerEntity {
  String? id;
  String? createTime;
  String? updateTime;
  String? userId;
  String? shopId;
  String? name;
  int? hasAdmin;
  int? hasReal;
  int? hasWallet;
  String? realName;
  String? idNumber;
  int? userType;
  String? username;
  String? nickname;
  bool? sex;
  String? phone;
  int? status;
  String? organizationId;

  TeamMangerEntity();

  factory TeamMangerEntity.fromJson(Map<String, dynamic> json) => $TeamMangerEntityFromJson(json);

  Map<String, dynamic> toJson() => $TeamMangerEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
