import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/notice_type_entity.g.dart';

@JsonSerializable()
class NoticeTypeEntity {
  NoticeTypeEntity();

  factory NoticeTypeEntity.fromJson(Map<String, dynamic> json) => $NoticeTypeEntityFromJson(json);

  Map<String, dynamic> toJson() => $NoticeTypeEntityToJson(this);

  String? id;
  String? type;
  int? count;
  String? dateTime;
  String? content;
  String? subText;
}
