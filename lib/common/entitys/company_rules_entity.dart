import 'dart:convert';

import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/company_rules_entity.g.dart';

@JsonSerializable()
class CompanyRulesEntity {
  String? regx;
  String? code;

  CompanyRulesEntity();

  factory CompanyRulesEntity.fromJson(Map<String, dynamic> json) => $CompanyRulesEntityFromJson(json);

  Map<String, dynamic> toJson() => $CompanyRulesEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
