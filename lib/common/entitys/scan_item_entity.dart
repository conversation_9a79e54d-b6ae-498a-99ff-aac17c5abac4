import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/scan_item_entity.g.dart';

@JsonSerializable()
class ScanItemEntity {
  ScanItemEntity();

  factory ScanItemEntity.fromJson(Map<String, dynamic> json) => $ScanItemEntityFromJson(json);

  Map<String, dynamic> toJson() => $ScanItemEntityToJson(this);

  String? id;
  String? orderNo;
  String? orderType;
  String? bizNo;
  String? channelId;
  String? channelName;
  String? siteId;
  String? siteName;
  String? cabinetLocationId;
  String? cabinetLocationName;
  String? cabinetName;
  String? courierId;
  String cabinetLocationCode = '';
  String? cabinetId;
  String? cabinetCode;
  String? cabinetNo;
  String? cabinetBoxPcbNo;
  String? cabinetBoxId;
  String? cabinetBoxLabel;
  String waybillNo = '';
  String brandCode = "";
  String brandName = '';
  String virtualPhone = '';  //虚拟手机号
  String? boxLabel;
  String? receiverName;
  String receiverMobile = '';
  String receiverMobileLast4 = '';
  bool? normalWaybill;
  int? sensitiveConsumer;
  int? newCustomer;
  int? boxType;
  int? needCallCustomer;
  String? shortNo;
  String? uno;
  String? cod;
  String? inboundUserCode;
  String? inboundUserName;
  String? inboundPhotoUrl;
  String? inboundDate;
  String? inboundYm;
  String? inboundYmd;
  String? inboundType;
  String? keepEffectTime;

  String? inboundDeliveryReceiptDate;
  String? inboundDeliveryState;
  String? inboundDeliveryYmd;

  String? checkCode;
  String? messageType;
  String? messageTplId;
  String? messageStatus;
  String? messageDeliveryTime;
  String? messageReceiptTime;
  String? messageReceiptContent;
  String? inboundUserId;
  String? inboundTime;
  String? outboundTime;
  String? outboundImageUrl;
  String? serviceDuration;
  String? isOutbound;
  String? outboundBatchNo;
  String? outboundUserCode;
  String? outboundUserName;
  String? outboundDate;
  String? outboundPhotoUrl;
  String? outboundYm;
  String? outboundYmd;

  String? isSign;
  String? signDeliveryReceiptDate;
  String? signDeliveryState;
  String? signDeliveryYmd;

  String? isBack;
  String? backReason;
  String? backUserCode;
  String? backUserName;
  String? backDate;
  String? backYm;
  String? backYmd;

  String? backDeliveryReceiptDate;
  String? backDeliveryState;
  String? backDeliveryYmd;

  String? pjDeliveryReceiptDate;
  String? pjDeliveryState;

  String? isKeep;
  String? keepDayDesc;
  String? keepEffectDate;

  String? smsState;
  String? isSendSms;
  String? smsTplCode;
  String? sendSmsDate;
  String? createBy;
  String? createDate;
  String? updateBy;
  String? updateDate;
  String? remarks;

  String? scanId;
  String? type;
  String? status;
  String? engine;
  String? rule;
  String? day;
  String? shelfNo;
  String? no;
  String? ymd;
  String? isHb;
  String? isIntercept;
  String? msg;
  String virtualNumber = '';
  String platform = '';
  String? phone;
  String? secretWaybill;
  String? hasSubstituteSms;
  String? inboundImageUrl;

  bool? addInCabinet = false;
  int? deliveryWaybill = 0;
}
