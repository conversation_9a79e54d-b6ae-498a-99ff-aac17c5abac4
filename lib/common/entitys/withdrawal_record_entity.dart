import 'dart:convert';

import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/withdrawal_record_entity.g.dart';

@JsonSerializable()
class WithdrawalRecordEntity {
  String? id;
  String? createTime;
  String? updateTime;
  int? organizationType;
  String? organizationId;
  String? accountId;
  String? taskNo;
  String? applyTime;
  String? applyYm;
  String? applyYmd;
  int? taskTargetType;
  String? taskTargetId;
  String? taskTargetName;
  int? withdrawalChannel;
  int? withdrawalMoney;
  int? withdrawalServiceFee;
  int? money;
  int? status;

  WithdrawalRecordEntity();

  factory WithdrawalRecordEntity.fromJson(Map<String, dynamic> json) => $WithdrawalRecordEntityFromJson(json);

  Map<String, dynamic> toJson() => $WithdrawalRecordEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
