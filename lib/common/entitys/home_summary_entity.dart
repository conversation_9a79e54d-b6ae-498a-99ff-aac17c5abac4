import 'dart:convert';

import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/home_summary_entity.g.dart';

@JsonSerializable()
class HomeSummaryEntity {
  int? inboundCount;
  int? outboundCount;
  int? income;
  int? outcome;
  int? gt3dayKeepEffectCount;
  int? gt7dayKeepEffectCount;
  int? smsErrorCount;
  int? expressGateWayErrorCount;

  HomeSummaryEntity();

  factory HomeSummaryEntity.fromJson(Map<String, dynamic> json) => $HomeSummaryEntityFromJson(json);

  Map<String, dynamic> toJson() => $HomeSummaryEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
