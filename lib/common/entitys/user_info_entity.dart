import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/user_info_entity.g.dart';
import 'dart:convert';
export 'package:cabinet_flutter_app/generated/json/user_info_entity.g.dart';

@JsonSerializable()
class UserInfoEntity {
  late String? channel;
  late int? createTime;
  late String? type;
  late String? deviceId;
  late String cabinetLocationIds;
  late String? cabinetNameList;
  late String? deviceModel;
  late int? id;
  late String? loginName;
  late String? mobile;
  late String? name;
  late String? password;
  late int? shopId;
  late int? siteId;
  late String? sopAccountId;
  late String? stationAddress;
  late String? stationName;
  late int? updateTime;
  late int? verified;

  UserInfoEntity();

  factory UserInfoEntity.fromJson(Map<String, dynamic> json) => $UserInfoEntityFromJson(json);

  Map<String, dynamic> toJson() => $UserInfoEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
