import 'dart:convert';

import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/customer_mobile_entity.g.dart';

@JsonSerializable()
class CustomerMobileEntity{

	late String name;
	late String mobile;
  late String label;
  late int labelNotice;
  late int isNew;
	late List<CustomerMobileOrderList> orderList;
  
  CustomerMobileEntity();

  factory CustomerMobileEntity.fromJson(Map<String, dynamic> json) => $CustomerMobileEntityFromJson(json);

  Map<String, dynamic> toJson() => $CustomerMobileEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class CustomerMobileOrderList {

	late String brandCode;
	late String waybillNo;
	late String keepEffectTime;
	late String cabinetName;
	late String cabinetId;
	late String cabinetBoxId;
	late int cabinetBoxType;
	late String cabinetBoxLabel;
  
  CustomerMobileOrderList();

  factory CustomerMobileOrderList.fromJson(Map<String, dynamic> json) => $CustomerMobileOrderListFromJson(json);

  Map<String, dynamic> toJson() => $CustomerMobileOrderListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}