import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/cabinet_entity.g.dart';

@JsonSerializable()
class CabinetEntity {
  CabinetEntity({this.appointJson = '{}', this.hasCollected = 0});

  factory CabinetEntity.fromJson(Map<String, dynamic> json) => $CabinetEntityFromJson(json);

  Map<String, dynamic> toJson() => $CabinetEntityToJson(this);

  String? siteId;
  String? id;
  String? code = '';
  String? name = '';
  int? type;
  String? cabinetLocationId;
  String? provinceName;
  String? provinceCode;
  String? areaName;
  String? areaCode;
  String? cityName;
  String? cityCode;
  String? address = '';
  String? dispatchJson;
  String? rentJson;
  String appointJson;
  int? boxTotalNum;
  int? boxEmptyNum;
  int? boxAvailableNum;
  int? superCount;
  int? hugeCount;
  int? largeCount;
  int? mediumCount;
  int? smallCount;
  int? miniCount;
  int? microCount;
  int? status;
  int? hasUsed;
  int overdueOutboundDays = 5;
  double? distance;
  double? latitude;
  double? longitude;
  int hasCollected;
  int? hasAddCabinet;
  int? switchBatch;
  int? switchBook;
  String? bookJson;
  int? switchRent;
  int? hasChangeBrand;
}
