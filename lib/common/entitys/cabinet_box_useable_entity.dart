import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/cabinet_box_useable_entity.g.dart';
import 'dart:convert';

@JsonSerializable()
class CabinetBoxUseableEntity {

	String? code;
	String? cabinetId;
	String? cabinetName;
	int? superCount;
	int? hugeCount;
	int? largeCount;
	int? mediumCount;
	int? smallCount;
	int? miniCount;
	int? microCount;
	int? total;
  
  CabinetBoxUseableEntity();

  factory CabinetBoxUseableEntity.fromJson(Map<String, dynamic> json) => $CabinetBoxUseableEntityFromJson(json);

  Map<String, dynamic> toJson() => $CabinetBoxUseableEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}