import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/trade_record_entity.g.dart';

@JsonSerializable()
class TradeRecordEntity {
  TradeRecordEntity({this.tradeMoney = 0, this.isIncome = false});

  factory TradeRecordEntity.fromJson(Map<String, dynamic> json) => $TradeRecordEntityFromJson(json);

  Map<String, dynamic> toJson() => $TradeRecordEntityToJson(this);

  String? tradeNo;
  String? shopId;
  int? tradeSubject;
  int? tradeType;
  String? createTime;
  String? updateTime;
  String? tradeYmd;
  int tradeMoney;
  int? payType;
  bool isIncome;
  String? remark;
}
