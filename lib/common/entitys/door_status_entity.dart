import 'dart:convert';

import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/door_status_entity.g.dart';

export 'package:cabinet_flutter_app/generated/json/door_status_entity.g.dart';

@JsonSerializable()
class DoorStatusEntity {
  late String status;
  late String serialNo;
  late String pcbNo;

  DoorStatusEntity();

  factory DoorStatusEntity.fromJson(Map<String, dynamic> json) => $DoorStatusEntityFromJson(json);

  Map<String, dynamic> toJson() => $DoorStatusEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
