import 'dart:convert';

import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/update_app_entity.g.dart';

@JsonSerializable()
class UpdateAppEntity {

	String? appName;
	String? appVer;
	String? fileUrl;
	String? releaseNotes;
	int? hasForce;
  
  UpdateAppEntity();

  factory UpdateAppEntity.fromJson(Map<String, dynamic> json) => $UpdateAppEntityFromJson(json);

  Map<String, dynamic> toJson() => $UpdateAppEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}