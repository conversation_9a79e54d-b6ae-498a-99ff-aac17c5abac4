import 'dart:convert';

import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/keep_effect_summary_entity.g.dart';

@JsonSerializable()
class KeepEffectSummaryEntity {
  int? keepEffectCount;
  int? gt3dayKeepEffectCount;
  int? gt7dayKeepEffectCount;

  KeepEffectSummaryEntity({this.keepEffectCount = 0, this.gt3dayKeepEffectCount = 0, this.gt7dayKeepEffectCount = 0});

  factory KeepEffectSummaryEntity.fromJson(Map<String, dynamic> json) => $KeepEffectSummaryEntityFromJson(json);

  Map<String, dynamic> toJson() => $KeepEffectSummaryEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
