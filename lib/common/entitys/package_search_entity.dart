import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/package_search_entity.g.dart';
import 'dart:convert';
import 'package:intl/intl.dart';

@JsonSerializable()
class PackageSearchEntity {
  String beginYmd = new DateFormat("yyyy-MM-dd").format(DateTime.now());
  String? timeRanger;
  String endYmd = new DateFormat("yyyy-MM-dd").format(DateTime.now());
  String? brandCode;
  String? cabinetLocationCode;
  String? keyword;
  bool? inboundError;
  int? keepEffectStatus;
  int? keepEffectDay;
  bool? signError;
  bool? returnError;
  bool? smsError;
  bool? smsSuccess;
  bool? overdue;
  int? outbound;
  int? size;
  String? storeType;
  String? current;

  
  PackageSearchEntity();

  factory PackageSearchEntity.fromJson(Map<String, dynamic> json) => $PackageSearchEntityFromJson(json);

  Map<String, dynamic> toJson() => $PackageSearchEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}