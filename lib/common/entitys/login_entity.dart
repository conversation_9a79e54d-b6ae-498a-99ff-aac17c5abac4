import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/login_entity.g.dart';

@JsonSerializable()
class LoginEntity {
  LoginEntity({this.tokenName = '', this.tokenValue = ''});

  factory LoginEntity.fromJson(Map<String, dynamic> json) => $LoginEntityFromJson(json);

  Map<String, dynamic> toJson() => $LoginEntityToJson(this);

  int? expires;
  String? token;
  String tokenName;
  String tokenValue;
  int? tokenTimeOut;
}
