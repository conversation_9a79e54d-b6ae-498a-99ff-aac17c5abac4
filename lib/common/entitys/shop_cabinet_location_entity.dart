import 'dart:convert';

import 'package:cabinet_flutter_app/generated/json/base/json_field.dart';
import 'package:cabinet_flutter_app/generated/json/shop_cabinet_location_entity.g.dart';

@JsonSerializable()
class ShopCabinetLocationEntity {
  String? id;
  int? hasAddCabinet;
  int? hasChangeBrand;
  String? code;
  String? name;
  int? type;
  String? linkman;
  String? mobile;
  int? smsPrice;
  int? wxPrice;
  int? noticeType;
  int? cabinetType;
  String? servicePhone;
  String? hostActivationCode1;
  int? hostStatus1;
  String? hostFactory1;
  String? provinceCode;
  String? provinceName;
  String? cityCode;
  String? cityName;
  String? areaCode;
  String? areaName;
  String? address;
  double? longitude;
  double? latitude;
  int? status;
  int? applyTime;
  String? dispatchJson;
  int? switchRent;
  String? rentJson;
  int? switchAppoint;
  String? appointJson;
  bool? switchCustomer;
  String? customerJson;
  bool? switchKeep;
  String? keepJson;
  int? cabinetNoType;
  List<CabinetItemEntity>? cabinetList;

  ShopCabinetLocationEntity();

  factory ShopCabinetLocationEntity.fromJson(Map<String, dynamic> json) => $ShopCabinetLocationEntityFromJson(json);

  Map<String, dynamic> toJson() => $ShopCabinetLocationEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
@JsonSerializable()
class CabinetItemEntity {

  String? id;
  String? cabinetLocationId;
  String? code;
  String? name;
  int? type;
  int? locationNo;
  String? columns;
  String? hostBoxArray;
  int? serialNo;
  int? boxTotalNum;
  int? boxEmptyNum;
  int? boxAvailableNum;
  int? status;
  List<CabinetBoxItem>? boxList;

  CabinetItemEntity();

  factory CabinetItemEntity.fromJson(Map<String, dynamic> json) => $CabinetItemEntityFromJson(json);

  Map<String, dynamic> toJson() => $CabinetItemEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class CabinetBoxItem {
  String? id;
  String? boxLabel;
  int? pcbNo;
  int? type;
  int? specialStatus;
  int? workStatus;
  int? status;
  int? hasSmsError;
  int? hasKeepEffect;
  String? inboundTime;
  String? orderId;

  CabinetBoxItem();

  factory CabinetBoxItem.fromJson(Map<String, dynamic> json) =>
      $CabinetBoxItemFromJson(json);

  Map<String, dynamic> toJson() => $CabinetBoxItemToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}