import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:cabinet_flutter_app/common/ab/provider/task/ScanDbProvider.dart';
import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/dao/CabinetDao.dart';
import 'package:cabinet_flutter_app/common/dao/CourierDao.dart';
import 'package:cabinet_flutter_app/common/dao/DaoResult.dart';
import 'package:cabinet_flutter_app/common/dao/UserDao.dart';
import 'package:cabinet_flutter_app/common/dao/WaybillDao.dart';
import 'package:cabinet_flutter_app/common/entitys/box_item_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_box_useable_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/customer_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/customer_mobile_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/door_status_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/mobile_guss_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/mqtt_config_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/scan_item_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/shop_cabinet_location_entity.dart';
import 'package:cabinet_flutter_app/common/local/LocalStorage.dart';
import 'package:cabinet_flutter_app/common/utils/BrandUtil.dart';
import 'package:cabinet_flutter_app/common/utils/CheckUtils.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/LoadingUtil.dart';
import 'package:cabinet_flutter_app/common/utils/LocalImageUtil.dart';
import 'package:cabinet_flutter_app/common/utils/MqttUtils.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/utils/RegExpUtil.dart';
import 'package:cabinet_flutter_app/common/utils/ScanUtils.dart';
import 'package:cabinet_flutter_app/common/utils/SoundUtils.dart';
import 'package:cabinet_flutter_app/common/utils/ThrottleUtil.dart';
import 'package:cabinet_flutter_app/common/utils/VoiceUtilDialog.dart';
import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/generated/json/mobile_guss_entity.g.dart';
import 'package:cabinet_flutter_app/generated/json/shop_cabinet_location_entity.g.dart';
import 'package:cabinet_flutter_app/widget/CountDownWidget.dart';
import 'package:cabinet_flutter_app/widget/keyborad/MyKeyEvent.dart';
import 'package:cabinet_flutter_app/widget/myDialog/MyDialog.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/utils.dart';
import 'package:intl/intl.dart';
import 'package:mqtt5_client/mqtt5_client.dart';
import 'package:mqtt5_client/mqtt5_server_client.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:scan/device_info.dart';
import 'package:scan/scan.dart';

mixin ScanMixin<T extends StatefulWidget> on State<T> {
  final GlobalKey<ScaffoldState> scaffoldKey_ = new GlobalKey<ScaffoldState>();
  bool isChooseBrandCode = false;
  bool isBatch = false; // 是否批量
  bool isBlind = false; // 是否盲投
  TextEditingController waybillNoEditingController = TextEditingController();
  TextEditingController mobileEditingController = TextEditingController();
  TextEditingController nameEditingController = TextEditingController();
  FocusNode mobileFocusNode_ = FocusNode();
  FocusNode nameFocusNode_ = FocusNode();
  TextEditingController cabinetBoxController = TextEditingController();
  FocusNode cabinetBoxFocusNode_ = FocusNode();
  GlobalKey<CountDownWidgetState> countDownKey3 = GlobalKey();

  String currentChooseBrandCode = '';

  TextEditingController textEditingController = TextEditingController();

  bool isInProcess = false; // 是否正在扫码处理中
  Scan scanPlugin = Scan();
  ScanView? scanView;

  // ignore: cancel_subscriptions
  StreamSubscription<dynamic>? scanSubscription_;

  ScanViewController? scanViewController;

//创建FocusNode对象实例
  FocusNode focusNode_ = FocusNode();
  bool checkFlash = false;
  AnimationController? controllerWidth;
  AnimationController? controllerHeight;
  Animation<double>? animationWidth;
  Animation<double>? animationHeight;
  String imageOcrPath = '';
  String imagePath = '';
  String receiver = "";
  ThrottleUtil throttleUtil = ThrottleUtil();
  Timer? timer_;

  // 新定义
  String day = new DateFormat("dd").format(DateTime.now()); // 当前日期
  bool isBottom = true;
  String brandCode = '';
  ScanItemEntity showScanDb = new ScanItemEntity(); // 当前识别的单子
  int count = 0; // 本地数据库扫码条数
  int ocrTempCheck = 5; //
  int countDown = 100; // 倒计时
  bool isCkPhoto = true; // 是否返回图片
  bool isOutCofirm = false; // 是否出库确认(扫码时询问是否出库)
  bool isDelivery = false; // 是否送货上门
  bool isSynchronizedLogistics = false; // 是否同步物流
  bool isBDP = false; // 是否补到派
  bool isAddCabinet = false; // 是否追加入柜
  bool isOCR = true; // 是否开始OCR识别
  bool hasShopOpenAddCabinet = false; // 是否网点开启追加入柜
  bool hasOpenSingleCustomerListNotice = true; // 是否单客户提醒
  bool hasChangeBrand = false; // 是否修改品牌
  bool setting = false;
  bool hasOpenFastInto = false; // 是否极速入库
  Timer? timer;
  Timer? countDownTimer;
  String cabinetCode = '';
  double height = 0.0;
  Map<dynamic, dynamic> brandMap = {};
  var openCabinet = true;
  double scanInputModelHeight = 190;
  String lastCabinetBoxId = '';
  CabinetBoxItem? currentBoxItem;
  CabinetBoxItem? lastBoxItem;
  bool hasFeedBack = false;
  String virtualNumber = '', platform = '';
  String? cabinetId;
  String? currentCabinetId;
  String cabinetLocationCode = '';
  String cabinetName = '';
  String? cabinetBoxId;
  List<CabinetBoxUseableEntity> boxList = [];
  CabinetBoxUseableEntity? selectCabinet;
  int selectBoxType = -1;
  int availableCount = 0;
  int boxType = 0;
  Map cabinetTypeMap = DefaultConfig().configs.CABINET_BOX_TYPE2;
  String? inboundTime;
  String? orderId;
  bool? isCountDown = false;
  int countTime = 60;
  bool? substituteSmsWaybill;
  var waybillRemoteCheckRes;
  bool? vipWaybill;
  bool voiceStatus = false;
  int lastScanTime = 0;
  String lastScanWaybillNo = '';
  String lastScanWaybillNo1 = '';
  String lastOcrPhone = '';
  String lastOcrName = '';
  String lastOcrAddress = '';
  String ocrTexts = '';
  String extraParam = '';
  String virtualPhone = ''; // 虚拟手机号
  bool isCourier = true;
  late MobileGussEntity gussEntity;
  bool isGussWaybill = false; // 是否调用单号检测接口 未调用则延迟等待
  bool isPda = false;
  bool isMqttProcess = false;
  double leadingWidth = 80;
  var ocrTemp; // ocr结果

  List<BoxItemEntity?>? batchBoxList = []; // 批量入库格口列表
  ///EntryScan 独有
  List boxData = [];
  Map<String, List> boxMapPriceData = {};

  /// 是否是柜子 用于处理调用格口开门还是入库操作
  bool isCabinet = true;

  bool isPause = false;
  bool showMobileGussByLast4Button = false;
  bool showKeyboard = false;
  TextInputType textInputType = TextInputType.name;
  bool isIData = false;

  MqttServerClient? mqttClient;

  /// 是否暂停 弹框时为true

  initListener() {
    mobileFocusNode_.addListener(() {
      bool isFocus = mobileFocusNode_.hasFocus;
      if (isFocus) {
        stopSpot();
      } else {
        showKeyboard = false;
        // ignore: unnecessary_null_comparison
        if (showScanDb.waybillNo == '' || showScanDb.waybillNo == null) {
          startSpot(flag: 2);
        }
      }
    });
    nameFocusNode_.addListener(() {
      bool isFocus = nameFocusNode_.hasFocus;
      if (isFocus) {
        showKeyboard = false;
      }
    });
  }

  initFunc({bool? isCabinet}) async {
    if (isCabinet != null) {
      this.isCabinet = isCabinet;
    }
    isCourier = CheckUtils.isCourier(context);
    setState(() {});
  }

  ///开始识别
  startSpot({int? flag}) {
    if (kDebugMode) {
      print('flag ====== $flag');
    }
    isInProcess = false;
    scanViewController?.startSpot();
    setState(() {});
  }

  /// 停止识别
  stopSpot() {
    if (isPda) {
      scanViewController?.stopSpot();
    }
  }

  startRecognizer() {
    scanPlugin.startRecognizer();
  }

  stopRecognizer() {
    scanPlugin.stopRecognizer();
  }

  /// 获取开通的入柜品牌
  getBrandList() async {
    brandMap = {};
    var res = await BrandUtils.getBrandBindList();
    if (res != null) {
      brandMap = res;
      brandMap.putIfAbsent('UNKNOW', () => '其他');
    }
  }

  /// 打开、关闭闪光灯
  setFlash() {
    checkFlash = !checkFlash;
    if (checkFlash) {
      scanViewController?.openFlashlight();
    } else {
      scanViewController?.closeFlashlight();
    }
    setState(() {});
  }

  checkFuc({double height = 190}) async {
    isPda = await CheckUtils.isPda();

    /// 判断是否自动识别快递品牌
    scanInputModelHeight = height;
    isAddCabinet = await CheckUtils.checkAddCabinetIsOpen();
    isSynchronizedLogistics = await CheckUtils.checkSynchronizedLogistics();
    currentChooseBrandCode = await CheckUtils.getBindCompany();
    hasShopOpenAddCabinet = await CheckUtils.checkSiteOpenAddCabinetIsOpen();
    hasOpenSingleCustomerListNotice =
        await CheckUtils.checkSingleCustomerListNotice();
    hasChangeBrand = await CheckUtils.checkChangeBrand();
    isBDP = await CheckUtils.checkDPIsOpen();
    isOCR = await CheckUtils.checkOCRIsOpen();
    hasOpenFastInto = await CheckUtils.checkFastIntoIsOpen();
    setState(() {});
    DeviceInfo deviceInfo = await scanPlugin.getDeviceInfo();
    if (deviceInfo.brand == 'iData') {
      isIData = true;
    }
    if (int.tryParse(deviceInfo.osVersion!, radix: 10)! < 29) {
      CheckUtils.checkPermission(
          Permission.storage, this.context, "请打开存储权限用于存储面单图片");
    }
  }

  ///  去掉空格
  removeSpacesRule(String phone) {
    return phone.replaceAll(new RegExp(r"\s+\b|\b\s"), "");
  }

  /// 语音输入
  openVoiceCountDown() async {
    stopSpot();
    startRecognizer();
    timer_ = Timer.periodic(Duration(milliseconds: 10000), (timer) {
      stopReg();
    });
    await VoiceUtilDialog.show(context, () {
      stopReg();
    });
  }

  buildNewScan(
      {String waybillNo = '',
      String brandCode = '',
      String receiveMobile = '',
      String checkCode = ''}) {
    return ScanDbDbProvider.buildScan(
      waybillNo,
      this.isCabinet ? 'RK' : 'INBOUND',
      brandCode,
      receiverMobile: receiveMobile,
      receiverName: '',
      checkCode: checkCode,
      cod: '0',
      normalWaybill: true,
      cabinetId: currentCabinetId ?? '',
      cabinetBoxId: currentBoxItem?.id ?? '',
      boxType: currentBoxItem?.type,
      sensitiveConsumer: 0,
      cabinetLocationCode: cabinetLocationCode,
    );
  }

  stopReg() {
    stopRecognizer();
    setState(() {
      voiceStatus = false;
    });
    timer_?.cancel();
    Navigator.pop(context);
    startSpot(flag: 1);
    Fluttertoast.showToast(msg: '未匹配到正确手机号');
  }

  Future<void> initScan(int offsetBottom, {bool isContinue = false}) async {
    bool isPda = await CheckUtils.isPda();
    bool isOcr = await CheckUtils.checkOCRIsOpen();
    scanSubscription_ = scanPlugin.onScanChanged.listen((result) {
      print("OCR返回数据" + result.toString());
      // ocr 二次返回
      lastOcrPhone = result['phone'] ?? '';
      lastOcrName = result['name'] ?? '';
      lastOcrAddress = result['address'] ?? '';
      imageOcrPath = result['ocrImg'] ?? '';
      ocrTexts = result['ocrTexts'] ?? '';
      extraParam = result['extraParam'] ?? '';

      final List<dynamic> ocrTextsList =
          ocrTexts.isNotEmpty ? jsonDecode(ocrTexts) : [];
      // 获取所有符合条件的虚拟号码
      final virtualNumbers = ocrTextsList
          .where((item) =>
              item['virtual_ext_number'] != null &&
              item['virtual_ext_number'].toString().length >= 2)
          .map((item) => item['virtual_ext_number'].toString())
          .toList();

      // 统计每个号码出现的次数
      final frequencyMap = <String, int>{};
      for (final number in virtualNumbers) {
        frequencyMap[number] = (frequencyMap[number] ?? 0) + 1;
      }
      // 获取出现次数最多的号码
      String mostFrequentNumber = '';
      if (frequencyMap.isNotEmpty) {
        mostFrequentNumber = frequencyMap.entries
            .reduce((a, b) => a.value > b.value ? a : b)
            .key;
      } else if (virtualNumbers.isNotEmpty) {
        mostFrequentNumber = virtualNumbers.first;
      }
      // 更新虚拟手机号
      if (result['virtualPhone'] != null && result['virtualPhone'] != '') {
        showScanDb.virtualPhone ="${result['virtualPhone']}转$mostFrequentNumber";
        print(
            "虚拟手机号: ${result['virtualPhone']} - 最常出现: $mostFrequentNumber - 所有: $virtualNumbers");
      }
      // virtualPhone =  result['virtualPhone'] ?  "${result['virtualPhone']}-$mostFrequentNumber" : '';
      // print("虚拟手机号: ${result['virtualPhone']} - 最常出现: $mostFrequentNumber - 所有: $virtualNumbers");
      int nowtTime = new DateTime.now().millisecondsSinceEpoch;
      if (((nowtTime - lastScanTime) / 1000) < 3 &&
          lastScanWaybillNo != '' &&
          lastScanWaybillNo == result['text'] &&
          isPda) {
        // 3s内扫码同一单号什么都不做
      } else {
        lastScanTime = nowtTime;
        lastScanWaybillNo = result['text'] ?? '';
        if (result['text'] != '' && result['text'] != null) {
          addScan(result['text'], img: result['img']);
        }
      }
      // 上传图片
      if (result.containsKey('fileName')) {
        CheckUtils.savePhotoLocal(result['path'], result['fileName'],
            result['company'], result['status'], result['msg'], result['type']);
      }
    });
    // 初始化摄像头
    if (!isPda) {
      Timer(const Duration(milliseconds: 500), () {
        initScanView(offsetBottom, isContinue);
      });
    } else {
      if (isOcr) {
        scanPlugin?.enableOcr();
      } else {
        scanPlugin?.disableOcr();
      }
    }
  }

  /// 初始化mqtt
  void initMqtt() async {
    if (isBatch) {
      DataResult res_ = await CabinetDao.getMqttConfig(cabinetLocationCode);
      if (res_.result && res_.data != null) {
        MqttConfigEntity mqttConfig = $MqttConfigEntityFromJson(res_.data);
        // 连接mqtt
        mqttClient = await MqttUtils.init(mqttConfig);
        // 订阅批量开柜主题
        mqttClient?.subscribe(mqttConfig.topic, MqttQos.atMostOnce);
        // 监听数据
        mqttClient?.updates
            .listen((List<MqttReceivedMessage<MqttMessage>> c) async {
          if (isMqttProcess) {
            return;
          }
          isMqttProcess = true;
          try {
            final MqttPublishMessage message =
                c[0].payload as MqttPublishMessage;

            final String payload = utf8.decode(message.payload.message!);
            DoorStatusEntity doorStatusEntity =
                DoorStatusEntity.fromJson(jsonDecode(payload));
            // print('mqtt Received message:$payload from topic: ${c[0].topic}>');
            BoxItemEntity? boxItem = batchBoxList
                ?.where((e) =>
                    e?.isUsed == false &&
                    e?.serialNo.toString() == doorStatusEntity.serialNo &&
                    e?.pcbNo.toString() == doorStatusEntity.pcbNo)
                .firstOrNull;
            if (boxItem != null &&
                showScanDb.waybillNo != '' &&
                (showScanDb.receiverMobile != '' &&
                    showScanDb.receiverMobile.length >= 11)) {
              setState(() {
                CabinetBoxItem cabinetBoxItem = $CabinetBoxItemFromJson({
                  'id': boxItem.boxId,
                  'type': boxItem.boxType,
                  'boxLabel': boxItem.boxLabel,
                  'pcbNo': boxItem.pcbNo
                });
                cabinetBoxId = cabinetBoxItem.id;
                boxType = boxItem.boxType!;
                currentBoxItem = cabinetBoxItem;
                showScanDb.cabinetId = currentCabinetId;
                showScanDb.cabinetBoxId = boxItem.boxId;
                showScanDb.boxType = boxItem.boxType;
                cabinetBoxController.value =
                    TextEditingValue(text: boxItem.boxLabel!);
              });
              // 自动提交
              await confirmNextStep(isInbound: true);
            }
          } catch (e) {
            print('mqtt 异常: $e');
          }
          isMqttProcess = false;
        });
      }
    }
  }

  initScanView(int offsetBottom, bool isContinue) {
    this.scanView = ScanView(
      onScanViewCreated: (ScanViewController controller) {
        scanViewController = controller;
        scanViewController?.setScanTopOffset(
            60 +
                int.parse(
                    MediaQuery.of(context).padding.top.toStringAsFixed(0)),
            offsetBottom);
        scanViewController?.changeScanType(type: 'rk');
        scanViewController
            ?.setCornerColor(DefaultConfig().configs.PRIMARY_COLOR_TEXT);
        scanViewController?.showPhoto();
        if (isOCR) {
          scanViewController?.enableOcr();
        } else {
          scanViewController?.disableOcr();
        }
        if (isContinue) {
          if (showScanDb.waybillNo != '') {
            scanViewController?.stopSpot();
          }
        }
      },
      onScanViewError: () {
        CommonUtils.showMessage(context,
            msg: "调用摄像头失败", success: false, duration: 2);
      },
    );
    try {
      setState(() {});
    } catch (e) {
      print('$e');
    }
  }

  checkCabinetBoxLabel(String boxLabel) async {
    String cabinetBoxNo = boxLabel.replaceAll(RegExp(r'^B0*'), '');
    if (isInProcess) {
      return false;
    }
    int? index =
        batchBoxList?.indexWhere((item) => item?.boxLabel == cabinetBoxNo);
    if (index == null || index == -1) {
      return false;
    }
    if (cabinetBoxNo == currentBoxItem?.boxLabel) {
      Timer(const Duration(milliseconds: 200), () {
        startSpot(flag: 10);
      });
      return false;
    }
    isInProcess = true;
    DataResult res =
        await CabinetDao.getBoxItem(cabinetLocationCode, cabinetBoxNo);
    isInProcess = false;
    if (res.result) {
      currentCabinetId = res.data['cabinetId'];
      CabinetBoxItem boxItem = $CabinetBoxItemFromJson({
        'id': res.data['boxId'],
        'type': res.data['boxType'],
        'boxLabel': res.data['boxLabel'],
        'pcbNo': res.data['pcbNo']
      });
      cabinetBoxId = boxItem.id;
      cabinetName = res.data['cabinetName'] ?? '';
      boxType = res.data['boxType'];
      currentBoxItem = boxItem;
      showScanDb.cabinetId = currentCabinetId;
      showScanDb.cabinetBoxId = boxItem.id;
      showScanDb.boxType = boxItem.type;
      cabinetBoxController.value = TextEditingValue(text: boxItem.boxLabel!);
      SoundUtils.audioPushFn(SoundUtils.SUCCESS_OK);
      if (showScanDb.waybillNo == '') {
        lastScanWaybillNo = '';
        startSpot(flag: 9);
      }
      setState(() {});
    } else {
      Timer(const Duration(milliseconds: 200), () {
        startSpot(flag: 8);
      });
      lastScanWaybillNo = '';
      SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
    }
  }

  /// 扫码处理方法
  addScan(String waybillNo, {img = '', bool isScan = true}) async {
    String value = waybillNo.toUpperCase();
    if (value.startsWith(RegExp(r'^B'))) {
      if (isBatch && !isPause) {
        checkCabinetBoxLabel(value);
      } else {
        startSpot(flag: 7);
      }
      return;
    }

    /// 是否手动输入 手动输入清除手机号 只做单号校验
    if (waybillNo.startsWith('R02Z') || waybillNo.startsWith('R02T')) {
      waybillNo = waybillNo.substring(4);
    }
    if (img != '' && img != null) {
      imagePath = img;
    }

    /// 本地猜单
    brandCode = await ScanUtils.getCompany(context, waybillNo);
    if (brandCode == "") {
      brandCode = 'UNKNOW';
      SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
    }
    if (showScanDb.waybillNo != waybillNo) {
      if (isPda) {
        showScanDb.receiverMobile = '';
        mobileEditingController.value = TextEditingValue(text: '');
      }
      showScanDb.waybillNo = waybillNo;
      this.brandCode = brandCode;
      showScanDb.brandCode = this.brandCode;
      waybillNoEditingController.value = TextEditingValue(text: waybillNo);
      if (brandCode != '') {
        SoundUtils.audioPushFn(SoundUtils.BRAND_SOUND[showScanDb.brandCode]!);
      }

      ///差异性
      if (isCourier && !isBatch) {
        setBoxPrice(brandCode);
      }

      /// 远程单号检测
      waybillRemoteGuss(isScan: isScan);
    } else {
      if (isPda) {
        if (showScanDb.receiverMobile == '' ||
            showScanDb.receiverMobile == '*') {
          waybillRemoteGuss(isScan: isScan);
        }
      } else {
        checkCanScanBox();
      }
    }
    setState(() {});
  }

  Future<DataResult> fetchWithDelay<T>() async {
    if (Platform.isIOS) {
      // ios异步ocr，需要等待
      await Future.delayed(Duration(milliseconds: 200));
    }
    // 发起异步请求
    return await WaybillDao.waybillCheck(
      cabinetLocationCode,
      showScanDb.waybillNo,
      showScanDb.brandCode,
      ocrTexts: ocrTexts,
      extraParam: extraParam,
    );
  }

  /// 远程单号检测
  waybillRemoteGuss({bool isScan = true}) async {
    if (isInProcess) {
      return false;
    }
    isInProcess = true;
    isGussWaybill = false;
    // 改接口已经包含了校验特殊件
    // 如果不超过400ms，让其400ms返回数据
    // DataResult res = await WaybillDao.waybillCheck(cabinetLocationCode, showScanDb.waybillNo, showScanDb.brandCode);
    DataResult res = await fetchWithDelay();

    isInProcess = false;
    setState(() {
      platform = '';
    });
    if (res.result) {
      isGussWaybill = true;
      gussEntity = $MobileGussEntityFromJson(res.data);
      gussEntity.guessTime = new DateTime.now().millisecondsSinceEpoch;
      if (gussEntity.yzChannel != '') {
        SoundUtils.audioPushFn(SoundUtils.YZ_CHANNEL[gussEntity.yzChannel]!);
      }
      bool isContinue = await specialWayBillAlert(showScanDb.waybillNo);
      if (!isContinue) {
        resetScan();
      }

      var phone = lastOcrPhone;
      var name = lastOcrName;
      // 判断是不是手机号格式11位数字
      if (CheckUtils.isMobile(phone)) {
        var isSame = gussEntity.gussMobiles
            .firstWhereOrNull((element) => element.mobile == phone);
        if (isSame == null) {
          var isNew = 1;
          var mobileDesc;
          if (name == '') {
            var result = await CourierDao.courierMobileCheck(lastOcrPhone,
                cabinetLocationCode: cabinetLocationCode, noCheckOrder: true);
            if (result.result) {
              CustomerMobileEntity customerMobileEntity =
                  CustomerMobileEntity.fromJson(result.data);
              // 如果存在姓名,使用返回的姓名和是否新客户标识
              if (customerMobileEntity.name != '') {
                name = customerMobileEntity.name;
                isNew = customerMobileEntity.isNew;
              }
            }
          }
          if (isNew == 1) {
            mobileDesc = '新客户';
            SoundUtils.audioPushFn(SoundUtils.XKH);
          } else if (isNew == 0) {
            mobileDesc = '';
          }
          // 新用户提醒
          await CommonUtils.showMobileConfirmModal(
              context, imageOcrPath, phone, name, (item) {
            isPause = false;
            if (item != '') {
              SoundUtils.audioPushFn(SoundUtils.SUCCESS_OK);
              gussEntity.gussMobiles.add(CustomerEntity(
                  mobile: phone,
                  name: name ?? '',
                  hasSubstituteSms: true,
                  mobileDesc: mobileDesc,
                  isNew: isNew));
            } else {
              showScanDb.newCustomer = 0;
              SoundUtils.audioPushFn(SoundUtils.INPUT_MOBILE);
              FocusScope.of(context).requestFocus(mobileFocusNode_);
            }
            Navigator.of(context).pop();
          });
        } else {
          imageOcrPath = '';
        }
      } else {
        imageOcrPath = '';
      }
      if (gussEntity.gussMobiles.length == 0) {
        Fluttertoast.showToast(msg: '该单号不支持代发，请输入全手机号');
        SoundUtils.audioPushFn(SoundUtils.INPUT_ALL_MOBILE);
        return;
      }

      /// 只有一条数据 且非黑名单 且未开启但客户提醒 直接回填这条数据
      if (gussEntity.gussMobiles.length == 1 &&
          (gussEntity.gussMobiles[0].mobile.contains('*') ||
              gussEntity.gussMobiles[0].mobile.contains('-') ||
              (!hasOpenSingleCustomerListNotice &&
                  gussEntity.gussMobiles[0].isBlacklist == 0 &&
                  (gussEntity.gussMobiles[0].name != '' || isCourier) &&
                  !gussEntity.gussMobiles[0].mobile.contains('*') &&
                  !gussEntity.gussMobiles[0].mobile.contains('-')))) {
        showScanDb.receiverName = gussEntity.gussMobiles[0].name;
        nameEditingController.value =
            TextEditingValue(text: gussEntity.gussMobiles[0].name);
        showScanDb.receiverMobile = gussEntity.gussMobiles[0].mobile;
        mobileEditingController.value =
            TextEditingValue(text: gussEntity.gussMobiles[0].mobile);
        platform = gussEntity.gussMobiles[0].platform!;
        setState(() {});
        if (!gussEntity.gussMobiles[0].mobile.contains('*') &&
            !gussEntity.gussMobiles[0].mobile.contains('-')) {
          checkPhone(gussEntity.gussMobiles[0].mobile, flag: 3);
        }
        confirmNextStep(flag: 3);
      } else {
        if (gussEntity.gussMobiles.length == 2 &&
            !hasOpenSingleCustomerListNotice) {
          bool isNotice = false;
          CustomerEntity? fullCustomer;
          for (var item in gussEntity.gussMobiles) {
            if (CheckUtils.isMobile(item.mobile)) {
              fullCustomer = item;
            }
            if (item.mobile.contains("*") || item.mobile.contains("-")) {
              isNotice = true;
            }
            if (item.isBlacklist == 1) {
              isNotice = false;
            }
          }
          if (isNotice && fullCustomer != null) {
            showScanDb.receiverName = fullCustomer.name;
            nameEditingController.value =
                TextEditingValue(text: fullCustomer.name);
            showScanDb.receiverMobile = fullCustomer.mobile;
            mobileEditingController.value =
                TextEditingValue(text: fullCustomer.mobile);
            platform = fullCustomer.platform!;
            setState(() {});
            SoundUtils.audioPushFn(SoundUtils.CHOOSE_PHONE);
            checkPhone(fullCustomer.mobile, flag: 3);
            confirmNextStep(flag: 3);
            return;
          }
        }
        SoundUtils.audioPushFn(SoundUtils.CHOOSE_PHONE);
        isPause = true;
        List<CustomerEntity> list = gussEntity.gussMobiles;
        await CommonUtils.showBottomGussMobileModal(context, list,
            (CustomerEntity? item) {
          isPause = false;
          Navigator.pop(context);
          if (item != null) {
            if ((item.name != '' || isCourier) || item.mobile.contains('*')) {
              showScanDb.receiverName = item.name;
              nameEditingController.value = TextEditingValue(text: item.name);
              showScanDb.receiverMobile = item.mobile;
              mobileEditingController.value =
                  TextEditingValue(text: item.mobile);
              platform = item.platform!;
              setState(() {});
              SoundUtils.audioPushFn(SoundUtils.SUCCESS_OK);
              if (!item.mobile.contains('*')) {
                checkPhone(item.mobile, flag: 5);
              }
            } else {
              // 没有名称需要录入名称
              CommonUtils.inputWaybillNo(
                context,
                '',
                (name) async {
                  if (name != '') {
                    item.name = name;
                    Map<String, dynamic> params = item.toJson();
                    if (cabinetLocationCode != '') {
                      params.putIfAbsent(
                          'cabinetLocationCode', () => cabinetLocationCode);
                    }
                    DataResult res = await CourierDao.updateCustomer(params);
                    if (res.result) {
                      if (res.data) {
                        isPause = false;
                        showScanDb.receiverName = item.name;
                        nameEditingController.value =
                            TextEditingValue(text: item.name);
                        showScanDb.receiverMobile = item.mobile;
                        mobileEditingController.value =
                            TextEditingValue(text: item.mobile);
                        // Navigator.pop(context);
                        confirmNextStep(flag: 3);
                        checkPhone(item.mobile, flag: 5);
                      }
                    }
                  }
                },
                title: '客户名称',
                label: '姓名',
              );
            }
          } else {
            int index = list.indexWhere((element) => element.hasSubstituteSms);
            if (index > -1) {
              // 默认查询一个能代发的回填
              showScanDb.receiverMobile = list[index].name;
              mobileEditingController.value =
                  TextEditingValue(text: list[index].name);
              showScanDb.receiverMobile = list[index].mobile;
              mobileEditingController.value =
                  TextEditingValue(text: list[index].mobile);
              platform = list[index].platform!;
            } else {
              // 取消选择，则清空手机号
              showScanDb.receiverMobile = "";
              mobileEditingController.value =
                  TextEditingValue(text: showScanDb.receiverMobile);
            }
          }
        }, cabinetLocationCode: cabinetLocationCode);
      }
    } else {
      SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
    }
  }

  checkMobileInput(String phone) {
    String mobile = removeSpacesRule(phone);
    showMobileGussByLast4Button = false;
    if (mobile.length == 4) {
      showMobileGussByLast4Button = true;
    } else if (mobile.length > 11) {
      SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
      Fluttertoast.showToast(msg: '手机号格式错误');
    } else if (mobile.length == 11) {
      // ignore: unnecessary_null_comparison
      bool hasWaybillNo =
          showScanDb.waybillNo != '' && showScanDb.waybillNo != null;
      if (!hasWaybillNo) {
        return;
      }
      bool isPhone = RegExpUtil.checkPhone(mobile);
      if (isPhone) {
        if (RegExpUtil.isPhone(mobile)) {
          if (gussEntity.guessTime != 0) {
            gussEntity.virtualNumber = 2;
          }
        }
        mobileFocusNode_.unfocus();
        imageOcrPath = '';
        setPhone(mobile,
            isCheckMobile: showScanDb.receiverMobile != mobile, source: 0);
      } else {
        SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
        Fluttertoast.showToast(msg: '手机号格式错误');
      }
    }
    setState(() {
      showScanDb.receiverMobile = phone;
    });
  }

  clearPhoneController() {
    mobileEditingController.value = TextEditingValue(text: '');
    setState(() {
      showMobileGussByLast4Button = false;
      showScanDb.receiverMobile = '';
      showScanDb.newCustomer = 0;
    });
  }

  mobileGussByLast4() async {
    mobileFocusNode_.unfocus();
    String mobile = removeSpacesRule(mobileEditingController.text);
    if (mobile.length == 4) {
      LoadingUtil(
        status: '正在检测用户...',
      ).show(context);
      DataResult res =
          await WaybillDao.mobileGussbyLast4(cabinetLocationCode, mobile);
      LoadingUtil.dismiss(context);
      if (res.result) {
        List<CustomerEntity>? mobiles =
            jsonConvert.convertListNotNull<CustomerEntity>(res.data);
        if (mobiles!.length > 0) {
          showMobileGussByLast4Button = false;
          // if (mobiles.length == 1 && !hasOpenSingleCustomerListNotice) {
          //   showScanDb.receiverName = mobiles[0].name;
          //   nameEditingController.value = TextEditingValue(text: mobiles[0].name);
          //   checkPhone(mobiles[0].mobile, flag: 2);
          // } else {
          SoundUtils.audioPushFn(SoundUtils.CHOOSE_PHONE);
          isPause = true;
          await CommonUtils.showBottomGussMobileModal(context, mobiles,
              (CustomerEntity? item) {
            if (item != null) {
              if (item.name != '' && item.name != null) {
                isPause = false;
                Navigator.pop(context);
                showScanDb.receiverName = item.name;
                nameEditingController.value = TextEditingValue(text: item.name);
                checkPhone(item.mobile, flag: 2);
              } else {
                CommonUtils.inputWaybillNo(context, '', (name) async {
                  if (name != '') {
                    item.name = name;
                    Map<String, dynamic> params = item.toJson();
                    if (cabinetLocationCode != '') {
                      params.putIfAbsent(
                          'cabinetLocationCode', () => cabinetLocationCode);
                    }
                    DataResult res = await CourierDao.updateCustomer(params);
                    if (res.result) {
                      if (res.data) {
                        isPause = false;
                        Navigator.pop(context);
                        showScanDb.receiverName = item.name;
                        nameEditingController.value =
                            TextEditingValue(text: item.name);
                        checkPhone(item.mobile, flag: 4);
                      }
                    }
                  }
                }, title: '客户名称', label: '姓名');
              }
            } else {
              int index =
                  mobiles.indexWhere((element) => element.hasSubstituteSms);
              if (index > -1) {
                isPause = false;
                Navigator.pop(context);
                // 取消选择，则清空手机号
                showScanDb.receiverMobile = mobiles[index].name;
                mobileEditingController.value =
                    TextEditingValue(text: mobiles[index].name);
                showScanDb.receiverMobile = mobiles[index].mobile;
                mobileEditingController.value =
                    TextEditingValue(text: mobiles[index].mobile);
              } else {
                isPause = false;
                Navigator.pop(context);
                // 取消选择，则清空手机号
                showScanDb.receiverMobile = "";
                mobileEditingController.value =
                    TextEditingValue(text: showScanDb.receiverMobile);
              }
            }
          }, cabinetLocationCode: cabinetLocationCode);
          setState(() {});
        }
        // }
      }
    }
  }

  /// 设置手机号
  /// pda模式下返回ocrImg
  setPhone(String? phone,
      {ocrImg,
      img,
      bool isCheckMobile = true,
      bool isOcr = false,
      int? source}) async {
    if (kDebugMode) {
      print('source===$source');
    }
    if (img != '' && img != null) {
      imagePath = ocrImg;
    }
    if (ocrImg != '' && ocrImg != null) {
      imageOcrPath = ocrImg;
    }

    if (isGussWaybill) {
      if (phone != '' && phone != null) {
        setState(() {
          if (voiceStatus) {
            imageOcrPath = '';
            voiceStatus = false;
            closeVoiceCountDown();
            stopRecognizer();
          }
        });
        bool isCheckMobile_ = isCheckMobile;

        bool isVirtual =
            ocrTemp == null ? false : ocrTemp['isVirtual'] ?? false;

        ///虚拟号不校验手机号
        if (gussEntity.virtualNumber == 1 || isVirtual) {
          isCheckMobile_ = false;
          SoundUtils.audioPushFn(SoundUtils.VIRTUAL_NUMBER);
        } else if (isOcr && phone.contains('*') && !gussEntity.hasSecret) {
          /// ocr 扫描带星手机号 未返回隐私件 播报隐私件语音
          SoundUtils.audioPushFn(SoundUtils.WAYBILL_TYPE[5]!);
        }

        if (isOcr) {
          if (gussEntity.virtualNumber == 2) {
            if (phone != showScanDb.receiverMobile) {
              showScanDb.receiverMobile = phone;
              mobileEditingController.value =
                  TextEditingValue(text: showScanDb.receiverMobile);
              SoundUtils.audioPushFn(SoundUtils.SUCCESS_OK);
            }
          }
        } else {
          if (phone != showScanDb.receiverMobile) {
            showScanDb.receiverMobile = phone;
            mobileEditingController.value =
                TextEditingValue(text: showScanDb.receiverMobile);
            SoundUtils.audioPushFn(SoundUtils.SUCCESS_OK);
          }
        }
        checkCustomer(phone,
            isCheckMobile: isCheckMobile_, isOcr: isOcr, flag: 3);
      }
    }
  }

  /// 通过手机号检测用户
  checkCustomer(String phone,
      {bool isCheckMobile = true, bool isOcr = false, int? flag}) async {
    if (kDebugMode) {
      print('flag ===== $flag');
    }
    if (isPause) {
      return false;
    }
    bool _checkMobile = isCheckMobile;
    if (!isCabinet && isDelivery) {
      // 送货上门 不校验手机号
      _checkMobile = false;
    }
    if (_checkMobile) {
      bool matched = RegExpUtil.checkPhoneContainsAsterisk(phone);
      if (matched) {
        if (phone.contains('*')) {
          DataResult res =
              await WaybillDao.mobileGuss2(cabinetLocationCode, phone);
          if (res.result) {
            List<CustomerEntity>? mobiles =
                jsonConvert.convertListNotNull<CustomerEntity>(res.data);
            if (mobiles!.length > 0) {
              /// 只有一条数据 且非黑名单 且未开启但客户提醒 直接回填这条数据
              if (mobiles.length == 1 &&
                  mobiles[0].isBlacklist == 0 &&
                  !hasOpenSingleCustomerListNotice) {
                showScanDb.receiverMobile = mobiles[0].mobile;
                showScanDb.receiverName = mobiles[0].name;
                mobileEditingController.value =
                    TextEditingValue(text: showScanDb.receiverMobile);
                nameEditingController.value =
                    TextEditingValue(text: showScanDb.receiverName ?? '');
                checkPhone(mobiles[0].mobile, flag: 3);
              } else {
                SoundUtils.audioPushFn(SoundUtils.CHOOSE_PHONE);
                isPause = true;
                await CommonUtils.showBottomGussMobileModal(context, mobiles,
                    (CustomerEntity? item) {
                  if (item != null) {
                    if (item.name != '' && item.name != null) {
                      isPause = false;
                      Navigator.pop(context);
                      showScanDb.receiverName = item.name;
                      nameEditingController.value =
                          TextEditingValue(text: item.name);
                      checkPhone(item.mobile, flag: 2);
                    } else {
                      CommonUtils.inputWaybillNo(context, '', (name) async {
                        if (name != '') {
                          item.name = name;
                          Map<String, dynamic> params = item.toJson();
                          if (cabinetLocationCode != '') {
                            params.putIfAbsent('cabinetLocationCode',
                                () => cabinetLocationCode);
                          }
                          DataResult res =
                              await CourierDao.updateCustomer(params);
                          if (res.result) {
                            if (res.data) {
                              isPause = false;
                              Navigator.pop(context);
                              showScanDb.receiverName = item.name;
                              nameEditingController.value =
                                  TextEditingValue(text: item.name);
                              checkPhone(item.mobile, flag: 4);
                            }
                          }
                        }
                      }, title: '客户名称', label: '姓名');
                    }
                  } else {
                    int index = mobiles
                        .indexWhere((element) => element.hasSubstituteSms);
                    if (index > -1) {
                      isPause = false;
                      Navigator.pop(context);
                      // 取消选择，则清空手机号
                      showScanDb.receiverMobile = mobiles[index].name;
                      mobileEditingController.value =
                          TextEditingValue(text: mobiles[index].name);
                      showScanDb.receiverMobile = mobiles[index].mobile;
                      mobileEditingController.value =
                          TextEditingValue(text: mobiles[index].mobile);
                    } else {
                      isPause = false;
                      Navigator.pop(context);
                      // 取消选择，则清空手机号
                      showScanDb.receiverMobile = "";
                      mobileEditingController.value =
                          TextEditingValue(text: showScanDb.receiverMobile);
                    }
                  }
                }, cabinetLocationCode: cabinetLocationCode);
              }
            } else {
              checkCanScanBox();
            }
          }
        } else {
          checkPhone(phone, flag: 1);
        }
      }
    } else {
      confirmNextStep(flag: 3);
    }
  }

  checkCanScanBox() {
    if (isBatch) {
      if (showScanDb.waybillNo != '' &&
          (showScanDb.receiverMobile != '' &&
              showScanDb.receiverMobile.length >= 11)) {
        if (showScanDb.cabinetBoxId == '' || showScanDb.cabinetBoxId == null) {
          startSpot(flag: 5);
        }
      }
    }
  }

  checkPhone(String phone, {int? flag}) async {
    if (kDebugMode) {
      print('check phone flag =$flag');
    }
    isPause = true;
    LoadingUtil(
      status: '正在检测用户...',
    ).show(context);
    var result = await CourierDao.courierMobileCheck(phone,
        cabinetLocationCode: cabinetLocationCode);
    isPause = false;
    LoadingUtil.dismiss(context);
    if (result.result) {
      CustomerMobileEntity customerMobileEntity =
          CustomerMobileEntity.fromJson(result.data);
      mobileNotice(customerMobileEntity);
    } else {
      showScanDb.receiverMobile = '';
      showScanDb.newCustomer = 0;
      showScanDb.receiverName = '';
      mobileEditingController.value = TextEditingValue(text: '');
      nameEditingController.value = TextEditingValue(text: '');
      SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
      setState(() {});
    }
  }

  mobileNotice(CustomerMobileEntity customerEntity) async {
    /// 隐藏手机号输入框 下一个方法默认会隐藏 手动输入时不需要隐藏
    bool hideMobileKeyBorard = true;
    showScanDb.newCustomer = customerEntity.isNew;
    setState(() {
      if (customerEntity.name != '') {
        showScanDb.receiverName = customerEntity.name;
        nameEditingController.value =
            TextEditingValue(text: customerEntity.name);
      }
      showScanDb.receiverMobile = customerEntity.mobile;
      mobileEditingController.value =
          TextEditingValue(text: showScanDb.receiverMobile);
    });
    // 进行追加入柜操作
    if (hasShopOpenAddCabinet &&
        isAddCabinet &&
        customerEntity.orderList.length > 0) {
      SoundUtils.audioPushFn(SoundUtils.ADD_CABINET);
      isPause = true;
      CustomerMobileOrderList? item =
          await CommonUtils.showBottomBoxSelectModal(
              context, customerEntity.orderList);
      isPause = false;
      if (item != null) {
        setState(() {
          boxType = item.cabinetBoxType;
          showScanDb.boxType = boxType;
          showScanDb.cabinetId = item.cabinetId;
          showScanDb.cabinetBoxId = item.cabinetBoxId;
          cabinetBoxController.value =
              TextEditingValue(text: item.cabinetBoxLabel);
          showScanDb.addInCabinet = true;
        });
      } else {
        showScanDb.boxType = currentBoxItem?.type;
        showScanDb.cabinetBoxId = currentBoxItem?.id ?? '';
        showScanDb.cabinetId = currentCabinetId;
        showScanDb.addInCabinet = false;
        checkCanScanBox();
      }
    } else if (customerEntity.labelNotice == 1) {
      bool res = await CommonUtils.confirm(context,
          '${customerEntity.mobile}为您标记的【${customerEntity.label}】客户，请问是否继续入柜？',
          title: '特殊客户提示', cancelText: '取消入柜', confirmText: '继续入柜');
      if (!res) {
        resetScan();
      }
    }
    confirmNextStep(flag: 1, hideMobileKeyBorard: hideMobileKeyBorard);
  }

  /// 确认入库 手动点击触发
  /// 非PDA模式下 批量入柜且开启急速入库 自动触发
  confirmNextStep(
      {bool isInbound = false,
      bool isOcr = true,
      bool hideMobileKeyBorard = true,
      int flag = 0}) async {
    if (hasOpenFastInto && !isBlind) {
      isInbound = true;
    }
    if (kDebugMode) {
      print('nextStep ===== $flag  $isInbound');
    }
    if (hideMobileKeyBorard) {
      mobileFocusNode_.unfocus();
    }
    
    nameFocusNode_.unfocus();
    checkCanScanBox();
    if (isInbound) {
      checkWaybillNotice();
    }
  }

  getButtonStatus() {}

  openBox() {}

  inbound() {}

  resetRes() {
    waybillRemoteCheckRes = null;
  }

  buildBDP({Color? colors = Colors.white, bool isLabel = false}) {
    return [
      Padding(padding: EdgeInsets.only(left: 22)),
      InkWell(
        onTap: () async {
          bool isCheck = !isBDP;
          if (isCheck) {
            var data = await UserDao.hasGpconfig();
            if (data.data != '' && data.result) {
              isBDP = isCheck;
            } else {
              print('未绑定共配账号');
              bool res =
                  await CommonUtils.confirm(context, '您未绑定共配账号，是否绑定共配账号？');
              if (res) {
                await NavigatorUtils.goCoMatchingBind(context);
              }
            }
          } else {
            bool res = await CommonUtils.confirm(context, '是否关闭补到派！');
            if (res) {
              isBDP = isCheck;
            }
          }
          setState(() {});
          await LocalStorage.save(DefaultConfig().configs.IS_DP, isBDP,
              isPrivate: true);
        },
        child: !isLabel
            ? (isBDP
                ? LocalImageUtil.getImageAsset('pdaBu',
                    isChannel: true, width: 28)
                : LocalImageUtil.getImageAsset('pdaBuDisable', width: 28))
            : (isBDP
                ? LocalImageUtil.getImageAsset('bu', width: 28)
                : LocalImageUtil.getImageAsset('buDisable', width: 28)),
      ),
      Offstage(
        offstage: !isLabel,
        child: Column(
          children: [
            Text(
              '补到派',
              style: TextStyle(color: colors, fontSize: 10),
            ),
            Padding(padding: EdgeInsets.only(bottom: 10)),
          ],
        ),
      )
    ];
  }

  buildDeliveryWaybill({Color? colors = Colors.white}) {
    return [];
    // ignore: dead_code
    return [
      Padding(padding: EdgeInsets.only(left: 22)),
      InkWell(
        onTap: () async {
          bool isCheck = !isDelivery;
          if (isCheck) {
            bool res = await CommonUtils.confirm(
                context, '开启“仅入库”后将不回传物流信息至快递公司，请先【签收】包裹后再入库');
            if (res) {
              isDelivery = isCheck;
            }
          } else {
            bool res = await CommonUtils.confirm(context, '是否关闭仅入库！');
            if (res) {
              isDelivery = isCheck;
            }
          }
          setState(() {});
        },
        child: isDelivery
            ? LocalImageUtil.getImageAsset('jrk', isChannel: true, width: 28)
            : LocalImageUtil.getImageAsset('jrkDisable', width: 28),
      )
    ];
  }

  buildSynchronizedLogistics(
      {Color? colors = Colors.white, bool isLabel = false}) {
    return [
      InkWell(
        onTap: () async {
          bool isCheck = !isSynchronizedLogistics;
          if (isCheck) {
            isSynchronizedLogistics = isCheck;
          } else {
            bool res = await CommonUtils.confirm(context, '是否关闭物流同步！');
            if (res) {
              isSynchronizedLogistics = isCheck;
            }
          }
          setState(() {});
          await LocalStorage.save(
              DefaultConfig().configs.SYNCHRONIZED_LOGISTICS,
              isSynchronizedLogistics,
              isPrivate: true);
        },
        child: !isLabel
            ? (isSynchronizedLogistics
                ? LocalImageUtil.getImageAsset('pdaLogistics',
                    isChannel: true, width: 28)
                : LocalImageUtil.getImageAsset('pdaLogisticsDisable',
                    width: 28))
            : (isSynchronizedLogistics
                ? LocalImageUtil.getImageAsset('logistics', width: 28)
                : LocalImageUtil.getImageAsset('logisticsDisable', width: 28)),
      ),
      Offstage(
        offstage: !isLabel,
        child: Column(
          children: [
            Text(
              '物流同步',
              style: TextStyle(color: colors, fontSize: 10),
            ),
            Padding(padding: EdgeInsets.only(bottom: 10)),
          ],
        ),
      )
    ];
  }

  buildAddCabinet({Color? colors = Colors.white, bool isLabel = false}) {
    return hasShopOpenAddCabinet
        ? [
            Padding(padding: EdgeInsets.only(left: 22)),
            InkWell(
              onTap: () async {
                bool isCheck = !isAddCabinet;
                if (isCheck) {
                  isAddCabinet = isCheck;
                } else {
                  bool res = await CommonUtils.confirm(context, '是否关闭追加入柜！');
                  if (res) {
                    isAddCabinet = isCheck;
                  }
                }
                setState(() {});
                await LocalStorage.save(
                    DefaultConfig().configs.ADD_CABINET, isAddCabinet,
                    isPrivate: true);
              },
              child: !isLabel
                  ? (isAddCabinet
                      ? LocalImageUtil.getImageAsset('pdaAdd',
                          isChannel: true, width: 28)
                      : LocalImageUtil.getImageAsset('pdaAddDisable',
                          width: 28))
                  : (isAddCabinet
                      ? LocalImageUtil.getImageAsset('add', width: 28)
                      : LocalImageUtil.getImageAsset('addDisable', width: 28)),
            ),
            Offstage(
              offstage: !isLabel,
              child: Column(
                children: [
                  Text(
                    '追加入柜',
                    style: TextStyle(color: colors, fontSize: 10),
                  ),
                  Padding(padding: EdgeInsets.only(bottom: 10)),
                ],
              ),
            )
          ]
        : [];
  }

  buildOpenOCR({Color? colors = Colors.white, bool isLabel = false}) {
    return [
      Padding(padding: EdgeInsets.only(left: 22)),
      InkWell(
        onTap: () async {
          bool isCheck = !isOCR;
          if (isCheck) {
            isOCR = isCheck;
            if (isPda) {
              scanPlugin?.enableOcr();
            } else {
              scanViewController?.enableOcr();
            }
          } else {
            bool res = await CommonUtils.confirm(context, '是否关闭OCR识别！');
            if (res) {
              isOCR = isCheck;
              if (isPda) {
                scanPlugin?.disableOcr();
              } else {
                scanViewController?.disableOcr();
              }
            }
          }
          await LocalStorage.save(DefaultConfig().configs.IS_OCR, isOCR,
              isPrivate: true);
          setState(() {});
        },
        child: !isLabel
            ? (isOCR
                ? LocalImageUtil.getImageAsset('pdaOcr',
                    isChannel: true, width: 28)
                : LocalImageUtil.getImageAsset('pdaOcrDisable', width: 28))
            : (isOCR
                ? LocalImageUtil.getImageAsset('ocr', width: 28)
                : LocalImageUtil.getImageAsset('ocrDisable', width: 28)),
      ),
      Offstage(
        offstage: !isLabel,
        child: Column(
          children: [
            Text(
              'OCR识别',
              style: TextStyle(color: colors, fontSize: 10),
            ),
            Padding(padding: EdgeInsets.only(bottom: 10)),
          ],
        ),
      )
    ];
  }

  buildSingleCustomerNotice(
      {Color? colors = Colors.white, bool isLabel = false}) {
    return [
      Padding(padding: EdgeInsets.only(left: 22)),
      InkWell(
        onTap: () async {
          bool isCheck = !hasOpenSingleCustomerListNotice;
          if (isCheck) {
            hasOpenSingleCustomerListNotice = isCheck;
          } else {
            bool res = await CommonUtils.confirm(context, '是否关闭单客户提醒！');
            if (res) {
              hasOpenSingleCustomerListNotice = isCheck;
            }
          }
          setState(() {});
          await LocalStorage.save(
              DefaultConfig().configs.HAS_OPEN_SINGEL_CUSTOMER_NOTICE,
              hasOpenSingleCustomerListNotice,
              isPrivate: true);
        },
        child: !isLabel
            ? (hasOpenSingleCustomerListNotice
                ? LocalImageUtil.getImageAsset('pdaKhtx',
                    isChannel: true, width: 28)
                : LocalImageUtil.getImageAsset('pdaKhtxDisable', width: 28))
            : (hasOpenSingleCustomerListNotice
                ? LocalImageUtil.getImageAsset('khtx', width: 28)
                : LocalImageUtil.getImageAsset('khtxDisable', width: 28)),
      ),
      Offstage(
        offstage: !isLabel,
        child: Column(
          children: [
            Text(
              '单客户提醒',
              style: TextStyle(color: colors, fontSize: 10),
            ),
            Padding(padding: EdgeInsets.only(bottom: 10)),
          ],
        ),
      )
    ];
  }

  ///极速入柜
  buildBatchFastInto({Color? colors = Colors.white, bool isLabel = false}) {
    return [
      Padding(padding: EdgeInsets.only(left: 22)),
      InkWell(
        onTap: () async {
          bool isCheck = !hasOpenFastInto;
          if (isCheck) {
            hasOpenFastInto = isCheck;
          } else {
            bool res = await CommonUtils.confirm(context, '是否关闭极速入柜！');
            if (res) {
              hasOpenFastInto = isCheck;
            }
          }
          setState(() {});
          await LocalStorage.save(
              DefaultConfig().configs.HAS_OPEN_FAST_INTO, hasOpenFastInto,
              isPrivate: true);
        },
        child: !isLabel
            ? (hasOpenFastInto
                ? LocalImageUtil.getImageAsset('pdaJsrg',
                    isChannel: true, width: 28)
                : LocalImageUtil.getImageAsset('pdaJsrgDisable', width: 28))
            : (hasOpenFastInto
                ? LocalImageUtil.getImageAsset('jsrg', width: 28)
                : LocalImageUtil.getImageAsset('jsrgDisable', width: 28)),
      ),
      Offstage(
        offstage: !isLabel,
        child: Column(
          children: [
            
            Text(
              '极速入柜',
              style: TextStyle(color: colors, fontSize: 10),
            ),
            Padding(padding: EdgeInsets.only(bottom: 10)),
          ],
        ),
        
      )
    ];
  }

  checkWaybillNotice() async {
    if (getButtonStatus()) {
      lastBoxItem = currentBoxItem;
      isCabinet ? openBox() : inbound();
    }
  }

  specialWayBillAlert(String waybillNo) async {
    String? desc = gussEntity.tagRemark;
    if (desc == null || desc == '拦截件检测接口调用异常') {
      desc = '';
    }
    bool isContinue = true;
    if (gussEntity.tagType != 1) {
      SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
      bool res = await CommonUtils.confirm(context, '[$waybillNo]$desc，是否继续入柜?',
          title: '特殊件提示', cancelText: '取消入柜', confirmText: '继续入柜');
      if (!res) {
        isContinue = false;
        resetScan();
      }
    }
    return isContinue;
  }

  /// 重置扫描
  /// isResetBoxCount 是否获取格口可用数量
  /// isSkipNext 是否自动跳转到下一格口
  resetScan({bool isResetBoxCount = false, bool isSkipNext = false}) {
    countDownKey3.currentState?.resetCountDown();
    isGussWaybill = false;
    imageOcrPath = '';
    imagePath = '';
    lastScanTime = 0;
    ocrTemp = null;
    lastScanWaybillNo = '';
    if (isBatch && isSkipNext && !(showScanDb.addInCabinet ?? true)) {
      skipBoxItem();
    }
    if (1 == 1) {
      showScanDb.cabinetBoxPcbNo = '';
      showScanDb.cabinetBoxId = '';
    }
    showScanDb = buildNewScan();
    imageCache.clear();
    gussEntity = new MobileGussEntity();
    mobileFocusNode_.unfocus();
    nameFocusNode_.unfocus();
    focusNode_.unfocus();

    timer?.cancel();
    resetRes();
    isPause = false;
    waybillNoEditingController.value = TextEditingValue(text: '');
    nameEditingController.value = TextEditingValue(text: '');
    mobileEditingController.value = TextEditingValue(text: '');
    cabinetBoxController.value = TextEditingValue(text: '');

    startSpot(flag: 4);
    brandCode = '';
    if (isCourier) {
      resetTimer(isResetBoxCount: isResetBoxCount);
    }
  }

  resetTimer({bool isResetBoxCount = false}) {}

  closeVoiceCountDown() {
    if (showScanDb.waybillNo == '') {
      startSpot(flag: 3);
    }
    timer_?.cancel();
    stopRecognizer();
    Navigator.pop(context);
  }

  /// 品牌选择
  selectBrand({String? brand, bool isForce = false}) {
    if (!hasChangeBrand) {
      Fluttertoast.showToast(msg: '该点位不允许修改快递品牌');
      return;
    }
    SoundUtils.audioPushFn(SoundUtils.CHOOSE_COMPANY);
    CommonUtils.showBottomBrandSelectModal(context, brandMap, (item) {
      if (item != '') {
        SoundUtils.audioPushFn(SoundUtils.BRAND_SOUND[item]!);
        brandCode = item;
        showScanDb.brandCode = item;
      } else {
        if (brand != null && brand != '') {
          brandCode = brand;
          showScanDb.brandCode = brand;
        }
      }
      if (isCourier && !isBatch) {
        this.setBoxPrice(brandCode);
        setState(() {});
      }
    }, bindCompany: brand, isForce: isForce);
  }

  ///根据快递公司设置格口价格
  setBoxPrice(String brandCode) {
    if (boxMapPriceData.keys.contains(brandCode)) {
      boxData = boxMapPriceData[brandCode]!;
    } else {
      boxData = boxMapPriceData['DEFAULT']!;
    }
  }

  cancelCountDown({int time = 60}) {
    countTime = time;
    countDownTimer?.cancel();
    countDownTimer = Timer.periodic(Duration(seconds: 1), (time) {
      setState(() {
        if (countTime > 0) {
          countTime--;
        } else {
          countDownTimer?.cancel();
          countDownTimer = null;
        }
      });
    });
  }

  /// 取消入柜
  cancelCabinetInbound(String cabinetLocationCode, String id,
      {bool showInput = true, String hostIndex = ''}) async {
    scanPlugin.playSound("请选择取消原因");
    String cancelReason = '投错包裹';
    String? reason = await CommonUtils.radioSelect(context,
        title: '取消入库',
        content: '请选择取消原因',
        selectValue: cancelReason,
        showInput: showInput);
    if (reason != null) {
      var _res = await CourierDao.courierOrderCancel(cabinetLocationCode, id,
          cancelReason: reason, hostIndex: hostIndex);
      if (_res != null && _res.result) {
        setState(() {
          isCountDown = false;
        });
        // 更新柜机数据取消入柜是更新柜机状态
        await updateCabinetStatus();

        if (isBatch) {
          rollbackBoxItem();
        }
        getCabinetUsableBox(cabinetLocationCode);
        resetScan();
        Fluttertoast.showToast(msg: '取消入库成功');
        scanPlugin.playSound("取消成功");
      }
    } else {
      cancelCountDown(time: countTime);
    }
  }

  getCabinetUsableBox(String cabinetLocationCode) {}

  ///批量开柜选择柜机相关逻辑

  /// 获取柜机可用格口
  getUseableCabinetBoxList(
      {bool isShowDialog = true, String hostIndex = ''}) async {
    boxList = [];
    DataResult res =
        await CabinetDao.getCabinetUseableList(cabinetLocationCode);
    if (res.result) {
      List<dynamic> list = res.data;
      boxList =
          jsonConvert.convertListNotNull<CabinetBoxUseableEntity>(list) ?? [];
      setState(() {});
      if (isShowDialog) {
        print('hostIndex: $hostIndex');
        // await checkCabinet(hostIndex: hostIndex);
        await selectCabinetInfo(hostIndex: hostIndex);
      }
    }
  }

  /// 批量开柜选择弹框
  selectCabinetInfo({String hostIndex = ''}) async {
    print('hostIndex-----: $hostIndex');
    bool showClose = selectCabinet != null;
    SoundUtils.audioPushFn(SoundUtils.CHOOSE_CABINET);
    await updateCabinetStatus();
    /// 柜机列表只有一个 默认选中
    if (this.boxList.length == 1) {
      selectCabinet = this.boxList[0];
       // 检查选中柜机是否还有可用格口
    if (getNumRender(selectCabinet!) == '0') {
      Fluttertoast.showToast(msg: '当前柜机格口已用完');
      return;
    }
      setState(() {});
    }
    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (_context, state) {
            return MyDialog(
              width: MediaQuery.of(context).size.width - 80,
              maxHeight: MediaQuery.of(context).size.height < 550
                  ? MediaQuery.of(context).size.height
                  : MediaQuery.of(context).size.height *
                      (boxList.length > 10 ? 0.75 : 0.55),
              autoClose: false,
              showClose: showClose,
              title: '请选择柜机',
              content: '这是内容部分',
              buttons: ['取消', '确认'],
              contentWidget: cabinetListWidget(state),
              otherButtons: buildBoxType(state),
              onTap: (index, context_) {
                if (!showClose) {
                  if (index == 1) {
                    print('hostIndex确认: $hostIndex');
                    confirmCabinet(hostIndex: hostIndex);
                  } else {
                    Navigator.of(context_)
                      ..pop()
                      ..pop();
                  }
                } else {
                  if (index == 1) {
                    confirmCabinet(hostIndex: hostIndex);
                  } else {
                    Navigator.pop(context_);
                  }
                }
              },
            );
          });
        });
  }

  checkCabinet({String hostIndex = ''}) async {
    bool showClose = selectCabinet != null;
    SoundUtils.audioPushFn(SoundUtils.CHOOSE_CABINET);

    /// 柜机列表只有一个 默认选中
    if (this.boxList.length == 1) {
      selectCabinet = this.boxList[0];
      setState(() {});
    }
    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (_context, state) {
            return MyDialog(
              width: MediaQuery.of(context).size.width - 80,
              maxHeight: MediaQuery.of(context).size.height < 550
                  ? MediaQuery.of(context).size.height
                  : MediaQuery.of(context).size.height *
                      (boxList.length > 10 ? 0.75 : 0.55),
              autoClose: false,
              showClose: showClose,
              title: '请选择柜机搭建',
              content: '这是内容部分',
              buttons: ['取消', '确认'],
              contentWidget: cabinetListWidget(state),
              otherButtons: buildBoxType(state),
              onTap: (index, context_) async {
                if (index == 1) {
                  await confirmBox(hostIndex: hostIndex);
                } else {
                  Navigator.of(context).pop();
                }
              },
            );
          });
        });
  }

  confirmBox({String hostIndex = ''}) async {
    if (selectCabinet == null) {
      Fluttertoast.showToast(msg: '请选择柜机');
      return false;
    }
    bool result = await CommonUtils.confirm(context, '是否确认切换柜机？');
    if (result) {
      LoadingUtil(
        status: '获取下一个格口...',
      ).show(context);
      DataResult res = await CabinetDao.cabinetOpenBox(
          selectCabinet!.cabinetId!, selectBoxType);
      LoadingUtil.dismiss(context);
      if (res.result) {
        setState(() {
          lastCabinetBoxId = res.data['cabinetBoxId'];
          currentCabinetId = res.data['cabinetId'];
          cabinetName = res.data['cabinetName'];
          cabinetBoxId = res.data['cabinetBoxId'];
          currentBoxItem = $CabinetBoxItemFromJson({
            'id': res.data['cabinetBoxId'],
            'type': res.data['boxType'],
            'boxLabel': res.data['boxLabel']
          });
          availableCount = res.data['availableCount'];
          boxType = res.data['boxType'];
        });
        showScanDb.cabinetId = currentCabinetId ?? '';
        showScanDb.cabinetBoxId = currentBoxItem?.id ?? '';
        showScanDb.boxType = currentBoxItem?.type;
        Navigator.of(context).pop();
      } else {
        resetScan();
        stopSpot();
        SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
      }
    }
  }

  cabinetListWidget(state) {
    List<Widget> widgets = <Widget>[];
    widgets = List.generate(boxList.length, (index) {
      return _renderItem(boxList[index], index, state);
    });
    return Container(
      color: Colors.grey.shade200,
      padding: EdgeInsets.only(top: 10, bottom: 10),
      child: SingleChildScrollView(
        child: Container(
          alignment: Alignment.center,
          child: Wrap(children: widgets),
        ),
      ),
    );
  }

  // 添加更新柜机数据的方法
Future<void> updateCabinetStatus() async {
  try {
    DataResult res = await CabinetDao.getCabinetUseableList(cabinetLocationCode);
    if (res.result) {
      List<dynamic> list = res.data;
      boxList = jsonConvert.convertListNotNull<CabinetBoxUseableEntity>(list) ?? [];
      
      // 更新当前选中柜机的数据
      if (selectCabinet != null) {
        int index = boxList.indexWhere((item) => item.cabinetId == selectCabinet?.cabinetId);
        if (index != -1) {
          selectCabinet = boxList[index];
        }
      }
      
      setState(() {});
    }
  } catch (e) {
    print("更新柜机数据错误: $e");
    Fluttertoast.showToast(msg: '更新柜机数据失败，请稍后重试');
  }
}

  buildBoxType(state) {
    List<String> buttonList = ['全部', '极大', '超大'];
    List<String> buttonList2 = ['大', '中', '小'];
    List<String> buttonList3 = ['超小', '极小'];
    return Column(
      children: [
        Container(
          margin: EdgeInsets.fromLTRB(10, 0, 10, 8),
          child: Text('请选择格口类型'),
        ),
        Container(
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: colorH,
                width: 1,
              ),
            ),
          ),
        ),
        Row(
          children: buttonList.map((res) {
            int index = buttonList.indexOf(res);
            return Expanded(
                flex: 1,
                child: Container(
                  height: 40,
                  decoration: (index == 2)
                      ? BoxDecoration()
                      : BoxDecoration(
                          border: Border(
                            right: BorderSide(
                              color: colorH,
                              width: 1.0,
                            ),
                          ),
                        ),
                  child: defaultCustomButton(context,
                      needCloseDialog: false,
                      text: res,
                      textColor: (selectBoxType == index - 1)
                          ? Theme.of(context).primaryColor
                          : Color(0xFF999999),
                      backgroundColor: (selectBoxType == index - 1)
                          ? Theme.of(context).primaryColorLight
                          : Color(0xFFFFFFFF),
                      buttonHeight: 40.0, onTap: () {
                    state(() {
                      selectBoxType = index - 1;
                    });
                  }),
                ));
          }).toList(),
        ),
        Container(
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: colorH,
                width: 1,
              ),
            ),
          ),
        ),
        Row(
          children: buttonList2.map((res) {
            int index = buttonList2.indexOf(res);
            return Expanded(
                flex: 1,
                child: Container(
                  height: 40,
                  decoration: (index == 2)
                      ? BoxDecoration()
                      : BoxDecoration(
                          border: Border(
                            right: BorderSide(
                              color: colorH,
                              width: 1.0,
                            ),
                          ),
                        ),
                  child: defaultCustomButton(context,
                      needCloseDialog: false,
                      text: res,
                      textColor: (selectBoxType == index + 2)
                          ? Theme.of(context).primaryColor
                          : Color(0xFF999999),
                      backgroundColor: (selectBoxType == index + 2)
                          ? Theme.of(context).primaryColorLight
                          : Color(0xFFFFFFFF),
                      buttonHeight: 40.0, onTap: () {
                    state(() {
                      selectBoxType = index + 2;
                    });
                  }),
                ));
          }).toList(),
        ),
        Container(
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: colorH,
                width: 1,
              ),
            ),
          ),
        ),
        Row(
          children: buttonList3.map((res) {
            int index = buttonList3.indexOf(res);
            return Expanded(
                flex: 1,
                child: Container(
                  height: 40,
                  decoration: (index == 2)
                      ? BoxDecoration()
                      : BoxDecoration(
                          border: Border(
                            right: BorderSide(
                              color: colorH,
                              width: 1.0,
                            ),
                          ),
                        ),
                  child: defaultCustomButton(context,
                      needCloseDialog: false,
                      text: res,
                      textColor: (selectBoxType == index + 5)
                          ? Theme.of(context).primaryColor
                          : Color(0xFF999999),
                      backgroundColor: (selectBoxType == index + 5)
                          ? Theme.of(context).primaryColorLight
                          : Color(0xFFFFFFFF),
                      buttonHeight: 40.0, onTap: () {
                    state(() {
                      selectBoxType = index + 5;
                    });
                  }),
                ));
          }).toList(),
        )
      ],
    );
  }

  _renderItem(CabinetBoxUseableEntity item, int index, state) {
    return InkWell(
      onTap: () {
        // if (item.cabinetId != selectCabinet?.cabinetId) {
        //   selectBoxType = 0;
        // }
        selectCabinet = item;
        state(() {});
      },
      child: Container(
        width: (MediaQuery.of(context).size.width - 80) / 2 - 30,
        margin: EdgeInsets.only(right: index % 2 == 0 ? 10 : 0, bottom: 20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(5)),
          border: Border.all(
              width: 1,
              color: selectCabinet?.cabinetId == item.cabinetId
                  ? Theme.of(context).primaryColorLight
                  : Colors.grey),
          color: selectCabinet?.cabinetId == item.cabinetId
              ? Theme.of(context).primaryColorLight
              : Color(0xFFFFFFFF),
        ),
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('${item.cabinetName}号柜',
                      style:
                          TextStyle(fontSize: 16, fontWeight: FontWeight.w400)),
                  Row(
                    children: [
                      Text(':',
                          style: TextStyle(
                              fontSize: 14, fontWeight: FontWeight.w400)),
                      Text(getNumRender(item),
                          style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w700,
                              color: Theme.of(context).primaryColor)),
                    ],
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 根据类型显示格口可用数量
  getNumRender(CabinetBoxUseableEntity item) {
    int? count = item.total;
    switch (selectBoxType) {
      case 0:
        count = item.superCount;
        break;
      case 1:
        count = item.hugeCount;
        break;
      case 2:
        count = item.largeCount;
        break;
      case 3:
        count = item.mediumCount;
        break;
      case 4:
        count = item.smallCount;
        break;
      case 5:
        count = item.miniCount;
        break;
      case 6:
        count = item.microCount;
        break;
      default:
        count = item.total;
        break;
    }
    return '${count ?? ''}';
  }

  /// 确认批量开柜
  confirmCabinet({String hostIndex = ''}) async {
    if (selectCabinet == null) {
      Fluttertoast.showToast(msg: '请选择柜机');
      return false;
    }
    bool result = await CommonUtils.confirm(context, '是否确认批量开柜？');
    if (result) {
      // 缓存批量开柜格口列表
      DataResult res = await CabinetDao.batchOpenBox(
          cabinetLocationCode, [selectCabinet!.cabinetId!], selectBoxType,
          hostIndex: hostIndex);
      if (res.result) {
        setState(() {
          batchBoxList = jsonConvert.convertList<BoxItemEntity>(res.data);
          cabinetName = selectCabinet!.cabinetName!;
          BoxItemEntity? cabinetBox = batchBoxList![0];
          lastCabinetBoxId = cabinetBox!.boxId!;
          currentCabinetId = cabinetBox.cabinetId!;
          cabinetName = cabinetBox.cabinetName!;
          cabinetBoxId = cabinetBox.boxId!;
          boxType = cabinetBox.boxType!;
          var boxLabel = cabinetBox.boxLabel!;
          currentBoxItem = $CabinetBoxItemFromJson(
              {'id': cabinetBoxId, 'type': boxType, 'boxLabel': boxLabel});
          showScanDb.boxType = boxType;
          showScanDb.cabinetId = currentCabinetId;
          showScanDb.cabinetBoxId = cabinetBoxId;
          cabinetBoxController.value = TextEditingValue(text: boxLabel);
        });
        Navigator.of(context).pop();
      } else {
        SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
      }
    }
  }

  openNextBox({int? cabinetBoxType}) async {
    LoadingUtil(
      status: '获取下一个格口...',
    ).show(context);
    var res = await CourierDao.courierShopOpenNextDoor(currentBoxItem?.id,
        cabinetBoxType: cabinetBoxType);
    LoadingUtil.dismiss(context);
    if (res != null && res.result) {
      if (cabinetBoxType != null) {
        SoundUtils.audioPushFn(SoundUtils.SUCCESS_OK);
      }
      setState(() {
        lastCabinetBoxId = res.data['cabinetBoxId'];
        currentCabinetId = res.data['cabinetId'];
        cabinetName = res.data['cabinetName'];
        cabinetBoxId = res.data['cabinetBoxId'];
        currentBoxItem = $CabinetBoxItemFromJson({
          'id': res.data['cabinetBoxId'],
          'type': res.data['boxType'],
          'boxLabel': res.data['boxLabel']
        });
        availableCount = res.data['availableCount'];
        boxType = res.data['boxType'];
      });
      if (cabinetBoxType == null) {
        resetScan();
      } else {
        showScanDb.cabinetId = currentCabinetId ?? '';
        showScanDb.cabinetBoxId = currentBoxItem?.id ?? '';
        showScanDb.boxType = currentBoxItem?.type;
      }
    } else {
      resetScan();
      stopSpot();
      SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_NO);
    }
  }

  double paddingSizeTop(BuildContext context, {bool isPlatform = false}) {
    final MediaQueryData data = MediaQuery.of(context);
    EdgeInsets padding = data.padding;
    padding = padding.copyWith(bottom: data.viewPadding.top);
    if (isPlatform) {
      if (Platform.isIOS) {
        return padding.top > 0 ? 5 : padding.top;
      }
    }
    return padding.top;
  }

  selectBoxItem() async {
    List<BoxItemEntity?>? list =
        batchBoxList?.where((e) => e?.isUsed == false).toList();
    if (list != null) {
      BoxItemEntity? boxItem = await CommonUtils.showBottomBoxItemSelectModal(
          context, list, currentBoxItem?.id);
      if (boxItem != null) {
        CabinetBoxItem cabinetBoxItem = $CabinetBoxItemFromJson({
          'id': boxItem.boxId,
          'type': boxItem.boxType,
          'boxLabel': boxItem.boxLabel,
          'pcbNo': boxItem.pcbNo
        });
        cabinetBoxId = cabinetBoxItem.id;
        boxType = boxItem.boxType!;
        currentBoxItem = cabinetBoxItem;
        showScanDb.cabinetId = currentCabinetId;
        showScanDb.cabinetBoxId = boxItem.boxId;
        showScanDb.boxType = boxItem.boxType;
        cabinetBoxController.value = TextEditingValue(text: boxItem.boxLabel!);
      }
    }
    setState(() {});
  }

  rollbackBoxItem() {
    int? indexUnused = batchBoxList
        ?.indexWhere((item) => item?.boxId == lastBoxItem?.id && !item!.isUsed);
    int? indexAll =
        batchBoxList?.indexWhere((item) => item?.boxId == lastBoxItem?.id);
    if (indexUnused != null) {
      if (indexUnused >= 1) {
        BoxItemEntity boxItem = batchBoxList![indexUnused - 1]!;
        batchBoxList![indexUnused - 1]?.isUsed = false;
        CabinetBoxItem cabinetBoxItem = $CabinetBoxItemFromJson({
          'id': boxItem.boxId,
          'type': boxItem.boxType,
          'boxLabel': boxItem.boxLabel,
          'pcbNo': boxItem.pcbNo
        });
        cabinetBoxId = cabinetBoxItem.id;
        boxType = boxItem.boxType!;
        currentBoxItem = cabinetBoxItem;
        showScanDb.cabinetId = currentCabinetId;
        showScanDb.cabinetBoxId = boxItem.boxId;
        showScanDb.boxType = boxItem.boxType;
        cabinetBoxController.value = TextEditingValue(text: boxItem.boxLabel!);
      } else if (indexUnused == -1) {
        if (indexAll != null) {
          batchBoxList![indexAll]?.isUsed = false;
        }
      } else {
        Fluttertoast.showToast(msg: '格口异常');
      }
    }
    setState(() {});
  }

  /// 跳过格口
  skipBoxItem({bool playSound = false}) {
    List<BoxItemEntity?>? list =
        batchBoxList?.where((e) => e?.isUsed == false).toList();
    int? indexUnused =
        list?.indexWhere((item) => item?.boxId == currentBoxItem?.id);
    int? indexAll = batchBoxList?.indexWhere(
        (item) => item?.boxId == currentBoxItem?.id && !item!.isUsed);
    bool flag = false;
    if (list == null) {
      flag = true;
    } else if (list.length == 0) {
      flag = true;
    }
    if (indexUnused == null) {
      flag = true;
    }
    if (indexAll == null) {
      flag = true;
    }
    if (flag || list?.isEmpty == true) {
      clearCurrentBox();
      return;
    }
    if (indexUnused! > 0) {
      if (indexAll! > -1) {
        batchBoxList![indexAll]?.isUsed = true;
      }
      BoxItemEntity boxItem = list![0]!;
      CabinetBoxItem cabinetBoxItem = $CabinetBoxItemFromJson({
        'id': boxItem.boxId,
        'type': boxItem.boxType,
        'boxLabel': boxItem.boxLabel,
        'pcbNo': boxItem.pcbNo
      });
      if (playSound) {
        SoundUtils.audioPushFn(SoundUtils.SKIP_SUCCESS);
      }
      cabinetBoxId = cabinetBoxItem.id;
      boxType = boxItem.boxType!;
      currentBoxItem = cabinetBoxItem;
      showScanDb.cabinetId = currentCabinetId;
      showScanDb.cabinetBoxId = boxItem.boxId;
      showScanDb.boxType = boxItem.boxType;
      cabinetBoxController.value = TextEditingValue(text: boxItem.boxLabel!);
    } else if (indexUnused == 0) {
      if (indexAll! > -1) {
        batchBoxList![indexAll]?.isUsed = true;
      }
      if (list!.length > 1) {
        BoxItemEntity boxItem = list[1]!;
        if (boxItem.boxId != currentBoxItem?.id) {
          if (playSound) {
            SoundUtils.audioPushFn(SoundUtils.SKIP_SUCCESS);
          }
          CabinetBoxItem cabinetBoxItem = $CabinetBoxItemFromJson({
            'id': boxItem.boxId,
            'type': boxItem.boxType,
            'boxLabel': boxItem.boxLabel,
            'pcbNo': boxItem.pcbNo
          });
          cabinetBoxId = cabinetBoxItem.id;
          boxType = boxItem.boxType!;
          currentBoxItem = cabinetBoxItem;
          showScanDb.cabinetId = currentCabinetId;
          showScanDb.cabinetBoxId = boxItem.boxId;
          showScanDb.boxType = boxItem.boxType;
          cabinetBoxController.value =
              TextEditingValue(text: boxItem.boxLabel!);
        } else {
          clearCurrentBox();
        }
      } else {
        clearCurrentBox();
      }
    } else {
      clearCurrentBox();
    }
    setState(() {});
  }

  /// 批量入柜格口用完清空并提示 // 修改 clearCurrentBox 方法
  clearCurrentBox() async {
    // 获取最新柜机数据
    await getUseableCabinetBoxList(isShowDialog: false);

   // 检查是否还有可用柜机
  bool hasAvailableBox = boxList.any((cabinet) => 
    getNumRender(cabinet) != '0' && getNumRender(cabinet).isNotEmpty
  );
  
  if (hasAvailableBox) {
    // 自动弹出选择框
    await selectCabinetInfo();
  } else {
    Fluttertoast.showToast(msg: '所有柜机格口已用完');
    SoundUtils.audioPushFn(SoundUtils.BATCH_FINISH);
    currentBoxItem = null;
    showScanDb.cabinetBoxId = '';
  }
  setState(() {});
  }

  showKeyBoard(TextInputType type) {
    textInputType = type;
    showKeyboard = true;
    setState(() {});
  }

  onKeyDown(MyKeyEvent data) {
    bool mobileFocus = mobileFocusNode_.hasFocus;
    if (mobileFocus) {
      handleEvent(data, mobileEditingController, 11,
          isScanMobile: true, checkMobileLast4: true);
    }
    setState(() {});
  }

  handleEvent(MyKeyEvent data, TextEditingController controller, int length,
      {bool isScanMobile = false, bool checkMobileLast4 = false}) {
    if (data.isChangeText()) {
      textInputType = TextInputType.name;
    } else if (data.isChangeNumber()) {
      textInputType = TextInputType.number;
    } else if (data.isHide()) {
      showKeyboard = false;
    } else if (data.isDone()) {
      showKeyboard = false;
      mobileGussByLast4();
    } else if (data.isDelete()) {
      if (controller.text.isNotEmpty) {
        String value = controller.text.substring(0, controller.text.length - 1);
        controller.value = TextEditingValue(text: value);
        controller.selection = TextSelection.fromPosition(
            TextPosition(offset: controller.text.length));
        if (isScanMobile) {
          checkMobileInput(controller.text);
        }
      }
    } else if (data.isClear()) {
      controller.value = const TextEditingValue(text: '');
      if (isScanMobile) {
        checkMobileInput(controller.text);
      }
    } else {
      String value = controller.text;
      if (value.length < length) {
        value += data.key;
        controller.value = TextEditingValue(text: value);
        controller.selection = TextSelection.fromPosition(
            TextPosition(offset: controller.text.length));
        if (isScanMobile) {
          checkMobileInput(controller.text);
        }
      }
    }
  }
}
