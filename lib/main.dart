import 'dart:async';
import 'dart:io';

import 'package:cabinet_flutter_app/common/config/DefaultConfig.dart';
import 'package:cabinet_flutter_app/common/event/HttpErrorEvent.dart';
import 'package:cabinet_flutter_app/common/localization/AppLocalizationsDelegate.dart';
import 'package:cabinet_flutter_app/common/net/Code.dart';
import 'package:cabinet_flutter_app/common/page_life_cycle.dart';
import 'package:cabinet_flutter_app/common/redux/AppState.dart';
import 'package:cabinet_flutter_app/common/utils/CommonUtils.dart';
import 'package:cabinet_flutter_app/common/utils/NavigatorUtils.dart';
import 'package:cabinet_flutter_app/common/utils/SoundUtils.dart';
import 'package:cabinet_flutter_app/page/HomePage.dart';
import 'package:cabinet_flutter_app/page/WelcomePage.dart';
import 'package:cabinet_flutter_app/page/login/LoginPageNew.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:fluwx/fluwx.dart';
import 'package:redux/redux.dart';
import 'package:rhttp/rhttp.dart';
import 'package:scan/scan.dart';

import 'common/dao/UserDao.dart';
import 'common/local/LocalStorage.dart';

Future<void> main() async {
  // Ensure that plugin services are initialized so that `availableCameras()`
  // can be called before `runApp()`
  WidgetsFlutterBinding.ensureInitialized();
  await Rhttp.init();
  runApp(new FlutterReduxApp());
  PaintingBinding.instance.imageCache.maximumSize = 100;

  if (Platform.isAndroid) {
    SystemUiOverlayStyle systemUiOverlayStyle = const SystemUiOverlayStyle(statusBarColor: Colors.transparent);
    SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);
  }
}

class FlutterReduxApp extends StatelessWidget {
  static const channel = String.fromEnvironment("CHANNEL");

  /// 创建Store，引用 AppState 中的 appReducer 实现 Reducer 方法
  /// initialState 初始化 State
  final store = new Store<AppState>(
    appReducer,

    ///初始化数据
    initialState: new AppState(
        scan: '',
        themeData: ThemeData(
          useMaterial3: false,
          primarySwatch: getChannelPrimarySwatch(),
          primaryColor: getChannelPrimaryColor(),
          fontFamily: 'Nunito',
//          primarySwatch: Colors.green,
//          primaryColor: Color(0xFF1ABCA2),
//          platform: TargetPlatform.android //滑动返回
        ),
        locale: Locale('zh', 'CH')),
  );

  static getChannelPrimaryColor() {
    return DefaultConfig().configs.PRIMARY_COLOR;
  }

  static getChannelPrimarySwatch() {
    return DefaultConfig().configs.PRIMARY_COLOR_SWATCH;
  }

  FlutterReduxApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    /// 通过 StoreProvider 应用 store
    return new StoreProvider(
      store: store,
      child: new StoreBuilder<AppState>(builder: (context, store) {
        return new MaterialApp(
          navigatorObservers: [PageNavigatorObserver()],
          title: DefaultConfig().configs.APP_NAME,

          ///多语言实现代理
          localizationsDelegates: [
            GlobalCupertinoLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            AppLocalizationsDelegate.delegate,
          ],
          locale: store.state.locale,
          supportedLocales: [
            const Locale('zh', 'CH'),
            const Locale('en', 'US'),
//            store.state.locale
          ],
          theme: store.state.themeData,
          routes: {
            WelcomePage.sName: (context) {
              store.state.platformLocale = Localizations.localeOf(context);
              return WelcomePage();
            },
            HomePage.sName: (context) {
              ///通过 Localizations.override 包裹一层，
              return new AppLocalizations(
                child: new HomePage(),
              );
            },
            LoginPageNew.sName: (context) {
              return new AppLocalizations(
                child: new LoginPageNew(''),
                // child: new CheckPhonePage(),
              );
            },
            // LoginOrRegisterPage.sName: (context) {
            //   ///通过 Localizations.override 包裹一层，
            //   return new AppLocalizations(
            //     child: new LoginOrRegisterPage(),
            //   );
            // },
          },
          builder: (BuildContext? context, Widget? child) {
            return FlutterEasyLoading(
              child: child!,
            );
          },
        );
      }),
    );
  }
}

class AppLocalizations extends StatefulWidget {
  final Widget child;

  AppLocalizations({Key? key, required this.child}) : super(key: key);

  @override
  State<AppLocalizations> createState() {
    return new _AppLocalizations();
  }
}

class _AppLocalizations extends State<AppLocalizations> {
  StreamSubscription? stream;
  Scan scan = new Scan();
  String scanResult = '';
  StreamSubscription<String>? _scanSubscription;
  int lastTime = 0;

  @override
  Widget build(BuildContext context) {
    return new StoreBuilder<AppState>(builder: (context, store) {
      ///通过 StoreBuilder 和 Localizations 实现实时多语言切换
      return new Localizations.override(
        context: context,
        locale: store.state.locale,
        child: widget.child,
      );
    });
  }

//  Store<AppState> _getStore() {
//    return StoreProvider.of(context);
//  }

  @override
  void initState() {
    super.initState();
    initWxPay();

    ///监听网络返回错误
    stream = Code.eventBus.on<HttpErrorEvent>().listen((event) {
      errorHandleFunction(event.code, event.message);
    });
//    if (Platform.isAndroid) {
//      _scanSubscription = scan.onScanChanged.listen((String result) {
////        print(result);
//        _getStore().dispatch(new UpdateScanAction(result));
//      });
//    }
  }

  @override
  void dispose() {
    super.dispose();
    stream?.cancel();
    _scanSubscription?.cancel();
  }

  errorHandleFunction(int code, message) async {
    switch (code) {
      case Code.NETWORK_TIMEOUT:
        SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_READ);
        //超时
        // Fluttertoast.showToast(msg: CommonUtils.getLocale(context).networkErrorTimeout);
        showMsg(context, CommonUtils.getLocale(context).networkErrorTimeout);
        break;
      case Code.NETWORK_ERROR:
        SoundUtils.audioPushFn(SoundUtils.ERROR_NOT_READ);
        showMsg(context, CommonUtils.getLocale(context).networkError);
        break;
      case 401:
        int nowSitamp = new DateTime.now().millisecondsSinceEpoch;
        bool isPush = false;
        if (lastTime == 0) {
          lastTime = nowSitamp;
          isPush = true;
        } else {
          if (nowSitamp - lastTime < 200) {
            lastTime = 0;
            isPush = false;
          }
        }
        if (LoginPageNew.sName != ModalRoute.of(context)?.settings.name) {
          showMsg(context, message != null ? message : CommonUtils.getLocale(context).networkError_401);
          NavigatorUtils.goLoginNew(context);
        }
        break;
      case 404:
        showMsg(context, message != null ? message : CommonUtils.getLocale(context).networkError_404);
        break;
      default:
        showMsg(context, message);
        break;
    }
  }

  showMsg(context, message) {
    bool isHomePage = HomePage.sName == ModalRoute.of(context)?.settings.name;
    if (isHomePage) {
      Fluttertoast.showToast(msg: message, toastLength: Toast.LENGTH_LONG, timeInSecForIosWeb: 2);
    } else {
      CommonUtils.showMessage(context, msg: message);
    }
  }

  autoLogin() async {
    print('自动登录');
    String? loginName = await LocalStorage.get<String>(DefaultConfig().configs.USER_NAME_KEY);
    String? password = await LocalStorage.get(DefaultConfig().configs.PW_KEY, isPrivate: true);
    var isLogout = await LocalStorage.get(DefaultConfig().configs.IS_LOGOUT);
    int? userType = await LocalStorage.get(DefaultConfig().configs.USER_TYPE, isPrivate: true);
    if (loginName != null && password != null && isLogout != true) {
      Store<AppState> store = StoreProvider.of(context);
      UserDao.autoLogin(loginName, password, userType, store, context);
    }
    NavigatorUtils.goLoginNew(context);
  }

  void initWxPay() async {
    registerWxApi(
      appId: DefaultConfig().configs.WX_APP_ID,
      doOnAndroid: true,
      doOnIOS: true,
      universalLink: DefaultConfig().configs.UNIVERSAL_LINK, //查看微信开放平台
    );
  }
}
