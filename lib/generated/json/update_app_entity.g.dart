import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/update_app_entity.dart';

UpdateAppEntity $UpdateAppEntityFromJson(Map<String, dynamic> json) {
  final UpdateAppEntity updateAppEntity = UpdateAppEntity();
  final String? appName = jsonConvert.convert<String>(json['appName']);
  if (appName != null) {
    updateAppEntity.appName = appName;
  }
  final String? appVer = jsonConvert.convert<String>(json['appVer']);
  if (appVer != null) {
    updateAppEntity.appVer = appVer;
  }
  final String? fileUrl = jsonConvert.convert<String>(json['fileUrl']);
  if (fileUrl != null) {
    updateAppEntity.fileUrl = fileUrl;
  }
  final String? releaseNotes = jsonConvert.convert<String>(json['releaseNotes']);
  if (releaseNotes != null) {
    updateAppEntity.releaseNotes = releaseNotes;
  }
  final int? hasForce = jsonConvert.convert<int>(json['hasForce']);
  if (hasForce != null) {
    updateAppEntity.hasForce = hasForce;
  }
  return updateAppEntity;
}

Map<String, dynamic> $UpdateAppEntityToJson(UpdateAppEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['appName'] = entity.appName;
  data['appVer'] = entity.appVer;
  data['fileUrl'] = entity.fileUrl;
  data['releaseNotes'] = entity.releaseNotes;
  data['hasForce'] = entity.hasForce;
  return data;
}

extension UpdateAppEntityExtension on UpdateAppEntity {
  UpdateAppEntity copyWith({
    String? appName,
    String? appVer,
    String? fileUrl,
    String? releaseNotes,
    int? hasForce,
  }) {
    return UpdateAppEntity()
      ..appName = appName ?? this.appName
      ..appVer = appVer ?? this.appVer
      ..fileUrl = fileUrl ?? this.fileUrl
      ..releaseNotes = releaseNotes ?? this.releaseNotes
      ..hasForce = hasForce ?? this.hasForce;
  }
}