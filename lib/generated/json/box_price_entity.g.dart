import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/box_price_entity.dart';

BoxPriceEntity $BoxPriceEntityFromJson(Map<String, dynamic> json) {
  final BoxPriceEntity boxPriceEntity = BoxPriceEntity();
  final int? supers = jsonConvert.convert<int>(json['supers']);
  if (supers != null) {
    boxPriceEntity.supers = supers;
  }
  final int? huge = jsonConvert.convert<int>(json['huge']);
  if (huge != null) {
    boxPriceEntity.huge = huge;
  }
  final int? large = jsonConvert.convert<int>(json['large']);
  if (large != null) {
    boxPriceEntity.large = large;
  }
  final int? medium = jsonConvert.convert<int>(json['medium']);
  if (medium != null) {
    boxPriceEntity.medium = medium;
  }
  final int? small = jsonConvert.convert<int>(json['small']);
  if (small != null) {
    boxPriceEntity.small = small;
  }
  final int? mini = jsonConvert.convert<int>(json['mini']);
  if (mini != null) {
    boxPriceEntity.mini = mini;
  }
  final int? micro = jsonConvert.convert<int>(json['micro']);
  if (micro != null) {
    boxPriceEntity.micro = micro;
  }
  return boxPriceEntity;
}

Map<String, dynamic> $BoxPriceEntityToJson(BoxPriceEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['supers'] = entity.supers;
  data['huge'] = entity.huge;
  data['large'] = entity.large;
  data['medium'] = entity.medium;
  data['small'] = entity.small;
  data['mini'] = entity.mini;
  data['micro'] = entity.micro;
  return data;
}

extension BoxPriceEntityExtension on BoxPriceEntity {
  BoxPriceEntity copyWith({
    int? supers,
    int? huge,
    int? large,
    int? medium,
    int? small,
    int? mini,
    int? micro,
  }) {
    return BoxPriceEntity()
      ..supers = supers ?? this.supers
      ..huge = huge ?? this.huge
      ..large = large ?? this.large
      ..medium = medium ?? this.medium
      ..small = small ?? this.small
      ..mini = mini ?? this.mini
      ..micro = micro ?? this.micro;
  }
}