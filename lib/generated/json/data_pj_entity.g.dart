import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/data_pj_entity.dart';

DataPjEntity $DataPjEntityFromJson(Map<String, dynamic> json) {
  final DataPjEntity dataPjEntity = DataPjEntity();
  final String? cabinetLocationId = jsonConvert.convert<String>(json['cabinetLocationId']);
  if (cabinetLocationId != null) {
    dataPjEntity.cabinetLocationId = cabinetLocationId;
  }
  final String? cabinetLocationName = jsonConvert.convert<String>(json['cabinetLocationName']);
  if (cabinetLocationName != null) {
    dataPjEntity.cabinetLocationName = cabinetLocationName;
  }
  final String? cabinetLocationCode = jsonConvert.convert<String>(json['cabinetLocationCode']);
  if (cabinetLocationCode != null) {
    dataPjEntity.cabinetLocationCode = cabinetLocationCode;
  }
  final int? num = jsonConvert.convert<int>(json['num']);
  if (num != null) {
    dataPjEntity.num = num;
  }
  return dataPjEntity;
}

Map<String, dynamic> $DataPjEntityToJson(DataPjEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['cabinetLocationId'] = entity.cabinetLocationId;
  data['cabinetLocationName'] = entity.cabinetLocationName;
  data['cabinetLocationCode'] = entity.cabinetLocationCode;
  data['num'] = entity.num;
  return data;
}

extension DataPjEntityExtension on DataPjEntity {
  DataPjEntity copyWith({
    String? cabinetLocationId,
    String? cabinetLocationName,
    String? cabinetLocationCode,
    int? num,
  }) {
    return DataPjEntity()
      ..cabinetLocationId = cabinetLocationId ?? this.cabinetLocationId
      ..cabinetLocationName = cabinetLocationName ?? this.cabinetLocationName
      ..cabinetLocationCode = cabinetLocationCode ?? this.cabinetLocationCode
      ..num = num ?? this.num;
  }
}