import 'package:cabinet_flutter_app/common/entitys/user_info_entity.dart';
import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';

UserInfoEntity $UserInfoEntityFromJson(Map<String, dynamic> json) {
  final UserInfoEntity userInfoEntity = UserInfoEntity();
  final String? channel = jsonConvert.convert<String>(json['channel']);
  if (channel != null) {
    userInfoEntity.channel = channel;
  }
  final int? createTime = jsonConvert.convert<int>(json['createTime']);
  if (createTime != null) {
    userInfoEntity.createTime = createTime;
  }
  final String? type = jsonConvert.convert<String>(json['type']);
  if (type != null) {
    userInfoEntity.type = type;
  }
  final String? cabinetNameList = jsonConvert.convert<String>(json['cabinetNameList']);
  if (type != null) {
    userInfoEntity.cabinetNameList = cabinetNameList;
  }
  final String? deviceId = jsonConvert.convert<String>(json['deviceId']);
  if (deviceId != null) {
    userInfoEntity.deviceId = deviceId;
  } else {
    userInfoEntity.deviceId = '';
  }
  final String? cabinetLocationIds = jsonConvert.convert<String>(json['cabinetLocationIds']);
  if (cabinetLocationIds != null) {
    userInfoEntity.cabinetLocationIds = cabinetLocationIds;
  } else {
    userInfoEntity.cabinetLocationIds = '';
  }
  final String? deviceModel = jsonConvert.convert<String>(json['deviceModel']);
  if (deviceModel != null) {
    userInfoEntity.deviceModel = deviceModel;
  } else {
    userInfoEntity.deviceModel = '';
  }
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    userInfoEntity.id = id;
  }
  final String? loginName = jsonConvert.convert<String>(json['loginName']);
  if (loginName != null) {
    userInfoEntity.loginName = loginName;
  }
  final String? mobile = jsonConvert.convert<String>(json['mobile']);
  if (mobile != null) {
    userInfoEntity.mobile = mobile;
  } else {
    userInfoEntity.mobile = '';
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    userInfoEntity.name = name;
  } else {
    userInfoEntity.name = '';
  }
  final String? password = jsonConvert.convert<String>(json['password']);
  if (password != null) {
    userInfoEntity.password = password;
  }
  final int? shopId = jsonConvert.convert<int>(json['shopId']);
  if (shopId != null) {
    userInfoEntity.shopId = shopId;
  }
  final int? siteId = jsonConvert.convert<int>(json['siteId']);
  if (siteId != null) {
    userInfoEntity.siteId = siteId;
  }
  final String? sopAccountId = jsonConvert.convert<String>(json['sopAccountId']);
  if (sopAccountId != null) {
    userInfoEntity.sopAccountId = sopAccountId;
  }
  final String? stationAddress = jsonConvert.convert<String>(json['stationAddress']);
  if (stationAddress != null) {
    userInfoEntity.stationAddress = stationAddress;
  } else {
    userInfoEntity.stationAddress = '';
  }
  final String? stationName = jsonConvert.convert<String>(json['stationName']);
  if (stationName != null) {
    userInfoEntity.stationName = stationName;
  } else {
    userInfoEntity.stationName = '';
  }
  final int? updateTime = jsonConvert.convert<int>(json['updateTime']);
  if (updateTime != null) {
    userInfoEntity.updateTime = updateTime;
  }
  final int? verified = jsonConvert.convert<int>(json['verified']);
  if (verified != null) {
    userInfoEntity.verified = verified;
  }
  return userInfoEntity;
}

Map<String, dynamic> $UserInfoEntityToJson(UserInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['channel'] = entity.channel;
  data['createTime'] = entity.createTime;
  data['type'] = entity.type;
  data['cabinetNameList'] = entity.cabinetNameList;
  data['deviceId'] = entity.deviceId;
  data['cabinetLocationIds'] = entity.cabinetLocationIds;
  data['deviceModel'] = entity.deviceModel;
  data['id'] = entity.id;
  data['loginName'] = entity.loginName;
  data['mobile'] = entity.mobile;
  data['name'] = entity.name;
  data['password'] = entity.password;
  data['shopId'] = entity.shopId;
  data['siteId'] = entity.siteId;
  data['sopAccountId'] = entity.sopAccountId;
  data['stationAddress'] = entity.stationAddress;
  data['stationName'] = entity.stationName;
  data['updateTime'] = entity.updateTime;
  data['verified'] = entity.verified;
  return data;
}

extension UserInfoEntityExtension on UserInfoEntity {
  UserInfoEntity copyWith({
    String? channel,
    int? createTime,
    String? type,
    String? cabinetNameList,
    String? deviceId,
    String? cabinetLocationIds,
    String? deviceModel,
    int? id,
    String? loginName,
    String? mobile,
    String? name,
    String? password,
    int? shopId,
    int? siteId,
    String? sopAccountId,
    String? stationAddress,
    String? stationName,
    int? updateTime,
    int? verified,
  }) {
    return UserInfoEntity()
      ..channel = channel ?? this.channel
      ..createTime = createTime ?? this.createTime
      ..type = type ?? this.type
      ..cabinetNameList = cabinetNameList ?? this.cabinetNameList
      ..deviceId = deviceId ?? this.deviceId
      ..cabinetLocationIds = cabinetLocationIds ?? this.cabinetLocationIds
      ..deviceModel = deviceModel ?? this.deviceModel
      ..id = id ?? this.id
      ..loginName = loginName ?? this.loginName
      ..mobile = mobile ?? this.mobile
      ..name = name ?? this.name
      ..password = password ?? this.password
      ..shopId = shopId ?? this.shopId
      ..siteId = siteId ?? this.siteId
      ..sopAccountId = sopAccountId ?? this.sopAccountId
      ..stationAddress = stationAddress ?? this.stationAddress
      ..stationName = stationName ?? this.stationName
      ..updateTime = updateTime ?? this.updateTime
      ..verified = verified ?? this.verified;
  }
}
