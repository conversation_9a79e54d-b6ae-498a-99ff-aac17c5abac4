import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/brand_bind_entity.dart';

BrandBindEntity $BrandBindEntityFromJson(Map<String, dynamic> json) {
  final BrandBindEntity brandBindEntity = BrandBindEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    brandBindEntity.id = id;
  }
  final String? brandCode = jsonConvert.convert<String>(json['brandCode']);
  if (brandCode != null) {
    brandBindEntity.brandCode = brandCode;
  }
  final int? allowIn = jsonConvert.convert<int>(json['allowIn']);
  if (allowIn != null) {
    brandBindEntity.allowIn = allowIn;
  }
  final int? syncTrace = jsonConvert.convert<int>(json['syncTrace']);
  if (syncTrace != null) {
    brandBindEntity.syncTrace = syncTrace;
  }
  final int? switchAutoSign = jsonConvert.convert<int>(json['switchAutoSign']);
  if (switchAutoSign != null) {
    brandBindEntity.switchAutoSign = switchAutoSign;
  }
  final int? autoSignDay = jsonConvert.convert<int>(json['autoSignDay']);
  if (autoSignDay != null) {
    brandBindEntity.autoSignDay = autoSignDay;
  }
  return brandBindEntity;
}

Map<String, dynamic> $BrandBindEntityToJson(BrandBindEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['brandCode'] = entity.brandCode;
  data['allowIn'] = entity.allowIn;
  data['syncTrace'] = entity.syncTrace;
  data['switchAutoSign'] = entity.switchAutoSign;
  data['autoSignDay'] = entity.autoSignDay;
  return data;
}

extension BrandBindEntityExtension on BrandBindEntity {
  BrandBindEntity copyWith({
    String? id,
    String? brandCode,
    int? allowIn,
    int? syncTrace,
    int? switchAutoSign,
    int? autoSignDay,
  }) {
    return BrandBindEntity()
      ..id = id ?? this.id
      ..brandCode = brandCode ?? this.brandCode
      ..allowIn = allowIn ?? this.allowIn
      ..syncTrace = syncTrace ?? this.syncTrace
      ..switchAutoSign = switchAutoSign ?? this.switchAutoSign
      ..autoSignDay = autoSignDay ?? this.autoSignDay;
  }
}