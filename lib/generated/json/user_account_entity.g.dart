import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/user_account_entity.dart';

UserAccountEntity $UserAccountEntityFromJson(Map<String, dynamic> json) {
  final UserAccountEntity userAccountEntity = UserAccountEntity();
  final String? createBy = jsonConvert.convert<String>(json['createBy']);
  if (createBy != null) {
    userAccountEntity.createBy = createBy;
  }
  final String? createDate = jsonConvert.convert<String>(json['createDate']);
  if (createDate != null) {
    userAccountEntity.createDate = createDate;
  }
  final String? deviceId = jsonConvert.convert<String>(json['deviceId']);
  if (deviceId != null) {
    userAccountEntity.deviceId = deviceId;
  }
  final String? deviceName = jsonConvert.convert<String>(json['deviceName']);
  if (deviceName != null) {
    userAccountEntity.deviceName = deviceName;
  }
  final String? expireDate = jsonConvert.convert<String>(json['expireDate']);
  if (expireDate != null) {
    userAccountEntity.expireDate = expireDate;
  }
  final String? company = jsonConvert.convert<String>(json['company']);
  if (company != null) {
    userAccountEntity.company = company;
  }
  final UserAccountExt? ext = jsonConvert.convert<UserAccountExt>(json['ext']);
  if (ext != null) {
    userAccountEntity.ext = ext;
  }
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    userAccountEntity.id = id;
  }
  final bool? isIntercept = jsonConvert.convert<bool>(json['isIntercept']);
  if (isIntercept != null) {
    userAccountEntity.isIntercept = isIntercept;
  }
  final String? loginName = jsonConvert.convert<String>(json['loginName']);
  if (loginName != null) {
    userAccountEntity.loginName = loginName;
  }
  final String? mobile = jsonConvert.convert<String>(json['mobile']);
  if (mobile != null) {
    userAccountEntity.mobile = mobile;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    userAccountEntity.name = name;
  }
  final String? openId = jsonConvert.convert<String>(json['openId']);
  if (openId != null) {
    userAccountEntity.openId = openId;
  }
  final String? password = jsonConvert.convert<String>(json['password']);
  if (password != null) {
    userAccountEntity.password = password;
  }
  final String? password2 = jsonConvert.convert<String>(json['password2']);
  if (password2 != null) {
    userAccountEntity.password2 = password2;
  }
  final String? pjCode = jsonConvert.convert<String>(json['pjCode']);
  if (pjCode != null) {
    userAccountEntity.pjCode = pjCode;
  }
  final String? preSite = jsonConvert.convert<String>(json['preSite']);
  if (preSite != null) {
    userAccountEntity.preSite = preSite;
  }
  final String? remarks = jsonConvert.convert<String>(json['remarks']);
  if (remarks != null) {
    userAccountEntity.remarks = remarks;
  }
  final String? siteCode = jsonConvert.convert<String>(json['siteCode']);
  if (siteCode != null) {
    userAccountEntity.siteCode = siteCode;
  }
  final String? siteName = jsonConvert.convert<String>(json['siteName']);
  if (siteName != null) {
    userAccountEntity.siteName = siteName;
  }
  final String? sms = jsonConvert.convert<String>(json['sms']);
  if (sms != null) {
    userAccountEntity.sms = sms;
  }
  final String? threeCode = jsonConvert.convert<String>(json['threeCode']);
  if (threeCode != null) {
    userAccountEntity.threeCode = threeCode;
  }
  final String? token = jsonConvert.convert<String>(json['token']);
  if (token != null) {
    userAccountEntity.token = token;
  }
  final String? type = jsonConvert.convert<String>(json['type']);
  if (type != null) {
    userAccountEntity.type = type;
  }
  final String? updateBy = jsonConvert.convert<String>(json['updateBy']);
  if (updateBy != null) {
    userAccountEntity.updateBy = updateBy;
  }
  final String? updateDate = jsonConvert.convert<String>(json['updateDate']);
  if (updateDate != null) {
    userAccountEntity.updateDate = updateDate;
  }
  final String? userCode = jsonConvert.convert<String>(json['userCode']);
  if (userCode != null) {
    userAccountEntity.userCode = userCode;
  }
  return userAccountEntity;
}

Map<String, dynamic> $UserAccountEntityToJson(UserAccountEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['createBy'] = entity.createBy;
  data['createDate'] = entity.createDate;
  data['deviceId'] = entity.deviceId;
  data['deviceName'] = entity.deviceName;
  data['expireDate'] = entity.expireDate;
  data['company'] = entity.company;
  data['ext'] = entity.ext?.toJson();
  data['id'] = entity.id;
  data['isIntercept'] = entity.isIntercept;
  data['loginName'] = entity.loginName;
  data['mobile'] = entity.mobile;
  data['name'] = entity.name;
  data['openId'] = entity.openId;
  data['password'] = entity.password;
  data['password2'] = entity.password2;
  data['pjCode'] = entity.pjCode;
  data['preSite'] = entity.preSite;
  data['remarks'] = entity.remarks;
  data['siteCode'] = entity.siteCode;
  data['siteName'] = entity.siteName;
  data['sms'] = entity.sms;
  data['threeCode'] = entity.threeCode;
  data['token'] = entity.token;
  data['type'] = entity.type;
  data['updateBy'] = entity.updateBy;
  data['updateDate'] = entity.updateDate;
  data['userCode'] = entity.userCode;
  return data;
}

extension UserAccountEntityExtension on UserAccountEntity {
  UserAccountEntity copyWith({
    String? createBy,
    String? createDate,
    String? deviceId,
    String? deviceName,
    String? expireDate,
    String? company,
    UserAccountExt? ext,
    String? id,
    bool? isIntercept,
    String? loginName,
    String? mobile,
    String? name,
    String? openId,
    String? password,
    String? password2,
    String? pjCode,
    String? preSite,
    String? remarks,
    String? siteCode,
    String? siteName,
    String? sms,
    String? threeCode,
    String? token,
    String? type,
    String? updateBy,
    String? updateDate,
    String? userCode,
  }) {
    return UserAccountEntity()
      ..createBy = createBy ?? this.createBy
      ..createDate = createDate ?? this.createDate
      ..deviceId = deviceId ?? this.deviceId
      ..deviceName = deviceName ?? this.deviceName
      ..expireDate = expireDate ?? this.expireDate
      ..company = company ?? this.company
      ..ext = ext ?? this.ext
      ..id = id ?? this.id
      ..isIntercept = isIntercept ?? this.isIntercept
      ..loginName = loginName ?? this.loginName
      ..mobile = mobile ?? this.mobile
      ..name = name ?? this.name
      ..openId = openId ?? this.openId
      ..password = password ?? this.password
      ..password2 = password2 ?? this.password2
      ..pjCode = pjCode ?? this.pjCode
      ..preSite = preSite ?? this.preSite
      ..remarks = remarks ?? this.remarks
      ..siteCode = siteCode ?? this.siteCode
      ..siteName = siteName ?? this.siteName
      ..sms = sms ?? this.sms
      ..threeCode = threeCode ?? this.threeCode
      ..token = token ?? this.token
      ..type = type ?? this.type
      ..updateBy = updateBy ?? this.updateBy
      ..updateDate = updateDate ?? this.updateDate
      ..userCode = userCode ?? this.userCode;
  }
}

UserAccountExt $UserAccountExtFromJson(Map<String, dynamic> json) {
  final UserAccountExt userAccountExt = UserAccountExt();
  return userAccountExt;
}

Map<String, dynamic> $UserAccountExtToJson(UserAccountExt entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  return data;
}

extension UserAccountExtExtension on UserAccountExt {
}