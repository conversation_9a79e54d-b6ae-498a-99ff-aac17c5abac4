import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/user_pay_account_entity.dart';

UserPayAccountEntity $UserPayAccountEntityFromJson(Map<String, dynamic> json) {
  final UserPayAccountEntity userPayAccountEntity = UserPayAccountEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    userPayAccountEntity.id = id;
  }
  final String? code = jsonConvert.convert<String>(json['code']);
  if (code != null) {
    userPayAccountEntity.code = code;
  }
  final int? commission = jsonConvert.convert<int>(json['commission']);
  if (commission != null) {
    userPayAccountEntity.commission = commission;
  }
  final int? balance = jsonConvert.convert<int>(json['balance']);
  if (balance != null) {
    userPayAccountEntity.balance = balance;
  }
  final int? waitWithdrawalMoney = jsonConvert.convert<int>(json['waitWithdrawalMoney']);
  if (waitWithdrawalMoney != null) {
    userPayAccountEntity.waitWithdrawalMoney = waitWithdrawalMoney;
  }
  final int? totalWithdrawalMoney = jsonConvert.convert<int>(json['totalWithdrawalMoney']);
  if (totalWithdrawalMoney != null) {
    userPayAccountEntity.totalWithdrawalMoney = totalWithdrawalMoney;
  }
  final int? frozenMoney = jsonConvert.convert<int>(json['frozenMoney']);
  if (frozenMoney != null) {
    userPayAccountEntity.frozenMoney = frozenMoney;
  }
  final String? realName = jsonConvert.convert<String>(json['realName']);
  if (realName != null) {
    userPayAccountEntity.realName = realName;
  }
  final String? wechatId = jsonConvert.convert<String>(json['wechatId']);
  if (wechatId != null) {
    userPayAccountEntity.wechatId = wechatId;
  }
  final String? wechatAccount = jsonConvert.convert<String>(json['wechatAccount']);
  if (wechatAccount != null) {
    userPayAccountEntity.wechatAccount = wechatAccount;
  }
  final String? alipayId = jsonConvert.convert<String>(json['alipayId']);
  if (alipayId != null) {
    userPayAccountEntity.alipayId = alipayId;
  }
  final String? alipayAccount = jsonConvert.convert<String>(json['alipayAccount']);
  if (alipayAccount != null) {
    userPayAccountEntity.alipayAccount = alipayAccount;
  }
  final String? alipayIdNumber = jsonConvert.convert<String>(json['alipayIdNumber']);
  if (alipayIdNumber != null) {
    userPayAccountEntity.alipayIdNumber = alipayIdNumber;
  }
  final String? bindTime = jsonConvert.convert<String>(json['bindTime']);
  if (bindTime != null) {
    userPayAccountEntity.bindTime = bindTime;
  }
  final String? bindTargetName = jsonConvert.convert<String>(json['bindTargetName']);
  if (bindTargetName != null) {
    userPayAccountEntity.bindTargetName = bindTargetName;
  }
  final String? bindTargetId = jsonConvert.convert<String>(json['bindTargetId']);
  if (bindTargetId != null) {
    userPayAccountEntity.bindTargetId = bindTargetId;
  }
  final int? bindTargetType = jsonConvert.convert<int>(json['bindTargetType']);
  if (bindTargetType != null) {
    userPayAccountEntity.bindTargetType = bindTargetType;
  }
  final int? minWithdrawMoney = jsonConvert.convert<int>(json['minWithdrawMoney']);
  if (minWithdrawMoney != null) {
    userPayAccountEntity.minWithdrawMoney = minWithdrawMoney;
  }
  final int? maxWithdrawMoney = jsonConvert.convert<int>(json['maxWithdrawMoney']);
  if (maxWithdrawMoney != null) {
    userPayAccountEntity.maxWithdrawMoney = maxWithdrawMoney;
  }
  return userPayAccountEntity;
}

Map<String, dynamic> $UserPayAccountEntityToJson(UserPayAccountEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['code'] = entity.code;
  data['commission'] = entity.commission;
  data['balance'] = entity.balance;
  data['waitWithdrawalMoney'] = entity.waitWithdrawalMoney;
  data['totalWithdrawalMoney'] = entity.totalWithdrawalMoney;
  data['frozenMoney'] = entity.frozenMoney;
  data['realName'] = entity.realName;
  data['wechatId'] = entity.wechatId;
  data['wechatAccount'] = entity.wechatAccount;
  data['alipayId'] = entity.alipayId;
  data['alipayAccount'] = entity.alipayAccount;
  data['alipayIdNumber'] = entity.alipayIdNumber;
  data['bindTime'] = entity.bindTime;
  data['bindTargetName'] = entity.bindTargetName;
  data['bindTargetId'] = entity.bindTargetId;
  data['bindTargetType'] = entity.bindTargetType;
  data['minWithdrawMoney'] = entity.minWithdrawMoney;
  data['maxWithdrawMoney'] = entity.maxWithdrawMoney;
  return data;
}

extension UserPayAccountEntityExtension on UserPayAccountEntity {
  UserPayAccountEntity copyWith({
    String? id,
    String? code,
    int? commission,
    int? balance,
    int? waitWithdrawalMoney,
    int? totalWithdrawalMoney,
    int? frozenMoney,
    String? realName,
    String? wechatId,
    String? wechatAccount,
    String? alipayId,
    String? alipayAccount,
    String? alipayIdNumber,
    String? bindTime,
    String? bindTargetName,
    String? bindTargetId,
    int? bindTargetType,
    int? minWithdrawMoney,
    int? maxWithdrawMoney,
  }) {
    return UserPayAccountEntity()
      ..id = id ?? this.id
      ..code = code ?? this.code
      ..commission = commission ?? this.commission
      ..balance = balance ?? this.balance
      ..waitWithdrawalMoney = waitWithdrawalMoney ?? this.waitWithdrawalMoney
      ..totalWithdrawalMoney = totalWithdrawalMoney ?? this.totalWithdrawalMoney
      ..frozenMoney = frozenMoney ?? this.frozenMoney
      ..realName = realName ?? this.realName
      ..wechatId = wechatId ?? this.wechatId
      ..wechatAccount = wechatAccount ?? this.wechatAccount
      ..alipayId = alipayId ?? this.alipayId
      ..alipayAccount = alipayAccount ?? this.alipayAccount
      ..alipayIdNumber = alipayIdNumber ?? this.alipayIdNumber
      ..bindTime = bindTime ?? this.bindTime
      ..bindTargetName = bindTargetName ?? this.bindTargetName
      ..bindTargetId = bindTargetId ?? this.bindTargetId
      ..bindTargetType = bindTargetType ?? this.bindTargetType
      ..minWithdrawMoney = minWithdrawMoney ?? this.minWithdrawMoney
      ..maxWithdrawMoney = maxWithdrawMoney ?? this.maxWithdrawMoney;
  }
}