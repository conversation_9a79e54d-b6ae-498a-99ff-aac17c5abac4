import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/package_view_entity.dart';

PackageViewEntity $PackageViewEntityFromJson(Map<String, dynamic> json) {
  final PackageViewEntity packageViewEntity = PackageViewEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    packageViewEntity.id = id;
  }
  final String? waybillNo = jsonConvert.convert<String>(json['waybillNo']);
  if (waybillNo != null) {
    packageViewEntity.waybillNo = waybillNo;
  }
  final String? cabinetLocationCode = jsonConvert.convert<String>(json['cabinetLocationCode']);
  if (cabinetLocationCode != null) {
    packageViewEntity.cabinetLocationCode = cabinetLocationCode;
  }
  final String? cabinetLocationName = jsonConvert.convert<String>(json['cabinetLocationName']);
  if (cabinetLocationName != null) {
    packageViewEntity.cabinetLocationName = cabinetLocationName;
  }
  final String? waybillOrderId = jsonConvert.convert<String>(json['waybillOrderId']);
  if (waybillOrderId != null) {
    packageViewEntity.waybillOrderId = waybillOrderId;
  }
  final String? brandCode = jsonConvert.convert<String>(json['brandCode']);
  if (brandCode != null) {
    packageViewEntity.brandCode = brandCode;
  }
  final String? brandName = jsonConvert.convert<String>(json['brandName']);
  if (brandName != null) {
    packageViewEntity.brandName = brandName;
  }
  final String? orderId = jsonConvert.convert<String>(json['orderId']);
  if (orderId != null) {
    packageViewEntity.orderId = orderId;
  }
  final String? receiverName = jsonConvert.convert<String>(json['receiverName']);
  if (receiverName != null) {
    packageViewEntity.receiverName = receiverName;
  }
  final String? receiverMobile = jsonConvert.convert<String>(json['receiverMobile']);
  if (receiverMobile != null) {
    packageViewEntity.receiverMobile = receiverMobile;
  }
   final String? virtualPhone = jsonConvert.convert<String>(json['virtualPhone']);
  if (virtualPhone != null) {
    packageViewEntity.virtualPhone = virtualPhone;
  }
  final String? keepEffectTime = jsonConvert.convert<String>(json['keepEffectTime']);
  if (keepEffectTime != null) {
    packageViewEntity.keepEffectTime = keepEffectTime;
  }
  final String? cabinetBoxLabel = jsonConvert.convert<String>(json['cabinetBoxLabel']);
  if (cabinetBoxLabel != null) {
    packageViewEntity.cabinetBoxLabel = cabinetBoxLabel;
  }
  final String? inboundTime = jsonConvert.convert<String>(json['inboundTime']);
  if (inboundTime != null) {
    packageViewEntity.inboundTime = inboundTime;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    packageViewEntity.createTime = createTime;
  }
  final int? orderType = jsonConvert.convert<int>(json['orderType']);
  if (orderType != null) {
    packageViewEntity.orderType = orderType;
  }
  final int? outboundType = jsonConvert.convert<int>(json['outboundType']);
  if (outboundType != null) {
    packageViewEntity.outboundType = outboundType;
  }
  final String? outboundTime = jsonConvert.convert<String>(json['outboundTime']);
  if (outboundTime != null) {
    packageViewEntity.outboundTime = outboundTime;
  }
  final String? checkCode = jsonConvert.convert<String>(json['checkCode']);
  if (checkCode != null) {
    packageViewEntity.checkCode = checkCode;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    packageViewEntity.status = status;
  }
  final int? messageSmsStatus = jsonConvert.convert<int>(json['messageSmsStatus']);
  if (messageSmsStatus != null) {
    packageViewEntity.messageSmsStatus = messageSmsStatus;
  }
  final int? messageWxStatus = jsonConvert.convert<int>(json['messageWxStatus']);
  if (messageWxStatus != null) {
    packageViewEntity.messageWxStatus = messageWxStatus;
  }
  final int? price = jsonConvert.convert<int>(json['price']);
  if (price != null) {
    packageViewEntity.price = price;
  }
  final int? messageType = jsonConvert.convert<int>(json['messageType']);
  if (messageType != null) {
    packageViewEntity.messageType = messageType;
  }
  final int? cabinetType = jsonConvert.convert<int>(json['cabinetType']);
  if (cabinetType != null) {
    packageViewEntity.cabinetType = cabinetType;
  }
  final String? cabinetName = jsonConvert.convert<String>(json['cabinetName']);
  if (cabinetName != null) {
    packageViewEntity.cabinetName = cabinetName;
  }
  final String? cabinetBoxId = jsonConvert.convert<String>(json['cabinetBoxId']);
  if (cabinetBoxId != null) {
    packageViewEntity.cabinetBoxId = cabinetBoxId;
  }
  final bool? keepEffect = jsonConvert.convert<bool>(json['keepEffect']);
  if (keepEffect != null) {
    packageViewEntity.keepEffect = keepEffect;
  }
  final int? hasOutbound = jsonConvert.convert<int>(json['hasOutbound']);
  if (hasOutbound != null) {
    packageViewEntity.hasOutbound = hasOutbound;
  }
  final int? inboundDeliveryStatus = jsonConvert.convert<int>(json['inboundDeliveryStatus']);
  if (inboundDeliveryStatus != null) {
    packageViewEntity.inboundDeliveryStatus = inboundDeliveryStatus;
  }
  final int? storeType = jsonConvert.convert<int>(json['storeType']);
  if (storeType != null) {
    packageViewEntity.storeType = storeType;
  }
  final String? shelfName = jsonConvert.convert<String>(json['shelfName']);
  if (shelfName != null) {
    packageViewEntity.shelfName = shelfName;
  }
  final String? overdueTime = jsonConvert.convert<String>(json['overdueTime']);
  if (overdueTime != null) {
    packageViewEntity.overdueTime = overdueTime;
  }
  final String? overdueEffectTime = jsonConvert.convert<String>(json['overdueEffectTime']);
  if (overdueEffectTime != null) {
    packageViewEntity.overdueEffectTime = overdueEffectTime;
  }
  final String? inboundImageUrl = jsonConvert.convert<String>(json['inboundImageUrl']);
  if (inboundImageUrl != null) {
    packageViewEntity.inboundImageUrl = inboundImageUrl;
  }
  final String? outboundImageUrl = jsonConvert.convert<String>(json['outboundImageUrl']);
  if (outboundImageUrl != null) {
    packageViewEntity.outboundImageUrl = outboundImageUrl;
  }
  final String? inboundUserName = jsonConvert.convert<String>(json['inboundUserName']);
  if (inboundUserName != null) {
    packageViewEntity.inboundUserName = inboundUserName;
  }
  final String? inboundUserMobile = jsonConvert.convert<String>(json['inboundUserMobile']);
  if (inboundUserMobile != null) {
    packageViewEntity.inboundUserMobile = inboundUserMobile;
  }
  final int? inboundUserFee = jsonConvert.convert<int>(json['inboundUserFee']);
  if (inboundUserFee != null) {
    packageViewEntity.inboundUserFee = inboundUserFee;
  }
  final int? hasSign = jsonConvert.convert<int>(json['hasSign']);
  if (hasSign != null) {
    packageViewEntity.hasSign = hasSign;
  }
  final int? signDeliveryStatus = jsonConvert.convert<int>(json['signDeliveryStatus']);
  if (signDeliveryStatus != null) {
    packageViewEntity.signDeliveryStatus = signDeliveryStatus;
  }
  final int? hasReturn = jsonConvert.convert<int>(json['hasReturn']);
  if (hasReturn != null) {
    packageViewEntity.hasReturn = hasReturn;
  }
  final int? returnDeliveryStatus = jsonConvert.convert<int>(json['returnDeliveryStatus']);
  if (returnDeliveryStatus != null) {
    packageViewEntity.returnDeliveryStatus = returnDeliveryStatus;
  }
  final int? hasDp = jsonConvert.convert<int>(json['hasDp']);
  if (hasDp != null) {
    packageViewEntity.hasDp = hasDp;
  }
  final int? dpStatus = jsonConvert.convert<int>(json['dpStatus']);
  if (dpStatus != null) {
    packageViewEntity.dpStatus = dpStatus;
  }
  final String? dpMsg = jsonConvert.convert<String>(json['dpMsg']);
  if (dpMsg != null) {
    packageViewEntity.dpMsg = dpMsg;
  }
  final String? yzAccountId = jsonConvert.convert<String>(json['yzAccountId']);
  if (dpMsg != null) {
    packageViewEntity.yzAccountId = yzAccountId;
  }
  return packageViewEntity;
}

Map<String, dynamic> $PackageViewEntityToJson(PackageViewEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['waybillNo'] = entity.waybillNo;
  data['cabinetLocationCode'] = entity.cabinetLocationCode;
  data['cabinetLocationName'] = entity.cabinetLocationName;
  data['waybillOrderId'] = entity.waybillOrderId;
  data['brandCode'] = entity.brandCode;
  data['brandName'] = entity.brandName;
  data['orderId'] = entity.orderId;
  data['receiverName'] = entity.receiverName;
  data['receiverMobile'] = entity.receiverMobile;
  data['keepEffectTime'] = entity.keepEffectTime;
  data['cabinetBoxLabel'] = entity.cabinetBoxLabel;
  data['inboundTime'] = entity.inboundTime;
  data['createTime'] = entity.createTime;
  data['orderType'] = entity.orderType;
  data['outboundType'] = entity.outboundType;
  data['outboundTime'] = entity.outboundTime;
  data['checkCode'] = entity.checkCode;
  data['status'] = entity.status;
  data['messageSmsStatus'] = entity.messageSmsStatus;
  data['messageWxStatus'] = entity.messageWxStatus;
  data['price'] = entity.price;
  data['messageType'] = entity.messageType;
  data['cabinetType'] = entity.cabinetType;
  data['cabinetName'] = entity.cabinetName;
  data['cabinetBoxId'] = entity.cabinetBoxId;
  data['keepEffect'] = entity.keepEffect;
  data['hasOutbound'] = entity.hasOutbound;
  data['inboundDeliveryStatus'] = entity.inboundDeliveryStatus;
  data['storeType'] = entity.storeType;
  data['shelfName'] = entity.shelfName;
  data['overdueTime'] = entity.overdueTime;
  data['overdueEffectTime'] = entity.overdueEffectTime;
  data['inboundImageUrl'] = entity.inboundImageUrl;
  data['outboundImageUrl'] = entity.outboundImageUrl;
  data['inboundUserName'] = entity.inboundUserName;
  data['inboundUserMobile'] = entity.inboundUserMobile;
  data['inboundUserFee'] = entity.inboundUserFee;
  data['hasSign'] = entity.hasSign;
  data['signDeliveryStatus'] = entity.signDeliveryStatus;
  data['hasReturn'] = entity.hasReturn;
  data['returnDeliveryStatus'] = entity.returnDeliveryStatus;
  data['hasDp'] = entity.hasDp;
  data['dpStatus'] = entity.dpStatus;
  data['dpMsg'] = entity.dpMsg;
  data['yzAccountId'] = entity.yzAccountId;
  return data;
}

extension PackageViewEntityExtension on PackageViewEntity {
  PackageViewEntity copyWith({
    String? id,
    String? waybillNo,
    String? cabinetLocationCode,
    String? cabinetLocationName,
    String? waybillOrderId,
    String? brandCode,
    String? brandName,
    String? orderId,
    String? receiverName,
    String? receiverMobile,
    String? keepEffectTime,
    String? cabinetBoxLabel,
    String? inboundTime,
    String? createTime,
    int? orderType,
    int? outboundType,
    String? outboundTime,
    String? checkCode,
    int? status,
    int? messageSmsStatus,
    int? messageWxStatus,
    int? price,
    int? messageType,
    int? cabinetType,
    String? cabinetName,
    String? cabinetBoxId,
    bool? keepEffect,
    int? hasOutbound,
    int? inboundDeliveryStatus,
    int? storeType,
    String? shelfName,
    String? overdueTime,
    String? overdueEffectTime,
    String? inboundImageUrl,
    String? outboundImageUrl,
    String? inboundUserName,
    String? inboundUserMobile,
    int? inboundUserFee,
    int? hasSign,
    int? signDeliveryStatus,
    int? hasReturn,
    int? returnDeliveryStatus,
    int? hasDp,
    int? dpStatus,
    String? dpMsg,
    String? yzAccountId,
  }) {
    return PackageViewEntity()
      ..id = id ?? this.id
      ..waybillNo = waybillNo ?? this.waybillNo
      ..cabinetLocationCode = cabinetLocationCode ?? this.cabinetLocationCode
      ..cabinetLocationName = cabinetLocationName ?? this.cabinetLocationName
      ..waybillOrderId = waybillOrderId ?? this.waybillOrderId
      ..brandCode = brandCode ?? this.brandCode
      ..brandName = brandName ?? this.brandName
      ..orderId = orderId ?? this.orderId
      ..receiverName = receiverName ?? this.receiverName
      ..receiverMobile = receiverMobile ?? this.receiverMobile
      ..keepEffectTime = keepEffectTime ?? this.keepEffectTime
      ..cabinetBoxLabel = cabinetBoxLabel ?? this.cabinetBoxLabel
      ..inboundTime = inboundTime ?? this.inboundTime
      ..createTime = createTime ?? this.createTime
      ..orderType = orderType ?? this.orderType
      ..outboundType = outboundType ?? this.outboundType
      ..outboundTime = outboundTime ?? this.outboundTime
      ..checkCode = checkCode ?? this.checkCode
      ..status = status ?? this.status
      ..messageSmsStatus = messageSmsStatus ?? this.messageSmsStatus
      ..messageWxStatus = messageWxStatus ?? this.messageWxStatus
      ..price = price ?? this.price
      ..messageType = messageType ?? this.messageType
      ..cabinetType = cabinetType ?? this.cabinetType
      ..cabinetName = cabinetName ?? this.cabinetName
      ..cabinetBoxId = cabinetBoxId ?? this.cabinetBoxId
      ..keepEffect = keepEffect ?? this.keepEffect
      ..hasOutbound = hasOutbound ?? this.hasOutbound
      ..inboundDeliveryStatus = inboundDeliveryStatus ?? this.inboundDeliveryStatus
      ..storeType = storeType ?? this.storeType
      ..shelfName = shelfName ?? this.shelfName
      ..overdueTime = overdueTime ?? this.overdueTime
      ..overdueEffectTime = overdueEffectTime ?? this.overdueEffectTime
      ..inboundImageUrl = inboundImageUrl ?? this.inboundImageUrl
      ..outboundImageUrl = outboundImageUrl ?? this.outboundImageUrl
      ..inboundUserName = inboundUserName ?? this.inboundUserName
      ..inboundUserMobile = inboundUserMobile ?? this.inboundUserMobile
      ..inboundUserFee = inboundUserFee ?? this.inboundUserFee
      ..hasSign = hasSign ?? this.hasSign
      ..signDeliveryStatus = signDeliveryStatus ?? this.signDeliveryStatus
      ..hasReturn = hasReturn ?? this.hasReturn
      ..returnDeliveryStatus = returnDeliveryStatus ?? this.returnDeliveryStatus
      ..hasDp = hasDp ?? this.hasDp
      ..dpStatus = dpStatus ?? this.dpStatus
      ..dpMsg = dpMsg ?? this.dpMsg
      ..yzAccountId = yzAccountId ?? this.yzAccountId;
  }
}