import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_location_entity.dart';

CabinetLocationEntity $CabinetLocationEntityFromJson(Map<String, dynamic> json) {
  final CabinetLocationEntity cabinetLocationEntity = CabinetLocationEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    cabinetLocationEntity.id = id;
  }
  final String? siteId = jsonConvert.convert<String>(json['siteId']);
  if (siteId != null) {
    cabinetLocationEntity.siteId = siteId;
  }
  final String? code = jsonConvert.convert<String>(json['code']);
  if (code != null) {
    cabinetLocationEntity.code = code;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    cabinetLocationEntity.name = name;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    cabinetLocationEntity.type = type;
  }
  final String? linkman = jsonConvert.convert<String>(json['linkman']);
  if (linkman != null) {
    cabinetLocationEntity.linkman = linkman;
  }
  final String? mobile = jsonConvert.convert<String>(json['mobile']);
  if (mobile != null) {
    cabinetLocationEntity.mobile = mobile;
  }
  final String? address = jsonConvert.convert<String>(json['address']);
  if (address != null) {
    cabinetLocationEntity.address = address;
  }
  final String? smsPrice = jsonConvert.convert<String>(json['smsPrice']);
  if (smsPrice != null) {
    cabinetLocationEntity.smsPrice = smsPrice;
  }
  final String? wxPrice = jsonConvert.convert<String>(json['wxPrice']);
  if (wxPrice != null) {
    cabinetLocationEntity.wxPrice = wxPrice;
  }
  final int? noticeType = jsonConvert.convert<int>(json['noticeType']);
  if (noticeType != null) {
    cabinetLocationEntity.noticeType = noticeType;
  }
  final String? cabinetType = jsonConvert.convert<String>(json['cabinetType']);
  if (cabinetType != null) {
    cabinetLocationEntity.cabinetType = cabinetType;
  }
  final String? servicePhone = jsonConvert.convert<String>(json['servicePhone']);
  if (servicePhone != null) {
    cabinetLocationEntity.servicePhone = servicePhone;
  }
  final String? provinceCode = jsonConvert.convert<String>(json['provinceCode']);
  if (provinceCode != null) {
    cabinetLocationEntity.provinceCode = provinceCode;
  }
  final String? provinceName = jsonConvert.convert<String>(json['provinceName']);
  if (provinceName != null) {
    cabinetLocationEntity.provinceName = provinceName;
  }
  final String? areaCode = jsonConvert.convert<String>(json['areaCode']);
  if (areaCode != null) {
    cabinetLocationEntity.areaCode = areaCode;
  }
  final String? areaName = jsonConvert.convert<String>(json['areaName']);
  if (areaName != null) {
    cabinetLocationEntity.areaName = areaName;
  }
  final String? cityName = jsonConvert.convert<String>(json['cityName']);
  if (cityName != null) {
    cabinetLocationEntity.cityName = cityName;
  }
  final String? cityCode = jsonConvert.convert<String>(json['cityCode']);
  if (cityCode != null) {
    cabinetLocationEntity.cityCode = cityCode;
  }
  final String? longitude = jsonConvert.convert<String>(json['longitude']);
  if (longitude != null) {
    cabinetLocationEntity.longitude = longitude;
  }
  final String? latitude = jsonConvert.convert<String>(json['latitude']);
  if (latitude != null) {
    cabinetLocationEntity.latitude = latitude;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    cabinetLocationEntity.status = status;
  }
  final String? applyTime = jsonConvert.convert<String>(json['applyTime']);
  if (applyTime != null) {
    cabinetLocationEntity.applyTime = applyTime;
  }
  final int? switchRent = jsonConvert.convert<int>(json['switchRent']);
  if (switchRent != null) {
    cabinetLocationEntity.switchRent = switchRent;
  }
  final int? boxAvailableNum = jsonConvert.convert<int>(json['boxAvailableNum']);
  if (boxAvailableNum != null) {
    cabinetLocationEntity.boxAvailableNum = boxAvailableNum;
  }
  final int? boxEmptyNum = jsonConvert.convert<int>(json['boxEmptyNum']);
  if (boxEmptyNum != null) {
    cabinetLocationEntity.boxEmptyNum = boxEmptyNum;
  }
  final String? cabinetLocationId = jsonConvert.convert<String>(json['cabinetLocationId']);
  if (cabinetLocationId != null) {
    cabinetLocationEntity.cabinetLocationId = cabinetLocationId;
  }
  final bool? inCommonUse = jsonConvert.convert<bool>(json['inCommonUse']);
  if (inCommonUse != null) {
    cabinetLocationEntity.inCommonUse = inCommonUse;
  }
  final bool? isCollect = jsonConvert.convert<bool>(json['isCollect']);
  if (isCollect != null) {
    cabinetLocationEntity.isCollect = isCollect;
  }
  return cabinetLocationEntity;
}

Map<String, dynamic> $CabinetLocationEntityToJson(CabinetLocationEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['siteId'] = entity.siteId;
  data['code'] = entity.code;
  data['name'] = entity.name;
  data['type'] = entity.type;
  data['linkman'] = entity.linkman;
  data['mobile'] = entity.mobile;
  data['address'] = entity.address;
  data['smsPrice'] = entity.smsPrice;
  data['wxPrice'] = entity.wxPrice;
  data['noticeType'] = entity.noticeType;
  data['cabinetType'] = entity.cabinetType;
  data['servicePhone'] = entity.servicePhone;
  data['provinceCode'] = entity.provinceCode;
  data['provinceName'] = entity.provinceName;
  data['areaCode'] = entity.areaCode;
  data['areaName'] = entity.areaName;
  data['cityName'] = entity.cityName;
  data['cityCode'] = entity.cityCode;
  data['longitude'] = entity.longitude;
  data['latitude'] = entity.latitude;
  data['status'] = entity.status;
  data['applyTime'] = entity.applyTime;
  data['switchRent'] = entity.switchRent;
  data['boxAvailableNum'] = entity.boxAvailableNum;
  data['boxEmptyNum'] = entity.boxEmptyNum;
  data['cabinetLocationId'] = entity.cabinetLocationId;
  data['inCommonUse'] = entity.inCommonUse;
  data['isCollect'] = entity.isCollect;
  return data;
}

extension CabinetLocationEntityExtension on CabinetLocationEntity {
  CabinetLocationEntity copyWith({
    String? id,
    String? siteId,
    String? code,
    String? name,
    int? type,
    String? linkman,
    String? mobile,
    String? address,
    String? smsPrice,
    String? wxPrice,
    int? noticeType,
    String? cabinetType,
    String? servicePhone,
    String? provinceCode,
    String? provinceName,
    String? areaCode,
    String? areaName,
    String? cityName,
    String? cityCode,
    String? longitude,
    String? latitude,
    int? status,
    String? applyTime,
    int? switchRent,
    int? boxAvailableNum,
    int? boxEmptyNum,
    String? cabinetLocationId,
    bool? inCommonUse,
    bool? isCollect,
  }) {
    return CabinetLocationEntity()
      ..id = id ?? this.id
      ..siteId = siteId ?? this.siteId
      ..code = code ?? this.code
      ..name = name ?? this.name
      ..type = type ?? this.type
      ..linkman = linkman ?? this.linkman
      ..mobile = mobile ?? this.mobile
      ..address = address ?? this.address
      ..smsPrice = smsPrice ?? this.smsPrice
      ..wxPrice = wxPrice ?? this.wxPrice
      ..noticeType = noticeType ?? this.noticeType
      ..cabinetType = cabinetType ?? this.cabinetType
      ..servicePhone = servicePhone ?? this.servicePhone
      ..provinceCode = provinceCode ?? this.provinceCode
      ..provinceName = provinceName ?? this.provinceName
      ..areaCode = areaCode ?? this.areaCode
      ..areaName = areaName ?? this.areaName
      ..cityName = cityName ?? this.cityName
      ..cityCode = cityCode ?? this.cityCode
      ..longitude = longitude ?? this.longitude
      ..latitude = latitude ?? this.latitude
      ..status = status ?? this.status
      ..applyTime = applyTime ?? this.applyTime
      ..switchRent = switchRent ?? this.switchRent
      ..boxAvailableNum = boxAvailableNum ?? this.boxAvailableNum
      ..boxEmptyNum = boxEmptyNum ?? this.boxEmptyNum
      ..cabinetLocationId = cabinetLocationId ?? this.cabinetLocationId
      ..inCommonUse = inCommonUse ?? this.inCommonUse
      ..isCollect = isCollect ?? this.isCollect;
  }
}