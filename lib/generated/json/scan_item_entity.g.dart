import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/scan_item_entity.dart';

ScanItemEntity $ScanItemEntityFromJson(Map<String, dynamic> json) {
  final ScanItemEntity scanItemEntity = ScanItemEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    scanItemEntity.id = id;
  }
  final String? orderNo = jsonConvert.convert<String>(json['orderNo']);
  if (orderNo != null) {
    scanItemEntity.orderNo = orderNo;
  }
  final String? orderType = jsonConvert.convert<String>(json['orderType']);
  if (orderType != null) {
    scanItemEntity.orderType = orderType;
  }
  final String? bizNo = jsonConvert.convert<String>(json['bizNo']);
  if (bizNo != null) {
    scanItemEntity.bizNo = bizNo;
  }
  final String? channelId = jsonConvert.convert<String>(json['channelId']);
  if (channelId != null) {
    scanItemEntity.channelId = channelId;
  }
  final String? channelName = jsonConvert.convert<String>(json['channelName']);
  if (channelName != null) {
    scanItemEntity.channelName = channelName;
  }
  final String? siteId = jsonConvert.convert<String>(json['siteId']);
  if (siteId != null) {
    scanItemEntity.siteId = siteId;
  }
  final String? siteName = jsonConvert.convert<String>(json['siteName']);
  if (siteName != null) {
    scanItemEntity.siteName = siteName;
  }
  final String? cabinetLocationId = jsonConvert.convert<String>(json['cabinetLocationId']);
  if (cabinetLocationId != null) {
    scanItemEntity.cabinetLocationId = cabinetLocationId;
  }
  final String? cabinetLocationName = jsonConvert.convert<String>(json['cabinetLocationName']);
  if (cabinetLocationName != null) {
    scanItemEntity.cabinetLocationName = cabinetLocationName;
  }
  final String? cabinetName = jsonConvert.convert<String>(json['cabinetName']);
  if (cabinetName != null) {
    scanItemEntity.cabinetName = cabinetName;
  }
  final String? courierId = jsonConvert.convert<String>(json['courierId']);
  if (courierId != null) {
    scanItemEntity.courierId = courierId;
  }
  final String? cabinetLocationCode = jsonConvert.convert<String>(json['cabinetLocationCode']);
  if (cabinetLocationCode != null) {
    scanItemEntity.cabinetLocationCode = cabinetLocationCode;
  }
  final String? cabinetId = jsonConvert.convert<String>(json['cabinetId']);
  if (cabinetId != null) {
    scanItemEntity.cabinetId = cabinetId;
  }
  final String? cabinetCode = jsonConvert.convert<String>(json['cabinetCode']);
  if (cabinetCode != null) {
    scanItemEntity.cabinetCode = cabinetCode;
  }
  final String? cabinetNo = jsonConvert.convert<String>(json['cabinetNo']);
  if (cabinetNo != null) {
    scanItemEntity.cabinetNo = cabinetNo;
  }
  final String? cabinetBoxPcbNo = jsonConvert.convert<String>(json['cabinetBoxPcbNo']);
  if (cabinetBoxPcbNo != null) {
    scanItemEntity.cabinetBoxPcbNo = cabinetBoxPcbNo;
  }
  final String? cabinetBoxId = jsonConvert.convert<String>(json['cabinetBoxId']);
  if (cabinetBoxId != null) {
    scanItemEntity.cabinetBoxId = cabinetBoxId;
  }
  final String? cabinetBoxLabel = jsonConvert.convert<String>(json['cabinetBoxLabel']);
  if (cabinetBoxLabel != null) {
    scanItemEntity.cabinetBoxLabel = cabinetBoxLabel;
  }
  final String? waybillNo = jsonConvert.convert<String>(json['waybillNo']);
  if (waybillNo != null) {
    scanItemEntity.waybillNo = waybillNo;
  }
  final String? brandCode = jsonConvert.convert<String>(json['brandCode']);
  if (brandCode != null) {
    scanItemEntity.brandCode = brandCode;
  }
  final String? brandName = jsonConvert.convert<String>(json['brandName']);
  if (brandName != null) {
    scanItemEntity.brandName = brandName;
  }
  final String? boxLabel = jsonConvert.convert<String>(json['boxLabel']);
  if (boxLabel != null) {
    scanItemEntity.boxLabel = boxLabel;
  }
  final String? receiverName = jsonConvert.convert<String>(json['receiverName']);
  if (receiverName != null) {
    scanItemEntity.receiverName = receiverName;
  }
  final String? receiverMobile = jsonConvert.convert<String>(json['receiverMobile']);
  if (receiverMobile != null) {
    scanItemEntity.receiverMobile = receiverMobile;
  }
  final String? receiverMobileLast4 = jsonConvert.convert<String>(json['receiverMobileLast4']);
  if (receiverMobileLast4 != null) {
    scanItemEntity.receiverMobileLast4 = receiverMobileLast4;
  }
  final bool? normalWaybill = jsonConvert.convert<bool>(json['normalWaybill']);
  if (normalWaybill != null) {
    scanItemEntity.normalWaybill = normalWaybill;
  }
  final int? sensitiveConsumer = jsonConvert.convert<int>(json['sensitiveConsumer']);
  if (sensitiveConsumer != null) {
    scanItemEntity.sensitiveConsumer = sensitiveConsumer;
  }
  final int? newCustomer = jsonConvert.convert<int>(json['newCustomer']);
  if (newCustomer != null) {
    scanItemEntity.newCustomer = newCustomer;
  }
  final int? boxType = jsonConvert.convert<int>(json['boxType']);
  if (boxType != null) {
    scanItemEntity.boxType = boxType;
  }
  final int? needCallCustomer = jsonConvert.convert<int>(json['needCallCustomer']);
  if (needCallCustomer != null) {
    scanItemEntity.needCallCustomer = needCallCustomer;
  }
  final String? shortNo = jsonConvert.convert<String>(json['shortNo']);
  if (shortNo != null) {
    scanItemEntity.shortNo = shortNo;
  }
  final String? uno = jsonConvert.convert<String>(json['uno']);
  if (uno != null) {
    scanItemEntity.uno = uno;
  }
  final String? cod = jsonConvert.convert<String>(json['cod']);
  if (cod != null) {
    scanItemEntity.cod = cod;
  }
  final String? inboundUserCode = jsonConvert.convert<String>(json['inboundUserCode']);
  if (inboundUserCode != null) {
    scanItemEntity.inboundUserCode = inboundUserCode;
  }
  final String? inboundUserName = jsonConvert.convert<String>(json['inboundUserName']);
  if (inboundUserName != null) {
    scanItemEntity.inboundUserName = inboundUserName;
  }
  final String? inboundPhotoUrl = jsonConvert.convert<String>(json['inboundPhotoUrl']);
  if (inboundPhotoUrl != null) {
    scanItemEntity.inboundPhotoUrl = inboundPhotoUrl;
  }
  final String? inboundDate = jsonConvert.convert<String>(json['inboundDate']);
  if (inboundDate != null) {
    scanItemEntity.inboundDate = inboundDate;
  }
  final String? inboundYm = jsonConvert.convert<String>(json['inboundYm']);
  if (inboundYm != null) {
    scanItemEntity.inboundYm = inboundYm;
  }
  final String? inboundYmd = jsonConvert.convert<String>(json['inboundYmd']);
  if (inboundYmd != null) {
    scanItemEntity.inboundYmd = inboundYmd;
  }
  final String? inboundType = jsonConvert.convert<String>(json['inboundType']);
  if (inboundType != null) {
    scanItemEntity.inboundType = inboundType;
  }
  final String? keepEffectTime = jsonConvert.convert<String>(json['keepEffectTime']);
  if (keepEffectTime != null) {
    scanItemEntity.keepEffectTime = keepEffectTime;
  }
  final String? inboundDeliveryReceiptDate = jsonConvert.convert<String>(json['inboundDeliveryReceiptDate']);
  if (inboundDeliveryReceiptDate != null) {
    scanItemEntity.inboundDeliveryReceiptDate = inboundDeliveryReceiptDate;
  }
  final String? inboundDeliveryState = jsonConvert.convert<String>(json['inboundDeliveryState']);
  if (inboundDeliveryState != null) {
    scanItemEntity.inboundDeliveryState = inboundDeliveryState;
  }
  final String? inboundDeliveryYmd = jsonConvert.convert<String>(json['inboundDeliveryYmd']);
  if (inboundDeliveryYmd != null) {
    scanItemEntity.inboundDeliveryYmd = inboundDeliveryYmd;
  }
  final String? checkCode = jsonConvert.convert<String>(json['checkCode']);
  if (checkCode != null) {
    scanItemEntity.checkCode = checkCode;
  }
  final String? messageType = jsonConvert.convert<String>(json['messageType']);
  if (messageType != null) {
    scanItemEntity.messageType = messageType;
  }
  final String? messageTplId = jsonConvert.convert<String>(json['messageTplId']);
  if (messageTplId != null) {
    scanItemEntity.messageTplId = messageTplId;
  }
  final String? messageStatus = jsonConvert.convert<String>(json['messageStatus']);
  if (messageStatus != null) {
    scanItemEntity.messageStatus = messageStatus;
  }
  final String? messageDeliveryTime = jsonConvert.convert<String>(json['messageDeliveryTime']);
  if (messageDeliveryTime != null) {
    scanItemEntity.messageDeliveryTime = messageDeliveryTime;
  }
  final String? messageReceiptTime = jsonConvert.convert<String>(json['messageReceiptTime']);
  if (messageReceiptTime != null) {
    scanItemEntity.messageReceiptTime = messageReceiptTime;
  }
  final String? messageReceiptContent = jsonConvert.convert<String>(json['messageReceiptContent']);
  if (messageReceiptContent != null) {
    scanItemEntity.messageReceiptContent = messageReceiptContent;
  }
  final String? inboundUserId = jsonConvert.convert<String>(json['inboundUserId']);
  if (inboundUserId != null) {
    scanItemEntity.inboundUserId = inboundUserId;
  }
  final String? inboundTime = jsonConvert.convert<String>(json['inboundTime']);
  if (inboundTime != null) {
    scanItemEntity.inboundTime = inboundTime;
  }
  final String? outboundTime = jsonConvert.convert<String>(json['outboundTime']);
  if (outboundTime != null) {
    scanItemEntity.outboundTime = outboundTime;
  }
  final String? outboundImageUrl = jsonConvert.convert<String>(json['outboundImageUrl']);
  if (outboundImageUrl != null) {
    scanItemEntity.outboundImageUrl = outboundImageUrl;
  }
  final String? serviceDuration = jsonConvert.convert<String>(json['serviceDuration']);
  if (serviceDuration != null) {
    scanItemEntity.serviceDuration = serviceDuration;
  }
  final String? isOutbound = jsonConvert.convert<String>(json['isOutbound']);
  if (isOutbound != null) {
    scanItemEntity.isOutbound = isOutbound;
  }
  final String? outboundBatchNo = jsonConvert.convert<String>(json['outboundBatchNo']);
  if (outboundBatchNo != null) {
    scanItemEntity.outboundBatchNo = outboundBatchNo;
  }
  final String? outboundUserCode = jsonConvert.convert<String>(json['outboundUserCode']);
  if (outboundUserCode != null) {
    scanItemEntity.outboundUserCode = outboundUserCode;
  }
  final String? outboundUserName = jsonConvert.convert<String>(json['outboundUserName']);
  if (outboundUserName != null) {
    scanItemEntity.outboundUserName = outboundUserName;
  }
  final String? outboundDate = jsonConvert.convert<String>(json['outboundDate']);
  if (outboundDate != null) {
    scanItemEntity.outboundDate = outboundDate;
  }
  final String? outboundPhotoUrl = jsonConvert.convert<String>(json['outboundPhotoUrl']);
  if (outboundPhotoUrl != null) {
    scanItemEntity.outboundPhotoUrl = outboundPhotoUrl;
  }
  final String? outboundYm = jsonConvert.convert<String>(json['outboundYm']);
  if (outboundYm != null) {
    scanItemEntity.outboundYm = outboundYm;
  }
  final String? outboundYmd = jsonConvert.convert<String>(json['outboundYmd']);
  if (outboundYmd != null) {
    scanItemEntity.outboundYmd = outboundYmd;
  }
  final String? isSign = jsonConvert.convert<String>(json['isSign']);
  if (isSign != null) {
    scanItemEntity.isSign = isSign;
  }
  final String? signDeliveryReceiptDate = jsonConvert.convert<String>(json['signDeliveryReceiptDate']);
  if (signDeliveryReceiptDate != null) {
    scanItemEntity.signDeliveryReceiptDate = signDeliveryReceiptDate;
  }
  final String? signDeliveryState = jsonConvert.convert<String>(json['signDeliveryState']);
  if (signDeliveryState != null) {
    scanItemEntity.signDeliveryState = signDeliveryState;
  }
  final String? signDeliveryYmd = jsonConvert.convert<String>(json['signDeliveryYmd']);
  if (signDeliveryYmd != null) {
    scanItemEntity.signDeliveryYmd = signDeliveryYmd;
  }
  final String? isBack = jsonConvert.convert<String>(json['isBack']);
  if (isBack != null) {
    scanItemEntity.isBack = isBack;
  }
  final String? backReason = jsonConvert.convert<String>(json['backReason']);
  if (backReason != null) {
    scanItemEntity.backReason = backReason;
  }
  final String? backUserCode = jsonConvert.convert<String>(json['backUserCode']);
  if (backUserCode != null) {
    scanItemEntity.backUserCode = backUserCode;
  }
  final String? backUserName = jsonConvert.convert<String>(json['backUserName']);
  if (backUserName != null) {
    scanItemEntity.backUserName = backUserName;
  }
  final String? backDate = jsonConvert.convert<String>(json['backDate']);
  if (backDate != null) {
    scanItemEntity.backDate = backDate;
  }
  final String? backYm = jsonConvert.convert<String>(json['backYm']);
  if (backYm != null) {
    scanItemEntity.backYm = backYm;
  }
  final String? backYmd = jsonConvert.convert<String>(json['backYmd']);
  if (backYmd != null) {
    scanItemEntity.backYmd = backYmd;
  }
  final String? backDeliveryReceiptDate = jsonConvert.convert<String>(json['backDeliveryReceiptDate']);
  if (backDeliveryReceiptDate != null) {
    scanItemEntity.backDeliveryReceiptDate = backDeliveryReceiptDate;
  }
  final String? backDeliveryState = jsonConvert.convert<String>(json['backDeliveryState']);
  if (backDeliveryState != null) {
    scanItemEntity.backDeliveryState = backDeliveryState;
  }
  final String? backDeliveryYmd = jsonConvert.convert<String>(json['backDeliveryYmd']);
  if (backDeliveryYmd != null) {
    scanItemEntity.backDeliveryYmd = backDeliveryYmd;
  }
  final String? pjDeliveryReceiptDate = jsonConvert.convert<String>(json['pjDeliveryReceiptDate']);
  if (pjDeliveryReceiptDate != null) {
    scanItemEntity.pjDeliveryReceiptDate = pjDeliveryReceiptDate;
  }
  final String? pjDeliveryState = jsonConvert.convert<String>(json['pjDeliveryState']);
  if (pjDeliveryState != null) {
    scanItemEntity.pjDeliveryState = pjDeliveryState;
  }
  final String? isKeep = jsonConvert.convert<String>(json['isKeep']);
  if (isKeep != null) {
    scanItemEntity.isKeep = isKeep;
  }
  final String? keepDayDesc = jsonConvert.convert<String>(json['keepDayDesc']);
  if (keepDayDesc != null) {
    scanItemEntity.keepDayDesc = keepDayDesc;
  }
  final String? keepEffectDate = jsonConvert.convert<String>(json['keepEffectDate']);
  if (keepEffectDate != null) {
    scanItemEntity.keepEffectDate = keepEffectDate;
  }
  final String? smsState = jsonConvert.convert<String>(json['smsState']);
  if (smsState != null) {
    scanItemEntity.smsState = smsState;
  }
  final String? isSendSms = jsonConvert.convert<String>(json['isSendSms']);
  if (isSendSms != null) {
    scanItemEntity.isSendSms = isSendSms;
  }
  final String? smsTplCode = jsonConvert.convert<String>(json['smsTplCode']);
  if (smsTplCode != null) {
    scanItemEntity.smsTplCode = smsTplCode;
  }
  final String? sendSmsDate = jsonConvert.convert<String>(json['sendSmsDate']);
  if (sendSmsDate != null) {
    scanItemEntity.sendSmsDate = sendSmsDate;
  }
  final String? createBy = jsonConvert.convert<String>(json['createBy']);
  if (createBy != null) {
    scanItemEntity.createBy = createBy;
  }
  final String? createDate = jsonConvert.convert<String>(json['createDate']);
  if (createDate != null) {
    scanItemEntity.createDate = createDate;
  }
  final String? updateBy = jsonConvert.convert<String>(json['updateBy']);
  if (updateBy != null) {
    scanItemEntity.updateBy = updateBy;
  }
  final String? updateDate = jsonConvert.convert<String>(json['updateDate']);
  if (updateDate != null) {
    scanItemEntity.updateDate = updateDate;
  }
  final String? remarks = jsonConvert.convert<String>(json['remarks']);
  if (remarks != null) {
    scanItemEntity.remarks = remarks;
  }
  final String? scanId = jsonConvert.convert<String>(json['scanId']);
  if (scanId != null) {
    scanItemEntity.scanId = scanId;
  }
  final String? type = jsonConvert.convert<String>(json['type']);
  if (type != null) {
    scanItemEntity.type = type;
  }
  final String? status = jsonConvert.convert<String>(json['status']);
  if (status != null) {
    scanItemEntity.status = status;
  }
  final String? engine = jsonConvert.convert<String>(json['engine']);
  if (engine != null) {
    scanItemEntity.engine = engine;
  }
  final String? rule = jsonConvert.convert<String>(json['rule']);
  if (rule != null) {
    scanItemEntity.rule = rule;
  }
  final String? day = jsonConvert.convert<String>(json['day']);
  if (day != null) {
    scanItemEntity.day = day;
  }
  final String? shelfNo = jsonConvert.convert<String>(json['shelfNo']);
  if (shelfNo != null) {
    scanItemEntity.shelfNo = shelfNo;
  }
  final String? no = jsonConvert.convert<String>(json['no']);
  if (no != null) {
    scanItemEntity.no = no;
  }
  final String? ymd = jsonConvert.convert<String>(json['ymd']);
  if (ymd != null) {
    scanItemEntity.ymd = ymd;
  }
  final String? isHb = jsonConvert.convert<String>(json['isHb']);
  if (isHb != null) {
    scanItemEntity.isHb = isHb;
  }
  final String? isIntercept = jsonConvert.convert<String>(json['isIntercept']);
  if (isIntercept != null) {
    scanItemEntity.isIntercept = isIntercept;
  }
  final String? msg = jsonConvert.convert<String>(json['msg']);
  if (msg != null) {
    scanItemEntity.msg = msg;
  }
  final String? virtualNumber = jsonConvert.convert<String>(json['virtualNumber']);
  if (virtualNumber != null) {
    scanItemEntity.virtualNumber = virtualNumber;
  }
  final String? platform = jsonConvert.convert<String>(json['platform']);
  if (platform != null) {
    scanItemEntity.platform = platform;
  }
  final String? phone = jsonConvert.convert<String>(json['phone']);
  if (phone != null) {
    scanItemEntity.phone = phone;
  }
  final String? secretWaybill = jsonConvert.convert<String>(json['secretWaybill']);
  if (secretWaybill != null) {
    scanItemEntity.secretWaybill = secretWaybill;
  }
  final String? hasSubstituteSms = jsonConvert.convert<String>(json['hasSubstituteSms']);
  if (hasSubstituteSms != null) {
    scanItemEntity.hasSubstituteSms = hasSubstituteSms;
  }
  final String? inboundImageUrl = jsonConvert.convert<String>(json['inboundImageUrl']);
  if (inboundImageUrl != null) {
    scanItemEntity.inboundImageUrl = inboundImageUrl;
  }
  final bool? addInCabinet = jsonConvert.convert<bool>(json['addInCabinet']);
  if (addInCabinet != null) {
    scanItemEntity.addInCabinet = addInCabinet;
  }
  final int? deliveryWaybill = jsonConvert.convert<int>(json['deliveryWaybill']);
  if (deliveryWaybill != null) {
    scanItemEntity.deliveryWaybill = deliveryWaybill;
  }
  return scanItemEntity;
}

Map<String, dynamic> $ScanItemEntityToJson(ScanItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['orderNo'] = entity.orderNo;
  data['orderType'] = entity.orderType;
  data['bizNo'] = entity.bizNo;
  data['channelId'] = entity.channelId;
  data['channelName'] = entity.channelName;
  data['siteId'] = entity.siteId;
  data['siteName'] = entity.siteName;
  data['cabinetLocationId'] = entity.cabinetLocationId;
  data['cabinetLocationName'] = entity.cabinetLocationName;
  data['cabinetName'] = entity.cabinetName;
  data['courierId'] = entity.courierId;
  data['cabinetLocationCode'] = entity.cabinetLocationCode;
  data['cabinetId'] = entity.cabinetId;
  data['cabinetCode'] = entity.cabinetCode;
  data['cabinetNo'] = entity.cabinetNo;
  data['cabinetBoxPcbNo'] = entity.cabinetBoxPcbNo;
  data['cabinetBoxId'] = entity.cabinetBoxId;
  data['cabinetBoxLabel'] = entity.cabinetBoxLabel;
  data['waybillNo'] = entity.waybillNo;
  data['brandCode'] = entity.brandCode;
  data['brandName'] = entity.brandName;
  data['boxLabel'] = entity.boxLabel;
  data['receiverName'] = entity.receiverName;
  data['receiverMobile'] = entity.receiverMobile;
  data['receiverMobileLast4'] = entity.receiverMobileLast4;
  data['normalWaybill'] = entity.normalWaybill;
  data['sensitiveConsumer'] = entity.sensitiveConsumer;
  data['newCustomer'] = entity.newCustomer;
  data['boxType'] = entity.boxType;
  data['needCallCustomer'] = entity.needCallCustomer;
  data['shortNo'] = entity.shortNo;
  data['uno'] = entity.uno;
  data['cod'] = entity.cod;
  data['inboundUserCode'] = entity.inboundUserCode;
  data['inboundUserName'] = entity.inboundUserName;
  data['inboundPhotoUrl'] = entity.inboundPhotoUrl;
  data['inboundDate'] = entity.inboundDate;
  data['inboundYm'] = entity.inboundYm;
  data['inboundYmd'] = entity.inboundYmd;
  data['inboundType'] = entity.inboundType;
  data['keepEffectTime'] = entity.keepEffectTime;
  data['inboundDeliveryReceiptDate'] = entity.inboundDeliveryReceiptDate;
  data['inboundDeliveryState'] = entity.inboundDeliveryState;
  data['inboundDeliveryYmd'] = entity.inboundDeliveryYmd;
  data['checkCode'] = entity.checkCode;
  data['messageType'] = entity.messageType;
  data['messageTplId'] = entity.messageTplId;
  data['messageStatus'] = entity.messageStatus;
  data['messageDeliveryTime'] = entity.messageDeliveryTime;
  data['messageReceiptTime'] = entity.messageReceiptTime;
  data['messageReceiptContent'] = entity.messageReceiptContent;
  data['inboundUserId'] = entity.inboundUserId;
  data['inboundTime'] = entity.inboundTime;
  data['outboundTime'] = entity.outboundTime;
  data['outboundImageUrl'] = entity.outboundImageUrl;
  data['serviceDuration'] = entity.serviceDuration;
  data['isOutbound'] = entity.isOutbound;
  data['outboundBatchNo'] = entity.outboundBatchNo;
  data['outboundUserCode'] = entity.outboundUserCode;
  data['outboundUserName'] = entity.outboundUserName;
  data['outboundDate'] = entity.outboundDate;
  data['outboundPhotoUrl'] = entity.outboundPhotoUrl;
  data['outboundYm'] = entity.outboundYm;
  data['outboundYmd'] = entity.outboundYmd;
  data['isSign'] = entity.isSign;
  data['signDeliveryReceiptDate'] = entity.signDeliveryReceiptDate;
  data['signDeliveryState'] = entity.signDeliveryState;
  data['signDeliveryYmd'] = entity.signDeliveryYmd;
  data['isBack'] = entity.isBack;
  data['backReason'] = entity.backReason;
  data['backUserCode'] = entity.backUserCode;
  data['backUserName'] = entity.backUserName;
  data['backDate'] = entity.backDate;
  data['backYm'] = entity.backYm;
  data['backYmd'] = entity.backYmd;
  data['backDeliveryReceiptDate'] = entity.backDeliveryReceiptDate;
  data['backDeliveryState'] = entity.backDeliveryState;
  data['backDeliveryYmd'] = entity.backDeliveryYmd;
  data['pjDeliveryReceiptDate'] = entity.pjDeliveryReceiptDate;
  data['pjDeliveryState'] = entity.pjDeliveryState;
  data['isKeep'] = entity.isKeep;
  data['keepDayDesc'] = entity.keepDayDesc;
  data['keepEffectDate'] = entity.keepEffectDate;
  data['smsState'] = entity.smsState;
  data['isSendSms'] = entity.isSendSms;
  data['smsTplCode'] = entity.smsTplCode;
  data['sendSmsDate'] = entity.sendSmsDate;
  data['createBy'] = entity.createBy;
  data['createDate'] = entity.createDate;
  data['updateBy'] = entity.updateBy;
  data['updateDate'] = entity.updateDate;
  data['remarks'] = entity.remarks;
  data['scanId'] = entity.scanId;
  data['type'] = entity.type;
  data['status'] = entity.status;
  data['engine'] = entity.engine;
  data['rule'] = entity.rule;
  data['day'] = entity.day;
  data['shelfNo'] = entity.shelfNo;
  data['no'] = entity.no;
  data['ymd'] = entity.ymd;
  data['isHb'] = entity.isHb;
  data['isIntercept'] = entity.isIntercept;
  data['msg'] = entity.msg;
  data['virtualNumber'] = entity.virtualNumber;
  data['platform'] = entity.platform;
  data['phone'] = entity.phone;
  data['secretWaybill'] = entity.secretWaybill;
  data['hasSubstituteSms'] = entity.hasSubstituteSms;
  data['inboundImageUrl'] = entity.inboundImageUrl;
  data['addInCabinet'] = entity.addInCabinet;
  data['deliveryWaybill'] = entity.deliveryWaybill;
  return data;
}

extension ScanItemEntityExtension on ScanItemEntity {
  ScanItemEntity copyWith({
    String? id,
    String? orderNo,
    String? orderType,
    String? bizNo,
    String? channelId,
    String? channelName,
    String? siteId,
    String? siteName,
    String? cabinetLocationId,
    String? cabinetLocationName,
    String? cabinetName,
    String? courierId,
    String? cabinetLocationCode,
    String? cabinetId,
    String? cabinetCode,
    String? cabinetNo,
    String? cabinetBoxPcbNo,
    String? cabinetBoxId,
    String? cabinetBoxLabel,
    String? waybillNo,
    String? brandCode,
    String? brandName,
    String? boxLabel,
    String? receiverName,
    String? receiverMobile,
    String? receiverMobileLast4,
    bool? normalWaybill,
    int? sensitiveConsumer,
    int? newCustomer,
    int? boxType,
    int? needCallCustomer,
    String? shortNo,
    String? uno,
    String? cod,
    String? inboundUserCode,
    String? inboundUserName,
    String? inboundPhotoUrl,
    String? inboundDate,
    String? inboundYm,
    String? inboundYmd,
    String? inboundType,
    String? keepEffectTime,
    String? inboundDeliveryReceiptDate,
    String? inboundDeliveryState,
    String? inboundDeliveryYmd,
    String? checkCode,
    String? messageType,
    String? messageTplId,
    String? messageStatus,
    String? messageDeliveryTime,
    String? messageReceiptTime,
    String? messageReceiptContent,
    String? inboundUserId,
    String? inboundTime,
    String? outboundTime,
    String? outboundImageUrl,
    String? serviceDuration,
    String? isOutbound,
    String? outboundBatchNo,
    String? outboundUserCode,
    String? outboundUserName,
    String? outboundDate,
    String? outboundPhotoUrl,
    String? outboundYm,
    String? outboundYmd,
    String? isSign,
    String? signDeliveryReceiptDate,
    String? signDeliveryState,
    String? signDeliveryYmd,
    String? isBack,
    String? backReason,
    String? backUserCode,
    String? backUserName,
    String? backDate,
    String? backYm,
    String? backYmd,
    String? backDeliveryReceiptDate,
    String? backDeliveryState,
    String? backDeliveryYmd,
    String? pjDeliveryReceiptDate,
    String? pjDeliveryState,
    String? isKeep,
    String? keepDayDesc,
    String? keepEffectDate,
    String? smsState,
    String? isSendSms,
    String? smsTplCode,
    String? sendSmsDate,
    String? createBy,
    String? createDate,
    String? updateBy,
    String? updateDate,
    String? remarks,
    String? scanId,
    String? type,
    String? status,
    String? engine,
    String? rule,
    String? day,
    String? shelfNo,
    String? no,
    String? ymd,
    String? isHb,
    String? isIntercept,
    String? msg,
    String? virtualNumber,
    String? platform,
    String? phone,
    String? secretWaybill,
    String? hasSubstituteSms,
    String? inboundImageUrl,
    bool? addInCabinet,
    int? deliveryWaybill,
  }) {
    return ScanItemEntity()
      ..id = id ?? this.id
      ..orderNo = orderNo ?? this.orderNo
      ..orderType = orderType ?? this.orderType
      ..bizNo = bizNo ?? this.bizNo
      ..channelId = channelId ?? this.channelId
      ..channelName = channelName ?? this.channelName
      ..siteId = siteId ?? this.siteId
      ..siteName = siteName ?? this.siteName
      ..cabinetLocationId = cabinetLocationId ?? this.cabinetLocationId
      ..cabinetLocationName = cabinetLocationName ?? this.cabinetLocationName
      ..cabinetName = cabinetName ?? this.cabinetName
      ..courierId = courierId ?? this.courierId
      ..cabinetLocationCode = cabinetLocationCode ?? this.cabinetLocationCode
      ..cabinetId = cabinetId ?? this.cabinetId
      ..cabinetCode = cabinetCode ?? this.cabinetCode
      ..cabinetNo = cabinetNo ?? this.cabinetNo
      ..cabinetBoxPcbNo = cabinetBoxPcbNo ?? this.cabinetBoxPcbNo
      ..cabinetBoxId = cabinetBoxId ?? this.cabinetBoxId
      ..cabinetBoxLabel = cabinetBoxLabel ?? this.cabinetBoxLabel
      ..waybillNo = waybillNo ?? this.waybillNo
      ..brandCode = brandCode ?? this.brandCode
      ..brandName = brandName ?? this.brandName
      ..boxLabel = boxLabel ?? this.boxLabel
      ..receiverName = receiverName ?? this.receiverName
      ..receiverMobile = receiverMobile ?? this.receiverMobile
      ..receiverMobileLast4 = receiverMobileLast4 ?? this.receiverMobileLast4
      ..normalWaybill = normalWaybill ?? this.normalWaybill
      ..sensitiveConsumer = sensitiveConsumer ?? this.sensitiveConsumer
      ..newCustomer = newCustomer ?? this.newCustomer
      ..boxType = boxType ?? this.boxType
      ..needCallCustomer = needCallCustomer ?? this.needCallCustomer
      ..shortNo = shortNo ?? this.shortNo
      ..uno = uno ?? this.uno
      ..cod = cod ?? this.cod
      ..inboundUserCode = inboundUserCode ?? this.inboundUserCode
      ..inboundUserName = inboundUserName ?? this.inboundUserName
      ..inboundPhotoUrl = inboundPhotoUrl ?? this.inboundPhotoUrl
      ..inboundDate = inboundDate ?? this.inboundDate
      ..inboundYm = inboundYm ?? this.inboundYm
      ..inboundYmd = inboundYmd ?? this.inboundYmd
      ..inboundType = inboundType ?? this.inboundType
      ..keepEffectTime = keepEffectTime ?? this.keepEffectTime
      ..inboundDeliveryReceiptDate = inboundDeliveryReceiptDate ?? this.inboundDeliveryReceiptDate
      ..inboundDeliveryState = inboundDeliveryState ?? this.inboundDeliveryState
      ..inboundDeliveryYmd = inboundDeliveryYmd ?? this.inboundDeliveryYmd
      ..checkCode = checkCode ?? this.checkCode
      ..messageType = messageType ?? this.messageType
      ..messageTplId = messageTplId ?? this.messageTplId
      ..messageStatus = messageStatus ?? this.messageStatus
      ..messageDeliveryTime = messageDeliveryTime ?? this.messageDeliveryTime
      ..messageReceiptTime = messageReceiptTime ?? this.messageReceiptTime
      ..messageReceiptContent = messageReceiptContent ?? this.messageReceiptContent
      ..inboundUserId = inboundUserId ?? this.inboundUserId
      ..inboundTime = inboundTime ?? this.inboundTime
      ..outboundTime = outboundTime ?? this.outboundTime
      ..outboundImageUrl = outboundImageUrl ?? this.outboundImageUrl
      ..serviceDuration = serviceDuration ?? this.serviceDuration
      ..isOutbound = isOutbound ?? this.isOutbound
      ..outboundBatchNo = outboundBatchNo ?? this.outboundBatchNo
      ..outboundUserCode = outboundUserCode ?? this.outboundUserCode
      ..outboundUserName = outboundUserName ?? this.outboundUserName
      ..outboundDate = outboundDate ?? this.outboundDate
      ..outboundPhotoUrl = outboundPhotoUrl ?? this.outboundPhotoUrl
      ..outboundYm = outboundYm ?? this.outboundYm
      ..outboundYmd = outboundYmd ?? this.outboundYmd
      ..isSign = isSign ?? this.isSign
      ..signDeliveryReceiptDate = signDeliveryReceiptDate ?? this.signDeliveryReceiptDate
      ..signDeliveryState = signDeliveryState ?? this.signDeliveryState
      ..signDeliveryYmd = signDeliveryYmd ?? this.signDeliveryYmd
      ..isBack = isBack ?? this.isBack
      ..backReason = backReason ?? this.backReason
      ..backUserCode = backUserCode ?? this.backUserCode
      ..backUserName = backUserName ?? this.backUserName
      ..backDate = backDate ?? this.backDate
      ..backYm = backYm ?? this.backYm
      ..backYmd = backYmd ?? this.backYmd
      ..backDeliveryReceiptDate = backDeliveryReceiptDate ?? this.backDeliveryReceiptDate
      ..backDeliveryState = backDeliveryState ?? this.backDeliveryState
      ..backDeliveryYmd = backDeliveryYmd ?? this.backDeliveryYmd
      ..pjDeliveryReceiptDate = pjDeliveryReceiptDate ?? this.pjDeliveryReceiptDate
      ..pjDeliveryState = pjDeliveryState ?? this.pjDeliveryState
      ..isKeep = isKeep ?? this.isKeep
      ..keepDayDesc = keepDayDesc ?? this.keepDayDesc
      ..keepEffectDate = keepEffectDate ?? this.keepEffectDate
      ..smsState = smsState ?? this.smsState
      ..isSendSms = isSendSms ?? this.isSendSms
      ..smsTplCode = smsTplCode ?? this.smsTplCode
      ..sendSmsDate = sendSmsDate ?? this.sendSmsDate
      ..createBy = createBy ?? this.createBy
      ..createDate = createDate ?? this.createDate
      ..updateBy = updateBy ?? this.updateBy
      ..updateDate = updateDate ?? this.updateDate
      ..remarks = remarks ?? this.remarks
      ..scanId = scanId ?? this.scanId
      ..type = type ?? this.type
      ..status = status ?? this.status
      ..engine = engine ?? this.engine
      ..rule = rule ?? this.rule
      ..day = day ?? this.day
      ..shelfNo = shelfNo ?? this.shelfNo
      ..no = no ?? this.no
      ..ymd = ymd ?? this.ymd
      ..isHb = isHb ?? this.isHb
      ..isIntercept = isIntercept ?? this.isIntercept
      ..msg = msg ?? this.msg
      ..virtualNumber = virtualNumber ?? this.virtualNumber
      ..platform = platform ?? this.platform
      ..phone = phone ?? this.phone
      ..secretWaybill = secretWaybill ?? this.secretWaybill
      ..hasSubstituteSms = hasSubstituteSms ?? this.hasSubstituteSms
      ..inboundImageUrl = inboundImageUrl ?? this.inboundImageUrl
      ..addInCabinet = addInCabinet ?? this.addInCabinet
      ..deliveryWaybill = deliveryWaybill ?? this.deliveryWaybill;
  }
}