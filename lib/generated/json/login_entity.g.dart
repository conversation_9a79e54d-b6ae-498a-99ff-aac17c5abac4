import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/login_entity.dart';

LoginEntity $LoginEntityFromJson(Map<String, dynamic> json) {
  final LoginEntity loginEntity = LoginEntity();
  final int? expires = jsonConvert.convert<int>(json['expires']);
  if (expires != null) {
    loginEntity.expires = expires;
  }
  final String? token = jsonConvert.convert<String>(json['token']);
  if (token != null) {
    loginEntity.token = token;
  }
  final String? tokenName = jsonConvert.convert<String>(json['tokenName']);
  if (tokenName != null) {
    loginEntity.tokenName = tokenName;
  }
  final String? tokenValue = jsonConvert.convert<String>(json['tokenValue']);
  if (tokenValue != null) {
    loginEntity.tokenValue = tokenValue;
  }
  final int? tokenTimeOut = jsonConvert.convert<int>(json['tokenTimeOut']);
  if (tokenTimeOut != null) {
    loginEntity.tokenTimeOut = tokenTimeOut;
  }
  return loginEntity;
}

Map<String, dynamic> $LoginEntityToJson(LoginEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['expires'] = entity.expires;
  data['token'] = entity.token;
  data['tokenName'] = entity.tokenName;
  data['tokenValue'] = entity.tokenValue;
  data['tokenTimeOut'] = entity.tokenTimeOut;
  return data;
}

extension LoginEntityExtension on LoginEntity {
  LoginEntity copyWith({
    int? expires,
    String? token,
    String? tokenName,
    String? tokenValue,
    int? tokenTimeOut,
  }) {
    return LoginEntity()
      ..expires = expires ?? this.expires
      ..token = token ?? this.token
      ..tokenName = tokenName ?? this.tokenName
      ..tokenValue = tokenValue ?? this.tokenValue
      ..tokenTimeOut = tokenTimeOut ?? this.tokenTimeOut;
  }
}