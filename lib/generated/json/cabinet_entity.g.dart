import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_entity.dart';

CabinetEntity $CabinetEntityFromJson(Map<String, dynamic> json) {
  final CabinetEntity cabinetEntity = CabinetEntity();
  final String? siteId = jsonConvert.convert<String>(json['siteId']);
  if (siteId != null) {
    cabinetEntity.siteId = siteId;
  }
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    cabinetEntity.id = id;
  }
  final String? code = jsonConvert.convert<String>(json['code']);
  if (code != null) {
    cabinetEntity.code = code;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    cabinetEntity.name = name;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    cabinetEntity.type = type;
  }
  final String? cabinetLocationId = jsonConvert.convert<String>(json['cabinetLocationId']);
  if (cabinetLocationId != null) {
    cabinetEntity.cabinetLocationId = cabinetLocationId;
  }
  final String? provinceName = jsonConvert.convert<String>(json['provinceName']);
  if (provinceName != null) {
    cabinetEntity.provinceName = provinceName;
  }
  final String? provinceCode = jsonConvert.convert<String>(json['provinceCode']);
  if (provinceCode != null) {
    cabinetEntity.provinceCode = provinceCode;
  }
  final String? areaName = jsonConvert.convert<String>(json['areaName']);
  if (areaName != null) {
    cabinetEntity.areaName = areaName;
  }
  final String? areaCode = jsonConvert.convert<String>(json['areaCode']);
  if (areaCode != null) {
    cabinetEntity.areaCode = areaCode;
  }
  final String? cityName = jsonConvert.convert<String>(json['cityName']);
  if (cityName != null) {
    cabinetEntity.cityName = cityName;
  }
  final String? cityCode = jsonConvert.convert<String>(json['cityCode']);
  if (cityCode != null) {
    cabinetEntity.cityCode = cityCode;
  }
  final String? address = jsonConvert.convert<String>(json['address']);
  if (address != null) {
    cabinetEntity.address = address;
  }
  final String? dispatchJson = jsonConvert.convert<String>(json['dispatchJson']);
  if (dispatchJson != null) {
    cabinetEntity.dispatchJson = dispatchJson;
  }
  final String? rentJson = jsonConvert.convert<String>(json['rentJson']);
  if (rentJson != null) {
    cabinetEntity.rentJson = rentJson;
  }
  final String? appointJson = jsonConvert.convert<String>(json['appointJson']);
  if (appointJson != null) {
    cabinetEntity.appointJson = appointJson;
  }
  final int? boxTotalNum = jsonConvert.convert<int>(json['boxTotalNum']);
  if (boxTotalNum != null) {
    cabinetEntity.boxTotalNum = boxTotalNum;
  }
  final int? boxEmptyNum = jsonConvert.convert<int>(json['boxEmptyNum']);
  if (boxEmptyNum != null) {
    cabinetEntity.boxEmptyNum = boxEmptyNum;
  }
  final int? boxAvailableNum = jsonConvert.convert<int>(json['boxAvailableNum']);
  if (boxAvailableNum != null) {
    cabinetEntity.boxAvailableNum = boxAvailableNum;
  }
  final int? superCount = jsonConvert.convert<int>(json['superCount']);
  if (superCount != null) {
    cabinetEntity.superCount = superCount;
  }
  final int? hugeCount = jsonConvert.convert<int>(json['hugeCount']);
  if (hugeCount != null) {
    cabinetEntity.hugeCount = hugeCount;
  }
  final int? largeCount = jsonConvert.convert<int>(json['largeCount']);
  if (largeCount != null) {
    cabinetEntity.largeCount = largeCount;
  }
  final int? mediumCount = jsonConvert.convert<int>(json['mediumCount']);
  if (mediumCount != null) {
    cabinetEntity.mediumCount = mediumCount;
  }
  final int? smallCount = jsonConvert.convert<int>(json['smallCount']);
  if (smallCount != null) {
    cabinetEntity.smallCount = smallCount;
  }
  final int? miniCount = jsonConvert.convert<int>(json['miniCount']);
  if (miniCount != null) {
    cabinetEntity.miniCount = miniCount;
  }
  final int? microCount = jsonConvert.convert<int>(json['microCount']);
  if (microCount != null) {
    cabinetEntity.microCount = microCount;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    cabinetEntity.status = status;
  }
  final int? hasUsed = jsonConvert.convert<int>(json['hasUsed']);
  if (hasUsed != null) {
    cabinetEntity.hasUsed = hasUsed;
  }
  final int? overdueOutboundDays = jsonConvert.convert<int>(json['overdueOutboundDays']);
  if (overdueOutboundDays != null) {
    cabinetEntity.overdueOutboundDays = overdueOutboundDays;
  }
  final double? distance = jsonConvert.convert<double>(json['distance']);
  if (distance != null) {
    cabinetEntity.distance = distance;
  }
  final double? latitude = jsonConvert.convert<double>(json['latitude']);
  if (latitude != null) {
    cabinetEntity.latitude = latitude;
  }
  final double? longitude = jsonConvert.convert<double>(json['longitude']);
  if (longitude != null) {
    cabinetEntity.longitude = longitude;
  }
  final int? hasCollected = jsonConvert.convert<int>(json['hasCollected']);
  if (hasCollected != null) {
    cabinetEntity.hasCollected = hasCollected;
  }
  final int? hasAddCabinet = jsonConvert.convert<int>(json['hasAddCabinet']);
  if (hasAddCabinet != null) {
    cabinetEntity.hasAddCabinet = hasAddCabinet;
  }
  final int? hasChangeBrand = jsonConvert.convert<int>(json['hasChangeBrand']);
  if (hasChangeBrand != null) {
    cabinetEntity.hasChangeBrand = hasChangeBrand;
  }
  final int? switchBatch = jsonConvert.convert<int>(json['switchBatch']);
  if (switchBatch != null) {
    cabinetEntity.switchBatch = switchBatch;
  }
  final int? switchBook = jsonConvert.convert<int>(json['switchBook']);
  if (switchBook != null) {
    cabinetEntity.switchBook = switchBook;
  }
  final String? bookJson = jsonConvert.convert<String>(json['bookJson']);
  if (bookJson != null) {
    cabinetEntity.bookJson = bookJson;
  }
  final int? switchRent = jsonConvert.convert<int>(json['switchRent']);
  if (switchRent != null) {
    cabinetEntity.switchRent = switchRent;
  }
  return cabinetEntity;
}

Map<String, dynamic> $CabinetEntityToJson(CabinetEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['siteId'] = entity.siteId;
  data['id'] = entity.id;
  data['code'] = entity.code;
  data['name'] = entity.name;
  data['type'] = entity.type;
  data['cabinetLocationId'] = entity.cabinetLocationId;
  data['provinceName'] = entity.provinceName;
  data['provinceCode'] = entity.provinceCode;
  data['areaName'] = entity.areaName;
  data['areaCode'] = entity.areaCode;
  data['cityName'] = entity.cityName;
  data['cityCode'] = entity.cityCode;
  data['address'] = entity.address;
  data['dispatchJson'] = entity.dispatchJson;
  data['rentJson'] = entity.rentJson;
  data['appointJson'] = entity.appointJson;
  data['boxTotalNum'] = entity.boxTotalNum;
  data['boxEmptyNum'] = entity.boxEmptyNum;
  data['boxAvailableNum'] = entity.boxAvailableNum;
  data['superCount'] = entity.superCount;
  data['hugeCount'] = entity.hugeCount;
  data['largeCount'] = entity.largeCount;
  data['mediumCount'] = entity.mediumCount;
  data['smallCount'] = entity.smallCount;
  data['miniCount'] = entity.miniCount;
  data['microCount'] = entity.microCount;
  data['status'] = entity.status;
  data['hasUsed'] = entity.hasUsed;
  data['overdueOutboundDays'] = entity.overdueOutboundDays;
  data['distance'] = entity.distance;
  data['latitude'] = entity.latitude;
  data['longitude'] = entity.longitude;
  data['hasCollected'] = entity.hasCollected;
  data['hasAddCabinet'] = entity.hasAddCabinet;
  data['hasChangeBrand'] = entity.hasChangeBrand;
  data['switchBatch'] = entity.switchBatch;
  data['switchBook'] = entity.switchBook;
  data['bookJson'] = entity.bookJson;
  data['switchRent'] = entity.switchRent;
  return data;
}

extension CabinetEntityExtension on CabinetEntity {
  CabinetEntity copyWith({
    String? siteId,
    String? id,
    String? code,
    String? name,
    int? type,
    String? cabinetLocationId,
    String? provinceName,
    String? provinceCode,
    String? areaName,
    String? areaCode,
    String? cityName,
    String? cityCode,
    String? address,
    String? dispatchJson,
    String? rentJson,
    String? appointJson,
    int? boxTotalNum,
    int? boxEmptyNum,
    int? boxAvailableNum,
    int? superCount,
    int? hugeCount,
    int? largeCount,
    int? mediumCount,
    int? smallCount,
    int? miniCount,
    int? microCount,
    int? status,
    int? hasUsed,
    int? overdueOutboundDays,
    double? distance,
    double? latitude,
    double? longitude,
    int? hasCollected,
    int? hasAddCabinet,
    int? hasChangeBrand,
    int? switchBatch,
    int? switchBook,
    String? bookJson,
    int? switchRent,
  }) {
    return CabinetEntity()
      ..siteId = siteId ?? this.siteId
      ..id = id ?? this.id
      ..code = code ?? this.code
      ..name = name ?? this.name
      ..type = type ?? this.type
      ..cabinetLocationId = cabinetLocationId ?? this.cabinetLocationId
      ..provinceName = provinceName ?? this.provinceName
      ..provinceCode = provinceCode ?? this.provinceCode
      ..areaName = areaName ?? this.areaName
      ..areaCode = areaCode ?? this.areaCode
      ..cityName = cityName ?? this.cityName
      ..cityCode = cityCode ?? this.cityCode
      ..address = address ?? this.address
      ..dispatchJson = dispatchJson ?? this.dispatchJson
      ..rentJson = rentJson ?? this.rentJson
      ..appointJson = appointJson ?? this.appointJson
      ..boxTotalNum = boxTotalNum ?? this.boxTotalNum
      ..boxEmptyNum = boxEmptyNum ?? this.boxEmptyNum
      ..boxAvailableNum = boxAvailableNum ?? this.boxAvailableNum
      ..superCount = superCount ?? this.superCount
      ..hugeCount = hugeCount ?? this.hugeCount
      ..largeCount = largeCount ?? this.largeCount
      ..mediumCount = mediumCount ?? this.mediumCount
      ..smallCount = smallCount ?? this.smallCount
      ..miniCount = miniCount ?? this.miniCount
      ..microCount = microCount ?? this.microCount
      ..status = status ?? this.status
      ..hasUsed = hasUsed ?? this.hasUsed
      ..overdueOutboundDays = overdueOutboundDays ?? this.overdueOutboundDays
      ..distance = distance ?? this.distance
      ..latitude = latitude ?? this.latitude
      ..longitude = longitude ?? this.longitude
      ..hasCollected = hasCollected ?? this.hasCollected
      ..hasAddCabinet = hasAddCabinet ?? this.hasAddCabinet
      ..hasChangeBrand = hasChangeBrand ?? this.hasChangeBrand
      ..switchBatch = switchBatch ?? this.switchBatch
      ..switchBook = switchBook ?? this.switchBook
      ..bookJson = bookJson ?? this.bookJson
      ..switchRent = switchRent ?? this.switchRent;
  }
}