import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_box_entity.dart';

CabinetBoxEntity $CabinetBoxEntityFromJson(Map<String, dynamic> json) {
  final CabinetBoxEntity cabinetBoxEntity = CabinetBoxEntity();
  final String? cabinetBoxId = jsonConvert.convert<String>(json['cabinetBoxId']);
  if (cabinetBoxId != null) {
    cabinetBoxEntity.cabinetBoxId = cabinetBoxId;
  }
  final int? boxType = jsonConvert.convert<int>(json['boxType']);
  if (boxType != null) {
    cabinetBoxEntity.boxType = boxType;
  }
  final String? boxLabel = jsonConvert.convert<String>(json['boxLabel']);
  if (boxLabel != null) {
    cabinetBoxEntity.boxLabel = boxLabel;
  }
  final String? cabinetName = jsonConvert.convert<String>(json['cabinetName']);
  if (cabinetName != null) {
    cabinetBoxEntity.cabinetName = cabinetName;
  }
  final String? cabinetId = jsonConvert.convert<String>(json['cabinetId']);
  if (cabinetId != null) {
    cabinetBoxEntity.cabinetId = cabinetId;
  }
  final int? availableCount = jsonConvert.convert<int>(json['availableCount']);
  if (availableCount != null) {
    cabinetBoxEntity.availableCount = availableCount;
  }
  return cabinetBoxEntity;
}

Map<String, dynamic> $CabinetBoxEntityToJson(CabinetBoxEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['cabinetBoxId'] = entity.cabinetBoxId;
  data['boxType'] = entity.boxType;
  data['boxLabel'] = entity.boxLabel;
  data['cabinetName'] = entity.cabinetName;
  data['cabinetId'] = entity.cabinetId;
  data['availableCount'] = entity.availableCount;
  return data;
}

extension CabinetBoxEntityExtension on CabinetBoxEntity {
  CabinetBoxEntity copyWith({
    String? cabinetBoxId,
    int? boxType,
    String? boxLabel,
    String? cabinetName,
    String? cabinetId,
    int? availableCount,
  }) {
    return CabinetBoxEntity()
      ..cabinetBoxId = cabinetBoxId ?? this.cabinetBoxId
      ..boxType = boxType ?? this.boxType
      ..boxLabel = boxLabel ?? this.boxLabel
      ..cabinetName = cabinetName ?? this.cabinetName
      ..cabinetId = cabinetId ?? this.cabinetId
      ..availableCount = availableCount ?? this.availableCount;
  }
}