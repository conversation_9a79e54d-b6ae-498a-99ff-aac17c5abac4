import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/notify_entity.dart';

NotifyEntity $NotifyEntityFromJson(Map<String, dynamic> json) {
  final NotifyEntity notifyEntity = NotifyEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    notifyEntity.id = id;
  }
  final int? noticeType = jsonConvert.convert<int>(json['noticeType']);
  if (noticeType != null) {
    notifyEntity.noticeType = noticeType;
  }
  final int? unReadCount = jsonConvert.convert<int>(json['unReadCount']);
  if (unReadCount != null) {
    notifyEntity.unReadCount = unReadCount;
  }
  final String? noticeTitle = jsonConvert.convert<String>(json['noticeTitle']);
  if (noticeTitle != null) {
    notifyEntity.noticeTitle = noticeTitle;
  }
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    notifyEntity.content = content;
  }
  final String? noticeSubtitle = jsonConvert.convert<String>(json['noticeSubtitle']);
  if (noticeSubtitle != null) {
    notifyEntity.noticeSubtitle = noticeSubtitle;
  }
  final String? onlineTime = jsonConvert.convert<String>(json['onlineTime']);
  if (onlineTime != null) {
    notifyEntity.onlineTime = onlineTime;
  }
  final int? readStatus = jsonConvert.convert<int>(json['readStatus']);
  if (readStatus != null) {
    notifyEntity.readStatus = readStatus;
  }
  return notifyEntity;
}

Map<String, dynamic> $NotifyEntityToJson(NotifyEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['noticeType'] = entity.noticeType;
  data['unReadCount'] = entity.unReadCount;
  data['noticeTitle'] = entity.noticeTitle;
  data['content'] = entity.content;
  data['noticeSubtitle'] = entity.noticeSubtitle;
  data['onlineTime'] = entity.onlineTime;
  data['readStatus'] = entity.readStatus;
  return data;
}

extension NotifyEntityExtension on NotifyEntity {
  NotifyEntity copyWith({
    String? id,
    int? noticeType,
    int? unReadCount,
    String? noticeTitle,
    String? content,
    String? noticeSubtitle,
    String? onlineTime,
    int? readStatus,
  }) {
    return NotifyEntity()
      ..id = id ?? this.id
      ..noticeType = noticeType ?? this.noticeType
      ..unReadCount = unReadCount ?? this.unReadCount
      ..noticeTitle = noticeTitle ?? this.noticeTitle
      ..content = content ?? this.content
      ..noticeSubtitle = noticeSubtitle ?? this.noticeSubtitle
      ..onlineTime = onlineTime ?? this.onlineTime
      ..readStatus = readStatus ?? this.readStatus;
  }
}