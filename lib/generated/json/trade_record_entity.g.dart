import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/trade_record_entity.dart';

TradeRecordEntity $TradeRecordEntityFromJson(Map<String, dynamic> json) {
  final TradeRecordEntity tradeRecordEntity = TradeRecordEntity();
  final String? tradeNo = jsonConvert.convert<String>(json['tradeNo']);
  if (tradeNo != null) {
    tradeRecordEntity.tradeNo = tradeNo;
  }
  final String? shopId = jsonConvert.convert<String>(json['shopId']);
  if (shopId != null) {
    tradeRecordEntity.shopId = shopId;
  }
  final int? tradeSubject = jsonConvert.convert<int>(json['tradeSubject']);
  if (tradeSubject != null) {
    tradeRecordEntity.tradeSubject = tradeSubject;
  }
  final int? tradeType = jsonConvert.convert<int>(json['tradeType']);
  if (tradeType != null) {
    tradeRecordEntity.tradeType = tradeType;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    tradeRecordEntity.createTime = createTime;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    tradeRecordEntity.updateTime = updateTime;
  }
  final String? tradeYmd = jsonConvert.convert<String>(json['tradeYmd']);
  if (tradeYmd != null) {
    tradeRecordEntity.tradeYmd = tradeYmd;
  }
  final int? tradeMoney = jsonConvert.convert<int>(json['tradeMoney']);
  if (tradeMoney != null) {
    tradeRecordEntity.tradeMoney = tradeMoney;
  }
  final int? payType = jsonConvert.convert<int>(json['payType']);
  if (payType != null) {
    tradeRecordEntity.payType = payType;
  }
  final bool? isIncome = jsonConvert.convert<bool>(json['isIncome']);
  if (isIncome != null) {
    tradeRecordEntity.isIncome = isIncome;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    tradeRecordEntity.remark = remark;
  }
  return tradeRecordEntity;
}

Map<String, dynamic> $TradeRecordEntityToJson(TradeRecordEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['tradeNo'] = entity.tradeNo;
  data['shopId'] = entity.shopId;
  data['tradeSubject'] = entity.tradeSubject;
  data['tradeType'] = entity.tradeType;
  data['createTime'] = entity.createTime;
  data['updateTime'] = entity.updateTime;
  data['tradeYmd'] = entity.tradeYmd;
  data['tradeMoney'] = entity.tradeMoney;
  data['payType'] = entity.payType;
  data['isIncome'] = entity.isIncome;
  data['remark'] = entity.remark;
  return data;
}

extension TradeRecordEntityExtension on TradeRecordEntity {
  TradeRecordEntity copyWith({
    String? tradeNo,
    String? shopId,
    int? tradeSubject,
    int? tradeType,
    String? createTime,
    String? updateTime,
    String? tradeYmd,
    int? tradeMoney,
    int? payType,
    bool? isIncome,
    String? remark,
  }) {
    return TradeRecordEntity()
      ..tradeNo = tradeNo ?? this.tradeNo
      ..shopId = shopId ?? this.shopId
      ..tradeSubject = tradeSubject ?? this.tradeSubject
      ..tradeType = tradeType ?? this.tradeType
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime
      ..tradeYmd = tradeYmd ?? this.tradeYmd
      ..tradeMoney = tradeMoney ?? this.tradeMoney
      ..payType = payType ?? this.payType
      ..isIncome = isIncome ?? this.isIncome
      ..remark = remark ?? this.remark;
  }
}