import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/brand_bind_entity.dart';

import 'package:cabinet_flutter_app/common/entitys/user_account_entity.dart';


UserEntity $UserEntityFromJson(Map<String, dynamic> json) {
  final UserEntity userEntity = UserEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    userEntity.id = id;
  }
  final String? siteId = jsonConvert.convert<String>(json['siteId']);
  if (siteId != null) {
    userEntity.siteId = siteId;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    userEntity.userId = userId;
  }
  final String? accountId = jsonConvert.convert<String>(json['accountId']);
  if (accountId != null) {
    userEntity.accountId = accountId;
  }
  final String? brandCode = jsonConvert.convert<String>(json['brandCode']);
  if (brandCode != null) {
    userEntity.brandCode = brandCode;
  }
  final String? type = jsonConvert.convert<String>(json['type']);
  if (type != null) {
    userEntity.type = type;
  }
  final String? siteName = jsonConvert.convert<String>(json['siteName']);
  if (siteName != null) {
    userEntity.siteName = siteName;
  }
  final String? loginName = jsonConvert.convert<String>(json['loginName']);
  if (loginName != null) {
    userEntity.loginName = loginName;
  }
  final double? discountFactor = jsonConvert.convert<double>(json['discountFactor']);
  if (discountFactor != null) {
    userEntity.discountFactor = discountFactor;
  }
  final int? cutType = jsonConvert.convert<int>(json['cutType']);
  if (cutType != null) {
    userEntity.cutType = cutType;
  }
  final int? cutAmount = jsonConvert.convert<int>(json['cutAmount']);
  if (cutAmount != null) {
    userEntity.cutAmount = cutAmount;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    userEntity.name = name;
  }
  final String? realName = jsonConvert.convert<String>(json['realName']);
  if (realName != null) {
    userEntity.realName = realName;
  }
  final String? idNumber = jsonConvert.convert<String>(json['idNumber']);
  if (idNumber != null) {
    userEntity.idNumber = idNumber;
  }
  final String? yundaAccount = jsonConvert.convert<String>(json['yundaAccount']);
  if (yundaAccount != null) {
    userEntity.yundaAccount = yundaAccount;
  }
  final String? yzPhone = jsonConvert.convert<String>(json['yzPhone']);
  if (yzPhone != null) {
    userEntity.yzPhone = yzPhone;
  }
  final String? deployCode = jsonConvert.convert<String>(json['deployCode']);
  if (deployCode != null) {
    userEntity.deployCode = deployCode;
  }
  final bool? isAdmin = jsonConvert.convert<bool>(json['isAdmin']);
  if (isAdmin != null) {
    userEntity.isAdmin = isAdmin;
  }
  final int? hasReal = jsonConvert.convert<int>(json['hasReal']);
  if (hasReal != null) {
    userEntity.hasReal = hasReal;
  }
  final String? shopCode = jsonConvert.convert<String>(json['shopCode']);
  if (shopCode != null) {
    userEntity.shopCode = shopCode;
  }
  final String? shopName = jsonConvert.convert<String>(json['shopName']);
  if (shopName != null) {
    userEntity.shopName = shopName;
  }
  final String? shopMobile = jsonConvert.convert<String>(json['shopMobile']);
  if (shopMobile != null) {
    userEntity.shopMobile = shopMobile;
  }
  final int? reviewStatus = jsonConvert.convert<int>(json['reviewStatus']);
  if (reviewStatus != null) {
    userEntity.reviewStatus = reviewStatus;
  }
  final String? shopAddress = jsonConvert.convert<String>(json['shopAddress']);
  if (shopAddress != null) {
    userEntity.shopAddress = shopAddress;
  }
  final String? siteCode = jsonConvert.convert<String>(json['siteCode']);
  if (siteCode != null) {
    userEntity.siteCode = siteCode;
  }
  final String? userCode = jsonConvert.convert<String>(json['userCode']);
  if (userCode != null) {
    userEntity.userCode = userCode;
  }
  final String? userName = jsonConvert.convert<String>(json['userName']);
  if (userName != null) {
    userEntity.userName = userName;
  }
  final String? kdyId = jsonConvert.convert<String>(json['kdyId']);
  if (kdyId != null) {
    userEntity.kdyId = kdyId;
  }
  final int? hasSetPayPwd = jsonConvert.convert<int>(json['hasSetPayPwd']);
  if (hasSetPayPwd != null) {
    userEntity.hasSetPayPwd = hasSetPayPwd;
  }
  final int? hasBindAlipay = jsonConvert.convert<int>(json['hasBindAlipay']);
  if (hasBindAlipay != null) {
    userEntity.hasBindAlipay = hasBindAlipay;
  }
  final int? userType = jsonConvert.convert<int>(json['userType']);
  if (userType != null) {
    userEntity.userType = userType;
  }
  final int? hasAdmin = jsonConvert.convert<int>(json['hasAdmin']);
  if (hasAdmin != null) {
    userEntity.hasAdmin = hasAdmin;
  }
  final int? hasWallet = jsonConvert.convert<int>(json['hasWallet']);
  if (hasWallet != null) {
    userEntity.hasWallet = hasWallet;
  }
  final int? switchSend = jsonConvert.convert<int>(json['switchSend']);
  if (switchSend != null) {
    userEntity.switchSend = switchSend;
  }
  final String? shopId = jsonConvert.convert<String>(json['shopId']);
  if (shopId != null) {
    userEntity.shopId = shopId;
  }
  final List<UserAccountEntity?>? accounts = (json['accounts'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<UserAccountEntity>(e)).toList();
  if (accounts != null) {
    userEntity.accounts = accounts;
  }
  final List<String?>? companys = (json['companys'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e)).toList();
  if (companys != null) {
    userEntity.companys = companys;
  }
  final List<BrandBindEntity?>? brands = (json['brands'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<BrandBindEntity>(e)).toList();
  if (brands != null) {
    userEntity.brands = brands;
  }
  return userEntity;
}

Map<String, dynamic> $UserEntityToJson(UserEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['siteId'] = entity.siteId;
  data['userId'] = entity.userId;
  data['accountId'] = entity.accountId;
  data['brandCode'] = entity.brandCode;
  data['type'] = entity.type;
  data['siteName'] = entity.siteName;
  data['loginName'] = entity.loginName;
  data['discountFactor'] = entity.discountFactor;
  data['cutType'] = entity.cutType;
  data['cutAmount'] = entity.cutAmount;
  data['name'] = entity.name;
  data['realName'] = entity.realName;
  data['idNumber'] = entity.idNumber;
  data['yundaAccount'] = entity.yundaAccount;
  data['yzPhone'] = entity.yzPhone;
  data['deployCode'] = entity.deployCode;
  data['isAdmin'] = entity.isAdmin;
  data['hasReal'] = entity.hasReal;
  data['shopCode'] = entity.shopCode;
  data['shopName'] = entity.shopName;
  data['shopMobile'] = entity.shopMobile;
  data['reviewStatus'] = entity.reviewStatus;
  data['shopAddress'] = entity.shopAddress;
  data['siteCode'] = entity.siteCode;
  data['userCode'] = entity.userCode;
  data['userName'] = entity.userName;
  data['kdyId'] = entity.kdyId;
  data['hasSetPayPwd'] = entity.hasSetPayPwd;
  data['hasBindAlipay'] = entity.hasBindAlipay;
  data['userType'] = entity.userType;
  data['hasAdmin'] = entity.hasAdmin;
  data['hasWallet'] = entity.hasWallet;
  data['switchSend'] = entity.switchSend;
  data['shopId'] = entity.shopId;
  data['accounts'] = entity.accounts?.map((v) => v?.toJson()).toList();
  data['companys'] = entity.companys;
  data['brands'] = entity.brands?.map((v) => v?.toJson()).toList();
  return data;
}

extension UserEntityExtension on UserEntity {
  UserEntity copyWith({
    String? id,
    String? siteId,
    String? userId,
    String? accountId,
    String? brandCode,
    String? type,
    String? siteName,
    String? loginName,
    double? discountFactor,
    int? cutType,
    int? cutAmount,
    String? name,
    String? realName,
    String? idNumber,
    String? yundaAccount,
    String? yzPhone,
    String? deployCode,
    bool? isAdmin,
    int? hasReal,
    String? shopCode,
    String? shopName,
    String? shopMobile,
    int? reviewStatus,
    String? shopAddress,
    String? siteCode,
    String? userCode,
    String? userName,
    String? kdyId,
    int? hasSetPayPwd,
    int? hasBindAlipay,
    int? userType,
    int? hasAdmin,
    int? hasWallet,
    int? switchSend,
    String? shopId,
    List<UserAccountEntity?>? accounts,
    List<String?>? companys,
    List<BrandBindEntity?>? brands,
  }) {
    return UserEntity()
      ..id = id ?? this.id
      ..siteId = siteId ?? this.siteId
      ..userId = userId ?? this.userId
      ..accountId = accountId ?? this.accountId
      ..brandCode = brandCode ?? this.brandCode
      ..type = type ?? this.type
      ..siteName = siteName ?? this.siteName
      ..loginName = loginName ?? this.loginName
      ..discountFactor = discountFactor ?? this.discountFactor
      ..cutType = cutType ?? this.cutType
      ..cutAmount = cutAmount ?? this.cutAmount
      ..name = name ?? this.name
      ..realName = realName ?? this.realName
      ..idNumber = idNumber ?? this.idNumber
      ..yundaAccount = yundaAccount ?? this.yundaAccount
      ..yzPhone = yzPhone ?? this.yzPhone
      ..deployCode = deployCode ?? this.deployCode
      ..isAdmin = isAdmin ?? this.isAdmin
      ..hasReal = hasReal ?? this.hasReal
      ..shopCode = shopCode ?? this.shopCode
      ..shopName = shopName ?? this.shopName
      ..shopMobile = shopMobile ?? this.shopMobile
      ..reviewStatus = reviewStatus ?? this.reviewStatus
      ..shopAddress = shopAddress ?? this.shopAddress
      ..siteCode = siteCode ?? this.siteCode
      ..userCode = userCode ?? this.userCode
      ..userName = userName ?? this.userName
      ..kdyId = kdyId ?? this.kdyId
      ..hasSetPayPwd = hasSetPayPwd ?? this.hasSetPayPwd
      ..hasBindAlipay = hasBindAlipay ?? this.hasBindAlipay
      ..userType = userType ?? this.userType
      ..hasAdmin = hasAdmin ?? this.hasAdmin
      ..hasWallet = hasWallet ?? this.hasWallet
      ..switchSend = switchSend ?? this.switchSend
      ..shopId = shopId ?? this.shopId
      ..accounts = accounts ?? this.accounts
      ..companys = companys ?? this.companys
      ..brands = brands ?? this.brands;
  }
}