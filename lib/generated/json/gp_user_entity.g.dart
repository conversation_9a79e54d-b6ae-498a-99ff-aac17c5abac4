import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/gp_user_entity.dart';

GpUserEntity $GpUserEntityFromJson(Map<String, dynamic> json) {
  final GpUserEntity gpUserEntity = GpUserEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    gpUserEntity.id = id;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    gpUserEntity.createTime = createTime;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    gpUserEntity.updateTime = updateTime;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    gpUserEntity.type = type;
  }
  final String? bizId = jsonConvert.convert<String>(json['bizId']);
  if (bizId != null) {
    gpUserEntity.bizId = bizId;
  }
  final String? gpUserId = jsonConvert.convert<String>(json['gpUserId']);
  if (gpUserId != null) {
    gpUserEntity.gpUserId = gpUserId;
  }
  final String? gpUserName = jsonConvert.convert<String>(json['gpUserName']);
  if (gpUserName != null) {
    gpUserEntity.gpUserName = gpUserName;
  }
  return gpUserEntity;
}

Map<String, dynamic> $GpUserEntityToJson(GpUserEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['createTime'] = entity.createTime;
  data['updateTime'] = entity.updateTime;
  data['type'] = entity.type;
  data['bizId'] = entity.bizId;
  data['gpUserId'] = entity.gpUserId;
  data['gpUserName'] = entity.gpUserName;
  return data;
}

extension GpUserEntityExtension on GpUserEntity {
  GpUserEntity copyWith({
    String? id,
    String? createTime,
    String? updateTime,
    int? type,
    String? bizId,
    String? gpUserId,
    String? gpUserName,
  }) {
    return GpUserEntity()
      ..id = id ?? this.id
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime
      ..type = type ?? this.type
      ..bizId = bizId ?? this.bizId
      ..gpUserId = gpUserId ?? this.gpUserId
      ..gpUserName = gpUserName ?? this.gpUserName;
  }
}