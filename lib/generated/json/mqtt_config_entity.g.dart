import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/mqtt_config_entity.dart';

MqttConfigEntity $MqttConfigEntityFromJson(Map<String, dynamic> json) {
  final MqttConfigEntity mqttConfigEntity = MqttConfigEntity();
  final String? broker = jsonConvert.convert<String>(json['broker']);
  if (broker != null) {
    mqttConfigEntity.broker = broker;
  }
  final String? port = jsonConvert.convert<String>(json['port']);
  if (port != null) {
    mqttConfigEntity.port = port;
  }
  final String? productKey = jsonConvert.convert<String>(json['productKey']);
  if (productKey != null) {
    mqttConfigEntity.productKey = productKey;
  }
  final String? username = jsonConvert.convert<String>(json['username']);
  if (username != null) {
    mqttConfigEntity.username = username;
  }
  final String? password = jsonConvert.convert<String>(json['password']);
  if (password != null) {
    mqttConfigEntity.password = password;
  }
  final String? topic = jsonConvert.convert<String>(json['topic']);
  if (topic != null) {
    mqttConfigEntity.topic = topic;
  }
  return mqttConfigEntity;
}

Map<String, dynamic> $MqttConfigEntityToJson(MqttConfigEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['broker'] = entity.broker;
  data['port'] = entity.port;
  data['productKey'] = entity.productKey;
  data['username'] = entity.username;
  data['password'] = entity.password;
  data['topic'] = entity.topic;
  return data;
}

extension MqttConfigEntityExtension on MqttConfigEntity {
  MqttConfigEntity copyWith({
    String? broker,
    String? port,
    String? productKey,
    String? username,
    String? password,
    String? topic,
  }) {
    return MqttConfigEntity()
      ..broker = broker ?? this.broker
      ..port = port ?? this.port
      ..productKey = productKey ?? this.productKey
      ..username = username ?? this.username
      ..password = password ?? this.password
      ..topic = topic ?? this.topic;
  }
}