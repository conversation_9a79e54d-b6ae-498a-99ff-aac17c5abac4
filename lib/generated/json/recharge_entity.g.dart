import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/recharge_entity.dart';

RechargeEntity $RechargeEntityFromJson(Map<String, dynamic> json) {
  final RechargeEntity rechargeEntity = RechargeEntity();
  final String? packageValue = jsonConvert.convert<String>(json['packageValue']);
  if (packageValue != null) {
    rechargeEntity.packageValue = packageValue;
  }
  final String? appId = jsonConvert.convert<String>(json['appId']);
  if (appId != null) {
    rechargeEntity.appId = appId;
  }
  final String? sign = jsonConvert.convert<String>(json['sign']);
  if (sign != null) {
    rechargeEntity.sign = sign;
  }
  final String? partnerid = jsonConvert.convert<String>(json['partnerid']);
  if (partnerid != null) {
    rechargeEntity.partnerid = partnerid;
  }
  final String? prepayid = jsonConvert.convert<String>(json['prepayid']);
  if (prepayid != null) {
    rechargeEntity.prepayid = prepayid;
  }
  final String? noncestr = jsonConvert.convert<String>(json['noncestr']);
  if (noncestr != null) {
    rechargeEntity.noncestr = noncestr;
  }
  final String? timestamp = jsonConvert.convert<String>(json['timestamp']);
  if (timestamp != null) {
    rechargeEntity.timestamp = timestamp;
  }
  return rechargeEntity;
}

Map<String, dynamic> $RechargeEntityToJson(RechargeEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['packageValue'] = entity.packageValue;
  data['appId'] = entity.appId;
  data['sign'] = entity.sign;
  data['partnerid'] = entity.partnerid;
  data['prepayid'] = entity.prepayid;
  data['noncestr'] = entity.noncestr;
  data['timestamp'] = entity.timestamp;
  return data;
}

extension RechargeEntityExtension on RechargeEntity {
  RechargeEntity copyWith({
    String? packageValue,
    String? appId,
    String? sign,
    String? partnerid,
    String? prepayid,
    String? noncestr,
    String? timestamp,
  }) {
    return RechargeEntity()
      ..packageValue = packageValue ?? this.packageValue
      ..appId = appId ?? this.appId
      ..sign = sign ?? this.sign
      ..partnerid = partnerid ?? this.partnerid
      ..prepayid = prepayid ?? this.prepayid
      ..noncestr = noncestr ?? this.noncestr
      ..timestamp = timestamp ?? this.timestamp;
  }
}