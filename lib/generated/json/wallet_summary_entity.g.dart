import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/wallet_summary_entity.dart';

WalletSummaryEntity $WalletSummaryEntityFromJson(Map<String, dynamic> json) {
  final WalletSummaryEntity walletSummaryEntity = WalletSummaryEntity();
  final int? incomeAmount = jsonConvert.convert<int>(json['incomeAmount']);
  if (incomeAmount != null) {
    walletSummaryEntity.incomeAmount = incomeAmount;
  }
  final int? outcomeAmount = jsonConvert.convert<int>(json['outcomeAmount']);
  if (outcomeAmount != null) {
    walletSummaryEntity.outcomeAmount = outcomeAmount;
  }
  final int? rechargeAmount = jsonConvert.convert<int>(json['rechargeAmount']);
  if (rechargeAmount != null) {
    walletSummaryEntity.rechargeAmount = rechargeAmount;
  }
  final int? withdrawAmount = jsonConvert.convert<int>(json['withdrawAmount']);
  if (withdrawAmount != null) {
    walletSummaryEntity.withdrawAmount = withdrawAmount;
  }
  return walletSummaryEntity;
}

Map<String, dynamic> $WalletSummaryEntityToJson(WalletSummaryEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['incomeAmount'] = entity.incomeAmount;
  data['outcomeAmount'] = entity.outcomeAmount;
  data['rechargeAmount'] = entity.rechargeAmount;
  data['withdrawAmount'] = entity.withdrawAmount;
  return data;
}

extension WalletSummaryEntityExtension on WalletSummaryEntity {
  WalletSummaryEntity copyWith({
    int? incomeAmount,
    int? outcomeAmount,
    int? rechargeAmount,
    int? withdrawAmount,
  }) {
    return WalletSummaryEntity()
      ..incomeAmount = incomeAmount ?? this.incomeAmount
      ..outcomeAmount = outcomeAmount ?? this.outcomeAmount
      ..rechargeAmount = rechargeAmount ?? this.rechargeAmount
      ..withdrawAmount = withdrawAmount ?? this.withdrawAmount;
  }
}