import 'package:cabinet_flutter_app/common/entitys/customer_entity.dart';
import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';

CustomerEntity $CustomerEntityFromJson(Map<String, dynamic> json) {
  final CustomerEntity customerEntity = CustomerEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    customerEntity.id = id;
  }
  final String? shopCode = jsonConvert.convert<String>(json['shopCode']);
  if (shopCode != null) {
    customerEntity.shopCode = shopCode;
  }
  final String? code = jsonConvert.convert<String>(json['code']);
  if (code != null) {
    customerEntity.code = code;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    customerEntity.name = name;
  }
  final String? jianpin = jsonConvert.convert<String>(json['jianpin']);
  if (jianpin != null) {
    customerEntity.jianpin = jianpin;
  }
  final String? pinyin = jsonConvert.convert<String>(json['pinyin']);
  if (pinyin != null) {
    customerEntity.pinyin = pinyin;
  }
  final String? mobile = jsonConvert.convert<String>(json['mobile']);
  if (mobile != null) {
    customerEntity.mobile = mobile;
  }
  final int? labelNotice = jsonConvert.convert<int>(json['labelNotice']);
  if (labelNotice != null) {
    customerEntity.labelNotice = labelNotice;
  }
  final int? isNew = jsonConvert.convert<int>(json['isNew']);
  if (isNew != null) {
    customerEntity.isNew = isNew;
  }
  final int? isBlacklist = jsonConvert.convert<int>(json['isBlacklist']);
  if (isBlacklist != null) {
    customerEntity.isBlacklist = isBlacklist;
  }
  final String? mobileLastFour = jsonConvert.convert<String>(json['mobileLastFour']);
  if (mobileLastFour != null) {
    customerEntity.mobileLastFour = mobileLastFour;
  }
  final String? idNo = jsonConvert.convert<String>(json['idNo']);
  if (idNo != null) {
    customerEntity.idNo = idNo;
  }
  final String? email = jsonConvert.convert<String>(json['email']);
  if (email != null) {
    customerEntity.email = email;
  }
  final String? provinceCode = jsonConvert.convert<String>(json['provinceCode']);
  if (provinceCode != null) {
    customerEntity.provinceCode = provinceCode;
  }
  final String? provinceName = jsonConvert.convert<String>(json['provinceName']);
  if (provinceName != null) {
    customerEntity.provinceName = provinceName;
  }
  final String? cityCode = jsonConvert.convert<String>(json['cityCode']);
  if (cityCode != null) {
    customerEntity.cityCode = cityCode;
  }
  final String? cityName = jsonConvert.convert<String>(json['cityName']);
  if (cityName != null) {
    customerEntity.cityName = cityName;
  }
  final String? countyName = jsonConvert.convert<String>(json['countyName']);
  if (countyName != null) {
    customerEntity.countyName = countyName;
  }
  final String? countyCode = jsonConvert.convert<String>(json['countyCode']);
  if (countyCode != null) {
    customerEntity.countyCode = countyCode;
  }
  final String? address = jsonConvert.convert<String>(json['address']);
  if (address != null) {
    customerEntity.address = address;
  }
  final String? gender = jsonConvert.convert<String>(json['gender']);
  if (gender != null) {
    customerEntity.gender = gender;
  }
  final String? label = jsonConvert.convert<String>(json['label']);
  if (label != null) {
    customerEntity.label = label;
  }
  final String? createBy = jsonConvert.convert<String>(json['createBy']);
  if (createBy != null) {
    customerEntity.createBy = createBy;
  }
  final String? remarks = jsonConvert.convert<String>(json['remarks']);
  if (remarks != null) {
    customerEntity.remarks = remarks;
  }
  final String? ymd = jsonConvert.convert<String>(json['ymd']);
  if (ymd != null) {
    customerEntity.ymd = ymd;
  }
  final String? createDate = jsonConvert.convert<String>(json['createDate']);
  if (createDate != null) {
    customerEntity.createDate = createDate;
  }
  final String? isBindWx = jsonConvert.convert<String>(json['isBindWx']);
  if (isBindWx != null) {
    customerEntity.isBindWx = isBindWx;
  }
  final String? isSensitiveConsumer = jsonConvert.convert<String>(json['isSensitiveConsumer']);
  if (isSensitiveConsumer != null) {
    customerEntity.isSensitiveConsumer = isSensitiveConsumer;
  }
  final String? lastInboundTime = jsonConvert.convert<String>(json['lastInboundTime']);
  if (lastInboundTime != null) {
    customerEntity.lastInboundTime = lastInboundTime;
  }
  final bool? hasSubstituteSms = jsonConvert.convert<bool>(json['hasSubstituteSms']);
  if (hasSubstituteSms != null) {
    customerEntity.hasSubstituteSms = hasSubstituteSms;
  }
  final String? mobileDesc = jsonConvert.convert<String>(json['mobileDesc']);
  if (mobileDesc != null) {
    customerEntity.mobileDesc = mobileDesc;
  }
  final String? platform = jsonConvert.convert<String>(json['platform']);
  if (platform != null) {
    customerEntity.platform = platform;
  }
  return customerEntity;
}

Map<String, dynamic> $CustomerEntityToJson(CustomerEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['shopCode'] = entity.shopCode;
  data['code'] = entity.code;
  data['name'] = entity.name;
  data['jianpin'] = entity.jianpin;
  data['pinyin'] = entity.pinyin;
  data['mobile'] = entity.mobile;
  data['labelNotice'] = entity.labelNotice;
  data['isNew'] = entity.isNew;
  data['isBlacklist'] = entity.isBlacklist;
  data['mobileLastFour'] = entity.mobileLastFour;
  data['idNo'] = entity.idNo;
  data['email'] = entity.email;
  data['provinceCode'] = entity.provinceCode;
  data['provinceName'] = entity.provinceName;
  data['cityCode'] = entity.cityCode;
  data['cityName'] = entity.cityName;
  data['countyName'] = entity.countyName;
  data['countyCode'] = entity.countyCode;
  data['address'] = entity.address;
  data['gender'] = entity.gender;
  data['label'] = entity.label;
  data['createBy'] = entity.createBy;
  data['remarks'] = entity.remarks;
  data['ymd'] = entity.ymd;
  data['createDate'] = entity.createDate;
  data['isBindWx'] = entity.isBindWx;
  data['isSensitiveConsumer'] = entity.isSensitiveConsumer;
  data['lastInboundTime'] = entity.lastInboundTime;
  data['hasSubstituteSms'] = entity.hasSubstituteSms;
  data['mobileDesc'] = entity.mobileDesc;
  data['platform'] = entity.platform;
  return data;
}

extension CustomerEntityExtension on CustomerEntity {
  CustomerEntity copyWith({
    String? id,
    String? shopCode,
    String? code,
    String? name,
    String? jianpin,
    String? pinyin,
    String? mobile,
    int? labelNotice,
    int? isNew,
    int? isBlacklist,
    String? mobileLastFour,
    String? idNo,
    String? email,
    String? provinceCode,
    String? provinceName,
    String? cityCode,
    String? cityName,
    String? countyName,
    String? countyCode,
    String? address,
    String? gender,
    String? label,
    String? createBy,
    String? remarks,
    String? ymd,
    String? createDate,
    String? isBindWx,
    String? isSensitiveConsumer,
    String? lastInboundTime,
    bool? hasSubstituteSms,
    String? mobileDesc,
    String? platform,
  }) {
    return CustomerEntity()
      ..id = id ?? this.id
      ..shopCode = shopCode ?? this.shopCode
      ..code = code ?? this.code
      ..name = name ?? this.name
      ..jianpin = jianpin ?? this.jianpin
      ..pinyin = pinyin ?? this.pinyin
      ..mobile = mobile ?? this.mobile
      ..labelNotice = labelNotice ?? this.labelNotice
      ..isNew = isNew ?? this.isNew
      ..isBlacklist = isBlacklist ?? this.isBlacklist
      ..mobileLastFour = mobileLastFour ?? this.mobileLastFour
      ..idNo = idNo ?? this.idNo
      ..email = email ?? this.email
      ..provinceCode = provinceCode ?? this.provinceCode
      ..provinceName = provinceName ?? this.provinceName
      ..cityCode = cityCode ?? this.cityCode
      ..cityName = cityName ?? this.cityName
      ..countyName = countyName ?? this.countyName
      ..countyCode = countyCode ?? this.countyCode
      ..address = address ?? this.address
      ..gender = gender ?? this.gender
      ..label = label ?? this.label
      ..createBy = createBy ?? this.createBy
      ..remarks = remarks ?? this.remarks
      ..ymd = ymd ?? this.ymd
      ..createDate = createDate ?? this.createDate
      ..isBindWx = isBindWx ?? this.isBindWx
      ..isSensitiveConsumer = isSensitiveConsumer ?? this.isSensitiveConsumer
      ..lastInboundTime = lastInboundTime ?? this.lastInboundTime
      ..hasSubstituteSms = hasSubstituteSms ?? this.hasSubstituteSms
      ..mobileDesc = mobileDesc ?? this.mobileDesc
      ..platform = platform ?? this.platform;
  }
}