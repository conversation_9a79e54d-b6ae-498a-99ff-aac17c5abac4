import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/notice_type_entity.dart';

NoticeTypeEntity $NoticeTypeEntityFromJson(Map<String, dynamic> json) {
  final NoticeTypeEntity noticeTypeEntity = NoticeTypeEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    noticeTypeEntity.id = id;
  }
  final String? type = jsonConvert.convert<String>(json['type']);
  if (type != null) {
    noticeTypeEntity.type = type;
  }
  final int? count = jsonConvert.convert<int>(json['count']);
  if (count != null) {
    noticeTypeEntity.count = count;
  }
  final String? dateTime = jsonConvert.convert<String>(json['dateTime']);
  if (dateTime != null) {
    noticeTypeEntity.dateTime = dateTime;
  }
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    noticeTypeEntity.content = content;
  }
  final String? subText = jsonConvert.convert<String>(json['subText']);
  if (subText != null) {
    noticeTypeEntity.subText = subText;
  }
  return noticeTypeEntity;
}

Map<String, dynamic> $NoticeTypeEntityToJson(NoticeTypeEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['type'] = entity.type;
  data['count'] = entity.count;
  data['dateTime'] = entity.dateTime;
  data['content'] = entity.content;
  data['subText'] = entity.subText;
  return data;
}

extension NoticeTypeEntityExtension on NoticeTypeEntity {
  NoticeTypeEntity copyWith({
    String? id,
    String? type,
    int? count,
    String? dateTime,
    String? content,
    String? subText,
  }) {
    return NoticeTypeEntity()
      ..id = id ?? this.id
      ..type = type ?? this.type
      ..count = count ?? this.count
      ..dateTime = dateTime ?? this.dateTime
      ..content = content ?? this.content
      ..subText = subText ?? this.subText;
  }
}