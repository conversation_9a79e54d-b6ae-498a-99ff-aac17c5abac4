import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/door_status_entity.dart';

DoorStatusEntity $DoorStatusEntityFromJson(Map<String, dynamic> json) {
  final DoorStatusEntity doorStatusEntity = DoorStatusEntity();
  final String? status = jsonConvert.convert<String>(json['status']);
  if (status != null) {
    doorStatusEntity.status = status;
  }
  final String? serialNo = jsonConvert.convert<String>(json['serialNo']);
  if (serialNo != null) {
    doorStatusEntity.serialNo = serialNo;
  }
  final String? pcbNo = jsonConvert.convert<String>(json['pcbNo']);
  if (pcbNo != null) {
    doorStatusEntity.pcbNo = pcbNo;
  }
  return doorStatusEntity;
}

Map<String, dynamic> $DoorStatusEntityToJson(DoorStatusEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['status'] = entity.status;
  data['serialNo'] = entity.serialNo;
  data['pcbNo'] = entity.pcbNo;
  return data;
}

extension DoorStatusEntityExtension on DoorStatusEntity {
  DoorStatusEntity copyWith({
    String? status,
    String? serialNo,
    String? pcbNo,
  }) {
    return DoorStatusEntity()
      ..status = status ?? this.status
      ..serialNo = serialNo ?? this.serialNo
      ..pcbNo = pcbNo ?? this.pcbNo;
  }
}