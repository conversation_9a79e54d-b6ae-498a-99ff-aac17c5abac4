import 'package:cabinet_flutter_app/common/entitys/customer_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/mobile_guss_entity.dart';
import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';


MobileGussEntity $MobileGussEntityFromJson(Map<String, dynamic> json) {
  final MobileGussEntity mobileGussEntity = MobileGussEntity();
  final String? phone = jsonConvert.convert<String>(json['phone']);
  if (phone != null) {
    mobileGussEntity.phone = phone;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    mobileGussEntity.name = name;
  }
  final bool? hasSecret = jsonConvert.convert<bool>(json['hasSecret']);
  if (hasSecret != null) {
    mobileGussEntity.hasSecret = hasSecret;
  }
  final String? platform = jsonConvert.convert<String>(json['platform']);
  if (platform != null) {
    mobileGussEntity.platform = platform;
  }
  final int? virtualNumber = jsonConvert.convert<int>(json['virtualNumber']);
  if (virtualNumber != null) {
    mobileGussEntity.virtualNumber = virtualNumber;
  }
  final bool? hasSubstituteSms = jsonConvert.convert<bool>(json['hasSubstituteSms']);
  if (hasSubstituteSms != null) {
    mobileGussEntity.hasSubstituteSms = hasSubstituteSms;
  }
  final String? tagRemark = jsonConvert.convert<String>(json['tagRemark']);
  if (tagRemark != null) {
    mobileGussEntity.tagRemark = tagRemark;
  }
  final int? tagType = jsonConvert.convert<int>(json['tagType']);
  if (tagType != null) {
    mobileGussEntity.tagType = tagType;
  }
  final String? yzChannel = jsonConvert.convert<String>(json['yzChannel']);
  if (yzChannel != null) {
    mobileGussEntity.yzChannel = yzChannel;
  }
  final String? yzSopAccountId = jsonConvert.convert<String>(json['sopAccountId']);
  if (yzSopAccountId != null) {
    mobileGussEntity.yzSopAccountId = yzSopAccountId;
  }
  final int? guessTime = jsonConvert.convert<int>(json['guessTime']);
  if (guessTime != null) {
    mobileGussEntity.guessTime = guessTime;
  }
  final List<CustomerEntity>? gussMobiles = (json['gussMobiles'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<CustomerEntity>(e) as CustomerEntity).toList();
  if (gussMobiles != null) {
    mobileGussEntity.gussMobiles = gussMobiles;
  }
  return mobileGussEntity;
}

Map<String, dynamic> $MobileGussEntityToJson(MobileGussEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['phone'] = entity.phone;
  data['name'] = entity.name;
  data['hasSecret'] = entity.hasSecret;
  data['platform'] = entity.platform;
  data['virtualNumber'] = entity.virtualNumber;
  data['hasSubstituteSms'] = entity.hasSubstituteSms;
  data['tagRemark'] = entity.tagRemark;
  data['tagType'] = entity.tagType;
  data['yzChannel'] = entity.yzChannel;
  data['yzSopAccountId'] = entity.yzSopAccountId;
  data['guessTime'] = entity.guessTime;
  data['gussMobiles'] = entity.gussMobiles.map((v) => v.toJson()).toList();
  return data;
}

extension MobileGussEntityExtension on MobileGussEntity {
  MobileGussEntity copyWith({
    String? phone,
    String? name,
    bool? hasSecret,
    String? platform,
    int? virtualNumber,
    bool? hasSubstituteSms,
    String? tagRemark,
    int? tagType,
    String? yzChannel,
    String? yzSopAccountId,
    int? guessTime,
    List<CustomerEntity>? gussMobiles,
  }) {
    return MobileGussEntity()
      ..phone = phone ?? this.phone
      ..name = name ?? this.name
      ..hasSecret = hasSecret ?? this.hasSecret
      ..platform = platform ?? this.platform
      ..virtualNumber = virtualNumber ?? this.virtualNumber
      ..hasSubstituteSms = hasSubstituteSms ?? this.hasSubstituteSms
      ..tagRemark = tagRemark ?? this.tagRemark
      ..tagType = tagType ?? this.tagType
      ..yzChannel = yzChannel ?? this.yzChannel
      ..yzSopAccountId = yzSopAccountId ?? this.yzSopAccountId
      ..guessTime = guessTime ?? this.guessTime
      ..gussMobiles = gussMobiles ?? this.gussMobiles;
  }
}