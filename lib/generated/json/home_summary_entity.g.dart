import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/home_summary_entity.dart';

HomeSummaryEntity $HomeSummaryEntityFromJson(Map<String, dynamic> json) {
  final HomeSummaryEntity homeSummaryEntity = HomeSummaryEntity();
  final int? inboundCount = jsonConvert.convert<int>(json['inboundCount']);
  if (inboundCount != null) {
    homeSummaryEntity.inboundCount = inboundCount;
  }
  final int? outboundCount = jsonConvert.convert<int>(json['outboundCount']);
  if (outboundCount != null) {
    homeSummaryEntity.outboundCount = outboundCount;
  }
  final int? income = jsonConvert.convert<int>(json['income']);
  if (income != null) {
    homeSummaryEntity.income = income;
  }
  final int? outcome = jsonConvert.convert<int>(json['outcome']);
  if (outcome != null) {
    homeSummaryEntity.outcome = outcome;
  }
  final int? gt3dayKeepEffectCount = jsonConvert.convert<int>(json['gt3dayKeepEffectCount']);
  if (gt3dayKeepEffectCount != null) {
    homeSummaryEntity.gt3dayKeepEffectCount = gt3dayKeepEffectCount;
  }
  final int? gt7dayKeepEffectCount = jsonConvert.convert<int>(json['gt7dayKeepEffectCount']);
  if (gt7dayKeepEffectCount != null) {
    homeSummaryEntity.gt7dayKeepEffectCount = gt7dayKeepEffectCount;
  }
  final int? smsErrorCount = jsonConvert.convert<int>(json['smsErrorCount']);
  if (smsErrorCount != null) {
    homeSummaryEntity.smsErrorCount = smsErrorCount;
  }
  final int? expressGateWayErrorCount = jsonConvert.convert<int>(json['expressGateWayErrorCount']);
  if (expressGateWayErrorCount != null) {
    homeSummaryEntity.expressGateWayErrorCount = expressGateWayErrorCount;
  }
  return homeSummaryEntity;
}

Map<String, dynamic> $HomeSummaryEntityToJson(HomeSummaryEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['inboundCount'] = entity.inboundCount;
  data['outboundCount'] = entity.outboundCount;
  data['income'] = entity.income;
  data['outcome'] = entity.outcome;
  data['gt3dayKeepEffectCount'] = entity.gt3dayKeepEffectCount;
  data['gt7dayKeepEffectCount'] = entity.gt7dayKeepEffectCount;
  data['smsErrorCount'] = entity.smsErrorCount;
  data['expressGateWayErrorCount'] = entity.expressGateWayErrorCount;
  return data;
}

extension HomeSummaryEntityExtension on HomeSummaryEntity {
  HomeSummaryEntity copyWith({
    int? inboundCount,
    int? outboundCount,
    int? income,
    int? outcome,
    int? gt3dayKeepEffectCount,
    int? gt7dayKeepEffectCount,
    int? smsErrorCount,
    int? expressGateWayErrorCount,
  }) {
    return HomeSummaryEntity()
      ..inboundCount = inboundCount ?? this.inboundCount
      ..outboundCount = outboundCount ?? this.outboundCount
      ..income = income ?? this.income
      ..outcome = outcome ?? this.outcome
      ..gt3dayKeepEffectCount = gt3dayKeepEffectCount ?? this.gt3dayKeepEffectCount
      ..gt7dayKeepEffectCount = gt7dayKeepEffectCount ?? this.gt7dayKeepEffectCount
      ..smsErrorCount = smsErrorCount ?? this.smsErrorCount
      ..expressGateWayErrorCount = expressGateWayErrorCount ?? this.expressGateWayErrorCount;
  }
}