import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/today_summary_entity.dart';

TodaySummaryEntity $TodaySummaryEntityFromJson(Map<String, dynamic> json) {
  final TodaySummaryEntity todaySummaryEntity = TodaySummaryEntity();
  final int? inboundCount = jsonConvert.convert<int>(json['inboundCount']);
  if (inboundCount != null) {
    todaySummaryEntity.inboundCount = inboundCount;
  }
  final int? outboundCount = jsonConvert.convert<int>(json['outboundCount']);
  if (outboundCount != null) {
    todaySummaryEntity.outboundCount = outboundCount;
  }
  final int? income = jsonConvert.convert<int>(json['income']);
  if (income != null) {
    todaySummaryEntity.income = income;
  }
  final int? outcome = jsonConvert.convert<int>(json['outcome']);
  if (outcome != null) {
    todaySummaryEntity.outcome = outcome;
  }
  final int? storeIncome = jsonConvert.convert<int>(json['storeIncome']);
  if (storeIncome != null) {
    todaySummaryEntity.storeIncome = storeIncome;  // 暂存费收入
  }
  final int? sendIncome = jsonConvert.convert<int>(json['sendIncome']);
  if (sendIncome != null) { 
    todaySummaryEntity.sendIncome = sendIncome; // 寄件费收入
  }       
  final int? dispatchIncome = jsonConvert.convert<int>(json['dispatchIncome']);
  if (dispatchIncome != null) {
    todaySummaryEntity.dispatchIncome = dispatchIncome; // 投柜费收入
  }
  final int? dispatchOverTimeIncome = jsonConvert.convert<int>(json['dispatchOverTimeIncome']);
  if (dispatchOverTimeIncome != null) {
    todaySummaryEntity.dispatchOverTimeIncome = dispatchOverTimeIncome; // 用户超期费收入
  }
  final int? keepOverdueIncome = jsonConvert.convert<int>(json['keepOverdueIncome']);
  if (keepOverdueIncome != null) {
    todaySummaryEntity.keepOverdueIncome = keepOverdueIncome; // 暂存超期费收入
  }
  final int? bookIncome = jsonConvert.convert<int>(json['bookIncome']);
  if (bookIncome != null) {
    todaySummaryEntity.bookIncome = bookIncome; // 预约格口费用收入
  }
  final int? rentIncome = jsonConvert.convert<int>(json['rentIncome']);
  if (rentIncome != null) {
    todaySummaryEntity.rentIncome = rentIncome; // 租用格口收入
  }
  final int? smsOutgo = jsonConvert.convert<int>(json['smsOutgo']);

  
  if (smsOutgo != null) {
    todaySummaryEntity.smsOutgo = smsOutgo; // 短信费支出
  }
  final int? wxOutgo = jsonConvert.convert<int>(json['wxOutgo']);
  if (wxOutgo != null) {
    todaySummaryEntity.wxOutgo = wxOutgo; // 微信通知费支出
  }
  final int? virtualSmsOutgo = jsonConvert.convert<int>(json['virtualSmsOutgo']);
  if (virtualSmsOutgo != null) {
    todaySummaryEntity.virtualSmsOutgo = virtualSmsOutgo; // 代发短信支出
  }
  final int? serviceFeeOutgo = jsonConvert.convert<int>(json['serviceFeeOutgo']);
  if (serviceFeeOutgo != null) {
    todaySummaryEntity.serviceFeeOutgo = serviceFeeOutgo; // 服务费支出
  }
  final int? bdpoutgo = jsonConvert.convert<int>(json['bdpoutgo']);
  if (bdpoutgo != null) {
    todaySummaryEntity.bdpoutgo = bdpoutgo; // 补到派支出
  }
  final int? dayServiceFeeOutgo = jsonConvert.convert<int>(json['dayServiceFeeOutgo']);
  if (dayServiceFeeOutgo != null) {
    todaySummaryEntity.dayServiceFeeOutgo = dayServiceFeeOutgo; // 日服务费支出
  }
  final int? replInboundoutgo = jsonConvert.convert<int>(json['replInboundoutgo']);
  if (replInboundoutgo != null) {
    todaySummaryEntity.replInboundoutgo = replInboundoutgo; // 代入库费支出
  }
  final int? balanceWarningOutgo = jsonConvert.convert<int>(json['balanceWarningOutgo']);
  if (balanceWarningOutgo != null) {
    todaySummaryEntity.balanceWarningOutgo = balanceWarningOutgo; // 金额预警支出
  }
  final int? simCardRechargeOutgo = jsonConvert.convert<int>(json['simCardRechargeOutgo']);
  if (simCardRechargeOutgo != null) {
    todaySummaryEntity.simCardRechargeOutgo = simCardRechargeOutgo; // 流量卡流量充值支出
  }
  final int? simcardRenewOutgo = jsonConvert.convert<int>(json['simcardRenewOutgo']);
  if (simcardRenewOutgo != null) {
    todaySummaryEntity.simcardRenewOutgo = simcardRenewOutgo; // 流量卡续费支出
  }

  return todaySummaryEntity;
}

Map<String, dynamic> $TodaySummaryEntityToJson(TodaySummaryEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['inboundCount'] = entity.inboundCount;
  data['outboundCount'] = entity.outboundCount;
  data['income'] = entity.income;
  data['outcome'] = entity.outcome;
  return data;
}

extension TodaySummaryEntityExtension on TodaySummaryEntity {
  TodaySummaryEntity copyWith({
    int? inboundCount,
    int? outboundCount,
    int? income,
    int? outcome,
  }) {
    return TodaySummaryEntity()
      ..inboundCount = inboundCount ?? this.inboundCount
      ..outboundCount = outboundCount ?? this.outboundCount
      ..income = income ?? this.income
      ..outcome = outcome ?? this.outcome;
  }
}