import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/photo_entity.dart';

PhotoEntity $PhotoEntityFromJson(Map<String, dynamic> json) {
  final PhotoEntity photoEntity = PhotoEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    photoEntity.id = id;
  }
  final String? path = jsonConvert.convert<String>(json['path']);
  if (path != null) {
    photoEntity.path = path;
  }
  final String? fileName = jsonConvert.convert<String>(json['fileName']);
  if (fileName != null) {
    photoEntity.fileName = fileName;
  }
  final String? status = jsonConvert.convert<String>(json['status']);
  if (status != null) {
    photoEntity.status = status;
  }
  final String? type = jsonConvert.convert<String>(json['type']);
  if (type != null) {
    photoEntity.type = type;
  }
  final String? createDate = jsonConvert.convert<String>(json['createDate']);
  if (createDate != null) {
    photoEntity.createDate = createDate;
  }
  final String? ymd = jsonConvert.convert<String>(json['ymd']);
  if (ymd != null) {
    photoEntity.ymd = ymd;
  }
  final String? msg = jsonConvert.convert<String>(json['msg']);
  if (msg != null) {
    photoEntity.msg = msg;
  }
  final String? sheetNo = jsonConvert.convert<String>(json['sheetNo']);
  if (sheetNo != null) {
    photoEntity.sheetNo = sheetNo;
  }
  final String? company = jsonConvert.convert<String>(json['company']);
  if (company != null) {
    photoEntity.company = company;
  }
  return photoEntity;
}

Map<String, dynamic> $PhotoEntityToJson(PhotoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['path'] = entity.path;
  data['fileName'] = entity.fileName;
  data['status'] = entity.status;
  data['type'] = entity.type;
  data['createDate'] = entity.createDate;
  data['ymd'] = entity.ymd;
  data['msg'] = entity.msg;
  data['sheetNo'] = entity.sheetNo;
  data['company'] = entity.company;
  return data;
}

extension PhotoEntityExtension on PhotoEntity {
  PhotoEntity copyWith({
    String? id,
    String? path,
    String? fileName,
    String? status,
    String? type,
    String? createDate,
    String? ymd,
    String? msg,
    String? sheetNo,
    String? company,
  }) {
    return PhotoEntity()
      ..id = id ?? this.id
      ..path = path ?? this.path
      ..fileName = fileName ?? this.fileName
      ..status = status ?? this.status
      ..type = type ?? this.type
      ..createDate = createDate ?? this.createDate
      ..ymd = ymd ?? this.ymd
      ..msg = msg ?? this.msg
      ..sheetNo = sheetNo ?? this.sheetNo
      ..company = company ?? this.company;
  }
}