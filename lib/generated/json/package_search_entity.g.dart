import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/package_search_entity.dart';
import 'package:intl/intl.dart';


PackageSearchEntity $PackageSearchEntityFromJson(Map<String, dynamic> json) {
  final PackageSearchEntity packageSearchEntity = PackageSearchEntity();
  final String? beginYmd = jsonConvert.convert<String>(json['beginYmd']);
  if (beginYmd != null) {
    packageSearchEntity.beginYmd = beginYmd;
  }
  final String? timeRanger = jsonConvert.convert<String>(json['timeRanger']);
  if (timeRanger != null) {
    packageSearchEntity.timeRanger = timeRanger;
  }
  final String? endYmd = jsonConvert.convert<String>(json['endYmd']);
  if (endYmd != null) {
    packageSearchEntity.endYmd = endYmd;
  }
  final String? brandCode = jsonConvert.convert<String>(json['brandCode']);
  if (brandCode != null) {
    packageSearchEntity.brandCode = brandCode;
  }
  final String? cabinetLocationCode = jsonConvert.convert<String>(json['cabinetLocationCode']);
  if (cabinetLocationCode != null) {
    packageSearchEntity.cabinetLocationCode = cabinetLocationCode;
  }
  final String? keyword = jsonConvert.convert<String>(json['keyword']);
  if (keyword != null) {
    packageSearchEntity.keyword = keyword;
  }
  final bool? inboundError = jsonConvert.convert<bool>(json['inboundError']);
  if (inboundError != null) {
    packageSearchEntity.inboundError = inboundError;
  }
  final int? keepEffectStatus = jsonConvert.convert<int>(json['keepEffectStatus']);
  if (keepEffectStatus != null) {
    packageSearchEntity.keepEffectStatus = keepEffectStatus;
  }
  final int? keepEffectDay = jsonConvert.convert<int>(json['keepEffectDay']);
  if (keepEffectDay != null) {
    packageSearchEntity.keepEffectDay = keepEffectDay;
  }
  final bool? signError = jsonConvert.convert<bool>(json['signError']);
  if (signError != null) {
    packageSearchEntity.signError = signError;
  }
  final bool? returnError = jsonConvert.convert<bool>(json['returnError']);
  if (returnError != null) {
    packageSearchEntity.returnError = returnError;
  }
  final bool? smsError = jsonConvert.convert<bool>(json['smsError']);
  if (smsError != null) {
    packageSearchEntity.smsError = smsError;
  }
  final bool? overdue = jsonConvert.convert<bool>(json['overdue']);
  if (overdue != null) {
    packageSearchEntity.overdue = overdue;
  }
  final int? outbound = jsonConvert.convert<int>(json['outbound']);
  if (outbound != null) {
    packageSearchEntity.outbound = outbound;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    packageSearchEntity.size = size;
  }
  final String? storeType = jsonConvert.convert<String>(json['storeType']);
  if (storeType != null) {
    packageSearchEntity.storeType = storeType;
  }
  final String? current = jsonConvert.convert<String>(json['current']);
  if (current != null) {
    packageSearchEntity.current = current;
  }
  return packageSearchEntity;
}

Map<String, dynamic> $PackageSearchEntityToJson(PackageSearchEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['beginYmd'] = entity.beginYmd;
  data['timeRanger'] = entity.timeRanger;
  data['endYmd'] = entity.endYmd;
  data['brandCode'] = entity.brandCode;
  data['cabinetLocationCode'] = entity.cabinetLocationCode;
  data['keyword'] = entity.keyword;
  data['inboundError'] = entity.inboundError;
  data['keepEffectStatus'] = entity.keepEffectStatus;
  data['keepEffectDay'] = entity.keepEffectDay;
  data['signError'] = entity.signError;
  data['returnError'] = entity.returnError;
  data['smsError'] = entity.smsError;
  data['overdue'] = entity.overdue;
  data['outbound'] = entity.outbound;
  data['size'] = entity.size;
  data['storeType'] = entity.storeType;
  data['current'] = entity.current;
  return data;
}

extension PackageSearchEntityExtension on PackageSearchEntity {
  PackageSearchEntity copyWith({
    String? beginYmd,
    String? timeRanger,
    String? endYmd,
    String? brandCode,
    String? cabinetLocationCode,
    String? keyword,
    bool? inboundError,
    int? keepEffectStatus,
    int? keepEffectDay,
    bool? signError,
    bool? returnError,
    bool? smsError,
    bool? overdue,
    int? outbound,
    int? size,
    String? storeType,
    String? current,
  }) {
    return PackageSearchEntity()
      ..beginYmd = beginYmd ?? this.beginYmd
      ..timeRanger = timeRanger ?? this.timeRanger
      ..endYmd = endYmd ?? this.endYmd
      ..brandCode = brandCode ?? this.brandCode
      ..cabinetLocationCode = cabinetLocationCode ?? this.cabinetLocationCode
      ..keyword = keyword ?? this.keyword
      ..inboundError = inboundError ?? this.inboundError
      ..keepEffectStatus = keepEffectStatus ?? this.keepEffectStatus
      ..keepEffectDay = keepEffectDay ?? this.keepEffectDay
      ..signError = signError ?? this.signError
      ..returnError = returnError ?? this.returnError
      ..smsError = smsError ?? this.smsError
      ..overdue = overdue ?? this.overdue
      ..outbound = outbound ?? this.outbound
      ..size = size ?? this.size
      ..storeType = storeType ?? this.storeType
      ..current = current ?? this.current;
  }
}