import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/banner_entity.dart';

BannerEntity $BannerEntityFromJson(Map<String, dynamic> json) {
  final BannerEntity bannerEntity = BannerEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    bannerEntity.id = id;
  }
  final String? fileUrl = jsonConvert.convert<String>(json['fileUrl']);
  if (fileUrl != null) {
    bannerEntity.fileUrl = fileUrl;
  }
  return bannerEntity;
}

Map<String, dynamic> $BannerEntityToJson(BannerEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['fileUrl'] = entity.fileUrl;
  return data;
}

extension BannerEntityExtension on BannerEntity {
  BannerEntity copyWith({
    String? id,
    String? fileUrl,
  }) {
    return BannerEntity()
      ..id = id ?? this.id
      ..fileUrl = fileUrl ?? this.fileUrl;
  }
}