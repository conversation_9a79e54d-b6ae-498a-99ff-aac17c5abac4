import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/gp_user_list_entity.dart';

GpUserListEntity $GpUserListEntityFromJson(Map<String, dynamic> json) {
  final GpUserListEntity gpUserListEntity = GpUserListEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    gpUserListEntity.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    gpUserListEntity.name = name;
  }
  final String? company = jsonConvert.convert<String>(json['company']);
  if (company != null) {
    gpUserListEntity.company = company;
  }
  final String? mobile = jsonConvert.convert<String>(json['mobile']);
  if (mobile != null) {
    gpUserListEntity.mobile = mobile;
  }
  final String? siteCode = jsonConvert.convert<String>(json['siteCode']);
  if (siteCode != null) {
    gpUserListEntity.siteCode = siteCode;
  }
  final String? loginName = jsonConvert.convert<String>(json['loginName']);
  if (loginName != null) {
    gpUserListEntity.loginName = loginName;
  }
  final String? password = jsonConvert.convert<String>(json['password']);
  if (password != null) {
    gpUserListEntity.password = password;
  }
  final String? deviceId = jsonConvert.convert<String>(json['deviceId']);
  if (deviceId != null) {
    gpUserListEntity.deviceId = deviceId;
  }
  final String? deviceName = jsonConvert.convert<String>(json['deviceName']);
  if (deviceName != null) {
    gpUserListEntity.deviceName = deviceName;
  }
  final String? threeCode = jsonConvert.convert<String>(json['threeCode']);
  if (threeCode != null) {
    gpUserListEntity.threeCode = threeCode;
  }
  final bool? isPassed = jsonConvert.convert<bool>(json['isPassed']);
  if (isPassed != null) {
    gpUserListEntity.isPassed = isPassed;
  }
  final bool? isYbx = jsonConvert.convert<bool>(json['isYbx']);
  if (isYbx != null) {
    gpUserListEntity.isYbx = isYbx;
  }
  final bool? isXingzhe = jsonConvert.convert<bool>(json['isXingzhe']);
  if (isXingzhe != null) {
    gpUserListEntity.isXingzhe = isXingzhe;
  }
  final bool? isZtoPda = jsonConvert.convert<bool>(json['isZtoPda']);
  if (isZtoPda != null) {
    gpUserListEntity.isZtoPda = isZtoPda;
  }
  return gpUserListEntity;
}

Map<String, dynamic> $GpUserListEntityToJson(GpUserListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  data['company'] = entity.company;
  data['mobile'] = entity.mobile;
  data['siteCode'] = entity.siteCode;
  data['loginName'] = entity.loginName;
  data['password'] = entity.password;
  data['deviceId'] = entity.deviceId;
  data['deviceName'] = entity.deviceName;
  data['threeCode'] = entity.threeCode;
  data['isPassed'] = entity.isPassed;
  data['isYbx'] = entity.isYbx;
  data['isXingzhe'] = entity.isXingzhe;
  data['isZtoPda'] = entity.isZtoPda;
  return data;
}

extension GpUserListEntityExtension on GpUserListEntity {
  GpUserListEntity copyWith({
    String? id,
    String? name,
    String? company,
    String? mobile,
    String? siteCode,
    String? loginName,
    String? password,
    String? deviceId,
    String? deviceName,
    String? threeCode,
    bool? isPassed,
    bool? isYbx,
    bool? isXingzhe,
    bool? isZtoPda,
  }) {
    return GpUserListEntity()
      ..id = id ?? this.id
      ..name = name ?? this.name
      ..company = company ?? this.company
      ..mobile = mobile ?? this.mobile
      ..siteCode = siteCode ?? this.siteCode
      ..loginName = loginName ?? this.loginName
      ..password = password ?? this.password
      ..deviceId = deviceId ?? this.deviceId
      ..deviceName = deviceName ?? this.deviceName
      ..threeCode = threeCode ?? this.threeCode
      ..isPassed = isPassed ?? this.isPassed
      ..isYbx = isYbx ?? this.isYbx
      ..isXingzhe = isXingzhe ?? this.isXingzhe
      ..isZtoPda = isZtoPda ?? this.isZtoPda;
  }
}