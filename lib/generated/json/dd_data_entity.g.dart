import 'package:cabinet_flutter_app/common/entitys/dd_data_entity.dart';
import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';

DdDataEntity $DdDataEntityFromJson(Map<String, dynamic> json) {
  final DdDataEntity ddDataEntity = DdDataEntity();
  final String? key = jsonConvert.convert<String>(json['key']);
  if (key != null) {
    ddDataEntity.key = key;
  }
  final String? data = jsonConvert.convert<String>(json['data']);
  if (data != null) {
    ddDataEntity.data = data;
  }
  return ddDataEntity;
}

Map<String, dynamic> $DdDataEntityToJson(DdDataEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['key'] = entity.key;
  data['data'] = entity.data;
  return data;
}

extension DdDataEntityExtension on DdDataEntity {
  DdDataEntity copyWith({
    String? key,
    String? data,
  }) {
    return DdDataEntity()
      ..key = key ?? this.key
      ..data = data ?? this.data;
  }
}
