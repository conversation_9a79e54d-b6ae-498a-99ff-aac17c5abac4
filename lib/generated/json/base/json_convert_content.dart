// ignore_for_file: non_constant_identifier_names
// ignore_for_file: camel_case_types
// ignore_for_file: prefer_single_quotes

import 'package:cabinet_flutter_app/common/entitys/action_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/banner_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/box_item_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/box_price_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/brand_bind_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_book_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_box_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_box_useable_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_location_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_shop_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_waybill_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/company_rules_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/customer_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/customer_mobile_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/data_pj_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/dd_data_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/door_status_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/gp_user_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/gp_user_list_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/home_data_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/home_summary_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/keep_effect_summary_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/login_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/mobile_guss_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/mqtt_config_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/notice_type_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/notify_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/order_log_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/package_search_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/package_view_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/package_view_sj_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/photo_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/recharge_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/scan_item_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/shop_cabinet_location_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/shop_courier_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/team_manger_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/today_summary_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/trade_record_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/update_app_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/user_account_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/user_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/user_info_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/user_pay_account_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/wallet_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/wallet_summary_entity.dart';
import 'package:cabinet_flutter_app/common/entitys/withdrawal_record_entity.dart';
// This file is automatically generated. DO NOT EDIT, all your changes would be lost.
import 'package:flutter/material.dart' show debugPrint;

JsonConvert jsonConvert = JsonConvert();

typedef JsonConvertFunction<T> = T Function(Map<String, dynamic> json);
typedef EnumConvertFunction<T> = T Function(String value);
typedef ConvertExceptionHandler = void Function(Object error, StackTrace stackTrace);
extension MapSafeExt<K, V> on Map<K, V> {
  T? getOrNull<T>(K? key) {
    if (!containsKey(key) || key == null) {
      return null;
    } else {
      return this[key] as T?;
    }
  }
}

class JsonConvert {
  static ConvertExceptionHandler? onError;
  JsonConvertClassCollection convertFuncMap = JsonConvertClassCollection();

  /// When you are in the development, to generate a new model class, hot-reload doesn't find new generation model class, you can build on MaterialApp method called jsonConvert. ReassembleConvertFuncMap (); This method only works in a development environment
  /// https://flutter.cn/docs/development/tools/hot-reload
  /// class MyApp extends StatelessWidget {
  ///    const MyApp({Key? key})
  ///        : super(key: key);
  ///
  ///    @override
  ///    Widget build(BuildContext context) {
  ///      jsonConvert.reassembleConvertFuncMap();
  ///      return MaterialApp();
  ///    }
  /// }
  void reassembleConvertFuncMap() {
    bool isReleaseMode = const bool.fromEnvironment('dart.vm.product');
    if (!isReleaseMode) {
      convertFuncMap = JsonConvertClassCollection();
    }
  }

  T? convert<T>(dynamic value, {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    if (value is T) {
      return value;
    }
    try {
      return _asT<T>(value, enumConvert: enumConvert);
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return null;
    }
  }

  List<T?>? convertList<T>(List<dynamic>? value, {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    try {
      return value.map((dynamic e) => _asT<T>(e, enumConvert: enumConvert)).toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return <T>[];
    }
  }

  List<T>? convertListNotNull<T>(dynamic value, {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    try {
      return (value as List<dynamic>).map((dynamic e) => _asT<T>(e, enumConvert: enumConvert)!).toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return <T>[];
    }
  }

  T? _asT<T extends Object?>(dynamic value,
      {EnumConvertFunction? enumConvert}) {
    final String type = T.toString();
    final String valueS = value.toString();
    if (enumConvert != null) {
      return enumConvert(valueS) as T;
    } else if (type == "String") {
      return valueS as T;
    } else if (type == "int") {
      final int? intValue = int.tryParse(valueS);
      if (intValue == null) {
        return double.tryParse(valueS)?.toInt() as T?;
      } else {
        return intValue as T;
      }
    } else if (type == "double") {
      return double.parse(valueS) as T;
    } else if (type == "DateTime") {
      return DateTime.parse(valueS) as T;
    } else if (type == "bool") {
      if (valueS == '0' || valueS == '1') {
        return (valueS == '1') as T;
      }
      return (valueS == 'true') as T;
    } else if (type == "Map" || type.startsWith("Map<")) {
      return value as T;
    } else {
      if (convertFuncMap.containsKey(type)) {
        if (value == null) {
          return null;
        }
        var covertFunc = convertFuncMap[type]!;
        if (covertFunc is Map<String, dynamic>) {
          return covertFunc(value as Map<String, dynamic>) as T;
        } else {
          return covertFunc(Map<String, dynamic>.from(value)) as T;
        }
      } else {
        throw UnimplementedError('$type unimplemented,you can try running the app again');
      }
    }
  }

  //list is returned by type
  static M? _getListChildType<M>(List<Map<String, dynamic>> data) {
    if (<ActionEntity>[] is M) {
      return data.map<ActionEntity>((Map<String, dynamic> e) => ActionEntity.fromJson(e)).toList() as M;
    }
    if (<BannerEntity>[] is M) {
      return data.map<BannerEntity>((Map<String, dynamic> e) => BannerEntity.fromJson(e)).toList() as M;
    }
    if (<BoxItemEntity>[] is M) {
      return data.map<BoxItemEntity>((Map<String, dynamic> e) => BoxItemEntity.fromJson(e)).toList() as M;
    }
    if (<BoxPriceEntity>[] is M) {
      return data.map<BoxPriceEntity>((Map<String, dynamic> e) => BoxPriceEntity.fromJson(e)).toList() as M;
    }
    if (<BrandBindEntity>[] is M) {
      return data.map<BrandBindEntity>((Map<String, dynamic> e) => BrandBindEntity.fromJson(e)).toList() as M;
    }
    if (<CabinetBookEntity>[] is M) {
      return data.map<CabinetBookEntity>((Map<String, dynamic> e) => CabinetBookEntity.fromJson(e)).toList() as M;
    }
    if (<CabinetBookList>[] is M) {
      return data.map<CabinetBookList>((Map<String, dynamic> e) => CabinetBookList.fromJson(e)).toList() as M;
    }
    if (<CabinetBoxEntity>[] is M) {
      return data.map<CabinetBoxEntity>((Map<String, dynamic> e) => CabinetBoxEntity.fromJson(e)).toList() as M;
    }
    if (<CabinetBoxUseableEntity>[] is M) {
      return data.map<CabinetBoxUseableEntity>((Map<String, dynamic> e) => CabinetBoxUseableEntity.fromJson(e)).toList()
          as M;
    }
    if (<CabinetEntity>[] is M) {
      return data.map<CabinetEntity>((Map<String, dynamic> e) => CabinetEntity.fromJson(e)).toList() as M;
    }
    if (<CabinetLocationEntity>[] is M) {
      return data.map<CabinetLocationEntity>((Map<String, dynamic> e) => CabinetLocationEntity.fromJson(e)).toList()
          as M;
    }
    if (<CabinetShopEntity>[] is M) {
      return data.map<CabinetShopEntity>((Map<String, dynamic> e) => CabinetShopEntity.fromJson(e)).toList() as M;
    }
    if (<CabinetWaybillEntity>[] is M) {
      return data.map<CabinetWaybillEntity>((Map<String, dynamic> e) => CabinetWaybillEntity.fromJson(e)).toList() as M;
    }
    if (<CompanyRulesEntity>[] is M) {
      return data.map<CompanyRulesEntity>((Map<String, dynamic> e) => CompanyRulesEntity.fromJson(e)).toList() as M;
    }
    if (<CustomerEntity>[] is M) {
      return data.map<CustomerEntity>((Map<String, dynamic> e) => CustomerEntity.fromJson(e)).toList() as M;
    }
    if (<CustomerMobileEntity>[] is M) {
      return data.map<CustomerMobileEntity>((Map<String, dynamic> e) => CustomerMobileEntity.fromJson(e)).toList() as M;
    }
    if (<CustomerMobileOrderList>[] is M) {
      return data.map<CustomerMobileOrderList>((Map<String, dynamic> e) => CustomerMobileOrderList.fromJson(e)).toList()
          as M;
    }
    if (<DataPjEntity>[] is M) {
      return data.map<DataPjEntity>((Map<String, dynamic> e) => DataPjEntity.fromJson(e)).toList() as M;
    }
    if (<DdDataEntity>[] is M) {
      return data.map<DdDataEntity>((Map<String, dynamic> e) => DdDataEntity.fromJson(e)).toList() as M;
    }
    if (<DoorStatusEntity>[] is M) {
      return data.map<DoorStatusEntity>((Map<String, dynamic> e) => DoorStatusEntity.fromJson(e)).toList() as M;
    }
    if (<GpUserEntity>[] is M) {
      return data.map<GpUserEntity>((Map<String, dynamic> e) => GpUserEntity.fromJson(e)).toList() as M;
    }
    if (<GpUserListEntity>[] is M) {
      return data.map<GpUserListEntity>((Map<String, dynamic> e) => GpUserListEntity.fromJson(e)).toList() as M;
    }
    if (<HomeDataEntity>[] is M) {
      return data.map<HomeDataEntity>((Map<String, dynamic> e) => HomeDataEntity.fromJson(e)).toList() as M;
    }
    if (<HomeSummaryEntity>[] is M) {
      return data.map<HomeSummaryEntity>((Map<String, dynamic> e) => HomeSummaryEntity.fromJson(e)).toList() as M;
    }
    if (<KeepEffectSummaryEntity>[] is M) {
      return data.map<KeepEffectSummaryEntity>((Map<String, dynamic> e) => KeepEffectSummaryEntity.fromJson(e)).toList()
          as M;
    }
    if (<LoginEntity>[] is M) {
      return data.map<LoginEntity>((Map<String, dynamic> e) => LoginEntity.fromJson(e)).toList() as M;
    }
    if (<MobileGussEntity>[] is M) {
      return data.map<MobileGussEntity>((Map<String, dynamic> e) => MobileGussEntity.fromJson(e)).toList() as M;
    }
    if (<MqttConfigEntity>[] is M) {
      return data.map<MqttConfigEntity>((Map<String, dynamic> e) => MqttConfigEntity.fromJson(e)).toList() as M;
    }
    if (<NoticeTypeEntity>[] is M) {
      return data.map<NoticeTypeEntity>((Map<String, dynamic> e) => NoticeTypeEntity.fromJson(e)).toList() as M;
    }
    if (<NotifyEntity>[] is M) {
      return data.map<NotifyEntity>((Map<String, dynamic> e) => NotifyEntity.fromJson(e)).toList() as M;
    }
    if (<OrderLogEntity>[] is M) {
      return data.map<OrderLogEntity>((Map<String, dynamic> e) => OrderLogEntity.fromJson(e)).toList() as M;
    }
    if (<PackageSearchEntity>[] is M) {
      return data.map<PackageSearchEntity>((Map<String, dynamic> e) => PackageSearchEntity.fromJson(e)).toList() as M;
    }
    if (<PackageViewEntity>[] is M) {
      return data.map<PackageViewEntity>((Map<String, dynamic> e) => PackageViewEntity.fromJson(e)).toList() as M;
    }
    if (<PackageViewSjEntity>[] is M) {
      return data.map<PackageViewSjEntity>((Map<String, dynamic> e) => PackageViewSjEntity.fromJson(e)).toList() as M;
    }
    if (<PhotoEntity>[] is M) {
      return data.map<PhotoEntity>((Map<String, dynamic> e) => PhotoEntity.fromJson(e)).toList() as M;
    }
    if (<RechargeEntity>[] is M) {
      return data.map<RechargeEntity>((Map<String, dynamic> e) => RechargeEntity.fromJson(e)).toList() as M;
    }
    if (<ScanItemEntity>[] is M) {
      return data.map<ScanItemEntity>((Map<String, dynamic> e) => ScanItemEntity.fromJson(e)).toList() as M;
    }
    if (<ShopCabinetLocationEntity>[] is M) {
      return data
          .map<ShopCabinetLocationEntity>((Map<String, dynamic> e) => ShopCabinetLocationEntity.fromJson(e))
          .toList() as M;
    }
    if (<CabinetItemEntity>[] is M) {
      return data.map<CabinetItemEntity>((Map<String, dynamic> e) => CabinetItemEntity.fromJson(e)).toList() as M;
    }
    if (<CabinetBoxItem>[] is M) {
      return data.map<CabinetBoxItem>((Map<String, dynamic> e) => CabinetBoxItem.fromJson(e)).toList() as M;
    }
    if (<ShopCourierEntity>[] is M) {
      return data.map<ShopCourierEntity>((Map<String, dynamic> e) => ShopCourierEntity.fromJson(e)).toList() as M;
    }
    if (<TeamMangerEntity>[] is M) {
      return data.map<TeamMangerEntity>((Map<String, dynamic> e) => TeamMangerEntity.fromJson(e)).toList() as M;
    }
    if (<TodaySummaryEntity>[] is M) {
      return data.map<TodaySummaryEntity>((Map<String, dynamic> e) => TodaySummaryEntity.fromJson(e)).toList() as M;
    }
    if (<TradeRecordEntity>[] is M) {
      return data.map<TradeRecordEntity>((Map<String, dynamic> e) => TradeRecordEntity.fromJson(e)).toList() as M;
    }
    if (<UpdateAppEntity>[] is M) {
      return data.map<UpdateAppEntity>((Map<String, dynamic> e) => UpdateAppEntity.fromJson(e)).toList() as M;
    }
    if (<UserAccountEntity>[] is M) {
      return data.map<UserAccountEntity>((Map<String, dynamic> e) => UserAccountEntity.fromJson(e)).toList() as M;
    }
    if (<UserAccountExt>[] is M) {
      return data.map<UserAccountExt>((Map<String, dynamic> e) => UserAccountExt.fromJson(e)).toList() as M;
    }
    if (<UserEntity>[] is M) {
      return data.map<UserEntity>((Map<String, dynamic> e) => UserEntity.fromJson(e)).toList() as M;
    }
    if (<UserInfoEntity>[] is M) {
      return data.map<UserInfoEntity>((Map<String, dynamic> e) => UserInfoEntity.fromJson(e)).toList() as M;
    }
    if (<UserPayAccountEntity>[] is M) {
      return data.map<UserPayAccountEntity>((Map<String, dynamic> e) => UserPayAccountEntity.fromJson(e)).toList() as M;
    }
    if (<WalletEntity>[] is M) {
      return data.map<WalletEntity>((Map<String, dynamic> e) => WalletEntity.fromJson(e)).toList() as M;
    }
    if (<WalletSummaryEntity>[] is M) {
      return data.map<WalletSummaryEntity>((Map<String, dynamic> e) => WalletSummaryEntity.fromJson(e)).toList() as M;
    }
    if (<WithdrawalRecordEntity>[] is M) {
      return data.map<WithdrawalRecordEntity>((Map<String, dynamic> e) => WithdrawalRecordEntity.fromJson(e)).toList()
          as M;
    }

    debugPrint("$M not found");

    return null;
  }

  static M? fromJsonAsT<M>(dynamic json) {
    if (json is M) {
      return json;
    }
    if (json is List) {
      return _getListChildType<M>(json.map((dynamic e) => e as Map<String, dynamic>).toList());
    } else {
      return jsonConvert.convert<M>(json);
    }
  }
}

class JsonConvertClassCollection {
  Map<String, JsonConvertFunction> convertFuncMap = {
    (ActionEntity).toString(): ActionEntity.fromJson,
    (BannerEntity).toString(): BannerEntity.fromJson,
    (BoxItemEntity).toString(): BoxItemEntity.fromJson,
    (BoxPriceEntity).toString(): BoxPriceEntity.fromJson,
    (BrandBindEntity).toString(): BrandBindEntity.fromJson,
    (CabinetBookEntity).toString(): CabinetBookEntity.fromJson,
    (CabinetBookList).toString(): CabinetBookList.fromJson,
    (CabinetBoxEntity).toString(): CabinetBoxEntity.fromJson,
    (CabinetBoxUseableEntity).toString(): CabinetBoxUseableEntity.fromJson,
    (CabinetEntity).toString(): CabinetEntity.fromJson,
    (CabinetLocationEntity).toString(): CabinetLocationEntity.fromJson,
    (CabinetShopEntity).toString(): CabinetShopEntity.fromJson,
    (CabinetWaybillEntity).toString(): CabinetWaybillEntity.fromJson,
    (CompanyRulesEntity).toString(): CompanyRulesEntity.fromJson,
    (CustomerEntity).toString(): CustomerEntity.fromJson,
    (CustomerMobileEntity).toString(): CustomerMobileEntity.fromJson,
    (CustomerMobileOrderList).toString(): CustomerMobileOrderList.fromJson,
    (DataPjEntity).toString(): DataPjEntity.fromJson,
    (DdDataEntity).toString(): DdDataEntity.fromJson,
    (DoorStatusEntity).toString(): DoorStatusEntity.fromJson,
    (GpUserEntity).toString(): GpUserEntity.fromJson,
    (GpUserListEntity).toString(): GpUserListEntity.fromJson,
    (HomeDataEntity).toString(): HomeDataEntity.fromJson,
    (HomeSummaryEntity).toString(): HomeSummaryEntity.fromJson,
    (KeepEffectSummaryEntity).toString(): KeepEffectSummaryEntity.fromJson,
    (LoginEntity).toString(): LoginEntity.fromJson,
    (MobileGussEntity).toString(): MobileGussEntity.fromJson,
    (MqttConfigEntity).toString(): MqttConfigEntity.fromJson,
    (NoticeTypeEntity).toString(): NoticeTypeEntity.fromJson,
    (NotifyEntity).toString(): NotifyEntity.fromJson,
    (OrderLogEntity).toString(): OrderLogEntity.fromJson,
    (PackageSearchEntity).toString(): PackageSearchEntity.fromJson,
    (PackageViewEntity).toString(): PackageViewEntity.fromJson,
    (PackageViewSjEntity).toString(): PackageViewSjEntity.fromJson,
    (PhotoEntity).toString(): PhotoEntity.fromJson,
    (RechargeEntity).toString(): RechargeEntity.fromJson,
    (ScanItemEntity).toString(): ScanItemEntity.fromJson,
    (ShopCabinetLocationEntity).toString(): ShopCabinetLocationEntity.fromJson,
    (CabinetItemEntity).toString(): CabinetItemEntity.fromJson,
    (CabinetBoxItem).toString(): CabinetBoxItem.fromJson,
    (ShopCourierEntity).toString(): ShopCourierEntity.fromJson,
    (TeamMangerEntity).toString(): TeamMangerEntity.fromJson,
    (TodaySummaryEntity).toString(): TodaySummaryEntity.fromJson,
    (TradeRecordEntity).toString(): TradeRecordEntity.fromJson,
    (UpdateAppEntity).toString(): UpdateAppEntity.fromJson,
    (UserAccountEntity).toString(): UserAccountEntity.fromJson,
    (UserAccountExt).toString(): UserAccountExt.fromJson,
    (UserEntity).toString(): UserEntity.fromJson,
    (UserInfoEntity).toString(): UserInfoEntity.fromJson,
    (UserPayAccountEntity).toString(): UserPayAccountEntity.fromJson,
    (WalletEntity).toString(): WalletEntity.fromJson,
    (WalletSummaryEntity).toString(): WalletSummaryEntity.fromJson,
    (WithdrawalRecordEntity).toString(): WithdrawalRecordEntity.fromJson,
  };

  bool containsKey(String type) {
    return convertFuncMap.containsKey(type);
  }

  JsonConvertFunction? operator [](String key) {
    return convertFuncMap[key];
  }
}