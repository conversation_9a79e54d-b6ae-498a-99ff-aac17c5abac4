import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_box_useable_entity.dart';

CabinetBoxUseableEntity $CabinetBoxUseableEntityFromJson(Map<String, dynamic> json) {
  final CabinetBoxUseableEntity cabinetBoxUseableEntity = CabinetBoxUseableEntity();
  final String? code = jsonConvert.convert<String>(json['code']);
  if (code != null) {
    cabinetBoxUseableEntity.code = code;
  }
  final String? cabinetId = jsonConvert.convert<String>(json['cabinetId']);
  if (cabinetId != null) {
    cabinetBoxUseableEntity.cabinetId = cabinetId;
  }
  final String? cabinetName = jsonConvert.convert<String>(json['cabinetName']);
  if (cabinetName != null) {
    cabinetBoxUseableEntity.cabinetName = cabinetName;
  }
  final int? superCount = jsonConvert.convert<int>(json['superCount']);
  if (superCount != null) {
    cabinetBoxUseableEntity.superCount = superCount;
  }
  final int? hugeCount = jsonConvert.convert<int>(json['hugeCount']);
  if (hugeCount != null) {
    cabinetBoxUseableEntity.hugeCount = hugeCount;
  }
  final int? largeCount = jsonConvert.convert<int>(json['largeCount']);
  if (largeCount != null) {
    cabinetBoxUseableEntity.largeCount = largeCount;
  }
  final int? mediumCount = jsonConvert.convert<int>(json['mediumCount']);
  if (mediumCount != null) {
    cabinetBoxUseableEntity.mediumCount = mediumCount;
  }
  final int? smallCount = jsonConvert.convert<int>(json['smallCount']);
  if (smallCount != null) {
    cabinetBoxUseableEntity.smallCount = smallCount;
  }
  final int? miniCount = jsonConvert.convert<int>(json['miniCount']);
  if (miniCount != null) {
    cabinetBoxUseableEntity.miniCount = miniCount;
  }
  final int? microCount = jsonConvert.convert<int>(json['microCount']);
  if (microCount != null) {
    cabinetBoxUseableEntity.microCount = microCount;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    cabinetBoxUseableEntity.total = total;
  }
  return cabinetBoxUseableEntity;
}

Map<String, dynamic> $CabinetBoxUseableEntityToJson(CabinetBoxUseableEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['code'] = entity.code;
  data['cabinetId'] = entity.cabinetId;
  data['cabinetName'] = entity.cabinetName;
  data['superCount'] = entity.superCount;
  data['hugeCount'] = entity.hugeCount;
  data['largeCount'] = entity.largeCount;
  data['mediumCount'] = entity.mediumCount;
  data['smallCount'] = entity.smallCount;
  data['miniCount'] = entity.miniCount;
  data['microCount'] = entity.microCount;
  data['total'] = entity.total;
  return data;
}

extension CabinetBoxUseableEntityExtension on CabinetBoxUseableEntity {
  CabinetBoxUseableEntity copyWith({
    String? code,
    String? cabinetId,
    String? cabinetName,
    int? superCount,
    int? hugeCount,
    int? largeCount,
    int? mediumCount,
    int? smallCount,
    int? miniCount,
    int? microCount,
    int? total,
  }) {
    return CabinetBoxUseableEntity()
      ..code = code ?? this.code
      ..cabinetId = cabinetId ?? this.cabinetId
      ..cabinetName = cabinetName ?? this.cabinetName
      ..superCount = superCount ?? this.superCount
      ..hugeCount = hugeCount ?? this.hugeCount
      ..largeCount = largeCount ?? this.largeCount
      ..mediumCount = mediumCount ?? this.mediumCount
      ..smallCount = smallCount ?? this.smallCount
      ..miniCount = miniCount ?? this.miniCount
      ..microCount = microCount ?? this.microCount
      ..total = total ?? this.total;
  }
}