import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/withdrawal_record_entity.dart';

WithdrawalRecordEntity $WithdrawalRecordEntityFromJson(Map<String, dynamic> json) {
  final WithdrawalRecordEntity withdrawalRecordEntity = WithdrawalRecordEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    withdrawalRecordEntity.id = id;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    withdrawalRecordEntity.createTime = createTime;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    withdrawalRecordEntity.updateTime = updateTime;
  }
  final int? organizationType = jsonConvert.convert<int>(json['organizationType']);
  if (organizationType != null) {
    withdrawalRecordEntity.organizationType = organizationType;
  }
  final String? organizationId = jsonConvert.convert<String>(json['organizationId']);
  if (organizationId != null) {
    withdrawalRecordEntity.organizationId = organizationId;
  }
  final String? accountId = jsonConvert.convert<String>(json['accountId']);
  if (accountId != null) {
    withdrawalRecordEntity.accountId = accountId;
  }
  final String? taskNo = jsonConvert.convert<String>(json['taskNo']);
  if (taskNo != null) {
    withdrawalRecordEntity.taskNo = taskNo;
  }
  final String? applyTime = jsonConvert.convert<String>(json['applyTime']);
  if (applyTime != null) {
    withdrawalRecordEntity.applyTime = applyTime;
  }
  final String? applyYm = jsonConvert.convert<String>(json['applyYm']);
  if (applyYm != null) {
    withdrawalRecordEntity.applyYm = applyYm;
  }
  final String? applyYmd = jsonConvert.convert<String>(json['applyYmd']);
  if (applyYmd != null) {
    withdrawalRecordEntity.applyYmd = applyYmd;
  }
  final int? taskTargetType = jsonConvert.convert<int>(json['taskTargetType']);
  if (taskTargetType != null) {
    withdrawalRecordEntity.taskTargetType = taskTargetType;
  }
  final String? taskTargetId = jsonConvert.convert<String>(json['taskTargetId']);
  if (taskTargetId != null) {
    withdrawalRecordEntity.taskTargetId = taskTargetId;
  }
  final String? taskTargetName = jsonConvert.convert<String>(json['taskTargetName']);
  if (taskTargetName != null) {
    withdrawalRecordEntity.taskTargetName = taskTargetName;
  }
  final int? withdrawalChannel = jsonConvert.convert<int>(json['withdrawalChannel']);
  if (withdrawalChannel != null) {
    withdrawalRecordEntity.withdrawalChannel = withdrawalChannel;
  }
  final int? withdrawalMoney = jsonConvert.convert<int>(json['withdrawalMoney']);
  if (withdrawalMoney != null) {
    withdrawalRecordEntity.withdrawalMoney = withdrawalMoney;
  }
  final int? withdrawalServiceFee = jsonConvert.convert<int>(json['withdrawalServiceFee']);
  if (withdrawalServiceFee != null) {
    withdrawalRecordEntity.withdrawalServiceFee = withdrawalServiceFee;
  }
  final int? money = jsonConvert.convert<int>(json['money']);
  if (money != null) {
    withdrawalRecordEntity.money = money;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    withdrawalRecordEntity.status = status;
  }
  return withdrawalRecordEntity;
}

Map<String, dynamic> $WithdrawalRecordEntityToJson(WithdrawalRecordEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['createTime'] = entity.createTime;
  data['updateTime'] = entity.updateTime;
  data['organizationType'] = entity.organizationType;
  data['organizationId'] = entity.organizationId;
  data['accountId'] = entity.accountId;
  data['taskNo'] = entity.taskNo;
  data['applyTime'] = entity.applyTime;
  data['applyYm'] = entity.applyYm;
  data['applyYmd'] = entity.applyYmd;
  data['taskTargetType'] = entity.taskTargetType;
  data['taskTargetId'] = entity.taskTargetId;
  data['taskTargetName'] = entity.taskTargetName;
  data['withdrawalChannel'] = entity.withdrawalChannel;
  data['withdrawalMoney'] = entity.withdrawalMoney;
  data['withdrawalServiceFee'] = entity.withdrawalServiceFee;
  data['money'] = entity.money;
  data['status'] = entity.status;
  return data;
}

extension WithdrawalRecordEntityExtension on WithdrawalRecordEntity {
  WithdrawalRecordEntity copyWith({
    String? id,
    String? createTime,
    String? updateTime,
    int? organizationType,
    String? organizationId,
    String? accountId,
    String? taskNo,
    String? applyTime,
    String? applyYm,
    String? applyYmd,
    int? taskTargetType,
    String? taskTargetId,
    String? taskTargetName,
    int? withdrawalChannel,
    int? withdrawalMoney,
    int? withdrawalServiceFee,
    int? money,
    int? status,
  }) {
    return WithdrawalRecordEntity()
      ..id = id ?? this.id
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime
      ..organizationType = organizationType ?? this.organizationType
      ..organizationId = organizationId ?? this.organizationId
      ..accountId = accountId ?? this.accountId
      ..taskNo = taskNo ?? this.taskNo
      ..applyTime = applyTime ?? this.applyTime
      ..applyYm = applyYm ?? this.applyYm
      ..applyYmd = applyYmd ?? this.applyYmd
      ..taskTargetType = taskTargetType ?? this.taskTargetType
      ..taskTargetId = taskTargetId ?? this.taskTargetId
      ..taskTargetName = taskTargetName ?? this.taskTargetName
      ..withdrawalChannel = withdrawalChannel ?? this.withdrawalChannel
      ..withdrawalMoney = withdrawalMoney ?? this.withdrawalMoney
      ..withdrawalServiceFee = withdrawalServiceFee ?? this.withdrawalServiceFee
      ..money = money ?? this.money
      ..status = status ?? this.status;
  }
}