import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/team_manger_entity.dart';

TeamMangerEntity $TeamMangerEntityFromJson(Map<String, dynamic> json) {
  final TeamMangerEntity teamMangerEntity = TeamMangerEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    teamMangerEntity.id = id;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    teamMangerEntity.createTime = createTime;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    teamMangerEntity.updateTime = updateTime;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    teamMangerEntity.userId = userId;
  }
  final String? shopId = jsonConvert.convert<String>(json['shopId']);
  if (shopId != null) {
    teamMangerEntity.shopId = shopId;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    teamMangerEntity.name = name;
  }
  final int? hasAdmin = jsonConvert.convert<int>(json['hasAdmin']);
  if (hasAdmin != null) {
    teamMangerEntity.hasAdmin = hasAdmin;
  }
  final int? hasReal = jsonConvert.convert<int>(json['hasReal']);
  if (hasReal != null) {
    teamMangerEntity.hasReal = hasReal;
  }
  final int? hasWallet = jsonConvert.convert<int>(json['hasWallet']);
  if (hasWallet != null) {
    teamMangerEntity.hasWallet = hasWallet;
  }
  final String? realName = jsonConvert.convert<String>(json['realName']);
  if (realName != null) {
    teamMangerEntity.realName = realName;
  }
  final String? idNumber = jsonConvert.convert<String>(json['idNumber']);
  if (idNumber != null) {
    teamMangerEntity.idNumber = idNumber;
  }
  final int? userType = jsonConvert.convert<int>(json['userType']);
  if (userType != null) {
    teamMangerEntity.userType = userType;
  }
  final String? username = jsonConvert.convert<String>(json['username']);
  if (username != null) {
    teamMangerEntity.username = username;
  }
  final String? nickname = jsonConvert.convert<String>(json['nickname']);
  if (nickname != null) {
    teamMangerEntity.nickname = nickname;
  }
  final bool? sex = jsonConvert.convert<bool>(json['sex']);
  if (sex != null) {
    teamMangerEntity.sex = sex;
  }
  final String? phone = jsonConvert.convert<String>(json['phone']);
  if (phone != null) {
    teamMangerEntity.phone = phone;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    teamMangerEntity.status = status;
  }
  final String? organizationId = jsonConvert.convert<String>(json['organizationId']);
  if (organizationId != null) {
    teamMangerEntity.organizationId = organizationId;
  }
  return teamMangerEntity;
}

Map<String, dynamic> $TeamMangerEntityToJson(TeamMangerEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['createTime'] = entity.createTime;
  data['updateTime'] = entity.updateTime;
  data['userId'] = entity.userId;
  data['shopId'] = entity.shopId;
  data['name'] = entity.name;
  data['hasAdmin'] = entity.hasAdmin;
  data['hasReal'] = entity.hasReal;
  data['hasWallet'] = entity.hasWallet;
  data['realName'] = entity.realName;
  data['idNumber'] = entity.idNumber;
  data['userType'] = entity.userType;
  data['username'] = entity.username;
  data['nickname'] = entity.nickname;
  data['sex'] = entity.sex;
  data['phone'] = entity.phone;
  data['status'] = entity.status;
  data['organizationId'] = entity.organizationId;
  return data;
}

extension TeamMangerEntityExtension on TeamMangerEntity {
  TeamMangerEntity copyWith({
    String? id,
    String? createTime,
    String? updateTime,
    String? userId,
    String? shopId,
    String? name,
    int? hasAdmin,
    int? hasReal,
    int? hasWallet,
    String? realName,
    String? idNumber,
    int? userType,
    String? username,
    String? nickname,
    bool? sex,
    String? phone,
    int? status,
    String? organizationId,
  }) {
    return TeamMangerEntity()
      ..id = id ?? this.id
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime
      ..userId = userId ?? this.userId
      ..shopId = shopId ?? this.shopId
      ..name = name ?? this.name
      ..hasAdmin = hasAdmin ?? this.hasAdmin
      ..hasReal = hasReal ?? this.hasReal
      ..hasWallet = hasWallet ?? this.hasWallet
      ..realName = realName ?? this.realName
      ..idNumber = idNumber ?? this.idNumber
      ..userType = userType ?? this.userType
      ..username = username ?? this.username
      ..nickname = nickname ?? this.nickname
      ..sex = sex ?? this.sex
      ..phone = phone ?? this.phone
      ..status = status ?? this.status
      ..organizationId = organizationId ?? this.organizationId;
  }
}