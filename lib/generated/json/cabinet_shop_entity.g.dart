import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_shop_entity.dart';

CabinetShopEntity $CabinetShopEntityFromJson(Map<String, dynamic> json) {
  final CabinetShopEntity cabinetShopEntity = CabinetShopEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    cabinetShopEntity.id = id;
  }
  final String? createBy = jsonConvert.convert<String>(json['createBy']);
  if (createBy != null) {
    cabinetShopEntity.createBy = createBy;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    cabinetShopEntity.createTime = createTime;
  }
  final String? updateBy = jsonConvert.convert<String>(json['updateBy']);
  if (updateBy != null) {
    cabinetShopEntity.updateBy = updateBy;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    cabinetShopEntity.updateTime = updateTime;
  }
  final String? siteId = jsonConvert.convert<String>(json['siteId']);
  if (siteId != null) {
    cabinetShopEntity.siteId = siteId;
  }
  final String? shopId = jsonConvert.convert<String>(json['shopId']);
  if (shopId != null) {
    cabinetShopEntity.shopId = shopId;
  }
  final String? code = jsonConvert.convert<String>(json['code']);
  if (code != null) {
    cabinetShopEntity.code = code;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    cabinetShopEntity.name = name;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    cabinetShopEntity.type = type;
  }
  final String? linkman = jsonConvert.convert<String>(json['linkman']);
  if (linkman != null) {
    cabinetShopEntity.linkman = linkman;
  }
  final String? mobile = jsonConvert.convert<String>(json['mobile']);
  if (mobile != null) {
    cabinetShopEntity.mobile = mobile;
  }
  final int? smsPrice = jsonConvert.convert<int>(json['smsPrice']);
  if (smsPrice != null) {
    cabinetShopEntity.smsPrice = smsPrice;
  }
  final int? wxPrice = jsonConvert.convert<int>(json['wxPrice']);
  if (wxPrice != null) {
    cabinetShopEntity.wxPrice = wxPrice;
  }
  final int? noticeType = jsonConvert.convert<int>(json['noticeType']);
  if (noticeType != null) {
    cabinetShopEntity.noticeType = noticeType;
  }
  final int? cabinetType = jsonConvert.convert<int>(json['cabinetType']);
  if (cabinetType != null) {
    cabinetShopEntity.cabinetType = cabinetType;
  }
  final String? servicePhone = jsonConvert.convert<String>(json['servicePhone']);
  if (servicePhone != null) {
    cabinetShopEntity.servicePhone = servicePhone;
  }
  final String? hostActivationCode1 = jsonConvert.convert<String>(json['hostActivationCode1']);
  if (hostActivationCode1 != null) {
    cabinetShopEntity.hostActivationCode1 = hostActivationCode1;
  }
  final int? hostStatus1 = jsonConvert.convert<int>(json['hostStatus1']);
  if (hostStatus1 != null) {
    cabinetShopEntity.hostStatus1 = hostStatus1;
  }
  final String? hostVersion1 = jsonConvert.convert<String>(json['hostVersion1']);
  if (hostVersion1 != null) {
    cabinetShopEntity.hostVersion1 = hostVersion1;
  }
  final String? hostSerialPort1 = jsonConvert.convert<String>(json['hostSerialPort1']);
  if (hostSerialPort1 != null) {
    cabinetShopEntity.hostSerialPort1 = hostSerialPort1;
  }
  final String? hostBaudRate1 = jsonConvert.convert<String>(json['hostBaudRate1']);
  if (hostBaudRate1 != null) {
    cabinetShopEntity.hostBaudRate1 = hostBaudRate1;
  }
  final String? hostFactory1 = jsonConvert.convert<String>(json['hostFactory1']);
  if (hostFactory1 != null) {
    cabinetShopEntity.hostFactory1 = hostFactory1;
  }
  final String? provinceCode = jsonConvert.convert<String>(json['provinceCode']);
  if (provinceCode != null) {
    cabinetShopEntity.provinceCode = provinceCode;
  }
  final String? provinceName = jsonConvert.convert<String>(json['provinceName']);
  if (provinceName != null) {
    cabinetShopEntity.provinceName = provinceName;
  }
  final String? cityCode = jsonConvert.convert<String>(json['cityCode']);
  if (cityCode != null) {
    cabinetShopEntity.cityCode = cityCode;
  }
  final String? cityName = jsonConvert.convert<String>(json['cityName']);
  if (cityName != null) {
    cabinetShopEntity.cityName = cityName;
  }
  final String? areaCode = jsonConvert.convert<String>(json['areaCode']);
  if (areaCode != null) {
    cabinetShopEntity.areaCode = areaCode;
  }
  final String? areaName = jsonConvert.convert<String>(json['areaName']);
  if (areaName != null) {
    cabinetShopEntity.areaName = areaName;
  }
  final String? streetCode = jsonConvert.convert<String>(json['streetCode']);
  if (streetCode != null) {
    cabinetShopEntity.streetCode = streetCode;
  }
  final String? streetName = jsonConvert.convert<String>(json['streetName']);
  if (streetName != null) {
    cabinetShopEntity.streetName = streetName;
  }
  final String? address = jsonConvert.convert<String>(json['address']);
  if (address != null) {
    cabinetShopEntity.address = address;
  }
  final double? longitude = jsonConvert.convert<double>(json['longitude']);
  if (longitude != null) {
    cabinetShopEntity.longitude = longitude;
  }
  final double? latitude = jsonConvert.convert<double>(json['latitude']);
  if (latitude != null) {
    cabinetShopEntity.latitude = latitude;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    cabinetShopEntity.status = status;
  }
  final int? applyTime = jsonConvert.convert<int>(json['applyTime']);
  if (applyTime != null) {
    cabinetShopEntity.applyTime = applyTime;
  }
  final int? hasFeederLine = jsonConvert.convert<int>(json['hasFeederLine']);
  if (hasFeederLine != null) {
    cabinetShopEntity.hasFeederLine = hasFeederLine;
  }
  final String? deleted = jsonConvert.convert<String>(json['deleted']);
  if (deleted != null) {
    cabinetShopEntity.deleted = deleted;
  }
  final int? hugeCount = jsonConvert.convert<int>(json['hugeCount']);
  if (hugeCount != null) {
    cabinetShopEntity.hugeCount = hugeCount;
  }
  final int? largeCount = jsonConvert.convert<int>(json['largeCount']);
  if (largeCount != null) {
    cabinetShopEntity.largeCount = largeCount;
  }
  final int? mediumCount = jsonConvert.convert<int>(json['mediumCount']);
  if (mediumCount != null) {
    cabinetShopEntity.mediumCount = mediumCount;
  }
  final int? smallCount = jsonConvert.convert<int>(json['smallCount']);
  if (smallCount != null) {
    cabinetShopEntity.smallCount = smallCount;
  }
  final int? miniCount = jsonConvert.convert<int>(json['miniCount']);
  if (miniCount != null) {
    cabinetShopEntity.miniCount = miniCount;
  }
  final int? superCount = jsonConvert.convert<int>(json['superCount']);
  if (superCount != null) {
    cabinetShopEntity.superCount = superCount;
  }
  final int? microCount = jsonConvert.convert<int>(json['microCount']);
  if (microCount != null) {
    cabinetShopEntity.microCount = microCount;
  }
  return cabinetShopEntity;
}

Map<String, dynamic> $CabinetShopEntityToJson(CabinetShopEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['createBy'] = entity.createBy;
  data['createTime'] = entity.createTime;
  data['updateBy'] = entity.updateBy;
  data['updateTime'] = entity.updateTime;
  data['siteId'] = entity.siteId;
  data['shopId'] = entity.shopId;
  data['code'] = entity.code;
  data['name'] = entity.name;
  data['type'] = entity.type;
  data['linkman'] = entity.linkman;
  data['mobile'] = entity.mobile;
  data['smsPrice'] = entity.smsPrice;
  data['wxPrice'] = entity.wxPrice;
  data['noticeType'] = entity.noticeType;
  data['cabinetType'] = entity.cabinetType;
  data['servicePhone'] = entity.servicePhone;
  data['hostActivationCode1'] = entity.hostActivationCode1;
  data['hostStatus1'] = entity.hostStatus1;
  data['hostVersion1'] = entity.hostVersion1;
  data['hostSerialPort1'] = entity.hostSerialPort1;
  data['hostBaudRate1'] = entity.hostBaudRate1;
  data['hostFactory1'] = entity.hostFactory1;
  data['provinceCode'] = entity.provinceCode;
  data['provinceName'] = entity.provinceName;
  data['cityCode'] = entity.cityCode;
  data['cityName'] = entity.cityName;
  data['areaCode'] = entity.areaCode;
  data['areaName'] = entity.areaName;
  data['streetCode'] = entity.streetCode;
  data['streetName'] = entity.streetName;
  data['address'] = entity.address;
  data['longitude'] = entity.longitude;
  data['latitude'] = entity.latitude;
  data['status'] = entity.status;
  data['applyTime'] = entity.applyTime;
  data['hasFeederLine'] = entity.hasFeederLine;
  data['deleted'] = entity.deleted;
  data['hugeCount'] = entity.hugeCount;
  data['largeCount'] = entity.largeCount;
  data['mediumCount'] = entity.mediumCount;
  data['smallCount'] = entity.smallCount;
  data['miniCount'] = entity.miniCount;
  data['superCount'] = entity.superCount;
  data['microCount'] = entity.microCount;
  return data;
}

extension CabinetShopEntityExtension on CabinetShopEntity {
  CabinetShopEntity copyWith({
    String? id,
    String? createBy,
    String? createTime,
    String? updateBy,
    String? updateTime,
    String? siteId,
    String? shopId,
    String? code,
    String? name,
    int? type,
    String? linkman,
    String? mobile,
    int? smsPrice,
    int? wxPrice,
    int? noticeType,
    int? cabinetType,
    String? servicePhone,
    String? hostActivationCode1,
    int? hostStatus1,
    String? hostVersion1,
    String? hostSerialPort1,
    String? hostBaudRate1,
    String? hostFactory1,
    String? provinceCode,
    String? provinceName,
    String? cityCode,
    String? cityName,
    String? areaCode,
    String? areaName,
    String? streetCode,
    String? streetName,
    String? address,
    double? longitude,
    double? latitude,
    int? status,
    int? applyTime,
    int? hasFeederLine,
    String? deleted,
    int? hugeCount,
    int? largeCount,
    int? mediumCount,
    int? smallCount,
    int? miniCount,
    int? superCount,
    int? microCount,
  }) {
    return CabinetShopEntity()
      ..id = id ?? this.id
      ..createBy = createBy ?? this.createBy
      ..createTime = createTime ?? this.createTime
      ..updateBy = updateBy ?? this.updateBy
      ..updateTime = updateTime ?? this.updateTime
      ..siteId = siteId ?? this.siteId
      ..shopId = shopId ?? this.shopId
      ..code = code ?? this.code
      ..name = name ?? this.name
      ..type = type ?? this.type
      ..linkman = linkman ?? this.linkman
      ..mobile = mobile ?? this.mobile
      ..smsPrice = smsPrice ?? this.smsPrice
      ..wxPrice = wxPrice ?? this.wxPrice
      ..noticeType = noticeType ?? this.noticeType
      ..cabinetType = cabinetType ?? this.cabinetType
      ..servicePhone = servicePhone ?? this.servicePhone
      ..hostActivationCode1 = hostActivationCode1 ?? this.hostActivationCode1
      ..hostStatus1 = hostStatus1 ?? this.hostStatus1
      ..hostVersion1 = hostVersion1 ?? this.hostVersion1
      ..hostSerialPort1 = hostSerialPort1 ?? this.hostSerialPort1
      ..hostBaudRate1 = hostBaudRate1 ?? this.hostBaudRate1
      ..hostFactory1 = hostFactory1 ?? this.hostFactory1
      ..provinceCode = provinceCode ?? this.provinceCode
      ..provinceName = provinceName ?? this.provinceName
      ..cityCode = cityCode ?? this.cityCode
      ..cityName = cityName ?? this.cityName
      ..areaCode = areaCode ?? this.areaCode
      ..areaName = areaName ?? this.areaName
      ..streetCode = streetCode ?? this.streetCode
      ..streetName = streetName ?? this.streetName
      ..address = address ?? this.address
      ..longitude = longitude ?? this.longitude
      ..latitude = latitude ?? this.latitude
      ..status = status ?? this.status
      ..applyTime = applyTime ?? this.applyTime
      ..hasFeederLine = hasFeederLine ?? this.hasFeederLine
      ..hugeCount = hugeCount ?? this.hugeCount
      ..largeCount = largeCount ?? this.largeCount
      ..mediumCount = mediumCount ?? this.mediumCount
      ..smallCount = smallCount ?? this.smallCount
      ..miniCount = miniCount ?? this.miniCount
      ..superCount = superCount ?? this.superCount
      ..microCount = microCount ?? this.microCount
      ..deleted = deleted ?? this.deleted;
  }
}
