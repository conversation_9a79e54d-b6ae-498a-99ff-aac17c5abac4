import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/action_entity.dart';

ActionEntity $ActionEntityFromJson(Map<String, dynamic> json) {
  final ActionEntity actionEntity = ActionEntity();
  final String? actionCode = jsonConvert.convert<String>(json['actionCode']);
  if (actionCode != null) {
    actionEntity.actionCode = actionCode;
  }
  final String? actionName = jsonConvert.convert<String>(json['actionName']);
  if (actionName != null) {
    actionEntity.actionName = actionName;
  }
  final String? actionTime = jsonConvert.convert<String>(json['actionTime']);
  if (actionTime != null) {
    actionEntity.actionTime = actionTime;
  }
  final String? desc = jsonConvert.convert<String>(json['desc']);
  if (desc != null) {
    actionEntity.desc = desc;
  }
  final String? descPhotoUrl = jsonConvert.convert<String>(json['descPhotoUrl']);
  if (descPhotoUrl != null) {
    actionEntity.descPhotoUrl = descPhotoUrl;
  }
  return actionEntity;
}

Map<String, dynamic> $ActionEntityToJson(ActionEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['actionCode'] = entity.actionCode;
  data['actionName'] = entity.actionName;
  data['actionTime'] = entity.actionTime;
  data['desc'] = entity.desc;
  data['descPhotoUrl'] = entity.descPhotoUrl;
  return data;
}

extension ActionEntityExtension on ActionEntity {
  ActionEntity copyWith({
    String? actionCode,
    String? actionName,
    String? actionTime,
    String? desc,
    String? descPhotoUrl,
  }) {
    return ActionEntity()
      ..actionCode = actionCode ?? this.actionCode
      ..actionName = actionName ?? this.actionName
      ..actionTime = actionTime ?? this.actionTime
      ..desc = desc ?? this.desc
      ..descPhotoUrl = descPhotoUrl ?? this.descPhotoUrl;
  }
}