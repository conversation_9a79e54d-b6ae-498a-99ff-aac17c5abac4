import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/customer_mobile_entity.dart';

CustomerMobileEntity $CustomerMobileEntityFromJson(Map<String, dynamic> json) {
  final CustomerMobileEntity customerMobileEntity = CustomerMobileEntity();
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    customerMobileEntity.name = name;
  }
  final String? mobile = jsonConvert.convert<String>(json['mobile']);
  if (mobile != null) {
    customerMobileEntity.mobile = mobile;
  }
  final String? label = jsonConvert.convert<String>(json['label']);
  if (label != null) {
    customerMobileEntity.label = label;
  }
  final int? labelNotice = jsonConvert.convert<int>(json['labelNotice']);
  if (labelNotice != null) {
    customerMobileEntity.labelNotice = labelNotice;
  }
  final int? isNew = jsonConvert.convert<int>(json['isNew']);
  if (isNew != null) {
    customerMobileEntity.isNew = isNew;
  }
  final List<CustomerMobileOrderList>? orderList = (json['orderList'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<CustomerMobileOrderList>(e) as CustomerMobileOrderList).toList();
  if (orderList != null) {
    customerMobileEntity.orderList = orderList;
  }
  return customerMobileEntity;
}

Map<String, dynamic> $CustomerMobileEntityToJson(CustomerMobileEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['name'] = entity.name;
  data['mobile'] = entity.mobile;
  data['label'] = entity.label;
  data['labelNotice'] = entity.labelNotice;
  data['isNew'] = entity.isNew;
  data['orderList'] = entity.orderList.map((v) => v.toJson()).toList();
  return data;
}

extension CustomerMobileEntityExtension on CustomerMobileEntity {
  CustomerMobileEntity copyWith({
    String? name,
    String? mobile,
    String? label,
    int? labelNotice,
    int? isNew,
    List<CustomerMobileOrderList>? orderList,
  }) {
    return CustomerMobileEntity()
      ..name = name ?? this.name
      ..mobile = mobile ?? this.mobile
      ..label = label ?? this.label
      ..labelNotice = labelNotice ?? this.labelNotice
      ..isNew = isNew ?? this.isNew
      ..orderList = orderList ?? this.orderList;
  }
}

CustomerMobileOrderList $CustomerMobileOrderListFromJson(Map<String, dynamic> json) {
  final CustomerMobileOrderList customerMobileOrderList = CustomerMobileOrderList();
  final String? brandCode = jsonConvert.convert<String>(json['brandCode']);
  if (brandCode != null) {
    customerMobileOrderList.brandCode = brandCode;
  }
  final String? waybillNo = jsonConvert.convert<String>(json['waybillNo']);
  if (waybillNo != null) {
    customerMobileOrderList.waybillNo = waybillNo;
  }
  final String? keepEffectTime = jsonConvert.convert<String>(json['keepEffectTime']);
  if (keepEffectTime != null) {
    customerMobileOrderList.keepEffectTime = keepEffectTime;
  }
  final String? cabinetName = jsonConvert.convert<String>(json['cabinetName']);
  if (cabinetName != null) {
    customerMobileOrderList.cabinetName = cabinetName;
  }
  final String? cabinetId = jsonConvert.convert<String>(json['cabinetId']);
  if (cabinetId != null) {
    customerMobileOrderList.cabinetId = cabinetId;
  }
  final String? cabinetBoxId = jsonConvert.convert<String>(json['cabinetBoxId']);
  if (cabinetBoxId != null) {
    customerMobileOrderList.cabinetBoxId = cabinetBoxId;
  }
  final int? cabinetBoxType = jsonConvert.convert<int>(json['cabinetBoxType']);
  if (cabinetBoxType != null) {
    customerMobileOrderList.cabinetBoxType = cabinetBoxType;
  }
  final String? cabinetBoxLabel = jsonConvert.convert<String>(json['cabinetBoxLabel']);
  if (cabinetBoxLabel != null) {
    customerMobileOrderList.cabinetBoxLabel = cabinetBoxLabel;
  }
  return customerMobileOrderList;
}

Map<String, dynamic> $CustomerMobileOrderListToJson(CustomerMobileOrderList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['brandCode'] = entity.brandCode;
  data['waybillNo'] = entity.waybillNo;
  data['keepEffectTime'] = entity.keepEffectTime;
  data['cabinetName'] = entity.cabinetName;
  data['cabinetId'] = entity.cabinetId;
  data['cabinetBoxId'] = entity.cabinetBoxId;
  data['cabinetBoxType'] = entity.cabinetBoxType;
  data['cabinetBoxLabel'] = entity.cabinetBoxLabel;
  return data;
}

extension CustomerMobileOrderListExtension on CustomerMobileOrderList {
  CustomerMobileOrderList copyWith({
    String? brandCode,
    String? waybillNo,
    String? keepEffectTime,
    String? cabinetName,
    String? cabinetId,
    String? cabinetBoxId,
    int? cabinetBoxType,
    String? cabinetBoxLabel,
  }) {
    return CustomerMobileOrderList()
      ..brandCode = brandCode ?? this.brandCode
      ..waybillNo = waybillNo ?? this.waybillNo
      ..keepEffectTime = keepEffectTime ?? this.keepEffectTime
      ..cabinetName = cabinetName ?? this.cabinetName
      ..cabinetId = cabinetId ?? this.cabinetId
      ..cabinetBoxId = cabinetBoxId ?? this.cabinetBoxId
      ..cabinetBoxType = cabinetBoxType ?? this.cabinetBoxType
      ..cabinetBoxLabel = cabinetBoxLabel ?? this.cabinetBoxLabel;
  }
}