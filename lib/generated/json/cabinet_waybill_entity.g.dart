import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_waybill_entity.dart';

CabinetWaybillEntity $CabinetWaybillEntityFromJson(Map<String, dynamic> json) {
  final CabinetWaybillEntity cabinetWaybillEntity = CabinetWaybillEntity();
  final String? waybillNo = jsonConvert.convert<String>(json['waybillNo']);
  if (waybillNo != null) {
    cabinetWaybillEntity.waybillNo = waybillNo;
  }
  final String? brandCode = jsonConvert.convert<String>(json['brandCode']);
  if (brandCode != null) {
    cabinetWaybillEntity.brandCode = brandCode;
  }
  final String? brandName = jsonConvert.convert<String>(json['brandName']);
  if (brandName != null) {
    cabinetWaybillEntity.brandName = brandName;
  }
  final int? waybillStatus = jsonConvert.convert<int>(json['waybillStatus']);
  if (waybillStatus != null) {
    cabinetWaybillEntity.waybillStatus = waybillStatus;
  }
  final int? waybillType = jsonConvert.convert<int>(json['waybillType']);
  if (waybillType != null) {
    cabinetWaybillEntity.waybillType = waybillType;
  }
  final String? receiverMobile = jsonConvert.convert<String>(json['receiverMobile']);
  if (receiverMobile != null) {
    cabinetWaybillEntity.receiverMobile = receiverMobile;
  }
  final String? checkCode = jsonConvert.convert<String>(json['checkCode']);
  if (checkCode != null) {
    cabinetWaybillEntity.checkCode = checkCode;
  }
  final int? messageState = jsonConvert.convert<int>(json['messageState']);
  if (messageState != null) {
    cabinetWaybillEntity.messageState = messageState;
  }
  final int? messageType = jsonConvert.convert<int>(json['messageType']);
  if (messageType != null) {
    cabinetWaybillEntity.messageType = messageType;
  }
  final String? inboundTime = jsonConvert.convert<String>(json['inboundTime']);
  if (inboundTime != null) {
    cabinetWaybillEntity.inboundTime = inboundTime;
  }
  final String? outboundTime = jsonConvert.convert<String>(json['outboundTime']);
  if (outboundTime != null) {
    cabinetWaybillEntity.outboundTime = outboundTime;
  }
  final double? effectTimeFee = jsonConvert.convert<double>(json['effectTimeFee']);
  if (effectTimeFee != null) {
    cabinetWaybillEntity.effectTimeFee = effectTimeFee;
  }
  final String? cabinetName = jsonConvert.convert<String>(json['cabinetName']);
  if (cabinetName != null) {
    cabinetWaybillEntity.cabinetName = cabinetName;
  }
  final String? cabinetBoxNo = jsonConvert.convert<String>(json['cabinetBoxNo']);
  if (cabinetBoxNo != null) {
    cabinetWaybillEntity.cabinetBoxNo = cabinetBoxNo;
  }
  return cabinetWaybillEntity;
}

Map<String, dynamic> $CabinetWaybillEntityToJson(CabinetWaybillEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['waybillNo'] = entity.waybillNo;
  data['brandCode'] = entity.brandCode;
  data['brandName'] = entity.brandName;
  data['waybillStatus'] = entity.waybillStatus;
  data['waybillType'] = entity.waybillType;
  data['receiverMobile'] = entity.receiverMobile;
  data['checkCode'] = entity.checkCode;
  data['messageState'] = entity.messageState;
  data['messageType'] = entity.messageType;
  data['inboundTime'] = entity.inboundTime;
  data['outboundTime'] = entity.outboundTime;
  data['effectTimeFee'] = entity.effectTimeFee;
  data['cabinetName'] = entity.cabinetName;
  data['cabinetBoxNo'] = entity.cabinetBoxNo;
  return data;
}

extension CabinetWaybillEntityExtension on CabinetWaybillEntity {
  CabinetWaybillEntity copyWith({
    String? waybillNo,
    String? brandCode,
    String? brandName,
    int? waybillStatus,
    int? waybillType,
    String? receiverMobile,
    String? checkCode,
    int? messageState,
    int? messageType,
    String? inboundTime,
    String? outboundTime,
    double? effectTimeFee,
    String? cabinetName,
    String? cabinetBoxNo,
  }) {
    return CabinetWaybillEntity()
      ..waybillNo = waybillNo ?? this.waybillNo
      ..brandCode = brandCode ?? this.brandCode
      ..brandName = brandName ?? this.brandName
      ..waybillStatus = waybillStatus ?? this.waybillStatus
      ..waybillType = waybillType ?? this.waybillType
      ..receiverMobile = receiverMobile ?? this.receiverMobile
      ..checkCode = checkCode ?? this.checkCode
      ..messageState = messageState ?? this.messageState
      ..messageType = messageType ?? this.messageType
      ..inboundTime = inboundTime ?? this.inboundTime
      ..outboundTime = outboundTime ?? this.outboundTime
      ..effectTimeFee = effectTimeFee ?? this.effectTimeFee
      ..cabinetName = cabinetName ?? this.cabinetName
      ..cabinetBoxNo = cabinetBoxNo ?? this.cabinetBoxNo;
  }
}