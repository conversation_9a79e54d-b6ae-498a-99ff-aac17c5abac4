import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/shop_courier_entity.dart';

ShopCourierEntity $ShopCourierEntityFromJson(Map<String, dynamic> json) {
  final ShopCourierEntity shopCourierEntity = ShopCourierEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    shopCourierEntity.id = id;
  }
  final String? courierId = jsonConvert.convert<String>(json['courierId']);
  if (courierId != null) {
    shopCourierEntity.courierId = courierId;
  }
  final String? courierName = jsonConvert.convert<String>(json['courierName']);
  if (courierName != null) {
    shopCourierEntity.courierName = courierName;
  }
  final String? courierMobile = jsonConvert.convert<String>(json['courierMobile']);
  if (courierMobile != null) {
    shopCourierEntity.courierMobile = courierMobile;
  }
  final String? shopAddress = jsonConvert.convert<String>(json['shopAddress']);
  if (shopAddress != null) {
    shopCourierEntity.shopAddress = shopAddress;
  }
  final String? shopLinkman = jsonConvert.convert<String>(json['shopLinkman']);
  if (shopLinkman != null) {
    shopCourierEntity.shopLinkman = shopLinkman;
  }
  final String? shopMobile = jsonConvert.convert<String>(json['shopMobile']);
  if (shopMobile != null) {
    shopCourierEntity.shopMobile = shopMobile;
  }
  final String? shopId = jsonConvert.convert<String>(json['shopId']);
  if (shopId != null) {
    shopCourierEntity.shopId = shopId;
  }
  final String? shopName = jsonConvert.convert<String>(json['shopName']);
  if (shopName != null) {
    shopCourierEntity.shopName = shopName;
  }
  final String? brandCode = jsonConvert.convert<String>(json['brandCode']);
  if (brandCode != null) {
    shopCourierEntity.brandCode = brandCode;
  }
  final double? deliveryFee = jsonConvert.convert<double>(json['deliveryFee']);
  if (deliveryFee != null) {
    shopCourierEntity.deliveryFee = deliveryFee;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    shopCourierEntity.status = status;
  }
  return shopCourierEntity;
}

Map<String, dynamic> $ShopCourierEntityToJson(ShopCourierEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['courierId'] = entity.courierId;
  data['courierName'] = entity.courierName;
  data['courierMobile'] = entity.courierMobile;
  data['shopAddress'] = entity.shopAddress;
  data['shopLinkman'] = entity.shopLinkman;
  data['shopMobile'] = entity.shopMobile;
  data['shopId'] = entity.shopId;
  data['shopName'] = entity.shopName;
  data['brandCode'] = entity.brandCode;
  data['deliveryFee'] = entity.deliveryFee;
  data['status'] = entity.status;
  return data;
}

extension ShopCourierEntityExtension on ShopCourierEntity {
  ShopCourierEntity copyWith({
    String? id,
    String? courierId,
    String? courierName,
    String? courierMobile,
    String? shopAddress,
    String? shopLinkman,
    String? shopMobile,
    String? shopId,
    String? shopName,
    String? brandCode,
    double? deliveryFee,
    int? status,
  }) {
    return ShopCourierEntity()
      ..id = id ?? this.id
      ..courierId = courierId ?? this.courierId
      ..courierName = courierName ?? this.courierName
      ..courierMobile = courierMobile ?? this.courierMobile
      ..shopAddress = shopAddress ?? this.shopAddress
      ..shopLinkman = shopLinkman ?? this.shopLinkman
      ..shopMobile = shopMobile ?? this.shopMobile
      ..shopId = shopId ?? this.shopId
      ..shopName = shopName ?? this.shopName
      ..brandCode = brandCode ?? this.brandCode
      ..deliveryFee = deliveryFee ?? this.deliveryFee
      ..status = status ?? this.status;
  }
}