import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/package_view_sj_entity.dart';

PackageViewSjEntity $PackageViewSjEntityFromJson(Map<String, dynamic> json) {
  final PackageViewSjEntity packageViewSjEntity = PackageViewSjEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    packageViewSjEntity.id = id;
  }
  final String? createBy = jsonConvert.convert<String>(json['createBy']);
  if (createBy != null) {
    packageViewSjEntity.createBy = createBy;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    packageViewSjEntity.createTime = createTime;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    packageViewSjEntity.updateTime = updateTime;
  }
  final int? orderType = jsonConvert.convert<int>(json['orderType']);
  if (orderType != null) {
    packageViewSjEntity.orderType = orderType;
  }
  final int? orderStatus = jsonConvert.convert<int>(json['orderStatus']);
  if (orderStatus != null) {
    packageViewSjEntity.orderStatus = orderStatus;
  }
  final String? orderNo = jsonConvert.convert<String>(json['orderNo']);
  if (orderNo != null) {
    packageViewSjEntity.orderNo = orderNo;
  }
  final String? bizNo = jsonConvert.convert<String>(json['bizNo']);
  if (bizNo != null) {
    packageViewSjEntity.bizNo = bizNo;
  }
  final String? channelId = jsonConvert.convert<String>(json['channelId']);
  if (channelId != null) {
    packageViewSjEntity.channelId = channelId;
  }
  final String? channelName = jsonConvert.convert<String>(json['channelName']);
  if (channelName != null) {
    packageViewSjEntity.channelName = channelName;
  }
  final String? siteId = jsonConvert.convert<String>(json['siteId']);
  if (siteId != null) {
    packageViewSjEntity.siteId = siteId;
  }
  final String? siteName = jsonConvert.convert<String>(json['siteName']);
  if (siteName != null) {
    packageViewSjEntity.siteName = siteName;
  }
  final String? shopId = jsonConvert.convert<String>(json['shopId']);
  if (shopId != null) {
    packageViewSjEntity.shopId = shopId;
  }
  final String? shopName = jsonConvert.convert<String>(json['shopName']);
  if (shopName != null) {
    packageViewSjEntity.shopName = shopName;
  }
  final String? cabinetLocationId = jsonConvert.convert<String>(json['cabinetLocationId']);
  if (cabinetLocationId != null) {
    packageViewSjEntity.cabinetLocationId = cabinetLocationId;
  }
  final String? cabinetLocationCode = jsonConvert.convert<String>(json['cabinetLocationCode']);
  if (cabinetLocationCode != null) {
    packageViewSjEntity.cabinetLocationCode = cabinetLocationCode;
  }
  final String? cabinetLocationName = jsonConvert.convert<String>(json['cabinetLocationName']);
  if (cabinetLocationName != null) {
    packageViewSjEntity.cabinetLocationName = cabinetLocationName;
  }
  final String? cabinetLocationAddress = jsonConvert.convert<String>(json['cabinetLocationAddress']);
  if (cabinetLocationAddress != null) {
    packageViewSjEntity.cabinetLocationAddress = cabinetLocationAddress;
  }
  final String? cabinetId = jsonConvert.convert<String>(json['cabinetId']);
  if (cabinetId != null) {
    packageViewSjEntity.cabinetId = cabinetId;
  }
  final String? cabinetCode = jsonConvert.convert<String>(json['cabinetCode']);
  if (cabinetCode != null) {
    packageViewSjEntity.cabinetCode = cabinetCode;
  }
  final String? cabinetName = jsonConvert.convert<String>(json['cabinetName']);
  if (cabinetName != null) {
    packageViewSjEntity.cabinetName = cabinetName;
  }
  final int? cabinetSerialNo = jsonConvert.convert<int>(json['cabinetSerialNo']);
  if (cabinetSerialNo != null) {
    packageViewSjEntity.cabinetSerialNo = cabinetSerialNo;
  }
  final String? cabinetBoxId = jsonConvert.convert<String>(json['cabinetBoxId']);
  if (cabinetBoxId != null) {
    packageViewSjEntity.cabinetBoxId = cabinetBoxId;
  }
  final int? cabinetBoxType = jsonConvert.convert<int>(json['cabinetBoxType']);
  if (cabinetBoxType != null) {
    packageViewSjEntity.cabinetBoxType = cabinetBoxType;
  }
  final String? cabinetBoxLabel = jsonConvert.convert<String>(json['cabinetBoxLabel']);
  if (cabinetBoxLabel != null) {
    packageViewSjEntity.cabinetBoxLabel = cabinetBoxLabel;
  }
  final int? cabinetBoxPcbNo = jsonConvert.convert<int>(json['cabinetBoxPcbNo']);
  if (cabinetBoxPcbNo != null) {
    packageViewSjEntity.cabinetBoxPcbNo = cabinetBoxPcbNo;
  }
  final int? storeType = jsonConvert.convert<int>(json['storeType']);
  if (storeType != null) {
    packageViewSjEntity.storeType = storeType;
  }
  final String? receiverName = jsonConvert.convert<String>(json['receiverName']);
  if (receiverName != null) {
    packageViewSjEntity.receiverName = receiverName;
  }
  final String? receiverMobile = jsonConvert.convert<String>(json['receiverMobile']);
  if (receiverMobile != null) {
    packageViewSjEntity.receiverMobile = receiverMobile;
  }
  final String? receiverMobileLast4 = jsonConvert.convert<String>(json['receiverMobileLast4']);
  if (receiverMobileLast4 != null) {
    packageViewSjEntity.receiverMobileLast4 = receiverMobileLast4;
  }
  final String? checkCode = jsonConvert.convert<String>(json['checkCode']);
  if (checkCode != null) {
    packageViewSjEntity.checkCode = checkCode;
  }
  final int? messageType = jsonConvert.convert<int>(json['messageType']);
  if (messageType != null) {
    packageViewSjEntity.messageType = messageType;
  }
  final int? messageSmsStatus = jsonConvert.convert<int>(json['messageSmsStatus']);
  if (messageSmsStatus != null) {
    packageViewSjEntity.messageSmsStatus = messageSmsStatus;
  }
  final int? messageWxStatus = jsonConvert.convert<int>(json['messageWxStatus']);
  if (messageWxStatus != null) {
    packageViewSjEntity.messageWxStatus = messageWxStatus;
  }
  final String? inboundUserId = jsonConvert.convert<String>(json['inboundUserId']);
  if (inboundUserId != null) {
    packageViewSjEntity.inboundUserId = inboundUserId;
  }
  final String? inboundUserName = jsonConvert.convert<String>(json['inboundUserName']);
  if (inboundUserName != null) {
    packageViewSjEntity.inboundUserName = inboundUserName;
  }
  final int? inboundUserFee = jsonConvert.convert<int>(json['inboundUserFee']);
  if (inboundUserFee != null) {
    packageViewSjEntity.inboundUserFee = inboundUserFee;
  }
  final String? inboundUserMobile = jsonConvert.convert<String>(json['inboundUserMobile']);
  if (inboundUserMobile != null) {
    packageViewSjEntity.inboundUserMobile = inboundUserMobile;
  }
  final String? inboundTime = jsonConvert.convert<String>(json['inboundTime']);
  if (inboundTime != null) {
    packageViewSjEntity.inboundTime = inboundTime;
  }
  final String? inboundYm = jsonConvert.convert<String>(json['inboundYm']);
  if (inboundYm != null) {
    packageViewSjEntity.inboundYm = inboundYm;
  }
  final String? inboundYmd = jsonConvert.convert<String>(json['inboundYmd']);
  if (inboundYmd != null) {
    packageViewSjEntity.inboundYmd = inboundYmd;
  }
  final int? hasOutbound = jsonConvert.convert<int>(json['hasOutbound']);
  if (hasOutbound != null) {
    packageViewSjEntity.hasOutbound = hasOutbound;
  }
  final int? outboundUserFee = jsonConvert.convert<int>(json['outboundUserFee']);
  if (outboundUserFee != null) {
    packageViewSjEntity.outboundUserFee = outboundUserFee;
  }
  final String? remarks = jsonConvert.convert<String>(json['remarks']);
  if (remarks != null) {
    packageViewSjEntity.remarks = remarks;
  }
  final int? serviceDuration = jsonConvert.convert<int>(json['serviceDuration']);
  if (serviceDuration != null) {
    packageViewSjEntity.serviceDuration = serviceDuration;
  }
  final int? version = jsonConvert.convert<int>(json['version']);
  if (version != null) {
    packageViewSjEntity.version = version;
  }
  final int? sendStatus = jsonConvert.convert<int>(json['sendStatus']);
  if (sendStatus != null) {
    packageViewSjEntity.sendStatus = sendStatus;
  }
  final String? brandCode = jsonConvert.convert<String>(json['brandCode']);
  if (brandCode != null) {
    packageViewSjEntity.brandCode = brandCode;
  }
  final String? brandName = jsonConvert.convert<String>(json['brandName']);
  if (brandName != null) {
    packageViewSjEntity.brandName = brandName;
  }
  final String? senderName = jsonConvert.convert<String>(json['senderName']);
  if (senderName != null) {
    packageViewSjEntity.senderName = senderName;
  }
  final String? senderIdNumber = jsonConvert.convert<String>(json['senderIdNumber']);
  if (senderIdNumber != null) {
    packageViewSjEntity.senderIdNumber = senderIdNumber;
  }
  final String? senderMobile = jsonConvert.convert<String>(json['senderMobile']);
  if (senderMobile != null) {
    packageViewSjEntity.senderMobile = senderMobile;
  }
  final String? senderProvinceCode = jsonConvert.convert<String>(json['senderProvinceCode']);
  if (senderProvinceCode != null) {
    packageViewSjEntity.senderProvinceCode = senderProvinceCode;
  }
  final String? senderProvinceName = jsonConvert.convert<String>(json['senderProvinceName']);
  if (senderProvinceName != null) {
    packageViewSjEntity.senderProvinceName = senderProvinceName;
  }
  final String? senderCityCode = jsonConvert.convert<String>(json['senderCityCode']);
  if (senderCityCode != null) {
    packageViewSjEntity.senderCityCode = senderCityCode;
  }
  final String? senderCityName = jsonConvert.convert<String>(json['senderCityName']);
  if (senderCityName != null) {
    packageViewSjEntity.senderCityName = senderCityName;
  }
  final String? senderAreaCode = jsonConvert.convert<String>(json['senderAreaCode']);
  if (senderAreaCode != null) {
    packageViewSjEntity.senderAreaCode = senderAreaCode;
  }
  final String? senderAreaName = jsonConvert.convert<String>(json['senderAreaName']);
  if (senderAreaName != null) {
    packageViewSjEntity.senderAreaName = senderAreaName;
  }
  final String? senderStreetCode = jsonConvert.convert<String>(json['senderStreetCode']);
  if (senderStreetCode != null) {
    packageViewSjEntity.senderStreetCode = senderStreetCode;
  }
  final String? senderStreetName = jsonConvert.convert<String>(json['senderStreetName']);
  if (senderStreetName != null) {
    packageViewSjEntity.senderStreetName = senderStreetName;
  }
  final String? senderAddress = jsonConvert.convert<String>(json['senderAddress']);
  if (senderAddress != null) {
    packageViewSjEntity.senderAddress = senderAddress;
  }
  final String? receiverProvinceCode = jsonConvert.convert<String>(json['receiverProvinceCode']);
  if (receiverProvinceCode != null) {
    packageViewSjEntity.receiverProvinceCode = receiverProvinceCode;
  }
  final String? receiverProvinceName = jsonConvert.convert<String>(json['receiverProvinceName']);
  if (receiverProvinceName != null) {
    packageViewSjEntity.receiverProvinceName = receiverProvinceName;
  }
  final String? receiverCityCode = jsonConvert.convert<String>(json['receiverCityCode']);
  if (receiverCityCode != null) {
    packageViewSjEntity.receiverCityCode = receiverCityCode;
  }
  final String? receiverCityName = jsonConvert.convert<String>(json['receiverCityName']);
  if (receiverCityName != null) {
    packageViewSjEntity.receiverCityName = receiverCityName;
  }
  final String? receiverAreaCode = jsonConvert.convert<String>(json['receiverAreaCode']);
  if (receiverAreaCode != null) {
    packageViewSjEntity.receiverAreaCode = receiverAreaCode;
  }
  final String? receiverAreaName = jsonConvert.convert<String>(json['receiverAreaName']);
  if (receiverAreaName != null) {
    packageViewSjEntity.receiverAreaName = receiverAreaName;
  }
  final String? receiverStreetCode = jsonConvert.convert<String>(json['receiverStreetCode']);
  if (receiverStreetCode != null) {
    packageViewSjEntity.receiverStreetCode = receiverStreetCode;
  }
  final String? receiverStreetName = jsonConvert.convert<String>(json['receiverStreetName']);
  if (receiverStreetName != null) {
    packageViewSjEntity.receiverStreetName = receiverStreetName;
  }
  final String? receiverAddress = jsonConvert.convert<String>(json['receiverAddress']);
  if (receiverAddress != null) {
    packageViewSjEntity.receiverAddress = receiverAddress;
  }
  final String? waybillNo = jsonConvert.convert<String>(json['waybillNo']);
  if (waybillNo != null) {
    packageViewSjEntity.waybillNo = waybillNo;
  }
  final int? show = jsonConvert.convert<int>(json['show']);
  if (show != null) {
    packageViewSjEntity.show = show;
  }
  return packageViewSjEntity;
}

Map<String, dynamic> $PackageViewSjEntityToJson(PackageViewSjEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['createBy'] = entity.createBy;
  data['createTime'] = entity.createTime;
  data['updateTime'] = entity.updateTime;
  data['orderType'] = entity.orderType;
  data['orderStatus'] = entity.orderStatus;
  data['orderNo'] = entity.orderNo;
  data['bizNo'] = entity.bizNo;
  data['channelId'] = entity.channelId;
  data['channelName'] = entity.channelName;
  data['siteId'] = entity.siteId;
  data['siteName'] = entity.siteName;
  data['shopId'] = entity.shopId;
  data['shopName'] = entity.shopName;
  data['cabinetLocationId'] = entity.cabinetLocationId;
  data['cabinetLocationCode'] = entity.cabinetLocationCode;
  data['cabinetLocationName'] = entity.cabinetLocationName;
  data['cabinetLocationAddress'] = entity.cabinetLocationAddress;
  data['cabinetId'] = entity.cabinetId;
  data['cabinetCode'] = entity.cabinetCode;
  data['cabinetName'] = entity.cabinetName;
  data['cabinetSerialNo'] = entity.cabinetSerialNo;
  data['cabinetBoxId'] = entity.cabinetBoxId;
  data['cabinetBoxType'] = entity.cabinetBoxType;
  data['cabinetBoxLabel'] = entity.cabinetBoxLabel;
  data['cabinetBoxPcbNo'] = entity.cabinetBoxPcbNo;
  data['storeType'] = entity.storeType;
  data['receiverName'] = entity.receiverName;
  data['receiverMobile'] = entity.receiverMobile;
  data['receiverMobileLast4'] = entity.receiverMobileLast4;
  data['checkCode'] = entity.checkCode;
  data['messageType'] = entity.messageType;
  data['messageSmsStatus'] = entity.messageSmsStatus;
  data['messageWxStatus'] = entity.messageWxStatus;
  data['inboundUserId'] = entity.inboundUserId;
  data['inboundUserName'] = entity.inboundUserName;
  data['inboundUserFee'] = entity.inboundUserFee;
  data['inboundUserMobile'] = entity.inboundUserMobile;
  data['inboundTime'] = entity.inboundTime;
  data['inboundYm'] = entity.inboundYm;
  data['inboundYmd'] = entity.inboundYmd;
  data['hasOutbound'] = entity.hasOutbound;
  data['outboundUserFee'] = entity.outboundUserFee;
  data['remarks'] = entity.remarks;
  data['serviceDuration'] = entity.serviceDuration;
  data['version'] = entity.version;
  data['sendStatus'] = entity.sendStatus;
  data['brandCode'] = entity.brandCode;
  data['brandName'] = entity.brandName;
  data['senderName'] = entity.senderName;
  data['senderIdNumber'] = entity.senderIdNumber;
  data['senderMobile'] = entity.senderMobile;
  data['senderProvinceCode'] = entity.senderProvinceCode;
  data['senderProvinceName'] = entity.senderProvinceName;
  data['senderCityCode'] = entity.senderCityCode;
  data['senderCityName'] = entity.senderCityName;
  data['senderAreaCode'] = entity.senderAreaCode;
  data['senderAreaName'] = entity.senderAreaName;
  data['senderStreetCode'] = entity.senderStreetCode;
  data['senderStreetName'] = entity.senderStreetName;
  data['senderAddress'] = entity.senderAddress;
  data['receiverProvinceCode'] = entity.receiverProvinceCode;
  data['receiverProvinceName'] = entity.receiverProvinceName;
  data['receiverCityCode'] = entity.receiverCityCode;
  data['receiverCityName'] = entity.receiverCityName;
  data['receiverAreaCode'] = entity.receiverAreaCode;
  data['receiverAreaName'] = entity.receiverAreaName;
  data['receiverStreetCode'] = entity.receiverStreetCode;
  data['receiverStreetName'] = entity.receiverStreetName;
  data['receiverAddress'] = entity.receiverAddress;
  data['waybillNo'] = entity.waybillNo;
  data['show'] = entity.show;
  return data;
}

extension PackageViewSjEntityExtension on PackageViewSjEntity {
  PackageViewSjEntity copyWith({
    String? id,
    String? createBy,
    String? createTime,
    String? updateTime,
    int? orderType,
    int? orderStatus,
    String? orderNo,
    String? bizNo,
    String? channelId,
    String? channelName,
    String? siteId,
    String? siteName,
    String? shopId,
    String? shopName,
    String? cabinetLocationId,
    String? cabinetLocationCode,
    String? cabinetLocationName,
    String? cabinetLocationAddress,
    String? cabinetId,
    String? cabinetCode,
    String? cabinetName,
    int? cabinetSerialNo,
    String? cabinetBoxId,
    int? cabinetBoxType,
    String? cabinetBoxLabel,
    int? cabinetBoxPcbNo,
    int? storeType,
    String? receiverName,
    String? receiverMobile,
    String? receiverMobileLast4,
    String? checkCode,
    int? messageType,
    int? messageSmsStatus,
    int? messageWxStatus,
    String? inboundUserId,
    String? inboundUserName,
    int? inboundUserFee,
    String? inboundUserMobile,
    String? inboundTime,
    String? inboundYm,
    String? inboundYmd,
    int? hasOutbound,
    int? outboundUserFee,
    String? remarks,
    int? serviceDuration,
    int? version,
    int? sendStatus,
    String? brandCode,
    String? brandName,
    String? senderName,
    String? senderIdNumber,
    String? senderMobile,
    String? senderProvinceCode,
    String? senderProvinceName,
    String? senderCityCode,
    String? senderCityName,
    String? senderAreaCode,
    String? senderAreaName,
    String? senderStreetCode,
    String? senderStreetName,
    String? senderAddress,
    String? receiverProvinceCode,
    String? receiverProvinceName,
    String? receiverCityCode,
    String? receiverCityName,
    String? receiverAreaCode,
    String? receiverAreaName,
    String? receiverStreetCode,
    String? receiverStreetName,
    String? receiverAddress,
    String? waybillNo,
    int? show,
  }) {
    return PackageViewSjEntity()
      ..id = id ?? this.id
      ..createBy = createBy ?? this.createBy
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime
      ..orderType = orderType ?? this.orderType
      ..orderStatus = orderStatus ?? this.orderStatus
      ..orderNo = orderNo ?? this.orderNo
      ..bizNo = bizNo ?? this.bizNo
      ..channelId = channelId ?? this.channelId
      ..channelName = channelName ?? this.channelName
      ..siteId = siteId ?? this.siteId
      ..siteName = siteName ?? this.siteName
      ..shopId = shopId ?? this.shopId
      ..shopName = shopName ?? this.shopName
      ..cabinetLocationId = cabinetLocationId ?? this.cabinetLocationId
      ..cabinetLocationCode = cabinetLocationCode ?? this.cabinetLocationCode
      ..cabinetLocationName = cabinetLocationName ?? this.cabinetLocationName
      ..cabinetLocationAddress = cabinetLocationAddress ?? this.cabinetLocationAddress
      ..cabinetId = cabinetId ?? this.cabinetId
      ..cabinetCode = cabinetCode ?? this.cabinetCode
      ..cabinetName = cabinetName ?? this.cabinetName
      ..cabinetSerialNo = cabinetSerialNo ?? this.cabinetSerialNo
      ..cabinetBoxId = cabinetBoxId ?? this.cabinetBoxId
      ..cabinetBoxType = cabinetBoxType ?? this.cabinetBoxType
      ..cabinetBoxLabel = cabinetBoxLabel ?? this.cabinetBoxLabel
      ..cabinetBoxPcbNo = cabinetBoxPcbNo ?? this.cabinetBoxPcbNo
      ..storeType = storeType ?? this.storeType
      ..receiverName = receiverName ?? this.receiverName
      ..receiverMobile = receiverMobile ?? this.receiverMobile
      ..receiverMobileLast4 = receiverMobileLast4 ?? this.receiverMobileLast4
      ..checkCode = checkCode ?? this.checkCode
      ..messageType = messageType ?? this.messageType
      ..messageSmsStatus = messageSmsStatus ?? this.messageSmsStatus
      ..messageWxStatus = messageWxStatus ?? this.messageWxStatus
      ..inboundUserId = inboundUserId ?? this.inboundUserId
      ..inboundUserName = inboundUserName ?? this.inboundUserName
      ..inboundUserFee = inboundUserFee ?? this.inboundUserFee
      ..inboundUserMobile = inboundUserMobile ?? this.inboundUserMobile
      ..inboundTime = inboundTime ?? this.inboundTime
      ..inboundYm = inboundYm ?? this.inboundYm
      ..inboundYmd = inboundYmd ?? this.inboundYmd
      ..hasOutbound = hasOutbound ?? this.hasOutbound
      ..outboundUserFee = outboundUserFee ?? this.outboundUserFee
      ..remarks = remarks ?? this.remarks
      ..serviceDuration = serviceDuration ?? this.serviceDuration
      ..version = version ?? this.version
      ..sendStatus = sendStatus ?? this.sendStatus
      ..brandCode = brandCode ?? this.brandCode
      ..brandName = brandName ?? this.brandName
      ..senderName = senderName ?? this.senderName
      ..senderIdNumber = senderIdNumber ?? this.senderIdNumber
      ..senderMobile = senderMobile ?? this.senderMobile
      ..senderProvinceCode = senderProvinceCode ?? this.senderProvinceCode
      ..senderProvinceName = senderProvinceName ?? this.senderProvinceName
      ..senderCityCode = senderCityCode ?? this.senderCityCode
      ..senderCityName = senderCityName ?? this.senderCityName
      ..senderAreaCode = senderAreaCode ?? this.senderAreaCode
      ..senderAreaName = senderAreaName ?? this.senderAreaName
      ..senderStreetCode = senderStreetCode ?? this.senderStreetCode
      ..senderStreetName = senderStreetName ?? this.senderStreetName
      ..senderAddress = senderAddress ?? this.senderAddress
      ..receiverProvinceCode = receiverProvinceCode ?? this.receiverProvinceCode
      ..receiverProvinceName = receiverProvinceName ?? this.receiverProvinceName
      ..receiverCityCode = receiverCityCode ?? this.receiverCityCode
      ..receiverCityName = receiverCityName ?? this.receiverCityName
      ..receiverAreaCode = receiverAreaCode ?? this.receiverAreaCode
      ..receiverAreaName = receiverAreaName ?? this.receiverAreaName
      ..receiverStreetCode = receiverStreetCode ?? this.receiverStreetCode
      ..receiverStreetName = receiverStreetName ?? this.receiverStreetName
      ..receiverAddress = receiverAddress ?? this.receiverAddress
      ..waybillNo = waybillNo ?? this.waybillNo
      ..show = show ?? this.show;
  }
}