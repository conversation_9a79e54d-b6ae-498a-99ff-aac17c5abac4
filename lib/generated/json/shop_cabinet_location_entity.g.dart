import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/shop_cabinet_location_entity.dart';

ShopCabinetLocationEntity $ShopCabinetLocationEntityFromJson(Map<String, dynamic> json) {
  final ShopCabinetLocationEntity shopCabinetLocationEntity = ShopCabinetLocationEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    shopCabinetLocationEntity.id = id;
  }
  final int? hasAddCabinet = jsonConvert.convert<int>(json['hasAddCabinet']);
  if (hasAddCabinet != null) {
    shopCabinetLocationEntity.hasAddCabinet = hasAddCabinet;
  }
  final int? hasChangeBrand = jsonConvert.convert<int>(json['hasChangeBrand']);
  if (hasChangeBrand != null) {
    shopCabinetLocationEntity.hasChangeBrand = hasChangeBrand;
  }
  final String? code = jsonConvert.convert<String>(json['code']);
  if (code != null) {
    shopCabinetLocationEntity.code = code;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    shopCabinetLocationEntity.name = name;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    shopCabinetLocationEntity.type = type;
  }
  final String? linkman = jsonConvert.convert<String>(json['linkman']);
  if (linkman != null) {
    shopCabinetLocationEntity.linkman = linkman;
  }
  final String? mobile = jsonConvert.convert<String>(json['mobile']);
  if (mobile != null) {
    shopCabinetLocationEntity.mobile = mobile;
  }
  final int? smsPrice = jsonConvert.convert<int>(json['smsPrice']);
  if (smsPrice != null) {
    shopCabinetLocationEntity.smsPrice = smsPrice;
  }
  final int? wxPrice = jsonConvert.convert<int>(json['wxPrice']);
  if (wxPrice != null) {
    shopCabinetLocationEntity.wxPrice = wxPrice;
  }
  final int? cabinetNoType = jsonConvert.convert<int>(json['cabinetNoType']);
  if (cabinetNoType != null) {
    shopCabinetLocationEntity.cabinetNoType = cabinetNoType;
  }
  final int? noticeType = jsonConvert.convert<int>(json['noticeType']);
  if (noticeType != null) {
    shopCabinetLocationEntity.noticeType = noticeType;
  }
  final int? cabinetType = jsonConvert.convert<int>(json['cabinetType']);
  if (cabinetType != null) {
    shopCabinetLocationEntity.cabinetType = cabinetType;
  }
  final String? servicePhone = jsonConvert.convert<String>(json['servicePhone']);
  if (servicePhone != null) {
    shopCabinetLocationEntity.servicePhone = servicePhone;
  }
  final String? hostActivationCode1 = jsonConvert.convert<String>(json['hostActivationCode1']);
  if (hostActivationCode1 != null) {
    shopCabinetLocationEntity.hostActivationCode1 = hostActivationCode1;
  }
  final int? hostStatus1 = jsonConvert.convert<int>(json['hostStatus1']);
  if (hostStatus1 != null) {
    shopCabinetLocationEntity.hostStatus1 = hostStatus1;
  }
  final String? hostFactory1 = jsonConvert.convert<String>(json['hostFactory1']);
  if (hostFactory1 != null) {
    shopCabinetLocationEntity.hostFactory1 = hostFactory1;
  }
  final String? provinceCode = jsonConvert.convert<String>(json['provinceCode']);
  if (provinceCode != null) {
    shopCabinetLocationEntity.provinceCode = provinceCode;
  }
  final String? provinceName = jsonConvert.convert<String>(json['provinceName']);
  if (provinceName != null) {
    shopCabinetLocationEntity.provinceName = provinceName;
  }
  final String? cityCode = jsonConvert.convert<String>(json['cityCode']);
  if (cityCode != null) {
    shopCabinetLocationEntity.cityCode = cityCode;
  }
  final String? cityName = jsonConvert.convert<String>(json['cityName']);
  if (cityName != null) {
    shopCabinetLocationEntity.cityName = cityName;
  }
  final String? areaCode = jsonConvert.convert<String>(json['areaCode']);
  if (areaCode != null) {
    shopCabinetLocationEntity.areaCode = areaCode;
  }
  final String? areaName = jsonConvert.convert<String>(json['areaName']);
  if (areaName != null) {
    shopCabinetLocationEntity.areaName = areaName;
  }
  final String? address = jsonConvert.convert<String>(json['address']);
  if (address != null) {
    shopCabinetLocationEntity.address = address;
  }
  final double? longitude = jsonConvert.convert<double>(json['longitude']);
  if (longitude != null) {
    shopCabinetLocationEntity.longitude = longitude;
  }
  final double? latitude = jsonConvert.convert<double>(json['latitude']);
  if (latitude != null) {
    shopCabinetLocationEntity.latitude = latitude;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    shopCabinetLocationEntity.status = status;
  }
  final int? applyTime = jsonConvert.convert<int>(json['applyTime']);
  if (applyTime != null) {
    shopCabinetLocationEntity.applyTime = applyTime;
  }
  final String? dispatchJson = jsonConvert.convert<String>(json['dispatchJson']);
  if (dispatchJson != null) {
    shopCabinetLocationEntity.dispatchJson = dispatchJson;
  }
  final int? switchRent = jsonConvert.convert<int>(json['switchRent']);
  if (switchRent != null) {
    shopCabinetLocationEntity.switchRent = switchRent;
  }
  final String? rentJson = jsonConvert.convert<String>(json['rentJson']);
  if (rentJson != null) {
    shopCabinetLocationEntity.rentJson = rentJson;
  }
  final int? switchAppoint = jsonConvert.convert<int>(json['switchAppoint']);
  if (switchAppoint != null) {
    shopCabinetLocationEntity.switchAppoint = switchAppoint;
  }
  final String? appointJson = jsonConvert.convert<String>(json['appointJson']);
  if (appointJson != null) {
    shopCabinetLocationEntity.appointJson = appointJson;
  }
  final bool? switchCustomer = jsonConvert.convert<bool>(json['switchCustomer']);
  if (switchCustomer != null) {
    shopCabinetLocationEntity.switchCustomer = switchCustomer;
  }
  final String? customerJson = jsonConvert.convert<String>(json['customerJson']);
  if (customerJson != null) {
    shopCabinetLocationEntity.customerJson = customerJson;
  }
  final bool? switchKeep = jsonConvert.convert<bool>(json['switchKeep']);
  if (switchKeep != null) {
    shopCabinetLocationEntity.switchKeep = switchKeep;
  }
  final String? keepJson = jsonConvert.convert<String>(json['keepJson']);
  if (keepJson != null) {
    shopCabinetLocationEntity.keepJson = keepJson;
  }
  final List<CabinetItemEntity>? cabinetList = (json['cabinetList'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<CabinetItemEntity>(e) as CabinetItemEntity).toList();
  if (cabinetList != null) {
    shopCabinetLocationEntity.cabinetList = cabinetList;
  }
  return shopCabinetLocationEntity;
}

Map<String, dynamic> $ShopCabinetLocationEntityToJson(ShopCabinetLocationEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['hasAddCabinet'] = entity.hasAddCabinet;
  data['hasChangeBrand'] = entity.hasChangeBrand;
  data['code'] = entity.code;
  data['name'] = entity.name;
  data['type'] = entity.type;
  data['linkman'] = entity.linkman;
  data['mobile'] = entity.mobile;
  data['smsPrice'] = entity.smsPrice;
  data['wxPrice'] = entity.wxPrice;
  data['cabinetNoType'] = entity.cabinetNoType;
  data['noticeType'] = entity.noticeType;
  data['cabinetType'] = entity.cabinetType;
  data['servicePhone'] = entity.servicePhone;
  data['hostActivationCode1'] = entity.hostActivationCode1;
  data['hostStatus1'] = entity.hostStatus1;
  data['hostFactory1'] = entity.hostFactory1;
  data['provinceCode'] = entity.provinceCode;
  data['provinceName'] = entity.provinceName;
  data['cityCode'] = entity.cityCode;
  data['cityName'] = entity.cityName;
  data['areaCode'] = entity.areaCode;
  data['areaName'] = entity.areaName;
  data['address'] = entity.address;
  data['longitude'] = entity.longitude;
  data['latitude'] = entity.latitude;
  data['status'] = entity.status;
  data['applyTime'] = entity.applyTime;
  data['dispatchJson'] = entity.dispatchJson;
  data['switchRent'] = entity.switchRent;
  data['rentJson'] = entity.rentJson;
  data['switchAppoint'] = entity.switchAppoint;
  data['appointJson'] = entity.appointJson;
  data['switchCustomer'] = entity.switchCustomer;
  data['customerJson'] = entity.customerJson;
  data['switchKeep'] = entity.switchKeep;
  data['keepJson'] = entity.keepJson;
  data['cabinetList'] = entity.cabinetList?.map((v) => v.toJson()).toList();
  return data;
}

extension ShopCabinetLocationEntityExtension on ShopCabinetLocationEntity {
  ShopCabinetLocationEntity copyWith({
    String? id,
    int? hasAddCabinet,
    int? hasChangeBrand,
    String? code,
    String? name,
    int? type,
    String? linkman,
    String? mobile,
    int? smsPrice,
    int? wxPrice,
    int? cabinetNoType,
    int? noticeType,
    int? cabinetType,
    String? servicePhone,
    String? hostActivationCode1,
    int? hostStatus1,
    String? hostFactory1,
    String? provinceCode,
    String? provinceName,
    String? cityCode,
    String? cityName,
    String? areaCode,
    String? areaName,
    String? address,
    double? longitude,
    double? latitude,
    int? status,
    int? applyTime,
    String? dispatchJson,
    int? switchRent,
    String? rentJson,
    int? switchAppoint,
    String? appointJson,
    bool? switchCustomer,
    String? customerJson,
    bool? switchKeep,
    String? keepJson,
    List<CabinetItemEntity>? cabinetList,
  }) {
    return ShopCabinetLocationEntity()
      ..id = id ?? this.id
      ..hasAddCabinet = hasAddCabinet ?? this.hasAddCabinet
      ..hasChangeBrand = hasChangeBrand ?? this.hasChangeBrand
      ..code = code ?? this.code
      ..name = name ?? this.name
      ..type = type ?? this.type
      ..linkman = linkman ?? this.linkman
      ..mobile = mobile ?? this.mobile
      ..smsPrice = smsPrice ?? this.smsPrice
      ..wxPrice = wxPrice ?? this.wxPrice
      ..cabinetNoType = cabinetNoType ?? this.cabinetNoType
      ..noticeType = noticeType ?? this.noticeType
      ..cabinetType = cabinetType ?? this.cabinetType
      ..servicePhone = servicePhone ?? this.servicePhone
      ..hostActivationCode1 = hostActivationCode1 ?? this.hostActivationCode1
      ..hostStatus1 = hostStatus1 ?? this.hostStatus1
      ..hostFactory1 = hostFactory1 ?? this.hostFactory1
      ..provinceCode = provinceCode ?? this.provinceCode
      ..provinceName = provinceName ?? this.provinceName
      ..cityCode = cityCode ?? this.cityCode
      ..cityName = cityName ?? this.cityName
      ..areaCode = areaCode ?? this.areaCode
      ..areaName = areaName ?? this.areaName
      ..address = address ?? this.address
      ..longitude = longitude ?? this.longitude
      ..latitude = latitude ?? this.latitude
      ..status = status ?? this.status
      ..applyTime = applyTime ?? this.applyTime
      ..dispatchJson = dispatchJson ?? this.dispatchJson
      ..switchRent = switchRent ?? this.switchRent
      ..rentJson = rentJson ?? this.rentJson
      ..switchAppoint = switchAppoint ?? this.switchAppoint
      ..appointJson = appointJson ?? this.appointJson
      ..switchCustomer = switchCustomer ?? this.switchCustomer
      ..customerJson = customerJson ?? this.customerJson
      ..switchKeep = switchKeep ?? this.switchKeep
      ..keepJson = keepJson ?? this.keepJson
      ..cabinetList = cabinetList ?? this.cabinetList;
  }
}

CabinetItemEntity $CabinetItemEntityFromJson(Map<String, dynamic> json) {
  final CabinetItemEntity cabinetItemEntity = CabinetItemEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    cabinetItemEntity.id = id;
  }
  final String? cabinetLocationId = jsonConvert.convert<String>(json['cabinetLocationId']);
  if (cabinetLocationId != null) {
    cabinetItemEntity.cabinetLocationId = cabinetLocationId;
  }
  final String? code = jsonConvert.convert<String>(json['code']);
  if (code != null) {
    cabinetItemEntity.code = code;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    cabinetItemEntity.name = name;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    cabinetItemEntity.type = type;
  }
  final int? locationNo = jsonConvert.convert<int>(json['locationNo']);
  if (locationNo != null) {
    cabinetItemEntity.locationNo = locationNo;
  }
  final String? columns = jsonConvert.convert<String>(json['columns']);
  if (columns != null) {
    cabinetItemEntity.columns = columns;
  }
  final String? hostBoxArray = jsonConvert.convert<String>(json['hostBoxArray']);
  if (hostBoxArray != null) {
    cabinetItemEntity.hostBoxArray = hostBoxArray;
  }
  final int? serialNo = jsonConvert.convert<int>(json['serialNo']);
  if (serialNo != null) {
    cabinetItemEntity.serialNo = serialNo;
  }
  final int? boxTotalNum = jsonConvert.convert<int>(json['boxTotalNum']);
  if (boxTotalNum != null) {
    cabinetItemEntity.boxTotalNum = boxTotalNum;
  }
  final int? boxEmptyNum = jsonConvert.convert<int>(json['boxEmptyNum']);
  if (boxEmptyNum != null) {
    cabinetItemEntity.boxEmptyNum = boxEmptyNum;
  }
  final int? boxAvailableNum = jsonConvert.convert<int>(json['boxAvailableNum']);
  if (boxAvailableNum != null) {
    cabinetItemEntity.boxAvailableNum = boxAvailableNum;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    cabinetItemEntity.status = status;
  }
  final List<CabinetBoxItem>? boxList = (json['boxList'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<CabinetBoxItem>(e) as CabinetBoxItem).toList();
  if (boxList != null) {
    cabinetItemEntity.boxList = boxList;
  }
  return cabinetItemEntity;
}

Map<String, dynamic> $CabinetItemEntityToJson(CabinetItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['cabinetLocationId'] = entity.cabinetLocationId;
  data['code'] = entity.code;
  data['name'] = entity.name;
  data['type'] = entity.type;
  data['locationNo'] = entity.locationNo;
  data['columns'] = entity.columns;
  data['hostBoxArray'] = entity.hostBoxArray;
  data['serialNo'] = entity.serialNo;
  data['boxTotalNum'] = entity.boxTotalNum;
  data['boxEmptyNum'] = entity.boxEmptyNum;
  data['boxAvailableNum'] = entity.boxAvailableNum;
  data['status'] = entity.status;
  data['boxList'] = entity.boxList?.map((v) => v.toJson()).toList();
  return data;
}

extension CabinetItemEntityExtension on CabinetItemEntity {
  CabinetItemEntity copyWith({
    String? id,
    String? cabinetLocationId,
    String? code,
    String? name,
    int? type,
    int? locationNo,
    String? columns,
    String? hostBoxArray,
    int? serialNo,
    int? boxTotalNum,
    int? boxEmptyNum,
    int? boxAvailableNum,
    int? status,
    List<CabinetBoxItem>? boxList,
  }) {
    return CabinetItemEntity()
      ..id = id ?? this.id
      ..cabinetLocationId = cabinetLocationId ?? this.cabinetLocationId
      ..code = code ?? this.code
      ..name = name ?? this.name
      ..type = type ?? this.type
      ..locationNo = locationNo ?? this.locationNo
      ..columns = columns ?? this.columns
      ..hostBoxArray = hostBoxArray ?? this.hostBoxArray
      ..serialNo = serialNo ?? this.serialNo
      ..boxTotalNum = boxTotalNum ?? this.boxTotalNum
      ..boxEmptyNum = boxEmptyNum ?? this.boxEmptyNum
      ..boxAvailableNum = boxAvailableNum ?? this.boxAvailableNum
      ..status = status ?? this.status
      ..boxList = boxList ?? this.boxList;
  }
}

CabinetBoxItem $CabinetBoxItemFromJson(Map<String, dynamic> json) {
  final CabinetBoxItem cabinetBoxItem = CabinetBoxItem();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    cabinetBoxItem.id = id;
  }
  final String? boxLabel = jsonConvert.convert<String>(json['boxLabel']);
  if (boxLabel != null) {
    cabinetBoxItem.boxLabel = boxLabel;
  }

  final int? pcbNo = jsonConvert.convert<int>(json['pcbNo']);
  if (pcbNo != null) {
    cabinetBoxItem.pcbNo = pcbNo;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    cabinetBoxItem.type = type;
  }
  final int? specialStatus = jsonConvert.convert<int>(json['specialStatus']);
  if (specialStatus != null) {
    cabinetBoxItem.specialStatus = specialStatus;
  }
  final int? workStatus = jsonConvert.convert<int>(json['workStatus']);
  if (workStatus != null) {
    cabinetBoxItem.workStatus = workStatus;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    cabinetBoxItem.status = status;
  }
  final int? hasSmsError = jsonConvert.convert<int>(json['hasSmsError']);
  if (hasSmsError != null) {
    cabinetBoxItem.hasSmsError = hasSmsError;
  }
  final int? hasKeepEffect = jsonConvert.convert<int>(json['hasKeepEffect']);
  if (hasKeepEffect != null) {
    cabinetBoxItem.hasKeepEffect = hasKeepEffect;
  }
  final String? inboundTime = jsonConvert.convert<String>(json['inboundTime']);
  if (inboundTime != null) {  
    cabinetBoxItem.inboundTime = inboundTime;
  }
  final String? orderId = jsonConvert.convert<String>(json['orderId']);
  if (orderId != null) {
    cabinetBoxItem.orderId = orderId;
  }
  return cabinetBoxItem;
}

Map<String, dynamic> $CabinetBoxItemToJson(CabinetBoxItem entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['boxLabel'] = entity.boxLabel;
  data['pcbNo'] = entity.pcbNo;
  data['type'] = entity.type;
  data['specialStatus'] = entity.specialStatus;
  data['workStatus'] = entity.workStatus;
  data['status'] = entity.status;
  data['hasSmsError'] = entity.hasSmsError;
  data['hasKeepEffect'] = entity.hasKeepEffect;
  return data;
}

extension CabinetBoxItemExtension on CabinetBoxItem {
  CabinetBoxItem copyWith({
    String? id,
    String? boxLabel,
    int? pcbNo,
    int? type,
    int? specialStatus,
    int? workStatus,
    int? status,
    int? hasSmsError,
    int? hasKeepEffect,
  }) {
    return CabinetBoxItem()
      ..id = id ?? this.id
      ..boxLabel = boxLabel ?? this.boxLabel
      ..pcbNo = pcbNo ?? this.pcbNo
      ..type = type ?? this.type
      ..specialStatus = specialStatus ?? this.specialStatus
      ..workStatus = workStatus ?? this.workStatus
      ..status = status ?? this.status
      ..hasSmsError = hasSmsError ?? this.hasSmsError
      ..hasKeepEffect = hasKeepEffect ?? this.hasKeepEffect;
  }
}