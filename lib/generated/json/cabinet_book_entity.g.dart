import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/cabinet_book_entity.dart';

CabinetBookEntity $CabinetBookEntityFromJson(Map<String, dynamic> json) {
  final CabinetBookEntity cabinetBookEntity = CabinetBookEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    cabinetBookEntity.id = id;
  }
  final int? orderStatus = jsonConvert.convert<int>(json['orderStatus']);
  if (orderStatus != null) {
    cabinetBookEntity.orderStatus = orderStatus;
  }
  final String? orderNo = jsonConvert.convert<String>(json['orderNo']);
  if (orderNo != null) {
    cabinetBookEntity.orderNo = orderNo;
  }
  final String? channelId = jsonConvert.convert<String>(json['channelId']);
  if (channelId != null) {
    cabinetBookEntity.channelId = channelId;
  }
  final String? channelName = jsonConvert.convert<String>(json['channelName']);
  if (channelName != null) {
    cabinetBookEntity.channelName = channelName;
  }
  final String? siteId = jsonConvert.convert<String>(json['siteId']);
  if (siteId != null) {
    cabinetBookEntity.siteId = siteId;
  }
  final String? siteName = jsonConvert.convert<String>(json['siteName']);
  if (siteName != null) {
    cabinetBookEntity.siteName = siteName;
  }
  final String? shopId = jsonConvert.convert<String>(json['shopId']);
  if (shopId != null) {
    cabinetBookEntity.shopId = shopId;
  }
  final String? shopName = jsonConvert.convert<String>(json['shopName']);
  if (shopName != null) {
    cabinetBookEntity.shopName = shopName;
  }
  final String? cabinetLocationId = jsonConvert.convert<String>(json['cabinetLocationId']);
  if (cabinetLocationId != null) {
    cabinetBookEntity.cabinetLocationId = cabinetLocationId;
  }
  final String? cabinetLocationCode = jsonConvert.convert<String>(json['cabinetLocationCode']);
  if (cabinetLocationCode != null) {
    cabinetBookEntity.cabinetLocationCode = cabinetLocationCode;
  }
  final String? cabinetLocationName = jsonConvert.convert<String>(json['cabinetLocationName']);
  if (cabinetLocationName != null) {
    cabinetBookEntity.cabinetLocationName = cabinetLocationName;
  }
  final int? cabinetLocationOwner = jsonConvert.convert<int>(json['cabinetLocationOwner']);
  if (cabinetLocationOwner != null) {
    cabinetBookEntity.cabinetLocationOwner = cabinetLocationOwner;
  }
  final String? bookUserId = jsonConvert.convert<String>(json['bookUserId']);
  if (bookUserId != null) {
    cabinetBookEntity.bookUserId = bookUserId;
  }
  final String? bookUserName = jsonConvert.convert<String>(json['bookUserName']);
  if (bookUserName != null) {
    cabinetBookEntity.bookUserName = bookUserName;
  }
  final String? bookUserMobile = jsonConvert.convert<String>(json['bookUserMobile']);
  if (bookUserMobile != null) {
    cabinetBookEntity.bookUserMobile = bookUserMobile;
  }
  final int? totalFee = jsonConvert.convert<int>(json['totalFee']);
  if (totalFee != null) {
    cabinetBookEntity.totalFee = totalFee;
  }
  final int? minute = jsonConvert.convert<int>(json['minute']);
  if (minute != null) {
    cabinetBookEntity.minute = minute;
  }
  final int? superBoxCount = jsonConvert.convert<int>(json['superBoxCount']);
  if (superBoxCount != null) {
    cabinetBookEntity.superBoxCount = superBoxCount;
  }
  final int? hugeBoxCount = jsonConvert.convert<int>(json['hugeBoxCount']);
  if (hugeBoxCount != null) {
    cabinetBookEntity.hugeBoxCount = hugeBoxCount;
  }
  final int? largeBoxCount = jsonConvert.convert<int>(json['largeBoxCount']);
  if (largeBoxCount != null) {
    cabinetBookEntity.largeBoxCount = largeBoxCount;
  }
  final int? mediumBoxCount = jsonConvert.convert<int>(json['mediumBoxCount']);
  if (mediumBoxCount != null) {
    cabinetBookEntity.mediumBoxCount = mediumBoxCount;
  }
  final int? smallBoxCount = jsonConvert.convert<int>(json['smallBoxCount']);
  if (smallBoxCount != null) {
    cabinetBookEntity.smallBoxCount = smallBoxCount;
  }
  final int? miniBoxCount = jsonConvert.convert<int>(json['miniBoxCount']);
  if (miniBoxCount != null) {
    cabinetBookEntity.miniBoxCount = miniBoxCount;
  }
  final int? microBoxCount = jsonConvert.convert<int>(json['microBoxCount']);
  if (microBoxCount != null) {
    cabinetBookEntity.microBoxCount = microBoxCount;
  }
  final String? bookStartTime = jsonConvert.convert<String>(json['bookStartTime']);
  if (bookStartTime != null) {
    cabinetBookEntity.bookStartTime = bookStartTime;
  }
  final String? bookEndTime = jsonConvert.convert<String>(json['bookEndTime']);
  if (bookEndTime != null) {
    cabinetBookEntity.bookEndTime = bookEndTime;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    cabinetBookEntity.createTime = createTime;
  }
  final String? bookJson = jsonConvert.convert<String>(json['bookJson']);
  if (bookJson != null) {
    cabinetBookEntity.bookJson = bookJson;
  }
  final List<CabinetBookList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<CabinetBookList>(e) as CabinetBookList).toList();
  if (list != null) {
    cabinetBookEntity.list = list;
  }
  return cabinetBookEntity;
}

Map<String, dynamic> $CabinetBookEntityToJson(CabinetBookEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['orderStatus'] = entity.orderStatus;
  data['orderNo'] = entity.orderNo;
  data['channelId'] = entity.channelId;
  data['channelName'] = entity.channelName;
  data['siteId'] = entity.siteId;
  data['siteName'] = entity.siteName;
  data['shopId'] = entity.shopId;
  data['shopName'] = entity.shopName;
  data['cabinetLocationId'] = entity.cabinetLocationId;
  data['cabinetLocationCode'] = entity.cabinetLocationCode;
  data['cabinetLocationName'] = entity.cabinetLocationName;
  data['cabinetLocationOwner'] = entity.cabinetLocationOwner;
  data['bookUserId'] = entity.bookUserId;
  data['bookUserName'] = entity.bookUserName;
  data['bookUserMobile'] = entity.bookUserMobile;
  data['totalFee'] = entity.totalFee;
  data['minute'] = entity.minute;
  data['superBoxCount'] = entity.superBoxCount;
  data['hugeBoxCount'] = entity.hugeBoxCount;
  data['largeBoxCount'] = entity.largeBoxCount;
  data['mediumBoxCount'] = entity.mediumBoxCount;
  data['smallBoxCount'] = entity.smallBoxCount;
  data['miniBoxCount'] = entity.miniBoxCount;
  data['microBoxCount'] = entity.microBoxCount;
  data['bookStartTime'] = entity.bookStartTime;
  data['bookEndTime'] = entity.bookEndTime;
  data['createTime'] = entity.createTime;
  data['bookJson'] = entity.bookJson;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension CabinetBookEntityExtension on CabinetBookEntity {
  CabinetBookEntity copyWith({
    String? id,
    int? orderStatus,
    String? orderNo,
    String? channelId,
    String? channelName,
    String? siteId,
    String? siteName,
    String? shopId,
    String? shopName,
    String? cabinetLocationId,
    String? cabinetLocationCode,
    String? cabinetLocationName,
    int? cabinetLocationOwner,
    String? bookUserId,
    String? bookUserName,
    String? bookUserMobile,
    int? totalFee,
    int? minute,
    int? superBoxCount,
    int? hugeBoxCount,
    int? largeBoxCount,
    int? mediumBoxCount,
    int? smallBoxCount,
    int? miniBoxCount,
    int? microBoxCount,
    String? bookStartTime,
    String? bookEndTime,
    String? createTime,
    String? bookJson,
    List<CabinetBookList>? list,
  }) {
    return CabinetBookEntity()
      ..id = id ?? this.id
      ..orderStatus = orderStatus ?? this.orderStatus
      ..orderNo = orderNo ?? this.orderNo
      ..channelId = channelId ?? this.channelId
      ..channelName = channelName ?? this.channelName
      ..siteId = siteId ?? this.siteId
      ..siteName = siteName ?? this.siteName
      ..shopId = shopId ?? this.shopId
      ..shopName = shopName ?? this.shopName
      ..cabinetLocationId = cabinetLocationId ?? this.cabinetLocationId
      ..cabinetLocationCode = cabinetLocationCode ?? this.cabinetLocationCode
      ..cabinetLocationName = cabinetLocationName ?? this.cabinetLocationName
      ..cabinetLocationOwner = cabinetLocationOwner ?? this.cabinetLocationOwner
      ..bookUserId = bookUserId ?? this.bookUserId
      ..bookUserName = bookUserName ?? this.bookUserName
      ..bookUserMobile = bookUserMobile ?? this.bookUserMobile
      ..totalFee = totalFee ?? this.totalFee
      ..minute = minute ?? this.minute
      ..superBoxCount = superBoxCount ?? this.superBoxCount
      ..hugeBoxCount = hugeBoxCount ?? this.hugeBoxCount
      ..largeBoxCount = largeBoxCount ?? this.largeBoxCount
      ..mediumBoxCount = mediumBoxCount ?? this.mediumBoxCount
      ..smallBoxCount = smallBoxCount ?? this.smallBoxCount
      ..miniBoxCount = miniBoxCount ?? this.miniBoxCount
      ..microBoxCount = microBoxCount ?? this.microBoxCount
      ..bookStartTime = bookStartTime ?? this.bookStartTime
      ..bookEndTime = bookEndTime ?? this.bookEndTime
      ..createTime = createTime ?? this.createTime
      ..bookJson = bookJson ?? this.bookJson
      ..list = list ?? this.list;
  }
}

CabinetBookList $CabinetBookListFromJson(Map<String, dynamic> json) {
  final CabinetBookList cabinetBookList = CabinetBookList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    cabinetBookList.id = id;
  }
  final String? orderId = jsonConvert.convert<String>(json['orderId']);
  if (orderId != null) {
    cabinetBookList.orderId = orderId;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    cabinetBookList.status = status;
  }
  final String? cabinetId = jsonConvert.convert<String>(json['cabinetId']);
  if (cabinetId != null) {
    cabinetBookList.cabinetId = cabinetId;
  }
  final String? cabinetCode = jsonConvert.convert<String>(json['cabinetCode']);
  if (cabinetCode != null) {
    cabinetBookList.cabinetCode = cabinetCode;
  }
  final String? cabinetName = jsonConvert.convert<String>(json['cabinetName']);
  if (cabinetName != null) {
    cabinetBookList.cabinetName = cabinetName;
  }
  final int? cabinetSerialNo = jsonConvert.convert<int>(json['cabinetSerialNo']);
  if (cabinetSerialNo != null) {
    cabinetBookList.cabinetSerialNo = cabinetSerialNo;
  }
  final String? cabinetBoxId = jsonConvert.convert<String>(json['cabinetBoxId']);
  if (cabinetBoxId != null) {
    cabinetBookList.cabinetBoxId = cabinetBoxId;
  }
  final int? cabinetBoxType = jsonConvert.convert<int>(json['cabinetBoxType']);
  if (cabinetBoxType != null) {
    cabinetBookList.cabinetBoxType = cabinetBoxType;
  }
  final String? cabinetBoxLabel = jsonConvert.convert<String>(json['cabinetBoxLabel']);
  if (cabinetBoxLabel != null) {
    cabinetBookList.cabinetBoxLabel = cabinetBoxLabel;
  }
  final int? cabinetBoxPcbNo = jsonConvert.convert<int>(json['cabinetBoxPcbNo']);
  if (cabinetBoxPcbNo != null) {
    cabinetBookList.cabinetBoxPcbNo = cabinetBoxPcbNo;
  }
  final int? fee = jsonConvert.convert<int>(json['fee']);
  if (fee != null) {
    cabinetBookList.fee = fee;
  }
  return cabinetBookList;
}

Map<String, dynamic> $CabinetBookListToJson(CabinetBookList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['orderId'] = entity.orderId;
  data['status'] = entity.status;
  data['cabinetId'] = entity.cabinetId;
  data['cabinetCode'] = entity.cabinetCode;
  data['cabinetName'] = entity.cabinetName;
  data['cabinetSerialNo'] = entity.cabinetSerialNo;
  data['cabinetBoxId'] = entity.cabinetBoxId;
  data['cabinetBoxType'] = entity.cabinetBoxType;
  data['cabinetBoxLabel'] = entity.cabinetBoxLabel;
  data['cabinetBoxPcbNo'] = entity.cabinetBoxPcbNo;
  data['fee'] = entity.fee;
  return data;
}

extension CabinetBookListExtension on CabinetBookList {
  CabinetBookList copyWith({
    String? id,
    String? orderId,
    int? status,
    String? cabinetId,
    String? cabinetCode,
    String? cabinetName,
    int? cabinetSerialNo,
    String? cabinetBoxId,
    int? cabinetBoxType,
    String? cabinetBoxLabel,
    int? cabinetBoxPcbNo,
    int? fee,
  }) {
    return CabinetBookList()
      ..id = id ?? this.id
      ..orderId = orderId ?? this.orderId
      ..status = status ?? this.status
      ..cabinetId = cabinetId ?? this.cabinetId
      ..cabinetCode = cabinetCode ?? this.cabinetCode
      ..cabinetName = cabinetName ?? this.cabinetName
      ..cabinetSerialNo = cabinetSerialNo ?? this.cabinetSerialNo
      ..cabinetBoxId = cabinetBoxId ?? this.cabinetBoxId
      ..cabinetBoxType = cabinetBoxType ?? this.cabinetBoxType
      ..cabinetBoxLabel = cabinetBoxLabel ?? this.cabinetBoxLabel
      ..cabinetBoxPcbNo = cabinetBoxPcbNo ?? this.cabinetBoxPcbNo
      ..fee = fee ?? this.fee;
  }
}