import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/company_rules_entity.dart';

CompanyRulesEntity $CompanyRulesEntityFromJson(Map<String, dynamic> json) {
  final CompanyRulesEntity companyRulesEntity = CompanyRulesEntity();
  final String? regx = jsonConvert.convert<String>(json['regx']);
  if (regx != null) {
    companyRulesEntity.regx = regx;
  }
  final String? code = jsonConvert.convert<String>(json['code']);
  if (code != null) {
    companyRulesEntity.code = code;
  }
  return companyRulesEntity;
}

Map<String, dynamic> $CompanyRulesEntityToJson(CompanyRulesEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['regx'] = entity.regx;
  data['code'] = entity.code;
  return data;
}

extension CompanyRulesEntityExtension on CompanyRulesEntity {
  CompanyRulesEntity copyWith({
    String? regx,
    String? code,
  }) {
    return CompanyRulesEntity()
      ..regx = regx ?? this.regx
      ..code = code ?? this.code;
  }
}