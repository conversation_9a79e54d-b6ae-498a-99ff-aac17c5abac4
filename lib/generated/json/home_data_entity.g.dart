import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/home_data_entity.dart';

HomeDataEntity $HomeDataEntityFromJson(Map<String, dynamic> json) {
  final HomeDataEntity homeDataEntity = HomeDataEntity();
  final int? waitPickUpNum = jsonConvert.convert<int>(json['waitPickUpNum']);
  if (waitPickUpNum != null) {
    homeDataEntity.waitPickUpNum = waitPickUpNum;
  }
  final int? keepEffectNum = jsonConvert.convert<int>(json['keepEffectNum']);
  if (keepEffectNum != null) {
    homeDataEntity.keepEffectNum = keepEffectNum;
  }
  final int? waitTakenNum = jsonConvert.convert<int>(json['waitTakenNum']);
  if (waitTakenNum != null) {
    homeDataEntity.waitTakenNum = waitTakenNum;
  }
  final int? shelfWaitPickUpNum = jsonConvert.convert<int>(json['shelfWaitPickUpNum']);
  if (shelfWaitPickUpNum != null) {
    homeDataEntity.shelfWaitPickUpNum = shelfWaitPickUpNum;
  }
  final int? shelfKeepEffectNum = jsonConvert.convert<int>(json['shelfKeepEffectNum']);
  if (shelfKeepEffectNum != null) {
    homeDataEntity.shelfKeepEffectNum = shelfKeepEffectNum;
  }
  final int? takenNum = jsonConvert.convert<int>(json['takenNum']);
  if (takenNum != null) {
    homeDataEntity.takenNum = takenNum;
  }
  return homeDataEntity;
}

Map<String, dynamic> $HomeDataEntityToJson(HomeDataEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['waitPickUpNum'] = entity.waitPickUpNum;
  data['keepEffectNum'] = entity.keepEffectNum;
  data['waitTakenNum'] = entity.waitTakenNum;
  data['shelfWaitPickUpNum'] = entity.shelfWaitPickUpNum;
  data['shelfKeepEffectNum'] = entity.shelfKeepEffectNum;
  data['takenNum'] = entity.takenNum;
  return data;
}

extension HomeDataEntityExtension on HomeDataEntity {
  HomeDataEntity copyWith({
    int? waitPickUpNum,
    int? keepEffectNum,
    int? waitTakenNum,
    int? shelfWaitPickUpNum,
    int? shelfKeepEffectNum,
    int? takenNum,
  }) {
    return HomeDataEntity()
      ..waitPickUpNum = waitPickUpNum ?? this.waitPickUpNum
      ..keepEffectNum = keepEffectNum ?? this.keepEffectNum
      ..waitTakenNum = waitTakenNum ?? this.waitTakenNum
      ..shelfWaitPickUpNum = shelfWaitPickUpNum ?? this.shelfWaitPickUpNum
      ..shelfKeepEffectNum = shelfKeepEffectNum ?? this.shelfKeepEffectNum
      ..takenNum = takenNum ?? this.takenNum;
  }
}