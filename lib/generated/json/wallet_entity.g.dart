import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/wallet_entity.dart';

WalletEntity $WalletEntityFromJson(Map<String, dynamic> json) {
  final WalletEntity walletEntity = WalletEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    walletEntity.id = id;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    walletEntity.createTime = createTime;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    walletEntity.updateTime = updateTime;
  }
  final String? code = jsonConvert.convert<String>(json['code']);
  if (code != null) {
    walletEntity.code = code;
  }
  final int? balance = jsonConvert.convert<int>(json['balance']);
  if (balance != null) {
    walletEntity.balance = balance;
  }
  final int? commission = jsonConvert.convert<int>(json['commission']);
  if (commission != null) {
    walletEntity.commission = commission;
  }
  final int? waitWithdrawalMoney = jsonConvert.convert<int>(json['waitWithdrawalMoney']);
  if (waitWithdrawalMoney != null) {
    walletEntity.waitWithdrawalMoney = waitWithdrawalMoney;
  }
  final int? totalWithdrawalMoney = jsonConvert.convert<int>(json['totalWithdrawalMoney']);
  if (totalWithdrawalMoney != null) {
    walletEntity.totalWithdrawalMoney = totalWithdrawalMoney;
  }
  final int? frozenMoney = jsonConvert.convert<int>(json['frozenMoney']);
  if (frozenMoney != null) {
    walletEntity.frozenMoney = frozenMoney;
  }
  final String? bindTime = jsonConvert.convert<String>(json['bindTime']);
  if (bindTime != null) {
    walletEntity.bindTime = bindTime;
  }
  final int? bindTargetType = jsonConvert.convert<int>(json['bindTargetType']);
  if (bindTargetType != null) {
    walletEntity.bindTargetType = bindTargetType;
  }
  final String? bindTargetId = jsonConvert.convert<String>(json['bindTargetId']);
  if (bindTargetId != null) {
    walletEntity.bindTargetId = bindTargetId;
  }
  final String? bindTargetName = jsonConvert.convert<String>(json['bindTargetName']);
  if (bindTargetName != null) {
    walletEntity.bindTargetName = bindTargetName;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    walletEntity.status = status;
  }
  final int? version = jsonConvert.convert<int>(json['version']);
  if (version != null) {
    walletEntity.version = version;
  }
  return walletEntity;
}

Map<String, dynamic> $WalletEntityToJson(WalletEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['createTime'] = entity.createTime;
  data['updateTime'] = entity.updateTime;
  data['code'] = entity.code;
  data['balance'] = entity.balance;
  data['commission'] = entity.commission;
  data['waitWithdrawalMoney'] = entity.waitWithdrawalMoney;
  data['totalWithdrawalMoney'] = entity.totalWithdrawalMoney;
  data['frozenMoney'] = entity.frozenMoney;
  data['bindTime'] = entity.bindTime;
  data['bindTargetType'] = entity.bindTargetType;
  data['bindTargetId'] = entity.bindTargetId;
  data['bindTargetName'] = entity.bindTargetName;
  data['status'] = entity.status;
  data['version'] = entity.version;
  return data;
}

extension WalletEntityExtension on WalletEntity {
  WalletEntity copyWith({
    String? id,
    String? createTime,
    String? updateTime,
    String? code,
    int? balance,
    int? commission,
    int? waitWithdrawalMoney,
    int? totalWithdrawalMoney,
    int? frozenMoney,
    String? bindTime,
    int? bindTargetType,
    String? bindTargetId,
    String? bindTargetName,
    int? status,
    int? version,
  }) {
    return WalletEntity()
      ..id = id ?? this.id
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime
      ..code = code ?? this.code
      ..balance = balance ?? this.balance
      ..commission = commission ?? this.commission
      ..waitWithdrawalMoney = waitWithdrawalMoney ?? this.waitWithdrawalMoney
      ..totalWithdrawalMoney = totalWithdrawalMoney ?? this.totalWithdrawalMoney
      ..frozenMoney = frozenMoney ?? this.frozenMoney
      ..bindTime = bindTime ?? this.bindTime
      ..bindTargetType = bindTargetType ?? this.bindTargetType
      ..bindTargetId = bindTargetId ?? this.bindTargetId
      ..bindTargetName = bindTargetName ?? this.bindTargetName
      ..status = status ?? this.status
      ..version = version ?? this.version;
  }
}