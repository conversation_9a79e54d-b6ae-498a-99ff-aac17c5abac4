import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/order_log_entity.dart';

OrderLogEntity $OrderLogEntityFromJson(Map<String, dynamic> json) {
  final OrderLogEntity orderLogEntity = OrderLogEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    orderLogEntity.id = id;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    orderLogEntity.createTime = createTime;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    orderLogEntity.updateTime = updateTime;
  }
  final String? orderId = jsonConvert.convert<String>(json['orderId']);
  if (orderId != null) {
    orderLogEntity.orderId = orderId;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    orderLogEntity.type = type;
  }
  final int? actionType = jsonConvert.convert<int>(json['actionType']);
  if (actionType != null) {
    orderLogEntity.actionType = actionType;
  }
  final String? scanType = jsonConvert.convert<String>(json['scanType']);
  if (scanType != null) {
    orderLogEntity.scanType = scanType;
  }
  final String? mobile = jsonConvert.convert<String>(json['mobile']);
  if (mobile != null) {
    orderLogEntity.mobile = mobile;
  }
  final String? smsTplId = jsonConvert.convert<String>(json['smsTplId']);
  if (smsTplId != null) {
    orderLogEntity.smsTplId = smsTplId;
  }
  final String? smsContent = jsonConvert.convert<String>(json['smsContent']);
  if (smsContent != null) {
    orderLogEntity.smsContent = smsContent;
  }
  final int? wordNum = jsonConvert.convert<int>(json['wordNum']);
  if (wordNum != null) {
    orderLogEntity.wordNum = wordNum;
  }
  final int? billingNum = jsonConvert.convert<int>(json['billingNum']);
  if (billingNum != null) {
    orderLogEntity.billingNum = billingNum;
  }
  final int? billingPrice = jsonConvert.convert<int>(json['billingPrice']);
  if (billingPrice != null) {
    orderLogEntity.billingPrice = billingPrice;
  }
  final int? billingMoney = jsonConvert.convert<int>(json['billingMoney']);
  if (billingMoney != null) {
    orderLogEntity.billingMoney = billingMoney;
  }
  final int? secretWaybill = jsonConvert.convert<int>(json['secretWaybill']);
  if (secretWaybill != null) {
    orderLogEntity.secretWaybill = secretWaybill;
  }
  final String? ym = jsonConvert.convert<String>(json['ym']);
  if (ym != null) {
    orderLogEntity.ym = ym;
  }
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    orderLogEntity.content = content;
  }
  final String? ymd = jsonConvert.convert<String>(json['ymd']);
  if (ymd != null) {
    orderLogEntity.ymd = ymd;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    orderLogEntity.status = status;
  }
  final String? action = jsonConvert.convert<String>(json['action']);
  if (action != null) {
    orderLogEntity.action = action;
  }
  final String? beginTime = jsonConvert.convert<String>(json['beginTime']);
  if (beginTime != null) {
    orderLogEntity.beginTime = beginTime;
  }
  final String? exception = jsonConvert.convert<String>(json['exception']);
  if (exception != null) {
    orderLogEntity.exception = exception;
  }
  final String? errorMessage = jsonConvert.convert<String>(json['errorMessage']);
  if (errorMessage != null) {
    orderLogEntity.errorMessage = errorMessage;
  }
  return orderLogEntity;
}

Map<String, dynamic> $OrderLogEntityToJson(OrderLogEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['createTime'] = entity.createTime;
  data['updateTime'] = entity.updateTime;
  data['orderId'] = entity.orderId;
  data['type'] = entity.type;
  data['actionType'] = entity.actionType;
  data['scanType'] = entity.scanType;
  data['mobile'] = entity.mobile;
  data['smsTplId'] = entity.smsTplId;
  data['smsContent'] = entity.smsContent;
  data['wordNum'] = entity.wordNum;
  data['billingNum'] = entity.billingNum;
  data['billingPrice'] = entity.billingPrice;
  data['billingMoney'] = entity.billingMoney;
  data['secretWaybill'] = entity.secretWaybill;
  data['ym'] = entity.ym;
  data['content'] = entity.content;
  data['ymd'] = entity.ymd;
  data['status'] = entity.status;
  data['action'] = entity.action;
  data['beginTime'] = entity.beginTime;
  data['exception'] = entity.exception;
  data['errorMessage'] = entity.errorMessage;
  return data;
}

extension OrderLogEntityExtension on OrderLogEntity {
  OrderLogEntity copyWith({
    String? id,
    String? createTime,
    String? updateTime,
    String? orderId,
    int? type,
    int? actionType,
    String? scanType,
    String? mobile,
    String? smsTplId,
    String? smsContent,
    int? wordNum,
    int? billingNum,
    int? billingPrice,
    int? billingMoney,
    int? secretWaybill,
    String? ym,
    String? content,
    String? ymd,
    int? status,
    String? action,
    String? beginTime,
    String? exception,
    String? errorMessage,
  }) {
    return OrderLogEntity()
      ..id = id ?? this.id
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime
      ..orderId = orderId ?? this.orderId
      ..type = type ?? this.type
      ..actionType = actionType ?? this.actionType
      ..scanType = scanType ?? this.scanType
      ..mobile = mobile ?? this.mobile
      ..smsTplId = smsTplId ?? this.smsTplId
      ..smsContent = smsContent ?? this.smsContent
      ..wordNum = wordNum ?? this.wordNum
      ..billingNum = billingNum ?? this.billingNum
      ..billingPrice = billingPrice ?? this.billingPrice
      ..billingMoney = billingMoney ?? this.billingMoney
      ..secretWaybill = secretWaybill ?? this.secretWaybill
      ..ym = ym ?? this.ym
      ..content = content ?? this.content
      ..ymd = ymd ?? this.ymd
      ..status = status ?? this.status
      ..action = action ?? this.action
      ..beginTime = beginTime ?? this.beginTime
      ..exception = exception ?? this.exception
      ..errorMessage = errorMessage ?? this.errorMessage;
  }
}