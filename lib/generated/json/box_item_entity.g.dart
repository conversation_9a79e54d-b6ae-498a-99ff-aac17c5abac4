import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/box_item_entity.dart';

BoxItemEntity $BoxItemEntityFromJson(Map<String, dynamic> json) {
  final BoxItemEntity boxItemEntity = BoxItemEntity();
  final String? cabinetLocationCode = jsonConvert.convert<String>(json['cabinetLocationCode']);
  if (cabinetLocationCode != null) {
    boxItemEntity.cabinetLocationCode = cabinetLocationCode;
  }
  final String? cabinetId = jsonConvert.convert<String>(json['cabinetId']);
  if (cabinetId != null) {
    boxItemEntity.cabinetId = cabinetId;
  }
  final String? cabinetCode = jsonConvert.convert<String>(json['cabinetCode']);
  if (cabinetCode != null) {
    boxItemEntity.cabinetCode = cabinetCode;
  }
  final int? serialNo = jsonConvert.convert<int>(json['serialNo']);
  if (serialNo != null) {
    boxItemEntity.serialNo = serialNo;
  }
  final String? cabinetName = jsonConvert.convert<String>(json['cabinetName']);
  if (cabinetName != null) {
    boxItemEntity.cabinetName = cabinetName;
  }
  final String? cabinetLocationNo = jsonConvert.convert<String>(json['cabinetLocationNo']);
  if (cabinetLocationNo != null) {
    boxItemEntity.cabinetLocationNo = cabinetLocationNo;
  }
  final String? boxId = jsonConvert.convert<String>(json['boxId']);
  if (boxId != null) {
    boxItemEntity.boxId = boxId;
  }
  final int? boxType = jsonConvert.convert<int>(json['boxType']);
  if (boxType != null) {
    boxItemEntity.boxType = boxType;
  }
  final int? pcbNo = jsonConvert.convert<int>(json['pcbNo']);
  if (pcbNo != null) {
    boxItemEntity.pcbNo = pcbNo;
  }
  final String? boxLabel = jsonConvert.convert<String>(json['boxLabel']);
  if (boxLabel != null) {
    boxItemEntity.boxLabel = boxLabel;
  }
  final bool? isUsed = jsonConvert.convert<bool>(json['isUsed']);
  if (isUsed != null) {
    boxItemEntity.isUsed = isUsed;
  }
  return boxItemEntity;
}

Map<String, dynamic> $BoxItemEntityToJson(BoxItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['cabinetLocationCode'] = entity.cabinetLocationCode;
  data['cabinetId'] = entity.cabinetId;
  data['cabinetCode'] = entity.cabinetCode;
  data['serialNo'] = entity.serialNo;
  data['cabinetName'] = entity.cabinetName;
  data['cabinetLocationNo'] = entity.cabinetLocationNo;
  data['boxId'] = entity.boxId;
  data['boxType'] = entity.boxType;
  data['pcbNo'] = entity.pcbNo;
  data['boxLabel'] = entity.boxLabel;
  data['isUsed'] = entity.isUsed;
  return data;
}

extension BoxItemEntityExtension on BoxItemEntity {
  BoxItemEntity copyWith({
    String? cabinetLocationCode,
    String? cabinetId,
    String? cabinetCode,
    int? serialNo,
    String? cabinetName,
    String? cabinetLocationNo,
    String? boxId,
    int? boxType,
    int? pcbNo,
    String? boxLabel,
    bool? isUsed,
  }) {
    return BoxItemEntity()
      ..cabinetLocationCode = cabinetLocationCode ?? this.cabinetLocationCode
      ..cabinetId = cabinetId ?? this.cabinetId
      ..cabinetCode = cabinetCode ?? this.cabinetCode
      ..serialNo = serialNo ?? this.serialNo
      ..cabinetName = cabinetName ?? this.cabinetName
      ..cabinetLocationNo = cabinetLocationNo ?? this.cabinetLocationNo
      ..boxId = boxId ?? this.boxId
      ..boxType = boxType ?? this.boxType
      ..pcbNo = pcbNo ?? this.pcbNo
      ..boxLabel = boxLabel ?? this.boxLabel
      ..isUsed = isUsed ?? this.isUsed;
  }
}