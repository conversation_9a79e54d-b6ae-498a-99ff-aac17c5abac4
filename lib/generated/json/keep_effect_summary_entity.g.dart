import 'package:cabinet_flutter_app/generated/json/base/json_convert_content.dart';
import 'package:cabinet_flutter_app/common/entitys/keep_effect_summary_entity.dart';

KeepEffectSummaryEntity $KeepEffectSummaryEntityFromJson(Map<String, dynamic> json) {
  final KeepEffectSummaryEntity keepEffectSummaryEntity = KeepEffectSummaryEntity();
  final int? keepEffectCount = jsonConvert.convert<int>(json['keepEffectCount']);
  if (keepEffectCount != null) {
    keepEffectSummaryEntity.keepEffectCount = keepEffectCount;
  }
  final int? gt3dayKeepEffectCount = jsonConvert.convert<int>(json['gt3dayKeepEffectCount']);
  if (gt3dayKeepEffectCount != null) {
    keepEffectSummaryEntity.gt3dayKeepEffectCount = gt3dayKeepEffectCount;
  }
  final int? gt7dayKeepEffectCount = jsonConvert.convert<int>(json['gt7dayKeepEffectCount']);
  if (gt7dayKeepEffectCount != null) {
    keepEffectSummaryEntity.gt7dayKeepEffectCount = gt7dayKeepEffectCount;
  }
  return keepEffectSummaryEntity;
}

Map<String, dynamic> $KeepEffectSummaryEntityToJson(KeepEffectSummaryEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['keepEffectCount'] = entity.keepEffectCount;
  data['gt3dayKeepEffectCount'] = entity.gt3dayKeepEffectCount;
  data['gt7dayKeepEffectCount'] = entity.gt7dayKeepEffectCount;
  return data;
}

extension KeepEffectSummaryEntityExtension on KeepEffectSummaryEntity {
  KeepEffectSummaryEntity copyWith({
    int? keepEffectCount,
    int? gt3dayKeepEffectCount,
    int? gt7dayKeepEffectCount,
  }) {
    return KeepEffectSummaryEntity()
      ..keepEffectCount = keepEffectCount ?? this.keepEffectCount
      ..gt3dayKeepEffectCount = gt3dayKeepEffectCount ?? this.gt3dayKeepEffectCount
      ..gt7dayKeepEffectCount = gt7dayKeepEffectCount ?? this.gt7dayKeepEffectCount;
  }
}