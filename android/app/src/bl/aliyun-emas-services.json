{"config": {"emas.appKey": "333759048", "emas.appSecret": "5cba2e6ecab54a6a91876d246c3a726a", "emas.packageName": "com.blzng.cabinet.helper", "hotfix.idSecret": "333759048-1", "hotfix.rsaSecret": "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCCzKxv3Rhlwmzbt0McF4+343bekIAOrT15pRf5sNPWusWtZ3CcN0jkWoxew53n0/bMm0U3QzUIudbv6dUQTKxOEUtnkf4fIid8qC3GvvyTJxJwgX0kTWBS50cMXN3y3sxGkXjcdB7RWStRo91+LhdhSOgqwRhAK4s5rRpw9sNSzmrER5Sw9b6iBKvy9Xno7odxRlW4uTZxyqIUH45WL/GTKovOZ0sNipDDn1cC3kyG6XKakfFJ8xM/TBV+/vaIywVincjlBt+igMSKgas/gCDpkDrzv/vrC8U23hStwVQP3CpOYD5qvaGwc6l2J69ezjv2GUmuXgHchvJI/EzqZE+zAgMBAAECggEAZSnoNJzMQegeSdF0FkYi4hcL2Rfp83AKHAuUf4pETdAnE/btIeWgev2Nk724Y5WNTLLK1fMlEZqqouQ35mNm4Pp/V5uS8IF6CPAKd60/rJCLsnh0UZ+G4JgAW494GmA3nlzlHZyyOTi+SGZ06SK88oFxD6BJIViMVg+MY9J3P5NCPWldV/zkL3EEK2wBjmq2N3rwh40YUafx6+8nmQOcNRdPSkhuwDrC8OsL4QYW3NCzkQv9xdU50Xtkc++M3YpFtwNFBU+qbkI4/3pqqRA77CP1hn9mDCgNYFZ6APuthcXNCYyL546YVXnjygGhveLIWOVPPfqpiiC6VABDe8cwAQKBgQDtlKh268VWscDZ7CF/fkQtLZiAgS6tIIIUP763E/wWK1ArutQvCFlZIbLSjnVwM1UTeNiOCFytGbfBNqaxfSlRrTA3M2A75T8qwN0J+fjaWxZhCYfCQuO1lfQTvjcCSpC2EEKgA9N6bGYmRncl67tbRybVLHDq8AGTkwbA9MVr3QKBgQCM8LG6h/c2lVReKnao/YM31D0lXbHPTbn9kU1ZKCAxvt2V4vhQ/JQcQqlNHvxLqlRJ84ESCO0G68Z7J5jpgSveC6+HTqMqiaMExNuwm4i0eU4Lem/rYFPkReebpT+9uRNwnAwOTOwX5Eiyrofpc7KF4H6JBCOEJvljSv8gBRT4zwKBgDqK2Bw9qqnbMS9BzVtSLNCJHLwFmQ/WI9eDgaYxSXvY/m9YITLZttKMyDi0G+Ii9ALRU2NkUhnhbXMx2U+BDO5wY6Imib8YlUEetxjfPN/pdBFDPO20d9Hl+nfm9j0dTJ1gUlqFttXC4kTqNi/k74zlcn8y7TwnGexYXWCsr/0NAoGAVAn5kAYe7JciVXvwgVeY9KNDSpKRwdDRBFetzWnVuJPcwNEzy+av0iIVnXzKZ7vKwsXFwqE75JKWKT01OpbhN2e0ZHQJawXjBKb1cFttql9ioAmDztADdewQ7Iged9xxsQX7UlOfu6dNie3z8MoiOx3flYe4PcaXkhNYCRm+pZUCgYBCEOXZ9JEGptIppSbsy+TBKwYLVR/DHqxjfrP7OA775WHjZsSCtq/ZmYa83nGPDowwQRHvsE7sRHcs6x8rdBtwQf2rqxXcLPxU8GqG4/TP4fPRwXHvKd9tq5BtXAHVE4nauoYg7/s2q4yMc+Huuq9TEvFcqt0d39CR6+wk/T+atw==", "httpdns.accountId": "104355", "httpdns.secretKey": "54b2c81136d455ec5ed06db77fe79d59", "appmonitor.tlog.rsaSecret": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC7vJGrgf/N0jRmDRIb21Q1+moio4Ws5HmWu5pyotoF+iR73Xq75bF6nVhH6nh4yBMGB9wDwFydRSzQiaFSDreKPm190QOtBZ3PwXxvrOsbjJ25QLLq7J8eiCo45Dq+On9j0PyIwz1rdtlknpD0fV6u0x9nqZ9k4NCafJL8U4qo8wIDAQAB", "appmonitor.rsaSecret": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC7vJGrgf/N0jRmDRIb21Q1+moio4Ws5HmWu5pyotoF+iR73Xq75bF6nVhH6nh4yBMGB9wDwFydRSzQiaFSDreKPm190QOtBZ3PwXxvrOsbjJ25QLLq7J8eiCo45Dq+On9j0PyIwz1rdtlknpD0fV6u0x9nqZ9k4NCafJL8U4qo8wIDAQAB"}, "services": {"hotfix_service": {"status": 1, "version": "3.3.8"}, "ha-adapter_service": {"status": 1, "version": "*******-open"}, "feedback_service": {"status": 1, "version": "3.3.9"}, "tlog_service": {"status": 1, "version": "*******-open"}, "httpdns_service": {"status": 1, "version": "2.2.2"}, "apm_service": {"status": 1, "version": "1.1.0.0-open"}, "man_service": {"status": 1, "version": "1.2.7"}, "cps_service": {"status": 1, "version": "3.7.7"}}, "use_maven": true, "proguard_keeplist": "\n#httpdns\n-keep class com.taobao.** {*;}\n-keep class com.alibaba.** {*;}\n-keep class com.ta.**{*;}\n-keep class com.ut.**{*;}\n-dontwarn com.taobao.**\n-dontwarn com.alibaba.**\n-dontwarn com.ta.**\n-dontwarn com.ut.**\n\n#cps\n-keep class com.taobao.** {*;}\n-keep class com.alibaba.** {*;}\n-keep class com.ta.**{*;}\n-keep class com.ut.**{*;}\n-dontwarn com.taobao.**\n-dontwarn com.alibaba.**\n-dontwarn com.ta.**\n-dontwarn com.ut.**\n-keepclasseswithmembernames class ** {\nnative <methods>;\n}\n-keepattributes Signature\n-keep class sun.misc.Unsafe { *; }\n-keep class com.alipay.** {*;}\n-dontwarn com.alipay.**\n-keep class anet.**{*;}\n-keep class org.android.spdy.**{*;}\n-keep class org.android.agoo.**{*;}\n-dontwarn anet.**\n-dontwarn org.android.spdy.**\n-dontwarn org.android.agoo.**\n\n#hotfix\n#基线包使用，生成mapping.txt\n-printmapping mapping.txt\n#生成的mapping.txt在app/buidl/outputs/mapping/release路径下，移动到/app路径下\n#修复后的项目使用，保证混淆结果一致\n#-applymapping mapping.txt\n#hotfix\n-keep class com.taobao.sophix.**{*;}\n-keep class com.ta.utdid2.device.**{*;}\n#防止inline\n-dontoptimize\n\n#man\n-keep class com.taobao.** {*;}\n-keep class com.alibaba.** {*;}\n-keep class com.ta.**{*;}\n-keep class com.ut.**{*;}\n-dontwarn com.taobao.**\n-dontwarn com.alibaba.**\n-dontwarn com.ta.**\n-dontwarn com.ut.**\n\n#feedback\n-keep class com.taobao.** {*;}\n-keep class com.alibaba.** {*;}\n-keep class com.ta.**{*;}\n-keep class com.ut.**{*;}\n-dontwarn com.taobao.**\n-dontwarn com.alibaba.**\n-dontwarn com.ta.**\n-dontwarn com.ut.**\n"}