package com.xmks.cabinet.helper

import android.Manifest
import android.annotation.SuppressLint
import android.app.AlertDialog
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.provider.Settings
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

/**
 * 继承了Activity，实现Android6.0的运行时权限检测
 * 需要进行运行时权限检测的Activity可以继承这个类
 *
 * @创建时间：2016年5月27日 下午3:01:31
 * @项目名称： AMapLocationDemo
 * <AUTHOR>
 * @文件名称：PermissionsChecker.java
 * @类型名称：PermissionsChecker
 * @since 2.5.0
 */
@SuppressLint("Registered")
open class CheckPermissionsActivity : io.flutter.embedding.android.FlutterActivity(),
    ActivityCompat.OnRequestPermissionsResultCallback {
    /**
     * 需要进行检测的权限数组
     */
    private var needPermissions = arrayOf(
        Manifest.permission.INTERNET,
        Manifest.permission.ACCESS_NETWORK_STATE,
//        Manifest.permission.ACCESS_COARSE_LOCATION,
//        Manifest.permission.RECORD_AUDIO,
//        Manifest.permission.ACCESS_FINE_LOCATION,
//        Manifest.permission.MANAGE_EXTERNAL_STORAGE,
//        Manifest.permission.WRITE_EXTERNAL_STORAGE,
//        Manifest.permission.READ_EXTERNAL_STORAGE,
//        Manifest.permission.BLUETOOTH,
//        Manifest.permission.BLUETOOTH_ADMIN,
//        Manifest.permission.CAMERA,
//        Manifest.permission.READ_PHONE_STATE
    )

    /**
     * 判断是否需要检测，防止不停的弹框
     */
    private var isNeedCheck = true

    override fun onResume() {
        super.onResume()
        if (isNeedCheck) {
            checkPermissions(*needPermissions)
        }
    }

    /**
     *
     * @since 2.5.0
     */
    private fun checkPermissions(vararg permissions: String) {
        val needRequestPermissonList = findDeniedPermissions(permissions as Array<*>)
        if (needRequestPermissonList.isNotEmpty()) {
            ActivityCompat.requestPermissions(
                this,
                needRequestPermissonList.toTypedArray(),
                PERMISSON_REQUESTCODE
            )
        }

//        if (Build.VERSION.SDK_INT >= 30) {
//            if (!Environment.isExternalStorageManager()) {
//                val builder = AlertDialog.Builder(this)
//                builder.setTitle(R.string.notifyTitle)
//                builder.setMessage("需要开启所有文件访问权限")
//
//                // 拒绝, 退出应用
//                builder.setNegativeButton(R.string.cancel) { _, _ -> finish() }
//
//                builder.setPositiveButton("去开启") { _, _ -> startPrivateSettings() }
//
//                builder.setCancelable(false)
//
//                builder.show()
//                isNeedCheck = false
//            }
//        }
    }

    /**
     * 获取权限集中需要申请权限的列表
     *
     * @param permissions
     * @return
     * @since 2.5.0
     */
    private fun findDeniedPermissions(permissions: Array<*>): List<String> {
        val needRequestPermissonList = ArrayList<String>()
        for (perm in permissions) {
            if (ContextCompat.checkSelfPermission(
                    this,
                    perm as String
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                needRequestPermissonList.add(perm)
            } else {
                if (ActivityCompat.shouldShowRequestPermissionRationale(
                        this, perm
                    )
                ) {
                    needRequestPermissonList.add(perm)
                }
            }
        }
        return needRequestPermissonList
    }

    /**
     * 检测是否说有的权限都已经授权
     * @param grantResults
     * @return
     * @since 2.5.0
     */
    private fun verifyPermissions(grantResults: IntArray): Boolean {
        for (result in grantResults) {
            if (result != PackageManager.PERMISSION_GRANTED) {
                return false
            }
        }
        return true
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>, paramArrayOfInt: IntArray
    ) {
        if (requestCode == PERMISSON_REQUESTCODE) {
            if (!verifyPermissions(paramArrayOfInt)) {
                showMissingPermissionDialog()
                isNeedCheck = false
            }
        }
    }

    /**
     * 显示提示信息
     *
     * @since 2.5.0
     */
    private fun showMissingPermissionDialog() {
        val builder = AlertDialog.Builder(this)
        builder.setTitle(R.string.notifyTitle)
        builder.setMessage(R.string.notifyMsg)

        // 拒绝, 退出应用
        builder.setNegativeButton(R.string.cancel) { _, _ -> finish() }

        builder.setPositiveButton(R.string.setting) { _, _ -> startAppSettings() }

        builder.setCancelable(false)

        builder.show()
    }

    /**
     * 启动应用的设置
     *
     * @since 2.5.0
     */
    private fun startAppSettings() {
        val intent = Intent(
            Settings.ACTION_APPLICATION_DETAILS_SETTINGS
        )
        intent.data = Uri.parse("package:$packageName")
        startActivity(intent)
    }

    private fun startPrivateSettings() {
        val intent = Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION)
        startActivity(intent)
    }

//    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
//        if (keyCode == KeyEvent.KEYCODE_BACK) {
//            this.finish()
//            return true
//        }
//        return super.onKeyDown(keyCode, event)
//    }

    companion object {

        private const val PERMISSON_REQUESTCODE = 0
    }

}
