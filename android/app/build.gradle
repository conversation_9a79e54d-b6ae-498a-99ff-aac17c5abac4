plugins {
    id "com.android.application"
    id "com.huawei.agconnect"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

android {
    namespace = "com.xmks.cabinet.helper"
    compileSdk = flutter.compileSdkVersion
//    ndkVersion = flutter.ndkVersion
    ndkVersion = ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }


    defaultConfig {
        minSdk = flutter.minSdkVersion
        targetSdk = 33
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }
    flavorDimensions 'channel'
    productFlavors {
        xm { //官方版本
            dimension "channel"
            applicationId "com.xmks.cabinet.helper"
            manifestPlaceholders = [
                    CHANNEL            : "xm",
                    PGYER_APPID        : "13c5f1e0acba5fc3c31db7759533e262",
                    ALI_APP_PUSH_KEY   : "333679731",
                    ALI_APP_PUSH_SECRET: "7009d262acd04aeb80cf5e51a4a4e8b7",
                    HUAWEI_APP_ID      : "106455771",
//                    VIVO_APP_ID        : "105567119",
//                    VIVO_APP_KEY       : "bdd8478dfbfbe9c2bae9b2271f66fd6e",
                    NLS_APP_KEY        : "mWbyr1RRsXZp3YdL",
                    NLS_ACCESS_KEY     : "LTAI4GGPjqqx8c5k738UQevd",
                    NLS_ACCESS_SECRET  : "******************************",
                    OSS_ACCESS_KEY     : "LTAI4GBn3as86qcLD82khyhK",
                    OSS_ACCESS_SECRET  : "******************************",
            ]
        }
        bl { // bl版本
            dimension "channel"
            applicationId "com.blzng.cabinet.helper"
            manifestPlaceholders = [
                    CHANNEL            : "bl",
                    PGYER_APPID        : "1c253d5fa2349c30ea501cd996ff996e",
                    ALI_APP_PUSH_KEY   : "333759048",
                    ALI_APP_PUSH_SECRET: "5cba2e6ecab54a6a91876d246c3a726a",
                    HUAWEI_APP_ID      : "106455771",
//                    VIVO_APP_ID        : "105567119",
//                    VIVO_APP_KEY       : "bdd8478dfbfbe9c2bae9b2271f66fd6e",
                    NLS_APP_KEY        : "mWbyr1RRsXZp3YdL",
                    NLS_ACCESS_KEY     : "LTAI4GGPjqqx8c5k738UQevd",
                    NLS_ACCESS_SECRET  : "******************************",
                    OSS_ACCESS_KEY     : "LTAI4GBn3as86qcLD82khyhK",
                    OSS_ACCESS_SECRET  : "******************************",
            ]
        }
//        productFlavors.all {
//            // 遍历productFlavors多渠道，设置渠道名称，在flutter层也能取到
//            flavor -> flavor.manifestPlaceholders.put("CHANNEL", name)
//        }
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
        main.jniLibs.srcDirs = ['libs']
        xm.res.srcDirs = ['src/main/res']
//        xmzg.res.srcDirs = ['src/main/res-xmzg']
        bl.res.srcDirs = ['src/main/res-bl']
//        blzg.res.srcDirs = ['src/main/res-blzg']
    }

    signingConfigs {
        release {
            storeFile file("../xmks.jks")
            storePassword "njxmks"
            keyAlias "njxmks"
            keyPassword "njxmks"
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.release
            shrinkResources false
            minifyEnabled false
            ndk {
//                abiFilters 'armeabi-v7a'
                abiFilters 'arm64-v8a'
            }
        }
        release {
            signingConfig signingConfigs.release
//            debuggable true
            shrinkResources true
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'),
                    'proguard-rules.pro'
            ndk {
//                abiFilters 'armeabi-v7a', 'arm64-v8a'
//                abiFilters 'armeabi-v7a'
                abiFilters 'arm64-v8a'
            }
        }
    }
    packagingOptions {
        jniLibs {
            keepDebugSymbols += ['*/*/*.so']
        }
    }

    androidResources {
        noCompress 'tflite', 'mlm', 'ms'
    }
    lint {
        checkReleaseBuilds false
        disable 'InvalidPackage'
    }
//    namespace 'com.xmks.cabinet.helper'
}

flutter {
    source '../..'
}
configurations {
    all*.exclude group: 'org.apache.httpcomponents' // 移除第三方库中的旧httpclient
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation fileTree(include: ['*.jar'], dir: 'libs')
//    //3D地图so及jar
    implementation 'com.amap.api:3dmap:8.1.0'
//    //定位功能
    implementation 'com.amap.api:location:6.2.0'
//    //搜索功能
    implementation 'com.amap.api:search:9.5.0'
    // 改用Android平台官方支持的版本
    implementation 'org.apache.httpcomponents:httpclient-android:4.3.5.1'
}
