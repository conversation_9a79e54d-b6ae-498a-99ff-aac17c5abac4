{"config": {"emas.appKey": "333679731", "emas.appSecret": "7009d262acd04aeb80cf5e51a4a4e8b7", "emas.packageName": "com.xmks.cabinet.helper", "hotfix.idSecret": "333679731-1", "hotfix.rsaSecret": "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQDJmFBre+FKbr/kkBXjRVVYkMW029j6Gd1AdYdLtnuCyoe0CUN99vtzHy2H6pYAUq31j87u32SeVuO77rZG6BrYeCY653Gtq2s/u2qdkpVCzDMV7saht24iNlUOBh2wemgLTuZuSQmJC/lsTyqYVeed2EncJjYGS34OGKMShtdY3uAuduapCDytqIT5gToXxhFiJwT4Fak80JocAdILZBztZMAv9gpN4VAa/dcXS4uhL1rQi7P+QuOwfOcdZn+eSF3LNrveON83MahhZQBXRAivK6yDgGexi3mKN7jeZKSUys9TKG6c2pTmy5Z3CoPOI5zXZ6Bab4swpbJpTie16CozAgMBAAECggEAbQlkHe6gGTkzszU3kXhzw/Zx6POsNQH+SJoI9quZvFE8+uSzy5LlyAuJ/eSlA0G084e33j2X14iZnpFrp4hs1L6PtvZt5wg7dvownU+mSb3Wt7sxVlC3UevfLzmhn01lrxAa1HXOoYCHhMyA9mooX5iXwmoseFUmtgsfUKr8GQVkozdWNYlMpVPu3e0P+yaBj0km+aQjlcRSV+QbMcbn2W6++wDikBNeOZE3AduuQuZoupxkgYqU0GPBFfMi9rGZma6u7iY1W3Vgdrqm9P+7RZ0Xp/IOokDPKq7CmWBr6QoiRyaqoB7JA2yyMAhxe2WwN1reToNLNL0nUUykXY3sGQKBgQD5GfYLFiZIp0R2szteS+Y1rgoL5Cc9yxWHAOS21PQK5xFtzFRwlH84ItNn61d9dPkr5dK8h/f3csoofsMLQXkX3iGn6GqsckGlXbS27OXaUjz789k+aHNpxW7A/9F4zOOwYxLZt6bZWkF+gPPdYnQbtfVNKM5S1W+WRSByhRTZbQKBgQDPLYyy5JMlGCGY2FFT7wNvphUzPQ9hJ00NmeaX+XJkMdsOqaDaPwjxRizXn6j5XJ3ACV89MtwZD660PIwIuBeg9T+Y00/b/dWz0Q+rsD5UdPNGFDc8qtHPR4pwDPKQpAQhKQBuCs/BvnXYhH0BJSsRuu+qhSfZMOx1hKTNQqluHwKBgQDFAntNmV795wyGtIonse+IVPUtImzGum90Loi08Qdx9/4Y0JmX0Fotk52PetnfJzxKM6HP6dl7oxWvoVGw/T9AWTZVDcQQTu0MVGgwpHvFh3Uj71/Js+JTrQpDKhAJckJBppmqB022aWOTIXm9hbg+klJHjBZh/NwcBN/8srQioQKBgQCcrir446pwZDaMXBPtcRXdKvJfhK8L32gDgD+G8YxUP+wVw601QHSn21VwYHuu0h8vKydOphcUEOsjvaJ9uZONE8xb7Hs5uVcTnWjHhAsvXh7ZAdLgW6M+qjEFGpWbU58X4OVFU4HZWQvQiinrYsuWphQ4lmkTdU9udLkZzm9KKwKBgQDGovA8fyi+0XnhM/F8I1LvxexxGKEQfeLZpHYvTJX9yWohlL/I2gjaoOFL46EEHbiqVdlfMh/871sMW1vOaD/3LrFdgFkhBi8oqnX4cPivgoDlxBuH+NoqVp78ltHP1+smHV1zKuQJe7HiWIW3Jkt1Q0A3D1ro/EuMp43+ExfLuQ==", "httpdns.accountId": "104355", "httpdns.secretKey": "54b2c81136d455ec5ed06db77fe79d59", "appmonitor.tlog.rsaSecret": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCzXkEOa2U6FUolyQki+MShIz6kQB9H3bNWfzdGiWyoctI1QF5ZyOEVIhtfhn7P5DZ0hgYtzU6Sq60fWwaL382M4Is/oz1CnSmg7pFhvAUPulZL3q9qF1HBUi8+KcCa+3gvUE1SMl49WHKU+cBw+hxWCSCW+r5eXBRGwkjmnYAazQIDAQAB", "appmonitor.rsaSecret": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCzXkEOa2U6FUolyQki+MShIz6kQB9H3bNWfzdGiWyoctI1QF5ZyOEVIhtfhn7P5DZ0hgYtzU6Sq60fWwaL382M4Is/oz1CnSmg7pFhvAUPulZL3q9qF1HBUi8+KcCa+3gvUE1SMl49WHKU+cBw+hxWCSCW+r5eXBRGwkjmnYAazQIDAQAB"}, "services": {"hotfix_service": {"status": 0, "version": "3.3.5"}, "ha-adapter_service": {"status": 1, "version": "1.1.5.2-open"}, "feedback_service": {"status": 0, "version": "3.3.7"}, "tlog_service": {"status": 0, "version": "1.1.4.4-open"}, "httpdns_service": {"status": 0, "version": "2.2.2"}, "apm_service": {"status": 1, "version": "1.1.0.0-open"}, "man_service": {"status": 1, "version": "1.2.7"}, "cps_service": {"status": 1, "version": "3.7.5"}}, "use_maven": true, "proguard_keeplist": "\n#httpdns\n-keep class com.taobao.** {*;}\n-keep class com.alibaba.** {*;}\n-keep class com.ta.**{*;}\n-keep class com.ut.**{*;}\n-dontwarn com.taobao.**\n-dontwarn com.alibaba.**\n-dontwarn com.ta.**\n-dontwarn com.ut.**\n\n#cps\n-keep class com.taobao.** {*;}\n-keep class com.alibaba.** {*;}\n-keep class com.ta.**{*;}\n-keep class com.ut.**{*;}\n-dontwarn com.taobao.**\n-dontwarn com.alibaba.**\n-dontwarn com.ta.**\n-dontwarn com.ut.**\n-keepclasseswithmembernames class ** {\nnative <methods>;\n}\n-keepattributes Signature\n-keep class sun.misc.Unsafe { *; }\n-keep class com.alipay.** {*;}\n-dontwarn com.alipay.**\n-keep class anet.**{*;}\n-keep class org.android.spdy.**{*;}\n-keep class org.android.agoo.**{*;}\n-dontwarn anet.**\n-dontwarn org.android.spdy.**\n-dontwarn org.android.agoo.**\n\n#hotfix\n#基线包使用，生成mapping.txt\n-printmapping mapping.txt\n#生成的mapping.txt在app/buidl/outputs/mapping/release路径下，移动到/app路径下\n#修复后的项目使用，保证混淆结果一致\n#-applymapping mapping.txt\n#hotfix\n-keep class com.taobao.sophix.**{*;}\n-keep class com.ta.utdid2.device.**{*;}\n#防止inline\n-dontoptimize\n\n#man\n-keep class com.taobao.** {*;}\n-keep class com.alibaba.** {*;}\n-keep class com.ta.**{*;}\n-keep class com.ut.**{*;}\n-dontwarn com.taobao.**\n-dontwarn com.alibaba.**\n-dontwarn com.ta.**\n-dontwarn com.ut.**\n\n#feedback\n-keep class com.taobao.** {*;}\n-keep class com.alibaba.** {*;}\n-keep class com.ta.**{*;}\n-keep class com.ut.**{*;}\n-dontwarn com.taobao.**\n-dontwarn com.alibaba.**\n-dontwarn com.ta.**\n-dontwarn com.ut.**\n"}