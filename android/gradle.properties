# 增加gradle运行的java虚拟机的大小
org.gradle.jvmargs=-Xmx4096m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
# 模块化项目和并行化编译
org.gradle.parallel=true
# 开启configuration on demand
org.gradle.configureondemand=true
org.gradle.daemon=true
# org.gradle.java.home=/Library/Java/JavaVirtualMachines/temurin-21.jdk

# 配置构建缓存
#org.gradle.caching=true
#systemProp.http.proxyHost=127.0.0.1
#systemProp.http.proxyPort=1087
#systemProp.https.proxyHost=127.0.0.1
#systemProp.https.proxyPort=1087
android.useAndroidX=true
android.enableJetifier=true
android.useDeprecatedNdk=true