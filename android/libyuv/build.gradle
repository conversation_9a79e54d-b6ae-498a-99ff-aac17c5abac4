apply plugin: 'com.android.library'

android {
    compileSdkVersion 29


    defaultConfig {

        externalNativeBuild {
            cmake {
                cppFlags ""
            }
        }
        consumerProguardFiles 'consumer-rules.pro'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    externalNativeBuild {
        cmake {
            path 'CMakeLists.txt'
        }
    }
    namespace 'com.libyuv.util'
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
}
