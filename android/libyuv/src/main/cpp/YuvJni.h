/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_libyuv_util_YuvUtil */

#ifndef _Included_com_libyuv_util_YuvUtil
#define _Included_com_libyuv_util_YuvUtil
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvCompress
 * Signature: ([BII[BIIIIZ)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvCompress
  (JNIEnv *, jclass, jbyteArray, jint, jint, jbyteArray, jint, jint, jint, jint, jboolean);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvCropI420
 * Signature: ([BII[BIIII)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvCropI420
  (JNIEnv *, jclass, jbyteArray, jint, jint, jbyte<PERSON>rray, jint, jint, jint, jint);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvMirrorI420
 * Signature: ([BII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvMirrorI420
  (JNIEnv *, jclass, jbyteArray, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvScaleI420
 * Signature: ([BII[BIII)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvScaleI420
  (JNIEnv *, jclass, jbyteArray, jint, jint, jbyteArray, jint, jint, jint);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvRotateI420
 * Signature: ([BII[BI)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvRotateI420
  (JNIEnv *, jclass, jbyteArray, jint, jint, jbyteArray, jint);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvNV21ToI420
 * Signature: ([BII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvNV21ToI420
  (JNIEnv *, jclass, jbyteArray, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvI420ToNV21
 * Signature: ([BII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvI420ToNV21
  (JNIEnv *, jclass, jbyteArray, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvNV21ToI420AndRotate
 * Signature: ([BII[BI)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvNV21ToI420AndRotate
  (JNIEnv *, jclass, jbyteArray, jint, jint, jbyteArray, jint);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvI420ToRGB24
 * Signature: ([BII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvI420ToRGB24
  (JNIEnv *, jclass, jbyteArray, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvI420ToARGB
 * Signature: ([BIII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvI420ToARGB
  (JNIEnv *, jclass, jbyteArray, jint, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvI420ToRGBAMac
 * Signature: ([BIII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvI420ToRGBAMac
  (JNIEnv *, jclass, jbyteArray, jint, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvI420ToARGB4444
 * Signature: ([BIII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvI420ToARGB4444
  (JNIEnv *, jclass, jbyteArray, jint, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvI420ToRGB565
 * Signature: ([BII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvI420ToRGB565
  (JNIEnv *, jclass, jbyteArray, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvI420ToRGB565Android
 * Signature: ([BII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvI420ToRGB565Android
  (JNIEnv *, jclass, jbyteArray, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvI420ToARGB1555
 * Signature: ([BIII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvI420ToARGB1555
  (JNIEnv *, jclass, jbyteArray, jint, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvI420ToYUY2
 * Signature: ([BIII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvI420ToYUY2
  (JNIEnv *, jclass, jbyteArray, jint, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvI420ToUYVY
 * Signature: ([BIII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvI420ToUYVY
  (JNIEnv *, jclass, jbyteArray, jint, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvI420ToYV12
 * Signature: ([BIII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvI420ToYV12
  (JNIEnv *, jclass, jbyteArray, jint, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvYV12ToI420
 * Signature: ([BII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvYV12ToI420
  (JNIEnv *, jclass, jbyteArray, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvNV12ToI420
 * Signature: ([BII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvNV12ToI420
  (JNIEnv *, jclass, jbyteArray, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvI420ToNv12
 * Signature: ([BII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvI420ToNv12
  (JNIEnv *, jclass, jbyteArray, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvNV12ToI420AndRotate
 * Signature: ([BII[BI)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvNV12ToI420AndRotate
  (JNIEnv *, jclass, jbyteArray, jint, jint, jbyteArray, jint);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvNV12ToRGB565
 * Signature: ([BII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvNV12ToRGB565
  (JNIEnv *, jclass, jbyteArray, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvI420ToRGBAIPhone
 * Signature: ([BIII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvI420ToRGBAIPhone
  (JNIEnv *, jclass, jbyteArray, jint, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvI420Copy
 * Signature: ([BIII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvI420Copy
  (JNIEnv *, jclass, jbyteArray, jint, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvUYVYToI420
 * Signature: ([BII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvUYVYToI420
  (JNIEnv *, jclass, jbyteArray, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvYUY2ToI420
 * Signature: ([BII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvYUY2ToI420
  (JNIEnv *, jclass, jbyteArray, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvRGB24ToARGB
 * Signature: ([BIII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvRGB24ToARGB
  (JNIEnv *, jclass, jbyteArray, jint, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvRGB24ToI420
 * Signature: ([BII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvRGB24ToI420
  (JNIEnv *, jclass, jbyteArray, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvI420ToARGBMac
 * Signature: ([BIII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvI420ToARGBMac
  (JNIEnv *, jclass, jbyteArray, jint, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvARGBMacToI420
 * Signature: ([BII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvARGBMacToI420
  (JNIEnv *, jclass, jbyteArray, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvMirrorI420LeftRight
 * Signature: ([BII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvMirrorI420LeftRight
  (JNIEnv *, jclass, jbyteArray, jint, jint, jbyteArray);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvMirrorI420UpDown
 * Signature: ([BII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_yuvMirrorI420UpDown
  (JNIEnv *, jclass, jbyteArray, jint, jint, jbyteArray);

JNIEXPORT void JNICALL Java_com_libyuv_util_YuvUtil_test
(JNIEnv *env, jclass clazz);

/*
 * Class:     com_libyuv_util_YuvUtil
 * Method:    yuvMirrorI420LeftRightAndRotate
 * Signature: ([BII[B)V
 */
JNIEXPORT void JNICALL Java_com_libyuv_util_yuvMirrorI420LeftRightAndRotate
  (JNIEnv *, jclass, jbyteArray, jint, jint, jbyteArray, jint);

#ifdef __cplusplus
}
#endif
#endif

