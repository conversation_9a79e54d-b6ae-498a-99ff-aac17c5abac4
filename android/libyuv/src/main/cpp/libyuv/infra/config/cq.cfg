# Commit Queue configuration file. The documentation of the format can be found
# at http://luci-config.appspot.com/schemas/projects/refs:cq.cfg.

version: 1
cq_name: "libyuv"
cq_status_url: "https://chromium-cq-status.appspot.com"
git_repo_url: "https://chromium.googlesource.com/libyuv/libyuv.git"

gerrit {}
rietveld {
  url: "https://codereview.chromium.org"
}


verifiers {
  reviewer_lgtm {
    committer_list: "project-libyuv-committers"
    dry_run_access_list: "project-libyuv-tryjob-access"
  }
  gerrit_cq_ability {
    committer_list: "project-libyuv-committers"
    dry_run_access_list: "project-libyuv-tryjob-access"
  }

  try_job {
    buckets {
      name: "master.tryserver.libyuv"
      builders { name: "win" }
      builders { name: "win_rel" }
      builders { name: "win_x64_rel" }
      builders { name: "win_clang" }
      builders { name: "win_clang_rel" }
      builders { name: "win_x64_clang_rel" }
      builders { name: "mac" }
      builders { name: "mac_rel" }
      builders { name: "mac_asan" }
      builders { name: "ios" }
      builders { name: "ios_rel" }
      builders { name: "ios_arm64" }
      builders { name: "ios_arm64_rel" }
      builders { name: "linux" }
      builders { name: "linux_rel" }
      builders {
        name: "linux_gcc"
        experiment_percentage: 100
      }
      builders { name: "linux_memcheck" }
      builders { name: "linux_tsan2" }
      builders { name: "linux_asan" }
      builders { name: "linux_msan" }
      builders { name: "linux_ubsan" }
      builders { name: "linux_ubsan_vptr" }
      builders { name: "android" }
      builders { name: "android_rel" }
      builders { name: "android_clang" }
      builders { name: "android_arm64" }
      builders { name: "android_x86" }
      builders { name: "android_x64" }
      builders {
        name: "android_mips"
        experiment_percentage: 100
      }
    }
  }
}
