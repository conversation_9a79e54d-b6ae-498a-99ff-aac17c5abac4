# Copyright 2014 The LibYuv Project Authors. All rights reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS. All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("libyuv.gni")
import("//testing/test.gni")

declare_args() {
  # Set to false to disable building with gflags.
  libyuv_use_gflags = true
}

config("libyuv_config") {
  include_dirs = [ "include" ]
  if (is_android && current_cpu == "arm64") {
    ldflags = [ "-Wl,--dynamic-linker,/system/bin/linker64" ]
  }
  if (is_android && current_cpu != "arm64") {
    ldflags = [ "-Wl,--dynamic-linker,/system/bin/linker" ]
  }
}

# This target is built when no specific target is specified on the command line.
group("default") {
  testonly = true
  deps = [
    ":libyuv",
  ]
  if (libyuv_include_tests) {
    deps += [
      ":compare",
      ":cpuid",
      ":libyuv_unittest",
      ":psnr",
      ":yuvconvert",
    ]
  }
}

group("libyuv") {
  public_configs = [ ":libyuv_config" ]

  if (is_win && target_cpu == "x64") {
    # Compile with clang in order to get inline assembly
    public_deps = [
      ":libyuv_internal(//build/toolchain/win:clang_x64)",
    ]
  } else {
    public_deps = [
      ":libyuv_internal",
    ]
  }

  if (!is_ios) {
    # Make sure that clients of libyuv link with libjpeg. This can't go in
    # libyuv_internal because in Windows x64 builds that will generate a clang
    # build of libjpeg, and we don't want two copies.
    deps = [
      "//third_party:jpeg",
    ]
  }
}

static_library("libyuv_internal") {
  visibility = [ ":*" ]

  sources = [
    # Headers
    "include/libyuv.h",
    "include/libyuv/basic_types.h",
    "include/libyuv/compare.h",
    "include/libyuv/convert.h",
    "include/libyuv/convert_argb.h",
    "include/libyuv/convert_from.h",
    "include/libyuv/convert_from_argb.h",
    "include/libyuv/cpu_id.h",
    "include/libyuv/mjpeg_decoder.h",
    "include/libyuv/planar_functions.h",
    "include/libyuv/rotate.h",
    "include/libyuv/rotate_argb.h",
    "include/libyuv/rotate_row.h",
    "include/libyuv/row.h",
    "include/libyuv/scale.h",
    "include/libyuv/scale_argb.h",
    "include/libyuv/scale_row.h",
    "include/libyuv/version.h",
    "include/libyuv/video_common.h",

    # Source Files
    "source/compare.cc",
    "source/compare_common.cc",
    "source/compare_gcc.cc",
    "source/compare_win.cc",
    "source/convert.cc",
    "source/convert_argb.cc",
    "source/convert_from.cc",
    "source/convert_from_argb.cc",
    "source/convert_jpeg.cc",
    "source/convert_to_argb.cc",
    "source/convert_to_i420.cc",
    "source/cpu_id.cc",
    "source/mjpeg_decoder.cc",
    "source/mjpeg_validate.cc",
    "source/planar_functions.cc",
    "source/rotate.cc",
    "source/rotate_any.cc",
    "source/rotate_argb.cc",
    "source/rotate_common.cc",
    "source/rotate_dspr2.cc",
    "source/rotate_gcc.cc",
    "source/rotate_win.cc",
    "source/row_any.cc",
    "source/row_common.cc",
    "source/row_dspr2.cc",
    "source/row_gcc.cc",
    "source/row_win.cc",
    "source/scale.cc",
    "source/scale_any.cc",
    "source/scale_argb.cc",
    "source/scale_common.cc",
    "source/scale_dspr2.cc",
    "source/scale_gcc.cc",
    "source/scale_win.cc",
    "source/video_common.cc",
  ]

  configs += [ ":libyuv_config" ]
  defines = []
  deps = []

  if (!is_ios) {
    defines += [ "HAVE_JPEG" ]

    # Needed to pull in libjpeg headers. Can't add //third_party:jpeg to deps
    # because in Windows x64 build it will get compiled with clang.
    deps += [ "//third_party:jpeg_includes" ]
  }

  if (libyuv_use_neon) {
    deps += [ ":libyuv_neon" ]
  }

  if (libyuv_use_msa) {
    deps += [ ":libyuv_msa" ]
  }

  # Always enable optimization for Release and NaCl builds (to workaround
  # crbug.com/538243).
  if (!is_debug || is_nacl) {
    configs -= [ "//build/config/compiler:default_optimization" ]

    # Enable optimize for speed (-O2) over size (-Os).
    configs += [ "//build/config/compiler:optimize_max" ]
  }

  # To enable AVX2 or other cpu optimization, pass flag here
  # cflags = [ "-mavx2" ]
  # cflags = [ "-mpopcnt" ]
}

if (libyuv_use_neon) {
  static_library("libyuv_neon") {
    sources = [
      # ARM Source Files
      "source/compare_neon.cc",
      "source/compare_neon64.cc",
      "source/rotate_neon.cc",
      "source/rotate_neon64.cc",
      "source/row_neon.cc",
      "source/row_neon64.cc",
      "source/scale_neon.cc",
      "source/scale_neon64.cc",
    ]

    public_configs = [ ":libyuv_config" ]

    # Always enable optimization for Release and NaCl builds (to workaround
    # crbug.com/538243).
    if (!is_debug) {
      configs -= [ "//build/config/compiler:default_optimization" ]

      # Enable optimize for speed (-O2) over size (-Os).
      configs += [ "//build/config/compiler:optimize_max" ]
    }

    if (current_cpu != "arm64") {
      configs -= [ "//build/config/compiler:compiler_arm_fpu" ]
      cflags = [ "-mfpu=neon" ]
    }
  }
}

if (libyuv_use_msa) {
  static_library("libyuv_msa") {
    sources = [
      # MSA Source Files
      "source/rotate_msa.cc",
      "source/row_msa.cc",
      "source/scale_msa.cc",
    ]

    public_configs = [ ":libyuv_config" ]
  }
}

if (libyuv_include_tests) {
  config("libyuv_unittest_warnings_config") {
    if (!is_win) {
      cflags = [
        # TODO(fbarchard): Fix sign and unused variable warnings.
        "-Wno-sign-compare",
        "-Wno-unused-variable",
      ]
    }
    if (is_win) {
      cflags = [
        "/wd4245",  # signed/unsigned mismatch
        "/wd4189",  # local variable is initialized but not referenced
      ]
    }
  }
  config("libyuv_unittest_config") {
    defines = [ "GTEST_RELATIVE_PATH" ]
  }

  test("libyuv_unittest") {
    testonly = true

    sources = [
      # sources
      # headers
      "unit_test/basictypes_test.cc",
      "unit_test/color_test.cc",
      "unit_test/compare_test.cc",
      "unit_test/convert_test.cc",
      "unit_test/cpu_test.cc",
      "unit_test/cpu_thread_test.cc",
      "unit_test/math_test.cc",
      "unit_test/planar_test.cc",
      "unit_test/rotate_argb_test.cc",
      "unit_test/rotate_test.cc",
      "unit_test/scale_argb_test.cc",
      "unit_test/scale_test.cc",
      "unit_test/unit_test.cc",
      "unit_test/unit_test.h",
      "unit_test/video_common_test.cc",
    ]

    deps = [
      ":libyuv",
      "//testing/gtest",
    ]

    defines = []
    if (libyuv_use_gflags) {
      defines += [ "LIBYUV_USE_GFLAGS" ]
      deps += [ "//third_party/gflags" ]
    }

    configs += [ ":libyuv_unittest_warnings_config" ]

    public_deps = [
      "//testing/gtest",
    ]
    public_configs = [ ":libyuv_unittest_config" ]

    if (is_linux) {
      cflags = [ "-fexceptions" ]
    }
    if (is_ios) {
      configs -= [ "//build/config/compiler:default_symbols" ]
      configs += [ "//build/config/compiler:symbols" ]
      cflags = [ "-Wno-sometimes-uninitialized" ]
    }
    if (!is_ios && !libyuv_disable_jpeg) {
      defines += [ "HAVE_JPEG" ]
    }
    if (is_android) {
      deps += [ "//testing/android/native_test:native_test_native_code" ]
    }

    # TODO(YangZhang): These lines can be removed when high accuracy
    # YUV to RGB to Neon is ported.
    if ((target_cpu == "armv7" || target_cpu == "armv7s" ||
         (target_cpu == "arm" && arm_version >= 7) || target_cpu == "arm64") &&
        (arm_use_neon || arm_optionally_use_neon)) {
      defines += [ "LIBYUV_NEON" ]
    }

    defines += [
      # Enable the following 3 macros to turn off assembly for specified CPU.
      # "LIBYUV_DISABLE_X86",
      # "LIBYUV_DISABLE_NEON",
      # "LIBYUV_DISABLE_DSPR2",
      # Enable the following macro to build libyuv as a shared library (dll).
      # "LIBYUV_USING_SHARED_LIBRARY"
    ]
  }

  executable("compare") {
    sources = [
      # sources
      "util/compare.cc",
    ]
    deps = [
      ":libyuv",
      "//build/config:exe_and_shlib_deps",  # for asan on llvm libc++
    ]
    if (is_linux) {
      cflags = [ "-fexceptions" ]
    }
  }

  executable("yuvconvert") {
    sources = [
      # sources
      "util/yuvconvert.cc",
    ]
    deps = [
      ":libyuv",
      "//build/config:exe_and_shlib_deps",  # for new[] on llvm libc++
    ]
    if (is_linux) {
      cflags = [ "-fexceptions" ]
    }
  }

  executable("psnr") {
    sources = [
      # sources
      "util/psnr.cc",
      "util/psnr_main.cc",
      "util/ssim.cc",
    ]
    deps = [
      ":libyuv",
      "//build/config:exe_and_shlib_deps",  # for new[] on llvm libc++
    ]

    if (!is_ios && !libyuv_disable_jpeg) {
      defines = [ "HAVE_JPEG" ]
    }
  }

  executable("cpuid") {
    sources = [
      # sources
      "util/cpuid.c",
    ]
    deps = [
      ":libyuv",
      "//build/config:exe_and_shlib_deps",  # for asan on llvm libc++
    ]
  }
}
