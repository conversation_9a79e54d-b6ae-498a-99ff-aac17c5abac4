#############################################################################
# UBSan vptr blacklist.
# Function and type based blacklisting use a mangled name, and it is especially
# tricky to represent C++ types. For now, any possible changes by name manglings
# are simply represented as wildcard expressions of regexp, and thus it might be
# over-blacklisted.
#
# Please think twice before you add or remove these rules.
#
# This is a stripped down copy of Chromium's vptr_blacklist.txt, to enable
# adding libyuv-specific blacklist entries.

#############################################################################
# Using raw pointer values.
#
# A raw pointer value (16) is used to infer the field offset by
# GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET.

# Example:
# src:*/third_party/protobuf/src/google/protobuf/compiler/plugin.pb.cc

