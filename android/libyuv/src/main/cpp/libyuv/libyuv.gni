# Copyright 2016 The LibYuv Project Authors. All rights reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS. All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("//build_overrides/build.gni")
import("//build/config/arm.gni")
import("//build/config/mips.gni")

declare_args() {
  libyuv_include_tests = !build_with_chromium
  libyuv_disable_jpeg = false
  libyuv_use_neon = (current_cpu == "arm64" ||
      (current_cpu == "arm" && (arm_use_neon || arm_optionally_use_neon)))
  libyuv_use_msa = (current_cpu == "mips64el" || current_cpu == "mipsel") &&
    mips_use_msa
}
