# Copyright 2016 The LibYuv Project Authors. All rights reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS. All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

# Some non-Chromium builds don't use Chromium's third_party/binutils.
linux_use_bundled_binutils_override = true

# Variable that can be used to support multiple build scenarios, like having
# Chromium specific targets in a client project's GN file etc.
build_with_chromium = false

# Some non-Chromium builds don't support building java targets.
enable_java_templates = true

# Allow using custom suppressions files (currently not used by libyuv).
asan_suppressions_file = "//build/sanitizers/asan_suppressions.cc"
lsan_suppressions_file = "//build/sanitizers/lsan_suppressions.cc"
tsan_suppressions_file = "//build/sanitizers/tsan_suppressions.cc"

msan_blacklist_path =
    rebase_path("//tools_libyuv/msan/blacklist.txt", root_build_dir)
ubsan_blacklist_path =
    rebase_path("//tools_libyuv/ubsan/blacklist.txt", root_build_dir)
ubsan_vptr_blacklist_path =
    rebase_path("//tools_libyuv/ubsan/vptr_blacklist.txt", root_build_dir)

# For Chromium, Android 32-bit non-component, non-clang builds hit a 4GiB size
# limit, making them requiring symbol_level=2. WebRTC doesn't hit that problem
# so we just ignore that assert. See https://crbug.com/648948 for more info.
ignore_elf32_limitations = true

# Use system Xcode installation instead of the Chromium bundled Mac toolchain,
# since it contains only SDK 10.11, not 10.12 which WebRTC needs.
use_system_xcode = true
