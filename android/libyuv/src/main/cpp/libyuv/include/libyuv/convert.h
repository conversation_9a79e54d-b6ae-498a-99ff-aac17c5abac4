/*
 *  Copyright 2011 The LibYuv Project Authors. All rights reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS. All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

#ifndef INCLUDE_LIBYUV_CONVERT_H_
#define INCLUDE_LIBYUV_CONVERT_H_

#include "libyuv/basic_types.h"

#include "libyuv/rotate.h"  // For enum RotationMode.

// TODO(fbarchard): fix WebRTC source to include following libyuv headers:
#include "libyuv/convert_argb.h"      // For WebRTC I420ToARGB. b/620
#include "libyuv/convert_from.h"      // For WebRTC ConvertFromI420. b/620
#include "libyuv/planar_functions.h"  // For WebRTC I420Rect, CopyPlane. b/618

#ifdef __cplusplus
namespace libyuv {
extern "C" {
#endif

// Convert I444 to I420.
LIBYUV_API
int I444ToI420(const uint8* src_y,
               int src_stride_y,
               const uint8* src_u,
               int src_stride_u,
               const uint8* src_v,
               int src_stride_v,
               uint8* dst_y,
               int dst_stride_y,
               uint8* dst_u,
               int dst_stride_u,
               uint8* dst_v,
               int dst_stride_v,
               int width,
               int height);

// Convert I422 to I420.
LIBYUV_API
int I422ToI420(const uint8* src_y,
               int src_stride_y,
               const uint8* src_u,
               int src_stride_u,
               const uint8* src_v,
               int src_stride_v,
               uint8* dst_y,
               int dst_stride_y,
               uint8* dst_u,
               int dst_stride_u,
               uint8* dst_v,
               int dst_stride_v,
               int width,
               int height);

// Copy I420 to I420.
#define I420ToI420 I420Copy
LIBYUV_API
int I420Copy(const uint8* src_y,
             int src_stride_y,
             const uint8* src_u,
             int src_stride_u,
             const uint8* src_v,
             int src_stride_v,
             uint8* dst_y,
             int dst_stride_y,
             uint8* dst_u,
             int dst_stride_u,
             uint8* dst_v,
             int dst_stride_v,
             int width,
             int height);

// Convert I400 (grey) to I420.
LIBYUV_API
int I400ToI420(const uint8* src_y,
               int src_stride_y,
               uint8* dst_y,
               int dst_stride_y,
               uint8* dst_u,
               int dst_stride_u,
               uint8* dst_v,
               int dst_stride_v,
               int width,
               int height);

#define J400ToJ420 I400ToI420

// Convert NV12 to I420.
LIBYUV_API
int NV12ToI420(const uint8* src_y,
               int src_stride_y,
               const uint8* src_uv,
               int src_stride_uv,
               uint8* dst_y,
               int dst_stride_y,
               uint8* dst_u,
               int dst_stride_u,
               uint8* dst_v,
               int dst_stride_v,
               int width,
               int height);

// Convert NV21 to I420.
LIBYUV_API
int NV21ToI420(const uint8* src_y,
               int src_stride_y,
               const uint8* src_vu,
               int src_stride_vu,
               uint8* dst_y,
               int dst_stride_y,
               uint8* dst_u,
               int dst_stride_u,
               uint8* dst_v,
               int dst_stride_v,
               int width,
               int height);

// Convert YUY2 to I420.
LIBYUV_API
int YUY2ToI420(const uint8* src_yuy2,
               int src_stride_yuy2,
               uint8* dst_y,
               int dst_stride_y,
               uint8* dst_u,
               int dst_stride_u,
               uint8* dst_v,
               int dst_stride_v,
               int width,
               int height);

// Convert UYVY to I420.
LIBYUV_API
int UYVYToI420(const uint8* src_uyvy,
               int src_stride_uyvy,
               uint8* dst_y,
               int dst_stride_y,
               uint8* dst_u,
               int dst_stride_u,
               uint8* dst_v,
               int dst_stride_v,
               int width,
               int height);

// Convert M420 to I420.
LIBYUV_API
int M420ToI420(const uint8* src_m420,
               int src_stride_m420,
               uint8* dst_y,
               int dst_stride_y,
               uint8* dst_u,
               int dst_stride_u,
               uint8* dst_v,
               int dst_stride_v,
               int width,
               int height);

// Convert Android420 to I420.
LIBYUV_API
int Android420ToI420(const uint8* src_y,
                     int src_stride_y,
                     const uint8* src_u,
                     int src_stride_u,
                     const uint8* src_v,
                     int src_stride_v,
                     int pixel_stride_uv,
                     uint8* dst_y,
                     int dst_stride_y,
                     uint8* dst_u,
                     int dst_stride_u,
                     uint8* dst_v,
                     int dst_stride_v,
                     int width,
                     int height);

// ARGB little endian (bgra in memory) to I420.
LIBYUV_API
int ARGBToI420(const uint8* src_frame,
               int src_stride_frame,
               uint8* dst_y,
               int dst_stride_y,
               uint8* dst_u,
               int dst_stride_u,
               uint8* dst_v,
               int dst_stride_v,
               int width,
               int height);

// BGRA little endian (argb in memory) to I420.
LIBYUV_API
int BGRAToI420(const uint8* src_frame,
               int src_stride_frame,
               uint8* dst_y,
               int dst_stride_y,
               uint8* dst_u,
               int dst_stride_u,
               uint8* dst_v,
               int dst_stride_v,
               int width,
               int height);

// ABGR little endian (rgba in memory) to I420.
LIBYUV_API
int ABGRToI420(const uint8* src_frame,
               int src_stride_frame,
               uint8* dst_y,
               int dst_stride_y,
               uint8* dst_u,
               int dst_stride_u,
               uint8* dst_v,
               int dst_stride_v,
               int width,
               int height);

// RGBA little endian (abgr in memory) to I420.
LIBYUV_API
int RGBAToI420(const uint8* src_frame,
               int src_stride_frame,
               uint8* dst_y,
               int dst_stride_y,
               uint8* dst_u,
               int dst_stride_u,
               uint8* dst_v,
               int dst_stride_v,
               int width,
               int height);

// RGB little endian (bgr in memory) to I420.
LIBYUV_API
int RGB24ToI420(const uint8* src_frame,
                int src_stride_frame,
                uint8* dst_y,
                int dst_stride_y,
                uint8* dst_u,
                int dst_stride_u,
                uint8* dst_v,
                int dst_stride_v,
                int width,
                int height);

// RGB big endian (rgb in memory) to I420.
LIBYUV_API
int RAWToI420(const uint8* src_frame,
              int src_stride_frame,
              uint8* dst_y,
              int dst_stride_y,
              uint8* dst_u,
              int dst_stride_u,
              uint8* dst_v,
              int dst_stride_v,
              int width,
              int height);

// RGB16 (RGBP fourcc) little endian to I420.
LIBYUV_API
int RGB565ToI420(const uint8* src_frame,
                 int src_stride_frame,
                 uint8* dst_y,
                 int dst_stride_y,
                 uint8* dst_u,
                 int dst_stride_u,
                 uint8* dst_v,
                 int dst_stride_v,
                 int width,
                 int height);

// RGB15 (RGBO fourcc) little endian to I420.
LIBYUV_API
int ARGB1555ToI420(const uint8* src_frame,
                   int src_stride_frame,
                   uint8* dst_y,
                   int dst_stride_y,
                   uint8* dst_u,
                   int dst_stride_u,
                   uint8* dst_v,
                   int dst_stride_v,
                   int width,
                   int height);

// RGB12 (R444 fourcc) little endian to I420.
LIBYUV_API
int ARGB4444ToI420(const uint8* src_frame,
                   int src_stride_frame,
                   uint8* dst_y,
                   int dst_stride_y,
                   uint8* dst_u,
                   int dst_stride_u,
                   uint8* dst_v,
                   int dst_stride_v,
                   int width,
                   int height);

#ifdef HAVE_JPEG
// src_width/height provided by capture.
// dst_width/height for clipping determine final size.
LIBYUV_API
int MJPGToI420(const uint8* sample,
               size_t sample_size,
               uint8* dst_y,
               int dst_stride_y,
               uint8* dst_u,
               int dst_stride_u,
               uint8* dst_v,
               int dst_stride_v,
               int src_width,
               int src_height,
               int dst_width,
               int dst_height);

// Query size of MJPG in pixels.
LIBYUV_API
int MJPGSize(const uint8* sample, size_t sample_size, int* width, int* height);
#endif

// Convert camera sample to I420 with cropping, rotation and vertical flip.
// "src_size" is needed to parse MJPG.
// "dst_stride_y" number of bytes in a row of the dst_y plane.
//   Normally this would be the same as dst_width, with recommended alignment
//   to 16 bytes for better efficiency.
//   If rotation of 90 or 270 is used, stride is affected. The caller should
//   allocate the I420 buffer according to rotation.
// "dst_stride_u" number of bytes in a row of the dst_u plane.
//   Normally this would be the same as (dst_width + 1) / 2, with
//   recommended alignment to 16 bytes for better efficiency.
//   If rotation of 90 or 270 is used, stride is affected.
// "crop_x" and "crop_y" are starting position for cropping.
//   To center, crop_x = (src_width - dst_width) / 2
//              crop_y = (src_height - dst_height) / 2
// "src_width" / "src_height" is size of src_frame in pixels.
//   "src_height" can be negative indicating a vertically flipped image source.
// "crop_width" / "crop_height" is the size to crop the src to.
//    Must be less than or equal to src_width/src_height
//    Cropping parameters are pre-rotation.
// "rotation" can be 0, 90, 180 or 270.
// "format" is a fourcc. ie 'I420', 'YUY2'
// Returns 0 for successful; -1 for invalid parameter. Non-zero for failure.
LIBYUV_API
int ConvertToI420(const uint8* src_frame,
                  size_t src_size,
                  uint8* dst_y,
                  int dst_stride_y,
                  uint8* dst_u,
                  int dst_stride_u,
                  uint8* dst_v,
                  int dst_stride_v,
                  int crop_x,
                  int crop_y,
                  int src_width,
                  int src_height,
                  int crop_width,
                  int crop_height,
                  enum RotationMode rotation,
                  uint32 format);

#ifdef __cplusplus
}  // extern "C"
}  // namespace libyuv
#endif

#endif  // INCLUDE_LIBYUV_CONVERT_H_
