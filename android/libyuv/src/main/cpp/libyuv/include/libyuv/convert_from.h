/*
 *  Copyright 2011 The LibYuv Project Authors. All rights reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS. All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

#ifndef INCLUDE_LIBYUV_CONVERT_FROM_H_
#define INCLUDE_LIBYUV_CONVERT_FROM_H_

#include "libyuv/basic_types.h"
#include "libyuv/rotate.h"

#ifdef __cplusplus
namespace libyuv {
extern "C" {
#endif

// See Also convert.h for conversions from formats to I420.

// I420Copy in convert to I420ToI420.

LIBYUV_API
int I420ToI422(const uint8* src_y,
               int src_stride_y,
               const uint8* src_u,
               int src_stride_u,
               const uint8* src_v,
               int src_stride_v,
               uint8* dst_y,
               int dst_stride_y,
               uint8* dst_u,
               int dst_stride_u,
               uint8* dst_v,
               int dst_stride_v,
               int width,
               int height);

LIBYUV_API
int I420ToI444(const uint8* src_y,
               int src_stride_y,
               const uint8* src_u,
               int src_stride_u,
               const uint8* src_v,
               int src_stride_v,
               uint8* dst_y,
               int dst_stride_y,
               uint8* dst_u,
               int dst_stride_u,
               uint8* dst_v,
               int dst_stride_v,
               int width,
               int height);

// Copy to I400. Source can be I420, I422, I444, I400, NV12 or NV21.
LIBYUV_API
int I400Copy(const uint8* src_y,
             int src_stride_y,
             uint8* dst_y,
             int dst_stride_y,
             int width,
             int height);

LIBYUV_API
int I420ToNV12(const uint8* src_y,
               int src_stride_y,
               const uint8* src_u,
               int src_stride_u,
               const uint8* src_v,
               int src_stride_v,
               uint8* dst_y,
               int dst_stride_y,
               uint8* dst_uv,
               int dst_stride_uv,
               int width,
               int height);

LIBYUV_API
int I420ToNV21(const uint8* src_y,
               int src_stride_y,
               const uint8* src_u,
               int src_stride_u,
               const uint8* src_v,
               int src_stride_v,
               uint8* dst_y,
               int dst_stride_y,
               uint8* dst_vu,
               int dst_stride_vu,
               int width,
               int height);

LIBYUV_API
int I420ToYUY2(const uint8* src_y,
               int src_stride_y,
               const uint8* src_u,
               int src_stride_u,
               const uint8* src_v,
               int src_stride_v,
               uint8* dst_frame,
               int dst_stride_frame,
               int width,
               int height);

LIBYUV_API
int I420ToUYVY(const uint8* src_y,
               int src_stride_y,
               const uint8* src_u,
               int src_stride_u,
               const uint8* src_v,
               int src_stride_v,
               uint8* dst_frame,
               int dst_stride_frame,
               int width,
               int height);

LIBYUV_API
int I420ToARGB(const uint8* src_y,
               int src_stride_y,
               const uint8* src_u,
               int src_stride_u,
               const uint8* src_v,
               int src_stride_v,
               uint8* dst_argb,
               int dst_stride_argb,
               int width,
               int height);

LIBYUV_API
int I420ToBGRA(const uint8* src_y,
               int src_stride_y,
               const uint8* src_u,
               int src_stride_u,
               const uint8* src_v,
               int src_stride_v,
               uint8* dst_argb,
               int dst_stride_argb,
               int width,
               int height);

LIBYUV_API
int I420ToABGR(const uint8* src_y,
               int src_stride_y,
               const uint8* src_u,
               int src_stride_u,
               const uint8* src_v,
               int src_stride_v,
               uint8* dst_argb,
               int dst_stride_argb,
               int width,
               int height);

LIBYUV_API
int I420ToRGBA(const uint8* src_y,
               int src_stride_y,
               const uint8* src_u,
               int src_stride_u,
               const uint8* src_v,
               int src_stride_v,
               uint8* dst_rgba,
               int dst_stride_rgba,
               int width,
               int height);

LIBYUV_API
int I420ToRGB24(const uint8* src_y,
                int src_stride_y,
                const uint8* src_u,
                int src_stride_u,
                const uint8* src_v,
                int src_stride_v,
                uint8* dst_frame,
                int dst_stride_frame,
                int width,
                int height);

LIBYUV_API
int I420ToRAW(const uint8* src_y,
              int src_stride_y,
              const uint8* src_u,
              int src_stride_u,
              const uint8* src_v,
              int src_stride_v,
              uint8* dst_frame,
              int dst_stride_frame,
              int width,
              int height);

LIBYUV_API
int I420ToRGB565(const uint8* src_y,
                 int src_stride_y,
                 const uint8* src_u,
                 int src_stride_u,
                 const uint8* src_v,
                 int src_stride_v,
                 uint8* dst_frame,
                 int dst_stride_frame,
                 int width,
                 int height);

LIBYUV_API
int I422ToRGB565(const uint8* src_y,
                 int src_stride_y,
                 const uint8* src_u,
                 int src_stride_u,
                 const uint8* src_v,
                 int src_stride_v,
                 uint8* dst_frame,
                 int dst_stride_frame,
                 int width,
                 int height);

// Convert I420 To RGB565 with 4x4 dither matrix (16 bytes).
// Values in dither matrix from 0 to 7 recommended.
// The order of the dither matrix is first byte is upper left.

LIBYUV_API
int I420ToRGB565Dither(const uint8* src_y,
                       int src_stride_y,
                       const uint8* src_u,
                       int src_stride_u,
                       const uint8* src_v,
                       int src_stride_v,
                       uint8* dst_frame,
                       int dst_stride_frame,
                       const uint8* dither4x4,
                       int width,
                       int height);

LIBYUV_API
int I420ToARGB1555(const uint8* src_y,
                   int src_stride_y,
                   const uint8* src_u,
                   int src_stride_u,
                   const uint8* src_v,
                   int src_stride_v,
                   uint8* dst_frame,
                   int dst_stride_frame,
                   int width,
                   int height);

LIBYUV_API
int I420ToARGB4444(const uint8* src_y,
                   int src_stride_y,
                   const uint8* src_u,
                   int src_stride_u,
                   const uint8* src_v,
                   int src_stride_v,
                   uint8* dst_frame,
                   int dst_stride_frame,
                   int width,
                   int height);

// Convert I420 to specified format.
// "dst_sample_stride" is bytes in a row for the destination. Pass 0 if the
//    buffer has contiguous rows. Can be negative. A multiple of 16 is optimal.
LIBYUV_API
int ConvertFromI420(const uint8* y,
                    int y_stride,
                    const uint8* u,
                    int u_stride,
                    const uint8* v,
                    int v_stride,
                    uint8* dst_sample,
                    int dst_sample_stride,
                    int width,
                    int height,
                    uint32 format);

#ifdef __cplusplus
}  // extern "C"
}  // namespace libyuv
#endif

#endif  // INCLUDE_LIBYUV_CONVERT_FROM_H_
