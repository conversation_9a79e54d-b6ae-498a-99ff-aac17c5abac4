<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_292) on Sat Dec 25 08:13:28 UTC 2021 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>BackgroundSubtractorMOG2 (OpenCV 4.5.5 Java documentation)</title>
<meta name="date" content="2021-12-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BackgroundSubtractorMOG2 (OpenCV 4.5.5 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/video/BackgroundSubtractorKNN.html" title="class in org.opencv.video"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/video/DenseOpticalFlow.html" title="class in org.opencv.video"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/video/BackgroundSubtractorMOG2.html" target="_top">Frames</a></li>
<li><a href="BackgroundSubtractorMOG2.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.video</div>
<h2 title="Class BackgroundSubtractorMOG2" class="title">Class BackgroundSubtractorMOG2</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/video/BackgroundSubtractor.html" title="class in org.opencv.video">org.opencv.video.BackgroundSubtractor</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.video.BackgroundSubtractorMOG2</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">BackgroundSubtractorMOG2</span>
extends <a href="../../../org/opencv/video/BackgroundSubtractor.html" title="class in org.opencv.video">BackgroundSubtractor</a></pre>
<div class="block">Gaussian Mixture-based Background/Foreground Segmentation Algorithm.

 The class implements the Gaussian mixture model background subtraction described in CITE: Zivkovic2004
 and CITE: Zivkovic2006 .</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html" title="class in org.opencv.video">BackgroundSubtractorMOG2</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#apply-org.opencv.core.Mat-org.opencv.core.Mat-">apply</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;fgmask)</code>
<div class="block">Computes a foreground mask.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#apply-org.opencv.core.Mat-org.opencv.core.Mat-double-">apply</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;fgmask,
     double&nbsp;learningRate)</code>
<div class="block">Computes a foreground mask.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#getBackgroundRatio--">getBackgroundRatio</a></span>()</code>
<div class="block">Returns the "background ratio" parameter of the algorithm

     If a foreground pixel keeps semi-constant value for about backgroundRatio\*history frames, it's
     considered background and added to the model as a center of a new component.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#getComplexityReductionThreshold--">getComplexityReductionThreshold</a></span>()</code>
<div class="block">Returns the complexity reduction threshold

     This parameter defines the number of samples needed to accept to prove the component exists.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#getDetectShadows--">getDetectShadows</a></span>()</code>
<div class="block">Returns the shadow detection flag

     If true, the algorithm detects shadows and marks them.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#getHistory--">getHistory</a></span>()</code>
<div class="block">Returns the number of last frames that affect the background model</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#getNMixtures--">getNMixtures</a></span>()</code>
<div class="block">Returns the number of gaussian components in the background model</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#getShadowThreshold--">getShadowThreshold</a></span>()</code>
<div class="block">Returns the shadow threshold

     A shadow is detected if pixel is a darker version of the background.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#getShadowValue--">getShadowValue</a></span>()</code>
<div class="block">Returns the shadow value

     Shadow value is the value used to mark shadows in the foreground mask.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#getVarInit--">getVarInit</a></span>()</code>
<div class="block">Returns the initial variance of each gaussian component</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#getVarMax--">getVarMax</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#getVarMin--">getVarMin</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#getVarThreshold--">getVarThreshold</a></span>()</code>
<div class="block">Returns the variance threshold for the pixel-model match

     The main threshold on the squared Mahalanobis distance to decide if the sample is well described by
     the background model or not.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#getVarThresholdGen--">getVarThresholdGen</a></span>()</code>
<div class="block">Returns the variance threshold for the pixel-model match used for new mixture component generation

     Threshold for the squared Mahalanobis distance that helps decide when a sample is close to the
     existing components (corresponds to Tg in the paper).</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#setBackgroundRatio-double-">setBackgroundRatio</a></span>(double&nbsp;ratio)</code>
<div class="block">Sets the "background ratio" parameter of the algorithm</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#setComplexityReductionThreshold-double-">setComplexityReductionThreshold</a></span>(double&nbsp;ct)</code>
<div class="block">Sets the complexity reduction threshold</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#setDetectShadows-boolean-">setDetectShadows</a></span>(boolean&nbsp;detectShadows)</code>
<div class="block">Enables or disables shadow detection</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#setHistory-int-">setHistory</a></span>(int&nbsp;history)</code>
<div class="block">Sets the number of last frames that affect the background model</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#setNMixtures-int-">setNMixtures</a></span>(int&nbsp;nmixtures)</code>
<div class="block">Sets the number of gaussian components in the background model.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#setShadowThreshold-double-">setShadowThreshold</a></span>(double&nbsp;threshold)</code>
<div class="block">Sets the shadow threshold</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#setShadowValue-int-">setShadowValue</a></span>(int&nbsp;value)</code>
<div class="block">Sets the shadow value</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#setVarInit-double-">setVarInit</a></span>(double&nbsp;varInit)</code>
<div class="block">Sets the initial variance of each gaussian component</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#setVarMax-double-">setVarMax</a></span>(double&nbsp;varMax)</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#setVarMin-double-">setVarMin</a></span>(double&nbsp;varMin)</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#setVarThreshold-double-">setVarThreshold</a></span>(double&nbsp;varThreshold)</code>
<div class="block">Sets the variance threshold for the pixel-model match</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#setVarThresholdGen-double-">setVarThresholdGen</a></span>(double&nbsp;varThresholdGen)</code>
<div class="block">Sets the variance threshold for the pixel-model match used for new mixture component generation</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.video.BackgroundSubtractor">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.video.<a href="../../../org/opencv/video/BackgroundSubtractor.html" title="class in org.opencv.video">BackgroundSubtractor</a></h3>
<code><a href="../../../org/opencv/video/BackgroundSubtractor.html#getBackgroundImage-org.opencv.core.Mat-">getBackgroundImage</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#empty--">empty</a>, <a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">getDefaultName</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html" title="class in org.opencv.video">BackgroundSubtractorMOG2</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="apply-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>apply</h4>
<pre>public&nbsp;void&nbsp;apply(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;fgmask)</pre>
<div class="block">Computes a foreground mask.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../org/opencv/video/BackgroundSubtractor.html#apply-org.opencv.core.Mat-org.opencv.core.Mat-">apply</a></code>&nbsp;in class&nbsp;<code><a href="../../../org/opencv/video/BackgroundSubtractor.html" title="class in org.opencv.video">BackgroundSubtractor</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - Next video frame. Floating point frame will be used without scaling and should be in range \([0,255]\).</dd>
<dd><code>fgmask</code> - The output foreground mask as an 8-bit binary image.
     learnt. Negative parameter value makes the algorithm to use some automatically chosen learning
     rate. 0 means that the background model is not updated at all, 1 means that the background model
     is completely reinitialized from the last frame.</dd>
</dl>
</li>
</ul>
<a name="apply-org.opencv.core.Mat-org.opencv.core.Mat-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>apply</h4>
<pre>public&nbsp;void&nbsp;apply(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;fgmask,
                  double&nbsp;learningRate)</pre>
<div class="block">Computes a foreground mask.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../org/opencv/video/BackgroundSubtractor.html#apply-org.opencv.core.Mat-org.opencv.core.Mat-double-">apply</a></code>&nbsp;in class&nbsp;<code><a href="../../../org/opencv/video/BackgroundSubtractor.html" title="class in org.opencv.video">BackgroundSubtractor</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - Next video frame. Floating point frame will be used without scaling and should be in range \([0,255]\).</dd>
<dd><code>fgmask</code> - The output foreground mask as an 8-bit binary image.</dd>
<dd><code>learningRate</code> - The value between 0 and 1 that indicates how fast the background model is
     learnt. Negative parameter value makes the algorithm to use some automatically chosen learning
     rate. 0 means that the background model is not updated at all, 1 means that the background model
     is completely reinitialized from the last frame.</dd>
</dl>
</li>
</ul>
<a name="getBackgroundRatio--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBackgroundRatio</h4>
<pre>public&nbsp;double&nbsp;getBackgroundRatio()</pre>
<div class="block">Returns the "background ratio" parameter of the algorithm

     If a foreground pixel keeps semi-constant value for about backgroundRatio\*history frames, it's
     considered background and added to the model as a center of a new component. It corresponds to TB
     parameter in the paper.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getComplexityReductionThreshold--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getComplexityReductionThreshold</h4>
<pre>public&nbsp;double&nbsp;getComplexityReductionThreshold()</pre>
<div class="block">Returns the complexity reduction threshold

     This parameter defines the number of samples needed to accept to prove the component exists. CT=0.05
     is a default value for all the samples. By setting CT=0 you get an algorithm very similar to the
     standard Stauffer&amp;Grimson algorithm.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getDetectShadows--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDetectShadows</h4>
<pre>public&nbsp;boolean&nbsp;getDetectShadows()</pre>
<div class="block">Returns the shadow detection flag

     If true, the algorithm detects shadows and marks them. See createBackgroundSubtractorMOG2 for
     details.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getHistory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHistory</h4>
<pre>public&nbsp;int&nbsp;getHistory()</pre>
<div class="block">Returns the number of last frames that affect the background model</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getNMixtures--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNMixtures</h4>
<pre>public&nbsp;int&nbsp;getNMixtures()</pre>
<div class="block">Returns the number of gaussian components in the background model</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getShadowThreshold--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShadowThreshold</h4>
<pre>public&nbsp;double&nbsp;getShadowThreshold()</pre>
<div class="block">Returns the shadow threshold

     A shadow is detected if pixel is a darker version of the background. The shadow threshold (Tau in
     the paper) is a threshold defining how much darker the shadow can be. Tau= 0.5 means that if a pixel
     is more than twice darker then it is not shadow. See Prati, Mikic, Trivedi and Cucchiara,
 Detecting Moving Shadows...*, IEEE PAMI,2003.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getShadowValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShadowValue</h4>
<pre>public&nbsp;int&nbsp;getShadowValue()</pre>
<div class="block">Returns the shadow value

     Shadow value is the value used to mark shadows in the foreground mask. Default value is 127. Value 0
     in the mask always means background, 255 means foreground.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getVarInit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVarInit</h4>
<pre>public&nbsp;double&nbsp;getVarInit()</pre>
<div class="block">Returns the initial variance of each gaussian component</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getVarMax--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVarMax</h4>
<pre>public&nbsp;double&nbsp;getVarMax()</pre>
</li>
</ul>
<a name="getVarMin--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVarMin</h4>
<pre>public&nbsp;double&nbsp;getVarMin()</pre>
</li>
</ul>
<a name="getVarThreshold--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVarThreshold</h4>
<pre>public&nbsp;double&nbsp;getVarThreshold()</pre>
<div class="block">Returns the variance threshold for the pixel-model match

     The main threshold on the squared Mahalanobis distance to decide if the sample is well described by
     the background model or not. Related to Cthr from the paper.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getVarThresholdGen--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVarThresholdGen</h4>
<pre>public&nbsp;double&nbsp;getVarThresholdGen()</pre>
<div class="block">Returns the variance threshold for the pixel-model match used for new mixture component generation

     Threshold for the squared Mahalanobis distance that helps decide when a sample is close to the
     existing components (corresponds to Tg in the paper). If a pixel is not close to any component, it
     is considered foreground or added as a new component. 3 sigma =&gt; Tg=3\*3=9 is default. A smaller Tg
     value generates more components. A higher Tg value may result in a small number of components but
     they can grow too large.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setBackgroundRatio-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBackgroundRatio</h4>
<pre>public&nbsp;void&nbsp;setBackgroundRatio(double&nbsp;ratio)</pre>
<div class="block">Sets the "background ratio" parameter of the algorithm</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>ratio</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setComplexityReductionThreshold-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setComplexityReductionThreshold</h4>
<pre>public&nbsp;void&nbsp;setComplexityReductionThreshold(double&nbsp;ct)</pre>
<div class="block">Sets the complexity reduction threshold</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>ct</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setDetectShadows-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDetectShadows</h4>
<pre>public&nbsp;void&nbsp;setDetectShadows(boolean&nbsp;detectShadows)</pre>
<div class="block">Enables or disables shadow detection</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>detectShadows</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setHistory-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHistory</h4>
<pre>public&nbsp;void&nbsp;setHistory(int&nbsp;history)</pre>
<div class="block">Sets the number of last frames that affect the background model</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>history</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setNMixtures-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNMixtures</h4>
<pre>public&nbsp;void&nbsp;setNMixtures(int&nbsp;nmixtures)</pre>
<div class="block">Sets the number of gaussian components in the background model.

     The model needs to be reinitalized to reserve memory.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nmixtures</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setShadowThreshold-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShadowThreshold</h4>
<pre>public&nbsp;void&nbsp;setShadowThreshold(double&nbsp;threshold)</pre>
<div class="block">Sets the shadow threshold</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>threshold</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setShadowValue-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShadowValue</h4>
<pre>public&nbsp;void&nbsp;setShadowValue(int&nbsp;value)</pre>
<div class="block">Sets the shadow value</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setVarInit-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVarInit</h4>
<pre>public&nbsp;void&nbsp;setVarInit(double&nbsp;varInit)</pre>
<div class="block">Sets the initial variance of each gaussian component</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>varInit</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setVarMax-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVarMax</h4>
<pre>public&nbsp;void&nbsp;setVarMax(double&nbsp;varMax)</pre>
</li>
</ul>
<a name="setVarMin-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVarMin</h4>
<pre>public&nbsp;void&nbsp;setVarMin(double&nbsp;varMin)</pre>
</li>
</ul>
<a name="setVarThreshold-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVarThreshold</h4>
<pre>public&nbsp;void&nbsp;setVarThreshold(double&nbsp;varThreshold)</pre>
<div class="block">Sets the variance threshold for the pixel-model match</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>varThreshold</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setVarThresholdGen-double-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setVarThresholdGen</h4>
<pre>public&nbsp;void&nbsp;setVarThresholdGen(double&nbsp;varThresholdGen)</pre>
<div class="block">Sets the variance threshold for the pixel-model match used for new mixture component generation</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>varThresholdGen</code> - automatically generated</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/video/BackgroundSubtractorKNN.html" title="class in org.opencv.video"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/video/DenseOpticalFlow.html" title="class in org.opencv.video"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/video/BackgroundSubtractorMOG2.html" target="_top">Frames</a></li>
<li><a href="BackgroundSubtractorMOG2.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2021-12-25 08:13:27 / OpenCV 4.5.5</small></p>
</body>
</html>
