<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_292) on Sat Dec 25 08:13:28 UTC 2021 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RTrees (OpenCV 4.5.5 Java documentation)</title>
<meta name="date" content="2021-12-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RTrees (OpenCV 4.5.5 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":9,"i9":9,"i10":10,"i11":10,"i12":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/ml/RTrees.html" target="_top">Frames</a></li>
<li><a href="RTrees.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.org.opencv.ml.DTrees">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.ml</div>
<h2 title="Class RTrees" class="title">Class RTrees</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">org.opencv.ml.StatModel</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/ml/DTrees.html" title="class in org.opencv.ml">org.opencv.ml.DTrees</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.ml.RTrees</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">RTrees</span>
extends <a href="../../../org/opencv/ml/DTrees.html" title="class in org.opencv.ml">DTrees</a></pre>
<div class="block">The class implements the random forest predictor.

 SEE: REF: ml_intro_rtrees</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.opencv.ml.DTrees">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.opencv.ml.<a href="../../../org/opencv/ml/DTrees.html" title="class in org.opencv.ml">DTrees</a></h3>
<code><a href="../../../org/opencv/ml/DTrees.html#PREDICT_AUTO">PREDICT_AUTO</a>, <a href="../../../org/opencv/ml/DTrees.html#PREDICT_MASK">PREDICT_MASK</a>, <a href="../../../org/opencv/ml/DTrees.html#PREDICT_MAX_VOTE">PREDICT_MAX_VOTE</a>, <a href="../../../org/opencv/ml/DTrees.html#PREDICT_SUM">PREDICT_SUM</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.opencv.ml.StatModel">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.opencv.ml.<a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="../../../org/opencv/ml/StatModel.html#COMPRESSED_INPUT">COMPRESSED_INPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#PREPROCESSED_INPUT">PREPROCESSED_INPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#RAW_OUTPUT">RAW_OUTPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#UPDATE_MODEL">UPDATE_MODEL</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/RTrees.html" title="class in org.opencv.ml">RTrees</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/RTrees.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/RTrees.html" title="class in org.opencv.ml">RTrees</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/RTrees.html#create--">create</a></span>()</code>
<div class="block">Creates the empty model.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/RTrees.html#getActiveVarCount--">getActiveVarCount</a></span>()</code>
<div class="block">SEE: setActiveVarCount</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/RTrees.html#getCalculateVarImportance--">getCalculateVarImportance</a></span>()</code>
<div class="block">SEE: setCalculateVarImportance</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/RTrees.html#getOOBError--">getOOBError</a></span>()</code>
<div class="block">Returns the OOB error value, computed at the training stage when calcOOBError is set to true.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/RTrees.html#getTermCriteria--">getTermCriteria</a></span>()</code>
<div class="block">SEE: setTermCriteria</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/RTrees.html#getVarImportance--">getVarImportance</a></span>()</code>
<div class="block">Returns the variable importance array.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/RTrees.html#getVotes-org.opencv.core.Mat-org.opencv.core.Mat-int-">getVotes</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results,
        int&nbsp;flags)</code>
<div class="block">Returns the result of each individual tree in the forest.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/RTrees.html" title="class in org.opencv.ml">RTrees</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/RTrees.html#load-java.lang.String-">load</a></span>(java.lang.String&nbsp;filepath)</code>
<div class="block">Loads and creates a serialized RTree from a file

 Use RTree::save to serialize and store an RTree to disk.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/RTrees.html" title="class in org.opencv.ml">RTrees</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/RTrees.html#load-java.lang.String-java.lang.String-">load</a></span>(java.lang.String&nbsp;filepath,
    java.lang.String&nbsp;nodeName)</code>
<div class="block">Loads and creates a serialized RTree from a file

 Use RTree::save to serialize and store an RTree to disk.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/RTrees.html#setActiveVarCount-int-">setActiveVarCount</a></span>(int&nbsp;val)</code>
<div class="block">getActiveVarCount SEE: getActiveVarCount</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/RTrees.html#setCalculateVarImportance-boolean-">setCalculateVarImportance</a></span>(boolean&nbsp;val)</code>
<div class="block">getCalculateVarImportance SEE: getCalculateVarImportance</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/RTrees.html#setTermCriteria-org.opencv.core.TermCriteria-">setTermCriteria</a></span>(<a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;val)</code>
<div class="block">getTermCriteria SEE: getTermCriteria</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.ml.DTrees">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.ml.<a href="../../../org/opencv/ml/DTrees.html" title="class in org.opencv.ml">DTrees</a></h3>
<code><a href="../../../org/opencv/ml/DTrees.html#getCVFolds--">getCVFolds</a>, <a href="../../../org/opencv/ml/DTrees.html#getMaxCategories--">getMaxCategories</a>, <a href="../../../org/opencv/ml/DTrees.html#getMaxDepth--">getMaxDepth</a>, <a href="../../../org/opencv/ml/DTrees.html#getMinSampleCount--">getMinSampleCount</a>, <a href="../../../org/opencv/ml/DTrees.html#getPriors--">getPriors</a>, <a href="../../../org/opencv/ml/DTrees.html#getRegressionAccuracy--">getRegressionAccuracy</a>, <a href="../../../org/opencv/ml/DTrees.html#getTruncatePrunedTree--">getTruncatePrunedTree</a>, <a href="../../../org/opencv/ml/DTrees.html#getUse1SERule--">getUse1SERule</a>, <a href="../../../org/opencv/ml/DTrees.html#getUseSurrogates--">getUseSurrogates</a>, <a href="../../../org/opencv/ml/DTrees.html#setCVFolds-int-">setCVFolds</a>, <a href="../../../org/opencv/ml/DTrees.html#setMaxCategories-int-">setMaxCategories</a>, <a href="../../../org/opencv/ml/DTrees.html#setMaxDepth-int-">setMaxDepth</a>, <a href="../../../org/opencv/ml/DTrees.html#setMinSampleCount-int-">setMinSampleCount</a>, <a href="../../../org/opencv/ml/DTrees.html#setPriors-org.opencv.core.Mat-">setPriors</a>, <a href="../../../org/opencv/ml/DTrees.html#setRegressionAccuracy-float-">setRegressionAccuracy</a>, <a href="../../../org/opencv/ml/DTrees.html#setTruncatePrunedTree-boolean-">setTruncatePrunedTree</a>, <a href="../../../org/opencv/ml/DTrees.html#setUse1SERule-boolean-">setUse1SERule</a>, <a href="../../../org/opencv/ml/DTrees.html#setUseSurrogates-boolean-">setUseSurrogates</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.ml.StatModel">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.ml.<a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="../../../org/opencv/ml/StatModel.html#calcError-org.opencv.ml.TrainData-boolean-org.opencv.core.Mat-">calcError</a>, <a href="../../../org/opencv/ml/StatModel.html#empty--">empty</a>, <a href="../../../org/opencv/ml/StatModel.html#getVarCount--">getVarCount</a>, <a href="../../../org/opencv/ml/StatModel.html#isClassifier--">isClassifier</a>, <a href="../../../org/opencv/ml/StatModel.html#isTrained--">isTrained</a>, <a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-">predict</a>, <a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-org.opencv.core.Mat-">predict</a>, <a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-org.opencv.core.Mat-int-">predict</a>, <a href="../../../org/opencv/ml/StatModel.html#train-org.opencv.core.Mat-int-org.opencv.core.Mat-">train</a>, <a href="../../../org/opencv/ml/StatModel.html#train-org.opencv.ml.TrainData-">train</a>, <a href="../../../org/opencv/ml/StatModel.html#train-org.opencv.ml.TrainData-int-">train</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">getDefaultName</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/RTrees.html" title="class in org.opencv.ml">RTrees</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="create--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/RTrees.html" title="class in org.opencv.ml">RTrees</a>&nbsp;create()</pre>
<div class="block">Creates the empty model.
     Use StatModel::train to train the model, StatModel::train to create and train the model,
     Algorithm::load to load the pre-trained model.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getActiveVarCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActiveVarCount</h4>
<pre>public&nbsp;int&nbsp;getActiveVarCount()</pre>
<div class="block">SEE: setActiveVarCount</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getCalculateVarImportance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalculateVarImportance</h4>
<pre>public&nbsp;boolean&nbsp;getCalculateVarImportance()</pre>
<div class="block">SEE: setCalculateVarImportance</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getOOBError--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOOBError</h4>
<pre>public&nbsp;double&nbsp;getOOBError()</pre>
<div class="block">Returns the OOB error value, computed at the training stage when calcOOBError is set to true.
 If this flag was set to false, 0 is returned. The OOB error is also scaled by sample weighting.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getTermCriteria--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTermCriteria</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;getTermCriteria()</pre>
<div class="block">SEE: setTermCriteria</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getVarImportance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVarImportance</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getVarImportance()</pre>
<div class="block">Returns the variable importance array.
     The method returns the variable importance vector, computed at the training stage when
     CalculateVarImportance is set to true. If this flag was set to false, the empty matrix is
     returned.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getVotes-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVotes</h4>
<pre>public&nbsp;void&nbsp;getVotes(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results,
                     int&nbsp;flags)</pre>
<div class="block">Returns the result of each individual tree in the forest.
     In case the model is a regression problem, the method will return each of the trees'
     results for each of the sample cases. If the model is a classifier, it will return
     a Mat with samples + 1 rows, where the first row gives the class number and the
     following rows return the votes each class had for each sample.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - Array containing the samples for which votes will be calculated.</dd>
<dd><code>results</code> - Array where the result of the calculation will be written.</dd>
<dd><code>flags</code> - Flags for defining the type of RTrees.</dd>
</dl>
</li>
</ul>
<a name="load-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>load</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/RTrees.html" title="class in org.opencv.ml">RTrees</a>&nbsp;load(java.lang.String&nbsp;filepath)</pre>
<div class="block">Loads and creates a serialized RTree from a file

 Use RTree::save to serialize and store an RTree to disk.
 Load the RTree from this file again, by calling this function with the path to the file.
 Optionally specify the node for the file containing the classifier</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filepath</code> - path to serialized RTree</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="load-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>load</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/RTrees.html" title="class in org.opencv.ml">RTrees</a>&nbsp;load(java.lang.String&nbsp;filepath,
                          java.lang.String&nbsp;nodeName)</pre>
<div class="block">Loads and creates a serialized RTree from a file

 Use RTree::save to serialize and store an RTree to disk.
 Load the RTree from this file again, by calling this function with the path to the file.
 Optionally specify the node for the file containing the classifier</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filepath</code> - path to serialized RTree</dd>
<dd><code>nodeName</code> - name of node containing the classifier</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setActiveVarCount-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActiveVarCount</h4>
<pre>public&nbsp;void&nbsp;setActiveVarCount(int&nbsp;val)</pre>
<div class="block">getActiveVarCount SEE: getActiveVarCount</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setCalculateVarImportance-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCalculateVarImportance</h4>
<pre>public&nbsp;void&nbsp;setCalculateVarImportance(boolean&nbsp;val)</pre>
<div class="block">getCalculateVarImportance SEE: getCalculateVarImportance</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setTermCriteria-org.opencv.core.TermCriteria-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setTermCriteria</h4>
<pre>public&nbsp;void&nbsp;setTermCriteria(<a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;val)</pre>
<div class="block">getTermCriteria SEE: getTermCriteria</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/ml/RTrees.html" target="_top">Frames</a></li>
<li><a href="RTrees.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.org.opencv.ml.DTrees">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2021-12-25 08:13:27 / OpenCV 4.5.5</small></p>
</body>
</html>
