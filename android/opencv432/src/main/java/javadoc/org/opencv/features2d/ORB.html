<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_292) on Sat Dec 25 08:13:29 UTC 2021 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ORB (OpenCV 4.5.5 Java documentation)</title>
<meta name="date" content="2021-12-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ORB (OpenCV 4.5.5 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/features2d/MSER.html" title="class in org.opencv.features2d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/features2d/SIFT.html" title="class in org.opencv.features2d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/ORB.html" target="_top">Frames</a></li>
<li><a href="ORB.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.features2d</div>
<h2 title="Class ORB" class="title">Class ORB</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">org.opencv.features2d.Feature2D</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.features2d.ORB</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ORB</span>
extends <a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></pre>
<div class="block">Class implementing the ORB (*oriented BRIEF*) keypoint detector and descriptor extractor

 described in CITE: RRKB11 . The algorithm uses FAST in pyramids to detect stable keypoints, selects
 the strongest features using FAST or Harris response, finds their orientation using first-order
 moments and computes the descriptors using BRIEF (where the coordinates of random point pairs (or
 k-tuples) are rotated according to the measured orientation).</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#FAST_SCORE">FAST_SCORE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#HARRIS_SCORE">HARRIS_SCORE</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d">ORB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d">ORB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#create--">create</a></span>()</code>
<div class="block">The ORB constructor

     pyramid, where each next level has 4x less pixels than the previous, but such a big scale factor
     will degrade feature matching scores dramatically.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d">ORB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#create-int-">create</a></span>(int&nbsp;nfeatures)</code>
<div class="block">The ORB constructor</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d">ORB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#create-int-float-">create</a></span>(int&nbsp;nfeatures,
      float&nbsp;scaleFactor)</code>
<div class="block">The ORB constructor</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d">ORB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#create-int-float-int-">create</a></span>(int&nbsp;nfeatures,
      float&nbsp;scaleFactor,
      int&nbsp;nlevels)</code>
<div class="block">The ORB constructor</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d">ORB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#create-int-float-int-int-">create</a></span>(int&nbsp;nfeatures,
      float&nbsp;scaleFactor,
      int&nbsp;nlevels,
      int&nbsp;edgeThreshold)</code>
<div class="block">The ORB constructor</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d">ORB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#create-int-float-int-int-int-">create</a></span>(int&nbsp;nfeatures,
      float&nbsp;scaleFactor,
      int&nbsp;nlevels,
      int&nbsp;edgeThreshold,
      int&nbsp;firstLevel)</code>
<div class="block">The ORB constructor</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d">ORB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#create-int-float-int-int-int-int-">create</a></span>(int&nbsp;nfeatures,
      float&nbsp;scaleFactor,
      int&nbsp;nlevels,
      int&nbsp;edgeThreshold,
      int&nbsp;firstLevel,
      int&nbsp;WTA_K)</code>
<div class="block">The ORB constructor</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d">ORB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#create-int-float-int-int-int-int-int-">create</a></span>(int&nbsp;nfeatures,
      float&nbsp;scaleFactor,
      int&nbsp;nlevels,
      int&nbsp;edgeThreshold,
      int&nbsp;firstLevel,
      int&nbsp;WTA_K,
      int&nbsp;scoreType)</code>
<div class="block">The ORB constructor</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d">ORB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#create-int-float-int-int-int-int-int-int-">create</a></span>(int&nbsp;nfeatures,
      float&nbsp;scaleFactor,
      int&nbsp;nlevels,
      int&nbsp;edgeThreshold,
      int&nbsp;firstLevel,
      int&nbsp;WTA_K,
      int&nbsp;scoreType,
      int&nbsp;patchSize)</code>
<div class="block">The ORB constructor</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d">ORB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#create-int-float-int-int-int-int-int-int-int-">create</a></span>(int&nbsp;nfeatures,
      float&nbsp;scaleFactor,
      int&nbsp;nlevels,
      int&nbsp;edgeThreshold,
      int&nbsp;firstLevel,
      int&nbsp;WTA_K,
      int&nbsp;scoreType,
      int&nbsp;patchSize,
      int&nbsp;fastThreshold)</code>
<div class="block">The ORB constructor</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#getDefaultName--">getDefaultName</a></span>()</code>
<div class="block">Returns the algorithm string identifier.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#getEdgeThreshold--">getEdgeThreshold</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#getFastThreshold--">getFastThreshold</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#getFirstLevel--">getFirstLevel</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#getMaxFeatures--">getMaxFeatures</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#getNLevels--">getNLevels</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#getPatchSize--">getPatchSize</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#getScaleFactor--">getScaleFactor</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#getScoreType--">getScoreType</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#getWTA_K--">getWTA_K</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#setEdgeThreshold-int-">setEdgeThreshold</a></span>(int&nbsp;edgeThreshold)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#setFastThreshold-int-">setFastThreshold</a></span>(int&nbsp;fastThreshold)</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#setFirstLevel-int-">setFirstLevel</a></span>(int&nbsp;firstLevel)</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#setMaxFeatures-int-">setMaxFeatures</a></span>(int&nbsp;maxFeatures)</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#setNLevels-int-">setNLevels</a></span>(int&nbsp;nlevels)</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#setPatchSize-int-">setPatchSize</a></span>(int&nbsp;patchSize)</code>&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#setScaleFactor-double-">setScaleFactor</a></span>(double&nbsp;scaleFactor)</code>&nbsp;</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#setScoreType-int-">setScoreType</a></span>(int&nbsp;scoreType)</code>&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/ORB.html#setWTA_K-int-">setWTA_K</a></span>(int&nbsp;wta_k)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.features2d.Feature2D">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.features2d.<a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></h3>
<code><a href="../../../org/opencv/features2d/Feature2D.html#compute-java.util.List-java.util.List-java.util.List-">compute</a>, <a href="../../../org/opencv/features2d/Feature2D.html#compute-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-">compute</a>, <a href="../../../org/opencv/features2d/Feature2D.html#defaultNorm--">defaultNorm</a>, <a href="../../../org/opencv/features2d/Feature2D.html#descriptorSize--">descriptorSize</a>, <a href="../../../org/opencv/features2d/Feature2D.html#descriptorType--">descriptorType</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detect-java.util.List-java.util.List-">detect</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detect-java.util.List-java.util.List-java.util.List-">detect</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detect-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-">detect</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detect-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-">detect</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detectAndCompute-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-">detectAndCompute</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detectAndCompute-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-boolean-">detectAndCompute</a>, <a href="../../../org/opencv/features2d/Feature2D.html#empty--">empty</a>, <a href="../../../org/opencv/features2d/Feature2D.html#read-java.lang.String-">read</a>, <a href="../../../org/opencv/features2d/Feature2D.html#write-java.lang.String-">write</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="FAST_SCORE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FAST_SCORE</h4>
<pre>public static final&nbsp;int FAST_SCORE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.ORB.FAST_SCORE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HARRIS_SCORE">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HARRIS_SCORE</h4>
<pre>public static final&nbsp;int HARRIS_SCORE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.ORB.HARRIS_SCORE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d">ORB</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="create--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d">ORB</a>&nbsp;create()</pre>
<div class="block">The ORB constructor

     pyramid, where each next level has 4x less pixels than the previous, but such a big scale factor
     will degrade feature matching scores dramatically. On the other hand, too close to 1 scale factor
     will mean that to cover certain scale range you will need more pyramid levels and so the speed
     will suffer.
     input_image_linear_size/pow(scaleFactor, nlevels - firstLevel).
     roughly match the patchSize parameter.
     with upscaled source image.
     default value 2 means the BRIEF where we take a random point pair and compare their brightnesses,
     so we get 0/1 response. Other possible values are 3 and 4. For example, 3 means that we take 3
     random points (of course, those point coordinates are random, but they are generated from the
     pre-defined seed, so each element of BRIEF descriptor is computed deterministically from the pixel
     rectangle), find point of maximum brightness and output index of the winner (0, 1 or 2). Such
     output will occupy 2 bits, and therefore it will need a special variant of Hamming distance,
     denoted as NORM_HAMMING2 (2 bits per bin). When WTA_K=4, we take 4 random points to compute each
     bin (that will also occupy 2 bits with possible values 0, 1, 2 or 3).
     (the score is written to KeyPoint::score and is used to retain best nfeatures features);
     FAST_SCORE is alternative value of the parameter that produces slightly less stable keypoints,
     but it is a little faster to compute.
     pyramid layers the perceived image area covered by a feature will be larger.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d">ORB</a>&nbsp;create(int&nbsp;nfeatures)</pre>
<div class="block">The ORB constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nfeatures</code> - The maximum number of features to retain.
     pyramid, where each next level has 4x less pixels than the previous, but such a big scale factor
     will degrade feature matching scores dramatically. On the other hand, too close to 1 scale factor
     will mean that to cover certain scale range you will need more pyramid levels and so the speed
     will suffer.
     input_image_linear_size/pow(scaleFactor, nlevels - firstLevel).
     roughly match the patchSize parameter.
     with upscaled source image.
     default value 2 means the BRIEF where we take a random point pair and compare their brightnesses,
     so we get 0/1 response. Other possible values are 3 and 4. For example, 3 means that we take 3
     random points (of course, those point coordinates are random, but they are generated from the
     pre-defined seed, so each element of BRIEF descriptor is computed deterministically from the pixel
     rectangle), find point of maximum brightness and output index of the winner (0, 1 or 2). Such
     output will occupy 2 bits, and therefore it will need a special variant of Hamming distance,
     denoted as NORM_HAMMING2 (2 bits per bin). When WTA_K=4, we take 4 random points to compute each
     bin (that will also occupy 2 bits with possible values 0, 1, 2 or 3).
     (the score is written to KeyPoint::score and is used to retain best nfeatures features);
     FAST_SCORE is alternative value of the parameter that produces slightly less stable keypoints,
     but it is a little faster to compute.
     pyramid layers the perceived image area covered by a feature will be larger.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d">ORB</a>&nbsp;create(int&nbsp;nfeatures,
                         float&nbsp;scaleFactor)</pre>
<div class="block">The ORB constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nfeatures</code> - The maximum number of features to retain.</dd>
<dd><code>scaleFactor</code> - Pyramid decimation ratio, greater than 1. scaleFactor==2 means the classical
     pyramid, where each next level has 4x less pixels than the previous, but such a big scale factor
     will degrade feature matching scores dramatically. On the other hand, too close to 1 scale factor
     will mean that to cover certain scale range you will need more pyramid levels and so the speed
     will suffer.
     input_image_linear_size/pow(scaleFactor, nlevels - firstLevel).
     roughly match the patchSize parameter.
     with upscaled source image.
     default value 2 means the BRIEF where we take a random point pair and compare their brightnesses,
     so we get 0/1 response. Other possible values are 3 and 4. For example, 3 means that we take 3
     random points (of course, those point coordinates are random, but they are generated from the
     pre-defined seed, so each element of BRIEF descriptor is computed deterministically from the pixel
     rectangle), find point of maximum brightness and output index of the winner (0, 1 or 2). Such
     output will occupy 2 bits, and therefore it will need a special variant of Hamming distance,
     denoted as NORM_HAMMING2 (2 bits per bin). When WTA_K=4, we take 4 random points to compute each
     bin (that will also occupy 2 bits with possible values 0, 1, 2 or 3).
     (the score is written to KeyPoint::score and is used to retain best nfeatures features);
     FAST_SCORE is alternative value of the parameter that produces slightly less stable keypoints,
     but it is a little faster to compute.
     pyramid layers the perceived image area covered by a feature will be larger.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-float-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d">ORB</a>&nbsp;create(int&nbsp;nfeatures,
                         float&nbsp;scaleFactor,
                         int&nbsp;nlevels)</pre>
<div class="block">The ORB constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nfeatures</code> - The maximum number of features to retain.</dd>
<dd><code>scaleFactor</code> - Pyramid decimation ratio, greater than 1. scaleFactor==2 means the classical
     pyramid, where each next level has 4x less pixels than the previous, but such a big scale factor
     will degrade feature matching scores dramatically. On the other hand, too close to 1 scale factor
     will mean that to cover certain scale range you will need more pyramid levels and so the speed
     will suffer.</dd>
<dd><code>nlevels</code> - The number of pyramid levels. The smallest level will have linear size equal to
     input_image_linear_size/pow(scaleFactor, nlevels - firstLevel).
     roughly match the patchSize parameter.
     with upscaled source image.
     default value 2 means the BRIEF where we take a random point pair and compare their brightnesses,
     so we get 0/1 response. Other possible values are 3 and 4. For example, 3 means that we take 3
     random points (of course, those point coordinates are random, but they are generated from the
     pre-defined seed, so each element of BRIEF descriptor is computed deterministically from the pixel
     rectangle), find point of maximum brightness and output index of the winner (0, 1 or 2). Such
     output will occupy 2 bits, and therefore it will need a special variant of Hamming distance,
     denoted as NORM_HAMMING2 (2 bits per bin). When WTA_K=4, we take 4 random points to compute each
     bin (that will also occupy 2 bits with possible values 0, 1, 2 or 3).
     (the score is written to KeyPoint::score and is used to retain best nfeatures features);
     FAST_SCORE is alternative value of the parameter that produces slightly less stable keypoints,
     but it is a little faster to compute.
     pyramid layers the perceived image area covered by a feature will be larger.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-float-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d">ORB</a>&nbsp;create(int&nbsp;nfeatures,
                         float&nbsp;scaleFactor,
                         int&nbsp;nlevels,
                         int&nbsp;edgeThreshold)</pre>
<div class="block">The ORB constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nfeatures</code> - The maximum number of features to retain.</dd>
<dd><code>scaleFactor</code> - Pyramid decimation ratio, greater than 1. scaleFactor==2 means the classical
     pyramid, where each next level has 4x less pixels than the previous, but such a big scale factor
     will degrade feature matching scores dramatically. On the other hand, too close to 1 scale factor
     will mean that to cover certain scale range you will need more pyramid levels and so the speed
     will suffer.</dd>
<dd><code>nlevels</code> - The number of pyramid levels. The smallest level will have linear size equal to
     input_image_linear_size/pow(scaleFactor, nlevels - firstLevel).</dd>
<dd><code>edgeThreshold</code> - This is size of the border where the features are not detected. It should
     roughly match the patchSize parameter.
     with upscaled source image.
     default value 2 means the BRIEF where we take a random point pair and compare their brightnesses,
     so we get 0/1 response. Other possible values are 3 and 4. For example, 3 means that we take 3
     random points (of course, those point coordinates are random, but they are generated from the
     pre-defined seed, so each element of BRIEF descriptor is computed deterministically from the pixel
     rectangle), find point of maximum brightness and output index of the winner (0, 1 or 2). Such
     output will occupy 2 bits, and therefore it will need a special variant of Hamming distance,
     denoted as NORM_HAMMING2 (2 bits per bin). When WTA_K=4, we take 4 random points to compute each
     bin (that will also occupy 2 bits with possible values 0, 1, 2 or 3).
     (the score is written to KeyPoint::score and is used to retain best nfeatures features);
     FAST_SCORE is alternative value of the parameter that produces slightly less stable keypoints,
     but it is a little faster to compute.
     pyramid layers the perceived image area covered by a feature will be larger.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-float-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d">ORB</a>&nbsp;create(int&nbsp;nfeatures,
                         float&nbsp;scaleFactor,
                         int&nbsp;nlevels,
                         int&nbsp;edgeThreshold,
                         int&nbsp;firstLevel)</pre>
<div class="block">The ORB constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nfeatures</code> - The maximum number of features to retain.</dd>
<dd><code>scaleFactor</code> - Pyramid decimation ratio, greater than 1. scaleFactor==2 means the classical
     pyramid, where each next level has 4x less pixels than the previous, but such a big scale factor
     will degrade feature matching scores dramatically. On the other hand, too close to 1 scale factor
     will mean that to cover certain scale range you will need more pyramid levels and so the speed
     will suffer.</dd>
<dd><code>nlevels</code> - The number of pyramid levels. The smallest level will have linear size equal to
     input_image_linear_size/pow(scaleFactor, nlevels - firstLevel).</dd>
<dd><code>edgeThreshold</code> - This is size of the border where the features are not detected. It should
     roughly match the patchSize parameter.</dd>
<dd><code>firstLevel</code> - The level of pyramid to put source image to. Previous layers are filled
     with upscaled source image.
     default value 2 means the BRIEF where we take a random point pair and compare their brightnesses,
     so we get 0/1 response. Other possible values are 3 and 4. For example, 3 means that we take 3
     random points (of course, those point coordinates are random, but they are generated from the
     pre-defined seed, so each element of BRIEF descriptor is computed deterministically from the pixel
     rectangle), find point of maximum brightness and output index of the winner (0, 1 or 2). Such
     output will occupy 2 bits, and therefore it will need a special variant of Hamming distance,
     denoted as NORM_HAMMING2 (2 bits per bin). When WTA_K=4, we take 4 random points to compute each
     bin (that will also occupy 2 bits with possible values 0, 1, 2 or 3).
     (the score is written to KeyPoint::score and is used to retain best nfeatures features);
     FAST_SCORE is alternative value of the parameter that produces slightly less stable keypoints,
     but it is a little faster to compute.
     pyramid layers the perceived image area covered by a feature will be larger.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-float-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d">ORB</a>&nbsp;create(int&nbsp;nfeatures,
                         float&nbsp;scaleFactor,
                         int&nbsp;nlevels,
                         int&nbsp;edgeThreshold,
                         int&nbsp;firstLevel,
                         int&nbsp;WTA_K)</pre>
<div class="block">The ORB constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nfeatures</code> - The maximum number of features to retain.</dd>
<dd><code>scaleFactor</code> - Pyramid decimation ratio, greater than 1. scaleFactor==2 means the classical
     pyramid, where each next level has 4x less pixels than the previous, but such a big scale factor
     will degrade feature matching scores dramatically. On the other hand, too close to 1 scale factor
     will mean that to cover certain scale range you will need more pyramid levels and so the speed
     will suffer.</dd>
<dd><code>nlevels</code> - The number of pyramid levels. The smallest level will have linear size equal to
     input_image_linear_size/pow(scaleFactor, nlevels - firstLevel).</dd>
<dd><code>edgeThreshold</code> - This is size of the border where the features are not detected. It should
     roughly match the patchSize parameter.</dd>
<dd><code>firstLevel</code> - The level of pyramid to put source image to. Previous layers are filled
     with upscaled source image.</dd>
<dd><code>WTA_K</code> - The number of points that produce each element of the oriented BRIEF descriptor. The
     default value 2 means the BRIEF where we take a random point pair and compare their brightnesses,
     so we get 0/1 response. Other possible values are 3 and 4. For example, 3 means that we take 3
     random points (of course, those point coordinates are random, but they are generated from the
     pre-defined seed, so each element of BRIEF descriptor is computed deterministically from the pixel
     rectangle), find point of maximum brightness and output index of the winner (0, 1 or 2). Such
     output will occupy 2 bits, and therefore it will need a special variant of Hamming distance,
     denoted as NORM_HAMMING2 (2 bits per bin). When WTA_K=4, we take 4 random points to compute each
     bin (that will also occupy 2 bits with possible values 0, 1, 2 or 3).
     (the score is written to KeyPoint::score and is used to retain best nfeatures features);
     FAST_SCORE is alternative value of the parameter that produces slightly less stable keypoints,
     but it is a little faster to compute.
     pyramid layers the perceived image area covered by a feature will be larger.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-float-int-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d">ORB</a>&nbsp;create(int&nbsp;nfeatures,
                         float&nbsp;scaleFactor,
                         int&nbsp;nlevels,
                         int&nbsp;edgeThreshold,
                         int&nbsp;firstLevel,
                         int&nbsp;WTA_K,
                         int&nbsp;scoreType)</pre>
<div class="block">The ORB constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nfeatures</code> - The maximum number of features to retain.</dd>
<dd><code>scaleFactor</code> - Pyramid decimation ratio, greater than 1. scaleFactor==2 means the classical
     pyramid, where each next level has 4x less pixels than the previous, but such a big scale factor
     will degrade feature matching scores dramatically. On the other hand, too close to 1 scale factor
     will mean that to cover certain scale range you will need more pyramid levels and so the speed
     will suffer.</dd>
<dd><code>nlevels</code> - The number of pyramid levels. The smallest level will have linear size equal to
     input_image_linear_size/pow(scaleFactor, nlevels - firstLevel).</dd>
<dd><code>edgeThreshold</code> - This is size of the border where the features are not detected. It should
     roughly match the patchSize parameter.</dd>
<dd><code>firstLevel</code> - The level of pyramid to put source image to. Previous layers are filled
     with upscaled source image.</dd>
<dd><code>WTA_K</code> - The number of points that produce each element of the oriented BRIEF descriptor. The
     default value 2 means the BRIEF where we take a random point pair and compare their brightnesses,
     so we get 0/1 response. Other possible values are 3 and 4. For example, 3 means that we take 3
     random points (of course, those point coordinates are random, but they are generated from the
     pre-defined seed, so each element of BRIEF descriptor is computed deterministically from the pixel
     rectangle), find point of maximum brightness and output index of the winner (0, 1 or 2). Such
     output will occupy 2 bits, and therefore it will need a special variant of Hamming distance,
     denoted as NORM_HAMMING2 (2 bits per bin). When WTA_K=4, we take 4 random points to compute each
     bin (that will also occupy 2 bits with possible values 0, 1, 2 or 3).</dd>
<dd><code>scoreType</code> - The default HARRIS_SCORE means that Harris algorithm is used to rank features
     (the score is written to KeyPoint::score and is used to retain best nfeatures features);
     FAST_SCORE is alternative value of the parameter that produces slightly less stable keypoints,
     but it is a little faster to compute.
     pyramid layers the perceived image area covered by a feature will be larger.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-float-int-int-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d">ORB</a>&nbsp;create(int&nbsp;nfeatures,
                         float&nbsp;scaleFactor,
                         int&nbsp;nlevels,
                         int&nbsp;edgeThreshold,
                         int&nbsp;firstLevel,
                         int&nbsp;WTA_K,
                         int&nbsp;scoreType,
                         int&nbsp;patchSize)</pre>
<div class="block">The ORB constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nfeatures</code> - The maximum number of features to retain.</dd>
<dd><code>scaleFactor</code> - Pyramid decimation ratio, greater than 1. scaleFactor==2 means the classical
     pyramid, where each next level has 4x less pixels than the previous, but such a big scale factor
     will degrade feature matching scores dramatically. On the other hand, too close to 1 scale factor
     will mean that to cover certain scale range you will need more pyramid levels and so the speed
     will suffer.</dd>
<dd><code>nlevels</code> - The number of pyramid levels. The smallest level will have linear size equal to
     input_image_linear_size/pow(scaleFactor, nlevels - firstLevel).</dd>
<dd><code>edgeThreshold</code> - This is size of the border where the features are not detected. It should
     roughly match the patchSize parameter.</dd>
<dd><code>firstLevel</code> - The level of pyramid to put source image to. Previous layers are filled
     with upscaled source image.</dd>
<dd><code>WTA_K</code> - The number of points that produce each element of the oriented BRIEF descriptor. The
     default value 2 means the BRIEF where we take a random point pair and compare their brightnesses,
     so we get 0/1 response. Other possible values are 3 and 4. For example, 3 means that we take 3
     random points (of course, those point coordinates are random, but they are generated from the
     pre-defined seed, so each element of BRIEF descriptor is computed deterministically from the pixel
     rectangle), find point of maximum brightness and output index of the winner (0, 1 or 2). Such
     output will occupy 2 bits, and therefore it will need a special variant of Hamming distance,
     denoted as NORM_HAMMING2 (2 bits per bin). When WTA_K=4, we take 4 random points to compute each
     bin (that will also occupy 2 bits with possible values 0, 1, 2 or 3).</dd>
<dd><code>scoreType</code> - The default HARRIS_SCORE means that Harris algorithm is used to rank features
     (the score is written to KeyPoint::score and is used to retain best nfeatures features);
     FAST_SCORE is alternative value of the parameter that produces slightly less stable keypoints,
     but it is a little faster to compute.</dd>
<dd><code>patchSize</code> - size of the patch used by the oriented BRIEF descriptor. Of course, on smaller
     pyramid layers the perceived image area covered by a feature will be larger.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-float-int-int-int-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d">ORB</a>&nbsp;create(int&nbsp;nfeatures,
                         float&nbsp;scaleFactor,
                         int&nbsp;nlevels,
                         int&nbsp;edgeThreshold,
                         int&nbsp;firstLevel,
                         int&nbsp;WTA_K,
                         int&nbsp;scoreType,
                         int&nbsp;patchSize,
                         int&nbsp;fastThreshold)</pre>
<div class="block">The ORB constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nfeatures</code> - The maximum number of features to retain.</dd>
<dd><code>scaleFactor</code> - Pyramid decimation ratio, greater than 1. scaleFactor==2 means the classical
     pyramid, where each next level has 4x less pixels than the previous, but such a big scale factor
     will degrade feature matching scores dramatically. On the other hand, too close to 1 scale factor
     will mean that to cover certain scale range you will need more pyramid levels and so the speed
     will suffer.</dd>
<dd><code>nlevels</code> - The number of pyramid levels. The smallest level will have linear size equal to
     input_image_linear_size/pow(scaleFactor, nlevels - firstLevel).</dd>
<dd><code>edgeThreshold</code> - This is size of the border where the features are not detected. It should
     roughly match the patchSize parameter.</dd>
<dd><code>firstLevel</code> - The level of pyramid to put source image to. Previous layers are filled
     with upscaled source image.</dd>
<dd><code>WTA_K</code> - The number of points that produce each element of the oriented BRIEF descriptor. The
     default value 2 means the BRIEF where we take a random point pair and compare their brightnesses,
     so we get 0/1 response. Other possible values are 3 and 4. For example, 3 means that we take 3
     random points (of course, those point coordinates are random, but they are generated from the
     pre-defined seed, so each element of BRIEF descriptor is computed deterministically from the pixel
     rectangle), find point of maximum brightness and output index of the winner (0, 1 or 2). Such
     output will occupy 2 bits, and therefore it will need a special variant of Hamming distance,
     denoted as NORM_HAMMING2 (2 bits per bin). When WTA_K=4, we take 4 random points to compute each
     bin (that will also occupy 2 bits with possible values 0, 1, 2 or 3).</dd>
<dd><code>scoreType</code> - The default HARRIS_SCORE means that Harris algorithm is used to rank features
     (the score is written to KeyPoint::score and is used to retain best nfeatures features);
     FAST_SCORE is alternative value of the parameter that produces slightly less stable keypoints,
     but it is a little faster to compute.</dd>
<dd><code>patchSize</code> - size of the patch used by the oriented BRIEF descriptor. Of course, on smaller
     pyramid layers the perceived image area covered by a feature will be larger.</dd>
<dd><code>fastThreshold</code> - the fast threshold</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getDefaultName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDefaultName()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">Algorithm</a></code></span></div>
<div class="block">Returns the algorithm string identifier.
 This string is used as top level xml/yml node tag when the object is saved to a file or string.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../org/opencv/features2d/Feature2D.html#getDefaultName--">getDefaultName</a></code>&nbsp;in class&nbsp;<code><a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getEdgeThreshold--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEdgeThreshold</h4>
<pre>public&nbsp;int&nbsp;getEdgeThreshold()</pre>
</li>
</ul>
<a name="getFastThreshold--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFastThreshold</h4>
<pre>public&nbsp;int&nbsp;getFastThreshold()</pre>
</li>
</ul>
<a name="getFirstLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFirstLevel</h4>
<pre>public&nbsp;int&nbsp;getFirstLevel()</pre>
</li>
</ul>
<a name="getMaxFeatures--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxFeatures</h4>
<pre>public&nbsp;int&nbsp;getMaxFeatures()</pre>
</li>
</ul>
<a name="getNLevels--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNLevels</h4>
<pre>public&nbsp;int&nbsp;getNLevels()</pre>
</li>
</ul>
<a name="getPatchSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPatchSize</h4>
<pre>public&nbsp;int&nbsp;getPatchSize()</pre>
</li>
</ul>
<a name="getScaleFactor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScaleFactor</h4>
<pre>public&nbsp;double&nbsp;getScaleFactor()</pre>
</li>
</ul>
<a name="getScoreType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScoreType</h4>
<pre>public&nbsp;int&nbsp;getScoreType()</pre>
</li>
</ul>
<a name="getWTA_K--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWTA_K</h4>
<pre>public&nbsp;int&nbsp;getWTA_K()</pre>
</li>
</ul>
<a name="setEdgeThreshold-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEdgeThreshold</h4>
<pre>public&nbsp;void&nbsp;setEdgeThreshold(int&nbsp;edgeThreshold)</pre>
</li>
</ul>
<a name="setFastThreshold-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFastThreshold</h4>
<pre>public&nbsp;void&nbsp;setFastThreshold(int&nbsp;fastThreshold)</pre>
</li>
</ul>
<a name="setFirstLevel-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFirstLevel</h4>
<pre>public&nbsp;void&nbsp;setFirstLevel(int&nbsp;firstLevel)</pre>
</li>
</ul>
<a name="setMaxFeatures-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxFeatures</h4>
<pre>public&nbsp;void&nbsp;setMaxFeatures(int&nbsp;maxFeatures)</pre>
</li>
</ul>
<a name="setNLevels-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNLevels</h4>
<pre>public&nbsp;void&nbsp;setNLevels(int&nbsp;nlevels)</pre>
</li>
</ul>
<a name="setPatchSize-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPatchSize</h4>
<pre>public&nbsp;void&nbsp;setPatchSize(int&nbsp;patchSize)</pre>
</li>
</ul>
<a name="setScaleFactor-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScaleFactor</h4>
<pre>public&nbsp;void&nbsp;setScaleFactor(double&nbsp;scaleFactor)</pre>
</li>
</ul>
<a name="setScoreType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScoreType</h4>
<pre>public&nbsp;void&nbsp;setScoreType(int&nbsp;scoreType)</pre>
</li>
</ul>
<a name="setWTA_K-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setWTA_K</h4>
<pre>public&nbsp;void&nbsp;setWTA_K(int&nbsp;wta_k)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/features2d/MSER.html" title="class in org.opencv.features2d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/features2d/SIFT.html" title="class in org.opencv.features2d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/ORB.html" target="_top">Frames</a></li>
<li><a href="ORB.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2021-12-25 08:13:27 / OpenCV 4.5.5</small></p>
</body>
</html>
