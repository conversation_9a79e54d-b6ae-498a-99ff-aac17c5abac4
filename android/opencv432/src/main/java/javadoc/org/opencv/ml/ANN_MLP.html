<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_292) on Sat Dec 25 08:13:28 UTC 2021 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ANN_MLP (OpenCV 4.5.5 Java documentation)</title>
<meta name="date" content="2021-12-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ANN_MLP (OpenCV 4.5.5 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":9,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../org/opencv/ml/Boost.html" title="class in org.opencv.ml"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/ml/ANN_MLP.html" target="_top">Frames</a></li>
<li><a href="ANN_MLP.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.ml</div>
<h2 title="Class ANN_MLP" class="title">Class ANN_MLP</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">org.opencv.ml.StatModel</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.ml.ANN_MLP</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ANN_MLP</span>
extends <a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></pre>
<div class="block">Artificial Neural Networks - Multi-Layer Perceptrons.

 Unlike many other models in ML that are constructed and trained at once, in the MLP model these
 steps are separated. First, a network with the specified topology is created using the non-default
 constructor or the method ANN_MLP::create. All the weights are set to zeros. Then, the network is
 trained using a set of input and output vectors. The training procedure can be repeated more than
 once, that is, the weights can be adjusted based on the new training data.

 Additional flags for StatModel::train are available: ANN_MLP::TrainFlags.

 SEE: REF: ml_intro_ann</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#ANNEAL">ANNEAL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#BACKPROP">BACKPROP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#GAUSSIAN">GAUSSIAN</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#IDENTITY">IDENTITY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#LEAKYRELU">LEAKYRELU</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#NO_INPUT_SCALE">NO_INPUT_SCALE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#NO_OUTPUT_SCALE">NO_OUTPUT_SCALE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#RELU">RELU</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#RPROP">RPROP</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#SIGMOID_SYM">SIGMOID_SYM</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#UPDATE_WEIGHTS">UPDATE_WEIGHTS</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.opencv.ml.StatModel">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.opencv.ml.<a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="../../../org/opencv/ml/StatModel.html#COMPRESSED_INPUT">COMPRESSED_INPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#PREPROCESSED_INPUT">PREPROCESSED_INPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#RAW_OUTPUT">RAW_OUTPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#UPDATE_MODEL">UPDATE_MODEL</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/ANN_MLP.html" title="class in org.opencv.ml">ANN_MLP</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/ANN_MLP.html" title="class in org.opencv.ml">ANN_MLP</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#create--">create</a></span>()</code>
<div class="block">Creates empty model

     Use StatModel::train to train the model, Algorithm::load&lt;ANN_MLP&gt;(filename) to load the pre-trained model.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getAnnealCoolingRatio--">getAnnealCoolingRatio</a></span>()</code>
<div class="block">SEE: setAnnealCoolingRatio</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getAnnealFinalT--">getAnnealFinalT</a></span>()</code>
<div class="block">SEE: setAnnealFinalT</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getAnnealInitialT--">getAnnealInitialT</a></span>()</code>
<div class="block">SEE: setAnnealInitialT</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getAnnealItePerStep--">getAnnealItePerStep</a></span>()</code>
<div class="block">SEE: setAnnealItePerStep</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getBackpropMomentumScale--">getBackpropMomentumScale</a></span>()</code>
<div class="block">SEE: setBackpropMomentumScale</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getBackpropWeightScale--">getBackpropWeightScale</a></span>()</code>
<div class="block">SEE: setBackpropWeightScale</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getLayerSizes--">getLayerSizes</a></span>()</code>
<div class="block">Integer vector specifying the number of neurons in each layer including the input and output layers.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getRpropDW0--">getRpropDW0</a></span>()</code>
<div class="block">SEE: setRpropDW0</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getRpropDWMax--">getRpropDWMax</a></span>()</code>
<div class="block">SEE: setRpropDWMax</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getRpropDWMin--">getRpropDWMin</a></span>()</code>
<div class="block">SEE: setRpropDWMin</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getRpropDWMinus--">getRpropDWMinus</a></span>()</code>
<div class="block">SEE: setRpropDWMinus</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getRpropDWPlus--">getRpropDWPlus</a></span>()</code>
<div class="block">SEE: setRpropDWPlus</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getTermCriteria--">getTermCriteria</a></span>()</code>
<div class="block">SEE: setTermCriteria</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getTrainMethod--">getTrainMethod</a></span>()</code>
<div class="block">Returns current training method</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getWeights-int-">getWeights</a></span>(int&nbsp;layerIdx)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/ANN_MLP.html" title="class in org.opencv.ml">ANN_MLP</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#load-java.lang.String-">load</a></span>(java.lang.String&nbsp;filepath)</code>
<div class="block">Loads and creates a serialized ANN from a file

 Use ANN::save to serialize and store an ANN to disk.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setActivationFunction-int-">setActivationFunction</a></span>(int&nbsp;type)</code>
<div class="block">Initialize the activation function for each neuron.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setActivationFunction-int-double-">setActivationFunction</a></span>(int&nbsp;type,
                     double&nbsp;param1)</code>
<div class="block">Initialize the activation function for each neuron.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setActivationFunction-int-double-double-">setActivationFunction</a></span>(int&nbsp;type,
                     double&nbsp;param1,
                     double&nbsp;param2)</code>
<div class="block">Initialize the activation function for each neuron.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setAnnealCoolingRatio-double-">setAnnealCoolingRatio</a></span>(double&nbsp;val)</code>
<div class="block">getAnnealCoolingRatio SEE: getAnnealCoolingRatio</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setAnnealFinalT-double-">setAnnealFinalT</a></span>(double&nbsp;val)</code>
<div class="block">getAnnealFinalT SEE: getAnnealFinalT</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setAnnealInitialT-double-">setAnnealInitialT</a></span>(double&nbsp;val)</code>
<div class="block">getAnnealInitialT SEE: getAnnealInitialT</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setAnnealItePerStep-int-">setAnnealItePerStep</a></span>(int&nbsp;val)</code>
<div class="block">getAnnealItePerStep SEE: getAnnealItePerStep</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setBackpropMomentumScale-double-">setBackpropMomentumScale</a></span>(double&nbsp;val)</code>
<div class="block">getBackpropMomentumScale SEE: getBackpropMomentumScale</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setBackpropWeightScale-double-">setBackpropWeightScale</a></span>(double&nbsp;val)</code>
<div class="block">getBackpropWeightScale SEE: getBackpropWeightScale</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setLayerSizes-org.opencv.core.Mat-">setLayerSizes</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;_layer_sizes)</code>
<div class="block">Integer vector specifying the number of neurons in each layer including the input and output layers.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setRpropDW0-double-">setRpropDW0</a></span>(double&nbsp;val)</code>
<div class="block">getRpropDW0 SEE: getRpropDW0</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setRpropDWMax-double-">setRpropDWMax</a></span>(double&nbsp;val)</code>
<div class="block">getRpropDWMax SEE: getRpropDWMax</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setRpropDWMin-double-">setRpropDWMin</a></span>(double&nbsp;val)</code>
<div class="block">getRpropDWMin SEE: getRpropDWMin</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setRpropDWMinus-double-">setRpropDWMinus</a></span>(double&nbsp;val)</code>
<div class="block">getRpropDWMinus SEE: getRpropDWMinus</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setRpropDWPlus-double-">setRpropDWPlus</a></span>(double&nbsp;val)</code>
<div class="block">getRpropDWPlus SEE: getRpropDWPlus</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setTermCriteria-org.opencv.core.TermCriteria-">setTermCriteria</a></span>(<a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;val)</code>
<div class="block">getTermCriteria SEE: getTermCriteria</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setTrainMethod-int-">setTrainMethod</a></span>(int&nbsp;method)</code>
<div class="block">Sets training method and common parameters.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setTrainMethod-int-double-">setTrainMethod</a></span>(int&nbsp;method,
              double&nbsp;param1)</code>
<div class="block">Sets training method and common parameters.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setTrainMethod-int-double-double-">setTrainMethod</a></span>(int&nbsp;method,
              double&nbsp;param1,
              double&nbsp;param2)</code>
<div class="block">Sets training method and common parameters.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.ml.StatModel">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.ml.<a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="../../../org/opencv/ml/StatModel.html#calcError-org.opencv.ml.TrainData-boolean-org.opencv.core.Mat-">calcError</a>, <a href="../../../org/opencv/ml/StatModel.html#empty--">empty</a>, <a href="../../../org/opencv/ml/StatModel.html#getVarCount--">getVarCount</a>, <a href="../../../org/opencv/ml/StatModel.html#isClassifier--">isClassifier</a>, <a href="../../../org/opencv/ml/StatModel.html#isTrained--">isTrained</a>, <a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-">predict</a>, <a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-org.opencv.core.Mat-">predict</a>, <a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-org.opencv.core.Mat-int-">predict</a>, <a href="../../../org/opencv/ml/StatModel.html#train-org.opencv.core.Mat-int-org.opencv.core.Mat-">train</a>, <a href="../../../org/opencv/ml/StatModel.html#train-org.opencv.ml.TrainData-">train</a>, <a href="../../../org/opencv/ml/StatModel.html#train-org.opencv.ml.TrainData-int-">train</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">getDefaultName</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="ANNEAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ANNEAL</h4>
<pre>public static final&nbsp;int ANNEAL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.ANNEAL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BACKPROP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BACKPROP</h4>
<pre>public static final&nbsp;int BACKPROP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.BACKPROP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GAUSSIAN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GAUSSIAN</h4>
<pre>public static final&nbsp;int GAUSSIAN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.GAUSSIAN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IDENTITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IDENTITY</h4>
<pre>public static final&nbsp;int IDENTITY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.IDENTITY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LEAKYRELU">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LEAKYRELU</h4>
<pre>public static final&nbsp;int LEAKYRELU</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.LEAKYRELU">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NO_INPUT_SCALE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NO_INPUT_SCALE</h4>
<pre>public static final&nbsp;int NO_INPUT_SCALE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.NO_INPUT_SCALE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NO_OUTPUT_SCALE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NO_OUTPUT_SCALE</h4>
<pre>public static final&nbsp;int NO_OUTPUT_SCALE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.NO_OUTPUT_SCALE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RELU">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RELU</h4>
<pre>public static final&nbsp;int RELU</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.RELU">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RPROP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RPROP</h4>
<pre>public static final&nbsp;int RPROP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.RPROP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SIGMOID_SYM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SIGMOID_SYM</h4>
<pre>public static final&nbsp;int SIGMOID_SYM</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.SIGMOID_SYM">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="UPDATE_WEIGHTS">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>UPDATE_WEIGHTS</h4>
<pre>public static final&nbsp;int UPDATE_WEIGHTS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.UPDATE_WEIGHTS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/ANN_MLP.html" title="class in org.opencv.ml">ANN_MLP</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="create--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/ANN_MLP.html" title="class in org.opencv.ml">ANN_MLP</a>&nbsp;create()</pre>
<div class="block">Creates empty model

     Use StatModel::train to train the model, Algorithm::load&lt;ANN_MLP&gt;(filename) to load the pre-trained model.
     Note that the train method has optional flags: ANN_MLP::TrainFlags.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getAnnealCoolingRatio--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAnnealCoolingRatio</h4>
<pre>public&nbsp;double&nbsp;getAnnealCoolingRatio()</pre>
<div class="block">SEE: setAnnealCoolingRatio</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getAnnealFinalT--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAnnealFinalT</h4>
<pre>public&nbsp;double&nbsp;getAnnealFinalT()</pre>
<div class="block">SEE: setAnnealFinalT</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getAnnealInitialT--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAnnealInitialT</h4>
<pre>public&nbsp;double&nbsp;getAnnealInitialT()</pre>
<div class="block">SEE: setAnnealInitialT</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getAnnealItePerStep--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAnnealItePerStep</h4>
<pre>public&nbsp;int&nbsp;getAnnealItePerStep()</pre>
<div class="block">SEE: setAnnealItePerStep</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getBackpropMomentumScale--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBackpropMomentumScale</h4>
<pre>public&nbsp;double&nbsp;getBackpropMomentumScale()</pre>
<div class="block">SEE: setBackpropMomentumScale</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getBackpropWeightScale--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBackpropWeightScale</h4>
<pre>public&nbsp;double&nbsp;getBackpropWeightScale()</pre>
<div class="block">SEE: setBackpropWeightScale</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getLayerSizes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLayerSizes</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getLayerSizes()</pre>
<div class="block">Integer vector specifying the number of neurons in each layer including the input and output layers.
     The very first element specifies the number of elements in the input layer.
     The last element - number of elements in the output layer.
 SEE: setLayerSizes</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getRpropDW0--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRpropDW0</h4>
<pre>public&nbsp;double&nbsp;getRpropDW0()</pre>
<div class="block">SEE: setRpropDW0</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getRpropDWMax--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRpropDWMax</h4>
<pre>public&nbsp;double&nbsp;getRpropDWMax()</pre>
<div class="block">SEE: setRpropDWMax</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getRpropDWMin--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRpropDWMin</h4>
<pre>public&nbsp;double&nbsp;getRpropDWMin()</pre>
<div class="block">SEE: setRpropDWMin</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getRpropDWMinus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRpropDWMinus</h4>
<pre>public&nbsp;double&nbsp;getRpropDWMinus()</pre>
<div class="block">SEE: setRpropDWMinus</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getRpropDWPlus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRpropDWPlus</h4>
<pre>public&nbsp;double&nbsp;getRpropDWPlus()</pre>
<div class="block">SEE: setRpropDWPlus</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getTermCriteria--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTermCriteria</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;getTermCriteria()</pre>
<div class="block">SEE: setTermCriteria</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getTrainMethod--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTrainMethod</h4>
<pre>public&nbsp;int&nbsp;getTrainMethod()</pre>
<div class="block">Returns current training method</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getWeights-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWeights</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getWeights(int&nbsp;layerIdx)</pre>
</li>
</ul>
<a name="load-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>load</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/ANN_MLP.html" title="class in org.opencv.ml">ANN_MLP</a>&nbsp;load(java.lang.String&nbsp;filepath)</pre>
<div class="block">Loads and creates a serialized ANN from a file

 Use ANN::save to serialize and store an ANN to disk.
 Load the ANN from this file again, by calling this function with the path to the file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filepath</code> - path to serialized ANN</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setActivationFunction-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActivationFunction</h4>
<pre>public&nbsp;void&nbsp;setActivationFunction(int&nbsp;type)</pre>
<div class="block">Initialize the activation function for each neuron.
     Currently the default and the only fully supported activation function is ANN_MLP::SIGMOID_SYM.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - The type of activation function. See ANN_MLP::ActivationFunctions.</dd>
</dl>
</li>
</ul>
<a name="setActivationFunction-int-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActivationFunction</h4>
<pre>public&nbsp;void&nbsp;setActivationFunction(int&nbsp;type,
                                  double&nbsp;param1)</pre>
<div class="block">Initialize the activation function for each neuron.
     Currently the default and the only fully supported activation function is ANN_MLP::SIGMOID_SYM.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - The type of activation function. See ANN_MLP::ActivationFunctions.</dd>
<dd><code>param1</code> - The first parameter of the activation function, \(\alpha\). Default value is 0.</dd>
</dl>
</li>
</ul>
<a name="setActivationFunction-int-double-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActivationFunction</h4>
<pre>public&nbsp;void&nbsp;setActivationFunction(int&nbsp;type,
                                  double&nbsp;param1,
                                  double&nbsp;param2)</pre>
<div class="block">Initialize the activation function for each neuron.
     Currently the default and the only fully supported activation function is ANN_MLP::SIGMOID_SYM.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - The type of activation function. See ANN_MLP::ActivationFunctions.</dd>
<dd><code>param1</code> - The first parameter of the activation function, \(\alpha\). Default value is 0.</dd>
<dd><code>param2</code> - The second parameter of the activation function, \(\beta\). Default value is 0.</dd>
</dl>
</li>
</ul>
<a name="setAnnealCoolingRatio-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAnnealCoolingRatio</h4>
<pre>public&nbsp;void&nbsp;setAnnealCoolingRatio(double&nbsp;val)</pre>
<div class="block">getAnnealCoolingRatio SEE: getAnnealCoolingRatio</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setAnnealFinalT-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAnnealFinalT</h4>
<pre>public&nbsp;void&nbsp;setAnnealFinalT(double&nbsp;val)</pre>
<div class="block">getAnnealFinalT SEE: getAnnealFinalT</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setAnnealInitialT-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAnnealInitialT</h4>
<pre>public&nbsp;void&nbsp;setAnnealInitialT(double&nbsp;val)</pre>
<div class="block">getAnnealInitialT SEE: getAnnealInitialT</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setAnnealItePerStep-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAnnealItePerStep</h4>
<pre>public&nbsp;void&nbsp;setAnnealItePerStep(int&nbsp;val)</pre>
<div class="block">getAnnealItePerStep SEE: getAnnealItePerStep</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setBackpropMomentumScale-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBackpropMomentumScale</h4>
<pre>public&nbsp;void&nbsp;setBackpropMomentumScale(double&nbsp;val)</pre>
<div class="block">getBackpropMomentumScale SEE: getBackpropMomentumScale</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setBackpropWeightScale-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBackpropWeightScale</h4>
<pre>public&nbsp;void&nbsp;setBackpropWeightScale(double&nbsp;val)</pre>
<div class="block">getBackpropWeightScale SEE: getBackpropWeightScale</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setLayerSizes-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLayerSizes</h4>
<pre>public&nbsp;void&nbsp;setLayerSizes(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;_layer_sizes)</pre>
<div class="block">Integer vector specifying the number of neurons in each layer including the input and output layers.
     The very first element specifies the number of elements in the input layer.
     The last element - number of elements in the output layer. Default value is empty Mat.
 SEE: getLayerSizes</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>_layer_sizes</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setRpropDW0-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRpropDW0</h4>
<pre>public&nbsp;void&nbsp;setRpropDW0(double&nbsp;val)</pre>
<div class="block">getRpropDW0 SEE: getRpropDW0</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setRpropDWMax-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRpropDWMax</h4>
<pre>public&nbsp;void&nbsp;setRpropDWMax(double&nbsp;val)</pre>
<div class="block">getRpropDWMax SEE: getRpropDWMax</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setRpropDWMin-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRpropDWMin</h4>
<pre>public&nbsp;void&nbsp;setRpropDWMin(double&nbsp;val)</pre>
<div class="block">getRpropDWMin SEE: getRpropDWMin</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setRpropDWMinus-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRpropDWMinus</h4>
<pre>public&nbsp;void&nbsp;setRpropDWMinus(double&nbsp;val)</pre>
<div class="block">getRpropDWMinus SEE: getRpropDWMinus</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setRpropDWPlus-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRpropDWPlus</h4>
<pre>public&nbsp;void&nbsp;setRpropDWPlus(double&nbsp;val)</pre>
<div class="block">getRpropDWPlus SEE: getRpropDWPlus</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setTermCriteria-org.opencv.core.TermCriteria-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTermCriteria</h4>
<pre>public&nbsp;void&nbsp;setTermCriteria(<a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;val)</pre>
<div class="block">getTermCriteria SEE: getTermCriteria</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setTrainMethod-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTrainMethod</h4>
<pre>public&nbsp;void&nbsp;setTrainMethod(int&nbsp;method)</pre>
<div class="block">Sets training method and common parameters.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>method</code> - Default value is ANN_MLP::RPROP. See ANN_MLP::TrainingMethods.</dd>
</dl>
</li>
</ul>
<a name="setTrainMethod-int-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTrainMethod</h4>
<pre>public&nbsp;void&nbsp;setTrainMethod(int&nbsp;method,
                           double&nbsp;param1)</pre>
<div class="block">Sets training method and common parameters.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>method</code> - Default value is ANN_MLP::RPROP. See ANN_MLP::TrainingMethods.</dd>
<dd><code>param1</code> - passed to setRpropDW0 for ANN_MLP::RPROP and to setBackpropWeightScale for ANN_MLP::BACKPROP and to initialT for ANN_MLP::ANNEAL.</dd>
</dl>
</li>
</ul>
<a name="setTrainMethod-int-double-double-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setTrainMethod</h4>
<pre>public&nbsp;void&nbsp;setTrainMethod(int&nbsp;method,
                           double&nbsp;param1,
                           double&nbsp;param2)</pre>
<div class="block">Sets training method and common parameters.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>method</code> - Default value is ANN_MLP::RPROP. See ANN_MLP::TrainingMethods.</dd>
<dd><code>param1</code> - passed to setRpropDW0 for ANN_MLP::RPROP and to setBackpropWeightScale for ANN_MLP::BACKPROP and to initialT for ANN_MLP::ANNEAL.</dd>
<dd><code>param2</code> - passed to setRpropDWMin for ANN_MLP::RPROP and to setBackpropMomentumScale for ANN_MLP::BACKPROP and to finalT for ANN_MLP::ANNEAL.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../org/opencv/ml/Boost.html" title="class in org.opencv.ml"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/ml/ANN_MLP.html" target="_top">Frames</a></li>
<li><a href="ANN_MLP.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2021-12-25 08:13:27 / OpenCV 4.5.5</small></p>
</body>
</html>
