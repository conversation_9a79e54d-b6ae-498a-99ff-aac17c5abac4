<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_292) on Sat Dec 25 08:13:28 UTC 2021 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>CascadeClassifier (OpenCV 4.5.5 Java documentation)</title>
<meta name="date" content="2021-12-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CascadeClassifier (OpenCV 4.5.5 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/objdetect/BaseCascadeClassifier.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/objdetect/FaceDetectorYN.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/CascadeClassifier.html" target="_top">Frames</a></li>
<li><a href="CascadeClassifier.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.objdetect</div>
<h2 title="Class CascadeClassifier" class="title">Class CascadeClassifier</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.objdetect.CascadeClassifier</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">CascadeClassifier</span>
extends java.lang.Object</pre>
<div class="block">Cascade classifier class for object detection.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#CascadeClassifier--">CascadeClassifier</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#CascadeClassifier-java.lang.String-">CascadeClassifier</a></span>(java.lang.String&nbsp;filename)</code>
<div class="block">Loads a classifier from a file.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/objdetect/CascadeClassifier.html" title="class in org.opencv.objdetect">CascadeClassifier</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#convert-java.lang.String-java.lang.String-">convert</a></span>(java.lang.String&nbsp;oldcascade,
       java.lang.String&nbsp;newcascade)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-">detectMultiScale</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects)</code>
<div class="block">Detects objects of different sizes in the input image.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-double-">detectMultiScale</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                double&nbsp;scaleFactor)</code>
<div class="block">Detects objects of different sizes in the input image.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-double-int-">detectMultiScale</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                double&nbsp;scaleFactor,
                int&nbsp;minNeighbors)</code>
<div class="block">Detects objects of different sizes in the input image.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-double-int-int-">detectMultiScale</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                double&nbsp;scaleFactor,
                int&nbsp;minNeighbors,
                int&nbsp;flags)</code>
<div class="block">Detects objects of different sizes in the input image.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-double-int-int-org.opencv.core.Size-">detectMultiScale</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                double&nbsp;scaleFactor,
                int&nbsp;minNeighbors,
                int&nbsp;flags,
                <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize)</code>
<div class="block">Detects objects of different sizes in the input image.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-double-int-int-org.opencv.core.Size-org.opencv.core.Size-">detectMultiScale</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                double&nbsp;scaleFactor,
                int&nbsp;minNeighbors,
                int&nbsp;flags,
                <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize,
                <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;maxSize)</code>
<div class="block">Detects objects of different sizes in the input image.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#detectMultiScale2-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-">detectMultiScale2</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                 <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                 <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#detectMultiScale2-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-double-">detectMultiScale2</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                 <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                 <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections,
                 double&nbsp;scaleFactor)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#detectMultiScale2-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-double-int-">detectMultiScale2</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                 <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                 <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections,
                 double&nbsp;scaleFactor,
                 int&nbsp;minNeighbors)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#detectMultiScale2-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-double-int-int-">detectMultiScale2</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                 <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                 <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections,
                 double&nbsp;scaleFactor,
                 int&nbsp;minNeighbors,
                 int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#detectMultiScale2-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-double-int-int-org.opencv.core.Size-">detectMultiScale2</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                 <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                 <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections,
                 double&nbsp;scaleFactor,
                 int&nbsp;minNeighbors,
                 int&nbsp;flags,
                 <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#detectMultiScale2-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-double-int-int-org.opencv.core.Size-org.opencv.core.Size-">detectMultiScale2</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                 <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                 <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections,
                 double&nbsp;scaleFactor,
                 int&nbsp;minNeighbors,
                 int&nbsp;flags,
                 <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize,
                 <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;maxSize)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#detectMultiScale3-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-org.opencv.core.MatOfDouble-">detectMultiScale3</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                 <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                 <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
                 <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights)</code>
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#detectMultiScale3-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-org.opencv.core.MatOfDouble-double-">detectMultiScale3</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                 <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                 <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
                 <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
                 double&nbsp;scaleFactor)</code>
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#detectMultiScale3-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-org.opencv.core.MatOfDouble-double-int-">detectMultiScale3</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                 <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                 <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
                 <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
                 double&nbsp;scaleFactor,
                 int&nbsp;minNeighbors)</code>
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#detectMultiScale3-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-org.opencv.core.MatOfDouble-double-int-int-">detectMultiScale3</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                 <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                 <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
                 <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
                 double&nbsp;scaleFactor,
                 int&nbsp;minNeighbors,
                 int&nbsp;flags)</code>
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#detectMultiScale3-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-org.opencv.core.MatOfDouble-double-int-int-org.opencv.core.Size-">detectMultiScale3</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                 <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                 <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
                 <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
                 double&nbsp;scaleFactor,
                 int&nbsp;minNeighbors,
                 int&nbsp;flags,
                 <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize)</code>
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#detectMultiScale3-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-org.opencv.core.MatOfDouble-double-int-int-org.opencv.core.Size-org.opencv.core.Size-">detectMultiScale3</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                 <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                 <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
                 <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
                 double&nbsp;scaleFactor,
                 int&nbsp;minNeighbors,
                 int&nbsp;flags,
                 <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize,
                 <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;maxSize)</code>
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#detectMultiScale3-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-org.opencv.core.MatOfDouble-double-int-int-org.opencv.core.Size-org.opencv.core.Size-boolean-">detectMultiScale3</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                 <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                 <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
                 <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
                 double&nbsp;scaleFactor,
                 int&nbsp;minNeighbors,
                 int&nbsp;flags,
                 <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize,
                 <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;maxSize,
                 boolean&nbsp;outputRejectLevels)</code>
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#empty--">empty</a></span>()</code>
<div class="block">Checks whether the classifier has been loaded.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#getFeatureType--">getFeatureType</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#getNativeObjAddr--">getNativeObjAddr</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#getOriginalWindowSize--">getOriginalWindowSize</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#isOldFormatCascade--">isOldFormatCascade</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#load-java.lang.String-">load</a></span>(java.lang.String&nbsp;filename)</code>
<div class="block">Loads a classifier from a file.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="CascadeClassifier--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CascadeClassifier</h4>
<pre>public&nbsp;CascadeClassifier()</pre>
</li>
</ul>
<a name="CascadeClassifier-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CascadeClassifier</h4>
<pre>public&nbsp;CascadeClassifier(java.lang.String&nbsp;filename)</pre>
<div class="block">Loads a classifier from a file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - Name of the file from which the classifier is loaded.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/objdetect/CascadeClassifier.html" title="class in org.opencv.objdetect">CascadeClassifier</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="convert-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convert</h4>
<pre>public static&nbsp;boolean&nbsp;convert(java.lang.String&nbsp;oldcascade,
                              java.lang.String&nbsp;newcascade)</pre>
</li>
</ul>
<a name="detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                             <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects)</pre>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - Matrix of the type CV_8U containing an image where objects are detected.</dd>
<dd><code>objects</code> - Vector of rectangles where each rectangle contains the detected object, the
     rectangles may be partially outside the original image.
     to retain it.
     cvHaarDetectObjects. It is not used for a new cascade.

     The function is parallelized with the TBB library.

     <b>Note:</b>
 <ul>
   <li>
           (Python) A face detection example using cascade classifiers can be found at
             opencv_source_code/samples/python/facedetect.py
   </li>
 </ul></dd>
</dl>
</li>
</ul>
<a name="detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                             <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                             double&nbsp;scaleFactor)</pre>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - Matrix of the type CV_8U containing an image where objects are detected.</dd>
<dd><code>objects</code> - Vector of rectangles where each rectangle contains the detected object, the
     rectangles may be partially outside the original image.</dd>
<dd><code>scaleFactor</code> - Parameter specifying how much the image size is reduced at each image scale.
     to retain it.
     cvHaarDetectObjects. It is not used for a new cascade.

     The function is parallelized with the TBB library.

     <b>Note:</b>
 <ul>
   <li>
           (Python) A face detection example using cascade classifiers can be found at
             opencv_source_code/samples/python/facedetect.py
   </li>
 </ul></dd>
</dl>
</li>
</ul>
<a name="detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-double-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                             <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                             double&nbsp;scaleFactor,
                             int&nbsp;minNeighbors)</pre>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - Matrix of the type CV_8U containing an image where objects are detected.</dd>
<dd><code>objects</code> - Vector of rectangles where each rectangle contains the detected object, the
     rectangles may be partially outside the original image.</dd>
<dd><code>scaleFactor</code> - Parameter specifying how much the image size is reduced at each image scale.</dd>
<dd><code>minNeighbors</code> - Parameter specifying how many neighbors each candidate rectangle should have
     to retain it.
     cvHaarDetectObjects. It is not used for a new cascade.

     The function is parallelized with the TBB library.

     <b>Note:</b>
 <ul>
   <li>
           (Python) A face detection example using cascade classifiers can be found at
             opencv_source_code/samples/python/facedetect.py
   </li>
 </ul></dd>
</dl>
</li>
</ul>
<a name="detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-double-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                             <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                             double&nbsp;scaleFactor,
                             int&nbsp;minNeighbors,
                             int&nbsp;flags)</pre>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - Matrix of the type CV_8U containing an image where objects are detected.</dd>
<dd><code>objects</code> - Vector of rectangles where each rectangle contains the detected object, the
     rectangles may be partially outside the original image.</dd>
<dd><code>scaleFactor</code> - Parameter specifying how much the image size is reduced at each image scale.</dd>
<dd><code>minNeighbors</code> - Parameter specifying how many neighbors each candidate rectangle should have
     to retain it.</dd>
<dd><code>flags</code> - Parameter with the same meaning for an old cascade as in the function
     cvHaarDetectObjects. It is not used for a new cascade.

     The function is parallelized with the TBB library.

     <b>Note:</b>
 <ul>
   <li>
           (Python) A face detection example using cascade classifiers can be found at
             opencv_source_code/samples/python/facedetect.py
   </li>
 </ul></dd>
</dl>
</li>
</ul>
<a name="detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-double-int-int-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                             <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                             double&nbsp;scaleFactor,
                             int&nbsp;minNeighbors,
                             int&nbsp;flags,
                             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize)</pre>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - Matrix of the type CV_8U containing an image where objects are detected.</dd>
<dd><code>objects</code> - Vector of rectangles where each rectangle contains the detected object, the
     rectangles may be partially outside the original image.</dd>
<dd><code>scaleFactor</code> - Parameter specifying how much the image size is reduced at each image scale.</dd>
<dd><code>minNeighbors</code> - Parameter specifying how many neighbors each candidate rectangle should have
     to retain it.</dd>
<dd><code>flags</code> - Parameter with the same meaning for an old cascade as in the function
     cvHaarDetectObjects. It is not used for a new cascade.</dd>
<dd><code>minSize</code> - Minimum possible object size. Objects smaller than that are ignored.

     The function is parallelized with the TBB library.

     <b>Note:</b>
 <ul>
   <li>
           (Python) A face detection example using cascade classifiers can be found at
             opencv_source_code/samples/python/facedetect.py
   </li>
 </ul></dd>
</dl>
</li>
</ul>
<a name="detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-double-int-int-org.opencv.core.Size-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                             <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                             double&nbsp;scaleFactor,
                             int&nbsp;minNeighbors,
                             int&nbsp;flags,
                             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize,
                             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;maxSize)</pre>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - Matrix of the type CV_8U containing an image where objects are detected.</dd>
<dd><code>objects</code> - Vector of rectangles where each rectangle contains the detected object, the
     rectangles may be partially outside the original image.</dd>
<dd><code>scaleFactor</code> - Parameter specifying how much the image size is reduced at each image scale.</dd>
<dd><code>minNeighbors</code> - Parameter specifying how many neighbors each candidate rectangle should have
     to retain it.</dd>
<dd><code>flags</code> - Parameter with the same meaning for an old cascade as in the function
     cvHaarDetectObjects. It is not used for a new cascade.</dd>
<dd><code>minSize</code> - Minimum possible object size. Objects smaller than that are ignored.</dd>
<dd><code>maxSize</code> - Maximum possible object size. Objects larger than that are ignored. If <code>maxSize == minSize</code> model is evaluated on single scale.

     The function is parallelized with the TBB library.

     <b>Note:</b>
 <ul>
   <li>
           (Python) A face detection example using cascade classifiers can be found at
             opencv_source_code/samples/python/facedetect.py
   </li>
 </ul></dd>
</dl>
</li>
</ul>
<a name="detectMultiScale2-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale2</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale2(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                              <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                              <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - Matrix of the type CV_8U containing an image where objects are detected.</dd>
<dd><code>objects</code> - Vector of rectangles where each rectangle contains the detected object, the
     rectangles may be partially outside the original image.</dd>
<dd><code>numDetections</code> - Vector of detection numbers for the corresponding objects. An object's number
     of detections is the number of neighboring positively classified rectangles that were joined
     together to form the object.
     to retain it.
     cvHaarDetectObjects. It is not used for a new cascade.</dd>
</dl>
</li>
</ul>
<a name="detectMultiScale2-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale2</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale2(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                              <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                              <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections,
                              double&nbsp;scaleFactor)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - Matrix of the type CV_8U containing an image where objects are detected.</dd>
<dd><code>objects</code> - Vector of rectangles where each rectangle contains the detected object, the
     rectangles may be partially outside the original image.</dd>
<dd><code>numDetections</code> - Vector of detection numbers for the corresponding objects. An object's number
     of detections is the number of neighboring positively classified rectangles that were joined
     together to form the object.</dd>
<dd><code>scaleFactor</code> - Parameter specifying how much the image size is reduced at each image scale.
     to retain it.
     cvHaarDetectObjects. It is not used for a new cascade.</dd>
</dl>
</li>
</ul>
<a name="detectMultiScale2-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-double-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale2</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale2(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                              <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                              <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections,
                              double&nbsp;scaleFactor,
                              int&nbsp;minNeighbors)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - Matrix of the type CV_8U containing an image where objects are detected.</dd>
<dd><code>objects</code> - Vector of rectangles where each rectangle contains the detected object, the
     rectangles may be partially outside the original image.</dd>
<dd><code>numDetections</code> - Vector of detection numbers for the corresponding objects. An object's number
     of detections is the number of neighboring positively classified rectangles that were joined
     together to form the object.</dd>
<dd><code>scaleFactor</code> - Parameter specifying how much the image size is reduced at each image scale.</dd>
<dd><code>minNeighbors</code> - Parameter specifying how many neighbors each candidate rectangle should have
     to retain it.
     cvHaarDetectObjects. It is not used for a new cascade.</dd>
</dl>
</li>
</ul>
<a name="detectMultiScale2-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-double-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale2</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale2(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                              <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                              <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections,
                              double&nbsp;scaleFactor,
                              int&nbsp;minNeighbors,
                              int&nbsp;flags)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - Matrix of the type CV_8U containing an image where objects are detected.</dd>
<dd><code>objects</code> - Vector of rectangles where each rectangle contains the detected object, the
     rectangles may be partially outside the original image.</dd>
<dd><code>numDetections</code> - Vector of detection numbers for the corresponding objects. An object's number
     of detections is the number of neighboring positively classified rectangles that were joined
     together to form the object.</dd>
<dd><code>scaleFactor</code> - Parameter specifying how much the image size is reduced at each image scale.</dd>
<dd><code>minNeighbors</code> - Parameter specifying how many neighbors each candidate rectangle should have
     to retain it.</dd>
<dd><code>flags</code> - Parameter with the same meaning for an old cascade as in the function
     cvHaarDetectObjects. It is not used for a new cascade.</dd>
</dl>
</li>
</ul>
<a name="detectMultiScale2-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-double-int-int-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale2</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale2(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                              <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                              <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections,
                              double&nbsp;scaleFactor,
                              int&nbsp;minNeighbors,
                              int&nbsp;flags,
                              <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - Matrix of the type CV_8U containing an image where objects are detected.</dd>
<dd><code>objects</code> - Vector of rectangles where each rectangle contains the detected object, the
     rectangles may be partially outside the original image.</dd>
<dd><code>numDetections</code> - Vector of detection numbers for the corresponding objects. An object's number
     of detections is the number of neighboring positively classified rectangles that were joined
     together to form the object.</dd>
<dd><code>scaleFactor</code> - Parameter specifying how much the image size is reduced at each image scale.</dd>
<dd><code>minNeighbors</code> - Parameter specifying how many neighbors each candidate rectangle should have
     to retain it.</dd>
<dd><code>flags</code> - Parameter with the same meaning for an old cascade as in the function
     cvHaarDetectObjects. It is not used for a new cascade.</dd>
<dd><code>minSize</code> - Minimum possible object size. Objects smaller than that are ignored.</dd>
</dl>
</li>
</ul>
<a name="detectMultiScale2-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-double-int-int-org.opencv.core.Size-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale2</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale2(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                              <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                              <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections,
                              double&nbsp;scaleFactor,
                              int&nbsp;minNeighbors,
                              int&nbsp;flags,
                              <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize,
                              <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;maxSize)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - Matrix of the type CV_8U containing an image where objects are detected.</dd>
<dd><code>objects</code> - Vector of rectangles where each rectangle contains the detected object, the
     rectangles may be partially outside the original image.</dd>
<dd><code>numDetections</code> - Vector of detection numbers for the corresponding objects. An object's number
     of detections is the number of neighboring positively classified rectangles that were joined
     together to form the object.</dd>
<dd><code>scaleFactor</code> - Parameter specifying how much the image size is reduced at each image scale.</dd>
<dd><code>minNeighbors</code> - Parameter specifying how many neighbors each candidate rectangle should have
     to retain it.</dd>
<dd><code>flags</code> - Parameter with the same meaning for an old cascade as in the function
     cvHaarDetectObjects. It is not used for a new cascade.</dd>
<dd><code>minSize</code> - Minimum possible object size. Objects smaller than that are ignored.</dd>
<dd><code>maxSize</code> - Maximum possible object size. Objects larger than that are ignored. If <code>maxSize == minSize</code> model is evaluated on single scale.</dd>
</dl>
</li>
</ul>
<a name="detectMultiScale3-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-org.opencv.core.MatOfDouble-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale3</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale3(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                              <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                              <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
                              <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights)</pre>
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.
     For this, one needs to set <code>outputRejectLevels</code> on true and provide the <code>rejectLevels</code> and <code>levelWeights</code> parameter.
     For each resulting detection, <code>levelWeights</code> will then contain the certainty of classification at the final stage.
     This value can then be used to separate strong from weaker classifications.

     A code sample on how to use it efficiently can be found below:
     <code>
     Mat img;
     vector&lt;double&gt; weights;
     vector&lt;int&gt; levels;
     vector&lt;Rect&gt; detections;
     CascadeClassifier model("/path/to/your/model.xml");
     model.detectMultiScale(img, detections, levels, weights, 1.1, 3, 0, Size(), Size(), true);
     cerr &lt;&lt; "Detection " &lt;&lt; detections[0] &lt;&lt; " with weight " &lt;&lt; weights[0] &lt;&lt; endl;
     </code></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - automatically generated</dd>
<dd><code>objects</code> - automatically generated</dd>
<dd><code>rejectLevels</code> - automatically generated</dd>
<dd><code>levelWeights</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="detectMultiScale3-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-org.opencv.core.MatOfDouble-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale3</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale3(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                              <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                              <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
                              <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
                              double&nbsp;scaleFactor)</pre>
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.
     For this, one needs to set <code>outputRejectLevels</code> on true and provide the <code>rejectLevels</code> and <code>levelWeights</code> parameter.
     For each resulting detection, <code>levelWeights</code> will then contain the certainty of classification at the final stage.
     This value can then be used to separate strong from weaker classifications.

     A code sample on how to use it efficiently can be found below:
     <code>
     Mat img;
     vector&lt;double&gt; weights;
     vector&lt;int&gt; levels;
     vector&lt;Rect&gt; detections;
     CascadeClassifier model("/path/to/your/model.xml");
     model.detectMultiScale(img, detections, levels, weights, 1.1, 3, 0, Size(), Size(), true);
     cerr &lt;&lt; "Detection " &lt;&lt; detections[0] &lt;&lt; " with weight " &lt;&lt; weights[0] &lt;&lt; endl;
     </code></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - automatically generated</dd>
<dd><code>objects</code> - automatically generated</dd>
<dd><code>rejectLevels</code> - automatically generated</dd>
<dd><code>levelWeights</code> - automatically generated</dd>
<dd><code>scaleFactor</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="detectMultiScale3-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-org.opencv.core.MatOfDouble-double-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale3</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale3(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                              <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                              <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
                              <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
                              double&nbsp;scaleFactor,
                              int&nbsp;minNeighbors)</pre>
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.
     For this, one needs to set <code>outputRejectLevels</code> on true and provide the <code>rejectLevels</code> and <code>levelWeights</code> parameter.
     For each resulting detection, <code>levelWeights</code> will then contain the certainty of classification at the final stage.
     This value can then be used to separate strong from weaker classifications.

     A code sample on how to use it efficiently can be found below:
     <code>
     Mat img;
     vector&lt;double&gt; weights;
     vector&lt;int&gt; levels;
     vector&lt;Rect&gt; detections;
     CascadeClassifier model("/path/to/your/model.xml");
     model.detectMultiScale(img, detections, levels, weights, 1.1, 3, 0, Size(), Size(), true);
     cerr &lt;&lt; "Detection " &lt;&lt; detections[0] &lt;&lt; " with weight " &lt;&lt; weights[0] &lt;&lt; endl;
     </code></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - automatically generated</dd>
<dd><code>objects</code> - automatically generated</dd>
<dd><code>rejectLevels</code> - automatically generated</dd>
<dd><code>levelWeights</code> - automatically generated</dd>
<dd><code>scaleFactor</code> - automatically generated</dd>
<dd><code>minNeighbors</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="detectMultiScale3-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-org.opencv.core.MatOfDouble-double-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale3</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale3(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                              <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                              <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
                              <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
                              double&nbsp;scaleFactor,
                              int&nbsp;minNeighbors,
                              int&nbsp;flags)</pre>
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.
     For this, one needs to set <code>outputRejectLevels</code> on true and provide the <code>rejectLevels</code> and <code>levelWeights</code> parameter.
     For each resulting detection, <code>levelWeights</code> will then contain the certainty of classification at the final stage.
     This value can then be used to separate strong from weaker classifications.

     A code sample on how to use it efficiently can be found below:
     <code>
     Mat img;
     vector&lt;double&gt; weights;
     vector&lt;int&gt; levels;
     vector&lt;Rect&gt; detections;
     CascadeClassifier model("/path/to/your/model.xml");
     model.detectMultiScale(img, detections, levels, weights, 1.1, 3, 0, Size(), Size(), true);
     cerr &lt;&lt; "Detection " &lt;&lt; detections[0] &lt;&lt; " with weight " &lt;&lt; weights[0] &lt;&lt; endl;
     </code></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - automatically generated</dd>
<dd><code>objects</code> - automatically generated</dd>
<dd><code>rejectLevels</code> - automatically generated</dd>
<dd><code>levelWeights</code> - automatically generated</dd>
<dd><code>scaleFactor</code> - automatically generated</dd>
<dd><code>minNeighbors</code> - automatically generated</dd>
<dd><code>flags</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="detectMultiScale3-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-org.opencv.core.MatOfDouble-double-int-int-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale3</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale3(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                              <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                              <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
                              <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
                              double&nbsp;scaleFactor,
                              int&nbsp;minNeighbors,
                              int&nbsp;flags,
                              <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize)</pre>
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.
     For this, one needs to set <code>outputRejectLevels</code> on true and provide the <code>rejectLevels</code> and <code>levelWeights</code> parameter.
     For each resulting detection, <code>levelWeights</code> will then contain the certainty of classification at the final stage.
     This value can then be used to separate strong from weaker classifications.

     A code sample on how to use it efficiently can be found below:
     <code>
     Mat img;
     vector&lt;double&gt; weights;
     vector&lt;int&gt; levels;
     vector&lt;Rect&gt; detections;
     CascadeClassifier model("/path/to/your/model.xml");
     model.detectMultiScale(img, detections, levels, weights, 1.1, 3, 0, Size(), Size(), true);
     cerr &lt;&lt; "Detection " &lt;&lt; detections[0] &lt;&lt; " with weight " &lt;&lt; weights[0] &lt;&lt; endl;
     </code></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - automatically generated</dd>
<dd><code>objects</code> - automatically generated</dd>
<dd><code>rejectLevels</code> - automatically generated</dd>
<dd><code>levelWeights</code> - automatically generated</dd>
<dd><code>scaleFactor</code> - automatically generated</dd>
<dd><code>minNeighbors</code> - automatically generated</dd>
<dd><code>flags</code> - automatically generated</dd>
<dd><code>minSize</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="detectMultiScale3-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-org.opencv.core.MatOfDouble-double-int-int-org.opencv.core.Size-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale3</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale3(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                              <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                              <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
                              <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
                              double&nbsp;scaleFactor,
                              int&nbsp;minNeighbors,
                              int&nbsp;flags,
                              <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize,
                              <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;maxSize)</pre>
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.
     For this, one needs to set <code>outputRejectLevels</code> on true and provide the <code>rejectLevels</code> and <code>levelWeights</code> parameter.
     For each resulting detection, <code>levelWeights</code> will then contain the certainty of classification at the final stage.
     This value can then be used to separate strong from weaker classifications.

     A code sample on how to use it efficiently can be found below:
     <code>
     Mat img;
     vector&lt;double&gt; weights;
     vector&lt;int&gt; levels;
     vector&lt;Rect&gt; detections;
     CascadeClassifier model("/path/to/your/model.xml");
     model.detectMultiScale(img, detections, levels, weights, 1.1, 3, 0, Size(), Size(), true);
     cerr &lt;&lt; "Detection " &lt;&lt; detections[0] &lt;&lt; " with weight " &lt;&lt; weights[0] &lt;&lt; endl;
     </code></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - automatically generated</dd>
<dd><code>objects</code> - automatically generated</dd>
<dd><code>rejectLevels</code> - automatically generated</dd>
<dd><code>levelWeights</code> - automatically generated</dd>
<dd><code>scaleFactor</code> - automatically generated</dd>
<dd><code>minNeighbors</code> - automatically generated</dd>
<dd><code>flags</code> - automatically generated</dd>
<dd><code>minSize</code> - automatically generated</dd>
<dd><code>maxSize</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="detectMultiScale3-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-org.opencv.core.MatOfDouble-double-int-int-org.opencv.core.Size-org.opencv.core.Size-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale3</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale3(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                              <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                              <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
                              <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
                              double&nbsp;scaleFactor,
                              int&nbsp;minNeighbors,
                              int&nbsp;flags,
                              <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize,
                              <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;maxSize,
                              boolean&nbsp;outputRejectLevels)</pre>
<div class="block">This function allows you to retrieve the final stage decision certainty of classification.
     For this, one needs to set <code>outputRejectLevels</code> on true and provide the <code>rejectLevels</code> and <code>levelWeights</code> parameter.
     For each resulting detection, <code>levelWeights</code> will then contain the certainty of classification at the final stage.
     This value can then be used to separate strong from weaker classifications.

     A code sample on how to use it efficiently can be found below:
     <code>
     Mat img;
     vector&lt;double&gt; weights;
     vector&lt;int&gt; levels;
     vector&lt;Rect&gt; detections;
     CascadeClassifier model("/path/to/your/model.xml");
     model.detectMultiScale(img, detections, levels, weights, 1.1, 3, 0, Size(), Size(), true);
     cerr &lt;&lt; "Detection " &lt;&lt; detections[0] &lt;&lt; " with weight " &lt;&lt; weights[0] &lt;&lt; endl;
     </code></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - automatically generated</dd>
<dd><code>objects</code> - automatically generated</dd>
<dd><code>rejectLevels</code> - automatically generated</dd>
<dd><code>levelWeights</code> - automatically generated</dd>
<dd><code>scaleFactor</code> - automatically generated</dd>
<dd><code>minNeighbors</code> - automatically generated</dd>
<dd><code>flags</code> - automatically generated</dd>
<dd><code>minSize</code> - automatically generated</dd>
<dd><code>maxSize</code> - automatically generated</dd>
<dd><code>outputRejectLevels</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="empty--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>empty</h4>
<pre>public&nbsp;boolean&nbsp;empty()</pre>
<div class="block">Checks whether the classifier has been loaded.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getFeatureType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFeatureType</h4>
<pre>public&nbsp;int&nbsp;getFeatureType()</pre>
</li>
</ul>
<a name="getNativeObjAddr--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNativeObjAddr</h4>
<pre>public&nbsp;long&nbsp;getNativeObjAddr()</pre>
</li>
</ul>
<a name="getOriginalWindowSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOriginalWindowSize</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;getOriginalWindowSize()</pre>
</li>
</ul>
<a name="isOldFormatCascade--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isOldFormatCascade</h4>
<pre>public&nbsp;boolean&nbsp;isOldFormatCascade()</pre>
</li>
</ul>
<a name="load-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>load</h4>
<pre>public&nbsp;boolean&nbsp;load(java.lang.String&nbsp;filename)</pre>
<div class="block">Loads a classifier from a file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - Name of the file from which the classifier is loaded. The file may contain an old
     HAAR classifier trained by the haartraining application or a new cascade classifier trained by the
     traincascade application.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/objdetect/BaseCascadeClassifier.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/objdetect/FaceDetectorYN.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/CascadeClassifier.html" target="_top">Frames</a></li>
<li><a href="CascadeClassifier.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2021-12-25 08:13:27 / OpenCV 4.5.5</small></p>
</body>
</html>
