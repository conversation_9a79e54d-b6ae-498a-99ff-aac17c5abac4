<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_292) on Sat Dec 25 08:13:28 UTC 2021 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Converters (OpenCV 4.5.5 Java documentation)</title>
<meta name="date" content="2021-12-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Converters (OpenCV 4.5.5 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9,"i16":9,"i17":9,"i18":9,"i19":9,"i20":9,"i21":9,"i22":9,"i23":9,"i24":9,"i25":9,"i26":9,"i27":9,"i28":9,"i29":9,"i30":9,"i31":9,"i32":9,"i33":9,"i34":9,"i35":9,"i36":9,"i37":9,"i38":9,"i39":9,"i40":9,"i41":9,"i42":9,"i43":9,"i44":9,"i45":9,"i46":9,"i47":9,"i48":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/utils/Converters.html" target="_top">Frames</a></li>
<li><a href="Converters.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.utils</div>
<h2 title="Class Converters" class="title">Class Converters</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.utils.Converters</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Converters</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#Converters--">Converters</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#Mat_to_vector_char-org.opencv.core.Mat-java.util.List-">Mat_to_vector_char</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                  java.util.List&lt;java.lang.Byte&gt;&nbsp;bs)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#Mat_to_vector_DMatch-org.opencv.core.Mat-java.util.List-">Mat_to_vector_DMatch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                    java.util.List&lt;<a href="../../../org/opencv/core/DMatch.html" title="class in org.opencv.core">DMatch</a>&gt;&nbsp;matches)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#Mat_to_vector_double-org.opencv.core.Mat-java.util.List-">Mat_to_vector_double</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                    java.util.List&lt;java.lang.Double&gt;&nbsp;ds)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#Mat_to_vector_float-org.opencv.core.Mat-java.util.List-">Mat_to_vector_float</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                   java.util.List&lt;java.lang.Float&gt;&nbsp;fs)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#Mat_to_vector_int-org.opencv.core.Mat-java.util.List-">Mat_to_vector_int</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                 java.util.List&lt;java.lang.Integer&gt;&nbsp;is)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#Mat_to_vector_KeyPoint-org.opencv.core.Mat-java.util.List-">Mat_to_vector_KeyPoint</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                      java.util.List&lt;<a href="../../../org/opencv/core/KeyPoint.html" title="class in org.opencv.core">KeyPoint</a>&gt;&nbsp;kps)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#Mat_to_vector_Mat-org.opencv.core.Mat-java.util.List-">Mat_to_vector_Mat</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                 java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#Mat_to_vector_Point-org.opencv.core.Mat-java.util.List-">Mat_to_vector_Point</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                   java.util.List&lt;<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&gt;&nbsp;pts)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#Mat_to_vector_Point2d-org.opencv.core.Mat-java.util.List-">Mat_to_vector_Point2d</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                     java.util.List&lt;<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&gt;&nbsp;pts)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#Mat_to_vector_Point2f-org.opencv.core.Mat-java.util.List-">Mat_to_vector_Point2f</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                     java.util.List&lt;<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&gt;&nbsp;pts)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#Mat_to_vector_Point3-org.opencv.core.Mat-java.util.List-">Mat_to_vector_Point3</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                    java.util.List&lt;<a href="../../../org/opencv/core/Point3.html" title="class in org.opencv.core">Point3</a>&gt;&nbsp;pts)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#Mat_to_vector_Point3d-org.opencv.core.Mat-java.util.List-">Mat_to_vector_Point3d</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                     java.util.List&lt;<a href="../../../org/opencv/core/Point3.html" title="class in org.opencv.core">Point3</a>&gt;&nbsp;pts)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#Mat_to_vector_Point3f-org.opencv.core.Mat-java.util.List-">Mat_to_vector_Point3f</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                     java.util.List&lt;<a href="../../../org/opencv/core/Point3.html" title="class in org.opencv.core">Point3</a>&gt;&nbsp;pts)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#Mat_to_vector_Point3i-org.opencv.core.Mat-java.util.List-">Mat_to_vector_Point3i</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                     java.util.List&lt;<a href="../../../org/opencv/core/Point3.html" title="class in org.opencv.core">Point3</a>&gt;&nbsp;pts)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#Mat_to_vector_Rect-org.opencv.core.Mat-java.util.List-">Mat_to_vector_Rect</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                  java.util.List&lt;<a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&gt;&nbsp;rs)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#Mat_to_vector_Rect2d-org.opencv.core.Mat-java.util.List-">Mat_to_vector_Rect2d</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                    java.util.List&lt;<a href="../../../org/opencv/core/Rect2d.html" title="class in org.opencv.core">Rect2d</a>&gt;&nbsp;rs)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#Mat_to_vector_RotatedRect-org.opencv.core.Mat-java.util.List-">Mat_to_vector_RotatedRect</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                         java.util.List&lt;<a href="../../../org/opencv/core/RotatedRect.html" title="class in org.opencv.core">RotatedRect</a>&gt;&nbsp;rs)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#Mat_to_vector_uchar-org.opencv.core.Mat-java.util.List-">Mat_to_vector_uchar</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                   java.util.List&lt;java.lang.Byte&gt;&nbsp;us)</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#Mat_to_vector_vector_char-org.opencv.core.Mat-java.util.List-">Mat_to_vector_vector_char</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                         java.util.List&lt;java.util.List&lt;java.lang.Byte&gt;&gt;&nbsp;llb)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#Mat_to_vector_vector_DMatch-org.opencv.core.Mat-java.util.List-">Mat_to_vector_vector_DMatch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                           java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;lvdm)</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#Mat_to_vector_vector_KeyPoint-org.opencv.core.Mat-java.util.List-">Mat_to_vector_vector_KeyPoint</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                             java.util.List&lt;<a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&gt;&nbsp;kps)</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#Mat_to_vector_vector_Point-org.opencv.core.Mat-java.util.List-">Mat_to_vector_vector_Point</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                          java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&gt;&nbsp;pts)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#Mat_to_vector_vector_Point2f-org.opencv.core.Mat-java.util.List-">Mat_to_vector_vector_Point2f</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                            java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&gt;&nbsp;pts)</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#Mat_to_vector_vector_Point3f-org.opencv.core.Mat-java.util.List-">Mat_to_vector_vector_Point3f</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                            java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&gt;&nbsp;pts)</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#vector_char_to_Mat-java.util.List-">vector_char_to_Mat</a></span>(java.util.List&lt;java.lang.Byte&gt;&nbsp;bs)</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#vector_DMatch_to_Mat-java.util.List-">vector_DMatch_to_Mat</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/DMatch.html" title="class in org.opencv.core">DMatch</a>&gt;&nbsp;matches)</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#vector_double_to_Mat-java.util.List-">vector_double_to_Mat</a></span>(java.util.List&lt;java.lang.Double&gt;&nbsp;ds)</code>&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#vector_float_to_Mat-java.util.List-">vector_float_to_Mat</a></span>(java.util.List&lt;java.lang.Float&gt;&nbsp;fs)</code>&nbsp;</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#vector_int_to_Mat-java.util.List-">vector_int_to_Mat</a></span>(java.util.List&lt;java.lang.Integer&gt;&nbsp;is)</code>&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#vector_KeyPoint_to_Mat-java.util.List-">vector_KeyPoint_to_Mat</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/KeyPoint.html" title="class in org.opencv.core">KeyPoint</a>&gt;&nbsp;kps)</code>&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#vector_Mat_to_Mat-java.util.List-">vector_Mat_to_Mat</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</code>&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#vector_Point_to_Mat-java.util.List-">vector_Point_to_Mat</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&gt;&nbsp;pts)</code>&nbsp;</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#vector_Point_to_Mat-java.util.List-int-">vector_Point_to_Mat</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&gt;&nbsp;pts,
                   int&nbsp;typeDepth)</code>&nbsp;</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#vector_Point2d_to_Mat-java.util.List-">vector_Point2d_to_Mat</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&gt;&nbsp;pts)</code>&nbsp;</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#vector_Point2f_to_Mat-java.util.List-">vector_Point2f_to_Mat</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&gt;&nbsp;pts)</code>&nbsp;</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#vector_Point3_to_Mat-java.util.List-int-">vector_Point3_to_Mat</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Point3.html" title="class in org.opencv.core">Point3</a>&gt;&nbsp;pts,
                    int&nbsp;typeDepth)</code>&nbsp;</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#vector_Point3d_to_Mat-java.util.List-">vector_Point3d_to_Mat</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Point3.html" title="class in org.opencv.core">Point3</a>&gt;&nbsp;pts)</code>&nbsp;</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#vector_Point3f_to_Mat-java.util.List-">vector_Point3f_to_Mat</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Point3.html" title="class in org.opencv.core">Point3</a>&gt;&nbsp;pts)</code>&nbsp;</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#vector_Point3i_to_Mat-java.util.List-">vector_Point3i_to_Mat</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Point3.html" title="class in org.opencv.core">Point3</a>&gt;&nbsp;pts)</code>&nbsp;</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#vector_Rect_to_Mat-java.util.List-">vector_Rect_to_Mat</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&gt;&nbsp;rs)</code>&nbsp;</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#vector_Rect2d_to_Mat-java.util.List-">vector_Rect2d_to_Mat</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Rect2d.html" title="class in org.opencv.core">Rect2d</a>&gt;&nbsp;rs)</code>&nbsp;</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#vector_RotatedRect_to_Mat-java.util.List-">vector_RotatedRect_to_Mat</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/RotatedRect.html" title="class in org.opencv.core">RotatedRect</a>&gt;&nbsp;rs)</code>&nbsp;</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#vector_uchar_to_Mat-java.util.List-">vector_uchar_to_Mat</a></span>(java.util.List&lt;java.lang.Byte&gt;&nbsp;bs)</code>&nbsp;</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#vector_vector_char_to_Mat-java.util.List-java.util.List-">vector_vector_char_to_Mat</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&gt;&nbsp;lvb,
                         java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</code>&nbsp;</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#vector_vector_DMatch_to_Mat-java.util.List-java.util.List-">vector_vector_DMatch_to_Mat</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;lvdm,
                           java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</code>&nbsp;</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#vector_vector_KeyPoint_to_Mat-java.util.List-java.util.List-">vector_vector_KeyPoint_to_Mat</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&gt;&nbsp;kps,
                             java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</code>&nbsp;</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#vector_vector_Point_to_Mat-java.util.List-java.util.List-">vector_vector_Point_to_Mat</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&gt;&nbsp;pts,
                          java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</code>&nbsp;</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#vector_vector_Point2f_to_Mat-java.util.List-java.util.List-">vector_vector_Point2f_to_Mat</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&gt;&nbsp;pts,
                            java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</code>&nbsp;</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/utils/Converters.html#vector_vector_Point3f_to_Mat-java.util.List-java.util.List-">vector_vector_Point3f_to_Mat</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&gt;&nbsp;pts,
                            java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Converters--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Converters</h4>
<pre>public&nbsp;Converters()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Mat_to_vector_char-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat_to_vector_char</h4>
<pre>public static&nbsp;void&nbsp;Mat_to_vector_char(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                                      java.util.List&lt;java.lang.Byte&gt;&nbsp;bs)</pre>
</li>
</ul>
<a name="Mat_to_vector_DMatch-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat_to_vector_DMatch</h4>
<pre>public static&nbsp;void&nbsp;Mat_to_vector_DMatch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                                        java.util.List&lt;<a href="../../../org/opencv/core/DMatch.html" title="class in org.opencv.core">DMatch</a>&gt;&nbsp;matches)</pre>
</li>
</ul>
<a name="Mat_to_vector_double-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat_to_vector_double</h4>
<pre>public static&nbsp;void&nbsp;Mat_to_vector_double(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                                        java.util.List&lt;java.lang.Double&gt;&nbsp;ds)</pre>
</li>
</ul>
<a name="Mat_to_vector_float-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat_to_vector_float</h4>
<pre>public static&nbsp;void&nbsp;Mat_to_vector_float(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                                       java.util.List&lt;java.lang.Float&gt;&nbsp;fs)</pre>
</li>
</ul>
<a name="Mat_to_vector_int-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat_to_vector_int</h4>
<pre>public static&nbsp;void&nbsp;Mat_to_vector_int(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                                     java.util.List&lt;java.lang.Integer&gt;&nbsp;is)</pre>
</li>
</ul>
<a name="Mat_to_vector_KeyPoint-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat_to_vector_KeyPoint</h4>
<pre>public static&nbsp;void&nbsp;Mat_to_vector_KeyPoint(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                                          java.util.List&lt;<a href="../../../org/opencv/core/KeyPoint.html" title="class in org.opencv.core">KeyPoint</a>&gt;&nbsp;kps)</pre>
</li>
</ul>
<a name="Mat_to_vector_Mat-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat_to_vector_Mat</h4>
<pre>public static&nbsp;void&nbsp;Mat_to_vector_Mat(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</pre>
</li>
</ul>
<a name="Mat_to_vector_Point-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat_to_vector_Point</h4>
<pre>public static&nbsp;void&nbsp;Mat_to_vector_Point(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                                       java.util.List&lt;<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&gt;&nbsp;pts)</pre>
</li>
</ul>
<a name="Mat_to_vector_Point2d-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat_to_vector_Point2d</h4>
<pre>public static&nbsp;void&nbsp;Mat_to_vector_Point2d(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                                         java.util.List&lt;<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&gt;&nbsp;pts)</pre>
</li>
</ul>
<a name="Mat_to_vector_Point2f-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat_to_vector_Point2f</h4>
<pre>public static&nbsp;void&nbsp;Mat_to_vector_Point2f(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                                         java.util.List&lt;<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&gt;&nbsp;pts)</pre>
</li>
</ul>
<a name="Mat_to_vector_Point3-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat_to_vector_Point3</h4>
<pre>public static&nbsp;void&nbsp;Mat_to_vector_Point3(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                                        java.util.List&lt;<a href="../../../org/opencv/core/Point3.html" title="class in org.opencv.core">Point3</a>&gt;&nbsp;pts)</pre>
</li>
</ul>
<a name="Mat_to_vector_Point3d-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat_to_vector_Point3d</h4>
<pre>public static&nbsp;void&nbsp;Mat_to_vector_Point3d(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                                         java.util.List&lt;<a href="../../../org/opencv/core/Point3.html" title="class in org.opencv.core">Point3</a>&gt;&nbsp;pts)</pre>
</li>
</ul>
<a name="Mat_to_vector_Point3f-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat_to_vector_Point3f</h4>
<pre>public static&nbsp;void&nbsp;Mat_to_vector_Point3f(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                                         java.util.List&lt;<a href="../../../org/opencv/core/Point3.html" title="class in org.opencv.core">Point3</a>&gt;&nbsp;pts)</pre>
</li>
</ul>
<a name="Mat_to_vector_Point3i-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat_to_vector_Point3i</h4>
<pre>public static&nbsp;void&nbsp;Mat_to_vector_Point3i(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                                         java.util.List&lt;<a href="../../../org/opencv/core/Point3.html" title="class in org.opencv.core">Point3</a>&gt;&nbsp;pts)</pre>
</li>
</ul>
<a name="Mat_to_vector_Rect-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat_to_vector_Rect</h4>
<pre>public static&nbsp;void&nbsp;Mat_to_vector_Rect(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                                      java.util.List&lt;<a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&gt;&nbsp;rs)</pre>
</li>
</ul>
<a name="Mat_to_vector_Rect2d-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat_to_vector_Rect2d</h4>
<pre>public static&nbsp;void&nbsp;Mat_to_vector_Rect2d(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                                        java.util.List&lt;<a href="../../../org/opencv/core/Rect2d.html" title="class in org.opencv.core">Rect2d</a>&gt;&nbsp;rs)</pre>
</li>
</ul>
<a name="Mat_to_vector_RotatedRect-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat_to_vector_RotatedRect</h4>
<pre>public static&nbsp;void&nbsp;Mat_to_vector_RotatedRect(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                                             java.util.List&lt;<a href="../../../org/opencv/core/RotatedRect.html" title="class in org.opencv.core">RotatedRect</a>&gt;&nbsp;rs)</pre>
</li>
</ul>
<a name="Mat_to_vector_uchar-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat_to_vector_uchar</h4>
<pre>public static&nbsp;void&nbsp;Mat_to_vector_uchar(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                                       java.util.List&lt;java.lang.Byte&gt;&nbsp;us)</pre>
</li>
</ul>
<a name="Mat_to_vector_vector_char-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat_to_vector_vector_char</h4>
<pre>public static&nbsp;void&nbsp;Mat_to_vector_vector_char(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                                             java.util.List&lt;java.util.List&lt;java.lang.Byte&gt;&gt;&nbsp;llb)</pre>
</li>
</ul>
<a name="Mat_to_vector_vector_DMatch-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat_to_vector_vector_DMatch</h4>
<pre>public static&nbsp;void&nbsp;Mat_to_vector_vector_DMatch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                                               java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;lvdm)</pre>
</li>
</ul>
<a name="Mat_to_vector_vector_KeyPoint-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat_to_vector_vector_KeyPoint</h4>
<pre>public static&nbsp;void&nbsp;Mat_to_vector_vector_KeyPoint(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                                                 java.util.List&lt;<a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&gt;&nbsp;kps)</pre>
</li>
</ul>
<a name="Mat_to_vector_vector_Point-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat_to_vector_vector_Point</h4>
<pre>public static&nbsp;void&nbsp;Mat_to_vector_vector_Point(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                                              java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&gt;&nbsp;pts)</pre>
</li>
</ul>
<a name="Mat_to_vector_vector_Point2f-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat_to_vector_vector_Point2f</h4>
<pre>public static&nbsp;void&nbsp;Mat_to_vector_vector_Point2f(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                                                java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&gt;&nbsp;pts)</pre>
</li>
</ul>
<a name="Mat_to_vector_vector_Point3f-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat_to_vector_vector_Point3f</h4>
<pre>public static&nbsp;void&nbsp;Mat_to_vector_vector_Point3f(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                                                java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&gt;&nbsp;pts)</pre>
</li>
</ul>
<a name="vector_char_to_Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>vector_char_to_Mat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vector_char_to_Mat(java.util.List&lt;java.lang.Byte&gt;&nbsp;bs)</pre>
</li>
</ul>
<a name="vector_DMatch_to_Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>vector_DMatch_to_Mat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vector_DMatch_to_Mat(java.util.List&lt;<a href="../../../org/opencv/core/DMatch.html" title="class in org.opencv.core">DMatch</a>&gt;&nbsp;matches)</pre>
</li>
</ul>
<a name="vector_double_to_Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>vector_double_to_Mat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vector_double_to_Mat(java.util.List&lt;java.lang.Double&gt;&nbsp;ds)</pre>
</li>
</ul>
<a name="vector_float_to_Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>vector_float_to_Mat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vector_float_to_Mat(java.util.List&lt;java.lang.Float&gt;&nbsp;fs)</pre>
</li>
</ul>
<a name="vector_int_to_Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>vector_int_to_Mat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vector_int_to_Mat(java.util.List&lt;java.lang.Integer&gt;&nbsp;is)</pre>
</li>
</ul>
<a name="vector_KeyPoint_to_Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>vector_KeyPoint_to_Mat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vector_KeyPoint_to_Mat(java.util.List&lt;<a href="../../../org/opencv/core/KeyPoint.html" title="class in org.opencv.core">KeyPoint</a>&gt;&nbsp;kps)</pre>
</li>
</ul>
<a name="vector_Mat_to_Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>vector_Mat_to_Mat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vector_Mat_to_Mat(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</pre>
</li>
</ul>
<a name="vector_Point_to_Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>vector_Point_to_Mat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vector_Point_to_Mat(java.util.List&lt;<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&gt;&nbsp;pts)</pre>
</li>
</ul>
<a name="vector_Point_to_Mat-java.util.List-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>vector_Point_to_Mat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vector_Point_to_Mat(java.util.List&lt;<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&gt;&nbsp;pts,
                                      int&nbsp;typeDepth)</pre>
</li>
</ul>
<a name="vector_Point2d_to_Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>vector_Point2d_to_Mat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vector_Point2d_to_Mat(java.util.List&lt;<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&gt;&nbsp;pts)</pre>
</li>
</ul>
<a name="vector_Point2f_to_Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>vector_Point2f_to_Mat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vector_Point2f_to_Mat(java.util.List&lt;<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&gt;&nbsp;pts)</pre>
</li>
</ul>
<a name="vector_Point3_to_Mat-java.util.List-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>vector_Point3_to_Mat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vector_Point3_to_Mat(java.util.List&lt;<a href="../../../org/opencv/core/Point3.html" title="class in org.opencv.core">Point3</a>&gt;&nbsp;pts,
                                       int&nbsp;typeDepth)</pre>
</li>
</ul>
<a name="vector_Point3d_to_Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>vector_Point3d_to_Mat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vector_Point3d_to_Mat(java.util.List&lt;<a href="../../../org/opencv/core/Point3.html" title="class in org.opencv.core">Point3</a>&gt;&nbsp;pts)</pre>
</li>
</ul>
<a name="vector_Point3f_to_Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>vector_Point3f_to_Mat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vector_Point3f_to_Mat(java.util.List&lt;<a href="../../../org/opencv/core/Point3.html" title="class in org.opencv.core">Point3</a>&gt;&nbsp;pts)</pre>
</li>
</ul>
<a name="vector_Point3i_to_Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>vector_Point3i_to_Mat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vector_Point3i_to_Mat(java.util.List&lt;<a href="../../../org/opencv/core/Point3.html" title="class in org.opencv.core">Point3</a>&gt;&nbsp;pts)</pre>
</li>
</ul>
<a name="vector_Rect_to_Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>vector_Rect_to_Mat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vector_Rect_to_Mat(java.util.List&lt;<a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&gt;&nbsp;rs)</pre>
</li>
</ul>
<a name="vector_Rect2d_to_Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>vector_Rect2d_to_Mat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vector_Rect2d_to_Mat(java.util.List&lt;<a href="../../../org/opencv/core/Rect2d.html" title="class in org.opencv.core">Rect2d</a>&gt;&nbsp;rs)</pre>
</li>
</ul>
<a name="vector_RotatedRect_to_Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>vector_RotatedRect_to_Mat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vector_RotatedRect_to_Mat(java.util.List&lt;<a href="../../../org/opencv/core/RotatedRect.html" title="class in org.opencv.core">RotatedRect</a>&gt;&nbsp;rs)</pre>
</li>
</ul>
<a name="vector_uchar_to_Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>vector_uchar_to_Mat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vector_uchar_to_Mat(java.util.List&lt;java.lang.Byte&gt;&nbsp;bs)</pre>
</li>
</ul>
<a name="vector_vector_char_to_Mat-java.util.List-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>vector_vector_char_to_Mat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vector_vector_char_to_Mat(java.util.List&lt;<a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&gt;&nbsp;lvb,
                                            java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</pre>
</li>
</ul>
<a name="vector_vector_DMatch_to_Mat-java.util.List-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>vector_vector_DMatch_to_Mat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vector_vector_DMatch_to_Mat(java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;lvdm,
                                              java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</pre>
</li>
</ul>
<a name="vector_vector_KeyPoint_to_Mat-java.util.List-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>vector_vector_KeyPoint_to_Mat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vector_vector_KeyPoint_to_Mat(java.util.List&lt;<a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&gt;&nbsp;kps,
                                                java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</pre>
</li>
</ul>
<a name="vector_vector_Point_to_Mat-java.util.List-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>vector_vector_Point_to_Mat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vector_vector_Point_to_Mat(java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&gt;&nbsp;pts,
                                             java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</pre>
</li>
</ul>
<a name="vector_vector_Point2f_to_Mat-java.util.List-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>vector_vector_Point2f_to_Mat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vector_vector_Point2f_to_Mat(java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&gt;&nbsp;pts,
                                               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</pre>
</li>
</ul>
<a name="vector_vector_Point3f_to_Mat-java.util.List-java.util.List-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>vector_vector_Point3f_to_Mat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vector_vector_Point3f_to_Mat(java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&gt;&nbsp;pts,
                                               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/utils/Converters.html" target="_top">Frames</a></li>
<li><a href="Converters.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2021-12-25 08:13:27 / OpenCV 4.5.5</small></p>
</body>
</html>
