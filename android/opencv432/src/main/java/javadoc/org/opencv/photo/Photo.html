<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_292) on Sat Dec 25 08:13:29 UTC 2021 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Photo (OpenCV 4.5.5 Java documentation)</title>
<meta name="date" content="2021-12-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Photo (OpenCV 4.5.5 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9,"i16":9,"i17":9,"i18":9,"i19":9,"i20":9,"i21":9,"i22":9,"i23":9,"i24":9,"i25":9,"i26":9,"i27":9,"i28":9,"i29":9,"i30":9,"i31":9,"i32":9,"i33":9,"i34":9,"i35":9,"i36":9,"i37":9,"i38":9,"i39":9,"i40":9,"i41":9,"i42":9,"i43":9,"i44":9,"i45":9,"i46":9,"i47":9,"i48":9,"i49":9,"i50":9,"i51":9,"i52":9,"i53":9,"i54":9,"i55":9,"i56":9,"i57":9,"i58":9,"i59":9,"i60":9,"i61":9,"i62":9,"i63":9,"i64":9,"i65":9,"i66":9,"i67":9,"i68":9,"i69":9,"i70":9,"i71":9,"i72":9,"i73":9,"i74":9,"i75":9,"i76":9,"i77":9,"i78":9,"i79":9,"i80":9,"i81":9,"i82":9,"i83":9,"i84":9,"i85":9,"i86":9,"i87":9,"i88":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/photo/MergeRobertson.html" title="class in org.opencv.photo"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/photo/Tonemap.html" title="class in org.opencv.photo"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/photo/Photo.html" target="_top">Frames</a></li>
<li><a href="Photo.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.photo</div>
<h2 title="Class Photo" class="title">Class Photo</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.photo.Photo</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Photo</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#INPAINT_NS">INPAINT_NS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#INPAINT_TELEA">INPAINT_TELEA</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#LDR_SIZE">LDR_SIZE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#MIXED_CLONE">MIXED_CLONE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#MONOCHROME_TRANSFER">MONOCHROME_TRANSFER</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#NORMAL_CLONE">NORMAL_CLONE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#NORMCONV_FILTER">NORMCONV_FILTER</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#RECURS_FILTER">RECURS_FILTER</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#Photo--">Photo</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#colorChange-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">colorChange</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>
<div class="block">Given an original color image, two differently colored versions of this image can be mixed
 seamlessly.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#colorChange-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-">colorChange</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
           float&nbsp;red_mul)</code>
<div class="block">Given an original color image, two differently colored versions of this image can be mixed
 seamlessly.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#colorChange-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-float-">colorChange</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
           float&nbsp;red_mul,
           float&nbsp;green_mul)</code>
<div class="block">Given an original color image, two differently colored versions of this image can be mixed
 seamlessly.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#colorChange-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-float-float-">colorChange</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
           float&nbsp;red_mul,
           float&nbsp;green_mul,
           float&nbsp;blue_mul)</code>
<div class="block">Given an original color image, two differently colored versions of this image can be mixed
 seamlessly.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/AlignMTB.html" title="class in org.opencv.photo">AlignMTB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createAlignMTB--">createAlignMTB</a></span>()</code>
<div class="block">Creates AlignMTB object

 usually good enough (31 and 63 pixels shift respectively).</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/AlignMTB.html" title="class in org.opencv.photo">AlignMTB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createAlignMTB-int-">createAlignMTB</a></span>(int&nbsp;max_bits)</code>
<div class="block">Creates AlignMTB object</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/AlignMTB.html" title="class in org.opencv.photo">AlignMTB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createAlignMTB-int-int-">createAlignMTB</a></span>(int&nbsp;max_bits,
              int&nbsp;exclude_range)</code>
<div class="block">Creates AlignMTB object</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/AlignMTB.html" title="class in org.opencv.photo">AlignMTB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createAlignMTB-int-int-boolean-">createAlignMTB</a></span>(int&nbsp;max_bits,
              int&nbsp;exclude_range,
              boolean&nbsp;cut)</code>
<div class="block">Creates AlignMTB object</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/CalibrateDebevec.html" title="class in org.opencv.photo">CalibrateDebevec</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createCalibrateDebevec--">createCalibrateDebevec</a></span>()</code>
<div class="block">Creates CalibrateDebevec object

 response.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/CalibrateDebevec.html" title="class in org.opencv.photo">CalibrateDebevec</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createCalibrateDebevec-int-">createCalibrateDebevec</a></span>(int&nbsp;samples)</code>
<div class="block">Creates CalibrateDebevec object</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/CalibrateDebevec.html" title="class in org.opencv.photo">CalibrateDebevec</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createCalibrateDebevec-int-float-">createCalibrateDebevec</a></span>(int&nbsp;samples,
                      float&nbsp;lambda)</code>
<div class="block">Creates CalibrateDebevec object</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/CalibrateDebevec.html" title="class in org.opencv.photo">CalibrateDebevec</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createCalibrateDebevec-int-float-boolean-">createCalibrateDebevec</a></span>(int&nbsp;samples,
                      float&nbsp;lambda,
                      boolean&nbsp;random)</code>
<div class="block">Creates CalibrateDebevec object</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/CalibrateRobertson.html" title="class in org.opencv.photo">CalibrateRobertson</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createCalibrateRobertson--">createCalibrateRobertson</a></span>()</code>
<div class="block">Creates CalibrateRobertson object</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/CalibrateRobertson.html" title="class in org.opencv.photo">CalibrateRobertson</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createCalibrateRobertson-int-">createCalibrateRobertson</a></span>(int&nbsp;max_iter)</code>
<div class="block">Creates CalibrateRobertson object</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/CalibrateRobertson.html" title="class in org.opencv.photo">CalibrateRobertson</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createCalibrateRobertson-int-float-">createCalibrateRobertson</a></span>(int&nbsp;max_iter,
                        float&nbsp;threshold)</code>
<div class="block">Creates CalibrateRobertson object</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/MergeDebevec.html" title="class in org.opencv.photo">MergeDebevec</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createMergeDebevec--">createMergeDebevec</a></span>()</code>
<div class="block">Creates MergeDebevec object</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/MergeMertens.html" title="class in org.opencv.photo">MergeMertens</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createMergeMertens--">createMergeMertens</a></span>()</code>
<div class="block">Creates MergeMertens object</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/MergeMertens.html" title="class in org.opencv.photo">MergeMertens</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createMergeMertens-float-">createMergeMertens</a></span>(float&nbsp;contrast_weight)</code>
<div class="block">Creates MergeMertens object</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/MergeMertens.html" title="class in org.opencv.photo">MergeMertens</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createMergeMertens-float-float-">createMergeMertens</a></span>(float&nbsp;contrast_weight,
                  float&nbsp;saturation_weight)</code>
<div class="block">Creates MergeMertens object</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/MergeMertens.html" title="class in org.opencv.photo">MergeMertens</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createMergeMertens-float-float-float-">createMergeMertens</a></span>(float&nbsp;contrast_weight,
                  float&nbsp;saturation_weight,
                  float&nbsp;exposure_weight)</code>
<div class="block">Creates MergeMertens object</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/MergeRobertson.html" title="class in org.opencv.photo">MergeRobertson</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createMergeRobertson--">createMergeRobertson</a></span>()</code>
<div class="block">Creates MergeRobertson object</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/Tonemap.html" title="class in org.opencv.photo">Tonemap</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createTonemap--">createTonemap</a></span>()</code>
<div class="block">Creates simple linear mapper with gamma correction

 equal to 2.2f is suitable for most displays.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/Tonemap.html" title="class in org.opencv.photo">Tonemap</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createTonemap-float-">createTonemap</a></span>(float&nbsp;gamma)</code>
<div class="block">Creates simple linear mapper with gamma correction</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/TonemapDrago.html" title="class in org.opencv.photo">TonemapDrago</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createTonemapDrago--">createTonemapDrago</a></span>()</code>
<div class="block">Creates TonemapDrago object

 than 1 increase saturation and values less than 1 decrease it.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/TonemapDrago.html" title="class in org.opencv.photo">TonemapDrago</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createTonemapDrago-float-">createTonemapDrago</a></span>(float&nbsp;gamma)</code>
<div class="block">Creates TonemapDrago object</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/TonemapDrago.html" title="class in org.opencv.photo">TonemapDrago</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createTonemapDrago-float-float-">createTonemapDrago</a></span>(float&nbsp;gamma,
                  float&nbsp;saturation)</code>
<div class="block">Creates TonemapDrago object</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/TonemapDrago.html" title="class in org.opencv.photo">TonemapDrago</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createTonemapDrago-float-float-float-">createTonemapDrago</a></span>(float&nbsp;gamma,
                  float&nbsp;saturation,
                  float&nbsp;bias)</code>
<div class="block">Creates TonemapDrago object</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/TonemapMantiuk.html" title="class in org.opencv.photo">TonemapMantiuk</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createTonemapMantiuk--">createTonemapMantiuk</a></span>()</code>
<div class="block">Creates TonemapMantiuk object

 dynamic range.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/TonemapMantiuk.html" title="class in org.opencv.photo">TonemapMantiuk</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createTonemapMantiuk-float-">createTonemapMantiuk</a></span>(float&nbsp;gamma)</code>
<div class="block">Creates TonemapMantiuk object</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/TonemapMantiuk.html" title="class in org.opencv.photo">TonemapMantiuk</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createTonemapMantiuk-float-float-">createTonemapMantiuk</a></span>(float&nbsp;gamma,
                    float&nbsp;scale)</code>
<div class="block">Creates TonemapMantiuk object</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/TonemapMantiuk.html" title="class in org.opencv.photo">TonemapMantiuk</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createTonemapMantiuk-float-float-float-">createTonemapMantiuk</a></span>(float&nbsp;gamma,
                    float&nbsp;scale,
                    float&nbsp;saturation)</code>
<div class="block">Creates TonemapMantiuk object</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/TonemapReinhard.html" title="class in org.opencv.photo">TonemapReinhard</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createTonemapReinhard--">createTonemapReinhard</a></span>()</code>
<div class="block">Creates TonemapReinhard object

 value, if 0 it's global, otherwise it's a weighted mean of this two cases.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/TonemapReinhard.html" title="class in org.opencv.photo">TonemapReinhard</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createTonemapReinhard-float-">createTonemapReinhard</a></span>(float&nbsp;gamma)</code>
<div class="block">Creates TonemapReinhard object</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/TonemapReinhard.html" title="class in org.opencv.photo">TonemapReinhard</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createTonemapReinhard-float-float-">createTonemapReinhard</a></span>(float&nbsp;gamma,
                     float&nbsp;intensity)</code>
<div class="block">Creates TonemapReinhard object</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/TonemapReinhard.html" title="class in org.opencv.photo">TonemapReinhard</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createTonemapReinhard-float-float-float-">createTonemapReinhard</a></span>(float&nbsp;gamma,
                     float&nbsp;intensity,
                     float&nbsp;light_adapt)</code>
<div class="block">Creates TonemapReinhard object</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/TonemapReinhard.html" title="class in org.opencv.photo">TonemapReinhard</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createTonemapReinhard-float-float-float-float-">createTonemapReinhard</a></span>(float&nbsp;gamma,
                     float&nbsp;intensity,
                     float&nbsp;light_adapt,
                     float&nbsp;color_adapt)</code>
<div class="block">Creates TonemapReinhard object</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#decolor-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">decolor</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;grayscale,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;color_boost)</code>
<div class="block">Transforms a color image to a grayscale image.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#denoise_TVL1-java.util.List-org.opencv.core.Mat-">denoise_TVL1</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;observations,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;result)</code>
<div class="block">Primal-dual algorithm is an algorithm for solving special types of variational problems (that is,
 finding a function to minimize some functional).</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#denoise_TVL1-java.util.List-org.opencv.core.Mat-double-">denoise_TVL1</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;observations,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;result,
            double&nbsp;lambda)</code>
<div class="block">Primal-dual algorithm is an algorithm for solving special types of variational problems (that is,
 finding a function to minimize some functional).</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#denoise_TVL1-java.util.List-org.opencv.core.Mat-double-int-">denoise_TVL1</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;observations,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;result,
            double&nbsp;lambda,
            int&nbsp;niters)</code>
<div class="block">Primal-dual algorithm is an algorithm for solving special types of variational problems (that is,
 finding a function to minimize some functional).</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#detailEnhance-org.opencv.core.Mat-org.opencv.core.Mat-">detailEnhance</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>
<div class="block">This filter enhances the details of a particular image.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#detailEnhance-org.opencv.core.Mat-org.opencv.core.Mat-float-">detailEnhance</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
             float&nbsp;sigma_s)</code>
<div class="block">This filter enhances the details of a particular image.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#detailEnhance-org.opencv.core.Mat-org.opencv.core.Mat-float-float-">detailEnhance</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
             float&nbsp;sigma_s,
             float&nbsp;sigma_r)</code>
<div class="block">This filter enhances the details of a particular image.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#edgePreservingFilter-org.opencv.core.Mat-org.opencv.core.Mat-">edgePreservingFilter</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>
<div class="block">Filtering is the fundamental operation in image and video processing.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#edgePreservingFilter-org.opencv.core.Mat-org.opencv.core.Mat-int-">edgePreservingFilter</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                    int&nbsp;flags)</code>
<div class="block">Filtering is the fundamental operation in image and video processing.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#edgePreservingFilter-org.opencv.core.Mat-org.opencv.core.Mat-int-float-">edgePreservingFilter</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                    int&nbsp;flags,
                    float&nbsp;sigma_s)</code>
<div class="block">Filtering is the fundamental operation in image and video processing.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#edgePreservingFilter-org.opencv.core.Mat-org.opencv.core.Mat-int-float-float-">edgePreservingFilter</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                    int&nbsp;flags,
                    float&nbsp;sigma_s,
                    float&nbsp;sigma_r)</code>
<div class="block">Filtering is the fundamental operation in image and video processing.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoising-org.opencv.core.Mat-org.opencv.core.Mat-">fastNlMeansDenoising</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoising-org.opencv.core.Mat-org.opencv.core.Mat-float-">fastNlMeansDenoising</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                    float&nbsp;h)</code>
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoising-org.opencv.core.Mat-org.opencv.core.Mat-float-int-">fastNlMeansDenoising</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                    float&nbsp;h,
                    int&nbsp;templateWindowSize)</code>
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoising-org.opencv.core.Mat-org.opencv.core.Mat-float-int-int-">fastNlMeansDenoising</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                    float&nbsp;h,
                    int&nbsp;templateWindowSize,
                    int&nbsp;searchWindowSize)</code>
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoising-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfFloat-">fastNlMeansDenoising</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                    <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h)</code>
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoising-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfFloat-int-">fastNlMeansDenoising</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                    <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
                    int&nbsp;templateWindowSize)</code>
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoising-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfFloat-int-int-">fastNlMeansDenoising</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                    <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
                    int&nbsp;templateWindowSize,
                    int&nbsp;searchWindowSize)</code>
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoising-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfFloat-int-int-int-">fastNlMeansDenoising</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                    <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
                    int&nbsp;templateWindowSize,
                    int&nbsp;searchWindowSize,
                    int&nbsp;normType)</code>
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingColored-org.opencv.core.Mat-org.opencv.core.Mat-">fastNlMeansDenoisingColored</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>
<div class="block">Modification of fastNlMeansDenoising function for colored images</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingColored-org.opencv.core.Mat-org.opencv.core.Mat-float-">fastNlMeansDenoisingColored</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                           float&nbsp;h)</code>
<div class="block">Modification of fastNlMeansDenoising function for colored images</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingColored-org.opencv.core.Mat-org.opencv.core.Mat-float-float-">fastNlMeansDenoisingColored</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                           float&nbsp;h,
                           float&nbsp;hColor)</code>
<div class="block">Modification of fastNlMeansDenoising function for colored images</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingColored-org.opencv.core.Mat-org.opencv.core.Mat-float-float-int-">fastNlMeansDenoisingColored</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                           float&nbsp;h,
                           float&nbsp;hColor,
                           int&nbsp;templateWindowSize)</code>
<div class="block">Modification of fastNlMeansDenoising function for colored images</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingColored-org.opencv.core.Mat-org.opencv.core.Mat-float-float-int-int-">fastNlMeansDenoisingColored</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                           float&nbsp;h,
                           float&nbsp;hColor,
                           int&nbsp;templateWindowSize,
                           int&nbsp;searchWindowSize)</code>
<div class="block">Modification of fastNlMeansDenoising function for colored images</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingColoredMulti-java.util.List-org.opencv.core.Mat-int-int-">fastNlMeansDenoisingColoredMulti</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                int&nbsp;imgToDenoiseIndex,
                                int&nbsp;temporalWindowSize)</code>
<div class="block">Modification of fastNlMeansDenoisingMulti function for colored images sequences</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingColoredMulti-java.util.List-org.opencv.core.Mat-int-int-float-">fastNlMeansDenoisingColoredMulti</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                int&nbsp;imgToDenoiseIndex,
                                int&nbsp;temporalWindowSize,
                                float&nbsp;h)</code>
<div class="block">Modification of fastNlMeansDenoisingMulti function for colored images sequences</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingColoredMulti-java.util.List-org.opencv.core.Mat-int-int-float-float-">fastNlMeansDenoisingColoredMulti</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                int&nbsp;imgToDenoiseIndex,
                                int&nbsp;temporalWindowSize,
                                float&nbsp;h,
                                float&nbsp;hColor)</code>
<div class="block">Modification of fastNlMeansDenoisingMulti function for colored images sequences</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingColoredMulti-java.util.List-org.opencv.core.Mat-int-int-float-float-int-">fastNlMeansDenoisingColoredMulti</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                int&nbsp;imgToDenoiseIndex,
                                int&nbsp;temporalWindowSize,
                                float&nbsp;h,
                                float&nbsp;hColor,
                                int&nbsp;templateWindowSize)</code>
<div class="block">Modification of fastNlMeansDenoisingMulti function for colored images sequences</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingColoredMulti-java.util.List-org.opencv.core.Mat-int-int-float-float-int-int-">fastNlMeansDenoisingColoredMulti</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                int&nbsp;imgToDenoiseIndex,
                                int&nbsp;temporalWindowSize,
                                float&nbsp;h,
                                float&nbsp;hColor,
                                int&nbsp;templateWindowSize,
                                int&nbsp;searchWindowSize)</code>
<div class="block">Modification of fastNlMeansDenoisingMulti function for colored images sequences</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingMulti-java.util.List-org.opencv.core.Mat-int-int-">fastNlMeansDenoisingMulti</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                         int&nbsp;imgToDenoiseIndex,
                         int&nbsp;temporalWindowSize)</code>
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingMulti-java.util.List-org.opencv.core.Mat-int-int-float-">fastNlMeansDenoisingMulti</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                         int&nbsp;imgToDenoiseIndex,
                         int&nbsp;temporalWindowSize,
                         float&nbsp;h)</code>
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time.</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingMulti-java.util.List-org.opencv.core.Mat-int-int-float-int-">fastNlMeansDenoisingMulti</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                         int&nbsp;imgToDenoiseIndex,
                         int&nbsp;temporalWindowSize,
                         float&nbsp;h,
                         int&nbsp;templateWindowSize)</code>
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time.</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingMulti-java.util.List-org.opencv.core.Mat-int-int-float-int-int-">fastNlMeansDenoisingMulti</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                         int&nbsp;imgToDenoiseIndex,
                         int&nbsp;temporalWindowSize,
                         float&nbsp;h,
                         int&nbsp;templateWindowSize,
                         int&nbsp;searchWindowSize)</code>
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time.</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingMulti-java.util.List-org.opencv.core.Mat-int-int-org.opencv.core.MatOfFloat-">fastNlMeansDenoisingMulti</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                         int&nbsp;imgToDenoiseIndex,
                         int&nbsp;temporalWindowSize,
                         <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h)</code>
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time.</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingMulti-java.util.List-org.opencv.core.Mat-int-int-org.opencv.core.MatOfFloat-int-">fastNlMeansDenoisingMulti</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                         int&nbsp;imgToDenoiseIndex,
                         int&nbsp;temporalWindowSize,
                         <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
                         int&nbsp;templateWindowSize)</code>
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time.</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingMulti-java.util.List-org.opencv.core.Mat-int-int-org.opencv.core.MatOfFloat-int-int-">fastNlMeansDenoisingMulti</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                         int&nbsp;imgToDenoiseIndex,
                         int&nbsp;temporalWindowSize,
                         <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
                         int&nbsp;templateWindowSize,
                         int&nbsp;searchWindowSize)</code>
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time.</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingMulti-java.util.List-org.opencv.core.Mat-int-int-org.opencv.core.MatOfFloat-int-int-int-">fastNlMeansDenoisingMulti</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                         int&nbsp;imgToDenoiseIndex,
                         int&nbsp;temporalWindowSize,
                         <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
                         int&nbsp;templateWindowSize,
                         int&nbsp;searchWindowSize,
                         int&nbsp;normType)</code>
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time.</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#illuminationChange-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">illuminationChange</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>
<div class="block">Applying an appropriate non-linear transformation to the gradient field inside the selection and
 then integrating back with a Poisson solver, modifies locally the apparent illumination of an image.</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#illuminationChange-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-">illuminationChange</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                  float&nbsp;alpha)</code>
<div class="block">Applying an appropriate non-linear transformation to the gradient field inside the selection and
 then integrating back with a Poisson solver, modifies locally the apparent illumination of an image.</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#illuminationChange-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-float-">illuminationChange</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                  float&nbsp;alpha,
                  float&nbsp;beta)</code>
<div class="block">Applying an appropriate non-linear transformation to the gradient field inside the selection and
 then integrating back with a Poisson solver, modifies locally the apparent illumination of an image.</div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#inpaint-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-int-">inpaint</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inpaintMask,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
       double&nbsp;inpaintRadius,
       int&nbsp;flags)</code>
<div class="block">Restores the selected region in an image using the region neighborhood.</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#pencilSketch-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">pencilSketch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst1,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst2)</code>
<div class="block">Pencil-like non-photorealistic line drawing</div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#pencilSketch-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-">pencilSketch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst1,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst2,
            float&nbsp;sigma_s)</code>
<div class="block">Pencil-like non-photorealistic line drawing</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#pencilSketch-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-float-">pencilSketch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst1,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst2,
            float&nbsp;sigma_s,
            float&nbsp;sigma_r)</code>
<div class="block">Pencil-like non-photorealistic line drawing</div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#pencilSketch-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-float-float-">pencilSketch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst1,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst2,
            float&nbsp;sigma_s,
            float&nbsp;sigma_r,
            float&nbsp;shade_factor)</code>
<div class="block">Pencil-like non-photorealistic line drawing</div>
</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#seamlessClone-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Point-org.opencv.core.Mat-int-">seamlessClone</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
             <a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;p,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blend,
             int&nbsp;flags)</code>
<div class="block">Image editing tasks concern either global changes (color/intensity corrections, filters,
 deformations) or local changes concerned to a selection.</div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#stylization-org.opencv.core.Mat-org.opencv.core.Mat-">stylization</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>
<div class="block">Stylization aims to produce digital imagery with a wide variety of effects not focused on
 photorealism.</div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#stylization-org.opencv.core.Mat-org.opencv.core.Mat-float-">stylization</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
           float&nbsp;sigma_s)</code>
<div class="block">Stylization aims to produce digital imagery with a wide variety of effects not focused on
 photorealism.</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#stylization-org.opencv.core.Mat-org.opencv.core.Mat-float-float-">stylization</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
           float&nbsp;sigma_s,
           float&nbsp;sigma_r)</code>
<div class="block">Stylization aims to produce digital imagery with a wide variety of effects not focused on
 photorealism.</div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#textureFlattening-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">textureFlattening</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>
<div class="block">By retaining only the gradients at edge locations, before integrating with the Poisson solver, one
 washes out the texture of the selected region, giving its contents a flat aspect.</div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#textureFlattening-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-">textureFlattening</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                 float&nbsp;low_threshold)</code>
<div class="block">By retaining only the gradients at edge locations, before integrating with the Poisson solver, one
 washes out the texture of the selected region, giving its contents a flat aspect.</div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#textureFlattening-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-float-">textureFlattening</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                 float&nbsp;low_threshold,
                 float&nbsp;high_threshold)</code>
<div class="block">By retaining only the gradients at edge locations, before integrating with the Poisson solver, one
 washes out the texture of the selected region, giving its contents a flat aspect.</div>
</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#textureFlattening-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-float-int-">textureFlattening</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                 float&nbsp;low_threshold,
                 float&nbsp;high_threshold,
                 int&nbsp;kernel_size)</code>
<div class="block">By retaining only the gradients at edge locations, before integrating with the Poisson solver, one
 washes out the texture of the selected region, giving its contents a flat aspect.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="INPAINT_NS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INPAINT_NS</h4>
<pre>public static final&nbsp;int INPAINT_NS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.photo.Photo.INPAINT_NS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INPAINT_TELEA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INPAINT_TELEA</h4>
<pre>public static final&nbsp;int INPAINT_TELEA</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.photo.Photo.INPAINT_TELEA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LDR_SIZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LDR_SIZE</h4>
<pre>public static final&nbsp;int LDR_SIZE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.photo.Photo.LDR_SIZE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MIXED_CLONE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MIXED_CLONE</h4>
<pre>public static final&nbsp;int MIXED_CLONE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.photo.Photo.MIXED_CLONE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MONOCHROME_TRANSFER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MONOCHROME_TRANSFER</h4>
<pre>public static final&nbsp;int MONOCHROME_TRANSFER</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.photo.Photo.MONOCHROME_TRANSFER">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NORMAL_CLONE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NORMAL_CLONE</h4>
<pre>public static final&nbsp;int NORMAL_CLONE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.photo.Photo.NORMAL_CLONE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NORMCONV_FILTER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NORMCONV_FILTER</h4>
<pre>public static final&nbsp;int NORMCONV_FILTER</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.photo.Photo.NORMCONV_FILTER">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RECURS_FILTER">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>RECURS_FILTER</h4>
<pre>public static final&nbsp;int RECURS_FILTER</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.photo.Photo.RECURS_FILTER">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Photo--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Photo</h4>
<pre>public&nbsp;Photo()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="colorChange-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>colorChange</h4>
<pre>public static&nbsp;void&nbsp;colorChange(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
<div class="block">Given an original color image, two differently colored versions of this image can be mixed
 seamlessly.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>mask</code> - Input 8-bit 1 or 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .

 Multiplication factor is between .5 to 2.5.</dd>
</dl>
</li>
</ul>
<a name="colorChange-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>colorChange</h4>
<pre>public static&nbsp;void&nbsp;colorChange(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                               float&nbsp;red_mul)</pre>
<div class="block">Given an original color image, two differently colored versions of this image can be mixed
 seamlessly.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>mask</code> - Input 8-bit 1 or 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .</dd>
<dd><code>red_mul</code> - R-channel multiply factor.

 Multiplication factor is between .5 to 2.5.</dd>
</dl>
</li>
</ul>
<a name="colorChange-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>colorChange</h4>
<pre>public static&nbsp;void&nbsp;colorChange(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                               float&nbsp;red_mul,
                               float&nbsp;green_mul)</pre>
<div class="block">Given an original color image, two differently colored versions of this image can be mixed
 seamlessly.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>mask</code> - Input 8-bit 1 or 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .</dd>
<dd><code>red_mul</code> - R-channel multiply factor.</dd>
<dd><code>green_mul</code> - G-channel multiply factor.

 Multiplication factor is between .5 to 2.5.</dd>
</dl>
</li>
</ul>
<a name="colorChange-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>colorChange</h4>
<pre>public static&nbsp;void&nbsp;colorChange(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                               float&nbsp;red_mul,
                               float&nbsp;green_mul,
                               float&nbsp;blue_mul)</pre>
<div class="block">Given an original color image, two differently colored versions of this image can be mixed
 seamlessly.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>mask</code> - Input 8-bit 1 or 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .</dd>
<dd><code>red_mul</code> - R-channel multiply factor.</dd>
<dd><code>green_mul</code> - G-channel multiply factor.</dd>
<dd><code>blue_mul</code> - B-channel multiply factor.

 Multiplication factor is between .5 to 2.5.</dd>
</dl>
</li>
</ul>
<a name="createAlignMTB--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createAlignMTB</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/AlignMTB.html" title="class in org.opencv.photo">AlignMTB</a>&nbsp;createAlignMTB()</pre>
<div class="block">Creates AlignMTB object

 usually good enough (31 and 63 pixels shift respectively).
 median value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createAlignMTB-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createAlignMTB</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/AlignMTB.html" title="class in org.opencv.photo">AlignMTB</a>&nbsp;createAlignMTB(int&nbsp;max_bits)</pre>
<div class="block">Creates AlignMTB object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>max_bits</code> - logarithm to the base 2 of maximal shift in each dimension. Values of 5 and 6 are
 usually good enough (31 and 63 pixels shift respectively).
 median value.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createAlignMTB-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createAlignMTB</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/AlignMTB.html" title="class in org.opencv.photo">AlignMTB</a>&nbsp;createAlignMTB(int&nbsp;max_bits,
                                      int&nbsp;exclude_range)</pre>
<div class="block">Creates AlignMTB object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>max_bits</code> - logarithm to the base 2 of maximal shift in each dimension. Values of 5 and 6 are
 usually good enough (31 and 63 pixels shift respectively).</dd>
<dd><code>exclude_range</code> - range for exclusion bitmap that is constructed to suppress noise around the
 median value.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createAlignMTB-int-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createAlignMTB</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/AlignMTB.html" title="class in org.opencv.photo">AlignMTB</a>&nbsp;createAlignMTB(int&nbsp;max_bits,
                                      int&nbsp;exclude_range,
                                      boolean&nbsp;cut)</pre>
<div class="block">Creates AlignMTB object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>max_bits</code> - logarithm to the base 2 of maximal shift in each dimension. Values of 5 and 6 are
 usually good enough (31 and 63 pixels shift respectively).</dd>
<dd><code>exclude_range</code> - range for exclusion bitmap that is constructed to suppress noise around the
 median value.</dd>
<dd><code>cut</code> - if true cuts images, otherwise fills the new regions with zeros.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createCalibrateDebevec--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCalibrateDebevec</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/CalibrateDebevec.html" title="class in org.opencv.photo">CalibrateDebevec</a>&nbsp;createCalibrateDebevec()</pre>
<div class="block">Creates CalibrateDebevec object

 response.
 rectangular grid.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createCalibrateDebevec-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCalibrateDebevec</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/CalibrateDebevec.html" title="class in org.opencv.photo">CalibrateDebevec</a>&nbsp;createCalibrateDebevec(int&nbsp;samples)</pre>
<div class="block">Creates CalibrateDebevec object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - number of pixel locations to use
 response.
 rectangular grid.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createCalibrateDebevec-int-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCalibrateDebevec</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/CalibrateDebevec.html" title="class in org.opencv.photo">CalibrateDebevec</a>&nbsp;createCalibrateDebevec(int&nbsp;samples,
                                                      float&nbsp;lambda)</pre>
<div class="block">Creates CalibrateDebevec object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - number of pixel locations to use</dd>
<dd><code>lambda</code> - smoothness term weight. Greater values produce smoother results, but can alter the
 response.
 rectangular grid.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createCalibrateDebevec-int-float-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCalibrateDebevec</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/CalibrateDebevec.html" title="class in org.opencv.photo">CalibrateDebevec</a>&nbsp;createCalibrateDebevec(int&nbsp;samples,
                                                      float&nbsp;lambda,
                                                      boolean&nbsp;random)</pre>
<div class="block">Creates CalibrateDebevec object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - number of pixel locations to use</dd>
<dd><code>lambda</code> - smoothness term weight. Greater values produce smoother results, but can alter the
 response.</dd>
<dd><code>random</code> - if true sample pixel locations are chosen at random, otherwise they form a
 rectangular grid.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createCalibrateRobertson--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCalibrateRobertson</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/CalibrateRobertson.html" title="class in org.opencv.photo">CalibrateRobertson</a>&nbsp;createCalibrateRobertson()</pre>
<div class="block">Creates CalibrateRobertson object</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createCalibrateRobertson-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCalibrateRobertson</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/CalibrateRobertson.html" title="class in org.opencv.photo">CalibrateRobertson</a>&nbsp;createCalibrateRobertson(int&nbsp;max_iter)</pre>
<div class="block">Creates CalibrateRobertson object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>max_iter</code> - maximal number of Gauss-Seidel solver iterations.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createCalibrateRobertson-int-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCalibrateRobertson</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/CalibrateRobertson.html" title="class in org.opencv.photo">CalibrateRobertson</a>&nbsp;createCalibrateRobertson(int&nbsp;max_iter,
                                                          float&nbsp;threshold)</pre>
<div class="block">Creates CalibrateRobertson object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>max_iter</code> - maximal number of Gauss-Seidel solver iterations.</dd>
<dd><code>threshold</code> - target difference between results of two successive steps of the minimization.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createMergeDebevec--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createMergeDebevec</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/MergeDebevec.html" title="class in org.opencv.photo">MergeDebevec</a>&nbsp;createMergeDebevec()</pre>
<div class="block">Creates MergeDebevec object</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createMergeMertens--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createMergeMertens</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/MergeMertens.html" title="class in org.opencv.photo">MergeMertens</a>&nbsp;createMergeMertens()</pre>
<div class="block">Creates MergeMertens object</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createMergeMertens-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createMergeMertens</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/MergeMertens.html" title="class in org.opencv.photo">MergeMertens</a>&nbsp;createMergeMertens(float&nbsp;contrast_weight)</pre>
<div class="block">Creates MergeMertens object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>contrast_weight</code> - contrast measure weight. See MergeMertens.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createMergeMertens-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createMergeMertens</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/MergeMertens.html" title="class in org.opencv.photo">MergeMertens</a>&nbsp;createMergeMertens(float&nbsp;contrast_weight,
                                              float&nbsp;saturation_weight)</pre>
<div class="block">Creates MergeMertens object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>contrast_weight</code> - contrast measure weight. See MergeMertens.</dd>
<dd><code>saturation_weight</code> - saturation measure weight</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createMergeMertens-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createMergeMertens</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/MergeMertens.html" title="class in org.opencv.photo">MergeMertens</a>&nbsp;createMergeMertens(float&nbsp;contrast_weight,
                                              float&nbsp;saturation_weight,
                                              float&nbsp;exposure_weight)</pre>
<div class="block">Creates MergeMertens object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>contrast_weight</code> - contrast measure weight. See MergeMertens.</dd>
<dd><code>saturation_weight</code> - saturation measure weight</dd>
<dd><code>exposure_weight</code> - well-exposedness measure weight</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createMergeRobertson--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createMergeRobertson</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/MergeRobertson.html" title="class in org.opencv.photo">MergeRobertson</a>&nbsp;createMergeRobertson()</pre>
<div class="block">Creates MergeRobertson object</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createTonemap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTonemap</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/Tonemap.html" title="class in org.opencv.photo">Tonemap</a>&nbsp;createTonemap()</pre>
<div class="block">Creates simple linear mapper with gamma correction

 equal to 2.2f is suitable for most displays.
 Generally gamma &gt; 1 brightens the image and gamma &lt; 1 darkens it.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createTonemap-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTonemap</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/Tonemap.html" title="class in org.opencv.photo">Tonemap</a>&nbsp;createTonemap(float&nbsp;gamma)</pre>
<div class="block">Creates simple linear mapper with gamma correction</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>gamma</code> - positive value for gamma correction. Gamma value of 1.0 implies no correction, gamma
 equal to 2.2f is suitable for most displays.
 Generally gamma &gt; 1 brightens the image and gamma &lt; 1 darkens it.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createTonemapDrago--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTonemapDrago</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/TonemapDrago.html" title="class in org.opencv.photo">TonemapDrago</a>&nbsp;createTonemapDrago()</pre>
<div class="block">Creates TonemapDrago object

 than 1 increase saturation and values less than 1 decrease it.
 results, default value is 0.85.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createTonemapDrago-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTonemapDrago</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/TonemapDrago.html" title="class in org.opencv.photo">TonemapDrago</a>&nbsp;createTonemapDrago(float&nbsp;gamma)</pre>
<div class="block">Creates TonemapDrago object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>gamma</code> - gamma value for gamma correction. See createTonemap
 than 1 increase saturation and values less than 1 decrease it.
 results, default value is 0.85.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createTonemapDrago-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTonemapDrago</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/TonemapDrago.html" title="class in org.opencv.photo">TonemapDrago</a>&nbsp;createTonemapDrago(float&nbsp;gamma,
                                              float&nbsp;saturation)</pre>
<div class="block">Creates TonemapDrago object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>gamma</code> - gamma value for gamma correction. See createTonemap</dd>
<dd><code>saturation</code> - positive saturation enhancement value. 1.0 preserves saturation, values greater
 than 1 increase saturation and values less than 1 decrease it.
 results, default value is 0.85.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createTonemapDrago-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTonemapDrago</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/TonemapDrago.html" title="class in org.opencv.photo">TonemapDrago</a>&nbsp;createTonemapDrago(float&nbsp;gamma,
                                              float&nbsp;saturation,
                                              float&nbsp;bias)</pre>
<div class="block">Creates TonemapDrago object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>gamma</code> - gamma value for gamma correction. See createTonemap</dd>
<dd><code>saturation</code> - positive saturation enhancement value. 1.0 preserves saturation, values greater
 than 1 increase saturation and values less than 1 decrease it.</dd>
<dd><code>bias</code> - value for bias function in [0, 1] range. Values from 0.7 to 0.9 usually give best
 results, default value is 0.85.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createTonemapMantiuk--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTonemapMantiuk</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/TonemapMantiuk.html" title="class in org.opencv.photo">TonemapMantiuk</a>&nbsp;createTonemapMantiuk()</pre>
<div class="block">Creates TonemapMantiuk object

 dynamic range. Values from 0.6 to 0.9 produce best results.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createTonemapMantiuk-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTonemapMantiuk</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/TonemapMantiuk.html" title="class in org.opencv.photo">TonemapMantiuk</a>&nbsp;createTonemapMantiuk(float&nbsp;gamma)</pre>
<div class="block">Creates TonemapMantiuk object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>gamma</code> - gamma value for gamma correction. See createTonemap
 dynamic range. Values from 0.6 to 0.9 produce best results.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createTonemapMantiuk-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTonemapMantiuk</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/TonemapMantiuk.html" title="class in org.opencv.photo">TonemapMantiuk</a>&nbsp;createTonemapMantiuk(float&nbsp;gamma,
                                                  float&nbsp;scale)</pre>
<div class="block">Creates TonemapMantiuk object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>gamma</code> - gamma value for gamma correction. See createTonemap</dd>
<dd><code>scale</code> - contrast scale factor. HVS response is multiplied by this parameter, thus compressing
 dynamic range. Values from 0.6 to 0.9 produce best results.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createTonemapMantiuk-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTonemapMantiuk</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/TonemapMantiuk.html" title="class in org.opencv.photo">TonemapMantiuk</a>&nbsp;createTonemapMantiuk(float&nbsp;gamma,
                                                  float&nbsp;scale,
                                                  float&nbsp;saturation)</pre>
<div class="block">Creates TonemapMantiuk object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>gamma</code> - gamma value for gamma correction. See createTonemap</dd>
<dd><code>scale</code> - contrast scale factor. HVS response is multiplied by this parameter, thus compressing
 dynamic range. Values from 0.6 to 0.9 produce best results.</dd>
<dd><code>saturation</code> - saturation enhancement value. See createTonemapDrago</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createTonemapReinhard--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTonemapReinhard</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/TonemapReinhard.html" title="class in org.opencv.photo">TonemapReinhard</a>&nbsp;createTonemapReinhard()</pre>
<div class="block">Creates TonemapReinhard object

 value, if 0 it's global, otherwise it's a weighted mean of this two cases.
 if 0 adaptation level is the same for each channel.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createTonemapReinhard-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTonemapReinhard</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/TonemapReinhard.html" title="class in org.opencv.photo">TonemapReinhard</a>&nbsp;createTonemapReinhard(float&nbsp;gamma)</pre>
<div class="block">Creates TonemapReinhard object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>gamma</code> - gamma value for gamma correction. See createTonemap
 value, if 0 it's global, otherwise it's a weighted mean of this two cases.
 if 0 adaptation level is the same for each channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createTonemapReinhard-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTonemapReinhard</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/TonemapReinhard.html" title="class in org.opencv.photo">TonemapReinhard</a>&nbsp;createTonemapReinhard(float&nbsp;gamma,
                                                    float&nbsp;intensity)</pre>
<div class="block">Creates TonemapReinhard object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>gamma</code> - gamma value for gamma correction. See createTonemap</dd>
<dd><code>intensity</code> - result intensity in [-8, 8] range. Greater intensity produces brighter results.
 value, if 0 it's global, otherwise it's a weighted mean of this two cases.
 if 0 adaptation level is the same for each channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createTonemapReinhard-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTonemapReinhard</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/TonemapReinhard.html" title="class in org.opencv.photo">TonemapReinhard</a>&nbsp;createTonemapReinhard(float&nbsp;gamma,
                                                    float&nbsp;intensity,
                                                    float&nbsp;light_adapt)</pre>
<div class="block">Creates TonemapReinhard object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>gamma</code> - gamma value for gamma correction. See createTonemap</dd>
<dd><code>intensity</code> - result intensity in [-8, 8] range. Greater intensity produces brighter results.</dd>
<dd><code>light_adapt</code> - light adaptation in [0, 1] range. If 1 adaptation is based only on pixel
 value, if 0 it's global, otherwise it's a weighted mean of this two cases.
 if 0 adaptation level is the same for each channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createTonemapReinhard-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTonemapReinhard</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/TonemapReinhard.html" title="class in org.opencv.photo">TonemapReinhard</a>&nbsp;createTonemapReinhard(float&nbsp;gamma,
                                                    float&nbsp;intensity,
                                                    float&nbsp;light_adapt,
                                                    float&nbsp;color_adapt)</pre>
<div class="block">Creates TonemapReinhard object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>gamma</code> - gamma value for gamma correction. See createTonemap</dd>
<dd><code>intensity</code> - result intensity in [-8, 8] range. Greater intensity produces brighter results.</dd>
<dd><code>light_adapt</code> - light adaptation in [0, 1] range. If 1 adaptation is based only on pixel
 value, if 0 it's global, otherwise it's a weighted mean of this two cases.</dd>
<dd><code>color_adapt</code> - chromatic adaptation in [0, 1] range. If 1 channels are treated independently,
 if 0 adaptation level is the same for each channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="decolor-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decolor</h4>
<pre>public static&nbsp;void&nbsp;decolor(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;grayscale,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;color_boost)</pre>
<div class="block">Transforms a color image to a grayscale image. It is a basic tool in digital printing, stylized
 black-and-white photograph rendering, and in many single channel image processing applications
 CITE: CL12 .</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>grayscale</code> - Output 8-bit 1-channel image.</dd>
<dd><code>color_boost</code> - Output 8-bit 3-channel image.

 This function is to be applied on color images.</dd>
</dl>
</li>
</ul>
<a name="denoise_TVL1-java.util.List-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>denoise_TVL1</h4>
<pre>public static&nbsp;void&nbsp;denoise_TVL1(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;observations,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;result)</pre>
<div class="block">Primal-dual algorithm is an algorithm for solving special types of variational problems (that is,
 finding a function to minimize some functional). As the image denoising, in particular, may be seen
 as the variational problem, primal-dual algorithm then can be used to perform denoising and this is
 exactly what is implemented.

 It should be noted, that this implementation was taken from the July 2013 blog entry
 CITE: MA13 , which also contained (slightly more general) ready-to-use source code on Python.
 Subsequently, that code was rewritten on C++ with the usage of openCV by Vadim Pisarevsky at the end
 of July 2013 and finally it was slightly adapted by later authors.

 Although the thorough discussion and justification of the algorithm involved may be found in
 CITE: ChambolleEtAl, it might make sense to skim over it here, following CITE: MA13 . To begin
 with, we consider the 1-byte gray-level images as the functions from the rectangular domain of
 pixels (it may be seen as set
 \(\left\{(x,y)\in\mathbb{N}\times\mathbb{N}\mid 1\leq x\leq n,\;1\leq y\leq m\right\}\) for some
 \(m,\;n\in\mathbb{N}\)) into \(\{0,1,\dots,255\}\). We shall denote the noised images as \(f_i\) and with
 this view, given some image \(x\) of the same size, we may measure how bad it is by the formula

 \(\left\|\left\|\nabla x\right\|\right\| + \lambda\sum_i\left\|\left\|x-f_i\right\|\right\|\)

 \(\|\|\cdot\|\|\) here denotes \(L_2\)-norm and as you see, the first addend states that we want our
 image to be smooth (ideally, having zero gradient, thus being constant) and the second states that
 we want our result to be close to the observations we've got. If we treat \(x\) as a function, this is
 exactly the functional what we seek to minimize and here the Primal-Dual algorithm comes into play.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>observations</code> - This array should contain one or more noised versions of the image that is to
 be restored.</dd>
<dd><code>result</code> - Here the denoised image will be stored. There is no need to do pre-allocation of
 storage space, as it will be automatically allocated, if necessary.
 (blurred) images are treated more favorably than detailed (but maybe more noised) ones. Roughly
 speaking, as it becomes smaller, the result will be more blur but more sever outliers will be
 removed.
 better, but it is hard to quantitatively refine this statement, so just use the default and
 increase it if the results are poor.</dd>
</dl>
</li>
</ul>
<a name="denoise_TVL1-java.util.List-org.opencv.core.Mat-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>denoise_TVL1</h4>
<pre>public static&nbsp;void&nbsp;denoise_TVL1(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;observations,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;result,
                                double&nbsp;lambda)</pre>
<div class="block">Primal-dual algorithm is an algorithm for solving special types of variational problems (that is,
 finding a function to minimize some functional). As the image denoising, in particular, may be seen
 as the variational problem, primal-dual algorithm then can be used to perform denoising and this is
 exactly what is implemented.

 It should be noted, that this implementation was taken from the July 2013 blog entry
 CITE: MA13 , which also contained (slightly more general) ready-to-use source code on Python.
 Subsequently, that code was rewritten on C++ with the usage of openCV by Vadim Pisarevsky at the end
 of July 2013 and finally it was slightly adapted by later authors.

 Although the thorough discussion and justification of the algorithm involved may be found in
 CITE: ChambolleEtAl, it might make sense to skim over it here, following CITE: MA13 . To begin
 with, we consider the 1-byte gray-level images as the functions from the rectangular domain of
 pixels (it may be seen as set
 \(\left\{(x,y)\in\mathbb{N}\times\mathbb{N}\mid 1\leq x\leq n,\;1\leq y\leq m\right\}\) for some
 \(m,\;n\in\mathbb{N}\)) into \(\{0,1,\dots,255\}\). We shall denote the noised images as \(f_i\) and with
 this view, given some image \(x\) of the same size, we may measure how bad it is by the formula

 \(\left\|\left\|\nabla x\right\|\right\| + \lambda\sum_i\left\|\left\|x-f_i\right\|\right\|\)

 \(\|\|\cdot\|\|\) here denotes \(L_2\)-norm and as you see, the first addend states that we want our
 image to be smooth (ideally, having zero gradient, thus being constant) and the second states that
 we want our result to be close to the observations we've got. If we treat \(x\) as a function, this is
 exactly the functional what we seek to minimize and here the Primal-Dual algorithm comes into play.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>observations</code> - This array should contain one or more noised versions of the image that is to
 be restored.</dd>
<dd><code>result</code> - Here the denoised image will be stored. There is no need to do pre-allocation of
 storage space, as it will be automatically allocated, if necessary.</dd>
<dd><code>lambda</code> - Corresponds to \(\lambda\) in the formulas above. As it is enlarged, the smooth
 (blurred) images are treated more favorably than detailed (but maybe more noised) ones. Roughly
 speaking, as it becomes smaller, the result will be more blur but more sever outliers will be
 removed.
 better, but it is hard to quantitatively refine this statement, so just use the default and
 increase it if the results are poor.</dd>
</dl>
</li>
</ul>
<a name="denoise_TVL1-java.util.List-org.opencv.core.Mat-double-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>denoise_TVL1</h4>
<pre>public static&nbsp;void&nbsp;denoise_TVL1(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;observations,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;result,
                                double&nbsp;lambda,
                                int&nbsp;niters)</pre>
<div class="block">Primal-dual algorithm is an algorithm for solving special types of variational problems (that is,
 finding a function to minimize some functional). As the image denoising, in particular, may be seen
 as the variational problem, primal-dual algorithm then can be used to perform denoising and this is
 exactly what is implemented.

 It should be noted, that this implementation was taken from the July 2013 blog entry
 CITE: MA13 , which also contained (slightly more general) ready-to-use source code on Python.
 Subsequently, that code was rewritten on C++ with the usage of openCV by Vadim Pisarevsky at the end
 of July 2013 and finally it was slightly adapted by later authors.

 Although the thorough discussion and justification of the algorithm involved may be found in
 CITE: ChambolleEtAl, it might make sense to skim over it here, following CITE: MA13 . To begin
 with, we consider the 1-byte gray-level images as the functions from the rectangular domain of
 pixels (it may be seen as set
 \(\left\{(x,y)\in\mathbb{N}\times\mathbb{N}\mid 1\leq x\leq n,\;1\leq y\leq m\right\}\) for some
 \(m,\;n\in\mathbb{N}\)) into \(\{0,1,\dots,255\}\). We shall denote the noised images as \(f_i\) and with
 this view, given some image \(x\) of the same size, we may measure how bad it is by the formula

 \(\left\|\left\|\nabla x\right\|\right\| + \lambda\sum_i\left\|\left\|x-f_i\right\|\right\|\)

 \(\|\|\cdot\|\|\) here denotes \(L_2\)-norm and as you see, the first addend states that we want our
 image to be smooth (ideally, having zero gradient, thus being constant) and the second states that
 we want our result to be close to the observations we've got. If we treat \(x\) as a function, this is
 exactly the functional what we seek to minimize and here the Primal-Dual algorithm comes into play.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>observations</code> - This array should contain one or more noised versions of the image that is to
 be restored.</dd>
<dd><code>result</code> - Here the denoised image will be stored. There is no need to do pre-allocation of
 storage space, as it will be automatically allocated, if necessary.</dd>
<dd><code>lambda</code> - Corresponds to \(\lambda\) in the formulas above. As it is enlarged, the smooth
 (blurred) images are treated more favorably than detailed (but maybe more noised) ones. Roughly
 speaking, as it becomes smaller, the result will be more blur but more sever outliers will be
 removed.</dd>
<dd><code>niters</code> - Number of iterations that the algorithm will run. Of course, as more iterations as
 better, but it is hard to quantitatively refine this statement, so just use the default and
 increase it if the results are poor.</dd>
</dl>
</li>
</ul>
<a name="detailEnhance-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detailEnhance</h4>
<pre>public static&nbsp;void&nbsp;detailEnhance(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
<div class="block">This filter enhances the details of a particular image.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.</dd>
</dl>
</li>
</ul>
<a name="detailEnhance-org.opencv.core.Mat-org.opencv.core.Mat-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detailEnhance</h4>
<pre>public static&nbsp;void&nbsp;detailEnhance(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                 float&nbsp;sigma_s)</pre>
<div class="block">This filter enhances the details of a particular image.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.</dd>
<dd><code>sigma_s</code> - %Range between 0 to 200.</dd>
</dl>
</li>
</ul>
<a name="detailEnhance-org.opencv.core.Mat-org.opencv.core.Mat-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detailEnhance</h4>
<pre>public static&nbsp;void&nbsp;detailEnhance(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                 float&nbsp;sigma_s,
                                 float&nbsp;sigma_r)</pre>
<div class="block">This filter enhances the details of a particular image.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.</dd>
<dd><code>sigma_s</code> - %Range between 0 to 200.</dd>
<dd><code>sigma_r</code> - %Range between 0 to 1.</dd>
</dl>
</li>
</ul>
<a name="edgePreservingFilter-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>edgePreservingFilter</h4>
<pre>public static&nbsp;void&nbsp;edgePreservingFilter(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
<div class="block">Filtering is the fundamental operation in image and video processing. Edge-preserving smoothing
 filters are used in many different applications CITE: EM11 .</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output 8-bit 3-channel image.</dd>
</dl>
</li>
</ul>
<a name="edgePreservingFilter-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>edgePreservingFilter</h4>
<pre>public static&nbsp;void&nbsp;edgePreservingFilter(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                        int&nbsp;flags)</pre>
<div class="block">Filtering is the fundamental operation in image and video processing. Edge-preserving smoothing
 filters are used in many different applications CITE: EM11 .</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output 8-bit 3-channel image.</dd>
<dd><code>flags</code> - Edge preserving filters: cv::RECURS_FILTER or cv::NORMCONV_FILTER</dd>
</dl>
</li>
</ul>
<a name="edgePreservingFilter-org.opencv.core.Mat-org.opencv.core.Mat-int-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>edgePreservingFilter</h4>
<pre>public static&nbsp;void&nbsp;edgePreservingFilter(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                        int&nbsp;flags,
                                        float&nbsp;sigma_s)</pre>
<div class="block">Filtering is the fundamental operation in image and video processing. Edge-preserving smoothing
 filters are used in many different applications CITE: EM11 .</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output 8-bit 3-channel image.</dd>
<dd><code>flags</code> - Edge preserving filters: cv::RECURS_FILTER or cv::NORMCONV_FILTER</dd>
<dd><code>sigma_s</code> - %Range between 0 to 200.</dd>
</dl>
</li>
</ul>
<a name="edgePreservingFilter-org.opencv.core.Mat-org.opencv.core.Mat-int-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>edgePreservingFilter</h4>
<pre>public static&nbsp;void&nbsp;edgePreservingFilter(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                        int&nbsp;flags,
                                        float&nbsp;sigma_s,
                                        float&nbsp;sigma_r)</pre>
<div class="block">Filtering is the fundamental operation in image and video processing. Edge-preserving smoothing
 filters are used in many different applications CITE: EM11 .</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output 8-bit 3-channel image.</dd>
<dd><code>flags</code> - Edge preserving filters: cv::RECURS_FILTER or cv::NORMCONV_FILTER</dd>
<dd><code>sigma_s</code> - %Range between 0 to 200.</dd>
<dd><code>sigma_r</code> - %Range between 0 to 1.</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoising-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoising</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoising(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations. Noise expected to be a gaussian white noise</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 1-channel, 2-channel, 3-channel or 4-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels
 removes image details, smaller h value preserves details but also preserves some noise

 This function expected to be applied to grayscale images. For colored images look at
 fastNlMeansDenoisingColored. Advanced usage of this functions can be manual denoising of colored
 image in different colorspaces. Such approach is used in fastNlMeansDenoisingColored by converting
 image to CIELAB colorspace and then separately denoise L and AB components with different h
 parameter.</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoising-org.opencv.core.Mat-org.opencv.core.Mat-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoising</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoising(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                        float&nbsp;h)</pre>
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations. Noise expected to be a gaussian white noise</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 1-channel, 2-channel, 3-channel or 4-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength. Big h value perfectly removes noise but also
 removes image details, smaller h value preserves details but also preserves some noise

 This function expected to be applied to grayscale images. For colored images look at
 fastNlMeansDenoisingColored. Advanced usage of this functions can be manual denoising of colored
 image in different colorspaces. Such approach is used in fastNlMeansDenoisingColored by converting
 image to CIELAB colorspace and then separately denoise L and AB components with different h
 parameter.</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoising-org.opencv.core.Mat-org.opencv.core.Mat-float-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoising</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoising(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                        float&nbsp;h,
                                        int&nbsp;templateWindowSize)</pre>
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations. Noise expected to be a gaussian white noise</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 1-channel, 2-channel, 3-channel or 4-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength. Big h value perfectly removes noise but also
 removes image details, smaller h value preserves details but also preserves some noise

 This function expected to be applied to grayscale images. For colored images look at
 fastNlMeansDenoisingColored. Advanced usage of this functions can be manual denoising of colored
 image in different colorspaces. Such approach is used in fastNlMeansDenoisingColored by converting
 image to CIELAB colorspace and then separately denoise L and AB components with different h
 parameter.</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoising-org.opencv.core.Mat-org.opencv.core.Mat-float-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoising</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoising(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                        float&nbsp;h,
                                        int&nbsp;templateWindowSize,
                                        int&nbsp;searchWindowSize)</pre>
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations. Noise expected to be a gaussian white noise</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 1-channel, 2-channel, 3-channel or 4-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels</dd>
<dd><code>searchWindowSize</code> - Size in pixels of the window that is used to compute weighted average for
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength. Big h value perfectly removes noise but also
 removes image details, smaller h value preserves details but also preserves some noise

 This function expected to be applied to grayscale images. For colored images look at
 fastNlMeansDenoisingColored. Advanced usage of this functions can be manual denoising of colored
 image in different colorspaces. Such approach is used in fastNlMeansDenoisingColored by converting
 image to CIELAB colorspace and then separately denoise L and AB components with different h
 parameter.</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoising-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfFloat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoising</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoising(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                        <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h)</pre>
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations. Noise expected to be a gaussian white noise</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit or 16-bit (only with NORM_L1) 1-channel,
 2-channel, 3-channel or 4-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Array of parameters regulating filter strength, either one
 parameter applied to all channels or one per channel in dst. Big h value
 perfectly removes noise but also removes image details, smaller h
 value preserves details but also preserves some noise

 This function expected to be applied to grayscale images. For colored images look at
 fastNlMeansDenoisingColored. Advanced usage of this functions can be manual denoising of colored
 image in different colorspaces. Such approach is used in fastNlMeansDenoisingColored by converting
 image to CIELAB colorspace and then separately denoise L and AB components with different h
 parameter.</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoising-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfFloat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoising</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoising(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                        <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
                                        int&nbsp;templateWindowSize)</pre>
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations. Noise expected to be a gaussian white noise</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit or 16-bit (only with NORM_L1) 1-channel,
 2-channel, 3-channel or 4-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Array of parameters regulating filter strength, either one
 parameter applied to all channels or one per channel in dst. Big h value
 perfectly removes noise but also removes image details, smaller h
 value preserves details but also preserves some noise

 This function expected to be applied to grayscale images. For colored images look at
 fastNlMeansDenoisingColored. Advanced usage of this functions can be manual denoising of colored
 image in different colorspaces. Such approach is used in fastNlMeansDenoisingColored by converting
 image to CIELAB colorspace and then separately denoise L and AB components with different h
 parameter.</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoising-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfFloat-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoising</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoising(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                        <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
                                        int&nbsp;templateWindowSize,
                                        int&nbsp;searchWindowSize)</pre>
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations. Noise expected to be a gaussian white noise</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit or 16-bit (only with NORM_L1) 1-channel,
 2-channel, 3-channel or 4-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels</dd>
<dd><code>searchWindowSize</code> - Size in pixels of the window that is used to compute weighted average for
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Array of parameters regulating filter strength, either one
 parameter applied to all channels or one per channel in dst. Big h value
 perfectly removes noise but also removes image details, smaller h
 value preserves details but also preserves some noise

 This function expected to be applied to grayscale images. For colored images look at
 fastNlMeansDenoisingColored. Advanced usage of this functions can be manual denoising of colored
 image in different colorspaces. Such approach is used in fastNlMeansDenoisingColored by converting
 image to CIELAB colorspace and then separately denoise L and AB components with different h
 parameter.</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoising-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfFloat-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoising</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoising(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                        <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
                                        int&nbsp;templateWindowSize,
                                        int&nbsp;searchWindowSize,
                                        int&nbsp;normType)</pre>
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations. Noise expected to be a gaussian white noise</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit or 16-bit (only with NORM_L1) 1-channel,
 2-channel, 3-channel or 4-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels</dd>
<dd><code>searchWindowSize</code> - Size in pixels of the window that is used to compute weighted average for
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Array of parameters regulating filter strength, either one
 parameter applied to all channels or one per channel in dst. Big h value
 perfectly removes noise but also removes image details, smaller h
 value preserves details but also preserves some noise</dd>
<dd><code>normType</code> - Type of norm used for weight calculation. Can be either NORM_L2 or NORM_L1

 This function expected to be applied to grayscale images. For colored images look at
 fastNlMeansDenoisingColored. Advanced usage of this functions can be manual denoising of colored
 image in different colorspaces. Such approach is used in fastNlMeansDenoisingColored by converting
 image to CIELAB colorspace and then separately denoise L and AB components with different h
 parameter.</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoisingColored-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingColored</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingColored(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
<div class="block">Modification of fastNlMeansDenoising function for colored images</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels
 removes noise but also removes image details, smaller h value preserves details but also preserves
 some noise
 will be enough to remove colored noise and do not distort colors

 The function converts image to CIELAB colorspace and then separately denoise L and AB components
 with given h parameters using fastNlMeansDenoising function.</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoisingColored-org.opencv.core.Mat-org.opencv.core.Mat-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingColored</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingColored(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                               float&nbsp;h)</pre>
<div class="block">Modification of fastNlMeansDenoising function for colored images</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength for luminance component. Bigger h value perfectly
 removes noise but also removes image details, smaller h value preserves details but also preserves
 some noise
 will be enough to remove colored noise and do not distort colors

 The function converts image to CIELAB colorspace and then separately denoise L and AB components
 with given h parameters using fastNlMeansDenoising function.</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoisingColored-org.opencv.core.Mat-org.opencv.core.Mat-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingColored</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingColored(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                               float&nbsp;h,
                                               float&nbsp;hColor)</pre>
<div class="block">Modification of fastNlMeansDenoising function for colored images</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength for luminance component. Bigger h value perfectly
 removes noise but also removes image details, smaller h value preserves details but also preserves
 some noise</dd>
<dd><code>hColor</code> - The same as h but for color components. For most images value equals 10
 will be enough to remove colored noise and do not distort colors

 The function converts image to CIELAB colorspace and then separately denoise L and AB components
 with given h parameters using fastNlMeansDenoising function.</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoisingColored-org.opencv.core.Mat-org.opencv.core.Mat-float-float-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingColored</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingColored(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                               float&nbsp;h,
                                               float&nbsp;hColor,
                                               int&nbsp;templateWindowSize)</pre>
<div class="block">Modification of fastNlMeansDenoising function for colored images</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength for luminance component. Bigger h value perfectly
 removes noise but also removes image details, smaller h value preserves details but also preserves
 some noise</dd>
<dd><code>hColor</code> - The same as h but for color components. For most images value equals 10
 will be enough to remove colored noise and do not distort colors

 The function converts image to CIELAB colorspace and then separately denoise L and AB components
 with given h parameters using fastNlMeansDenoising function.</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoisingColored-org.opencv.core.Mat-org.opencv.core.Mat-float-float-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingColored</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingColored(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                               float&nbsp;h,
                                               float&nbsp;hColor,
                                               int&nbsp;templateWindowSize,
                                               int&nbsp;searchWindowSize)</pre>
<div class="block">Modification of fastNlMeansDenoising function for colored images</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels</dd>
<dd><code>searchWindowSize</code> - Size in pixels of the window that is used to compute weighted average for
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength for luminance component. Bigger h value perfectly
 removes noise but also removes image details, smaller h value preserves details but also preserves
 some noise</dd>
<dd><code>hColor</code> - The same as h but for color components. For most images value equals 10
 will be enough to remove colored noise and do not distort colors

 The function converts image to CIELAB colorspace and then separately denoise L and AB components
 with given h parameters using fastNlMeansDenoising function.</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoisingColoredMulti-java.util.List-org.opencv.core.Mat-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingColoredMulti</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingColoredMulti(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                                    int&nbsp;imgToDenoiseIndex,
                                                    int&nbsp;temporalWindowSize)</pre>
<div class="block">Modification of fastNlMeansDenoisingMulti function for colored images sequences</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>srcImgs</code> - Input 8-bit 3-channel images sequence. All images should have the same type and
 size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex - temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels
 removes noise but also removes image details, smaller h value preserves details but also preserves
 some noise.

 The function converts images to CIELAB colorspace and then separately denoise L and AB components
 with given h parameters using fastNlMeansDenoisingMulti function.</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoisingColoredMulti-java.util.List-org.opencv.core.Mat-int-int-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingColoredMulti</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingColoredMulti(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                                    int&nbsp;imgToDenoiseIndex,
                                                    int&nbsp;temporalWindowSize,
                                                    float&nbsp;h)</pre>
<div class="block">Modification of fastNlMeansDenoisingMulti function for colored images sequences</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>srcImgs</code> - Input 8-bit 3-channel images sequence. All images should have the same type and
 size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex - temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength for luminance component. Bigger h value perfectly
 removes noise but also removes image details, smaller h value preserves details but also preserves
 some noise.

 The function converts images to CIELAB colorspace and then separately denoise L and AB components
 with given h parameters using fastNlMeansDenoisingMulti function.</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoisingColoredMulti-java.util.List-org.opencv.core.Mat-int-int-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingColoredMulti</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingColoredMulti(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                                    int&nbsp;imgToDenoiseIndex,
                                                    int&nbsp;temporalWindowSize,
                                                    float&nbsp;h,
                                                    float&nbsp;hColor)</pre>
<div class="block">Modification of fastNlMeansDenoisingMulti function for colored images sequences</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>srcImgs</code> - Input 8-bit 3-channel images sequence. All images should have the same type and
 size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex - temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength for luminance component. Bigger h value perfectly
 removes noise but also removes image details, smaller h value preserves details but also preserves
 some noise.</dd>
<dd><code>hColor</code> - The same as h but for color components.

 The function converts images to CIELAB colorspace and then separately denoise L and AB components
 with given h parameters using fastNlMeansDenoisingMulti function.</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoisingColoredMulti-java.util.List-org.opencv.core.Mat-int-int-float-float-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingColoredMulti</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingColoredMulti(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                                    int&nbsp;imgToDenoiseIndex,
                                                    int&nbsp;temporalWindowSize,
                                                    float&nbsp;h,
                                                    float&nbsp;hColor,
                                                    int&nbsp;templateWindowSize)</pre>
<div class="block">Modification of fastNlMeansDenoisingMulti function for colored images sequences</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>srcImgs</code> - Input 8-bit 3-channel images sequence. All images should have the same type and
 size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex - temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength for luminance component. Bigger h value perfectly
 removes noise but also removes image details, smaller h value preserves details but also preserves
 some noise.</dd>
<dd><code>hColor</code> - The same as h but for color components.

 The function converts images to CIELAB colorspace and then separately denoise L and AB components
 with given h parameters using fastNlMeansDenoisingMulti function.</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoisingColoredMulti-java.util.List-org.opencv.core.Mat-int-int-float-float-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingColoredMulti</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingColoredMulti(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                                    int&nbsp;imgToDenoiseIndex,
                                                    int&nbsp;temporalWindowSize,
                                                    float&nbsp;h,
                                                    float&nbsp;hColor,
                                                    int&nbsp;templateWindowSize,
                                                    int&nbsp;searchWindowSize)</pre>
<div class="block">Modification of fastNlMeansDenoisingMulti function for colored images sequences</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>srcImgs</code> - Input 8-bit 3-channel images sequence. All images should have the same type and
 size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex - temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels</dd>
<dd><code>searchWindowSize</code> - Size in pixels of the window that is used to compute weighted average for
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength for luminance component. Bigger h value perfectly
 removes noise but also removes image details, smaller h value preserves details but also preserves
 some noise.</dd>
<dd><code>hColor</code> - The same as h but for color components.

 The function converts images to CIELAB colorspace and then separately denoise L and AB components
 with given h parameters using fastNlMeansDenoisingMulti function.</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoisingMulti-java.util.List-org.opencv.core.Mat-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingMulti</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingMulti(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                             int&nbsp;imgToDenoiseIndex,
                                             int&nbsp;temporalWindowSize)</pre>
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time. For example video. This version of the function is for grayscale
 images or for manual manipulation with colorspaces. For more details see
 &lt;http://citeseerx.ist.psu.edu/viewdoc/summary?doi=**********.6394&gt;</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>srcImgs</code> - Input 8-bit 1-channel, 2-channel, 3-channel or
 4-channel images sequence. All images should have the same type and
 size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex - temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels
 perfectly removes noise but also removes image details, smaller h
 value preserves details but also preserves some noise</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoisingMulti-java.util.List-org.opencv.core.Mat-int-int-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingMulti</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingMulti(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                             int&nbsp;imgToDenoiseIndex,
                                             int&nbsp;temporalWindowSize,
                                             float&nbsp;h)</pre>
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time. For example video. This version of the function is for grayscale
 images or for manual manipulation with colorspaces. For more details see
 &lt;http://citeseerx.ist.psu.edu/viewdoc/summary?doi=**********.6394&gt;</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>srcImgs</code> - Input 8-bit 1-channel, 2-channel, 3-channel or
 4-channel images sequence. All images should have the same type and
 size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex - temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength. Bigger h value
 perfectly removes noise but also removes image details, smaller h
 value preserves details but also preserves some noise</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoisingMulti-java.util.List-org.opencv.core.Mat-int-int-float-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingMulti</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingMulti(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                             int&nbsp;imgToDenoiseIndex,
                                             int&nbsp;temporalWindowSize,
                                             float&nbsp;h,
                                             int&nbsp;templateWindowSize)</pre>
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time. For example video. This version of the function is for grayscale
 images or for manual manipulation with colorspaces. For more details see
 &lt;http://citeseerx.ist.psu.edu/viewdoc/summary?doi=**********.6394&gt;</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>srcImgs</code> - Input 8-bit 1-channel, 2-channel, 3-channel or
 4-channel images sequence. All images should have the same type and
 size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex - temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength. Bigger h value
 perfectly removes noise but also removes image details, smaller h
 value preserves details but also preserves some noise</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoisingMulti-java.util.List-org.opencv.core.Mat-int-int-float-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingMulti</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingMulti(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                             int&nbsp;imgToDenoiseIndex,
                                             int&nbsp;temporalWindowSize,
                                             float&nbsp;h,
                                             int&nbsp;templateWindowSize,
                                             int&nbsp;searchWindowSize)</pre>
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time. For example video. This version of the function is for grayscale
 images or for manual manipulation with colorspaces. For more details see
 &lt;http://citeseerx.ist.psu.edu/viewdoc/summary?doi=**********.6394&gt;</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>srcImgs</code> - Input 8-bit 1-channel, 2-channel, 3-channel or
 4-channel images sequence. All images should have the same type and
 size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex - temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels</dd>
<dd><code>searchWindowSize</code> - Size in pixels of the window that is used to compute weighted average for
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength. Bigger h value
 perfectly removes noise but also removes image details, smaller h
 value preserves details but also preserves some noise</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoisingMulti-java.util.List-org.opencv.core.Mat-int-int-org.opencv.core.MatOfFloat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingMulti</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingMulti(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                             int&nbsp;imgToDenoiseIndex,
                                             int&nbsp;temporalWindowSize,
                                             <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h)</pre>
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time. For example video. This version of the function is for grayscale
 images or for manual manipulation with colorspaces. For more details see
 &lt;http://citeseerx.ist.psu.edu/viewdoc/summary?doi=**********.6394&gt;</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>srcImgs</code> - Input 8-bit or 16-bit (only with NORM_L1) 1-channel,
 2-channel, 3-channel or 4-channel images sequence. All images should
 have the same type and size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex - temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Array of parameters regulating filter strength, either one
 parameter applied to all channels or one per channel in dst. Big h value
 perfectly removes noise but also removes image details, smaller h
 value preserves details but also preserves some noise</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoisingMulti-java.util.List-org.opencv.core.Mat-int-int-org.opencv.core.MatOfFloat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingMulti</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingMulti(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                             int&nbsp;imgToDenoiseIndex,
                                             int&nbsp;temporalWindowSize,
                                             <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
                                             int&nbsp;templateWindowSize)</pre>
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time. For example video. This version of the function is for grayscale
 images or for manual manipulation with colorspaces. For more details see
 &lt;http://citeseerx.ist.psu.edu/viewdoc/summary?doi=**********.6394&gt;</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>srcImgs</code> - Input 8-bit or 16-bit (only with NORM_L1) 1-channel,
 2-channel, 3-channel or 4-channel images sequence. All images should
 have the same type and size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex - temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Array of parameters regulating filter strength, either one
 parameter applied to all channels or one per channel in dst. Big h value
 perfectly removes noise but also removes image details, smaller h
 value preserves details but also preserves some noise</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoisingMulti-java.util.List-org.opencv.core.Mat-int-int-org.opencv.core.MatOfFloat-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingMulti</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingMulti(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                             int&nbsp;imgToDenoiseIndex,
                                             int&nbsp;temporalWindowSize,
                                             <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
                                             int&nbsp;templateWindowSize,
                                             int&nbsp;searchWindowSize)</pre>
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time. For example video. This version of the function is for grayscale
 images or for manual manipulation with colorspaces. For more details see
 &lt;http://citeseerx.ist.psu.edu/viewdoc/summary?doi=**********.6394&gt;</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>srcImgs</code> - Input 8-bit or 16-bit (only with NORM_L1) 1-channel,
 2-channel, 3-channel or 4-channel images sequence. All images should
 have the same type and size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex - temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels</dd>
<dd><code>searchWindowSize</code> - Size in pixels of the window that is used to compute weighted average for
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Array of parameters regulating filter strength, either one
 parameter applied to all channels or one per channel in dst. Big h value
 perfectly removes noise but also removes image details, smaller h
 value preserves details but also preserves some noise</dd>
</dl>
</li>
</ul>
<a name="fastNlMeansDenoisingMulti-java.util.List-org.opencv.core.Mat-int-int-org.opencv.core.MatOfFloat-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingMulti</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingMulti(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                             int&nbsp;imgToDenoiseIndex,
                                             int&nbsp;temporalWindowSize,
                                             <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
                                             int&nbsp;templateWindowSize,
                                             int&nbsp;searchWindowSize,
                                             int&nbsp;normType)</pre>
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time. For example video. This version of the function is for grayscale
 images or for manual manipulation with colorspaces. For more details see
 &lt;http://citeseerx.ist.psu.edu/viewdoc/summary?doi=**********.6394&gt;</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>srcImgs</code> - Input 8-bit or 16-bit (only with NORM_L1) 1-channel,
 2-channel, 3-channel or 4-channel images sequence. All images should
 have the same type and size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex - temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels</dd>
<dd><code>searchWindowSize</code> - Size in pixels of the window that is used to compute weighted average for
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Array of parameters regulating filter strength, either one
 parameter applied to all channels or one per channel in dst. Big h value
 perfectly removes noise but also removes image details, smaller h
 value preserves details but also preserves some noise</dd>
<dd><code>normType</code> - Type of norm used for weight calculation. Can be either NORM_L2 or NORM_L1</dd>
</dl>
</li>
</ul>
<a name="illuminationChange-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>illuminationChange</h4>
<pre>public static&nbsp;void&nbsp;illuminationChange(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
<div class="block">Applying an appropriate non-linear transformation to the gradient field inside the selection and
 then integrating back with a Poisson solver, modifies locally the apparent illumination of an image.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>mask</code> - Input 8-bit 1 or 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.

 This is useful to highlight under-exposed foreground objects or to reduce specular reflections.</dd>
</dl>
</li>
</ul>
<a name="illuminationChange-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>illuminationChange</h4>
<pre>public static&nbsp;void&nbsp;illuminationChange(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                      float&nbsp;alpha)</pre>
<div class="block">Applying an appropriate non-linear transformation to the gradient field inside the selection and
 then integrating back with a Poisson solver, modifies locally the apparent illumination of an image.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>mask</code> - Input 8-bit 1 or 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.</dd>
<dd><code>alpha</code> - Value ranges between 0-2.

 This is useful to highlight under-exposed foreground objects or to reduce specular reflections.</dd>
</dl>
</li>
</ul>
<a name="illuminationChange-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>illuminationChange</h4>
<pre>public static&nbsp;void&nbsp;illuminationChange(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                      float&nbsp;alpha,
                                      float&nbsp;beta)</pre>
<div class="block">Applying an appropriate non-linear transformation to the gradient field inside the selection and
 then integrating back with a Poisson solver, modifies locally the apparent illumination of an image.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>mask</code> - Input 8-bit 1 or 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.</dd>
<dd><code>alpha</code> - Value ranges between 0-2.</dd>
<dd><code>beta</code> - Value ranges between 0-2.

 This is useful to highlight under-exposed foreground objects or to reduce specular reflections.</dd>
</dl>
</li>
</ul>
<a name="inpaint-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>inpaint</h4>
<pre>public static&nbsp;void&nbsp;inpaint(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inpaintMask,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                           double&nbsp;inpaintRadius,
                           int&nbsp;flags)</pre>
<div class="block">Restores the selected region in an image using the region neighborhood.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit, 16-bit unsigned or 32-bit float 1-channel or 8-bit 3-channel image.</dd>
<dd><code>inpaintMask</code> - Inpainting mask, 8-bit 1-channel image. Non-zero pixels indicate the area that
 needs to be inpainted.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .</dd>
<dd><code>inpaintRadius</code> - Radius of a circular neighborhood of each point inpainted that is considered
 by the algorithm.</dd>
<dd><code>flags</code> - Inpainting method that could be cv::INPAINT_NS or cv::INPAINT_TELEA

 The function reconstructs the selected image area from the pixel near the area boundary. The
 function may be used to remove dust and scratches from a scanned photo, or to remove undesirable
 objects from still images or video. See &lt;http://en.wikipedia.org/wiki/Inpainting&gt; for more details.

 <b>Note:</b>
 <ul>
   <li>
       An example using the inpainting technique can be found at
         opencv_source_code/samples/cpp/inpaint.cpp
   </li>
   <li>
       (Python) An example using the inpainting technique can be found at
         opencv_source_code/samples/python/inpaint.py
   </li>
 </ul></dd>
</dl>
</li>
</ul>
<a name="pencilSketch-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pencilSketch</h4>
<pre>public static&nbsp;void&nbsp;pencilSketch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst1,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst2)</pre>
<div class="block">Pencil-like non-photorealistic line drawing</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst1</code> - Output 8-bit 1-channel image.</dd>
<dd><code>dst2</code> - Output image with the same size and type as src.</dd>
</dl>
</li>
</ul>
<a name="pencilSketch-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pencilSketch</h4>
<pre>public static&nbsp;void&nbsp;pencilSketch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst1,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst2,
                                float&nbsp;sigma_s)</pre>
<div class="block">Pencil-like non-photorealistic line drawing</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst1</code> - Output 8-bit 1-channel image.</dd>
<dd><code>dst2</code> - Output image with the same size and type as src.</dd>
<dd><code>sigma_s</code> - %Range between 0 to 200.</dd>
</dl>
</li>
</ul>
<a name="pencilSketch-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pencilSketch</h4>
<pre>public static&nbsp;void&nbsp;pencilSketch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst1,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst2,
                                float&nbsp;sigma_s,
                                float&nbsp;sigma_r)</pre>
<div class="block">Pencil-like non-photorealistic line drawing</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst1</code> - Output 8-bit 1-channel image.</dd>
<dd><code>dst2</code> - Output image with the same size and type as src.</dd>
<dd><code>sigma_s</code> - %Range between 0 to 200.</dd>
<dd><code>sigma_r</code> - %Range between 0 to 1.</dd>
</dl>
</li>
</ul>
<a name="pencilSketch-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pencilSketch</h4>
<pre>public static&nbsp;void&nbsp;pencilSketch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst1,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst2,
                                float&nbsp;sigma_s,
                                float&nbsp;sigma_r,
                                float&nbsp;shade_factor)</pre>
<div class="block">Pencil-like non-photorealistic line drawing</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst1</code> - Output 8-bit 1-channel image.</dd>
<dd><code>dst2</code> - Output image with the same size and type as src.</dd>
<dd><code>sigma_s</code> - %Range between 0 to 200.</dd>
<dd><code>sigma_r</code> - %Range between 0 to 1.</dd>
<dd><code>shade_factor</code> - %Range between 0 to 0.1.</dd>
</dl>
</li>
</ul>
<a name="seamlessClone-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Point-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>seamlessClone</h4>
<pre>public static&nbsp;void&nbsp;seamlessClone(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                                 <a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;p,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blend,
                                 int&nbsp;flags)</pre>
<div class="block">Image editing tasks concern either global changes (color/intensity corrections, filters,
 deformations) or local changes concerned to a selection. Here we are interested in achieving local
 changes, ones that are restricted to a region manually selected (ROI), in a seamless and effortless
 manner. The extent of the changes ranges from slight distortions to complete replacement by novel
 content CITE: PM03 .</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Input 8-bit 3-channel image.</dd>
<dd><code>mask</code> - Input 8-bit 1 or 3-channel image.</dd>
<dd><code>p</code> - Point in dst image where object is placed.</dd>
<dd><code>blend</code> - Output image with the same size and type as dst.</dd>
<dd><code>flags</code> - Cloning method that could be cv::NORMAL_CLONE, cv::MIXED_CLONE or cv::MONOCHROME_TRANSFER</dd>
</dl>
</li>
</ul>
<a name="stylization-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stylization</h4>
<pre>public static&nbsp;void&nbsp;stylization(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
<div class="block">Stylization aims to produce digital imagery with a wide variety of effects not focused on
 photorealism. Edge-aware filters are ideal for stylization, as they can abstract regions of low
 contrast while preserving, or enhancing, high-contrast features.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.</dd>
</dl>
</li>
</ul>
<a name="stylization-org.opencv.core.Mat-org.opencv.core.Mat-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stylization</h4>
<pre>public static&nbsp;void&nbsp;stylization(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                               float&nbsp;sigma_s)</pre>
<div class="block">Stylization aims to produce digital imagery with a wide variety of effects not focused on
 photorealism. Edge-aware filters are ideal for stylization, as they can abstract regions of low
 contrast while preserving, or enhancing, high-contrast features.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.</dd>
<dd><code>sigma_s</code> - %Range between 0 to 200.</dd>
</dl>
</li>
</ul>
<a name="stylization-org.opencv.core.Mat-org.opencv.core.Mat-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stylization</h4>
<pre>public static&nbsp;void&nbsp;stylization(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                               float&nbsp;sigma_s,
                               float&nbsp;sigma_r)</pre>
<div class="block">Stylization aims to produce digital imagery with a wide variety of effects not focused on
 photorealism. Edge-aware filters are ideal for stylization, as they can abstract regions of low
 contrast while preserving, or enhancing, high-contrast features.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.</dd>
<dd><code>sigma_s</code> - %Range between 0 to 200.</dd>
<dd><code>sigma_r</code> - %Range between 0 to 1.</dd>
</dl>
</li>
</ul>
<a name="textureFlattening-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>textureFlattening</h4>
<pre>public static&nbsp;void&nbsp;textureFlattening(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
<div class="block">By retaining only the gradients at edge locations, before integrating with the Poisson solver, one
 washes out the texture of the selected region, giving its contents a flat aspect. Here Canny Edge %Detector is used.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>mask</code> - Input 8-bit 1 or 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.

 <b>Note:</b>
 The algorithm assumes that the color of the source image is close to that of the destination. This
 assumption means that when the colors don't match, the source image color gets tinted toward the
 color of the destination image.</dd>
</dl>
</li>
</ul>
<a name="textureFlattening-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>textureFlattening</h4>
<pre>public static&nbsp;void&nbsp;textureFlattening(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                     float&nbsp;low_threshold)</pre>
<div class="block">By retaining only the gradients at edge locations, before integrating with the Poisson solver, one
 washes out the texture of the selected region, giving its contents a flat aspect. Here Canny Edge %Detector is used.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>mask</code> - Input 8-bit 1 or 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.</dd>
<dd><code>low_threshold</code> - %Range from 0 to 100.

 <b>Note:</b>
 The algorithm assumes that the color of the source image is close to that of the destination. This
 assumption means that when the colors don't match, the source image color gets tinted toward the
 color of the destination image.</dd>
</dl>
</li>
</ul>
<a name="textureFlattening-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>textureFlattening</h4>
<pre>public static&nbsp;void&nbsp;textureFlattening(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                     float&nbsp;low_threshold,
                                     float&nbsp;high_threshold)</pre>
<div class="block">By retaining only the gradients at edge locations, before integrating with the Poisson solver, one
 washes out the texture of the selected region, giving its contents a flat aspect. Here Canny Edge %Detector is used.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>mask</code> - Input 8-bit 1 or 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.</dd>
<dd><code>low_threshold</code> - %Range from 0 to 100.</dd>
<dd><code>high_threshold</code> - Value &gt; 100.

 <b>Note:</b>
 The algorithm assumes that the color of the source image is close to that of the destination. This
 assumption means that when the colors don't match, the source image color gets tinted toward the
 color of the destination image.</dd>
</dl>
</li>
</ul>
<a name="textureFlattening-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-float-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>textureFlattening</h4>
<pre>public static&nbsp;void&nbsp;textureFlattening(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                     float&nbsp;low_threshold,
                                     float&nbsp;high_threshold,
                                     int&nbsp;kernel_size)</pre>
<div class="block">By retaining only the gradients at edge locations, before integrating with the Poisson solver, one
 washes out the texture of the selected region, giving its contents a flat aspect. Here Canny Edge %Detector is used.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>mask</code> - Input 8-bit 1 or 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.</dd>
<dd><code>low_threshold</code> - %Range from 0 to 100.</dd>
<dd><code>high_threshold</code> - Value &gt; 100.</dd>
<dd><code>kernel_size</code> - The size of the Sobel kernel to be used.

 <b>Note:</b>
 The algorithm assumes that the color of the source image is close to that of the destination. This
 assumption means that when the colors don't match, the source image color gets tinted toward the
 color of the destination image.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/photo/MergeRobertson.html" title="class in org.opencv.photo"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/photo/Tonemap.html" title="class in org.opencv.photo"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/photo/Photo.html" target="_top">Frames</a></li>
<li><a href="Photo.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2021-12-25 08:13:27 / OpenCV 4.5.5</small></p>
</body>
</html>
