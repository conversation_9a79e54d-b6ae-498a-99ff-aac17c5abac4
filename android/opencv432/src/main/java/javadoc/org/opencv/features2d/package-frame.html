<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_292) on Sat Dec 25 08:13:30 UTC 2021 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>org.opencv.features2d (OpenCV 4.5.5 Java documentation)</title>
<meta name="date" content="2021-12-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../org/opencv/features2d/package-summary.html" target="classFrame">org.opencv.features2d</a></h1>
<div class="indexContainer">
<h2 title="Classes">Classes</h2>
<ul title="Classes">
<li><a href="AffineFeature.html" title="class in org.opencv.features2d" target="classFrame">AffineFeature</a></li>
<li><a href="AgastFeatureDetector.html" title="class in org.opencv.features2d" target="classFrame">AgastFeatureDetector</a></li>
<li><a href="AKAZE.html" title="class in org.opencv.features2d" target="classFrame">AKAZE</a></li>
<li><a href="BFMatcher.html" title="class in org.opencv.features2d" target="classFrame">BFMatcher</a></li>
<li><a href="BOWImgDescriptorExtractor.html" title="class in org.opencv.features2d" target="classFrame">BOWImgDescriptorExtractor</a></li>
<li><a href="BOWKMeansTrainer.html" title="class in org.opencv.features2d" target="classFrame">BOWKMeansTrainer</a></li>
<li><a href="BOWTrainer.html" title="class in org.opencv.features2d" target="classFrame">BOWTrainer</a></li>
<li><a href="BRISK.html" title="class in org.opencv.features2d" target="classFrame">BRISK</a></li>
<li><a href="DescriptorMatcher.html" title="class in org.opencv.features2d" target="classFrame">DescriptorMatcher</a></li>
<li><a href="FastFeatureDetector.html" title="class in org.opencv.features2d" target="classFrame">FastFeatureDetector</a></li>
<li><a href="Feature2D.html" title="class in org.opencv.features2d" target="classFrame">Feature2D</a></li>
<li><a href="Features2d.html" title="class in org.opencv.features2d" target="classFrame">Features2d</a></li>
<li><a href="FlannBasedMatcher.html" title="class in org.opencv.features2d" target="classFrame">FlannBasedMatcher</a></li>
<li><a href="GFTTDetector.html" title="class in org.opencv.features2d" target="classFrame">GFTTDetector</a></li>
<li><a href="KAZE.html" title="class in org.opencv.features2d" target="classFrame">KAZE</a></li>
<li><a href="MSER.html" title="class in org.opencv.features2d" target="classFrame">MSER</a></li>
<li><a href="ORB.html" title="class in org.opencv.features2d" target="classFrame">ORB</a></li>
<li><a href="SIFT.html" title="class in org.opencv.features2d" target="classFrame">SIFT</a></li>
<li><a href="SimpleBlobDetector.html" title="class in org.opencv.features2d" target="classFrame">SimpleBlobDetector</a></li>
<li><a href="SimpleBlobDetector_Params.html" title="class in org.opencv.features2d" target="classFrame">SimpleBlobDetector_Params</a></li>
</ul>
</div>
</body>
</html>
