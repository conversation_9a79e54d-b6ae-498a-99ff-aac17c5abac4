<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_292) on Sat Dec 25 08:13:29 UTC 2021 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AKAZE (OpenCV 4.5.5 Java documentation)</title>
<meta name="date" content="2021-12-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AKAZE (OpenCV 4.5.5 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/features2d/AgastFeatureDetector.html" title="class in org.opencv.features2d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/features2d/BFMatcher.html" title="class in org.opencv.features2d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/AKAZE.html" target="_top">Frames</a></li>
<li><a href="AKAZE.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.features2d</div>
<h2 title="Class AKAZE" class="title">Class AKAZE</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">org.opencv.features2d.Feature2D</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.features2d.AKAZE</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">AKAZE</span>
extends <a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></pre>
<div class="block">Class implementing the AKAZE keypoint detector and descriptor extractor, described in CITE: ANB13.

 AKAZE descriptors can only be used with KAZE or AKAZE keypoints. This class is thread-safe.

 <b>Note:</b> When you need descriptors use Feature2D::detectAndCompute, which
 provides better performance. When using Feature2D::detect followed by
 Feature2D::compute scale space pyramid is computed twice.

 <b>Note:</b> AKAZE implements T-API. When image is passed as UMat some parts of the algorithm
 will use OpenCL.

 <b>Note:</b> [ANB13] Fast Explicit Diffusion for Accelerated Features in Nonlinear
 Scale Spaces. Pablo F. Alcantarilla, Jesús Nuevo and Adrien Bartoli. In
 British Machine Vision Conference (BMVC), Bristol, UK, September 2013.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#DESCRIPTOR_KAZE">DESCRIPTOR_KAZE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#DESCRIPTOR_KAZE_UPRIGHT">DESCRIPTOR_KAZE_UPRIGHT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#DESCRIPTOR_MLDB">DESCRIPTOR_MLDB</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#DESCRIPTOR_MLDB_UPRIGHT">DESCRIPTOR_MLDB_UPRIGHT</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/AKAZE.html" title="class in org.opencv.features2d">AKAZE</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/AKAZE.html" title="class in org.opencv.features2d">AKAZE</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#create--">create</a></span>()</code>
<div class="block">The AKAZE constructor

     DESCRIPTOR_KAZE_UPRIGHT, DESCRIPTOR_MLDB or DESCRIPTOR_MLDB_UPRIGHT.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/AKAZE.html" title="class in org.opencv.features2d">AKAZE</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#create-int-">create</a></span>(int&nbsp;descriptor_type)</code>
<div class="block">The AKAZE constructor</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/AKAZE.html" title="class in org.opencv.features2d">AKAZE</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#create-int-int-">create</a></span>(int&nbsp;descriptor_type,
      int&nbsp;descriptor_size)</code>
<div class="block">The AKAZE constructor</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/AKAZE.html" title="class in org.opencv.features2d">AKAZE</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#create-int-int-int-">create</a></span>(int&nbsp;descriptor_type,
      int&nbsp;descriptor_size,
      int&nbsp;descriptor_channels)</code>
<div class="block">The AKAZE constructor</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/AKAZE.html" title="class in org.opencv.features2d">AKAZE</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#create-int-int-int-float-">create</a></span>(int&nbsp;descriptor_type,
      int&nbsp;descriptor_size,
      int&nbsp;descriptor_channels,
      float&nbsp;threshold)</code>
<div class="block">The AKAZE constructor</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/AKAZE.html" title="class in org.opencv.features2d">AKAZE</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#create-int-int-int-float-int-">create</a></span>(int&nbsp;descriptor_type,
      int&nbsp;descriptor_size,
      int&nbsp;descriptor_channels,
      float&nbsp;threshold,
      int&nbsp;nOctaves)</code>
<div class="block">The AKAZE constructor</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/AKAZE.html" title="class in org.opencv.features2d">AKAZE</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#create-int-int-int-float-int-int-">create</a></span>(int&nbsp;descriptor_type,
      int&nbsp;descriptor_size,
      int&nbsp;descriptor_channels,
      float&nbsp;threshold,
      int&nbsp;nOctaves,
      int&nbsp;nOctaveLayers)</code>
<div class="block">The AKAZE constructor</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/AKAZE.html" title="class in org.opencv.features2d">AKAZE</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#create-int-int-int-float-int-int-int-">create</a></span>(int&nbsp;descriptor_type,
      int&nbsp;descriptor_size,
      int&nbsp;descriptor_channels,
      float&nbsp;threshold,
      int&nbsp;nOctaves,
      int&nbsp;nOctaveLayers,
      int&nbsp;diffusivity)</code>
<div class="block">The AKAZE constructor</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#getDefaultName--">getDefaultName</a></span>()</code>
<div class="block">Returns the algorithm string identifier.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#getDescriptorChannels--">getDescriptorChannels</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#getDescriptorSize--">getDescriptorSize</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#getDescriptorType--">getDescriptorType</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#getDiffusivity--">getDiffusivity</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#getNOctaveLayers--">getNOctaveLayers</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#getNOctaves--">getNOctaves</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#getThreshold--">getThreshold</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#setDescriptorChannels-int-">setDescriptorChannels</a></span>(int&nbsp;dch)</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#setDescriptorSize-int-">setDescriptorSize</a></span>(int&nbsp;dsize)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#setDescriptorType-int-">setDescriptorType</a></span>(int&nbsp;dtype)</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#setDiffusivity-int-">setDiffusivity</a></span>(int&nbsp;diff)</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#setNOctaveLayers-int-">setNOctaveLayers</a></span>(int&nbsp;octaveLayers)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#setNOctaves-int-">setNOctaves</a></span>(int&nbsp;octaves)</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AKAZE.html#setThreshold-double-">setThreshold</a></span>(double&nbsp;threshold)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.features2d.Feature2D">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.features2d.<a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></h3>
<code><a href="../../../org/opencv/features2d/Feature2D.html#compute-java.util.List-java.util.List-java.util.List-">compute</a>, <a href="../../../org/opencv/features2d/Feature2D.html#compute-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-">compute</a>, <a href="../../../org/opencv/features2d/Feature2D.html#defaultNorm--">defaultNorm</a>, <a href="../../../org/opencv/features2d/Feature2D.html#descriptorSize--">descriptorSize</a>, <a href="../../../org/opencv/features2d/Feature2D.html#descriptorType--">descriptorType</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detect-java.util.List-java.util.List-">detect</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detect-java.util.List-java.util.List-java.util.List-">detect</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detect-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-">detect</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detect-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-">detect</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detectAndCompute-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-">detectAndCompute</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detectAndCompute-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-boolean-">detectAndCompute</a>, <a href="../../../org/opencv/features2d/Feature2D.html#empty--">empty</a>, <a href="../../../org/opencv/features2d/Feature2D.html#read-java.lang.String-">read</a>, <a href="../../../org/opencv/features2d/Feature2D.html#write-java.lang.String-">write</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="DESCRIPTOR_KAZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESCRIPTOR_KAZE</h4>
<pre>public static final&nbsp;int DESCRIPTOR_KAZE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.AKAZE.DESCRIPTOR_KAZE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DESCRIPTOR_KAZE_UPRIGHT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESCRIPTOR_KAZE_UPRIGHT</h4>
<pre>public static final&nbsp;int DESCRIPTOR_KAZE_UPRIGHT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.AKAZE.DESCRIPTOR_KAZE_UPRIGHT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DESCRIPTOR_MLDB">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESCRIPTOR_MLDB</h4>
<pre>public static final&nbsp;int DESCRIPTOR_MLDB</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.AKAZE.DESCRIPTOR_MLDB">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DESCRIPTOR_MLDB_UPRIGHT">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DESCRIPTOR_MLDB_UPRIGHT</h4>
<pre>public static final&nbsp;int DESCRIPTOR_MLDB_UPRIGHT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.AKAZE.DESCRIPTOR_MLDB_UPRIGHT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/AKAZE.html" title="class in org.opencv.features2d">AKAZE</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="create--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/AKAZE.html" title="class in org.opencv.features2d">AKAZE</a>&nbsp;create()</pre>
<div class="block">The AKAZE constructor

     DESCRIPTOR_KAZE_UPRIGHT, DESCRIPTOR_MLDB or DESCRIPTOR_MLDB_UPRIGHT.
     DIFF_CHARBONNIER</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/AKAZE.html" title="class in org.opencv.features2d">AKAZE</a>&nbsp;create(int&nbsp;descriptor_type)</pre>
<div class="block">The AKAZE constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>descriptor_type</code> - Type of the extracted descriptor: DESCRIPTOR_KAZE,
     DESCRIPTOR_KAZE_UPRIGHT, DESCRIPTOR_MLDB or DESCRIPTOR_MLDB_UPRIGHT.
     DIFF_CHARBONNIER</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/AKAZE.html" title="class in org.opencv.features2d">AKAZE</a>&nbsp;create(int&nbsp;descriptor_type,
                           int&nbsp;descriptor_size)</pre>
<div class="block">The AKAZE constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>descriptor_type</code> - Type of the extracted descriptor: DESCRIPTOR_KAZE,
     DESCRIPTOR_KAZE_UPRIGHT, DESCRIPTOR_MLDB or DESCRIPTOR_MLDB_UPRIGHT.</dd>
<dd><code>descriptor_size</code> - Size of the descriptor in bits. 0 -&gt; Full size
     DIFF_CHARBONNIER</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/AKAZE.html" title="class in org.opencv.features2d">AKAZE</a>&nbsp;create(int&nbsp;descriptor_type,
                           int&nbsp;descriptor_size,
                           int&nbsp;descriptor_channels)</pre>
<div class="block">The AKAZE constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>descriptor_type</code> - Type of the extracted descriptor: DESCRIPTOR_KAZE,
     DESCRIPTOR_KAZE_UPRIGHT, DESCRIPTOR_MLDB or DESCRIPTOR_MLDB_UPRIGHT.</dd>
<dd><code>descriptor_size</code> - Size of the descriptor in bits. 0 -&gt; Full size</dd>
<dd><code>descriptor_channels</code> - Number of channels in the descriptor (1, 2, 3)
     DIFF_CHARBONNIER</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-int-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/AKAZE.html" title="class in org.opencv.features2d">AKAZE</a>&nbsp;create(int&nbsp;descriptor_type,
                           int&nbsp;descriptor_size,
                           int&nbsp;descriptor_channels,
                           float&nbsp;threshold)</pre>
<div class="block">The AKAZE constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>descriptor_type</code> - Type of the extracted descriptor: DESCRIPTOR_KAZE,
     DESCRIPTOR_KAZE_UPRIGHT, DESCRIPTOR_MLDB or DESCRIPTOR_MLDB_UPRIGHT.</dd>
<dd><code>descriptor_size</code> - Size of the descriptor in bits. 0 -&gt; Full size</dd>
<dd><code>descriptor_channels</code> - Number of channels in the descriptor (1, 2, 3)</dd>
<dd><code>threshold</code> - Detector response threshold to accept point
     DIFF_CHARBONNIER</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-int-float-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/AKAZE.html" title="class in org.opencv.features2d">AKAZE</a>&nbsp;create(int&nbsp;descriptor_type,
                           int&nbsp;descriptor_size,
                           int&nbsp;descriptor_channels,
                           float&nbsp;threshold,
                           int&nbsp;nOctaves)</pre>
<div class="block">The AKAZE constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>descriptor_type</code> - Type of the extracted descriptor: DESCRIPTOR_KAZE,
     DESCRIPTOR_KAZE_UPRIGHT, DESCRIPTOR_MLDB or DESCRIPTOR_MLDB_UPRIGHT.</dd>
<dd><code>descriptor_size</code> - Size of the descriptor in bits. 0 -&gt; Full size</dd>
<dd><code>descriptor_channels</code> - Number of channels in the descriptor (1, 2, 3)</dd>
<dd><code>threshold</code> - Detector response threshold to accept point</dd>
<dd><code>nOctaves</code> - Maximum octave evolution of the image
     DIFF_CHARBONNIER</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-int-float-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/AKAZE.html" title="class in org.opencv.features2d">AKAZE</a>&nbsp;create(int&nbsp;descriptor_type,
                           int&nbsp;descriptor_size,
                           int&nbsp;descriptor_channels,
                           float&nbsp;threshold,
                           int&nbsp;nOctaves,
                           int&nbsp;nOctaveLayers)</pre>
<div class="block">The AKAZE constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>descriptor_type</code> - Type of the extracted descriptor: DESCRIPTOR_KAZE,
     DESCRIPTOR_KAZE_UPRIGHT, DESCRIPTOR_MLDB or DESCRIPTOR_MLDB_UPRIGHT.</dd>
<dd><code>descriptor_size</code> - Size of the descriptor in bits. 0 -&gt; Full size</dd>
<dd><code>descriptor_channels</code> - Number of channels in the descriptor (1, 2, 3)</dd>
<dd><code>threshold</code> - Detector response threshold to accept point</dd>
<dd><code>nOctaves</code> - Maximum octave evolution of the image</dd>
<dd><code>nOctaveLayers</code> - Default number of sublevels per scale level
     DIFF_CHARBONNIER</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-int-float-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/AKAZE.html" title="class in org.opencv.features2d">AKAZE</a>&nbsp;create(int&nbsp;descriptor_type,
                           int&nbsp;descriptor_size,
                           int&nbsp;descriptor_channels,
                           float&nbsp;threshold,
                           int&nbsp;nOctaves,
                           int&nbsp;nOctaveLayers,
                           int&nbsp;diffusivity)</pre>
<div class="block">The AKAZE constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>descriptor_type</code> - Type of the extracted descriptor: DESCRIPTOR_KAZE,
     DESCRIPTOR_KAZE_UPRIGHT, DESCRIPTOR_MLDB or DESCRIPTOR_MLDB_UPRIGHT.</dd>
<dd><code>descriptor_size</code> - Size of the descriptor in bits. 0 -&gt; Full size</dd>
<dd><code>descriptor_channels</code> - Number of channels in the descriptor (1, 2, 3)</dd>
<dd><code>threshold</code> - Detector response threshold to accept point</dd>
<dd><code>nOctaves</code> - Maximum octave evolution of the image</dd>
<dd><code>nOctaveLayers</code> - Default number of sublevels per scale level</dd>
<dd><code>diffusivity</code> - Diffusivity type. DIFF_PM_G1, DIFF_PM_G2, DIFF_WEICKERT or
     DIFF_CHARBONNIER</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getDefaultName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDefaultName()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">Algorithm</a></code></span></div>
<div class="block">Returns the algorithm string identifier.
 This string is used as top level xml/yml node tag when the object is saved to a file or string.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../org/opencv/features2d/Feature2D.html#getDefaultName--">getDefaultName</a></code>&nbsp;in class&nbsp;<code><a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getDescriptorChannels--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDescriptorChannels</h4>
<pre>public&nbsp;int&nbsp;getDescriptorChannels()</pre>
</li>
</ul>
<a name="getDescriptorSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDescriptorSize</h4>
<pre>public&nbsp;int&nbsp;getDescriptorSize()</pre>
</li>
</ul>
<a name="getDescriptorType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDescriptorType</h4>
<pre>public&nbsp;int&nbsp;getDescriptorType()</pre>
</li>
</ul>
<a name="getDiffusivity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDiffusivity</h4>
<pre>public&nbsp;int&nbsp;getDiffusivity()</pre>
</li>
</ul>
<a name="getNOctaveLayers--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNOctaveLayers</h4>
<pre>public&nbsp;int&nbsp;getNOctaveLayers()</pre>
</li>
</ul>
<a name="getNOctaves--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNOctaves</h4>
<pre>public&nbsp;int&nbsp;getNOctaves()</pre>
</li>
</ul>
<a name="getThreshold--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThreshold</h4>
<pre>public&nbsp;double&nbsp;getThreshold()</pre>
</li>
</ul>
<a name="setDescriptorChannels-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDescriptorChannels</h4>
<pre>public&nbsp;void&nbsp;setDescriptorChannels(int&nbsp;dch)</pre>
</li>
</ul>
<a name="setDescriptorSize-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDescriptorSize</h4>
<pre>public&nbsp;void&nbsp;setDescriptorSize(int&nbsp;dsize)</pre>
</li>
</ul>
<a name="setDescriptorType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDescriptorType</h4>
<pre>public&nbsp;void&nbsp;setDescriptorType(int&nbsp;dtype)</pre>
</li>
</ul>
<a name="setDiffusivity-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDiffusivity</h4>
<pre>public&nbsp;void&nbsp;setDiffusivity(int&nbsp;diff)</pre>
</li>
</ul>
<a name="setNOctaveLayers-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNOctaveLayers</h4>
<pre>public&nbsp;void&nbsp;setNOctaveLayers(int&nbsp;octaveLayers)</pre>
</li>
</ul>
<a name="setNOctaves-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNOctaves</h4>
<pre>public&nbsp;void&nbsp;setNOctaves(int&nbsp;octaves)</pre>
</li>
</ul>
<a name="setThreshold-double-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setThreshold</h4>
<pre>public&nbsp;void&nbsp;setThreshold(double&nbsp;threshold)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/features2d/AgastFeatureDetector.html" title="class in org.opencv.features2d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/features2d/BFMatcher.html" title="class in org.opencv.features2d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/AKAZE.html" target="_top">Frames</a></li>
<li><a href="AKAZE.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2021-12-25 08:13:27 / OpenCV 4.5.5</small></p>
</body>
</html>
