<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_292) on Sat Dec 25 08:13:28 UTC 2021 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>QRCodeDetector (OpenCV 4.5.5 Java documentation)</title>
<meta name="date" content="2021-12-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="QRCodeDetector (OpenCV 4.5.5 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/objdetect/Objdetect.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/objdetect/QRCodeEncoder.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/QRCodeDetector.html" target="_top">Frames</a></li>
<li><a href="QRCodeDetector.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.objdetect</div>
<h2 title="Class QRCodeDetector" class="title">Class QRCodeDetector</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.objdetect.QRCodeDetector</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">QRCodeDetector</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetector.html#QRCodeDetector--">QRCodeDetector</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/objdetect/QRCodeDetector.html" title="class in org.opencv.objdetect">QRCodeDetector</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetector.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetector.html#decode-org.opencv.core.Mat-org.opencv.core.Mat-">decode</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</code>
<div class="block">Decodes QR code in image once it's found by the detect() method.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetector.html#decode-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">decode</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;straight_qrcode)</code>
<div class="block">Decodes QR code in image once it's found by the detect() method.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetector.html#decodeCurved-org.opencv.core.Mat-org.opencv.core.Mat-">decodeCurved</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</code>
<div class="block">Decodes QR code on a curved surface in image once it's found by the detect() method.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetector.html#decodeCurved-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">decodeCurved</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;straight_qrcode)</code>
<div class="block">Decodes QR code on a curved surface in image once it's found by the detect() method.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetector.html#decodeMulti-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-">decodeMulti</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
           java.util.List&lt;java.lang.String&gt;&nbsp;decoded_info)</code>
<div class="block">Decodes QR codes in image once it's found by the detect() method.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetector.html#decodeMulti-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-java.util.List-">decodeMulti</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
           java.util.List&lt;java.lang.String&gt;&nbsp;decoded_info,
           java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;straight_qrcode)</code>
<div class="block">Decodes QR codes in image once it's found by the detect() method.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetector.html#detect-org.opencv.core.Mat-org.opencv.core.Mat-">detect</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</code>
<div class="block">Detects QR code in image and returns the quadrangle containing the code.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetector.html#detectAndDecode-org.opencv.core.Mat-">detectAndDecode</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img)</code>
<div class="block">Both detects and decodes QR code</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetector.html#detectAndDecode-org.opencv.core.Mat-org.opencv.core.Mat-">detectAndDecode</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</code>
<div class="block">Both detects and decodes QR code</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetector.html#detectAndDecode-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">detectAndDecode</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;straight_qrcode)</code>
<div class="block">Both detects and decodes QR code</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetector.html#detectAndDecodeCurved-org.opencv.core.Mat-">detectAndDecodeCurved</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img)</code>
<div class="block">Both detects and decodes QR code on a curved surface</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetector.html#detectAndDecodeCurved-org.opencv.core.Mat-org.opencv.core.Mat-">detectAndDecodeCurved</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</code>
<div class="block">Both detects and decodes QR code on a curved surface</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetector.html#detectAndDecodeCurved-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">detectAndDecodeCurved</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;straight_qrcode)</code>
<div class="block">Both detects and decodes QR code on a curved surface</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetector.html#detectAndDecodeMulti-org.opencv.core.Mat-java.util.List-">detectAndDecodeMulti</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                    java.util.List&lt;java.lang.String&gt;&nbsp;decoded_info)</code>
<div class="block">Both detects and decodes QR codes</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetector.html#detectAndDecodeMulti-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-">detectAndDecodeMulti</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                    java.util.List&lt;java.lang.String&gt;&nbsp;decoded_info,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</code>
<div class="block">Both detects and decodes QR codes</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetector.html#detectAndDecodeMulti-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-java.util.List-">detectAndDecodeMulti</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                    java.util.List&lt;java.lang.String&gt;&nbsp;decoded_info,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
                    java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;straight_qrcode)</code>
<div class="block">Both detects and decodes QR codes</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetector.html#detectMulti-org.opencv.core.Mat-org.opencv.core.Mat-">detectMulti</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</code>
<div class="block">Detects QR codes in image and returns the vector of the quadrangles containing the codes.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetector.html#getNativeObjAddr--">getNativeObjAddr</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetector.html#setEpsX-double-">setEpsX</a></span>(double&nbsp;epsX)</code>
<div class="block">sets the epsilon used during the horizontal scan of QR code stop marker detection.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetector.html#setEpsY-double-">setEpsY</a></span>(double&nbsp;epsY)</code>
<div class="block">sets the epsilon used during the vertical scan of QR code stop marker detection.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="QRCodeDetector--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>QRCodeDetector</h4>
<pre>public&nbsp;QRCodeDetector()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/objdetect/QRCodeDetector.html" title="class in org.opencv.objdetect">QRCodeDetector</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="decode-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decode</h4>
<pre>public&nbsp;java.lang.String&nbsp;decode(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</pre>
<div class="block">Decodes QR code in image once it's found by the detect() method.

      Returns UTF8-encoded output string or empty string if the code cannot be decoded.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - grayscale or color (BGR) image containing QR code.</dd>
<dd><code>points</code> - Quadrangle vertices found by detect() method (or some other algorithm).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="decode-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decode</h4>
<pre>public&nbsp;java.lang.String&nbsp;decode(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;straight_qrcode)</pre>
<div class="block">Decodes QR code in image once it's found by the detect() method.

      Returns UTF8-encoded output string or empty string if the code cannot be decoded.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - grayscale or color (BGR) image containing QR code.</dd>
<dd><code>points</code> - Quadrangle vertices found by detect() method (or some other algorithm).</dd>
<dd><code>straight_qrcode</code> - The optional output image containing rectified and binarized QR code</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="decodeCurved-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decodeCurved</h4>
<pre>public&nbsp;java.lang.String&nbsp;decodeCurved(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</pre>
<div class="block">Decodes QR code on a curved surface in image once it's found by the detect() method.

      Returns UTF8-encoded output string or empty string if the code cannot be decoded.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - grayscale or color (BGR) image containing QR code.</dd>
<dd><code>points</code> - Quadrangle vertices found by detect() method (or some other algorithm).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="decodeCurved-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decodeCurved</h4>
<pre>public&nbsp;java.lang.String&nbsp;decodeCurved(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;straight_qrcode)</pre>
<div class="block">Decodes QR code on a curved surface in image once it's found by the detect() method.

      Returns UTF8-encoded output string or empty string if the code cannot be decoded.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - grayscale or color (BGR) image containing QR code.</dd>
<dd><code>points</code> - Quadrangle vertices found by detect() method (or some other algorithm).</dd>
<dd><code>straight_qrcode</code> - The optional output image containing rectified and binarized QR code</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="decodeMulti-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decodeMulti</h4>
<pre>public&nbsp;boolean&nbsp;decodeMulti(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
                           java.util.List&lt;java.lang.String&gt;&nbsp;decoded_info)</pre>
<div class="block">Decodes QR codes in image once it's found by the detect() method.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - grayscale or color (BGR) image containing QR codes.</dd>
<dd><code>decoded_info</code> - UTF8-encoded output vector of string or empty vector of string if the codes cannot be decoded.</dd>
<dd><code>points</code> - vector of Quadrangle vertices found by detect() method (or some other algorithm).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="decodeMulti-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decodeMulti</h4>
<pre>public&nbsp;boolean&nbsp;decodeMulti(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
                           java.util.List&lt;java.lang.String&gt;&nbsp;decoded_info,
                           java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;straight_qrcode)</pre>
<div class="block">Decodes QR codes in image once it's found by the detect() method.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - grayscale or color (BGR) image containing QR codes.</dd>
<dd><code>decoded_info</code> - UTF8-encoded output vector of string or empty vector of string if the codes cannot be decoded.</dd>
<dd><code>points</code> - vector of Quadrangle vertices found by detect() method (or some other algorithm).</dd>
<dd><code>straight_qrcode</code> - The optional output vector of images containing rectified and binarized QR codes</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="detect-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detect</h4>
<pre>public&nbsp;boolean&nbsp;detect(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</pre>
<div class="block">Detects QR code in image and returns the quadrangle containing the code.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - grayscale or color (BGR) image containing (or not) QR code.</dd>
<dd><code>points</code> - Output vector of vertices of the minimum-area quadrangle containing the code.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="detectAndDecode-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectAndDecode</h4>
<pre>public&nbsp;java.lang.String&nbsp;detectAndDecode(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img)</pre>
<div class="block">Both detects and decodes QR code</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - grayscale or color (BGR) image containing QR code.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="detectAndDecode-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectAndDecode</h4>
<pre>public&nbsp;java.lang.String&nbsp;detectAndDecode(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</pre>
<div class="block">Both detects and decodes QR code</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - grayscale or color (BGR) image containing QR code.</dd>
<dd><code>points</code> - optional output array of vertices of the found QR code quadrangle. Will be empty if not found.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="detectAndDecode-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectAndDecode</h4>
<pre>public&nbsp;java.lang.String&nbsp;detectAndDecode(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;straight_qrcode)</pre>
<div class="block">Both detects and decodes QR code</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - grayscale or color (BGR) image containing QR code.</dd>
<dd><code>points</code> - optional output array of vertices of the found QR code quadrangle. Will be empty if not found.</dd>
<dd><code>straight_qrcode</code> - The optional output image containing rectified and binarized QR code</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="detectAndDecodeCurved-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectAndDecodeCurved</h4>
<pre>public&nbsp;java.lang.String&nbsp;detectAndDecodeCurved(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img)</pre>
<div class="block">Both detects and decodes QR code on a curved surface</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - grayscale or color (BGR) image containing QR code.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="detectAndDecodeCurved-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectAndDecodeCurved</h4>
<pre>public&nbsp;java.lang.String&nbsp;detectAndDecodeCurved(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</pre>
<div class="block">Both detects and decodes QR code on a curved surface</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - grayscale or color (BGR) image containing QR code.</dd>
<dd><code>points</code> - optional output array of vertices of the found QR code quadrangle. Will be empty if not found.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="detectAndDecodeCurved-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectAndDecodeCurved</h4>
<pre>public&nbsp;java.lang.String&nbsp;detectAndDecodeCurved(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
                                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;straight_qrcode)</pre>
<div class="block">Both detects and decodes QR code on a curved surface</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - grayscale or color (BGR) image containing QR code.</dd>
<dd><code>points</code> - optional output array of vertices of the found QR code quadrangle. Will be empty if not found.</dd>
<dd><code>straight_qrcode</code> - The optional output image containing rectified and binarized QR code</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="detectAndDecodeMulti-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectAndDecodeMulti</h4>
<pre>public&nbsp;boolean&nbsp;detectAndDecodeMulti(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                                    java.util.List&lt;java.lang.String&gt;&nbsp;decoded_info)</pre>
<div class="block">Both detects and decodes QR codes</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - grayscale or color (BGR) image containing QR codes.</dd>
<dd><code>decoded_info</code> - UTF8-encoded output vector of string or empty vector of string if the codes cannot be decoded.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="detectAndDecodeMulti-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectAndDecodeMulti</h4>
<pre>public&nbsp;boolean&nbsp;detectAndDecodeMulti(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                                    java.util.List&lt;java.lang.String&gt;&nbsp;decoded_info,
                                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</pre>
<div class="block">Both detects and decodes QR codes</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - grayscale or color (BGR) image containing QR codes.</dd>
<dd><code>decoded_info</code> - UTF8-encoded output vector of string or empty vector of string if the codes cannot be decoded.</dd>
<dd><code>points</code> - optional output vector of vertices of the found QR code quadrangles. Will be empty if not found.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="detectAndDecodeMulti-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectAndDecodeMulti</h4>
<pre>public&nbsp;boolean&nbsp;detectAndDecodeMulti(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                                    java.util.List&lt;java.lang.String&gt;&nbsp;decoded_info,
                                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
                                    java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;straight_qrcode)</pre>
<div class="block">Both detects and decodes QR codes</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - grayscale or color (BGR) image containing QR codes.</dd>
<dd><code>decoded_info</code> - UTF8-encoded output vector of string or empty vector of string if the codes cannot be decoded.</dd>
<dd><code>points</code> - optional output vector of vertices of the found QR code quadrangles. Will be empty if not found.</dd>
<dd><code>straight_qrcode</code> - The optional output vector of images containing rectified and binarized QR codes</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="detectMulti-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMulti</h4>
<pre>public&nbsp;boolean&nbsp;detectMulti(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points)</pre>
<div class="block">Detects QR codes in image and returns the vector of the quadrangles containing the codes.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - grayscale or color (BGR) image containing (or not) QR codes.</dd>
<dd><code>points</code> - Output vector of vector of vertices of the minimum-area quadrangle containing the codes.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getNativeObjAddr--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNativeObjAddr</h4>
<pre>public&nbsp;long&nbsp;getNativeObjAddr()</pre>
</li>
</ul>
<a name="setEpsX-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEpsX</h4>
<pre>public&nbsp;void&nbsp;setEpsX(double&nbsp;epsX)</pre>
<div class="block">sets the epsilon used during the horizontal scan of QR code stop marker detection.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>epsX</code> - Epsilon neighborhood, which allows you to determine the horizontal pattern
      of the scheme 1:1:3:1:1 according to QR code standard.</dd>
</dl>
</li>
</ul>
<a name="setEpsY-double-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setEpsY</h4>
<pre>public&nbsp;void&nbsp;setEpsY(double&nbsp;epsY)</pre>
<div class="block">sets the epsilon used during the vertical scan of QR code stop marker detection.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>epsY</code> - Epsilon neighborhood, which allows you to determine the vertical pattern
      of the scheme 1:1:3:1:1 according to QR code standard.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/objdetect/Objdetect.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/objdetect/QRCodeEncoder.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/QRCodeDetector.html" target="_top">Frames</a></li>
<li><a href="QRCodeDetector.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2021-12-25 08:13:27 / OpenCV 4.5.5</small></p>
</body>
</html>
