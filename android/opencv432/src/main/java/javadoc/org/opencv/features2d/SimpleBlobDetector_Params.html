<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_292) on Sat Dec 25 08:13:29 UTC 2021 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>SimpleBlobDetector_Params (OpenCV 4.5.5 Java documentation)</title>
<meta name="date" content="2021-12-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SimpleBlobDetector_Params (OpenCV 4.5.5 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/features2d/SimpleBlobDetector.html" title="class in org.opencv.features2d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/SimpleBlobDetector_Params.html" target="_top">Frames</a></li>
<li><a href="SimpleBlobDetector_Params.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.features2d</div>
<h2 title="Class SimpleBlobDetector_Params" class="title">Class SimpleBlobDetector_Params</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.features2d.SimpleBlobDetector_Params</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">SimpleBlobDetector_Params</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#SimpleBlobDetector_Params--">SimpleBlobDetector_Params</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html" title="class in org.opencv.features2d">SimpleBlobDetector_Params</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#get_filterByArea--">get_filterByArea</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#get_filterByCircularity--">get_filterByCircularity</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#get_filterByColor--">get_filterByColor</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#get_filterByConvexity--">get_filterByConvexity</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#get_filterByInertia--">get_filterByInertia</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#get_maxArea--">get_maxArea</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#get_maxCircularity--">get_maxCircularity</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#get_maxConvexity--">get_maxConvexity</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#get_maxInertiaRatio--">get_maxInertiaRatio</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#get_maxThreshold--">get_maxThreshold</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#get_minArea--">get_minArea</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#get_minCircularity--">get_minCircularity</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#get_minConvexity--">get_minConvexity</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#get_minDistBetweenBlobs--">get_minDistBetweenBlobs</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#get_minInertiaRatio--">get_minInertiaRatio</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#get_minRepeatability--">get_minRepeatability</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#get_minThreshold--">get_minThreshold</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#get_thresholdStep--">get_thresholdStep</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#getNativeObjAddr--">getNativeObjAddr</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#set_filterByArea-boolean-">set_filterByArea</a></span>(boolean&nbsp;filterByArea)</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#set_filterByCircularity-boolean-">set_filterByCircularity</a></span>(boolean&nbsp;filterByCircularity)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#set_filterByColor-boolean-">set_filterByColor</a></span>(boolean&nbsp;filterByColor)</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#set_filterByConvexity-boolean-">set_filterByConvexity</a></span>(boolean&nbsp;filterByConvexity)</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#set_filterByInertia-boolean-">set_filterByInertia</a></span>(boolean&nbsp;filterByInertia)</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#set_maxArea-float-">set_maxArea</a></span>(float&nbsp;maxArea)</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#set_maxCircularity-float-">set_maxCircularity</a></span>(float&nbsp;maxCircularity)</code>&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#set_maxConvexity-float-">set_maxConvexity</a></span>(float&nbsp;maxConvexity)</code>&nbsp;</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#set_maxInertiaRatio-float-">set_maxInertiaRatio</a></span>(float&nbsp;maxInertiaRatio)</code>&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#set_maxThreshold-float-">set_maxThreshold</a></span>(float&nbsp;maxThreshold)</code>&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#set_minArea-float-">set_minArea</a></span>(float&nbsp;minArea)</code>&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#set_minCircularity-float-">set_minCircularity</a></span>(float&nbsp;minCircularity)</code>&nbsp;</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#set_minConvexity-float-">set_minConvexity</a></span>(float&nbsp;minConvexity)</code>&nbsp;</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#set_minDistBetweenBlobs-float-">set_minDistBetweenBlobs</a></span>(float&nbsp;minDistBetweenBlobs)</code>&nbsp;</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#set_minInertiaRatio-float-">set_minInertiaRatio</a></span>(float&nbsp;minInertiaRatio)</code>&nbsp;</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#set_minRepeatability-long-">set_minRepeatability</a></span>(long&nbsp;minRepeatability)</code>&nbsp;</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#set_minThreshold-float-">set_minThreshold</a></span>(float&nbsp;minThreshold)</code>&nbsp;</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html#set_thresholdStep-float-">set_thresholdStep</a></span>(float&nbsp;thresholdStep)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="SimpleBlobDetector_Params--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SimpleBlobDetector_Params</h4>
<pre>public&nbsp;SimpleBlobDetector_Params()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html" title="class in org.opencv.features2d">SimpleBlobDetector_Params</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="get_filterByArea--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_filterByArea</h4>
<pre>public&nbsp;boolean&nbsp;get_filterByArea()</pre>
</li>
</ul>
<a name="get_filterByCircularity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_filterByCircularity</h4>
<pre>public&nbsp;boolean&nbsp;get_filterByCircularity()</pre>
</li>
</ul>
<a name="get_filterByColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_filterByColor</h4>
<pre>public&nbsp;boolean&nbsp;get_filterByColor()</pre>
</li>
</ul>
<a name="get_filterByConvexity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_filterByConvexity</h4>
<pre>public&nbsp;boolean&nbsp;get_filterByConvexity()</pre>
</li>
</ul>
<a name="get_filterByInertia--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_filterByInertia</h4>
<pre>public&nbsp;boolean&nbsp;get_filterByInertia()</pre>
</li>
</ul>
<a name="get_maxArea--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_maxArea</h4>
<pre>public&nbsp;float&nbsp;get_maxArea()</pre>
</li>
</ul>
<a name="get_maxCircularity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_maxCircularity</h4>
<pre>public&nbsp;float&nbsp;get_maxCircularity()</pre>
</li>
</ul>
<a name="get_maxConvexity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_maxConvexity</h4>
<pre>public&nbsp;float&nbsp;get_maxConvexity()</pre>
</li>
</ul>
<a name="get_maxInertiaRatio--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_maxInertiaRatio</h4>
<pre>public&nbsp;float&nbsp;get_maxInertiaRatio()</pre>
</li>
</ul>
<a name="get_maxThreshold--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_maxThreshold</h4>
<pre>public&nbsp;float&nbsp;get_maxThreshold()</pre>
</li>
</ul>
<a name="get_minArea--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_minArea</h4>
<pre>public&nbsp;float&nbsp;get_minArea()</pre>
</li>
</ul>
<a name="get_minCircularity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_minCircularity</h4>
<pre>public&nbsp;float&nbsp;get_minCircularity()</pre>
</li>
</ul>
<a name="get_minConvexity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_minConvexity</h4>
<pre>public&nbsp;float&nbsp;get_minConvexity()</pre>
</li>
</ul>
<a name="get_minDistBetweenBlobs--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_minDistBetweenBlobs</h4>
<pre>public&nbsp;float&nbsp;get_minDistBetweenBlobs()</pre>
</li>
</ul>
<a name="get_minInertiaRatio--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_minInertiaRatio</h4>
<pre>public&nbsp;float&nbsp;get_minInertiaRatio()</pre>
</li>
</ul>
<a name="get_minRepeatability--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_minRepeatability</h4>
<pre>public&nbsp;long&nbsp;get_minRepeatability()</pre>
</li>
</ul>
<a name="get_minThreshold--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_minThreshold</h4>
<pre>public&nbsp;float&nbsp;get_minThreshold()</pre>
</li>
</ul>
<a name="get_thresholdStep--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_thresholdStep</h4>
<pre>public&nbsp;float&nbsp;get_thresholdStep()</pre>
</li>
</ul>
<a name="getNativeObjAddr--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNativeObjAddr</h4>
<pre>public&nbsp;long&nbsp;getNativeObjAddr()</pre>
</li>
</ul>
<a name="set_filterByArea-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_filterByArea</h4>
<pre>public&nbsp;void&nbsp;set_filterByArea(boolean&nbsp;filterByArea)</pre>
</li>
</ul>
<a name="set_filterByCircularity-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_filterByCircularity</h4>
<pre>public&nbsp;void&nbsp;set_filterByCircularity(boolean&nbsp;filterByCircularity)</pre>
</li>
</ul>
<a name="set_filterByColor-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_filterByColor</h4>
<pre>public&nbsp;void&nbsp;set_filterByColor(boolean&nbsp;filterByColor)</pre>
</li>
</ul>
<a name="set_filterByConvexity-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_filterByConvexity</h4>
<pre>public&nbsp;void&nbsp;set_filterByConvexity(boolean&nbsp;filterByConvexity)</pre>
</li>
</ul>
<a name="set_filterByInertia-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_filterByInertia</h4>
<pre>public&nbsp;void&nbsp;set_filterByInertia(boolean&nbsp;filterByInertia)</pre>
</li>
</ul>
<a name="set_maxArea-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_maxArea</h4>
<pre>public&nbsp;void&nbsp;set_maxArea(float&nbsp;maxArea)</pre>
</li>
</ul>
<a name="set_maxCircularity-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_maxCircularity</h4>
<pre>public&nbsp;void&nbsp;set_maxCircularity(float&nbsp;maxCircularity)</pre>
</li>
</ul>
<a name="set_maxConvexity-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_maxConvexity</h4>
<pre>public&nbsp;void&nbsp;set_maxConvexity(float&nbsp;maxConvexity)</pre>
</li>
</ul>
<a name="set_maxInertiaRatio-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_maxInertiaRatio</h4>
<pre>public&nbsp;void&nbsp;set_maxInertiaRatio(float&nbsp;maxInertiaRatio)</pre>
</li>
</ul>
<a name="set_maxThreshold-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_maxThreshold</h4>
<pre>public&nbsp;void&nbsp;set_maxThreshold(float&nbsp;maxThreshold)</pre>
</li>
</ul>
<a name="set_minArea-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_minArea</h4>
<pre>public&nbsp;void&nbsp;set_minArea(float&nbsp;minArea)</pre>
</li>
</ul>
<a name="set_minCircularity-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_minCircularity</h4>
<pre>public&nbsp;void&nbsp;set_minCircularity(float&nbsp;minCircularity)</pre>
</li>
</ul>
<a name="set_minConvexity-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_minConvexity</h4>
<pre>public&nbsp;void&nbsp;set_minConvexity(float&nbsp;minConvexity)</pre>
</li>
</ul>
<a name="set_minDistBetweenBlobs-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_minDistBetweenBlobs</h4>
<pre>public&nbsp;void&nbsp;set_minDistBetweenBlobs(float&nbsp;minDistBetweenBlobs)</pre>
</li>
</ul>
<a name="set_minInertiaRatio-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_minInertiaRatio</h4>
<pre>public&nbsp;void&nbsp;set_minInertiaRatio(float&nbsp;minInertiaRatio)</pre>
</li>
</ul>
<a name="set_minRepeatability-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_minRepeatability</h4>
<pre>public&nbsp;void&nbsp;set_minRepeatability(long&nbsp;minRepeatability)</pre>
</li>
</ul>
<a name="set_minThreshold-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_minThreshold</h4>
<pre>public&nbsp;void&nbsp;set_minThreshold(float&nbsp;minThreshold)</pre>
</li>
</ul>
<a name="set_thresholdStep-float-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>set_thresholdStep</h4>
<pre>public&nbsp;void&nbsp;set_thresholdStep(float&nbsp;thresholdStep)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/features2d/SimpleBlobDetector.html" title="class in org.opencv.features2d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/SimpleBlobDetector_Params.html" target="_top">Frames</a></li>
<li><a href="SimpleBlobDetector_Params.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2021-12-25 08:13:27 / OpenCV 4.5.5</small></p>
</body>
</html>
