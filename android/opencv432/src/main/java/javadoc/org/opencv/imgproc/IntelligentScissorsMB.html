<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_292) on Sat Dec 25 08:13:30 UTC 2021 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>IntelligentScissorsMB (OpenCV 4.5.5 Java documentation)</title>
<meta name="date" content="2021-12-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IntelligentScissorsMB (OpenCV 4.5.5 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/imgproc/Imgproc.html" title="class in org.opencv.imgproc"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/imgproc/LineSegmentDetector.html" title="class in org.opencv.imgproc"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/imgproc/IntelligentScissorsMB.html" target="_top">Frames</a></li>
<li><a href="IntelligentScissorsMB.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.imgproc</div>
<h2 title="Class IntelligentScissorsMB" class="title">Class IntelligentScissorsMB</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.imgproc.IntelligentScissorsMB</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">IntelligentScissorsMB</span>
extends java.lang.Object</pre>
<div class="block">Intelligent Scissors image segmentation

 This class is used to find the path (contour) between two points
 which can be used for image segmentation.

 Usage example:
 SNIPPET: snippets/imgproc_segmentation.cpp usage_example_intelligent_scissors

 Reference: &lt;a href="http://citeseerx.ist.psu.edu/viewdoc/download?doi=**********.3811&amp;rep=rep1&amp;type=pdf"&gt;"Intelligent Scissors for Image Composition"&lt;/a&gt;
 algorithm designed by Eric N. Mortensen and William A. Barrett, Brigham Young University
 CITE: Mortensen95intelligentscissors</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html#IntelligentScissorsMB--">IntelligentScissorsMB</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html#applyImage-org.opencv.core.Mat-">applyImage</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</code>
<div class="block">Specify input image and extract image features</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html#applyImageFeatures-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">applyImageFeatures</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;non_edge,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;gradient_direction,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;gradient_magnitude)</code>
<div class="block">Specify custom features of imput image

 Customized advanced variant of applyImage() call.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html#applyImageFeatures-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">applyImageFeatures</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;non_edge,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;gradient_direction,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;gradient_magnitude,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</code>
<div class="block">Specify custom features of imput image

 Customized advanced variant of applyImage() call.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html#buildMap-org.opencv.core.Point-">buildMap</a></span>(<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;sourcePt)</code>
<div class="block">Prepares a map of optimal paths for the given source point on the image

 <b>Note:</b> applyImage() / applyImageFeatures() must be called before this call</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html#getContour-org.opencv.core.Point-org.opencv.core.Mat-">getContour</a></span>(<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;targetPt,
          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;contour)</code>
<div class="block">Extracts optimal contour for the given target point on the image

 <b>Note:</b> buildMap() must be called before this call</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html#getContour-org.opencv.core.Point-org.opencv.core.Mat-boolean-">getContour</a></span>(<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;targetPt,
          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;contour,
          boolean&nbsp;backward)</code>
<div class="block">Extracts optimal contour for the given target point on the image

 <b>Note:</b> buildMap() must be called before this call</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html#getNativeObjAddr--">getNativeObjAddr</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html#setEdgeFeatureCannyParameters-double-double-">setEdgeFeatureCannyParameters</a></span>(double&nbsp;threshold1,
                             double&nbsp;threshold2)</code>
<div class="block">Switch edge feature extractor to use Canny edge detector

 <b>Note:</b> "Laplacian Zero-Crossing" feature extractor is used by default (following to original article)

 SEE: Canny</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html#setEdgeFeatureCannyParameters-double-double-int-">setEdgeFeatureCannyParameters</a></span>(double&nbsp;threshold1,
                             double&nbsp;threshold2,
                             int&nbsp;apertureSize)</code>
<div class="block">Switch edge feature extractor to use Canny edge detector

 <b>Note:</b> "Laplacian Zero-Crossing" feature extractor is used by default (following to original article)

 SEE: Canny</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html#setEdgeFeatureCannyParameters-double-double-int-boolean-">setEdgeFeatureCannyParameters</a></span>(double&nbsp;threshold1,
                             double&nbsp;threshold2,
                             int&nbsp;apertureSize,
                             boolean&nbsp;L2gradient)</code>
<div class="block">Switch edge feature extractor to use Canny edge detector

 <b>Note:</b> "Laplacian Zero-Crossing" feature extractor is used by default (following to original article)

 SEE: Canny</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html#setEdgeFeatureZeroCrossingParameters--">setEdgeFeatureZeroCrossingParameters</a></span>()</code>
<div class="block">Switch to "Laplacian Zero-Crossing" edge feature extractor and specify its parameters

 This feature extractor is used by default according to article.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html#setEdgeFeatureZeroCrossingParameters-float-">setEdgeFeatureZeroCrossingParameters</a></span>(float&nbsp;gradient_magnitude_min_value)</code>
<div class="block">Switch to "Laplacian Zero-Crossing" edge feature extractor and specify its parameters

 This feature extractor is used by default according to article.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html#setGradientMagnitudeMaxLimit--">setGradientMagnitudeMaxLimit</a></span>()</code>
<div class="block">Specify gradient magnitude max value threshold

 Zero limit value is used to disable gradient magnitude thresholding (default behavior, as described in original article).</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html#setGradientMagnitudeMaxLimit-float-">setGradientMagnitudeMaxLimit</a></span>(float&nbsp;gradient_magnitude_threshold_max)</code>
<div class="block">Specify gradient magnitude max value threshold

 Zero limit value is used to disable gradient magnitude thresholding (default behavior, as described in original article).</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html#setWeights-float-float-float-">setWeights</a></span>(float&nbsp;weight_non_edge,
          float&nbsp;weight_gradient_direction,
          float&nbsp;weight_gradient_magnitude)</code>
<div class="block">Specify weights of feature functions

 Consider keeping weights normalized (sum of weights equals to 1.0)
 Discrete dynamic programming (DP) goal is minimization of costs between pixels.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="IntelligentScissorsMB--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>IntelligentScissorsMB</h4>
<pre>public&nbsp;IntelligentScissorsMB()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="applyImage-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>applyImage</h4>
<pre>public&nbsp;<a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a>&nbsp;applyImage(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</pre>
<div class="block">Specify input image and extract image features</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input image. Type is #CV_8UC1 / #CV_8UC3</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="applyImageFeatures-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>applyImageFeatures</h4>
<pre>public&nbsp;<a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a>&nbsp;applyImageFeatures(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;non_edge,
                                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;gradient_direction,
                                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;gradient_magnitude)</pre>
<div class="block">Specify custom features of imput image

 Customized advanced variant of applyImage() call.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>non_edge</code> - Specify cost of non-edge pixels. Type is CV_8UC1. Expected values are <code>{0, 1}</code>.</dd>
<dd><code>gradient_direction</code> - Specify gradient direction feature. Type is CV_32FC2. Values are expected to be normalized: <code>x^2 + y^2 == 1</code></dd>
<dd><code>gradient_magnitude</code> - Specify cost of gradient magnitude function: Type is CV_32FC1. Values should be in range <code>[0, 1]</code>.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="applyImageFeatures-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>applyImageFeatures</h4>
<pre>public&nbsp;<a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a>&nbsp;applyImageFeatures(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;non_edge,
                                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;gradient_direction,
                                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;gradient_magnitude,
                                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</pre>
<div class="block">Specify custom features of imput image

 Customized advanced variant of applyImage() call.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>non_edge</code> - Specify cost of non-edge pixels. Type is CV_8UC1. Expected values are <code>{0, 1}</code>.</dd>
<dd><code>gradient_direction</code> - Specify gradient direction feature. Type is CV_32FC2. Values are expected to be normalized: <code>x^2 + y^2 == 1</code></dd>
<dd><code>gradient_magnitude</code> - Specify cost of gradient magnitude function: Type is CV_32FC1. Values should be in range <code>[0, 1]</code>.</dd>
<dd><code>image</code> - <b>Optional parameter</b>. Must be specified if subset of features is specified (non-specified features are calculated internally)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="buildMap-org.opencv.core.Point-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>buildMap</h4>
<pre>public&nbsp;void&nbsp;buildMap(<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;sourcePt)</pre>
<div class="block">Prepares a map of optimal paths for the given source point on the image

 <b>Note:</b> applyImage() / applyImageFeatures() must be called before this call</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourcePt</code> - The source point used to find the paths</dd>
</dl>
</li>
</ul>
<a name="getContour-org.opencv.core.Point-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContour</h4>
<pre>public&nbsp;void&nbsp;getContour(<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;targetPt,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;contour)</pre>
<div class="block">Extracts optimal contour for the given target point on the image

 <b>Note:</b> buildMap() must be called before this call</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>targetPt</code> - The target point</dd>
<dd><code>contour</code> - The list of pixels which contains optimal path between the source and the target points of the image. Type is CV_32SC2 (compatible with <code>std::vector&amp;lt;Point&amp;gt;</code>)</dd>
</dl>
</li>
</ul>
<a name="getContour-org.opencv.core.Point-org.opencv.core.Mat-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContour</h4>
<pre>public&nbsp;void&nbsp;getContour(<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;targetPt,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;contour,
                       boolean&nbsp;backward)</pre>
<div class="block">Extracts optimal contour for the given target point on the image

 <b>Note:</b> buildMap() must be called before this call</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>targetPt</code> - The target point</dd>
<dd><code>contour</code> - The list of pixels which contains optimal path between the source and the target points of the image. Type is CV_32SC2 (compatible with <code>std::vector&amp;lt;Point&amp;gt;</code>)</dd>
<dd><code>backward</code> - Flag to indicate reverse order of retrived pixels (use "true" value to fetch points from the target to the source point)</dd>
</dl>
</li>
</ul>
<a name="getNativeObjAddr--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNativeObjAddr</h4>
<pre>public&nbsp;long&nbsp;getNativeObjAddr()</pre>
</li>
</ul>
<a name="setEdgeFeatureCannyParameters-double-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEdgeFeatureCannyParameters</h4>
<pre>public&nbsp;<a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a>&nbsp;setEdgeFeatureCannyParameters(double&nbsp;threshold1,
                                                           double&nbsp;threshold2)</pre>
<div class="block">Switch edge feature extractor to use Canny edge detector

 <b>Note:</b> "Laplacian Zero-Crossing" feature extractor is used by default (following to original article)

 SEE: Canny</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>threshold1</code> - automatically generated</dd>
<dd><code>threshold2</code> - automatically generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setEdgeFeatureCannyParameters-double-double-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEdgeFeatureCannyParameters</h4>
<pre>public&nbsp;<a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a>&nbsp;setEdgeFeatureCannyParameters(double&nbsp;threshold1,
                                                           double&nbsp;threshold2,
                                                           int&nbsp;apertureSize)</pre>
<div class="block">Switch edge feature extractor to use Canny edge detector

 <b>Note:</b> "Laplacian Zero-Crossing" feature extractor is used by default (following to original article)

 SEE: Canny</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>threshold1</code> - automatically generated</dd>
<dd><code>threshold2</code> - automatically generated</dd>
<dd><code>apertureSize</code> - automatically generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setEdgeFeatureCannyParameters-double-double-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEdgeFeatureCannyParameters</h4>
<pre>public&nbsp;<a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a>&nbsp;setEdgeFeatureCannyParameters(double&nbsp;threshold1,
                                                           double&nbsp;threshold2,
                                                           int&nbsp;apertureSize,
                                                           boolean&nbsp;L2gradient)</pre>
<div class="block">Switch edge feature extractor to use Canny edge detector

 <b>Note:</b> "Laplacian Zero-Crossing" feature extractor is used by default (following to original article)

 SEE: Canny</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>threshold1</code> - automatically generated</dd>
<dd><code>threshold2</code> - automatically generated</dd>
<dd><code>apertureSize</code> - automatically generated</dd>
<dd><code>L2gradient</code> - automatically generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setEdgeFeatureZeroCrossingParameters--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEdgeFeatureZeroCrossingParameters</h4>
<pre>public&nbsp;<a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a>&nbsp;setEdgeFeatureZeroCrossingParameters()</pre>
<div class="block">Switch to "Laplacian Zero-Crossing" edge feature extractor and specify its parameters

 This feature extractor is used by default according to article.

 Implementation has additional filtering for regions with low-amplitude noise.
 This filtering is enabled through parameter of minimal gradient amplitude (use some small value 4, 8, 16).

 <b>Note:</b> Current implementation of this feature extractor is based on processing of grayscale images (color image is converted to grayscale image first).

 <b>Note:</b> Canny edge detector is a bit slower, but provides better results (especially on color images): use setEdgeFeatureCannyParameters().</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setEdgeFeatureZeroCrossingParameters-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEdgeFeatureZeroCrossingParameters</h4>
<pre>public&nbsp;<a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a>&nbsp;setEdgeFeatureZeroCrossingParameters(float&nbsp;gradient_magnitude_min_value)</pre>
<div class="block">Switch to "Laplacian Zero-Crossing" edge feature extractor and specify its parameters

 This feature extractor is used by default according to article.

 Implementation has additional filtering for regions with low-amplitude noise.
 This filtering is enabled through parameter of minimal gradient amplitude (use some small value 4, 8, 16).

 <b>Note:</b> Current implementation of this feature extractor is based on processing of grayscale images (color image is converted to grayscale image first).

 <b>Note:</b> Canny edge detector is a bit slower, but provides better results (especially on color images): use setEdgeFeatureCannyParameters().</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>gradient_magnitude_min_value</code> - Minimal gradient magnitude value for edge pixels (default: 0, check is disabled)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setGradientMagnitudeMaxLimit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGradientMagnitudeMaxLimit</h4>
<pre>public&nbsp;<a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a>&nbsp;setGradientMagnitudeMaxLimit()</pre>
<div class="block">Specify gradient magnitude max value threshold

 Zero limit value is used to disable gradient magnitude thresholding (default behavior, as described in original article).
 Otherwize pixels with <code>gradient magnitude &amp;gt;= threshold</code> have zero cost.

 <b>Note:</b> Thresholding should be used for images with irregular regions (to avoid stuck on parameters from high-contract areas, like embedded logos).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setGradientMagnitudeMaxLimit-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGradientMagnitudeMaxLimit</h4>
<pre>public&nbsp;<a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a>&nbsp;setGradientMagnitudeMaxLimit(float&nbsp;gradient_magnitude_threshold_max)</pre>
<div class="block">Specify gradient magnitude max value threshold

 Zero limit value is used to disable gradient magnitude thresholding (default behavior, as described in original article).
 Otherwize pixels with <code>gradient magnitude &amp;gt;= threshold</code> have zero cost.

 <b>Note:</b> Thresholding should be used for images with irregular regions (to avoid stuck on parameters from high-contract areas, like embedded logos).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>gradient_magnitude_threshold_max</code> - Specify gradient magnitude max value threshold (default: 0, disabled)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setWeights-float-float-float-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setWeights</h4>
<pre>public&nbsp;<a href="../../../org/opencv/imgproc/IntelligentScissorsMB.html" title="class in org.opencv.imgproc">IntelligentScissorsMB</a>&nbsp;setWeights(float&nbsp;weight_non_edge,
                                        float&nbsp;weight_gradient_direction,
                                        float&nbsp;weight_gradient_magnitude)</pre>
<div class="block">Specify weights of feature functions

 Consider keeping weights normalized (sum of weights equals to 1.0)
 Discrete dynamic programming (DP) goal is minimization of costs between pixels.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>weight_non_edge</code> - Specify cost of non-edge pixels (default: 0.43f)</dd>
<dd><code>weight_gradient_direction</code> - Specify cost of gradient direction function (default: 0.43f)</dd>
<dd><code>weight_gradient_magnitude</code> - Specify cost of gradient magnitude function (default: 0.14f)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/imgproc/Imgproc.html" title="class in org.opencv.imgproc"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/imgproc/LineSegmentDetector.html" title="class in org.opencv.imgproc"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/imgproc/IntelligentScissorsMB.html" target="_top">Frames</a></li>
<li><a href="IntelligentScissorsMB.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2021-12-25 08:13:27 / OpenCV 4.5.5</small></p>
</body>
</html>
