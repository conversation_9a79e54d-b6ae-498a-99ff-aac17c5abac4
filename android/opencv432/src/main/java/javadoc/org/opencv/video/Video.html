<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_292) on Sat Dec 25 08:13:28 UTC 2021 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Video (OpenCV 4.5.5 Java documentation)</title>
<meta name="date" content="2021-12-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Video (OpenCV 4.5.5 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9,"i16":9,"i17":9,"i18":9,"i19":9,"i20":9,"i21":9,"i22":9,"i23":9,"i24":9,"i25":9,"i26":9,"i27":9,"i28":9,"i29":9,"i30":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/video/VariationalRefinement.html" title="class in org.opencv.video"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/video/Video.html" target="_top">Frames</a></li>
<li><a href="Video.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.video</div>
<h2 title="Class Video" class="title">Class Video</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.video.Video</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Video</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#MOTION_AFFINE">MOTION_AFFINE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#MOTION_EUCLIDEAN">MOTION_EUCLIDEAN</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#MOTION_HOMOGRAPHY">MOTION_HOMOGRAPHY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#MOTION_TRANSLATION">MOTION_TRANSLATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#OPTFLOW_FARNEBACK_GAUSSIAN">OPTFLOW_FARNEBACK_GAUSSIAN</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#OPTFLOW_LK_GET_MIN_EIGENVALS">OPTFLOW_LK_GET_MIN_EIGENVALS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#OPTFLOW_USE_INITIAL_FLOW">OPTFLOW_USE_INITIAL_FLOW</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#TrackerSamplerCSC_MODE_DETECT">TrackerSamplerCSC_MODE_DETECT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#TrackerSamplerCSC_MODE_INIT_NEG">TrackerSamplerCSC_MODE_INIT_NEG</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#TrackerSamplerCSC_MODE_INIT_POS">TrackerSamplerCSC_MODE_INIT_POS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#TrackerSamplerCSC_MODE_TRACK_NEG">TrackerSamplerCSC_MODE_TRACK_NEG</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#TrackerSamplerCSC_MODE_TRACK_POS">TrackerSamplerCSC_MODE_TRACK_POS</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#Video--">Video</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#buildOpticalFlowPyramid-org.opencv.core.Mat-java.util.List-org.opencv.core.Size-int-">buildOpticalFlowPyramid</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                       java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;pyramid,
                       <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                       int&nbsp;maxLevel)</code>
<div class="block">Constructs the image pyramid which can be passed to calcOpticalFlowPyrLK.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#buildOpticalFlowPyramid-org.opencv.core.Mat-java.util.List-org.opencv.core.Size-int-boolean-">buildOpticalFlowPyramid</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                       java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;pyramid,
                       <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                       int&nbsp;maxLevel,
                       boolean&nbsp;withDerivatives)</code>
<div class="block">Constructs the image pyramid which can be passed to calcOpticalFlowPyrLK.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#buildOpticalFlowPyramid-org.opencv.core.Mat-java.util.List-org.opencv.core.Size-int-boolean-int-">buildOpticalFlowPyramid</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                       java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;pyramid,
                       <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                       int&nbsp;maxLevel,
                       boolean&nbsp;withDerivatives,
                       int&nbsp;pyrBorder)</code>
<div class="block">Constructs the image pyramid which can be passed to calcOpticalFlowPyrLK.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#buildOpticalFlowPyramid-org.opencv.core.Mat-java.util.List-org.opencv.core.Size-int-boolean-int-int-">buildOpticalFlowPyramid</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                       java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;pyramid,
                       <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                       int&nbsp;maxLevel,
                       boolean&nbsp;withDerivatives,
                       int&nbsp;pyrBorder,
                       int&nbsp;derivBorder)</code>
<div class="block">Constructs the image pyramid which can be passed to calcOpticalFlowPyrLK.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#buildOpticalFlowPyramid-org.opencv.core.Mat-java.util.List-org.opencv.core.Size-int-boolean-int-int-boolean-">buildOpticalFlowPyramid</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                       java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;pyramid,
                       <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                       int&nbsp;maxLevel,
                       boolean&nbsp;withDerivatives,
                       int&nbsp;pyrBorder,
                       int&nbsp;derivBorder,
                       boolean&nbsp;tryReuseInputImage)</code>
<div class="block">Constructs the image pyramid which can be passed to calcOpticalFlowPyrLK.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#calcOpticalFlowFarneback-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-int-int-int-int-double-int-">calcOpticalFlowFarneback</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prev,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;next,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;flow,
                        double&nbsp;pyr_scale,
                        int&nbsp;levels,
                        int&nbsp;winsize,
                        int&nbsp;iterations,
                        int&nbsp;poly_n,
                        double&nbsp;poly_sigma,
                        int&nbsp;flags)</code>
<div class="block">Computes a dense optical flow using the Gunnar Farneback's algorithm.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#calcOpticalFlowPyrLK-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfByte-org.opencv.core.MatOfFloat-">calcOpticalFlowPyrLK</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
                    <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
                    <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
                    <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
                    <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err)</code>
<div class="block">Calculates an optical flow for a sparse feature set using the iterative Lucas-Kanade method with
 pyramids.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#calcOpticalFlowPyrLK-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfByte-org.opencv.core.MatOfFloat-org.opencv.core.Size-">calcOpticalFlowPyrLK</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
                    <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
                    <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
                    <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
                    <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err,
                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize)</code>
<div class="block">Calculates an optical flow for a sparse feature set using the iterative Lucas-Kanade method with
 pyramids.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#calcOpticalFlowPyrLK-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfByte-org.opencv.core.MatOfFloat-org.opencv.core.Size-int-">calcOpticalFlowPyrLK</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
                    <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
                    <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
                    <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
                    <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err,
                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                    int&nbsp;maxLevel)</code>
<div class="block">Calculates an optical flow for a sparse feature set using the iterative Lucas-Kanade method with
 pyramids.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#calcOpticalFlowPyrLK-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfByte-org.opencv.core.MatOfFloat-org.opencv.core.Size-int-org.opencv.core.TermCriteria-">calcOpticalFlowPyrLK</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
                    <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
                    <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
                    <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
                    <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err,
                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                    int&nbsp;maxLevel,
                    <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</code>
<div class="block">Calculates an optical flow for a sparse feature set using the iterative Lucas-Kanade method with
 pyramids.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#calcOpticalFlowPyrLK-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfByte-org.opencv.core.MatOfFloat-org.opencv.core.Size-int-org.opencv.core.TermCriteria-int-">calcOpticalFlowPyrLK</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
                    <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
                    <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
                    <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
                    <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err,
                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                    int&nbsp;maxLevel,
                    <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria,
                    int&nbsp;flags)</code>
<div class="block">Calculates an optical flow for a sparse feature set using the iterative Lucas-Kanade method with
 pyramids.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#calcOpticalFlowPyrLK-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfByte-org.opencv.core.MatOfFloat-org.opencv.core.Size-int-org.opencv.core.TermCriteria-int-double-">calcOpticalFlowPyrLK</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
                    <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
                    <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
                    <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
                    <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err,
                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                    int&nbsp;maxLevel,
                    <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria,
                    int&nbsp;flags,
                    double&nbsp;minEigThreshold)</code>
<div class="block">Calculates an optical flow for a sparse feature set using the iterative Lucas-Kanade method with
 pyramids.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/RotatedRect.html" title="class in org.opencv.core">RotatedRect</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#CamShift-org.opencv.core.Mat-org.opencv.core.Rect-org.opencv.core.TermCriteria-">CamShift</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probImage,
        <a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;window,
        <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</code>
<div class="block">Finds an object center, size, and orientation.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#computeECC-org.opencv.core.Mat-org.opencv.core.Mat-">computeECC</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage)</code>
<div class="block">Computes the Enhanced Correlation Coefficient value between two images CITE: EP08 .</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#computeECC-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">computeECC</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputMask)</code>
<div class="block">Computes the Enhanced Correlation Coefficient value between two images CITE: EP08 .</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/BackgroundSubtractorKNN.html" title="class in org.opencv.video">BackgroundSubtractorKNN</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#createBackgroundSubtractorKNN--">createBackgroundSubtractorKNN</a></span>()</code>
<div class="block">Creates KNN Background Subtractor

 whether a pixel is close to that sample.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/BackgroundSubtractorKNN.html" title="class in org.opencv.video">BackgroundSubtractorKNN</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#createBackgroundSubtractorKNN-int-">createBackgroundSubtractorKNN</a></span>(int&nbsp;history)</code>
<div class="block">Creates KNN Background Subtractor</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/BackgroundSubtractorKNN.html" title="class in org.opencv.video">BackgroundSubtractorKNN</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#createBackgroundSubtractorKNN-int-double-">createBackgroundSubtractorKNN</a></span>(int&nbsp;history,
                             double&nbsp;dist2Threshold)</code>
<div class="block">Creates KNN Background Subtractor</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/BackgroundSubtractorKNN.html" title="class in org.opencv.video">BackgroundSubtractorKNN</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#createBackgroundSubtractorKNN-int-double-boolean-">createBackgroundSubtractorKNN</a></span>(int&nbsp;history,
                             double&nbsp;dist2Threshold,
                             boolean&nbsp;detectShadows)</code>
<div class="block">Creates KNN Background Subtractor</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html" title="class in org.opencv.video">BackgroundSubtractorMOG2</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#createBackgroundSubtractorMOG2--">createBackgroundSubtractorMOG2</a></span>()</code>
<div class="block">Creates MOG2 Background Subtractor

 to decide whether a pixel is well described by the background model.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html" title="class in org.opencv.video">BackgroundSubtractorMOG2</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#createBackgroundSubtractorMOG2-int-">createBackgroundSubtractorMOG2</a></span>(int&nbsp;history)</code>
<div class="block">Creates MOG2 Background Subtractor</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html" title="class in org.opencv.video">BackgroundSubtractorMOG2</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#createBackgroundSubtractorMOG2-int-double-">createBackgroundSubtractorMOG2</a></span>(int&nbsp;history,
                              double&nbsp;varThreshold)</code>
<div class="block">Creates MOG2 Background Subtractor</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html" title="class in org.opencv.video">BackgroundSubtractorMOG2</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#createBackgroundSubtractorMOG2-int-double-boolean-">createBackgroundSubtractorMOG2</a></span>(int&nbsp;history,
                              double&nbsp;varThreshold,
                              boolean&nbsp;detectShadows)</code>
<div class="block">Creates MOG2 Background Subtractor</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#findTransformECC-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">findTransformECC</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix)</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#findTransformECC-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-">findTransformECC</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix,
                int&nbsp;motionType)</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#findTransformECC-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-org.opencv.core.TermCriteria-">findTransformECC</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix,
                int&nbsp;motionType,
                <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#findTransformECC-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-org.opencv.core.TermCriteria-org.opencv.core.Mat-">findTransformECC</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix,
                int&nbsp;motionType,
                <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputMask)</code>&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#findTransformECC-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-org.opencv.core.TermCriteria-org.opencv.core.Mat-int-">findTransformECC</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix,
                int&nbsp;motionType,
                <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputMask,
                int&nbsp;gaussFiltSize)</code>
<div class="block">Finds the geometric transform (warp) between two images in terms of the ECC criterion CITE: EP08 .</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#meanShift-org.opencv.core.Mat-org.opencv.core.Rect-org.opencv.core.TermCriteria-">meanShift</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probImage,
         <a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;window,
         <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</code>
<div class="block">Finds an object on a back projection image.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#readOpticalFlow-java.lang.String-">readOpticalFlow</a></span>(java.lang.String&nbsp;path)</code>
<div class="block">Read a .flo file</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#writeOpticalFlow-java.lang.String-org.opencv.core.Mat-">writeOpticalFlow</a></span>(java.lang.String&nbsp;path,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;flow)</code>
<div class="block">Write a .flo to disk</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="MOTION_AFFINE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MOTION_AFFINE</h4>
<pre>public static final&nbsp;int MOTION_AFFINE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.video.Video.MOTION_AFFINE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MOTION_EUCLIDEAN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MOTION_EUCLIDEAN</h4>
<pre>public static final&nbsp;int MOTION_EUCLIDEAN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.video.Video.MOTION_EUCLIDEAN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MOTION_HOMOGRAPHY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MOTION_HOMOGRAPHY</h4>
<pre>public static final&nbsp;int MOTION_HOMOGRAPHY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.video.Video.MOTION_HOMOGRAPHY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MOTION_TRANSLATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MOTION_TRANSLATION</h4>
<pre>public static final&nbsp;int MOTION_TRANSLATION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.video.Video.MOTION_TRANSLATION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPTFLOW_FARNEBACK_GAUSSIAN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPTFLOW_FARNEBACK_GAUSSIAN</h4>
<pre>public static final&nbsp;int OPTFLOW_FARNEBACK_GAUSSIAN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.video.Video.OPTFLOW_FARNEBACK_GAUSSIAN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPTFLOW_LK_GET_MIN_EIGENVALS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPTFLOW_LK_GET_MIN_EIGENVALS</h4>
<pre>public static final&nbsp;int OPTFLOW_LK_GET_MIN_EIGENVALS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.video.Video.OPTFLOW_LK_GET_MIN_EIGENVALS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPTFLOW_USE_INITIAL_FLOW">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPTFLOW_USE_INITIAL_FLOW</h4>
<pre>public static final&nbsp;int OPTFLOW_USE_INITIAL_FLOW</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.video.Video.OPTFLOW_USE_INITIAL_FLOW">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TrackerSamplerCSC_MODE_DETECT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TrackerSamplerCSC_MODE_DETECT</h4>
<pre>public static final&nbsp;int TrackerSamplerCSC_MODE_DETECT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.video.Video.TrackerSamplerCSC_MODE_DETECT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TrackerSamplerCSC_MODE_INIT_NEG">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TrackerSamplerCSC_MODE_INIT_NEG</h4>
<pre>public static final&nbsp;int TrackerSamplerCSC_MODE_INIT_NEG</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.video.Video.TrackerSamplerCSC_MODE_INIT_NEG">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TrackerSamplerCSC_MODE_INIT_POS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TrackerSamplerCSC_MODE_INIT_POS</h4>
<pre>public static final&nbsp;int TrackerSamplerCSC_MODE_INIT_POS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.video.Video.TrackerSamplerCSC_MODE_INIT_POS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TrackerSamplerCSC_MODE_TRACK_NEG">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TrackerSamplerCSC_MODE_TRACK_NEG</h4>
<pre>public static final&nbsp;int TrackerSamplerCSC_MODE_TRACK_NEG</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.video.Video.TrackerSamplerCSC_MODE_TRACK_NEG">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TrackerSamplerCSC_MODE_TRACK_POS">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TrackerSamplerCSC_MODE_TRACK_POS</h4>
<pre>public static final&nbsp;int TrackerSamplerCSC_MODE_TRACK_POS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.video.Video.TrackerSamplerCSC_MODE_TRACK_POS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Video--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Video</h4>
<pre>public&nbsp;Video()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="buildOpticalFlowPyramid-org.opencv.core.Mat-java.util.List-org.opencv.core.Size-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>buildOpticalFlowPyramid</h4>
<pre>public static&nbsp;int&nbsp;buildOpticalFlowPyramid(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                                          java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;pyramid,
                                          <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                                          int&nbsp;maxLevel)</pre>
<div class="block">Constructs the image pyramid which can be passed to calcOpticalFlowPyrLK.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - 8-bit input image.</dd>
<dd><code>pyramid</code> - output pyramid.</dd>
<dd><code>winSize</code> - window size of optical flow algorithm. Must be not less than winSize argument of
 calcOpticalFlowPyrLK. It is needed to calculate required padding for pyramid levels.</dd>
<dd><code>maxLevel</code> - 0-based maximal pyramid level number.
 constructed without the gradients then calcOpticalFlowPyrLK will calculate them internally.
 to force data copying.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>number of levels in constructed pyramid. Can be less than maxLevel.</dd>
</dl>
</li>
</ul>
<a name="buildOpticalFlowPyramid-org.opencv.core.Mat-java.util.List-org.opencv.core.Size-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>buildOpticalFlowPyramid</h4>
<pre>public static&nbsp;int&nbsp;buildOpticalFlowPyramid(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                                          java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;pyramid,
                                          <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                                          int&nbsp;maxLevel,
                                          boolean&nbsp;withDerivatives)</pre>
<div class="block">Constructs the image pyramid which can be passed to calcOpticalFlowPyrLK.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - 8-bit input image.</dd>
<dd><code>pyramid</code> - output pyramid.</dd>
<dd><code>winSize</code> - window size of optical flow algorithm. Must be not less than winSize argument of
 calcOpticalFlowPyrLK. It is needed to calculate required padding for pyramid levels.</dd>
<dd><code>maxLevel</code> - 0-based maximal pyramid level number.</dd>
<dd><code>withDerivatives</code> - set to precompute gradients for the every pyramid level. If pyramid is
 constructed without the gradients then calcOpticalFlowPyrLK will calculate them internally.
 to force data copying.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>number of levels in constructed pyramid. Can be less than maxLevel.</dd>
</dl>
</li>
</ul>
<a name="buildOpticalFlowPyramid-org.opencv.core.Mat-java.util.List-org.opencv.core.Size-int-boolean-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>buildOpticalFlowPyramid</h4>
<pre>public static&nbsp;int&nbsp;buildOpticalFlowPyramid(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                                          java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;pyramid,
                                          <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                                          int&nbsp;maxLevel,
                                          boolean&nbsp;withDerivatives,
                                          int&nbsp;pyrBorder)</pre>
<div class="block">Constructs the image pyramid which can be passed to calcOpticalFlowPyrLK.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - 8-bit input image.</dd>
<dd><code>pyramid</code> - output pyramid.</dd>
<dd><code>winSize</code> - window size of optical flow algorithm. Must be not less than winSize argument of
 calcOpticalFlowPyrLK. It is needed to calculate required padding for pyramid levels.</dd>
<dd><code>maxLevel</code> - 0-based maximal pyramid level number.</dd>
<dd><code>withDerivatives</code> - set to precompute gradients for the every pyramid level. If pyramid is
 constructed without the gradients then calcOpticalFlowPyrLK will calculate them internally.</dd>
<dd><code>pyrBorder</code> - the border mode for pyramid layers.
 to force data copying.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>number of levels in constructed pyramid. Can be less than maxLevel.</dd>
</dl>
</li>
</ul>
<a name="buildOpticalFlowPyramid-org.opencv.core.Mat-java.util.List-org.opencv.core.Size-int-boolean-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>buildOpticalFlowPyramid</h4>
<pre>public static&nbsp;int&nbsp;buildOpticalFlowPyramid(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                                          java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;pyramid,
                                          <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                                          int&nbsp;maxLevel,
                                          boolean&nbsp;withDerivatives,
                                          int&nbsp;pyrBorder,
                                          int&nbsp;derivBorder)</pre>
<div class="block">Constructs the image pyramid which can be passed to calcOpticalFlowPyrLK.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - 8-bit input image.</dd>
<dd><code>pyramid</code> - output pyramid.</dd>
<dd><code>winSize</code> - window size of optical flow algorithm. Must be not less than winSize argument of
 calcOpticalFlowPyrLK. It is needed to calculate required padding for pyramid levels.</dd>
<dd><code>maxLevel</code> - 0-based maximal pyramid level number.</dd>
<dd><code>withDerivatives</code> - set to precompute gradients for the every pyramid level. If pyramid is
 constructed without the gradients then calcOpticalFlowPyrLK will calculate them internally.</dd>
<dd><code>pyrBorder</code> - the border mode for pyramid layers.</dd>
<dd><code>derivBorder</code> - the border mode for gradients.
 to force data copying.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>number of levels in constructed pyramid. Can be less than maxLevel.</dd>
</dl>
</li>
</ul>
<a name="buildOpticalFlowPyramid-org.opencv.core.Mat-java.util.List-org.opencv.core.Size-int-boolean-int-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>buildOpticalFlowPyramid</h4>
<pre>public static&nbsp;int&nbsp;buildOpticalFlowPyramid(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                                          java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;pyramid,
                                          <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                                          int&nbsp;maxLevel,
                                          boolean&nbsp;withDerivatives,
                                          int&nbsp;pyrBorder,
                                          int&nbsp;derivBorder,
                                          boolean&nbsp;tryReuseInputImage)</pre>
<div class="block">Constructs the image pyramid which can be passed to calcOpticalFlowPyrLK.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - 8-bit input image.</dd>
<dd><code>pyramid</code> - output pyramid.</dd>
<dd><code>winSize</code> - window size of optical flow algorithm. Must be not less than winSize argument of
 calcOpticalFlowPyrLK. It is needed to calculate required padding for pyramid levels.</dd>
<dd><code>maxLevel</code> - 0-based maximal pyramid level number.</dd>
<dd><code>withDerivatives</code> - set to precompute gradients for the every pyramid level. If pyramid is
 constructed without the gradients then calcOpticalFlowPyrLK will calculate them internally.</dd>
<dd><code>pyrBorder</code> - the border mode for pyramid layers.</dd>
<dd><code>derivBorder</code> - the border mode for gradients.</dd>
<dd><code>tryReuseInputImage</code> - put ROI of input image into the pyramid if possible. You can pass false
 to force data copying.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>number of levels in constructed pyramid. Can be less than maxLevel.</dd>
</dl>
</li>
</ul>
<a name="calcOpticalFlowFarneback-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-int-int-int-int-double-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calcOpticalFlowFarneback</h4>
<pre>public static&nbsp;void&nbsp;calcOpticalFlowFarneback(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prev,
                                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;next,
                                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;flow,
                                            double&nbsp;pyr_scale,
                                            int&nbsp;levels,
                                            int&nbsp;winsize,
                                            int&nbsp;iterations,
                                            int&nbsp;poly_n,
                                            double&nbsp;poly_sigma,
                                            int&nbsp;flags)</pre>
<div class="block">Computes a dense optical flow using the Gunnar Farneback's algorithm.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>prev</code> - first 8-bit single-channel input image.</dd>
<dd><code>next</code> - second input image of the same size and the same type as prev.</dd>
<dd><code>flow</code> - computed flow image that has the same size as prev and type CV_32FC2.</dd>
<dd><code>pyr_scale</code> - parameter, specifying the image scale (&lt;1) to build pyramids for each image;
 pyr_scale=0.5 means a classical pyramid, where each next layer is twice smaller than the previous
 one.</dd>
<dd><code>levels</code> - number of pyramid layers including the initial image; levels=1 means that no extra
 layers are created and only the original images are used.</dd>
<dd><code>winsize</code> - averaging window size; larger values increase the algorithm robustness to image
 noise and give more chances for fast motion detection, but yield more blurred motion field.</dd>
<dd><code>iterations</code> - number of iterations the algorithm does at each pyramid level.</dd>
<dd><code>poly_n</code> - size of the pixel neighborhood used to find polynomial expansion in each pixel;
 larger values mean that the image will be approximated with smoother surfaces, yielding more
 robust algorithm and more blurred motion field, typically poly_n =5 or 7.</dd>
<dd><code>poly_sigma</code> - standard deviation of the Gaussian that is used to smooth derivatives used as a
 basis for the polynomial expansion; for poly_n=5, you can set poly_sigma=1.1, for poly_n=7, a
 good value would be poly_sigma=1.5.</dd>
<dd><code>flags</code> - operation flags that can be a combination of the following:
 <ul>
   <li>
     <b>OPTFLOW_USE_INITIAL_FLOW</b> uses the input flow as an initial flow approximation.
   </li>
   <li>
     <b>OPTFLOW_FARNEBACK_GAUSSIAN</b> uses the Gaussian \(\texttt{winsize}\times\texttt{winsize}\)
      filter instead of a box filter of the same size for optical flow estimation; usually, this
      option gives z more accurate flow than with a box filter, at the cost of lower speed;
      normally, winsize for a Gaussian window should be set to a larger value to achieve the same
      level of robustness.
   </li>
 </ul>

 The function finds an optical flow for each prev pixel using the CITE: Farneback2003 algorithm so that

 \(\texttt{prev} (y,x)  \sim \texttt{next} ( y + \texttt{flow} (y,x)[1],  x + \texttt{flow} (y,x)[0])\)

 <b>Note:</b>

 <ul>
   <li>
    An example using the optical flow algorithm described by Gunnar Farneback can be found at
     opencv_source_code/samples/cpp/fback.cpp
   </li>
   <li>
    (Python) An example using the optical flow algorithm described by Gunnar Farneback can be
     found at opencv_source_code/samples/python/opt_flow.py
   </li>
 </ul></dd>
</dl>
</li>
</ul>
<a name="calcOpticalFlowPyrLK-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfByte-org.opencv.core.MatOfFloat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calcOpticalFlowPyrLK</h4>
<pre>public static&nbsp;void&nbsp;calcOpticalFlowPyrLK(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
                                        <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
                                        <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
                                        <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
                                        <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err)</pre>
<div class="block">Calculates an optical flow for a sparse feature set using the iterative Lucas-Kanade method with
 pyramids.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>prevImg</code> - first 8-bit input image or pyramid constructed by buildOpticalFlowPyramid.</dd>
<dd><code>nextImg</code> - second input image or pyramid of the same size and the same type as prevImg.</dd>
<dd><code>prevPts</code> - vector of 2D points for which the flow needs to be found; point coordinates must be
 single-precision floating-point numbers.</dd>
<dd><code>nextPts</code> - output vector of 2D points (with single-precision floating-point coordinates)
 containing the calculated new positions of input features in the second image; when
 OPTFLOW_USE_INITIAL_FLOW flag is passed, the vector must have the same size as in the input.</dd>
<dd><code>status</code> - output status vector (of unsigned chars); each element of the vector is set to 1 if
 the flow for the corresponding features has been found, otherwise, it is set to 0.</dd>
<dd><code>err</code> - output vector of errors; each element of the vector is set to an error for the
 corresponding feature, type of the error measure can be set in flags parameter; if the flow wasn't
 found then the error is not defined (use the status parameter to find such cases).
 level), if set to 1, two levels are used, and so on; if pyramids are passed to input then
 algorithm will use as many levels as pyramids have but no more than maxLevel.
 (after the specified maximum number of iterations criteria.maxCount or when the search window
 moves by less than criteria.epsilon.
 <ul>
   <li>
     <b>OPTFLOW_USE_INITIAL_FLOW</b> uses initial estimations, stored in nextPts; if the flag is
      not set, then prevPts is copied to nextPts and is considered the initial estimate.
   </li>
   <li>
     <b>OPTFLOW_LK_GET_MIN_EIGENVALS</b> use minimum eigen values as an error measure (see
      minEigThreshold description); if the flag is not set, then L1 distance between patches
      around the original and a moved point, divided by number of pixels in a window, is used as a
      error measure.
 optical flow equations (this matrix is called a spatial gradient matrix in CITE: Bouguet00), divided
 by number of pixels in a window; if this value is less than minEigThreshold, then a corresponding
 feature is filtered out and its flow is not processed, so it allows to remove bad points and get a
 performance boost.
   </li>
 </ul>

 The function implements a sparse iterative version of the Lucas-Kanade optical flow in pyramids. See
 CITE: Bouguet00 . The function is parallelized with the TBB library.

 <b>Note:</b>

 <ul>
   <li>
    An example using the Lucas-Kanade optical flow algorithm can be found at
     opencv_source_code/samples/cpp/lkdemo.cpp
   </li>
   <li>
    (Python) An example using the Lucas-Kanade optical flow algorithm can be found at
     opencv_source_code/samples/python/lk_track.py
   </li>
   <li>
    (Python) An example using the Lucas-Kanade tracker for homography matching can be found at
     opencv_source_code/samples/python/lk_homography.py
   </li>
 </ul></dd>
</dl>
</li>
</ul>
<a name="calcOpticalFlowPyrLK-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfByte-org.opencv.core.MatOfFloat-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calcOpticalFlowPyrLK</h4>
<pre>public static&nbsp;void&nbsp;calcOpticalFlowPyrLK(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
                                        <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
                                        <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
                                        <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
                                        <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err,
                                        <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize)</pre>
<div class="block">Calculates an optical flow for a sparse feature set using the iterative Lucas-Kanade method with
 pyramids.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>prevImg</code> - first 8-bit input image or pyramid constructed by buildOpticalFlowPyramid.</dd>
<dd><code>nextImg</code> - second input image or pyramid of the same size and the same type as prevImg.</dd>
<dd><code>prevPts</code> - vector of 2D points for which the flow needs to be found; point coordinates must be
 single-precision floating-point numbers.</dd>
<dd><code>nextPts</code> - output vector of 2D points (with single-precision floating-point coordinates)
 containing the calculated new positions of input features in the second image; when
 OPTFLOW_USE_INITIAL_FLOW flag is passed, the vector must have the same size as in the input.</dd>
<dd><code>status</code> - output status vector (of unsigned chars); each element of the vector is set to 1 if
 the flow for the corresponding features has been found, otherwise, it is set to 0.</dd>
<dd><code>err</code> - output vector of errors; each element of the vector is set to an error for the
 corresponding feature, type of the error measure can be set in flags parameter; if the flow wasn't
 found then the error is not defined (use the status parameter to find such cases).</dd>
<dd><code>winSize</code> - size of the search window at each pyramid level.
 level), if set to 1, two levels are used, and so on; if pyramids are passed to input then
 algorithm will use as many levels as pyramids have but no more than maxLevel.
 (after the specified maximum number of iterations criteria.maxCount or when the search window
 moves by less than criteria.epsilon.
 <ul>
   <li>
     <b>OPTFLOW_USE_INITIAL_FLOW</b> uses initial estimations, stored in nextPts; if the flag is
      not set, then prevPts is copied to nextPts and is considered the initial estimate.
   </li>
   <li>
     <b>OPTFLOW_LK_GET_MIN_EIGENVALS</b> use minimum eigen values as an error measure (see
      minEigThreshold description); if the flag is not set, then L1 distance between patches
      around the original and a moved point, divided by number of pixels in a window, is used as a
      error measure.
 optical flow equations (this matrix is called a spatial gradient matrix in CITE: Bouguet00), divided
 by number of pixels in a window; if this value is less than minEigThreshold, then a corresponding
 feature is filtered out and its flow is not processed, so it allows to remove bad points and get a
 performance boost.
   </li>
 </ul>

 The function implements a sparse iterative version of the Lucas-Kanade optical flow in pyramids. See
 CITE: Bouguet00 . The function is parallelized with the TBB library.

 <b>Note:</b>

 <ul>
   <li>
    An example using the Lucas-Kanade optical flow algorithm can be found at
     opencv_source_code/samples/cpp/lkdemo.cpp
   </li>
   <li>
    (Python) An example using the Lucas-Kanade optical flow algorithm can be found at
     opencv_source_code/samples/python/lk_track.py
   </li>
   <li>
    (Python) An example using the Lucas-Kanade tracker for homography matching can be found at
     opencv_source_code/samples/python/lk_homography.py
   </li>
 </ul></dd>
</dl>
</li>
</ul>
<a name="calcOpticalFlowPyrLK-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfByte-org.opencv.core.MatOfFloat-org.opencv.core.Size-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calcOpticalFlowPyrLK</h4>
<pre>public static&nbsp;void&nbsp;calcOpticalFlowPyrLK(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
                                        <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
                                        <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
                                        <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
                                        <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err,
                                        <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                                        int&nbsp;maxLevel)</pre>
<div class="block">Calculates an optical flow for a sparse feature set using the iterative Lucas-Kanade method with
 pyramids.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>prevImg</code> - first 8-bit input image or pyramid constructed by buildOpticalFlowPyramid.</dd>
<dd><code>nextImg</code> - second input image or pyramid of the same size and the same type as prevImg.</dd>
<dd><code>prevPts</code> - vector of 2D points for which the flow needs to be found; point coordinates must be
 single-precision floating-point numbers.</dd>
<dd><code>nextPts</code> - output vector of 2D points (with single-precision floating-point coordinates)
 containing the calculated new positions of input features in the second image; when
 OPTFLOW_USE_INITIAL_FLOW flag is passed, the vector must have the same size as in the input.</dd>
<dd><code>status</code> - output status vector (of unsigned chars); each element of the vector is set to 1 if
 the flow for the corresponding features has been found, otherwise, it is set to 0.</dd>
<dd><code>err</code> - output vector of errors; each element of the vector is set to an error for the
 corresponding feature, type of the error measure can be set in flags parameter; if the flow wasn't
 found then the error is not defined (use the status parameter to find such cases).</dd>
<dd><code>winSize</code> - size of the search window at each pyramid level.</dd>
<dd><code>maxLevel</code> - 0-based maximal pyramid level number; if set to 0, pyramids are not used (single
 level), if set to 1, two levels are used, and so on; if pyramids are passed to input then
 algorithm will use as many levels as pyramids have but no more than maxLevel.
 (after the specified maximum number of iterations criteria.maxCount or when the search window
 moves by less than criteria.epsilon.
 <ul>
   <li>
     <b>OPTFLOW_USE_INITIAL_FLOW</b> uses initial estimations, stored in nextPts; if the flag is
      not set, then prevPts is copied to nextPts and is considered the initial estimate.
   </li>
   <li>
     <b>OPTFLOW_LK_GET_MIN_EIGENVALS</b> use minimum eigen values as an error measure (see
      minEigThreshold description); if the flag is not set, then L1 distance between patches
      around the original and a moved point, divided by number of pixels in a window, is used as a
      error measure.
 optical flow equations (this matrix is called a spatial gradient matrix in CITE: Bouguet00), divided
 by number of pixels in a window; if this value is less than minEigThreshold, then a corresponding
 feature is filtered out and its flow is not processed, so it allows to remove bad points and get a
 performance boost.
   </li>
 </ul>

 The function implements a sparse iterative version of the Lucas-Kanade optical flow in pyramids. See
 CITE: Bouguet00 . The function is parallelized with the TBB library.

 <b>Note:</b>

 <ul>
   <li>
    An example using the Lucas-Kanade optical flow algorithm can be found at
     opencv_source_code/samples/cpp/lkdemo.cpp
   </li>
   <li>
    (Python) An example using the Lucas-Kanade optical flow algorithm can be found at
     opencv_source_code/samples/python/lk_track.py
   </li>
   <li>
    (Python) An example using the Lucas-Kanade tracker for homography matching can be found at
     opencv_source_code/samples/python/lk_homography.py
   </li>
 </ul></dd>
</dl>
</li>
</ul>
<a name="calcOpticalFlowPyrLK-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfByte-org.opencv.core.MatOfFloat-org.opencv.core.Size-int-org.opencv.core.TermCriteria-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calcOpticalFlowPyrLK</h4>
<pre>public static&nbsp;void&nbsp;calcOpticalFlowPyrLK(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
                                        <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
                                        <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
                                        <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
                                        <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err,
                                        <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                                        int&nbsp;maxLevel,
                                        <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</pre>
<div class="block">Calculates an optical flow for a sparse feature set using the iterative Lucas-Kanade method with
 pyramids.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>prevImg</code> - first 8-bit input image or pyramid constructed by buildOpticalFlowPyramid.</dd>
<dd><code>nextImg</code> - second input image or pyramid of the same size and the same type as prevImg.</dd>
<dd><code>prevPts</code> - vector of 2D points for which the flow needs to be found; point coordinates must be
 single-precision floating-point numbers.</dd>
<dd><code>nextPts</code> - output vector of 2D points (with single-precision floating-point coordinates)
 containing the calculated new positions of input features in the second image; when
 OPTFLOW_USE_INITIAL_FLOW flag is passed, the vector must have the same size as in the input.</dd>
<dd><code>status</code> - output status vector (of unsigned chars); each element of the vector is set to 1 if
 the flow for the corresponding features has been found, otherwise, it is set to 0.</dd>
<dd><code>err</code> - output vector of errors; each element of the vector is set to an error for the
 corresponding feature, type of the error measure can be set in flags parameter; if the flow wasn't
 found then the error is not defined (use the status parameter to find such cases).</dd>
<dd><code>winSize</code> - size of the search window at each pyramid level.</dd>
<dd><code>maxLevel</code> - 0-based maximal pyramid level number; if set to 0, pyramids are not used (single
 level), if set to 1, two levels are used, and so on; if pyramids are passed to input then
 algorithm will use as many levels as pyramids have but no more than maxLevel.</dd>
<dd><code>criteria</code> - parameter, specifying the termination criteria of the iterative search algorithm
 (after the specified maximum number of iterations criteria.maxCount or when the search window
 moves by less than criteria.epsilon.
 <ul>
   <li>
     <b>OPTFLOW_USE_INITIAL_FLOW</b> uses initial estimations, stored in nextPts; if the flag is
      not set, then prevPts is copied to nextPts and is considered the initial estimate.
   </li>
   <li>
     <b>OPTFLOW_LK_GET_MIN_EIGENVALS</b> use minimum eigen values as an error measure (see
      minEigThreshold description); if the flag is not set, then L1 distance between patches
      around the original and a moved point, divided by number of pixels in a window, is used as a
      error measure.
 optical flow equations (this matrix is called a spatial gradient matrix in CITE: Bouguet00), divided
 by number of pixels in a window; if this value is less than minEigThreshold, then a corresponding
 feature is filtered out and its flow is not processed, so it allows to remove bad points and get a
 performance boost.
   </li>
 </ul>

 The function implements a sparse iterative version of the Lucas-Kanade optical flow in pyramids. See
 CITE: Bouguet00 . The function is parallelized with the TBB library.

 <b>Note:</b>

 <ul>
   <li>
    An example using the Lucas-Kanade optical flow algorithm can be found at
     opencv_source_code/samples/cpp/lkdemo.cpp
   </li>
   <li>
    (Python) An example using the Lucas-Kanade optical flow algorithm can be found at
     opencv_source_code/samples/python/lk_track.py
   </li>
   <li>
    (Python) An example using the Lucas-Kanade tracker for homography matching can be found at
     opencv_source_code/samples/python/lk_homography.py
   </li>
 </ul></dd>
</dl>
</li>
</ul>
<a name="calcOpticalFlowPyrLK-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfByte-org.opencv.core.MatOfFloat-org.opencv.core.Size-int-org.opencv.core.TermCriteria-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calcOpticalFlowPyrLK</h4>
<pre>public static&nbsp;void&nbsp;calcOpticalFlowPyrLK(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
                                        <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
                                        <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
                                        <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
                                        <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err,
                                        <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                                        int&nbsp;maxLevel,
                                        <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria,
                                        int&nbsp;flags)</pre>
<div class="block">Calculates an optical flow for a sparse feature set using the iterative Lucas-Kanade method with
 pyramids.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>prevImg</code> - first 8-bit input image or pyramid constructed by buildOpticalFlowPyramid.</dd>
<dd><code>nextImg</code> - second input image or pyramid of the same size and the same type as prevImg.</dd>
<dd><code>prevPts</code> - vector of 2D points for which the flow needs to be found; point coordinates must be
 single-precision floating-point numbers.</dd>
<dd><code>nextPts</code> - output vector of 2D points (with single-precision floating-point coordinates)
 containing the calculated new positions of input features in the second image; when
 OPTFLOW_USE_INITIAL_FLOW flag is passed, the vector must have the same size as in the input.</dd>
<dd><code>status</code> - output status vector (of unsigned chars); each element of the vector is set to 1 if
 the flow for the corresponding features has been found, otherwise, it is set to 0.</dd>
<dd><code>err</code> - output vector of errors; each element of the vector is set to an error for the
 corresponding feature, type of the error measure can be set in flags parameter; if the flow wasn't
 found then the error is not defined (use the status parameter to find such cases).</dd>
<dd><code>winSize</code> - size of the search window at each pyramid level.</dd>
<dd><code>maxLevel</code> - 0-based maximal pyramid level number; if set to 0, pyramids are not used (single
 level), if set to 1, two levels are used, and so on; if pyramids are passed to input then
 algorithm will use as many levels as pyramids have but no more than maxLevel.</dd>
<dd><code>criteria</code> - parameter, specifying the termination criteria of the iterative search algorithm
 (after the specified maximum number of iterations criteria.maxCount or when the search window
 moves by less than criteria.epsilon.</dd>
<dd><code>flags</code> - operation flags:
 <ul>
   <li>
     <b>OPTFLOW_USE_INITIAL_FLOW</b> uses initial estimations, stored in nextPts; if the flag is
      not set, then prevPts is copied to nextPts and is considered the initial estimate.
   </li>
   <li>
     <b>OPTFLOW_LK_GET_MIN_EIGENVALS</b> use minimum eigen values as an error measure (see
      minEigThreshold description); if the flag is not set, then L1 distance between patches
      around the original and a moved point, divided by number of pixels in a window, is used as a
      error measure.
 optical flow equations (this matrix is called a spatial gradient matrix in CITE: Bouguet00), divided
 by number of pixels in a window; if this value is less than minEigThreshold, then a corresponding
 feature is filtered out and its flow is not processed, so it allows to remove bad points and get a
 performance boost.
   </li>
 </ul>

 The function implements a sparse iterative version of the Lucas-Kanade optical flow in pyramids. See
 CITE: Bouguet00 . The function is parallelized with the TBB library.

 <b>Note:</b>

 <ul>
   <li>
    An example using the Lucas-Kanade optical flow algorithm can be found at
     opencv_source_code/samples/cpp/lkdemo.cpp
   </li>
   <li>
    (Python) An example using the Lucas-Kanade optical flow algorithm can be found at
     opencv_source_code/samples/python/lk_track.py
   </li>
   <li>
    (Python) An example using the Lucas-Kanade tracker for homography matching can be found at
     opencv_source_code/samples/python/lk_homography.py
   </li>
 </ul></dd>
</dl>
</li>
</ul>
<a name="calcOpticalFlowPyrLK-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfByte-org.opencv.core.MatOfFloat-org.opencv.core.Size-int-org.opencv.core.TermCriteria-int-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calcOpticalFlowPyrLK</h4>
<pre>public static&nbsp;void&nbsp;calcOpticalFlowPyrLK(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
                                        <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
                                        <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
                                        <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
                                        <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err,
                                        <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                                        int&nbsp;maxLevel,
                                        <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria,
                                        int&nbsp;flags,
                                        double&nbsp;minEigThreshold)</pre>
<div class="block">Calculates an optical flow for a sparse feature set using the iterative Lucas-Kanade method with
 pyramids.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>prevImg</code> - first 8-bit input image or pyramid constructed by buildOpticalFlowPyramid.</dd>
<dd><code>nextImg</code> - second input image or pyramid of the same size and the same type as prevImg.</dd>
<dd><code>prevPts</code> - vector of 2D points for which the flow needs to be found; point coordinates must be
 single-precision floating-point numbers.</dd>
<dd><code>nextPts</code> - output vector of 2D points (with single-precision floating-point coordinates)
 containing the calculated new positions of input features in the second image; when
 OPTFLOW_USE_INITIAL_FLOW flag is passed, the vector must have the same size as in the input.</dd>
<dd><code>status</code> - output status vector (of unsigned chars); each element of the vector is set to 1 if
 the flow for the corresponding features has been found, otherwise, it is set to 0.</dd>
<dd><code>err</code> - output vector of errors; each element of the vector is set to an error for the
 corresponding feature, type of the error measure can be set in flags parameter; if the flow wasn't
 found then the error is not defined (use the status parameter to find such cases).</dd>
<dd><code>winSize</code> - size of the search window at each pyramid level.</dd>
<dd><code>maxLevel</code> - 0-based maximal pyramid level number; if set to 0, pyramids are not used (single
 level), if set to 1, two levels are used, and so on; if pyramids are passed to input then
 algorithm will use as many levels as pyramids have but no more than maxLevel.</dd>
<dd><code>criteria</code> - parameter, specifying the termination criteria of the iterative search algorithm
 (after the specified maximum number of iterations criteria.maxCount or when the search window
 moves by less than criteria.epsilon.</dd>
<dd><code>flags</code> - operation flags:
 <ul>
   <li>
     <b>OPTFLOW_USE_INITIAL_FLOW</b> uses initial estimations, stored in nextPts; if the flag is
      not set, then prevPts is copied to nextPts and is considered the initial estimate.
   </li>
   <li>
     <b>OPTFLOW_LK_GET_MIN_EIGENVALS</b> use minimum eigen values as an error measure (see
      minEigThreshold description); if the flag is not set, then L1 distance between patches
      around the original and a moved point, divided by number of pixels in a window, is used as a
      error measure.</dd>
<dd><code>minEigThreshold</code> - the algorithm calculates the minimum eigen value of a 2x2 normal matrix of
 optical flow equations (this matrix is called a spatial gradient matrix in CITE: Bouguet00), divided
 by number of pixels in a window; if this value is less than minEigThreshold, then a corresponding
 feature is filtered out and its flow is not processed, so it allows to remove bad points and get a
 performance boost.
   </li>
 </ul>

 The function implements a sparse iterative version of the Lucas-Kanade optical flow in pyramids. See
 CITE: Bouguet00 . The function is parallelized with the TBB library.

 <b>Note:</b>

 <ul>
   <li>
    An example using the Lucas-Kanade optical flow algorithm can be found at
     opencv_source_code/samples/cpp/lkdemo.cpp
   </li>
   <li>
    (Python) An example using the Lucas-Kanade optical flow algorithm can be found at
     opencv_source_code/samples/python/lk_track.py
   </li>
   <li>
    (Python) An example using the Lucas-Kanade tracker for homography matching can be found at
     opencv_source_code/samples/python/lk_homography.py
   </li>
 </ul></dd>
</dl>
</li>
</ul>
<a name="CamShift-org.opencv.core.Mat-org.opencv.core.Rect-org.opencv.core.TermCriteria-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CamShift</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/RotatedRect.html" title="class in org.opencv.core">RotatedRect</a>&nbsp;CamShift(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probImage,
                                   <a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;window,
                                   <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</pre>
<div class="block">Finds an object center, size, and orientation.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>probImage</code> - Back projection of the object histogram. See calcBackProject.</dd>
<dd><code>window</code> - Initial search window.</dd>
<dd><code>criteria</code> - Stop criteria for the underlying meanShift.
 returns
 (in old interfaces) Number of iterations CAMSHIFT took to converge
 The function implements the CAMSHIFT object tracking algorithm CITE: Bradski98 . First, it finds an
 object center using meanShift and then adjusts the window size and finds the optimal rotation. The
 function returns the rotated rectangle structure that includes the object position, size, and
 orientation. The next position of the search window can be obtained with RotatedRect::boundingRect()

 See the OpenCV sample camshiftdemo.c that tracks colored objects.

 <b>Note:</b>
 <ul>
   <li>
    (Python) A sample explaining the camshift tracking algorithm can be found at
     opencv_source_code/samples/python/camshift.py
   </li>
 </ul></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="computeECC-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>computeECC</h4>
<pre>public static&nbsp;double&nbsp;computeECC(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage)</pre>
<div class="block">Computes the Enhanced Correlation Coefficient value between two images CITE: EP08 .</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>templateImage</code> - single-channel template image; CV_8U or CV_32F array.</dd>
<dd><code>inputImage</code> - single-channel input image to be warped to provide an image similar to
  templateImage, same type as templateImage.

 SEE:
 findTransformECC</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="computeECC-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>computeECC</h4>
<pre>public static&nbsp;double&nbsp;computeECC(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputMask)</pre>
<div class="block">Computes the Enhanced Correlation Coefficient value between two images CITE: EP08 .</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>templateImage</code> - single-channel template image; CV_8U or CV_32F array.</dd>
<dd><code>inputImage</code> - single-channel input image to be warped to provide an image similar to
  templateImage, same type as templateImage.</dd>
<dd><code>inputMask</code> - An optional mask to indicate valid values of inputImage.

 SEE:
 findTransformECC</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createBackgroundSubtractorKNN--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createBackgroundSubtractorKNN</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/BackgroundSubtractorKNN.html" title="class in org.opencv.video">BackgroundSubtractorKNN</a>&nbsp;createBackgroundSubtractorKNN()</pre>
<div class="block">Creates KNN Background Subtractor

 whether a pixel is close to that sample. This parameter does not affect the background update.
 speed a bit, so if you do not need this feature, set the parameter to false.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createBackgroundSubtractorKNN-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createBackgroundSubtractorKNN</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/BackgroundSubtractorKNN.html" title="class in org.opencv.video">BackgroundSubtractorKNN</a>&nbsp;createBackgroundSubtractorKNN(int&nbsp;history)</pre>
<div class="block">Creates KNN Background Subtractor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>history</code> - Length of the history.
 whether a pixel is close to that sample. This parameter does not affect the background update.
 speed a bit, so if you do not need this feature, set the parameter to false.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createBackgroundSubtractorKNN-int-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createBackgroundSubtractorKNN</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/BackgroundSubtractorKNN.html" title="class in org.opencv.video">BackgroundSubtractorKNN</a>&nbsp;createBackgroundSubtractorKNN(int&nbsp;history,
                                                                    double&nbsp;dist2Threshold)</pre>
<div class="block">Creates KNN Background Subtractor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>history</code> - Length of the history.</dd>
<dd><code>dist2Threshold</code> - Threshold on the squared distance between the pixel and the sample to decide
 whether a pixel is close to that sample. This parameter does not affect the background update.
 speed a bit, so if you do not need this feature, set the parameter to false.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createBackgroundSubtractorKNN-int-double-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createBackgroundSubtractorKNN</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/BackgroundSubtractorKNN.html" title="class in org.opencv.video">BackgroundSubtractorKNN</a>&nbsp;createBackgroundSubtractorKNN(int&nbsp;history,
                                                                    double&nbsp;dist2Threshold,
                                                                    boolean&nbsp;detectShadows)</pre>
<div class="block">Creates KNN Background Subtractor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>history</code> - Length of the history.</dd>
<dd><code>dist2Threshold</code> - Threshold on the squared distance between the pixel and the sample to decide
 whether a pixel is close to that sample. This parameter does not affect the background update.</dd>
<dd><code>detectShadows</code> - If true, the algorithm will detect shadows and mark them. It decreases the
 speed a bit, so if you do not need this feature, set the parameter to false.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createBackgroundSubtractorMOG2--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createBackgroundSubtractorMOG2</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html" title="class in org.opencv.video">BackgroundSubtractorMOG2</a>&nbsp;createBackgroundSubtractorMOG2()</pre>
<div class="block">Creates MOG2 Background Subtractor

 to decide whether a pixel is well described by the background model. This parameter does not
 affect the background update.
 speed a bit, so if you do not need this feature, set the parameter to false.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createBackgroundSubtractorMOG2-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createBackgroundSubtractorMOG2</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html" title="class in org.opencv.video">BackgroundSubtractorMOG2</a>&nbsp;createBackgroundSubtractorMOG2(int&nbsp;history)</pre>
<div class="block">Creates MOG2 Background Subtractor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>history</code> - Length of the history.
 to decide whether a pixel is well described by the background model. This parameter does not
 affect the background update.
 speed a bit, so if you do not need this feature, set the parameter to false.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createBackgroundSubtractorMOG2-int-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createBackgroundSubtractorMOG2</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html" title="class in org.opencv.video">BackgroundSubtractorMOG2</a>&nbsp;createBackgroundSubtractorMOG2(int&nbsp;history,
                                                                      double&nbsp;varThreshold)</pre>
<div class="block">Creates MOG2 Background Subtractor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>history</code> - Length of the history.</dd>
<dd><code>varThreshold</code> - Threshold on the squared Mahalanobis distance between the pixel and the model
 to decide whether a pixel is well described by the background model. This parameter does not
 affect the background update.
 speed a bit, so if you do not need this feature, set the parameter to false.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="createBackgroundSubtractorMOG2-int-double-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createBackgroundSubtractorMOG2</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html" title="class in org.opencv.video">BackgroundSubtractorMOG2</a>&nbsp;createBackgroundSubtractorMOG2(int&nbsp;history,
                                                                      double&nbsp;varThreshold,
                                                                      boolean&nbsp;detectShadows)</pre>
<div class="block">Creates MOG2 Background Subtractor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>history</code> - Length of the history.</dd>
<dd><code>varThreshold</code> - Threshold on the squared Mahalanobis distance between the pixel and the model
 to decide whether a pixel is well described by the background model. This parameter does not
 affect the background update.</dd>
<dd><code>detectShadows</code> - If true, the algorithm will detect shadows and mark them. It decreases the
 speed a bit, so if you do not need this feature, set the parameter to false.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="findTransformECC-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findTransformECC</h4>
<pre>public static&nbsp;double&nbsp;findTransformECC(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix)</pre>
</li>
</ul>
<a name="findTransformECC-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findTransformECC</h4>
<pre>public static&nbsp;double&nbsp;findTransformECC(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix,
                                      int&nbsp;motionType)</pre>
</li>
</ul>
<a name="findTransformECC-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-org.opencv.core.TermCriteria-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findTransformECC</h4>
<pre>public static&nbsp;double&nbsp;findTransformECC(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix,
                                      int&nbsp;motionType,
                                      <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</pre>
</li>
</ul>
<a name="findTransformECC-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-org.opencv.core.TermCriteria-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findTransformECC</h4>
<pre>public static&nbsp;double&nbsp;findTransformECC(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix,
                                      int&nbsp;motionType,
                                      <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputMask)</pre>
</li>
</ul>
<a name="findTransformECC-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-org.opencv.core.TermCriteria-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findTransformECC</h4>
<pre>public static&nbsp;double&nbsp;findTransformECC(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix,
                                      int&nbsp;motionType,
                                      <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputMask,
                                      int&nbsp;gaussFiltSize)</pre>
<div class="block">Finds the geometric transform (warp) between two images in terms of the ECC criterion CITE: EP08 .</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>templateImage</code> - single-channel template image; CV_8U or CV_32F array.</dd>
<dd><code>inputImage</code> - single-channel input image which should be warped with the final warpMatrix in
 order to provide an image similar to templateImage, same type as templateImage.</dd>
<dd><code>warpMatrix</code> - floating-point \(2\times 3\) or \(3\times 3\) mapping matrix (warp).</dd>
<dd><code>motionType</code> - parameter, specifying the type of motion:
 <ul>
   <li>
     <b>MOTION_TRANSLATION</b> sets a translational motion model; warpMatrix is \(2\times 3\) with
      the first \(2\times 2\) part being the unity matrix and the rest two parameters being
      estimated.
   </li>
   <li>
     <b>MOTION_EUCLIDEAN</b> sets a Euclidean (rigid) transformation as motion model; three
      parameters are estimated; warpMatrix is \(2\times 3\).
   </li>
   <li>
     <b>MOTION_AFFINE</b> sets an affine motion model (DEFAULT); six parameters are estimated;
      warpMatrix is \(2\times 3\).
   </li>
   <li>
     <b>MOTION_HOMOGRAPHY</b> sets a homography as a motion model; eight parameters are
      estimated;\<code>warpMatrix\</code> is \(3\times 3\).</dd>
<dd><code>criteria</code> - parameter, specifying the termination criteria of the ECC algorithm;
 criteria.epsilon defines the threshold of the increment in the correlation coefficient between two
 iterations (a negative criteria.epsilon makes criteria.maxcount the only termination criterion).
 Default values are shown in the declaration above.</dd>
<dd><code>inputMask</code> - An optional mask to indicate valid values of inputImage.</dd>
<dd><code>gaussFiltSize</code> - An optional value indicating size of gaussian blur filter; (DEFAULT: 5)
   </li>
 </ul>

 The function estimates the optimum transformation (warpMatrix) with respect to ECC criterion
 (CITE: EP08), that is

 \(\texttt{warpMatrix} = \arg\max_{W} \texttt{ECC}(\texttt{templateImage}(x,y),\texttt{inputImage}(x',y'))\)

 where

 \(\begin{bmatrix} x' \\ y' \end{bmatrix} = W \cdot \begin{bmatrix} x \\ y \\ 1 \end{bmatrix}\)

 (the equation holds with homogeneous coordinates for homography). It returns the final enhanced
 correlation coefficient, that is the correlation coefficient between the template image and the
 final warped input image. When a \(3\times 3\) matrix is given with motionType =0, 1 or 2, the third
 row is ignored.

 Unlike findHomography and estimateRigidTransform, the function findTransformECC implements an
 area-based alignment that builds on intensity similarities. In essence, the function updates the
 initial transformation that roughly aligns the images. If this information is missing, the identity
 warp (unity matrix) is used as an initialization. Note that if images undergo strong
 displacements/rotations, an initial transformation that roughly aligns the images is necessary
 (e.g., a simple euclidean/similarity transform that allows for the images showing the same image
 content approximately). Use inverse warping in the second image to take an image close to the first
 one, i.e. use the flag WARP_INVERSE_MAP with warpAffine or warpPerspective. See also the OpenCV
 sample image_alignment.cpp that demonstrates the use of the function. Note that the function throws
 an exception if algorithm does not converges.

 SEE:
 computeECC, estimateAffine2D, estimateAffinePartial2D, findHomography</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="meanShift-org.opencv.core.Mat-org.opencv.core.Rect-org.opencv.core.TermCriteria-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>meanShift</h4>
<pre>public static&nbsp;int&nbsp;meanShift(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probImage,
                            <a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;window,
                            <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</pre>
<div class="block">Finds an object on a back projection image.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>probImage</code> - Back projection of the object histogram. See calcBackProject for details.</dd>
<dd><code>window</code> - Initial search window.</dd>
<dd><code>criteria</code> - Stop criteria for the iterative search algorithm.
 returns
 :   Number of iterations CAMSHIFT took to converge.
 The function implements the iterative object search algorithm. It takes the input back projection of
 an object and the initial position. The mass center in window of the back projection image is
 computed and the search window center shifts to the mass center. The procedure is repeated until the
 specified number of iterations criteria.maxCount is done or until the window center shifts by less
 than criteria.epsilon. The algorithm is used inside CamShift and, unlike CamShift , the search
 window size or orientation do not change during the search. You can simply pass the output of
 calcBackProject to this function. But better results can be obtained if you pre-filter the back
 projection and remove the noise. For example, you can do this by retrieving connected components
 with findContours , throwing away contours with small area ( contourArea ), and rendering the
 remaining contours with drawContours.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="readOpticalFlow-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readOpticalFlow</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;readOpticalFlow(java.lang.String&nbsp;path)</pre>
<div class="block">Read a .flo file</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - Path to the file to be loaded

  The function readOpticalFlow loads a flow field from a file and returns it as a single matrix.
  Resulting Mat has a type CV_32FC2 - floating-point, 2-channel. First channel corresponds to the
  flow in the horizontal direction (u), second - vertical (v).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="writeOpticalFlow-java.lang.String-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>writeOpticalFlow</h4>
<pre>public static&nbsp;boolean&nbsp;writeOpticalFlow(java.lang.String&nbsp;path,
                                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;flow)</pre>
<div class="block">Write a .flo to disk</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - Path to the file to be written</dd>
<dd><code>flow</code> - Flow field to be stored

  The function stores a flow field in a file, returns true on success, false otherwise.
  The flow field must be a 2-channel, floating-point matrix (CV_32FC2). First channel corresponds
  to the flow in the horizontal direction (u), second - vertical (v).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/video/VariationalRefinement.html" title="class in org.opencv.video"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/video/Video.html" target="_top">Frames</a></li>
<li><a href="Video.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2021-12-25 08:13:27 / OpenCV 4.5.5</small></p>
</body>
</html>
