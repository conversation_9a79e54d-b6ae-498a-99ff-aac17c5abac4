<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_292) on Sat Dec 25 08:13:30 UTC 2021 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>org.opencv.ml Class Hierarchy (OpenCV 4.5.5 Java documentation)</title>
<meta name="date" content="2021-12-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.opencv.ml Class Hierarchy (OpenCV 4.5.5 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/imgproc/package-tree.html">Prev</a></li>
<li><a href="../../../org/opencv/objdetect/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/ml/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">Hierarchy For Package org.opencv.ml</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core"><span class="typeNameLink">Algorithm</span></a>
<ul>
<li type="circle">org.opencv.ml.<a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml"><span class="typeNameLink">StatModel</span></a>
<ul>
<li type="circle">org.opencv.ml.<a href="../../../org/opencv/ml/ANN_MLP.html" title="class in org.opencv.ml"><span class="typeNameLink">ANN_MLP</span></a></li>
<li type="circle">org.opencv.ml.<a href="../../../org/opencv/ml/DTrees.html" title="class in org.opencv.ml"><span class="typeNameLink">DTrees</span></a>
<ul>
<li type="circle">org.opencv.ml.<a href="../../../org/opencv/ml/Boost.html" title="class in org.opencv.ml"><span class="typeNameLink">Boost</span></a></li>
<li type="circle">org.opencv.ml.<a href="../../../org/opencv/ml/RTrees.html" title="class in org.opencv.ml"><span class="typeNameLink">RTrees</span></a></li>
</ul>
</li>
<li type="circle">org.opencv.ml.<a href="../../../org/opencv/ml/EM.html" title="class in org.opencv.ml"><span class="typeNameLink">EM</span></a></li>
<li type="circle">org.opencv.ml.<a href="../../../org/opencv/ml/KNearest.html" title="class in org.opencv.ml"><span class="typeNameLink">KNearest</span></a></li>
<li type="circle">org.opencv.ml.<a href="../../../org/opencv/ml/LogisticRegression.html" title="class in org.opencv.ml"><span class="typeNameLink">LogisticRegression</span></a></li>
<li type="circle">org.opencv.ml.<a href="../../../org/opencv/ml/NormalBayesClassifier.html" title="class in org.opencv.ml"><span class="typeNameLink">NormalBayesClassifier</span></a></li>
<li type="circle">org.opencv.ml.<a href="../../../org/opencv/ml/SVM.html" title="class in org.opencv.ml"><span class="typeNameLink">SVM</span></a></li>
<li type="circle">org.opencv.ml.<a href="../../../org/opencv/ml/SVMSGD.html" title="class in org.opencv.ml"><span class="typeNameLink">SVMSGD</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">org.opencv.ml.<a href="../../../org/opencv/ml/Ml.html" title="class in org.opencv.ml"><span class="typeNameLink">Ml</span></a></li>
<li type="circle">org.opencv.ml.<a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml"><span class="typeNameLink">ParamGrid</span></a></li>
<li type="circle">org.opencv.ml.<a href="../../../org/opencv/ml/TrainData.html" title="class in org.opencv.ml"><span class="typeNameLink">TrainData</span></a></li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/imgproc/package-tree.html">Prev</a></li>
<li><a href="../../../org/opencv/objdetect/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/ml/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2021-12-25 08:13:27 / OpenCV 4.5.5</small></p>
</body>
</html>
