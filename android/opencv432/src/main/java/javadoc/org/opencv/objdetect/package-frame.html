<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_292) on Sat Dec 25 08:13:30 UTC 2021 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>org.opencv.objdetect (OpenCV 4.5.5 Java documentation)</title>
<meta name="date" content="2021-12-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../org/opencv/objdetect/package-summary.html" target="classFrame">org.opencv.objdetect</a></h1>
<div class="indexContainer">
<h2 title="Classes">Classes</h2>
<ul title="Classes">
<li><a href="BaseCascadeClassifier.html" title="class in org.opencv.objdetect" target="classFrame">BaseCascadeClassifier</a></li>
<li><a href="CascadeClassifier.html" title="class in org.opencv.objdetect" target="classFrame">CascadeClassifier</a></li>
<li><a href="FaceDetectorYN.html" title="class in org.opencv.objdetect" target="classFrame">FaceDetectorYN</a></li>
<li><a href="FaceRecognizerSF.html" title="class in org.opencv.objdetect" target="classFrame">FaceRecognizerSF</a></li>
<li><a href="HOGDescriptor.html" title="class in org.opencv.objdetect" target="classFrame">HOGDescriptor</a></li>
<li><a href="Objdetect.html" title="class in org.opencv.objdetect" target="classFrame">Objdetect</a></li>
<li><a href="QRCodeDetector.html" title="class in org.opencv.objdetect" target="classFrame">QRCodeDetector</a></li>
<li><a href="QRCodeEncoder.html" title="class in org.opencv.objdetect" target="classFrame">QRCodeEncoder</a></li>
<li><a href="QRCodeEncoder_Params.html" title="class in org.opencv.objdetect" target="classFrame">QRCodeEncoder_Params</a></li>
</ul>
</div>
</body>
</html>
