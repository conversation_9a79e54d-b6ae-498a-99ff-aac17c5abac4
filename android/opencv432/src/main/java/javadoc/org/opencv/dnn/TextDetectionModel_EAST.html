<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_292) on Sat Dec 25 08:13:29 UTC 2021 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>TextDetectionModel_EAST (OpenCV 4.5.5 Java documentation)</title>
<meta name="date" content="2021-12-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TextDetectionModel_EAST (OpenCV 4.5.5 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/dnn/TextDetectionModel_DB.html" title="class in org.opencv.dnn"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/dnn/TextRecognitionModel.html" title="class in org.opencv.dnn"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/dnn/TextDetectionModel_EAST.html" target="_top">Frames</a></li>
<li><a href="TextDetectionModel_EAST.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.dnn</div>
<h2 title="Class TextDetectionModel_EAST" class="title">Class TextDetectionModel_EAST</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">org.opencv.dnn.Model</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/dnn/TextDetectionModel.html" title="class in org.opencv.dnn">org.opencv.dnn.TextDetectionModel</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.dnn.TextDetectionModel_EAST</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">TextDetectionModel_EAST</span>
extends <a href="../../../org/opencv/dnn/TextDetectionModel.html" title="class in org.opencv.dnn">TextDetectionModel</a></pre>
<div class="block">This class represents high-level API for text detection DL networks compatible with EAST model.

 Configurable parameters:
 - (float) confThreshold - used to filter boxes by confidences, default: 0.5f
 - (float) nmsThreshold - used in non maximum suppression, default: 0.0f</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/TextDetectionModel_EAST.html#TextDetectionModel_EAST-org.opencv.dnn.Net-">TextDetectionModel_EAST</a></span>(<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;network)</code>
<div class="block">Create text detection algorithm from deep learning network</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/TextDetectionModel_EAST.html#TextDetectionModel_EAST-java.lang.String-">TextDetectionModel_EAST</a></span>(java.lang.String&nbsp;model)</code>
<div class="block">Create text detection model from network represented in one of the supported formats.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/TextDetectionModel_EAST.html#TextDetectionModel_EAST-java.lang.String-java.lang.String-">TextDetectionModel_EAST</a></span>(java.lang.String&nbsp;model,
                       java.lang.String&nbsp;config)</code>
<div class="block">Create text detection model from network represented in one of the supported formats.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/TextDetectionModel_EAST.html" title="class in org.opencv.dnn">TextDetectionModel_EAST</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/TextDetectionModel_EAST.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/TextDetectionModel_EAST.html#getConfidenceThreshold--">getConfidenceThreshold</a></span>()</code>
<div class="block">Get the detection confidence threshold</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/TextDetectionModel_EAST.html#getNMSThreshold--">getNMSThreshold</a></span>()</code>
<div class="block">Get the detection confidence threshold</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/dnn/TextDetectionModel_EAST.html" title="class in org.opencv.dnn">TextDetectionModel_EAST</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/TextDetectionModel_EAST.html#setConfidenceThreshold-float-">setConfidenceThreshold</a></span>(float&nbsp;confThreshold)</code>
<div class="block">Set the detection confidence threshold</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/dnn/TextDetectionModel_EAST.html" title="class in org.opencv.dnn">TextDetectionModel_EAST</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/TextDetectionModel_EAST.html#setNMSThreshold-float-">setNMSThreshold</a></span>(float&nbsp;nmsThreshold)</code>
<div class="block">Set the detection NMS filter threshold</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.dnn.TextDetectionModel">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.dnn.<a href="../../../org/opencv/dnn/TextDetectionModel.html" title="class in org.opencv.dnn">TextDetectionModel</a></h3>
<code><a href="../../../org/opencv/dnn/TextDetectionModel.html#detect-org.opencv.core.Mat-java.util.List-">detect</a>, <a href="../../../org/opencv/dnn/TextDetectionModel.html#detect-org.opencv.core.Mat-java.util.List-org.opencv.core.MatOfFloat-">detect</a>, <a href="../../../org/opencv/dnn/TextDetectionModel.html#detectTextRectangles-org.opencv.core.Mat-org.opencv.core.MatOfRotatedRect-">detectTextRectangles</a>, <a href="../../../org/opencv/dnn/TextDetectionModel.html#detectTextRectangles-org.opencv.core.Mat-org.opencv.core.MatOfRotatedRect-org.opencv.core.MatOfFloat-">detectTextRectangles</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.dnn.Model">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.dnn.<a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">Model</a></h3>
<code><a href="../../../org/opencv/dnn/Model.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/dnn/Model.html#predict-org.opencv.core.Mat-java.util.List-">predict</a>, <a href="../../../org/opencv/dnn/Model.html#setInputCrop-boolean-">setInputCrop</a>, <a href="../../../org/opencv/dnn/Model.html#setInputMean-org.opencv.core.Scalar-">setInputMean</a>, <a href="../../../org/opencv/dnn/Model.html#setInputParams--">setInputParams</a>, <a href="../../../org/opencv/dnn/Model.html#setInputParams-double-">setInputParams</a>, <a href="../../../org/opencv/dnn/Model.html#setInputParams-double-org.opencv.core.Size-">setInputParams</a>, <a href="../../../org/opencv/dnn/Model.html#setInputParams-double-org.opencv.core.Size-org.opencv.core.Scalar-">setInputParams</a>, <a href="../../../org/opencv/dnn/Model.html#setInputParams-double-org.opencv.core.Size-org.opencv.core.Scalar-boolean-">setInputParams</a>, <a href="../../../org/opencv/dnn/Model.html#setInputParams-double-org.opencv.core.Size-org.opencv.core.Scalar-boolean-boolean-">setInputParams</a>, <a href="../../../org/opencv/dnn/Model.html#setInputScale-double-">setInputScale</a>, <a href="../../../org/opencv/dnn/Model.html#setInputSize-int-int-">setInputSize</a>, <a href="../../../org/opencv/dnn/Model.html#setInputSize-org.opencv.core.Size-">setInputSize</a>, <a href="../../../org/opencv/dnn/Model.html#setInputSwapRB-boolean-">setInputSwapRB</a>, <a href="../../../org/opencv/dnn/Model.html#setPreferableBackend-int-">setPreferableBackend</a>, <a href="../../../org/opencv/dnn/Model.html#setPreferableTarget-int-">setPreferableTarget</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="TextDetectionModel_EAST-org.opencv.dnn.Net-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TextDetectionModel_EAST</h4>
<pre>public&nbsp;TextDetectionModel_EAST(<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;network)</pre>
<div class="block">Create text detection algorithm from deep learning network</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>network</code> - Net object</dd>
</dl>
</li>
</ul>
<a name="TextDetectionModel_EAST-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TextDetectionModel_EAST</h4>
<pre>public&nbsp;TextDetectionModel_EAST(java.lang.String&nbsp;model)</pre>
<div class="block">Create text detection model from network represented in one of the supported formats.
 An order of <code>model</code> and <code>config</code> arguments does not matter.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>model</code> - Binary file contains trained weights.</dd>
</dl>
</li>
</ul>
<a name="TextDetectionModel_EAST-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TextDetectionModel_EAST</h4>
<pre>public&nbsp;TextDetectionModel_EAST(java.lang.String&nbsp;model,
                               java.lang.String&nbsp;config)</pre>
<div class="block">Create text detection model from network represented in one of the supported formats.
 An order of <code>model</code> and <code>config</code> arguments does not matter.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>model</code> - Binary file contains trained weights.</dd>
<dd><code>config</code> - Text file contains network configuration.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/TextDetectionModel_EAST.html" title="class in org.opencv.dnn">TextDetectionModel_EAST</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="getConfidenceThreshold--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConfidenceThreshold</h4>
<pre>public&nbsp;float&nbsp;getConfidenceThreshold()</pre>
<div class="block">Get the detection confidence threshold</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getNMSThreshold--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNMSThreshold</h4>
<pre>public&nbsp;float&nbsp;getNMSThreshold()</pre>
<div class="block">Get the detection confidence threshold</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setConfidenceThreshold-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConfidenceThreshold</h4>
<pre>public&nbsp;<a href="../../../org/opencv/dnn/TextDetectionModel_EAST.html" title="class in org.opencv.dnn">TextDetectionModel_EAST</a>&nbsp;setConfidenceThreshold(float&nbsp;confThreshold)</pre>
<div class="block">Set the detection confidence threshold</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>confThreshold</code> - A threshold used to filter boxes by confidences</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setNMSThreshold-float-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setNMSThreshold</h4>
<pre>public&nbsp;<a href="../../../org/opencv/dnn/TextDetectionModel_EAST.html" title="class in org.opencv.dnn">TextDetectionModel_EAST</a>&nbsp;setNMSThreshold(float&nbsp;nmsThreshold)</pre>
<div class="block">Set the detection NMS filter threshold</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nmsThreshold</code> - A threshold used in non maximum suppression</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/dnn/TextDetectionModel_DB.html" title="class in org.opencv.dnn"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/dnn/TextRecognitionModel.html" title="class in org.opencv.dnn"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/dnn/TextDetectionModel_EAST.html" target="_top">Frames</a></li>
<li><a href="TextDetectionModel_EAST.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2021-12-25 08:13:27 / OpenCV 4.5.5</small></p>
</body>
</html>
