<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_292) on Sat Dec 25 08:13:28 UTC 2021 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>VideoWriter (OpenCV 4.5.5 Java documentation)</title>
<meta name="date" content="2021-12-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="VideoWriter (OpenCV 4.5.5 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/videoio/Videoio.html" title="class in org.opencv.videoio"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/videoio/VideoWriter.html" target="_top">Frames</a></li>
<li><a href="VideoWriter.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.videoio</div>
<h2 title="Class VideoWriter" class="title">Class VideoWriter</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.videoio.VideoWriter</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">VideoWriter</span>
extends java.lang.Object</pre>
<div class="block">Video writer class.

 The class provides C++ API for writing video files or image sequences.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#VideoWriter--">VideoWriter</a></span>()</code>
<div class="block">Default constructors

     The constructors/functions initialize video writers.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#VideoWriter-java.lang.String-int-double-org.opencv.core.Size-">VideoWriter</a></span>(java.lang.String&nbsp;filename,
           int&nbsp;fourcc,
           double&nbsp;fps,
           <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#VideoWriter-java.lang.String-int-double-org.opencv.core.Size-boolean-">VideoWriter</a></span>(java.lang.String&nbsp;filename,
           int&nbsp;fourcc,
           double&nbsp;fps,
           <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
           boolean&nbsp;isColor)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#VideoWriter-java.lang.String-int-double-org.opencv.core.Size-org.opencv.core.MatOfInt-">VideoWriter</a></span>(java.lang.String&nbsp;filename,
           int&nbsp;fourcc,
           double&nbsp;fps,
           <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
           <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code>
<div class="block">The <code>params</code> parameter allows to specify extra encoder parameters encoded as pairs (paramId_1, paramValue_1, paramId_2, paramValue_2, ...</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#VideoWriter-java.lang.String-int-int-double-org.opencv.core.Size-">VideoWriter</a></span>(java.lang.String&nbsp;filename,
           int&nbsp;apiPreference,
           int&nbsp;fourcc,
           double&nbsp;fps,
           <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize)</code>
<div class="block">The <code>apiPreference</code> parameter allows to specify API backends to use.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#VideoWriter-java.lang.String-int-int-double-org.opencv.core.Size-boolean-">VideoWriter</a></span>(java.lang.String&nbsp;filename,
           int&nbsp;apiPreference,
           int&nbsp;fourcc,
           double&nbsp;fps,
           <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
           boolean&nbsp;isColor)</code>
<div class="block">The <code>apiPreference</code> parameter allows to specify API backends to use.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#VideoWriter-java.lang.String-int-int-double-org.opencv.core.Size-org.opencv.core.MatOfInt-">VideoWriter</a></span>(java.lang.String&nbsp;filename,
           int&nbsp;apiPreference,
           int&nbsp;fourcc,
           double&nbsp;fps,
           <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
           <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/videoio/VideoWriter.html" title="class in org.opencv.videoio">VideoWriter</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#fourcc-char-char-char-char-">fourcc</a></span>(char&nbsp;c1,
      char&nbsp;c2,
      char&nbsp;c3,
      char&nbsp;c4)</code>
<div class="block">Concatenates 4 chars to a fourcc code</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#get-int-">get</a></span>(int&nbsp;propId)</code>
<div class="block">Returns the specified VideoWriter property</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#getBackendName--">getBackendName</a></span>()</code>
<div class="block">Returns used backend API name

      <b>Note:</b> Stream should be opened.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#getNativeObjAddr--">getNativeObjAddr</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#isOpened--">isOpened</a></span>()</code>
<div class="block">Returns true if video writer has been successfully initialized.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#open-java.lang.String-int-double-org.opencv.core.Size-">open</a></span>(java.lang.String&nbsp;filename,
    int&nbsp;fourcc,
    double&nbsp;fps,
    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize)</code>
<div class="block">Initializes or reinitializes video writer.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#open-java.lang.String-int-double-org.opencv.core.Size-boolean-">open</a></span>(java.lang.String&nbsp;filename,
    int&nbsp;fourcc,
    double&nbsp;fps,
    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
    boolean&nbsp;isColor)</code>
<div class="block">Initializes or reinitializes video writer.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#open-java.lang.String-int-double-org.opencv.core.Size-org.opencv.core.MatOfInt-">open</a></span>(java.lang.String&nbsp;filename,
    int&nbsp;fourcc,
    double&nbsp;fps,
    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
    <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#open-java.lang.String-int-int-double-org.opencv.core.Size-">open</a></span>(java.lang.String&nbsp;filename,
    int&nbsp;apiPreference,
    int&nbsp;fourcc,
    double&nbsp;fps,
    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#open-java.lang.String-int-int-double-org.opencv.core.Size-boolean-">open</a></span>(java.lang.String&nbsp;filename,
    int&nbsp;apiPreference,
    int&nbsp;fourcc,
    double&nbsp;fps,
    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
    boolean&nbsp;isColor)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#open-java.lang.String-int-int-double-org.opencv.core.Size-org.opencv.core.MatOfInt-">open</a></span>(java.lang.String&nbsp;filename,
    int&nbsp;apiPreference,
    int&nbsp;fourcc,
    double&nbsp;fps,
    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
    <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#release--">release</a></span>()</code>
<div class="block">Closes the video writer.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#set-int-double-">set</a></span>(int&nbsp;propId,
   double&nbsp;value)</code>
<div class="block">Sets a property in the VideoWriter.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#write-org.opencv.core.Mat-">write</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</code>
<div class="block">Writes the next video frame</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="VideoWriter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VideoWriter</h4>
<pre>public&nbsp;VideoWriter()</pre>
<div class="block">Default constructors

     The constructors/functions initialize video writers.
 <ul>
   <li>
        On Linux FFMPEG is used to write videos;
   </li>
   <li>
        On Windows FFMPEG or MSWF or DSHOW is used;
   </li>
   <li>
        On MacOSX AVFoundation is used.
   </li>
 </ul></div>
</li>
</ul>
<a name="VideoWriter-java.lang.String-int-double-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VideoWriter</h4>
<pre>public&nbsp;VideoWriter(java.lang.String&nbsp;filename,
                   int&nbsp;fourcc,
                   double&nbsp;fps,
                   <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - Name of the output video file.</dd>
<dd><code>fourcc</code> - 4-character code of codec used to compress the frames. For example,
     VideoWriter::fourcc('P','I','M','1') is a MPEG-1 codec, VideoWriter::fourcc('M','J','P','G') is a
     motion-jpeg codec etc. List of codes can be obtained at [Video Codecs by
     FOURCC](http://www.fourcc.org/codecs.php) page. FFMPEG backend with MP4 container natively uses
     other values as fourcc code: see [ObjectType](http://mp4ra.org/#/codecs),
     so you may receive a warning message from OpenCV about fourcc code conversion.</dd>
<dd><code>fps</code> - Framerate of the created video stream.</dd>
<dd><code>frameSize</code> - Size of the video frames.
     will work with grayscale frames.

     <b>Tips</b>:
 <ul>
   <li>
      With some backends <code>fourcc=-1</code> pops up the codec selection dialog from the system.
   </li>
   <li>
      To save image sequence use a proper filename (eg. <code>img_%02d.jpg</code>) and <code>fourcc=0</code>
       OR <code>fps=0</code>. Use uncompressed image format (eg. <code>img_%02d.BMP</code>) to save raw frames.
   </li>
   <li>
      Most codecs are lossy. If you want lossless video file you need to use a lossless codecs
       (eg. FFMPEG FFV1, Huffman HFYU, Lagarith LAGS, etc...)
   </li>
   <li>
      If FFMPEG is enabled, using <code>codec=0; fps=0;</code> you can create an uncompressed (raw) video file.
   </li>
 </ul></dd>
</dl>
</li>
</ul>
<a name="VideoWriter-java.lang.String-int-double-org.opencv.core.Size-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VideoWriter</h4>
<pre>public&nbsp;VideoWriter(java.lang.String&nbsp;filename,
                   int&nbsp;fourcc,
                   double&nbsp;fps,
                   <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
                   boolean&nbsp;isColor)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - Name of the output video file.</dd>
<dd><code>fourcc</code> - 4-character code of codec used to compress the frames. For example,
     VideoWriter::fourcc('P','I','M','1') is a MPEG-1 codec, VideoWriter::fourcc('M','J','P','G') is a
     motion-jpeg codec etc. List of codes can be obtained at [Video Codecs by
     FOURCC](http://www.fourcc.org/codecs.php) page. FFMPEG backend with MP4 container natively uses
     other values as fourcc code: see [ObjectType](http://mp4ra.org/#/codecs),
     so you may receive a warning message from OpenCV about fourcc code conversion.</dd>
<dd><code>fps</code> - Framerate of the created video stream.</dd>
<dd><code>frameSize</code> - Size of the video frames.</dd>
<dd><code>isColor</code> - If it is not zero, the encoder will expect and encode color frames, otherwise it
     will work with grayscale frames.

     <b>Tips</b>:
 <ul>
   <li>
      With some backends <code>fourcc=-1</code> pops up the codec selection dialog from the system.
   </li>
   <li>
      To save image sequence use a proper filename (eg. <code>img_%02d.jpg</code>) and <code>fourcc=0</code>
       OR <code>fps=0</code>. Use uncompressed image format (eg. <code>img_%02d.BMP</code>) to save raw frames.
   </li>
   <li>
      Most codecs are lossy. If you want lossless video file you need to use a lossless codecs
       (eg. FFMPEG FFV1, Huffman HFYU, Lagarith LAGS, etc...)
   </li>
   <li>
      If FFMPEG is enabled, using <code>codec=0; fps=0;</code> you can create an uncompressed (raw) video file.
   </li>
 </ul></dd>
</dl>
</li>
</ul>
<a name="VideoWriter-java.lang.String-int-double-org.opencv.core.Size-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VideoWriter</h4>
<pre>public&nbsp;VideoWriter(java.lang.String&nbsp;filename,
                   int&nbsp;fourcc,
                   double&nbsp;fps,
                   <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
                   <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</pre>
<div class="block">The <code>params</code> parameter allows to specify extra encoder parameters encoded as pairs (paramId_1, paramValue_1, paramId_2, paramValue_2, ... .)
 see cv::VideoWriterProperties</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - automatically generated</dd>
<dd><code>fourcc</code> - automatically generated</dd>
<dd><code>fps</code> - automatically generated</dd>
<dd><code>frameSize</code> - automatically generated</dd>
<dd><code>params</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="VideoWriter-java.lang.String-int-int-double-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VideoWriter</h4>
<pre>public&nbsp;VideoWriter(java.lang.String&nbsp;filename,
                   int&nbsp;apiPreference,
                   int&nbsp;fourcc,
                   double&nbsp;fps,
                   <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize)</pre>
<div class="block">The <code>apiPreference</code> parameter allows to specify API backends to use. Can be used to enforce a specific reader implementation
     if multiple are available: e.g. cv::CAP_FFMPEG or cv::CAP_GSTREAMER.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - automatically generated</dd>
<dd><code>apiPreference</code> - automatically generated</dd>
<dd><code>fourcc</code> - automatically generated</dd>
<dd><code>fps</code> - automatically generated</dd>
<dd><code>frameSize</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="VideoWriter-java.lang.String-int-int-double-org.opencv.core.Size-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VideoWriter</h4>
<pre>public&nbsp;VideoWriter(java.lang.String&nbsp;filename,
                   int&nbsp;apiPreference,
                   int&nbsp;fourcc,
                   double&nbsp;fps,
                   <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
                   boolean&nbsp;isColor)</pre>
<div class="block">The <code>apiPreference</code> parameter allows to specify API backends to use. Can be used to enforce a specific reader implementation
     if multiple are available: e.g. cv::CAP_FFMPEG or cv::CAP_GSTREAMER.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - automatically generated</dd>
<dd><code>apiPreference</code> - automatically generated</dd>
<dd><code>fourcc</code> - automatically generated</dd>
<dd><code>fps</code> - automatically generated</dd>
<dd><code>frameSize</code> - automatically generated</dd>
<dd><code>isColor</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="VideoWriter-java.lang.String-int-int-double-org.opencv.core.Size-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>VideoWriter</h4>
<pre>public&nbsp;VideoWriter(java.lang.String&nbsp;filename,
                   int&nbsp;apiPreference,
                   int&nbsp;fourcc,
                   double&nbsp;fps,
                   <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
                   <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/videoio/VideoWriter.html" title="class in org.opencv.videoio">VideoWriter</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="fourcc-char-char-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fourcc</h4>
<pre>public static&nbsp;int&nbsp;fourcc(char&nbsp;c1,
                         char&nbsp;c2,
                         char&nbsp;c3,
                         char&nbsp;c4)</pre>
<div class="block">Concatenates 4 chars to a fourcc code</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>c1</code> - automatically generated</dd>
<dd><code>c2</code> - automatically generated</dd>
<dd><code>c3</code> - automatically generated</dd>
<dd><code>c4</code> - automatically generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a fourcc code

     This static method constructs the fourcc code of the codec to be used in the constructor
     VideoWriter::VideoWriter or VideoWriter::open.</dd>
</dl>
</li>
</ul>
<a name="get-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get</h4>
<pre>public&nbsp;double&nbsp;get(int&nbsp;propId)</pre>
<div class="block">Returns the specified VideoWriter property</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>propId</code> - Property identifier from cv::VideoWriterProperties (eg. cv::VIDEOWRITER_PROP_QUALITY)
      or one of REF: videoio_flags_others</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Value for the specified property. Value 0 is returned when querying a property that is
      not supported by the backend used by the VideoWriter instance.</dd>
</dl>
</li>
</ul>
<a name="getBackendName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBackendName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getBackendName()</pre>
<div class="block">Returns used backend API name

      <b>Note:</b> Stream should be opened.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getNativeObjAddr--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNativeObjAddr</h4>
<pre>public&nbsp;long&nbsp;getNativeObjAddr()</pre>
</li>
</ul>
<a name="isOpened--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isOpened</h4>
<pre>public&nbsp;boolean&nbsp;isOpened()</pre>
<div class="block">Returns true if video writer has been successfully initialized.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="open-java.lang.String-int-double-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;boolean&nbsp;open(java.lang.String&nbsp;filename,
                    int&nbsp;fourcc,
                    double&nbsp;fps,
                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize)</pre>
<div class="block">Initializes or reinitializes video writer.

     The method opens video writer. Parameters are the same as in the constructor
     VideoWriter::VideoWriter.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - automatically generated</dd>
<dd><code>fourcc</code> - automatically generated</dd>
<dd><code>fps</code> - automatically generated</dd>
<dd><code>frameSize</code> - automatically generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if video writer has been successfully initialized

     The method first calls VideoWriter::release to close the already opened file.</dd>
</dl>
</li>
</ul>
<a name="open-java.lang.String-int-double-org.opencv.core.Size-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;boolean&nbsp;open(java.lang.String&nbsp;filename,
                    int&nbsp;fourcc,
                    double&nbsp;fps,
                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
                    boolean&nbsp;isColor)</pre>
<div class="block">Initializes or reinitializes video writer.

     The method opens video writer. Parameters are the same as in the constructor
     VideoWriter::VideoWriter.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - automatically generated</dd>
<dd><code>fourcc</code> - automatically generated</dd>
<dd><code>fps</code> - automatically generated</dd>
<dd><code>frameSize</code> - automatically generated</dd>
<dd><code>isColor</code> - automatically generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if video writer has been successfully initialized

     The method first calls VideoWriter::release to close the already opened file.</dd>
</dl>
</li>
</ul>
<a name="open-java.lang.String-int-double-org.opencv.core.Size-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;boolean&nbsp;open(java.lang.String&nbsp;filename,
                    int&nbsp;fourcc,
                    double&nbsp;fps,
                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
                    <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</pre>
</li>
</ul>
<a name="open-java.lang.String-int-int-double-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;boolean&nbsp;open(java.lang.String&nbsp;filename,
                    int&nbsp;apiPreference,
                    int&nbsp;fourcc,
                    double&nbsp;fps,
                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize)</pre>
</li>
</ul>
<a name="open-java.lang.String-int-int-double-org.opencv.core.Size-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;boolean&nbsp;open(java.lang.String&nbsp;filename,
                    int&nbsp;apiPreference,
                    int&nbsp;fourcc,
                    double&nbsp;fps,
                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
                    boolean&nbsp;isColor)</pre>
</li>
</ul>
<a name="open-java.lang.String-int-int-double-org.opencv.core.Size-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;boolean&nbsp;open(java.lang.String&nbsp;filename,
                    int&nbsp;apiPreference,
                    int&nbsp;fourcc,
                    double&nbsp;fps,
                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
                    <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</pre>
</li>
</ul>
<a name="release--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>release</h4>
<pre>public&nbsp;void&nbsp;release()</pre>
<div class="block">Closes the video writer.

     The method is automatically called by subsequent VideoWriter::open and by the VideoWriter
     destructor.</div>
</li>
</ul>
<a name="set-int-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set</h4>
<pre>public&nbsp;boolean&nbsp;set(int&nbsp;propId,
                   double&nbsp;value)</pre>
<div class="block">Sets a property in the VideoWriter.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>propId</code> - Property identifier from cv::VideoWriterProperties (eg. cv::VIDEOWRITER_PROP_QUALITY)
      or one of REF: videoio_flags_others</dd>
<dd><code>value</code> - Value of the property.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the property is supported by the backend used by the VideoWriter instance.</dd>
</dl>
</li>
</ul>
<a name="write-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>write</h4>
<pre>public&nbsp;void&nbsp;write(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</pre>
<div class="block">Writes the next video frame</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - The written frame. In general, color images are expected in BGR format.

     The function/method writes the specified image to video file. It must have the same size as has
     been specified when opening the video writer.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/videoio/Videoio.html" title="class in org.opencv.videoio"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/videoio/VideoWriter.html" target="_top">Frames</a></li>
<li><a href="VideoWriter.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2021-12-25 08:13:27 / OpenCV 4.5.5</small></p>
</body>
</html>
