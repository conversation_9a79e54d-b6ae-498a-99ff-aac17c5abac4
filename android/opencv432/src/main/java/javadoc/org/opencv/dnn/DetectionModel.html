<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_292) on Sat Dec 25 08:13:29 UTC 2021 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>DetectionModel (OpenCV 4.5.5 Java documentation)</title>
<meta name="date" content="2021-12-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DetectionModel (OpenCV 4.5.5 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/dnn/ClassificationModel.html" title="class in org.opencv.dnn"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/dnn/DictValue.html" title="class in org.opencv.dnn"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/dnn/DetectionModel.html" target="_top">Frames</a></li>
<li><a href="DetectionModel.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.dnn</div>
<h2 title="Class DetectionModel" class="title">Class DetectionModel</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">org.opencv.dnn.Model</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.dnn.DetectionModel</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">DetectionModel</span>
extends <a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">Model</a></pre>
<div class="block">This class represents high-level API for object detection networks.

 DetectionModel allows to set params for preprocessing input image.
 DetectionModel creates net from file with trained weights and config,
 sets preprocessing input, runs forward pass and return result detections.
 For DetectionModel SSD, Faster R-CNN, YOLO topologies are supported.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/DetectionModel.html#DetectionModel-org.opencv.dnn.Net-">DetectionModel</a></span>(<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;network)</code>
<div class="block">Create model from deep learning network.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/DetectionModel.html#DetectionModel-java.lang.String-">DetectionModel</a></span>(java.lang.String&nbsp;model)</code>
<div class="block">Create detection model from network represented in one of the supported formats.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/DetectionModel.html#DetectionModel-java.lang.String-java.lang.String-">DetectionModel</a></span>(java.lang.String&nbsp;model,
              java.lang.String&nbsp;config)</code>
<div class="block">Create detection model from network represented in one of the supported formats.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/DetectionModel.html" title="class in org.opencv.dnn">DetectionModel</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/DetectionModel.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/DetectionModel.html#detect-org.opencv.core.Mat-org.opencv.core.MatOfInt-org.opencv.core.MatOfFloat-org.opencv.core.MatOfRect-">detect</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;frame,
      <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;classIds,
      <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;confidences,
      <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;boxes)</code>
<div class="block">Given the <code>input</code> frame, create input blob, run net and return result detections.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/DetectionModel.html#detect-org.opencv.core.Mat-org.opencv.core.MatOfInt-org.opencv.core.MatOfFloat-org.opencv.core.MatOfRect-float-">detect</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;frame,
      <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;classIds,
      <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;confidences,
      <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;boxes,
      float&nbsp;confThreshold)</code>
<div class="block">Given the <code>input</code> frame, create input blob, run net and return result detections.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/DetectionModel.html#detect-org.opencv.core.Mat-org.opencv.core.MatOfInt-org.opencv.core.MatOfFloat-org.opencv.core.MatOfRect-float-float-">detect</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;frame,
      <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;classIds,
      <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;confidences,
      <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;boxes,
      float&nbsp;confThreshold,
      float&nbsp;nmsThreshold)</code>
<div class="block">Given the <code>input</code> frame, create input blob, run net and return result detections.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/DetectionModel.html#getNmsAcrossClasses--">getNmsAcrossClasses</a></span>()</code>
<div class="block">Getter for nmsAcrossClasses.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/dnn/DetectionModel.html" title="class in org.opencv.dnn">DetectionModel</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/DetectionModel.html#setNmsAcrossClasses-boolean-">setNmsAcrossClasses</a></span>(boolean&nbsp;value)</code>
<div class="block">nmsAcrossClasses defaults to false,
 such that when non max suppression is used during the detect() function, it will do so per-class.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.dnn.Model">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.dnn.<a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">Model</a></h3>
<code><a href="../../../org/opencv/dnn/Model.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/dnn/Model.html#predict-org.opencv.core.Mat-java.util.List-">predict</a>, <a href="../../../org/opencv/dnn/Model.html#setInputCrop-boolean-">setInputCrop</a>, <a href="../../../org/opencv/dnn/Model.html#setInputMean-org.opencv.core.Scalar-">setInputMean</a>, <a href="../../../org/opencv/dnn/Model.html#setInputParams--">setInputParams</a>, <a href="../../../org/opencv/dnn/Model.html#setInputParams-double-">setInputParams</a>, <a href="../../../org/opencv/dnn/Model.html#setInputParams-double-org.opencv.core.Size-">setInputParams</a>, <a href="../../../org/opencv/dnn/Model.html#setInputParams-double-org.opencv.core.Size-org.opencv.core.Scalar-">setInputParams</a>, <a href="../../../org/opencv/dnn/Model.html#setInputParams-double-org.opencv.core.Size-org.opencv.core.Scalar-boolean-">setInputParams</a>, <a href="../../../org/opencv/dnn/Model.html#setInputParams-double-org.opencv.core.Size-org.opencv.core.Scalar-boolean-boolean-">setInputParams</a>, <a href="../../../org/opencv/dnn/Model.html#setInputScale-double-">setInputScale</a>, <a href="../../../org/opencv/dnn/Model.html#setInputSize-int-int-">setInputSize</a>, <a href="../../../org/opencv/dnn/Model.html#setInputSize-org.opencv.core.Size-">setInputSize</a>, <a href="../../../org/opencv/dnn/Model.html#setInputSwapRB-boolean-">setInputSwapRB</a>, <a href="../../../org/opencv/dnn/Model.html#setPreferableBackend-int-">setPreferableBackend</a>, <a href="../../../org/opencv/dnn/Model.html#setPreferableTarget-int-">setPreferableTarget</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="DetectionModel-org.opencv.dnn.Net-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DetectionModel</h4>
<pre>public&nbsp;DetectionModel(<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;network)</pre>
<div class="block">Create model from deep learning network.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>network</code> - Net object.</dd>
</dl>
</li>
</ul>
<a name="DetectionModel-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DetectionModel</h4>
<pre>public&nbsp;DetectionModel(java.lang.String&nbsp;model)</pre>
<div class="block">Create detection model from network represented in one of the supported formats.
 An order of <code>model</code> and <code>config</code> arguments does not matter.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>model</code> - Binary file contains trained weights.</dd>
</dl>
</li>
</ul>
<a name="DetectionModel-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DetectionModel</h4>
<pre>public&nbsp;DetectionModel(java.lang.String&nbsp;model,
                      java.lang.String&nbsp;config)</pre>
<div class="block">Create detection model from network represented in one of the supported formats.
 An order of <code>model</code> and <code>config</code> arguments does not matter.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>model</code> - Binary file contains trained weights.</dd>
<dd><code>config</code> - Text file contains network configuration.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/DetectionModel.html" title="class in org.opencv.dnn">DetectionModel</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="detect-org.opencv.core.Mat-org.opencv.core.MatOfInt-org.opencv.core.MatOfFloat-org.opencv.core.MatOfRect-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detect</h4>
<pre>public&nbsp;void&nbsp;detect(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;frame,
                   <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;classIds,
                   <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;confidences,
                   <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;boxes)</pre>
<div class="block">Given the <code>input</code> frame, create input blob, run net and return result detections.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>classIds</code> - Class indexes in result detection.</dd>
<dd><code>confidences</code> - A set of corresponding confidences.</dd>
<dd><code>boxes</code> - A set of bounding boxes.</dd>
<dd><code>frame</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="detect-org.opencv.core.Mat-org.opencv.core.MatOfInt-org.opencv.core.MatOfFloat-org.opencv.core.MatOfRect-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detect</h4>
<pre>public&nbsp;void&nbsp;detect(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;frame,
                   <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;classIds,
                   <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;confidences,
                   <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;boxes,
                   float&nbsp;confThreshold)</pre>
<div class="block">Given the <code>input</code> frame, create input blob, run net and return result detections.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>classIds</code> - Class indexes in result detection.</dd>
<dd><code>confidences</code> - A set of corresponding confidences.</dd>
<dd><code>boxes</code> - A set of bounding boxes.</dd>
<dd><code>confThreshold</code> - A threshold used to filter boxes by confidences.</dd>
<dd><code>frame</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="detect-org.opencv.core.Mat-org.opencv.core.MatOfInt-org.opencv.core.MatOfFloat-org.opencv.core.MatOfRect-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detect</h4>
<pre>public&nbsp;void&nbsp;detect(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;frame,
                   <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;classIds,
                   <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;confidences,
                   <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;boxes,
                   float&nbsp;confThreshold,
                   float&nbsp;nmsThreshold)</pre>
<div class="block">Given the <code>input</code> frame, create input blob, run net and return result detections.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>classIds</code> - Class indexes in result detection.</dd>
<dd><code>confidences</code> - A set of corresponding confidences.</dd>
<dd><code>boxes</code> - A set of bounding boxes.</dd>
<dd><code>confThreshold</code> - A threshold used to filter boxes by confidences.</dd>
<dd><code>nmsThreshold</code> - A threshold used in non maximum suppression.</dd>
<dd><code>frame</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="getNmsAcrossClasses--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNmsAcrossClasses</h4>
<pre>public&nbsp;boolean&nbsp;getNmsAcrossClasses()</pre>
<div class="block">Getter for nmsAcrossClasses. This variable defaults to false,
 such that when non max suppression is used during the detect() function, it will do so only per-class</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setNmsAcrossClasses-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setNmsAcrossClasses</h4>
<pre>public&nbsp;<a href="../../../org/opencv/dnn/DetectionModel.html" title="class in org.opencv.dnn">DetectionModel</a>&nbsp;setNmsAcrossClasses(boolean&nbsp;value)</pre>
<div class="block">nmsAcrossClasses defaults to false,
 such that when non max suppression is used during the detect() function, it will do so per-class.
 This function allows you to toggle this behaviour.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - The new value for nmsAcrossClasses</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/dnn/ClassificationModel.html" title="class in org.opencv.dnn"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/dnn/DictValue.html" title="class in org.opencv.dnn"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/dnn/DetectionModel.html" target="_top">Frames</a></li>
<li><a href="DetectionModel.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2021-12-25 08:13:27 / OpenCV 4.5.5</small></p>
</body>
</html>
