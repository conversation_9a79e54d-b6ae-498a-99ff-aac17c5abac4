<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_292) on Sat Dec 25 08:13:29 UTC 2021 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>OpenCVLoader (OpenCV 4.5.5 Java documentation)</title>
<meta name="date" content="2021-12-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="OpenCVLoader (OpenCV 4.5.5 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/android/LoaderCallbackInterface.html" title="interface in org.opencv.android"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/android/Utils.html" title="class in org.opencv.android"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/android/OpenCVLoader.html" target="_top">Frames</a></li>
<li><a href="OpenCVLoader.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.android</div>
<h2 title="Class OpenCVLoader" class="title">Class OpenCVLoader</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.android.OpenCVLoader</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">OpenCVLoader</span>
extends java.lang.Object</pre>
<div class="block">Helper class provides common initialization methods for OpenCV library.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/OpenCVLoader.html#OPENCV_VERSION">OPENCV_VERSION</a></span></code>
<div class="block">Current OpenCV Library version</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_2_4_10">OPENCV_VERSION_2_4_10</a></span></code>
<div class="block">OpenCV Library version 2.4.10.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_2_4_11">OPENCV_VERSION_2_4_11</a></span></code>
<div class="block">OpenCV Library version 2.4.11.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_2_4_12">OPENCV_VERSION_2_4_12</a></span></code>
<div class="block">OpenCV Library version 2.4.12.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_2_4_13">OPENCV_VERSION_2_4_13</a></span></code>
<div class="block">OpenCV Library version 2.4.13.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_2_4_2">OPENCV_VERSION_2_4_2</a></span></code>
<div class="block">OpenCV Library version 2.4.2.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_2_4_3">OPENCV_VERSION_2_4_3</a></span></code>
<div class="block">OpenCV Library version 2.4.3.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_2_4_4">OPENCV_VERSION_2_4_4</a></span></code>
<div class="block">OpenCV Library version 2.4.4.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_2_4_5">OPENCV_VERSION_2_4_5</a></span></code>
<div class="block">OpenCV Library version 2.4.5.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_2_4_6">OPENCV_VERSION_2_4_6</a></span></code>
<div class="block">OpenCV Library version 2.4.6.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_2_4_7">OPENCV_VERSION_2_4_7</a></span></code>
<div class="block">OpenCV Library version 2.4.7.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_2_4_8">OPENCV_VERSION_2_4_8</a></span></code>
<div class="block">OpenCV Library version 2.4.8.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_2_4_9">OPENCV_VERSION_2_4_9</a></span></code>
<div class="block">OpenCV Library version 2.4.9.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_3_0_0">OPENCV_VERSION_3_0_0</a></span></code>
<div class="block">OpenCV Library version 3.0.0.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_3_1_0">OPENCV_VERSION_3_1_0</a></span></code>
<div class="block">OpenCV Library version 3.1.0.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_3_2_0">OPENCV_VERSION_3_2_0</a></span></code>
<div class="block">OpenCV Library version 3.2.0.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_3_3_0">OPENCV_VERSION_3_3_0</a></span></code>
<div class="block">OpenCV Library version 3.3.0.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_3_4_0">OPENCV_VERSION_3_4_0</a></span></code>
<div class="block">OpenCV Library version 3.4.0.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/android/OpenCVLoader.html#OpenCVLoader--">OpenCVLoader</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/OpenCVLoader.html#initAsync-java.lang.String-android.content.Context-org.opencv.android.LoaderCallbackInterface-">initAsync</a></span>(java.lang.String&nbsp;Version,
         android.content.Context&nbsp;AppContext,
         <a href="../../../org/opencv/android/LoaderCallbackInterface.html" title="interface in org.opencv.android">LoaderCallbackInterface</a>&nbsp;Callback)</code>
<div class="block">Loads and initializes OpenCV library using OpenCV Engine service.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/OpenCVLoader.html#initDebug--">initDebug</a></span>()</code>
<div class="block">Loads and initializes OpenCV library from current application package.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/OpenCVLoader.html#initDebug-boolean-">initDebug</a></span>(boolean&nbsp;InitCuda)</code>
<div class="block">Loads and initializes OpenCV library from current application package.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="OPENCV_VERSION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPENCV_VERSION</h4>
<pre>public static final&nbsp;java.lang.String OPENCV_VERSION</pre>
<div class="block">Current OpenCV Library version</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.android.OpenCVLoader.OPENCV_VERSION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPENCV_VERSION_2_4_10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPENCV_VERSION_2_4_10</h4>
<pre>public static final&nbsp;java.lang.String OPENCV_VERSION_2_4_10</pre>
<div class="block">OpenCV Library version 2.4.10.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.android.OpenCVLoader.OPENCV_VERSION_2_4_10">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPENCV_VERSION_2_4_11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPENCV_VERSION_2_4_11</h4>
<pre>public static final&nbsp;java.lang.String OPENCV_VERSION_2_4_11</pre>
<div class="block">OpenCV Library version 2.4.11.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.android.OpenCVLoader.OPENCV_VERSION_2_4_11">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPENCV_VERSION_2_4_12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPENCV_VERSION_2_4_12</h4>
<pre>public static final&nbsp;java.lang.String OPENCV_VERSION_2_4_12</pre>
<div class="block">OpenCV Library version 2.4.12.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.android.OpenCVLoader.OPENCV_VERSION_2_4_12">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPENCV_VERSION_2_4_13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPENCV_VERSION_2_4_13</h4>
<pre>public static final&nbsp;java.lang.String OPENCV_VERSION_2_4_13</pre>
<div class="block">OpenCV Library version 2.4.13.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.android.OpenCVLoader.OPENCV_VERSION_2_4_13">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPENCV_VERSION_2_4_2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPENCV_VERSION_2_4_2</h4>
<pre>public static final&nbsp;java.lang.String OPENCV_VERSION_2_4_2</pre>
<div class="block">OpenCV Library version 2.4.2.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.android.OpenCVLoader.OPENCV_VERSION_2_4_2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPENCV_VERSION_2_4_3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPENCV_VERSION_2_4_3</h4>
<pre>public static final&nbsp;java.lang.String OPENCV_VERSION_2_4_3</pre>
<div class="block">OpenCV Library version 2.4.3.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.android.OpenCVLoader.OPENCV_VERSION_2_4_3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPENCV_VERSION_2_4_4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPENCV_VERSION_2_4_4</h4>
<pre>public static final&nbsp;java.lang.String OPENCV_VERSION_2_4_4</pre>
<div class="block">OpenCV Library version 2.4.4.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.android.OpenCVLoader.OPENCV_VERSION_2_4_4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPENCV_VERSION_2_4_5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPENCV_VERSION_2_4_5</h4>
<pre>public static final&nbsp;java.lang.String OPENCV_VERSION_2_4_5</pre>
<div class="block">OpenCV Library version 2.4.5.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.android.OpenCVLoader.OPENCV_VERSION_2_4_5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPENCV_VERSION_2_4_6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPENCV_VERSION_2_4_6</h4>
<pre>public static final&nbsp;java.lang.String OPENCV_VERSION_2_4_6</pre>
<div class="block">OpenCV Library version 2.4.6.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.android.OpenCVLoader.OPENCV_VERSION_2_4_6">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPENCV_VERSION_2_4_7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPENCV_VERSION_2_4_7</h4>
<pre>public static final&nbsp;java.lang.String OPENCV_VERSION_2_4_7</pre>
<div class="block">OpenCV Library version 2.4.7.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.android.OpenCVLoader.OPENCV_VERSION_2_4_7">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPENCV_VERSION_2_4_8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPENCV_VERSION_2_4_8</h4>
<pre>public static final&nbsp;java.lang.String OPENCV_VERSION_2_4_8</pre>
<div class="block">OpenCV Library version 2.4.8.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.android.OpenCVLoader.OPENCV_VERSION_2_4_8">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPENCV_VERSION_2_4_9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPENCV_VERSION_2_4_9</h4>
<pre>public static final&nbsp;java.lang.String OPENCV_VERSION_2_4_9</pre>
<div class="block">OpenCV Library version 2.4.9.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.android.OpenCVLoader.OPENCV_VERSION_2_4_9">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPENCV_VERSION_3_0_0">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPENCV_VERSION_3_0_0</h4>
<pre>public static final&nbsp;java.lang.String OPENCV_VERSION_3_0_0</pre>
<div class="block">OpenCV Library version 3.0.0.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.android.OpenCVLoader.OPENCV_VERSION_3_0_0">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPENCV_VERSION_3_1_0">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPENCV_VERSION_3_1_0</h4>
<pre>public static final&nbsp;java.lang.String OPENCV_VERSION_3_1_0</pre>
<div class="block">OpenCV Library version 3.1.0.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.android.OpenCVLoader.OPENCV_VERSION_3_1_0">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPENCV_VERSION_3_2_0">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPENCV_VERSION_3_2_0</h4>
<pre>public static final&nbsp;java.lang.String OPENCV_VERSION_3_2_0</pre>
<div class="block">OpenCV Library version 3.2.0.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.android.OpenCVLoader.OPENCV_VERSION_3_2_0">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPENCV_VERSION_3_3_0">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPENCV_VERSION_3_3_0</h4>
<pre>public static final&nbsp;java.lang.String OPENCV_VERSION_3_3_0</pre>
<div class="block">OpenCV Library version 3.3.0.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.android.OpenCVLoader.OPENCV_VERSION_3_3_0">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPENCV_VERSION_3_4_0">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>OPENCV_VERSION_3_4_0</h4>
<pre>public static final&nbsp;java.lang.String OPENCV_VERSION_3_4_0</pre>
<div class="block">OpenCV Library version 3.4.0.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.android.OpenCVLoader.OPENCV_VERSION_3_4_0">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="OpenCVLoader--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>OpenCVLoader</h4>
<pre>public&nbsp;OpenCVLoader()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="initAsync-java.lang.String-android.content.Context-org.opencv.android.LoaderCallbackInterface-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>initAsync</h4>
<pre>public static&nbsp;boolean&nbsp;initAsync(java.lang.String&nbsp;Version,
                                android.content.Context&nbsp;AppContext,
                                <a href="../../../org/opencv/android/LoaderCallbackInterface.html" title="interface in org.opencv.android">LoaderCallbackInterface</a>&nbsp;Callback)</pre>
<div class="block">Loads and initializes OpenCV library using OpenCV Engine service.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>Version</code> - OpenCV library version.</dd>
<dd><code>AppContext</code> - application context for connecting to the service.</dd>
<dd><code>Callback</code> - object, that implements LoaderCallbackInterface for handling the connection status.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Returns true if initialization of OpenCV is successful.</dd>
</dl>
</li>
</ul>
<a name="initDebug--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>initDebug</h4>
<pre>public static&nbsp;boolean&nbsp;initDebug()</pre>
<div class="block">Loads and initializes OpenCV library from current application package. Roughly, it's an analog of system.loadLibrary("opencv_java").</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Returns true is initialization of OpenCV was successful.</dd>
</dl>
</li>
</ul>
<a name="initDebug-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>initDebug</h4>
<pre>public static&nbsp;boolean&nbsp;initDebug(boolean&nbsp;InitCuda)</pre>
<div class="block">Loads and initializes OpenCV library from current application package. Roughly, it's an analog of system.loadLibrary("opencv_java").</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>InitCuda</code> - load and initialize CUDA runtime libraries.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Returns true is initialization of OpenCV was successful.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/android/LoaderCallbackInterface.html" title="interface in org.opencv.android"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/android/Utils.html" title="class in org.opencv.android"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/android/OpenCVLoader.html" target="_top">Frames</a></li>
<li><a href="OpenCVLoader.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2021-12-25 08:13:27 / OpenCV 4.5.5</small></p>
</body>
</html>
