<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_292) on Sat Dec 25 08:13:28 UTC 2021 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>SVM (OpenCV 4.5.5 Java documentation)</title>
<meta name="date" content="2021-12-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SVM (OpenCV 4.5.5 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":10,"i3":10,"i4":10,"i5":10,"i6":9,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":9,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/ml/SVMSGD.html" title="class in org.opencv.ml"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/ml/SVM.html" target="_top">Frames</a></li>
<li><a href="SVM.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.ml</div>
<h2 title="Class SVM" class="title">Class SVM</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">org.opencv.ml.StatModel</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.ml.SVM</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">SVM</span>
extends <a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></pre>
<div class="block">Support Vector Machines.

 SEE: REF: ml_intro_svm</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#C">C</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#C_SVC">C_SVC</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#CHI2">CHI2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#COEF">COEF</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#CUSTOM">CUSTOM</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#DEGREE">DEGREE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#EPS_SVR">EPS_SVR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#GAMMA">GAMMA</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#INTER">INTER</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#LINEAR">LINEAR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#NU">NU</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#NU_SVC">NU_SVC</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#NU_SVR">NU_SVR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#ONE_CLASS">ONE_CLASS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#P">P</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#POLY">POLY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#RBF">RBF</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#SIGMOID">SIGMOID</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.opencv.ml.StatModel">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.opencv.ml.<a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="../../../org/opencv/ml/StatModel.html#COMPRESSED_INPUT">COMPRESSED_INPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#PREPROCESSED_INPUT">PREPROCESSED_INPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#RAW_OUTPUT">RAW_OUTPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#UPDATE_MODEL">UPDATE_MODEL</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/SVM.html" title="class in org.opencv.ml">SVM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/SVM.html" title="class in org.opencv.ml">SVM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#create--">create</a></span>()</code>
<div class="block">Creates empty model.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#getC--">getC</a></span>()</code>
<div class="block">SEE: setC</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#getClassWeights--">getClassWeights</a></span>()</code>
<div class="block">SEE: setClassWeights</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#getCoef0--">getCoef0</a></span>()</code>
<div class="block">SEE: setCoef0</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#getDecisionFunction-int-org.opencv.core.Mat-org.opencv.core.Mat-">getDecisionFunction</a></span>(int&nbsp;i,
                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;alpha,
                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;svidx)</code>
<div class="block">Retrieves the decision function</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#getDefaultGridPtr-int-">getDefaultGridPtr</a></span>(int&nbsp;param_id)</code>
<div class="block">Generates a grid for %SVM parameters.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#getDegree--">getDegree</a></span>()</code>
<div class="block">SEE: setDegree</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#getGamma--">getGamma</a></span>()</code>
<div class="block">SEE: setGamma</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#getKernelType--">getKernelType</a></span>()</code>
<div class="block">Type of a %SVM kernel.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#getNu--">getNu</a></span>()</code>
<div class="block">SEE: setNu</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#getP--">getP</a></span>()</code>
<div class="block">SEE: setP</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#getSupportVectors--">getSupportVectors</a></span>()</code>
<div class="block">Retrieves all the support vectors

     The method returns all the support vectors as a floating-point matrix, where support vectors are
     stored as matrix rows.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#getTermCriteria--">getTermCriteria</a></span>()</code>
<div class="block">SEE: setTermCriteria</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#getType--">getType</a></span>()</code>
<div class="block">SEE: setType</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#getUncompressedSupportVectors--">getUncompressedSupportVectors</a></span>()</code>
<div class="block">Retrieves all the uncompressed support vectors of a linear %SVM

     The method returns all the uncompressed support vectors of a linear %SVM that the compressed
     support vector, used for prediction, was derived from.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/SVM.html" title="class in org.opencv.ml">SVM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#load-java.lang.String-">load</a></span>(java.lang.String&nbsp;filepath)</code>
<div class="block">Loads and creates a serialized svm from a file

 Use SVM::save to serialize and store an SVM to disk.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#setC-double-">setC</a></span>(double&nbsp;val)</code>
<div class="block">getC SEE: getC</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#setClassWeights-org.opencv.core.Mat-">setClassWeights</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;val)</code>
<div class="block">getClassWeights SEE: getClassWeights</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#setCoef0-double-">setCoef0</a></span>(double&nbsp;val)</code>
<div class="block">getCoef0 SEE: getCoef0</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#setDegree-double-">setDegree</a></span>(double&nbsp;val)</code>
<div class="block">getDegree SEE: getDegree</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#setGamma-double-">setGamma</a></span>(double&nbsp;val)</code>
<div class="block">getGamma SEE: getGamma</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#setKernel-int-">setKernel</a></span>(int&nbsp;kernelType)</code>
<div class="block">Initialize with one of predefined kernels.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#setNu-double-">setNu</a></span>(double&nbsp;val)</code>
<div class="block">getNu SEE: getNu</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#setP-double-">setP</a></span>(double&nbsp;val)</code>
<div class="block">getP SEE: getP</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#setTermCriteria-org.opencv.core.TermCriteria-">setTermCriteria</a></span>(<a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;val)</code>
<div class="block">getTermCriteria SEE: getTermCriteria</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#setType-int-">setType</a></span>(int&nbsp;val)</code>
<div class="block">getType SEE: getType</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#trainAuto-org.opencv.core.Mat-int-org.opencv.core.Mat-">trainAuto</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
         int&nbsp;layout,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses)</code>
<div class="block">Trains an %SVM with optimal parameters</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#trainAuto-org.opencv.core.Mat-int-org.opencv.core.Mat-int-">trainAuto</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
         int&nbsp;layout,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
         int&nbsp;kFold)</code>
<div class="block">Trains an %SVM with optimal parameters</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#trainAuto-org.opencv.core.Mat-int-org.opencv.core.Mat-int-org.opencv.ml.ParamGrid-">trainAuto</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
         int&nbsp;layout,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
         int&nbsp;kFold,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid)</code>
<div class="block">Trains an %SVM with optimal parameters</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#trainAuto-org.opencv.core.Mat-int-org.opencv.core.Mat-int-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-">trainAuto</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
         int&nbsp;layout,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
         int&nbsp;kFold,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;gammaGrid)</code>
<div class="block">Trains an %SVM with optimal parameters</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#trainAuto-org.opencv.core.Mat-int-org.opencv.core.Mat-int-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-">trainAuto</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
         int&nbsp;layout,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
         int&nbsp;kFold,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;gammaGrid,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;pGrid)</code>
<div class="block">Trains an %SVM with optimal parameters</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#trainAuto-org.opencv.core.Mat-int-org.opencv.core.Mat-int-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-">trainAuto</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
         int&nbsp;layout,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
         int&nbsp;kFold,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;gammaGrid,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;pGrid,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;nuGrid)</code>
<div class="block">Trains an %SVM with optimal parameters</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#trainAuto-org.opencv.core.Mat-int-org.opencv.core.Mat-int-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-">trainAuto</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
         int&nbsp;layout,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
         int&nbsp;kFold,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;gammaGrid,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;pGrid,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;nuGrid,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;coeffGrid)</code>
<div class="block">Trains an %SVM with optimal parameters</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#trainAuto-org.opencv.core.Mat-int-org.opencv.core.Mat-int-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-">trainAuto</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
         int&nbsp;layout,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
         int&nbsp;kFold,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;gammaGrid,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;pGrid,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;nuGrid,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;coeffGrid,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;degreeGrid)</code>
<div class="block">Trains an %SVM with optimal parameters</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVM.html#trainAuto-org.opencv.core.Mat-int-org.opencv.core.Mat-int-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-boolean-">trainAuto</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
         int&nbsp;layout,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
         int&nbsp;kFold,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;gammaGrid,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;pGrid,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;nuGrid,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;coeffGrid,
         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;degreeGrid,
         boolean&nbsp;balanced)</code>
<div class="block">Trains an %SVM with optimal parameters</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.ml.StatModel">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.ml.<a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="../../../org/opencv/ml/StatModel.html#calcError-org.opencv.ml.TrainData-boolean-org.opencv.core.Mat-">calcError</a>, <a href="../../../org/opencv/ml/StatModel.html#empty--">empty</a>, <a href="../../../org/opencv/ml/StatModel.html#getVarCount--">getVarCount</a>, <a href="../../../org/opencv/ml/StatModel.html#isClassifier--">isClassifier</a>, <a href="../../../org/opencv/ml/StatModel.html#isTrained--">isTrained</a>, <a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-">predict</a>, <a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-org.opencv.core.Mat-">predict</a>, <a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-org.opencv.core.Mat-int-">predict</a>, <a href="../../../org/opencv/ml/StatModel.html#train-org.opencv.core.Mat-int-org.opencv.core.Mat-">train</a>, <a href="../../../org/opencv/ml/StatModel.html#train-org.opencv.ml.TrainData-">train</a>, <a href="../../../org/opencv/ml/StatModel.html#train-org.opencv.ml.TrainData-int-">train</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">getDefaultName</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="C">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C</h4>
<pre>public static final&nbsp;int C</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.SVM.C">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="C_SVC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C_SVC</h4>
<pre>public static final&nbsp;int C_SVC</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.SVM.C_SVC">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CHI2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CHI2</h4>
<pre>public static final&nbsp;int CHI2</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.SVM.CHI2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COEF">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COEF</h4>
<pre>public static final&nbsp;int COEF</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.SVM.COEF">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CUSTOM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CUSTOM</h4>
<pre>public static final&nbsp;int CUSTOM</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.SVM.CUSTOM">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DEGREE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DEGREE</h4>
<pre>public static final&nbsp;int DEGREE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.SVM.DEGREE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="EPS_SVR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EPS_SVR</h4>
<pre>public static final&nbsp;int EPS_SVR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.SVM.EPS_SVR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GAMMA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GAMMA</h4>
<pre>public static final&nbsp;int GAMMA</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.SVM.GAMMA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INTER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INTER</h4>
<pre>public static final&nbsp;int INTER</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.SVM.INTER">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LINEAR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LINEAR</h4>
<pre>public static final&nbsp;int LINEAR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.SVM.LINEAR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NU">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NU</h4>
<pre>public static final&nbsp;int NU</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.SVM.NU">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NU_SVC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NU_SVC</h4>
<pre>public static final&nbsp;int NU_SVC</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.SVM.NU_SVC">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NU_SVR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NU_SVR</h4>
<pre>public static final&nbsp;int NU_SVR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.SVM.NU_SVR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ONE_CLASS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ONE_CLASS</h4>
<pre>public static final&nbsp;int ONE_CLASS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.SVM.ONE_CLASS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="P">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>P</h4>
<pre>public static final&nbsp;int P</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.SVM.P">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="POLY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>POLY</h4>
<pre>public static final&nbsp;int POLY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.SVM.POLY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RBF">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RBF</h4>
<pre>public static final&nbsp;int RBF</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.SVM.RBF">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SIGMOID">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SIGMOID</h4>
<pre>public static final&nbsp;int SIGMOID</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.SVM.SIGMOID">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/SVM.html" title="class in org.opencv.ml">SVM</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="create--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/SVM.html" title="class in org.opencv.ml">SVM</a>&nbsp;create()</pre>
<div class="block">Creates empty model.
     Use StatModel::train to train the model. Since %SVM has several parameters, you may want to
 find the best parameters for your problem, it can be done with SVM::trainAuto.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getC--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getC</h4>
<pre>public&nbsp;double&nbsp;getC()</pre>
<div class="block">SEE: setC</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getClassWeights--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClassWeights</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getClassWeights()</pre>
<div class="block">SEE: setClassWeights</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getCoef0--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCoef0</h4>
<pre>public&nbsp;double&nbsp;getCoef0()</pre>
<div class="block">SEE: setCoef0</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getDecisionFunction-int-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDecisionFunction</h4>
<pre>public&nbsp;double&nbsp;getDecisionFunction(int&nbsp;i,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;alpha,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;svidx)</pre>
<div class="block">Retrieves the decision function</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>i</code> - the index of the decision function. If the problem solved is regression, 1-class or
         2-class classification, then there will be just one decision function and the index should
         always be 0. Otherwise, in the case of N-class classification, there will be \(N(N-1)/2\)
         decision functions.</dd>
<dd><code>alpha</code> - the optional output vector for weights, corresponding to different support vectors.
         In the case of linear %SVM all the alpha's will be 1's.</dd>
<dd><code>svidx</code> - the optional output vector of indices of support vectors within the matrix of
         support vectors (which can be retrieved by SVM::getSupportVectors). In the case of linear
         %SVM each decision function consists of a single "compressed" support vector.

     The method returns rho parameter of the decision function, a scalar subtracted from the weighted
     sum of kernel responses.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getDefaultGridPtr-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultGridPtr</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;getDefaultGridPtr(int&nbsp;param_id)</pre>
<div class="block">Generates a grid for %SVM parameters.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>param_id</code> - %SVM parameters IDs that must be one of the SVM::ParamTypes. The grid is
     generated for the parameter with this ID.

     The function generates a grid pointer for the specified parameter of the %SVM algorithm.
     The grid may be passed to the function SVM::trainAuto.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getDegree--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDegree</h4>
<pre>public&nbsp;double&nbsp;getDegree()</pre>
<div class="block">SEE: setDegree</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getGamma--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGamma</h4>
<pre>public&nbsp;double&nbsp;getGamma()</pre>
<div class="block">SEE: setGamma</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getKernelType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getKernelType</h4>
<pre>public&nbsp;int&nbsp;getKernelType()</pre>
<div class="block">Type of a %SVM kernel.
 See SVM::KernelTypes. Default value is SVM::RBF.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getNu--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNu</h4>
<pre>public&nbsp;double&nbsp;getNu()</pre>
<div class="block">SEE: setNu</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getP--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getP</h4>
<pre>public&nbsp;double&nbsp;getP()</pre>
<div class="block">SEE: setP</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getSupportVectors--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSupportVectors</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getSupportVectors()</pre>
<div class="block">Retrieves all the support vectors

     The method returns all the support vectors as a floating-point matrix, where support vectors are
     stored as matrix rows.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getTermCriteria--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTermCriteria</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;getTermCriteria()</pre>
<div class="block">SEE: setTermCriteria</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getType</h4>
<pre>public&nbsp;int&nbsp;getType()</pre>
<div class="block">SEE: setType</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getUncompressedSupportVectors--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUncompressedSupportVectors</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getUncompressedSupportVectors()</pre>
<div class="block">Retrieves all the uncompressed support vectors of a linear %SVM

     The method returns all the uncompressed support vectors of a linear %SVM that the compressed
     support vector, used for prediction, was derived from. They are returned in a floating-point
     matrix, where the support vectors are stored as matrix rows.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="load-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>load</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/SVM.html" title="class in org.opencv.ml">SVM</a>&nbsp;load(java.lang.String&nbsp;filepath)</pre>
<div class="block">Loads and creates a serialized svm from a file

 Use SVM::save to serialize and store an SVM to disk.
 Load the SVM from this file again, by calling this function with the path to the file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filepath</code> - path to serialized svm</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setC-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setC</h4>
<pre>public&nbsp;void&nbsp;setC(double&nbsp;val)</pre>
<div class="block">getC SEE: getC</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setClassWeights-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setClassWeights</h4>
<pre>public&nbsp;void&nbsp;setClassWeights(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;val)</pre>
<div class="block">getClassWeights SEE: getClassWeights</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setCoef0-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCoef0</h4>
<pre>public&nbsp;void&nbsp;setCoef0(double&nbsp;val)</pre>
<div class="block">getCoef0 SEE: getCoef0</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setDegree-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDegree</h4>
<pre>public&nbsp;void&nbsp;setDegree(double&nbsp;val)</pre>
<div class="block">getDegree SEE: getDegree</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setGamma-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGamma</h4>
<pre>public&nbsp;void&nbsp;setGamma(double&nbsp;val)</pre>
<div class="block">getGamma SEE: getGamma</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setKernel-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setKernel</h4>
<pre>public&nbsp;void&nbsp;setKernel(int&nbsp;kernelType)</pre>
<div class="block">Initialize with one of predefined kernels.
 See SVM::KernelTypes.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>kernelType</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setNu-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNu</h4>
<pre>public&nbsp;void&nbsp;setNu(double&nbsp;val)</pre>
<div class="block">getNu SEE: getNu</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setP-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setP</h4>
<pre>public&nbsp;void&nbsp;setP(double&nbsp;val)</pre>
<div class="block">getP SEE: getP</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setTermCriteria-org.opencv.core.TermCriteria-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTermCriteria</h4>
<pre>public&nbsp;void&nbsp;setTermCriteria(<a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;val)</pre>
<div class="block">getTermCriteria SEE: getTermCriteria</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setType</h4>
<pre>public&nbsp;void&nbsp;setType(int&nbsp;val)</pre>
<div class="block">getType SEE: getType</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="trainAuto-org.opencv.core.Mat-int-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>trainAuto</h4>
<pre>public&nbsp;boolean&nbsp;trainAuto(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                         int&nbsp;layout,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses)</pre>
<div class="block">Trains an %SVM with optimal parameters</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - training samples</dd>
<dd><code>layout</code> - See ml::SampleTypes.</dd>
<dd><code>responses</code> - vector of responses associated with the training samples.
         subset is used to test the model, the others form the train set. So, the %SVM algorithm is
         balanced cross-validation subsets that is proportions between classes in subsets are close
         to such proportion in the whole train dataset.

     The method trains the %SVM model automatically by choosing the optimal parameters C, gamma, p,
     nu, coef0, degree. Parameters are considered optimal when the cross-validation
     estimate of the test set error is minimal.

     This function only makes use of SVM::getDefaultGrid for parameter optimization and thus only
     offers rudimentary parameter options.

     This function works for the classification (SVM::C_SVC or SVM::NU_SVC) as well as for the
     regression (SVM::EPS_SVR or SVM::NU_SVR). If it is SVM::ONE_CLASS, no optimization is made and
     the usual %SVM with parameters specified in params is executed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="trainAuto-org.opencv.core.Mat-int-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>trainAuto</h4>
<pre>public&nbsp;boolean&nbsp;trainAuto(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                         int&nbsp;layout,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
                         int&nbsp;kFold)</pre>
<div class="block">Trains an %SVM with optimal parameters</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - training samples</dd>
<dd><code>layout</code> - See ml::SampleTypes.</dd>
<dd><code>responses</code> - vector of responses associated with the training samples.</dd>
<dd><code>kFold</code> - Cross-validation parameter. The training set is divided into kFold subsets. One
         subset is used to test the model, the others form the train set. So, the %SVM algorithm is
         balanced cross-validation subsets that is proportions between classes in subsets are close
         to such proportion in the whole train dataset.

     The method trains the %SVM model automatically by choosing the optimal parameters C, gamma, p,
     nu, coef0, degree. Parameters are considered optimal when the cross-validation
     estimate of the test set error is minimal.

     This function only makes use of SVM::getDefaultGrid for parameter optimization and thus only
     offers rudimentary parameter options.

     This function works for the classification (SVM::C_SVC or SVM::NU_SVC) as well as for the
     regression (SVM::EPS_SVR or SVM::NU_SVR). If it is SVM::ONE_CLASS, no optimization is made and
     the usual %SVM with parameters specified in params is executed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="trainAuto-org.opencv.core.Mat-int-org.opencv.core.Mat-int-org.opencv.ml.ParamGrid-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>trainAuto</h4>
<pre>public&nbsp;boolean&nbsp;trainAuto(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                         int&nbsp;layout,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
                         int&nbsp;kFold,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid)</pre>
<div class="block">Trains an %SVM with optimal parameters</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - training samples</dd>
<dd><code>layout</code> - See ml::SampleTypes.</dd>
<dd><code>responses</code> - vector of responses associated with the training samples.</dd>
<dd><code>kFold</code> - Cross-validation parameter. The training set is divided into kFold subsets. One
         subset is used to test the model, the others form the train set. So, the %SVM algorithm is</dd>
<dd><code>Cgrid</code> - grid for C
         balanced cross-validation subsets that is proportions between classes in subsets are close
         to such proportion in the whole train dataset.

     The method trains the %SVM model automatically by choosing the optimal parameters C, gamma, p,
     nu, coef0, degree. Parameters are considered optimal when the cross-validation
     estimate of the test set error is minimal.

     This function only makes use of SVM::getDefaultGrid for parameter optimization and thus only
     offers rudimentary parameter options.

     This function works for the classification (SVM::C_SVC or SVM::NU_SVC) as well as for the
     regression (SVM::EPS_SVR or SVM::NU_SVR). If it is SVM::ONE_CLASS, no optimization is made and
     the usual %SVM with parameters specified in params is executed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="trainAuto-org.opencv.core.Mat-int-org.opencv.core.Mat-int-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>trainAuto</h4>
<pre>public&nbsp;boolean&nbsp;trainAuto(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                         int&nbsp;layout,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
                         int&nbsp;kFold,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;gammaGrid)</pre>
<div class="block">Trains an %SVM with optimal parameters</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - training samples</dd>
<dd><code>layout</code> - See ml::SampleTypes.</dd>
<dd><code>responses</code> - vector of responses associated with the training samples.</dd>
<dd><code>kFold</code> - Cross-validation parameter. The training set is divided into kFold subsets. One
         subset is used to test the model, the others form the train set. So, the %SVM algorithm is</dd>
<dd><code>Cgrid</code> - grid for C</dd>
<dd><code>gammaGrid</code> - grid for gamma
         balanced cross-validation subsets that is proportions between classes in subsets are close
         to such proportion in the whole train dataset.

     The method trains the %SVM model automatically by choosing the optimal parameters C, gamma, p,
     nu, coef0, degree. Parameters are considered optimal when the cross-validation
     estimate of the test set error is minimal.

     This function only makes use of SVM::getDefaultGrid for parameter optimization and thus only
     offers rudimentary parameter options.

     This function works for the classification (SVM::C_SVC or SVM::NU_SVC) as well as for the
     regression (SVM::EPS_SVR or SVM::NU_SVR). If it is SVM::ONE_CLASS, no optimization is made and
     the usual %SVM with parameters specified in params is executed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="trainAuto-org.opencv.core.Mat-int-org.opencv.core.Mat-int-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>trainAuto</h4>
<pre>public&nbsp;boolean&nbsp;trainAuto(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                         int&nbsp;layout,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
                         int&nbsp;kFold,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;gammaGrid,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;pGrid)</pre>
<div class="block">Trains an %SVM with optimal parameters</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - training samples</dd>
<dd><code>layout</code> - See ml::SampleTypes.</dd>
<dd><code>responses</code> - vector of responses associated with the training samples.</dd>
<dd><code>kFold</code> - Cross-validation parameter. The training set is divided into kFold subsets. One
         subset is used to test the model, the others form the train set. So, the %SVM algorithm is</dd>
<dd><code>Cgrid</code> - grid for C</dd>
<dd><code>gammaGrid</code> - grid for gamma</dd>
<dd><code>pGrid</code> - grid for p
         balanced cross-validation subsets that is proportions between classes in subsets are close
         to such proportion in the whole train dataset.

     The method trains the %SVM model automatically by choosing the optimal parameters C, gamma, p,
     nu, coef0, degree. Parameters are considered optimal when the cross-validation
     estimate of the test set error is minimal.

     This function only makes use of SVM::getDefaultGrid for parameter optimization and thus only
     offers rudimentary parameter options.

     This function works for the classification (SVM::C_SVC or SVM::NU_SVC) as well as for the
     regression (SVM::EPS_SVR or SVM::NU_SVR). If it is SVM::ONE_CLASS, no optimization is made and
     the usual %SVM with parameters specified in params is executed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="trainAuto-org.opencv.core.Mat-int-org.opencv.core.Mat-int-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>trainAuto</h4>
<pre>public&nbsp;boolean&nbsp;trainAuto(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                         int&nbsp;layout,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
                         int&nbsp;kFold,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;gammaGrid,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;pGrid,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;nuGrid)</pre>
<div class="block">Trains an %SVM with optimal parameters</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - training samples</dd>
<dd><code>layout</code> - See ml::SampleTypes.</dd>
<dd><code>responses</code> - vector of responses associated with the training samples.</dd>
<dd><code>kFold</code> - Cross-validation parameter. The training set is divided into kFold subsets. One
         subset is used to test the model, the others form the train set. So, the %SVM algorithm is</dd>
<dd><code>Cgrid</code> - grid for C</dd>
<dd><code>gammaGrid</code> - grid for gamma</dd>
<dd><code>pGrid</code> - grid for p</dd>
<dd><code>nuGrid</code> - grid for nu
         balanced cross-validation subsets that is proportions between classes in subsets are close
         to such proportion in the whole train dataset.

     The method trains the %SVM model automatically by choosing the optimal parameters C, gamma, p,
     nu, coef0, degree. Parameters are considered optimal when the cross-validation
     estimate of the test set error is minimal.

     This function only makes use of SVM::getDefaultGrid for parameter optimization and thus only
     offers rudimentary parameter options.

     This function works for the classification (SVM::C_SVC or SVM::NU_SVC) as well as for the
     regression (SVM::EPS_SVR or SVM::NU_SVR). If it is SVM::ONE_CLASS, no optimization is made and
     the usual %SVM with parameters specified in params is executed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="trainAuto-org.opencv.core.Mat-int-org.opencv.core.Mat-int-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>trainAuto</h4>
<pre>public&nbsp;boolean&nbsp;trainAuto(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                         int&nbsp;layout,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
                         int&nbsp;kFold,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;gammaGrid,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;pGrid,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;nuGrid,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;coeffGrid)</pre>
<div class="block">Trains an %SVM with optimal parameters</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - training samples</dd>
<dd><code>layout</code> - See ml::SampleTypes.</dd>
<dd><code>responses</code> - vector of responses associated with the training samples.</dd>
<dd><code>kFold</code> - Cross-validation parameter. The training set is divided into kFold subsets. One
         subset is used to test the model, the others form the train set. So, the %SVM algorithm is</dd>
<dd><code>Cgrid</code> - grid for C</dd>
<dd><code>gammaGrid</code> - grid for gamma</dd>
<dd><code>pGrid</code> - grid for p</dd>
<dd><code>nuGrid</code> - grid for nu</dd>
<dd><code>coeffGrid</code> - grid for coeff
         balanced cross-validation subsets that is proportions between classes in subsets are close
         to such proportion in the whole train dataset.

     The method trains the %SVM model automatically by choosing the optimal parameters C, gamma, p,
     nu, coef0, degree. Parameters are considered optimal when the cross-validation
     estimate of the test set error is minimal.

     This function only makes use of SVM::getDefaultGrid for parameter optimization and thus only
     offers rudimentary parameter options.

     This function works for the classification (SVM::C_SVC or SVM::NU_SVC) as well as for the
     regression (SVM::EPS_SVR or SVM::NU_SVR). If it is SVM::ONE_CLASS, no optimization is made and
     the usual %SVM with parameters specified in params is executed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="trainAuto-org.opencv.core.Mat-int-org.opencv.core.Mat-int-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>trainAuto</h4>
<pre>public&nbsp;boolean&nbsp;trainAuto(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                         int&nbsp;layout,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
                         int&nbsp;kFold,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;gammaGrid,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;pGrid,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;nuGrid,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;coeffGrid,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;degreeGrid)</pre>
<div class="block">Trains an %SVM with optimal parameters</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - training samples</dd>
<dd><code>layout</code> - See ml::SampleTypes.</dd>
<dd><code>responses</code> - vector of responses associated with the training samples.</dd>
<dd><code>kFold</code> - Cross-validation parameter. The training set is divided into kFold subsets. One
         subset is used to test the model, the others form the train set. So, the %SVM algorithm is</dd>
<dd><code>Cgrid</code> - grid for C</dd>
<dd><code>gammaGrid</code> - grid for gamma</dd>
<dd><code>pGrid</code> - grid for p</dd>
<dd><code>nuGrid</code> - grid for nu</dd>
<dd><code>coeffGrid</code> - grid for coeff</dd>
<dd><code>degreeGrid</code> - grid for degree
         balanced cross-validation subsets that is proportions between classes in subsets are close
         to such proportion in the whole train dataset.

     The method trains the %SVM model automatically by choosing the optimal parameters C, gamma, p,
     nu, coef0, degree. Parameters are considered optimal when the cross-validation
     estimate of the test set error is minimal.

     This function only makes use of SVM::getDefaultGrid for parameter optimization and thus only
     offers rudimentary parameter options.

     This function works for the classification (SVM::C_SVC or SVM::NU_SVC) as well as for the
     regression (SVM::EPS_SVR or SVM::NU_SVR). If it is SVM::ONE_CLASS, no optimization is made and
     the usual %SVM with parameters specified in params is executed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="trainAuto-org.opencv.core.Mat-int-org.opencv.core.Mat-int-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-org.opencv.ml.ParamGrid-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>trainAuto</h4>
<pre>public&nbsp;boolean&nbsp;trainAuto(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                         int&nbsp;layout,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
                         int&nbsp;kFold,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;Cgrid,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;gammaGrid,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;pGrid,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;nuGrid,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;coeffGrid,
                         <a href="../../../org/opencv/ml/ParamGrid.html" title="class in org.opencv.ml">ParamGrid</a>&nbsp;degreeGrid,
                         boolean&nbsp;balanced)</pre>
<div class="block">Trains an %SVM with optimal parameters</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - training samples</dd>
<dd><code>layout</code> - See ml::SampleTypes.</dd>
<dd><code>responses</code> - vector of responses associated with the training samples.</dd>
<dd><code>kFold</code> - Cross-validation parameter. The training set is divided into kFold subsets. One
         subset is used to test the model, the others form the train set. So, the %SVM algorithm is</dd>
<dd><code>Cgrid</code> - grid for C</dd>
<dd><code>gammaGrid</code> - grid for gamma</dd>
<dd><code>pGrid</code> - grid for p</dd>
<dd><code>nuGrid</code> - grid for nu</dd>
<dd><code>coeffGrid</code> - grid for coeff</dd>
<dd><code>degreeGrid</code> - grid for degree</dd>
<dd><code>balanced</code> - If true and the problem is 2-class classification then the method creates more
         balanced cross-validation subsets that is proportions between classes in subsets are close
         to such proportion in the whole train dataset.

     The method trains the %SVM model automatically by choosing the optimal parameters C, gamma, p,
     nu, coef0, degree. Parameters are considered optimal when the cross-validation
     estimate of the test set error is minimal.

     This function only makes use of SVM::getDefaultGrid for parameter optimization and thus only
     offers rudimentary parameter options.

     This function works for the classification (SVM::C_SVC or SVM::NU_SVC) as well as for the
     regression (SVM::EPS_SVR or SVM::NU_SVR). If it is SVM::ONE_CLASS, no optimization is made and
     the usual %SVM with parameters specified in params is executed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/ml/SVMSGD.html" title="class in org.opencv.ml"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/ml/SVM.html" target="_top">Frames</a></li>
<li><a href="SVM.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2021-12-25 08:13:27 / OpenCV 4.5.5</small></p>
</body>
</html>
