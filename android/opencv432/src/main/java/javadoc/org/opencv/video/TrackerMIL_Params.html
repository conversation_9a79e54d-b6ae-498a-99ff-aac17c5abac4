<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_292) on Sat Dec 25 08:13:28 UTC 2021 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>TrackerMIL_Params (OpenCV 4.5.5 Java documentation)</title>
<meta name="date" content="2021-12-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TrackerMIL_Params (OpenCV 4.5.5 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/video/TrackerMIL.html" title="class in org.opencv.video"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/video/VariationalRefinement.html" title="class in org.opencv.video"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/video/TrackerMIL_Params.html" target="_top">Frames</a></li>
<li><a href="TrackerMIL_Params.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.video</div>
<h2 title="Class TrackerMIL_Params" class="title">Class TrackerMIL_Params</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.video.TrackerMIL_Params</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">TrackerMIL_Params</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/video/TrackerMIL_Params.html#TrackerMIL_Params--">TrackerMIL_Params</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/TrackerMIL_Params.html" title="class in org.opencv.video">TrackerMIL_Params</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/TrackerMIL_Params.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/TrackerMIL_Params.html#get_featureSetNumFeatures--">get_featureSetNumFeatures</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/TrackerMIL_Params.html#get_samplerInitInRadius--">get_samplerInitInRadius</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/TrackerMIL_Params.html#get_samplerInitMaxNegNum--">get_samplerInitMaxNegNum</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/TrackerMIL_Params.html#get_samplerSearchWinSize--">get_samplerSearchWinSize</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/TrackerMIL_Params.html#get_samplerTrackInRadius--">get_samplerTrackInRadius</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/TrackerMIL_Params.html#get_samplerTrackMaxNegNum--">get_samplerTrackMaxNegNum</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/TrackerMIL_Params.html#get_samplerTrackMaxPosNum--">get_samplerTrackMaxPosNum</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/TrackerMIL_Params.html#getNativeObjAddr--">getNativeObjAddr</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/TrackerMIL_Params.html#set_featureSetNumFeatures-int-">set_featureSetNumFeatures</a></span>(int&nbsp;featureSetNumFeatures)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/TrackerMIL_Params.html#set_samplerInitInRadius-float-">set_samplerInitInRadius</a></span>(float&nbsp;samplerInitInRadius)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/TrackerMIL_Params.html#set_samplerInitMaxNegNum-int-">set_samplerInitMaxNegNum</a></span>(int&nbsp;samplerInitMaxNegNum)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/TrackerMIL_Params.html#set_samplerSearchWinSize-float-">set_samplerSearchWinSize</a></span>(float&nbsp;samplerSearchWinSize)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/TrackerMIL_Params.html#set_samplerTrackInRadius-float-">set_samplerTrackInRadius</a></span>(float&nbsp;samplerTrackInRadius)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/TrackerMIL_Params.html#set_samplerTrackMaxNegNum-int-">set_samplerTrackMaxNegNum</a></span>(int&nbsp;samplerTrackMaxNegNum)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/TrackerMIL_Params.html#set_samplerTrackMaxPosNum-int-">set_samplerTrackMaxPosNum</a></span>(int&nbsp;samplerTrackMaxPosNum)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="TrackerMIL_Params--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TrackerMIL_Params</h4>
<pre>public&nbsp;TrackerMIL_Params()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/TrackerMIL_Params.html" title="class in org.opencv.video">TrackerMIL_Params</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="get_featureSetNumFeatures--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_featureSetNumFeatures</h4>
<pre>public&nbsp;int&nbsp;get_featureSetNumFeatures()</pre>
</li>
</ul>
<a name="get_samplerInitInRadius--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_samplerInitInRadius</h4>
<pre>public&nbsp;float&nbsp;get_samplerInitInRadius()</pre>
</li>
</ul>
<a name="get_samplerInitMaxNegNum--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_samplerInitMaxNegNum</h4>
<pre>public&nbsp;int&nbsp;get_samplerInitMaxNegNum()</pre>
</li>
</ul>
<a name="get_samplerSearchWinSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_samplerSearchWinSize</h4>
<pre>public&nbsp;float&nbsp;get_samplerSearchWinSize()</pre>
</li>
</ul>
<a name="get_samplerTrackInRadius--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_samplerTrackInRadius</h4>
<pre>public&nbsp;float&nbsp;get_samplerTrackInRadius()</pre>
</li>
</ul>
<a name="get_samplerTrackMaxNegNum--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_samplerTrackMaxNegNum</h4>
<pre>public&nbsp;int&nbsp;get_samplerTrackMaxNegNum()</pre>
</li>
</ul>
<a name="get_samplerTrackMaxPosNum--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_samplerTrackMaxPosNum</h4>
<pre>public&nbsp;int&nbsp;get_samplerTrackMaxPosNum()</pre>
</li>
</ul>
<a name="getNativeObjAddr--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNativeObjAddr</h4>
<pre>public&nbsp;long&nbsp;getNativeObjAddr()</pre>
</li>
</ul>
<a name="set_featureSetNumFeatures-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_featureSetNumFeatures</h4>
<pre>public&nbsp;void&nbsp;set_featureSetNumFeatures(int&nbsp;featureSetNumFeatures)</pre>
</li>
</ul>
<a name="set_samplerInitInRadius-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_samplerInitInRadius</h4>
<pre>public&nbsp;void&nbsp;set_samplerInitInRadius(float&nbsp;samplerInitInRadius)</pre>
</li>
</ul>
<a name="set_samplerInitMaxNegNum-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_samplerInitMaxNegNum</h4>
<pre>public&nbsp;void&nbsp;set_samplerInitMaxNegNum(int&nbsp;samplerInitMaxNegNum)</pre>
</li>
</ul>
<a name="set_samplerSearchWinSize-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_samplerSearchWinSize</h4>
<pre>public&nbsp;void&nbsp;set_samplerSearchWinSize(float&nbsp;samplerSearchWinSize)</pre>
</li>
</ul>
<a name="set_samplerTrackInRadius-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_samplerTrackInRadius</h4>
<pre>public&nbsp;void&nbsp;set_samplerTrackInRadius(float&nbsp;samplerTrackInRadius)</pre>
</li>
</ul>
<a name="set_samplerTrackMaxNegNum-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_samplerTrackMaxNegNum</h4>
<pre>public&nbsp;void&nbsp;set_samplerTrackMaxNegNum(int&nbsp;samplerTrackMaxNegNum)</pre>
</li>
</ul>
<a name="set_samplerTrackMaxPosNum-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>set_samplerTrackMaxPosNum</h4>
<pre>public&nbsp;void&nbsp;set_samplerTrackMaxPosNum(int&nbsp;samplerTrackMaxPosNum)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/video/TrackerMIL.html" title="class in org.opencv.video"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/video/VariationalRefinement.html" title="class in org.opencv.video"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/video/TrackerMIL_Params.html" target="_top">Frames</a></li>
<li><a href="TrackerMIL_Params.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2021-12-25 08:13:27 / OpenCV 4.5.5</small></p>
</body>
</html>
