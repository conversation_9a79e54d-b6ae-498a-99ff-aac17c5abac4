buildscript {
    ext.kotlin_version = '1.8.10'
    repositories {
        google()
        mavenCentral()
        maven {
            credentials {
                username '630468d3f1e6ca42b400683c'
                password ')CaVNST9HaD1'
            }
            url 'https://packages.aliyun.com/maven/repository/2268762-release-c10dNg/'
        }
        maven {
            credentials {
                username '630468d3f1e6ca42b400683c'
                password ')CaVNST9HaD1'
            }
            url 'https://packages.aliyun.com/maven/repository/2268762-snapshot-TxmMXU/'
        }
        maven {
            url "https://maven.aliyun.com/nexus/content/repositories/releases/"
        }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/nexus/content/repositories/jcenter' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven {
            url "https://maven.aliyun.com/nexus/content/repositories/releases/"
        }
        maven {url 'https://developer.huawei.com/repo/'}
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.8.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.huawei.agconnect:agcp:1.5.2.300'
    }
}

allprojects {
    repositories {
        maven {
            credentials {
                username '630468d3f1e6ca42b400683c'
                password ')CaVNST9HaD1'
            }
            url 'https://packages.aliyun.com/maven/repository/2268762-release-c10dNg/'
        }
        maven {
            credentials {
                username '630468d3f1e6ca42b400683c'
                password ')CaVNST9HaD1'
            }
            url 'https://packages.aliyun.com/maven/repository/2268762-snapshot-TxmMXU/'
        }
        maven {
            url "https://maven.aliyun.com/nexus/content/repositories/releases/"
        }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/nexus/content/repositories/jcenter' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven {
            url "https://maven.aliyun.com/nexus/content/repositories/releases/"
        }
        maven {url 'https://developer.huawei.com/repo/'}
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
