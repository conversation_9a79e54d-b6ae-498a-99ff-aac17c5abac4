# 业务统计页面 (BusinessStatisticsPage)

## 功能概述

业务统计页面是一个用于展示快递业务统计数据的页面，包含以下主要功能：

1. **Tab切换**: 支持按快递公司、按业务员、按员工三种维度查看统计数据
2. **时间筛选**: 支持近7天、近30天、本月、上月等快捷时间选择，也支持自定义日期范围
3. **统计卡片**: 展示入库量、待入库量、签收量、退件量、问题件、业务员结算等关键指标
4. **每日明细**: 以表格形式展示每日的详细统计数据

## 页面结构

### 1. Tab切换栏
- 按快递公司
- 按业务员  
- 按员工

### 2. 时间选择器
- 快捷选择按钮：近7天、近30天、本月、上月
- 自定义日期范围选择器

### 3. 统计卡片
采用橙色背景的卡片设计，展示6个关键指标：
- 入库量
- 待入库量
- 签收量
- 退件量
- 问题件
- 业务员结算（带信息图标）

### 4. 每日明细表格
包含以下列：
- 日期
- 入库量
- 待入库量
- 签收量
- 问题件
- 退件量

## API接口

### 1. 统计数据汇总接口
```dart
/// 业务统计-获取统计数据汇总
static getBusinessStatisticsSummary(Map<String, dynamic> params) async {
  var res = await HttpManager.post(Courier.businessStatisticsSummary, params);
  return DataResult(res.data, res.result);
}
```

**接口地址**: `business.statistics.summary`

**请求参数**:
```json
{
  "startDate": "2025-06-01",
  "endDate": "2025-06-30", 
  "type": "courier" // courier|staff|employee
}
```

**返回数据格式**:
```json
{
  "inboundCount": 1403,
  "waitInboundCount": 0,
  "signCount": 1524,
  "returnCount": 56,
  "problemCount": 0,
  "businessFailCount": 0
}
```

### 2. 每日明细接口
```dart
/// 业务统计-获取每日明细数据
static getBusinessStatisticsDaily(Map<String, dynamic> params) async {
  var res = await HttpManager.post(Courier.businessStatisticsDaily, params);
  return DataResult(res.data, res.result);
}
```

**接口地址**: `business.statistics.daily`

**请求参数**: 同统计数据汇总接口

**返回数据格式**:
```json
[
  {
    "date": "06/29",
    "inbound": 0,
    "waitInbound": 0,
    "sign": 1,
    "problem": 0,
    "return": 0
  },
  {
    "date": "06/28", 
    "inbound": 0,
    "waitInbound": 0,
    "sign": 1,
    "problem": 0,
    "return": 0
  }
]
```

## 使用方法

### 1. 页面跳转
```dart
// 从数据中心页面跳转
NavigatorUtils.goBusinessStatisticsPage(context);
```

### 2. 自定义统计数据
可以通过修改 `_statisticsData` 和 `_dailyDetails` 来自定义显示的数据：

```dart
// 统计数据
Map<String, dynamic> _statisticsData = {
  'inboundCount': 1403,
  'waitInboundCount': 0,
  'signCount': 1524,
  'returnCount': 56,
  'problemCount': 0,
  'businessFailCount': 0,
};

// 每日明细数据
List<Map<String, dynamic>> _dailyDetails = [
  {'date': '06/29', 'inbound': 0, 'waitInbound': 0, 'sign': 1, 'problem': 0, 'return': 0},
  // ... 更多数据
];
```

## 设计特点

1. **响应式设计**: 适配不同屏幕尺寸
2. **Material Design**: 遵循Material Design设计规范
3. **用户友好**: 提供直观的时间选择和数据展示
4. **可扩展**: 易于添加新的统计维度和指标
5. **性能优化**: 使用ListView.builder优化长列表性能

## 注意事项

1. 页面使用了 `SingleTickerProviderStateMixin` 来支持Tab动画
2. 时间选择器支持中文本地化
3. API调用包含错误处理机制
4. 统计卡片使用主题色彩配置
5. 表格支持滚动查看更多数据

## 后续扩展

1. 可以添加图表展示功能
2. 支持数据导出功能
3. 添加更多筛选条件
4. 支持实时数据刷新
5. 添加数据对比功能
