group 'com.amap.flutter.amap_flutter_location'
version '1.0'

buildscript {
    repositories {
        google()
        jcenter()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:3.5.1'
    }
}

rootProject.allprojects {
    repositories {
        google()
        jcenter()
    }
}

apply plugin: 'com.android.library'

android {
    namespace = "com.amap.flutter.location"
    compileSdkVersion 29

    defaultConfig {
        minSdkVersion 16
    }
    lintOptions {
        disable 'InvalidPackage'
    }
    dependencies {
        compileOnly 'com.amap.api:location:5.6.0'
    }
}
