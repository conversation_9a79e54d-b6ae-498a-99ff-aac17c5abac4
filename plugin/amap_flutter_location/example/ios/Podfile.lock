PODS:
  - amap_flutter_location (0.0.1):
    - AMapLocation
    - Flutter
  - AMapFoundation (1.6.9)
  - AMapLocation (2.8.0):
    - AMapFoundation (~> 1.6.9)
  - Flutter (1.0.0)
  - "permission_handler (5.1.0+2)":
    - Flutter

DEPENDENCIES:
  - amap_flutter_location (from `.symlinks/plugins/amap_flutter_location/ios`)
  - Flutter (from `Flutter`)
  - permission_handler (from `.symlinks/plugins/permission_handler/ios`)

SPEC REPOS:
  trunk:
    - AMapFoundation
    - AMapLocation

EXTERNAL SOURCES:
  amap_flutter_location:
    :path: ".symlinks/plugins/amap_flutter_location/ios"
  Flutter:
    :path: Flutter
  permission_handler:
    :path: ".symlinks/plugins/permission_handler/ios"

SPEC CHECKSUMS:
  amap_flutter_location: 44ff5beb64f42e0bf5feb402fe299dac0013af6f
  AMapFoundation: 8d8ecbb0b2e9ce5487995360d26c885d94642bfd
  AMapLocation: 5ef44a1117be7dc541cb7a7d43d03c5ee91e4387
  Flutter: 434fef37c0980e73bb6479ef766c45957d4b510c
  permission_handler: ccb20a9fad0ee9b1314a52b70b76b473c5f8dab0

PODFILE CHECKSUM: 8e679eca47255a8ca8067c4c67aab20e64cb974d

COCOAPODS: 1.10.0
